# Manufacturing ERP i18n Quality Check for GitLab CI
# 
# Include this in your main .gitlab-ci.yml:
# include:
#   - local: '.gitlab-ci-i18n.yml'

# Define the i18n quality check job
i18n-quality-check:
  stage: test
  image: node:18-alpine
  
  # Run in parallel with other tests - don't block pipeline
  allow_failure: true
  
  # Only run on relevant file changes
  rules:
    - changes:
        - "app/**/*.tsx"
        - "app/**/*.ts"
        - "components/**/*.tsx"
        - "components/**/*.ts"
        - "components/i18n-provider.tsx"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - when: manual
      allow_failure: true
  
  # Cache node_modules for faster builds
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
  
  # Environment variables for i18n workflow
  variables:
    I18N_POLICY: "warn"
    I18N_MAX_NEW_STRINGS: "20"
    I18N_OUTPUT_FORMAT: "json"
    I18N_CREATE_ARTIFACTS: "true"
  
  before_script:
    - echo "🚀 Manufacturing ERP i18n Quality Check"
    - echo "⚠️  ZERO BREAKING CHANGES: Non-blocking quality checks"
    - echo ""
    
    # Install dependencies
    - npm ci --legacy-peer-deps
    - npm install i18n-ai --save-dev --legacy-peer-deps || true
    
    # Setup i18n environment
    - mkdir -p i18n-parallel/{pending,approved,integrated,csv,logs}
    - mkdir -p i18n-temp i18n-cicd
    - chmod +x scripts/i18n-*.js || true
  
  script:
    - echo "🔍 Running i18n CI/CD integration..."
    - node scripts/i18n-cicd-integration.js run
  
  after_script:
    - echo "📊 i18n Quality Check completed"
    - echo "🚀 Pipeline continues normally (non-blocking)"
  
  # Collect artifacts for review
  artifacts:
    name: "i18n-quality-report-$CI_PIPELINE_ID"
    paths:
      - i18n-cicd/*.json
      - i18n-cicd/*.md
      - i18n-temp/*.json
      - i18n-parallel/validation/*.json
    reports:
      # Generate test report if available
      junit: i18n-temp/junit-report.xml
    expire_in: 30 days
    when: always
  
  # Optional: Send notifications on significant findings
  after_script:
    - |
      if [ -f "i18n-cicd/cicd-summary-*.md" ]; then
        echo "📋 i18n Quality Report generated"
        
        # Check if new strings were detected
        if grep -q "New Strings: [1-9]" i18n-cicd/cicd-summary-*.md; then
          echo "⚠️  New hardcoded strings detected"
          echo "💡 Consider using the i18n acceleration workflow"
        else
          echo "✅ No new hardcoded strings detected"
        fi
      fi

# Optional: Comprehensive i18n check (manual trigger)
i18n-comprehensive-check:
  extends: i18n-quality-check
  
  variables:
    I18N_CHECK_LEVEL: "comprehensive"
    I18N_MAX_NEW_STRINGS: "50"
  
  rules:
    - when: manual
      allow_failure: true
  
  script:
    - echo "🔍 Running comprehensive i18n quality check..."
    - node scripts/i18n-cicd-integration.js run
    - echo "📊 Running additional validations..."
    - node scripts/i18n-performance-assessor.js assess || true
    - node scripts/i18n-rollback-validator.js backup-check || true
