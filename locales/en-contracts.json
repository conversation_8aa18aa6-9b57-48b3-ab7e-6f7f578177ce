{"contracts.sales.title": "Sales Contracts", "contracts.sales.desc": "Create basic sales contracts from products and customers.", "contracts.sales.empty": "No sales contracts.", "contracts.purchase.title": "Purchase Contracts", "contracts.purchase.desc": "Issue POs against suppliers.", "contracts.purchase.empty": "No purchase contracts.", "contracts.form.number": "Contract Number", "contracts.form.customer": "Customer", "contracts.form.supplier": "Supplier", "contracts.form.currency": "<PERSON><PERSON><PERSON><PERSON>", "contracts.form.template": "Template", "contracts.form.items": "Contract Items", "contracts.form.product": "Product", "contracts.form.quantity": "Quantity", "contracts.form.price": "Price", "contracts.form.total": "Total", "contracts.form.add_item": "Add Item", "contracts.form.remove_item": "Remove", "contracts.form.contract_info": "Contract Information", "contracts.form.contract_info_desc": "Basic contract details and customer information", "contracts.form.items_section": "Contract Items", "contracts.form.items_section_desc": "Products and quantities for this contract", "contracts.form.template_section": "Contract Template", "contracts.form.template_section_desc": "Choose a template for contract document generation", "contracts.view.back": "Back to Contracts", "contracts.view.back_sales": "Back to Sales Contracts", "contracts.view.back_purchase": "Back to Purchase Contracts", "contracts.view.edit_contract": "Edit Contract", "contracts.view.export_pdf": "Export PDF", "contracts.view.loading": "Loading contract document...", "contracts.view.no_document": "No contract document available", "contracts.view.contract_summary": "Contract Summary", "contracts.view.contract_document": "Contract Document", "contracts.view.customer": "Customer", "contracts.view.supplier": "Supplier", "contracts.view.contract_date": "Contract Date", "contracts.view.total_value": "Total Value", "contracts.view.template": "Template", "contracts.view.no_email": "No email", "contracts.view.items_count": "{count} items", "contracts.view.sales_template": "sales template", "contracts.view.purchase_template": "purchase template", "contracts.edit.title_sales": "Edit Sales Contract", "contracts.edit.title_purchase": "Edit Purchase Contract", "contracts.edit.subtitle": "Update the details of contract {number}", "contracts.edit.back": "Back to Contracts", "contracts.edit.template_optional": "Contract Template (Optional)", "contracts.edit.template_desc": "sales contract template", "contracts.edit.preview": "Preview", "contracts.edit.items_title": "Contract Items", "contracts.edit.add_item": "Add Item", "contracts.edit.product": "Product", "contracts.edit.quantity": "Quantity", "contracts.edit.unit_price": "Unit Price", "contracts.edit.sku_label": "SKU: {sku}", "contracts.edit.unit_label": "Unit: {unit}", "contracts.create.title_sales": "Create Sales Contract", "contracts.create.title_purchase": "Create Purchase Contract", "contracts.create.subtitle_sales": "Enter the details for your new sales contract", "contracts.create.subtitle_purchase": "Enter the details for your new purchase contract", "contracts.create.back_sales": "Sales Contracts", "contracts.create.back_purchase": "Purchase Contracts", "contracts.create.add_new": "Add New Contract", "contracts.create.contract_info": "Contract Information", "contracts.create.contract_info_desc": "Basic contract details and customer information", "contracts.create.contract_info_desc_purchase": "Basic contract details and supplier information", "contracts.create.number": "Contract Number", "contracts.create.number_placeholder": "e.g., PC-2025-001", "contracts.create.supplier": "Supplier", "contracts.create.supplier_placeholder": "Select supplier...", "contracts.create.customer": "Customer", "contracts.create.customer_placeholder": "Select customer...", "contracts.create.currency": "<PERSON><PERSON><PERSON><PERSON>", "contracts.create.currency_placeholder": "e.g., USD", "contracts.create.template": "Contract Template (Optional)", "contracts.create.template_placeholder": "Select template...", "contracts.create.items": "Contract Items", "contracts.create.items_desc": "Add products and quantities for this contract", "contracts.create.add_item": "Add Item", "contracts.create.remove_item": "Remove Item", "contracts.create.product": "Product", "contracts.create.product_placeholder": "Select product...", "contracts.create.quantity": "Quantity", "contracts.create.quantity_placeholder": "0", "contracts.create.unit_price": "Unit Price", "contracts.create.unit_price_placeholder": "0.00", "contracts.create.total": "Total", "contracts.create.cancel": "Cancel", "contracts.create.create": "Create Contract", "contracts.create.creating": "Creating...", "contracts.create.success": "Contract created successfully!", "contracts.create.error": "Failed to create contract", "contracts.create.summary": "Contract Summary", "contracts.create.summary_desc": "Review the total contract value and details", "contracts.create.items_count": "Items:", "contracts.create.currency_label": "Currency:", "contracts.create.total_value": "Total Contract Value:", "contracts.templates.page_title": "Contract Templates", "contracts.templates.page_desc": "Manage contract templates for sales and purchase agreements", "contracts.templates.sales_section": "Sales Contract Templates", "contracts.templates.sales_desc": "Create and manage sales contract templates", "contracts.templates.purchase_section": "Purchase Contract Templates", "contracts.templates.purchase_desc": "Create and manage purchase contract templates", "contracts.templates.template_name": "Template Name", "contracts.templates.currency": "<PERSON><PERSON><PERSON><PERSON>", "contracts.templates.payment_terms": "Payment Terms", "contracts.templates.delivery_terms": "Delivery Terms", "contracts.templates.template_content": "Template Content", "contracts.templates.content_placeholder": "Enter contract template content with placeholders like {{customer_name}}, {{product_name}}, etc.", "contracts.templates.content_placeholder_purchase": "Enter contract template content with placeholders like {{supplier_name}}, {{material_name}}, etc.", "contracts.templates.create_template": "Create Template", "contracts.templates.existing_templates": "Existing Templates", "contracts.templates.name": "Name", "contracts.templates.actions": "Actions", "contracts.templates.payment_30_days": "30 days", "contracts.templates.payment_60_days": "60 days", "contracts.templates.delivery_fob": "FOB", "contracts.templates.delivery_cif": "CIF", "contracts.templates.delivery_exw": "EXW"}