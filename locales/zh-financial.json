{"finance.title": "财务会计", "finance.description": "管理发票、付款和财务报表", "finance.summary.totalAR": "应收总额", "finance.summary.outstandingAR": "未收应收", "finance.summary.totalAP": "应付总额", "finance.summary.netCashFlow": "净现金流", "finance.kpis.coreMetrics": "核心财务指标", "finance.kpis.totalRevenue": "总收入（年度）", "finance.kpis.totalExpenses": "总支出（年度）", "finance.kpis.profitLoss": "损益（年度）", "finance.kpis.netCashFlow": "净现金流", "finance.kpis.overdueIntelligence": "逾期智能分析", "finance.kpis.overdueAR": "逾期应收", "finance.kpis.overdueAP": "逾期应付", "finance.kpis.manufacturingIntelligence": "制造业智能分析", "finance.kpis.contractProfitability": "合同盈利能力", "finance.kpis.avgCollectionDays": "平均收款天数", "finance.kpis.manufacturingMargin": "制造业利润率", "finance.kpis.activeContracts": "活跃合同", "finance.ar.title": "应收账款", "finance.ar.description": "跟踪应收发票和账龄，集成合同管理", "finance.ar.invoiceNumber": "发票号码", "finance.ar.customer": "客户", "finance.ar.salesContract": "销售合同", "finance.ar.amount": "金额", "finance.ar.received": "已收款", "finance.ar.currency": "货币", "finance.ar.status": "状态", "finance.ar.invoiceDate": "发票日期", "finance.ar.dueDate": "到期日期", "finance.ar.paymentTerms": "付款条件", "finance.ar.aging": "账龄", "finance.ar.contract": "合同", "finance.ar.createInvoice": "创建应收发票", "finance.ar.noInvoices": "未找到应收发票", "finance.ap.title": "应付账款", "finance.ap.description": "跟踪应付发票和付款，集成合同管理", "finance.ap.invoiceNumber": "发票号码", "finance.ap.supplier": "供应商", "finance.ap.purchaseContract": "采购合同", "finance.ap.amount": "金额", "finance.ap.paid": "已付款", "finance.ap.currency": "货币", "finance.ap.status": "状态", "finance.ap.invoiceDate": "发票日期", "finance.ap.dueDate": "到期日期", "finance.ap.paymentTerms": "付款条件", "finance.ap.aging": "账龄", "finance.ap.contract": "合同", "finance.ap.createInvoice": "创建应付发票", "finance.ap.noInvoices": "未找到应付发票", "finance.paymentTerms.tt": "TT（电汇）", "finance.paymentTerms.dp": "DP（付款交单）", "finance.paymentTerms.lc": "LC（信用证）", "finance.paymentTerms.deposit": "定金（预付款）", "finance.paymentTerms.depositTT": "30%定金 + 70%电汇", "finance.paymentTerms.depositLC": "50%定金 + 50%信用证", "finance.status.depositReceived": "已收定金", "finance.status.partialPaid": "部分付款", "finance.status.depositPaid": "已付定金", "finance.ar.desc": "跟踪应收与账龄。", "finance.ar.empty": "暂无应收发票。", "finance.ap.desc": "跟踪应付与付款。", "finance.ap.empty": "暂无应付发票。"}