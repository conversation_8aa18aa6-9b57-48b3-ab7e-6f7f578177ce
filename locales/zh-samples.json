{"samples.title": "样品管理", "samples.subtitle": "管理产品样品和审批流程", "samples.add": "新建样品", "samples.refresh": "刷新", "samples.loading": "正在加载样品...", "samples.found": "找到 {count} 个样品", "samples.cards.outbound.title": "📤 出库样品", "samples.cards.outbound.description": "我们发送给客户", "samples.cards.inbound.title": "📥 入库样品", "samples.cards.inbound.description": "来自客户和供应商", "samples.cards.internal.title": "🏭 内部样品", "samples.cards.internal.description": "研发和测试", "samples.cards.quality.title": "🧪 质量流水线", "samples.cards.quality.description": "等待质检审批", "samples.table.sample": "样品", "samples.table.direction": "方向", "samples.table.purpose": "用途", "samples.table.relationship": "业务关系", "samples.table.product": "产品", "samples.table.status": "状态", "samples.table.priority": "优先级", "samples.table.created": "创建时间", "samples.table.actions": "操作", "samples.table.empty": "未找到样品。创建您的第一个样品开始使用。", "samples.actions.view": "查看", "samples.actions.edit": "编辑", "samples.actions.delete": "删除", "samples.actions.approve": "审批", "samples.actions.reject": "拒绝", "samples.filters.search": "按名称、编码或备注搜索样品...", "samples.filters.status.all": "所有状态", "samples.filters.status.pending": "待审批", "samples.filters.status.approved": "已审批", "samples.filters.status.rejected": "已拒绝", "samples.filters.type.all": "所有类型", "samples.filters.type.development": "开发", "samples.filters.type.production": "生产", "samples.filters.type.quality": "质量", "samples.filters.type.prototype": "原型", "samples.filters.direction.all": "所有方向", "samples.filters.direction.outbound": "出库", "samples.filters.direction.inbound": "入库", "samples.filters.direction.internal": "内部", "samples.filters.advanced": "高级", "samples.filters.clear": "清除筛选", "samples.delete.success.title": "样品已删除", "samples.delete.success.description": "样品 '{name}' 已成功删除", "samples.delete.error.title": "删除失败", "samples.delete.error.description": "删除样品失败，请重试。", "samples.delete.dialog.title": "删除样品", "samples.delete.dialog.description": "您确定要删除此样品吗？此操作无法撤销。", "samples.delete.dialog.warning": "此操作是永久性的，无法撤销。", "samples.delete.dialog.confirm": "删除样品", "samples.delete.dialog.deleting": "删除中...", "samples.fields.code": "样品编码", "samples.fields.name": "样品名称", "samples.fields.date": "日期", "samples.fields.status": "状态", "samples.fields.priority": "优先级", "samples.fields.type": "样品类型", "samples.fields.direction": "方向", "samples.fields.purpose": "用途", "samples.fields.customer": "客户", "samples.fields.supplier": "供应商", "samples.fields.product": "产品", "samples.fields.quantity": "数量", "samples.fields.unit": "单位", "samples.fields.cost": "成本", "samples.fields.currency": "货币", "samples.fields.delivery_date": "交付日期", "samples.fields.specifications": "技术规格", "samples.fields.quality_requirements": "质量要求", "samples.fields.notes": "备注", "samples.create.title": "创建样品", "samples.create.description": "创建新的样品记录用于跟踪和审批", "samples.create.basic_info": "基本信息", "samples.create.workflow": "样品流程", "samples.create.relationships": "业务关系", "samples.create.specifications": "规格和详情", "samples.create.success.title": "样品已创建", "samples.create.success.description": "样品已成功创建", "samples.create.error.title": "创建失败", "samples.create.error.description": "创建样品失败，请重试。", "samples.create.cancel": "取消", "samples.create.save": "创建样品", "samples.create.saving": "创建中...", "samples.view.back": "返回样品", "samples.view.pending_request": "待处理请求", "samples.view.approved": "已审批", "samples.view.rejected": "已拒绝", "samples.view.edit": "编辑", "samples.view.sample_info": "样品信息", "samples.view.sample_type": "样品类型", "samples.view.priority": "优先级", "samples.view.sample_date": "样品日期", "samples.view.quantity": "数量", "samples.view.delivery_date": "交付日期", "samples.view.cost": "成本", "samples.view.relationships": "业务关系", "samples.view.customer": "客户", "samples.view.contact": "联系人", "samples.view.email": "邮箱", "samples.view.product": "产品", "samples.view.sku": "SKU", "samples.view.supplier": "供应商", "samples.view.specifications": "规格和备注", "samples.view.technical_specs": "技术规格", "samples.view.quality_requirements": "质量要求", "samples.view.notes": "备注", "samples.view.approval_history": "审批历史", "samples.view.created": "已创建", "samples.view.revised": "已修订", "samples.view.pending": "待审批", "samples.view.revision_required": "需要修订", "samples.view.by_system": "由系统", "samples.view.by_current_user": "由当前用户", "samples.view.sample_created": "样品已创建并提交审批", "samples.view.sample_processed": "样品已处理", "samples.view.metadata": "元数据", "samples.view.created_date": "创建时间", "samples.view.approved_by": "审批人", "samples.view.approved_date": "审批日期", "samples.view.status.created": "已创建", "samples.view.status.pending": "待审批", "samples.view.status.approved": "已审批", "samples.view.status.rejected": "已拒绝", "samples.view.status.revised": "已修订", "samples.view.status.revision_required": "需要修订", "samples.view.actions.sample_created": "样品已创建并提交审批", "samples.view.actions.sample_processed": "样品已处理", "samples.view.actions.sample_approved": "样品已审批", "samples.view.actions.sample_rejected": "样品已拒绝", "samples.view.actions.revision_requested": "要求修订", "samples.view.by_system_on": "由系统于", "samples.view.by_current_user_on": "由当前用户于", "samples.view.by_user_on": "由 {user} 于", "samples.edit.title": "编辑样品", "samples.edit.description": "更新样品信息和规格", "samples.edit.loading": "正在加载样品数据...", "samples.edit.success.title": "样品已更新", "samples.edit.success.description": "样品已成功更新", "samples.edit.error.title": "更新失败", "samples.edit.error.description": "更新样品失败，请重试。", "samples.edit.error.load": "加载样品数据失败，请重试。", "samples.edit.back": "返回样品", "samples.edit.cancel": "取消", "samples.edit.save": "更新样品", "samples.edit.saving": "更新中...", "samples.edit.validation.name": "样品名称为必填项", "samples.edit.code.readonly": "样品编码无法更改", "samples.edit.basic_info": "基本信息", "samples.edit.basic_info_desc": "更新基本样品信息", "samples.edit.sample_code": "样品编码", "samples.edit.sample_name": "样品名称", "samples.edit.sample_name_placeholder": "输入样品名称", "samples.edit.sample_type": "样品类型", "samples.edit.priority": "优先级", "samples.edit.relationships": "业务关系", "samples.edit.relationships_desc": "将此样品与客户、产品和供应商关联", "samples.edit.customer": "客户", "samples.edit.customer_placeholder": "搜索客户...", "samples.edit.product": "产品", "samples.edit.product_placeholder": "搜索产品...", "samples.edit.supplier": "供应商", "samples.edit.supplier_placeholder": "搜索供应商...", "samples.edit.specifications": "规格和详情", "samples.edit.specifications_desc": "添加技术规格和其他详情", "samples.edit.quantity": "数量", "samples.edit.unit": "单位", "samples.edit.cost": "成本", "samples.edit.currency": "货币", "samples.edit.delivery_date": "交付日期", "samples.edit.technical_specs": "技术规格", "samples.edit.technical_specs_placeholder": "输入技术规格...", "samples.edit.quality_requirements": "质量要求", "samples.edit.quality_requirements_placeholder": "输入质量要求...", "samples.edit.notes": "备注", "samples.edit.notes_placeholder": "输入其他备注...", "samples.edit.search.no_results": "未找到结果", "samples.edit.search.add_new_customer": "添加新客户", "samples.edit.search.add_new_product": "添加新产品", "samples.edit.search.add_new_supplier": "添加新供应商", "samples.edit.search.loading": "加载中...", "samples.edit.search.type_to_search": "输入以搜索..."}