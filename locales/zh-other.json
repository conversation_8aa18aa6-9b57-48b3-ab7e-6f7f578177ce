{"app.name": "FC-CHINA", "cmd.placeholder": "搜索或快速跳转...", "home.title": "外贸制造 ERP", "home.subtitle": "统一管理主数据、合同、生产、库存、出口报关与财务。", "home.quick.master": "添加主数据", "home.quick.contract": "创建合同", "home.quick.stock": "记录出入库", "home.quick.declaration": "新建报关单", "kpi.customers": "客户数", "kpi.products": "产品数", "kpi.suppliers": "供应商", "kpi.contracts": "合同", "kpi.suppliers.desc": "活跃供应商", "kpi.contracts.desc": "销售和采购合同", "kpi.onhand": "在库数量（合计）", "kpi.openWos": "未完成工单", "sample.title": "示例数据", "sample.desc": "此工作区内置了贴近真实的示例数据，便于理解各模块之间的关联。", "sample.reset": "重置示例数据", "sample.clear": "清空示例数据", "alert.db.title": "数据库未配置", "alert.db.desc": "请设置 DATABASE_URL（Neon Postgres）。应用将在首次加载时安全地自动迁移并种子最小数据。你也可以手动执行 scripts/sql/001_init.sql 和 002_seed.sql。", "alert.prep.title": "正在准备工作区", "alert.prep.desc": "正在初始化数据库结构，请稍后刷新。", "field.code": "编码", "field.name": "名称", "field.spec": "规格", "field.moq": "起订量", "field.inStock": "现货", "field.contact": "联系人", "field.incoterm": "贸易术语", "field.paymentTerm": "付款条款", "field.address": "地址", "field.sku": "SKU", "field.unit": "单位", "field.hsCode": "HS 编码", "field.origin": "原产地", "field.packaging": "包装", "field.currency": "货币", "field.number": "编号", "field.customer": "客户", "field.supplier": "供应商", "field.product": "产品", "field.qty": "数量", "field.price": "价格", "field.total": "总计", "field.woNumber": "工单号", "field.salesContract": "销售合同", "field.location": "位置", "field.note": "备注", "field.reference": "参考", "field.declarationNo": "报关单号", "field.amount": "金额", "field.received": "已收", "field.paid": "已付", "field.invoiceNo": "发票号", "table.actions": "操作", "table.noData": "暂无数据", "action.add": "添加", "action.addItem": "添加明细", "action.remove": "移除", "action.delete": "删除", "action.create": "创建", "action.createContract": "创建合同", "action.createPO": "创建采购单", "action.createWO": "创建工单", "action.addInbound": "收货", "action.addOutbound": "发货", "action.createDeclaration": "创建报关单", "action.submit": "提交", "action.createAR": "创建应收", "action.createAP": "创建应付", "cta.open": "进入", "basic.samples.title": "样品中心", "basic.samples.desc": "编码、名称、规格、起订量、是否现货。", "basic.customers.title": "客户", "basic.customers.desc": "客户档案与贸易条款。", "basic.suppliers.title": "供应商", "basic.suppliers.desc": "合格供应商与联系方式。", "basic.products.title": "产品/SKU", "basic.products.desc": "单位、HS 编码、原产地、包装。", "production.title": "生产工单", "production.desc": "跟踪工序进度与路由。", "production.empty": "暂无工单。", "docs.plan.title": "参考脑图", "docs.plan.desc": "用于指导实施的中文原稿与英文译稿。", "module.master.title": "主数据", "module.master.desc": "样品、客户、供应商、产品及贸易条款。", "module.contracts.title": "合同", "module.contracts.desc": "基于 SKU 创建销售合同与采购订单。", "module.production.title": "生产", "module.production.desc": "生成工单并跟踪工序与质检。", "module.inventory.title": "库存", "module.inventory.desc": "入库/出库、先进先出及当前库位。", "module.export.title": "出口", "module.export.desc": "创建报关单并校验 HS 编码。", "module.export.declarations": "报关单", "module.finance.title": "财务", "module.finance.desc": "跟踪应收与应付及基础账龄。", "header.wo": "工单", "header.operations": "工序", "header.type": "类型", "header.time": "时间", "header.lot": "批次", "field.email": "邮箱", "field.phone": "电话", "field.company": "公司", "field.status": "状态", "field.type": "类型", "field.date": "日期", "field.notes": "备注", "field.contract": "合同", "field.template": "模板", "sales_contracts.title": "销售合同", "sales_contracts.subtitle": "管理您公司的销售合同。", "sales_contracts.add": "添加合同", "sales_contracts.add.title": "创建销售合同", "sales_contracts.add.description": "为您的客户创建新的销售合同。", "sales_contracts.edit.title": "编辑销售合同", "sales_contracts.edit.description": "更新销售合同信息。", "sales_contracts.delete.title": "您确定吗？", "sales_contracts.delete.description": "这将永久删除该合同。此操作无法撤销。", "sales_contracts.form.number": "合同编号", "sales_contracts.form.customer": "客户", "sales_contracts.form.template": "模板", "sales_contracts.form.currency": "货币", "sales_contracts.form.items": "明细", "sales_contracts.table.contract_number": "合同编号", "sales_contracts.table.number": "合同编号", "sales_contracts.table.customer": "客户", "sales_contracts.table.date": "日期", "sales_contracts.table.currency": "货币", "sales_contracts.table.items": "明细", "sales_contracts.table.total": "总计", "sales_contracts.table.status": "状态", "sales_contracts.table.created": "创建时间", "sales_contracts.table.actions": "操作", "sales_contracts.search_placeholder": "搜索合同...", "sales_contracts.success.created": "销售合同创建成功！", "sales_contracts.success.updated": "销售合同更新成功！", "sales_contracts.success.deleted": "合同删除成功。", "sales_contracts.empty": "未找到销售合同", "sales_contracts.empty.description": "创建您的第一个销售合同开始使用。", "purchase_contracts.title": "采购合同", "purchase_contracts.subtitle": "管理您公司的采购合同。", "purchase_contracts.add": "添加合同", "purchase_contracts.add.title": "创建采购合同", "purchase_contracts.add.description": "与您的供应商创建新的采购合同。", "purchase_contracts.edit.title": "编辑采购合同", "purchase_contracts.edit.description": "更新采购合同信息。", "purchase_contracts.delete.title": "您确定吗？", "purchase_contracts.delete.description": "这将永久删除该合同。此操作无法撤销。", "purchase_contracts.form.number": "合同编号", "purchase_contracts.form.supplier": "供应商", "purchase_contracts.form.template": "模板", "purchase_contracts.form.currency": "货币", "purchase_contracts.form.items": "明细", "purchase_contracts.table.contract_number": "合同编号", "purchase_contracts.table.number": "合同编号", "purchase_contracts.table.supplier": "供应商", "purchase_contracts.table.date": "日期", "purchase_contracts.table.currency": "货币", "purchase_contracts.table.items": "明细", "purchase_contracts.table.total": "总计", "purchase_contracts.table.status": "状态", "purchase_contracts.table.created": "创建时间", "purchase_contracts.table.actions": "操作", "purchase_contracts.search_placeholder": "搜索合同...", "purchase_contracts.success.created": "采购合同创建成功！", "purchase_contracts.success.updated": "采购合同更新成功！", "purchase_contracts.success.deleted": "合同删除成功。", "purchase_contracts.empty": "未找到采购合同", "purchase_contracts.empty.description": "创建您的第一个采购合同开始使用。", "company.profile.title": "公司资料", "company.profile.subtitle": "管理您的公司信息和设置", "company.profile.not_found": "未找到公司资料", "company.profile.not_found_desc": "看起来您还没有完成公司资料设置。", "company.profile.complete_setup": "完成公司设置", "company.profile.complete": "完整", "company.profile.incomplete": "不完整", "company.profile.edit": "编辑资料", "company.profile.save": "保存更改", "company.profile.cancel": "取消", "company.profile.tabs.basic": "基本信息", "company.profile.tabs.business": "业务详情", "company.profile.tabs.banking": "银行信息", "company.profile.tabs.export": "出口贸易", "company.profile.basic.description": "您公司的基本联系方式和地址信息", "company.profile.business.description": "业务注册和运营信息", "company.profile.banking.description": "银行和金融账户详情", "company.profile.export.description": "出口许可和贸易合规信息", "company.profile.success.updated": "公司资料更新成功！", "company.field.name": "公司名称", "company.field.legal_name": "法定公司名称", "company.field.email": "邮箱地址", "company.field.phone": "电话号码", "company.field.website": "网站", "company.field.country": "国家", "company.field.address_line1": "街道地址", "company.field.address_line2": "地址第二行", "company.field.city": "城市", "company.field.state_province": "省/州", "company.field.postal_code": "邮政编码", "company.field.industry": "行业", "company.field.business_type": "业务类型", "company.field.employee_count": "员工数量", "company.field.annual_revenue": "年收入", "company.field.registration_number": "注册号", "company.field.tax_id": "税务编号", "company.field.vat_number": "增值税号", "company.field.bank_name": "银行名称", "company.field.bank_account": "账户号码", "company.field.bank_swift": "SWIFT/BIC代码", "company.field.bank_address": "银行地址", "company.field.export_license": "出口许可证", "company.field.customs_code": "海关代码", "company.field.preferred_incoterms": "首选贸易条款", "company.field.preferred_payment_terms": "首选付款条件", "contract_templates.title": "合同模板", "contract_templates.subtitle": "管理销售和采购协议的可重用合同模板", "contract_templates.add": "添加模板", "contract_templates.table.name": "模板名称", "contract_templates.table.type": "类型", "contract_templates.table.language": "语言", "contract_templates.table.version": "版本", "contract_templates.table.status": "状态", "contract_templates.table.actions": "操作", "contract_templates.sales.title": "销售合同模板", "contract_templates.sales.description": "创建和管理销售合同模板", "contract_templates.sales.sample": "示例模板", "contract_templates.sales.sample_title": "专业销售合同模板", "contract_templates.sales.sample_desc": "复制此专业模板并粘贴到下面的模板内容字段中。", "contract_templates.purchase.title": "采购合同模板", "contract_templates.purchase.description": "创建和管理采购合同模板", "contract_templates.purchase.sample": "示例模板", "contract_templates.purchase.sample_title": "专业采购合同模板", "contract_templates.purchase.sample_desc": "复制此专业模板并粘贴到下面的模板内容字段中。", "workOrder.title": "生产工单", "workOrder.number": "工单编号", "workOrder.status.completed": "已完成", "workOrder.status.pending": "待开始", "workOrder.status.in-progress": "进行中", "work_orders.quality_gate.title": "需要质量审批", "work_orders.quality_gate.description": "此工单需要完成所有必需的质量检验审批后才能完成。", "work_orders.quality_gate.work_order_info": "工单信息", "work_orders.quality_gate.inspections_status": "质量检验状态", "work_orders.quality_gate.no_inspections": "未找到质量检验记录。可能需要创建检验。", "work_orders.quality_gate.completion_status": "完成状态", "work_orders.quality_gate.can_complete": "所有质量要求已满足。工单可以完成。", "work_orders.quality_gate.cannot_complete": "完成前需要质量审批。", "work_orders.quality_gate.pending_inspections": "待处理检验", "work_orders.quality_gate.complete_inspections_first": "请先完成所有待处理的质量检验。", "work_orders.quality_gate.go_to_quality_control": "前往质量控制", "work_orders.quality_gate.complete_work_order": "完成工单"}