{"app.name": "FC-CHINA", "nav.group.overview": "总览", "nav.group.master-data": "主数据", "nav.group.sales-purchasing": "销售采购", "nav.group.sales-process": "销售流程", "nav.group.production": "生产管理", "nav.group.production-planning": "生产计划", "nav.group.inventory-logistics": "库存物流", "nav.group.quality-inventory": "质量库存", "nav.group.shipping-export": "运输出口", "nav.group.export-trade": "出口贸易", "nav.group.finance-reporting": "财务报表", "nav.group.settings": "设置", "nav.group.administration": "系统管理", "nav.item.dashboard": "仪表盘", "nav.item.customers": "客户管理", "nav.item.suppliers": "供应商", "nav.item.products": "产品管理", "nav.item.samples": "样品管理", "nav.item.sales-contracts": "销售合同", "nav.item.purchase-contracts": "采购合同", "nav.item.contract-templates": "合同模板", "nav.item.work-orders": "生产工单", "nav.item.bom-management": "物料清单", "nav.item.quality-control": "质量控制", "nav.item.mrp-planning": "MRP计划", "nav.item.inventory": "库存管理", "nav.item.raw-materials": "原材料", "nav.item.locations": "仓库位置", "nav.item.shipping": "物流运输", "nav.item.export-declarations": "出口报关", "nav.item.trade-compliance": "贸易合规", "nav.item.documentation": "文档管理", "nav.item.accounting": "财务会计", "nav.item.financial-dashboard": "财务仪表盘", "nav.item.accounts-receivable": "应收账款", "nav.item.accounts-payable": "应付账款", "nav.item.reports": "报表分析", "nav.item.company-profile": "公司资料", "cmd.placeholder": "搜索或快速跳转...", "home.title": "外贸制造 ERP", "home.subtitle": "统一管理主数据、合同、生产、库存、出口报关与财务。", "home.quick.master": "添加主数据", "home.quick.contract": "创建合同", "home.quick.stock": "记录出入库", "home.quick.declaration": "新建报关单", "kpi.customers": "客户数", "kpi.products": "产品数", "kpi.suppliers": "供应商", "kpi.contracts": "合同", "kpi.suppliers.desc": "活跃供应商", "kpi.contracts.desc": "销售和采购合同", "kpi.onhand": "在库数量（合计）", "kpi.openWos": "未完成工单", "dashboard.quick_actions.title": "快速操作", "dashboard.quick_actions.subtitle": "常用任务帮助您快速开始", "dashboard.quick_actions.manage_customers": "管理客户", "dashboard.quick_actions.view_products": "查看产品", "dashboard.quick_actions.create_contract": "创建销售合同", "dashboard.quick_actions.update_profile": "更新公司资料", "dashboard.system_status.title": "系统状态", "dashboard.system_status.subtitle": "您的ERP系统设置进度", "dashboard.system_status.company_profile": "公司资料", "dashboard.system_status.customer_database": "客户数据库", "dashboard.system_status.product_catalog": "产品目录", "dashboard.system_status.first_contract": "首个销售合同", "dashboard.system_status.inventory_setup": "库存设置", "dashboard.system_status.complete": "完成", "dashboard.system_status.active": "活跃", "dashboard.system_status.ready": "就绪", "dashboard.system_status.pending": "待处理", "dashboard.getting_started.title": "🚀 快速开始", "dashboard.getting_started.subtitle": "完成这些步骤以充分利用您的制造业ERP系统", "dashboard.getting_started.step1.title": "1. 公司设置", "dashboard.getting_started.step1.desc": "您的公司资料已完成，可以开始业务。", "dashboard.getting_started.step2.title": "2. 创建您的第一个合同", "dashboard.getting_started.step2.desc": "通过创建您的第一个销售合同开始创收。", "dashboard.getting_started.step2.action": "创建合同", "dashboard.getting_started.step3.title": "3. 设置库存", "dashboard.getting_started.step3.desc": "跟踪您的原材料和成品库存。", "dashboard.getting_started.step3.action": "设置库存", "sample.title": "示例数据", "sample.desc": "此工作区内置了贴近真实的示例数据，便于理解各模块之间的关联。", "sample.reset": "重置示例数据", "sample.clear": "清空示例数据", "alert.db.title": "数据库未配置", "alert.db.desc": "请设置 DATABASE_URL（Neon Postgres）。应用将在首次加载时安全地自动迁移并种子最小数据。你也可以手动执行 scripts/sql/001_init.sql 和 002_seed.sql。", "alert.prep.title": "正在准备工作区", "alert.prep.desc": "正在初始化数据库结构，请稍后刷新。", "field.code": "编码", "field.name": "名称", "field.spec": "规格", "field.moq": "起订量", "field.inStock": "现货", "field.contact": "联系人", "field.incoterm": "贸易术语", "field.paymentTerm": "付款条款", "field.address": "地址", "field.sku": "SKU", "field.unit": "单位", "field.hsCode": "HS 编码", "field.origin": "原产地", "field.packaging": "包装", "field.currency": "货币", "field.number": "编号", "field.customer": "客户", "field.supplier": "供应商", "field.product": "产品", "field.qty": "数量", "field.price": "价格", "field.total": "总计", "field.woNumber": "工单号", "field.salesContract": "销售合同", "field.location": "位置", "field.note": "备注", "field.reference": "参考", "field.declarationNo": "报关单号", "field.amount": "金额", "field.received": "已收", "field.paid": "已付", "field.invoiceNo": "发票号", "table.actions": "操作", "table.noData": "暂无数据", "action.add": "添加", "action.addItem": "添加明细", "action.remove": "移除", "action.delete": "删除", "action.create": "创建", "action.createContract": "创建合同", "action.createPO": "创建采购单", "action.createWO": "创建工单", "action.addInbound": "收货", "action.addOutbound": "发货", "action.createDeclaration": "创建报关单", "action.submit": "提交", "action.createAR": "创建应收", "action.createAP": "创建应付", "cta.open": "进入", "basic.samples.title": "样品中心", "basic.samples.desc": "编码、名称、规格、起订量、是否现货。", "basic.customers.title": "客户", "basic.customers.desc": "客户档案与贸易条款。", "basic.suppliers.title": "供应商", "basic.suppliers.desc": "合格供应商与联系方式。", "basic.products.title": "产品/SKU", "basic.products.desc": "单位、HS 编码、原产地、包装。", "contracts.sales.title": "销售合同", "contracts.sales.desc": "根据产品与客户创建销售合同。", "contracts.sales.empty": "暂无销售合同。", "contracts.purchase.title": "采购合同", "contracts.purchase.desc": "向供应商下达采购订单。", "contracts.purchase.empty": "暂无采购合同。", "production.title": "生产工单", "production.desc": "跟踪工序进度与路由。", "production.empty": "暂无工单。", "inventory.inbound.title": "入库", "inventory.inbound.desc": "接收库存", "inventory.outbound.title": "出库", "inventory.outbound.desc": "发货库存", "inventory.stock.title": "库存", "inventory.stock.desc": "当前库存水平", "inventory.stock.empty": "暂无库存批次。", "inventory.txns.title": "库存交易", "inventory.txns.desc": "出库时应用先进先出（FIFO）。", "inventory.txns.empty": "暂无交易记录。", "export.title": "出口报关", "export.desc": "校验 HS 编码并跟踪报关状态。", "export.empty": "暂无报关单。", "finance.title": "财务会计", "finance.description": "管理发票、付款和财务报表", "finance.summary.totalAR": "应收总额", "finance.summary.outstandingAR": "未收应收", "finance.summary.totalAP": "应付总额", "finance.summary.netCashFlow": "净现金流", "finance.kpis.coreMetrics": "核心财务指标", "finance.kpis.totalRevenue": "总收入（年度）", "finance.kpis.totalExpenses": "总支出（年度）", "finance.kpis.profitLoss": "损益（年度）", "finance.kpis.netCashFlow": "净现金流", "finance.kpis.overdueIntelligence": "逾期智能分析", "finance.kpis.overdueAR": "逾期应收", "finance.kpis.overdueAP": "逾期应付", "finance.kpis.manufacturingIntelligence": "制造业智能分析", "finance.kpis.contractProfitability": "合同盈利能力", "finance.kpis.avgCollectionDays": "平均收款天数", "finance.kpis.manufacturingMargin": "制造业利润率", "finance.kpis.activeContracts": "活跃合同", "finance.ar.title": "应收账款", "finance.ar.description": "跟踪应收发票和账龄，集成合同管理", "finance.ar.invoiceNumber": "发票号码", "finance.ar.customer": "客户", "finance.ar.salesContract": "销售合同", "finance.ar.amount": "金额", "finance.ar.received": "已收款", "finance.ar.currency": "货币", "finance.ar.status": "状态", "finance.ar.invoiceDate": "发票日期", "finance.ar.dueDate": "到期日期", "finance.ar.paymentTerms": "付款条件", "finance.ar.aging": "账龄", "finance.ar.contract": "合同", "finance.ar.createInvoice": "创建应收发票", "finance.ar.noInvoices": "未找到应收发票", "finance.ap.title": "应付账款", "finance.ap.description": "跟踪应付发票和付款，集成合同管理", "finance.ap.invoiceNumber": "发票号码", "finance.ap.supplier": "供应商", "finance.ap.purchaseContract": "采购合同", "finance.ap.amount": "金额", "finance.ap.paid": "已付款", "finance.ap.currency": "货币", "finance.ap.status": "状态", "finance.ap.invoiceDate": "发票日期", "finance.ap.dueDate": "到期日期", "finance.ap.paymentTerms": "付款条件", "finance.ap.aging": "账龄", "finance.ap.contract": "合同", "finance.ap.createInvoice": "创建应付发票", "finance.ap.noInvoices": "未找到应付发票", "finance.paymentTerms.tt": "TT（电汇）", "finance.paymentTerms.dp": "DP（付款交单）", "finance.paymentTerms.lc": "LC（信用证）", "finance.paymentTerms.deposit": "定金（预付款）", "finance.paymentTerms.depositTT": "30%定金 + 70%电汇", "finance.paymentTerms.depositLC": "50%定金 + 50%信用证", "finance.status.depositReceived": "已收定金", "finance.status.partialPaid": "部分付款", "finance.status.depositPaid": "已付定金", "finance.ar.desc": "跟踪应收与账龄。", "finance.ar.empty": "暂无应收发票。", "finance.ap.desc": "跟踪应付与付款。", "finance.ap.empty": "暂无应付发票。", "docs.plan.title": "参考脑图", "docs.plan.desc": "用于指导实施的中文原稿与英文译稿。", "module.master.title": "主数据", "module.master.desc": "样品、客户、供应商、产品及贸易条款。", "module.contracts.title": "合同", "module.contracts.desc": "基于 SKU 创建销售合同与采购订单。", "module.production.title": "生产", "module.production.desc": "生成工单并跟踪工序与质检。", "module.inventory.title": "库存", "module.inventory.desc": "入库/出库、先进先出及当前库位。", "module.export.title": "出口", "module.export.desc": "创建报关单并校验 HS 编码。", "module.export.declarations": "报关单", "module.finance.title": "财务", "module.finance.desc": "跟踪应收与应付及基础账龄。", "landing.login": "登录", "landing.getStarted": "开始使用", "landing.learnMore": "了解更多", "landing.badge": "500+ 出口制造商信赖之选", "landing.hero.title": "一体化纺织制造与出口 ERP", "landing.hero.subtitle": "从生产到出口，简化您的纺织制造业务流程。在一个平台上管理客户、产品、质量控制和国际贸易合规。", "landing.features.noCredit": "无需信用卡", "landing.features.freeTrial": "30天免费试用", "landing.features.quickSetup": "几分钟完成设置", "landing.features.title": "出口制造所需的一切功能", "landing.features.subtitle": "从原材料到国际运输，管理您的整个制造工作流程", "landing.features.crm.title": "客户与供应商管理", "landing.features.crm.description": "全面的客户关系管理系统，管理国际客户和供应商的联系信息、付款条款和贸易历史。", "landing.features.inventory.title": "产品目录与库存", "landing.features.inventory.description": "详细的产品管理，包括SKU、规格、质量标准和实时库存跟踪。", "landing.features.production.title": "生产管理", "landing.features.production.description": "工单管理、生产调度，从裁剪到包装的实时跟踪。", "landing.features.quality.title": "质量控制", "landing.features.quality.description": "集成质量检验、缺陷跟踪和出口标准合规管理。", "landing.features.export.title": "出口文档", "landing.features.export.description": "自动化出口报关、运输文件和国际贸易合规管理。", "landing.features.analytics.title": "分析与报告", "landing.features.analytics.description": "实时仪表板、生产分析和全面的业务洞察报告。", "landing.benefits.title": "为什么选择我们的制造 ERP？", "landing.benefits.subtitle": "专为出口导向的纺织制造商打造", "landing.benefits.speed.title": "提升 50% 运营效率", "landing.benefits.speed.description": "简化的工作流程减少手工作业，加速生产周期", "landing.benefits.compliance.title": "100% 合规保障", "landing.benefits.compliance.description": "内置出口合规功能，确保满足所有国际贸易要求", "landing.benefits.global.title": "全球化就绪", "landing.benefits.global.description": "多币种、多语言支持，满足国际业务运营需求", "landing.hero.mock.last30days": "近30天", "landing.hero.mock.onTimeShipments": "准时发货率", "landing.cta.title": "准备好转型您的制造业务了吗？", "landing.cta.subtitle": "加入数百家已通过我们的 ERP 解决方案简化运营的制造商", "landing.cta.button": "立即开始免费试用", "landing.cta.features": "无需信用卡 • 30天免费试用 • 几分钟完成设置", "landing.footer.copyright": "© 2024 FC-CHINA. 为全球出口制造商而建。", "dashboard.loading": "正在加载仪表板...", "dashboard.error.title": "仪表板错误", "dashboard.error.description": "这可能表示您需要完成公司资料设置。", "dashboard.error.retry": "重试", "dashboard.welcome": "欢迎回来", "dashboard.subtitle": "这是您今天制造业务的最新情况。", "dashboard.stats.customers.title": "客户总数", "dashboard.stats.customers.description": "活跃客户账户", "dashboard.stats.products.title": "产品", "dashboard.stats.products.description": "目录中的产品", "dashboard.stats.suppliers.title": "供应商", "dashboard.stats.suppliers.description": "活跃供应商", "dashboard.stats.contracts.title": "活跃合同", "dashboard.stats.contracts.description": "销售和采购合同", "common.loading": "加载中...", "common.error": "错误", "common.success": "成功", "common.cancel": "取消", "common.save": "保存", "common.delete": "删除", "common.edit": "编辑", "common.view": "查看", "common.add": "添加", "common.create": "创建", "common.update": "更新", "common.search": "搜索", "common.filter": "筛选", "common.actions": "操作", "common.status": "状态", "common.name": "名称", "common.description": "描述", "common.date": "日期", "common.created": "创建时间", "common.updated": "更新时间", "common.active": "活跃", "common.inactive": "非活跃", "common.yes": "是", "common.no": "否", "common.confirm": "确认", "common.close": "关闭", "common.retry": "重试", "common.optional": "可选", "status.active": "活跃", "status.inactive": "非活跃", "status.draft": "草稿", "status.approved": "已审批", "status.pending": "待处理", "status.completed": "已完成", "status.cancelled": "已取消", "status.done": "完成", "header.wo": "工单", "header.operations": "工序", "header.type": "类型", "header.time": "时间", "header.lot": "批次", "loading.inventory": "正在加载库存...", "loading.try_again": "重试", "field.email": "邮箱", "field.phone": "电话", "field.company": "公司", "field.status": "状态", "field.type": "类型", "field.date": "日期", "field.notes": "备注", "field.contract": "合同", "field.template": "模板", "customers.title": "客户管理", "customers.subtitle": "管理您的客户数据库和关系", "customers.add": "添加客户", "customers.add.title": "添加新客户", "customers.add.description": "为您的业务创建新的客户记录。", "customers.edit.title": "编辑客户", "customers.edit.description": "更新客户信息。", "customers.delete.title": "删除客户", "customers.delete.description": "您确定要删除此客户吗？此操作无法撤销。", "customers.form.company_name": "公司名称", "customers.form.contact_name": "联系人", "customers.form.contact_phone": "电话号码", "customers.form.contact_email": "邮箱地址", "customers.form.address": "地址", "customers.form.tax_id": "税号", "customers.form.bank": "银行信息", "customers.form.incoterm": "贸易条款", "customers.form.payment_term": "付款条件", "customers.form.status": "状态", "customers.table.company_name": "公司名称", "customers.table.contact_person": "联系人", "customers.table.phone": "电话", "customers.table.email": "邮箱", "customers.table.address": "地址", "customers.table.incoterm": "贸易条款", "customers.table.payment_terms": "付款条件", "customers.table.status": "状态", "customers.table.actions": "操作", "customers.success.created": "客户创建成功！", "customers.success.updated": "客户更新成功！", "customers.success.deleted": "客户删除成功！", "customers.error.create": "创建客户失败", "customers.error.update": "更新客户失败", "customers.error.delete": "删除客户失败", "customers.empty": "未找到客户", "customers.empty.description": "添加您的第一个客户开始使用。", "products.title": "产品管理", "products.subtitle": "管理您的产品目录和库存", "products.add": "添加产品", "products.add.title": "添加新产品", "products.add.description": "在您的目录中创建新产品。", "products.edit.title": "编辑产品", "products.edit.description": "更新产品信息。", "products.delete.title": "删除产品", "products.delete.description": "您确定要删除此产品吗？此操作无法撤销。", "products.form.name": "产品名称", "products.form.sku": "SKU", "products.form.unit": "单位", "products.form.hs_code": "HS编码", "products.form.origin": "原产地", "products.form.package": "包装", "products.form.status": "状态", "products.form.pricing_information": "价格信息", "products.form.base_price": "基础价格", "products.form.cost_price": "成本价格", "products.form.margin_percentage": "利润率 %", "products.form.currency": "货币", "products.table.name": "产品名称", "products.table.sku": "SKU", "products.table.unit": "单位", "products.table.hs_code": "HS编码", "products.table.origin": "原产地", "products.table.package": "包装", "products.table.price": "价格", "products.table.status": "状态", "products.table.actions": "操作", "products.success.created": "产品创建成功！", "products.success.updated": "产品已更新", "products.success.deleted": "产品删除成功！", "products.error.create": "创建产品失败", "products.error.update": "更新失败", "products.error.delete": "删除产品失败", "products.empty": "未找到产品", "products.empty.description": "添加您的第一个产品开始使用。", "products.view.title": "产品详情", "products.view.description": "查看产品信息（只读）。", "sales_contracts.title": "销售合同", "sales_contracts.subtitle": "管理您公司的销售合同。", "sales_contracts.add": "添加合同", "sales_contracts.add.title": "创建销售合同", "sales_contracts.add.description": "为您的客户创建新的销售合同。", "sales_contracts.edit.title": "编辑销售合同", "sales_contracts.edit.description": "更新销售合同信息。", "sales_contracts.delete.title": "您确定吗？", "sales_contracts.delete.description": "这将永久删除该合同。此操作无法撤销。", "sales_contracts.form.number": "合同编号", "sales_contracts.form.customer": "客户", "sales_contracts.form.template": "模板", "sales_contracts.form.currency": "货币", "sales_contracts.form.items": "明细", "sales_contracts.table.contract_number": "合同编号", "sales_contracts.table.number": "合同编号", "sales_contracts.table.customer": "客户", "sales_contracts.table.date": "日期", "sales_contracts.table.currency": "货币", "sales_contracts.table.items": "明细", "sales_contracts.table.total": "总计", "sales_contracts.table.status": "状态", "sales_contracts.table.created": "创建时间", "sales_contracts.table.actions": "操作", "sales_contracts.search_placeholder": "搜索合同...", "sales_contracts.success.created": "销售合同创建成功！", "sales_contracts.success.updated": "销售合同更新成功！", "sales_contracts.success.deleted": "合同删除成功。", "sales_contracts.error.create": "创建销售合同失败", "sales_contracts.error.update": "更新销售合同失败", "sales_contracts.error.delete": "删除合同失败。", "sales_contracts.empty": "未找到销售合同", "sales_contracts.empty.description": "创建您的第一个销售合同开始使用。", "purchase_contracts.title": "采购合同", "purchase_contracts.subtitle": "管理您公司的采购合同。", "purchase_contracts.add": "添加合同", "purchase_contracts.add.title": "创建采购合同", "purchase_contracts.add.description": "与您的供应商创建新的采购合同。", "purchase_contracts.edit.title": "编辑采购合同", "purchase_contracts.edit.description": "更新采购合同信息。", "purchase_contracts.delete.title": "您确定吗？", "purchase_contracts.delete.description": "这将永久删除该合同。此操作无法撤销。", "purchase_contracts.form.number": "合同编号", "purchase_contracts.form.supplier": "供应商", "purchase_contracts.form.template": "模板", "purchase_contracts.form.currency": "货币", "purchase_contracts.form.items": "明细", "purchase_contracts.table.contract_number": "合同编号", "purchase_contracts.table.number": "合同编号", "purchase_contracts.table.supplier": "供应商", "purchase_contracts.table.date": "日期", "purchase_contracts.table.currency": "货币", "purchase_contracts.table.items": "明细", "purchase_contracts.table.total": "总计", "purchase_contracts.table.status": "状态", "purchase_contracts.table.created": "创建时间", "purchase_contracts.table.actions": "操作", "purchase_contracts.search_placeholder": "搜索合同...", "purchase_contracts.success.created": "采购合同创建成功！", "purchase_contracts.success.updated": "采购合同更新成功！", "purchase_contracts.success.deleted": "合同删除成功。", "purchase_contracts.error.create": "创建采购合同失败", "purchase_contracts.error.update": "更新采购合同失败", "purchase_contracts.error.delete": "删除合同失败。", "purchase_contracts.empty": "未找到采购合同", "purchase_contracts.empty.description": "创建您的第一个采购合同开始使用。", "suppliers.title": "供应商管理", "suppliers.subtitle": "管理您的供应商数据库和关系", "suppliers.add": "添加供应商", "suppliers.add.title": "添加新供应商", "suppliers.add.description": "为您的业务创建新的供应商记录。", "suppliers.edit.title": "编辑供应商", "suppliers.edit.description": "更新供应商信息。", "suppliers.delete.title": "删除供应商", "suppliers.delete.description": "您确定要删除此供应商吗？此操作无法撤销。", "suppliers.delete.confirmation": "这将永久删除供应商\"{name}\"并从我们的服务器中删除其数据。", "suppliers.form.name": "供应商名称", "suppliers.form.contact_name": "联系人", "suppliers.form.contact_phone": "电话号码", "suppliers.form.contact_email": "邮箱地址", "suppliers.form.address": "地址", "suppliers.form.bank": "银行详情", "suppliers.form.tax_id": "税务编号", "suppliers.form.status": "状态", "suppliers.table.company_name": "公司名称", "suppliers.table.name": "供应商名称", "suppliers.table.contact_person": "联系人", "suppliers.table.phone": "电话", "suppliers.table.email": "邮箱", "suppliers.table.address": "地址", "suppliers.table.bank": "银行详情", "suppliers.table.status": "状态", "suppliers.table.actions": "操作", "suppliers.success.created": "供应商创建成功！", "suppliers.success.updated": "供应商更新成功！", "suppliers.success.deleted": "供应商删除成功！", "suppliers.error.create": "创建供应商失败", "suppliers.error.update": "更新供应商失败", "suppliers.error.delete": "删除供应商失败", "suppliers.empty": "未找到供应商", "suppliers.empty.description": "添加您的第一个供应商开始使用。", "suppliers.view.subtitle": "查看供应商详情和相关采购合同", "suppliers.view.supplier_info": "供应商信息", "suppliers.view.supplier_info_desc": "基本供应商详情和联系信息", "suppliers.view.purchase_contracts": "采购合同", "suppliers.view.purchase_contracts_desc": "与此供应商相关的采购合同", "suppliers.view.no_contracts": "无采购合同", "suppliers.view.no_contracts_desc": "此供应商尚未有任何采购合同。", "suppliers.view.create_contract": "创建采购合同", "suppliers.view.view_all_contracts": "查看所有合同", "samples.title": "样品管理", "samples.subtitle": "管理产品样品和审批流程", "samples.add": "新建样品", "samples.refresh": "刷新", "samples.loading": "正在加载样品...", "samples.found": "找到 {count} 个样品", "samples.cards.outbound.title": "📤 出库样品", "samples.cards.outbound.description": "我们发送给客户", "samples.cards.inbound.title": "📥 入库样品", "samples.cards.inbound.description": "来自客户和供应商", "samples.cards.internal.title": "🏭 内部样品", "samples.cards.internal.description": "研发和测试", "samples.cards.quality.title": "🧪 质量流水线", "samples.cards.quality.description": "等待质检审批", "samples.table.sample": "样品", "samples.table.direction": "方向", "samples.table.purpose": "用途", "samples.table.relationship": "业务关系", "samples.table.product": "产品", "samples.table.status": "状态", "samples.table.priority": "优先级", "samples.table.created": "创建时间", "samples.table.actions": "操作", "samples.table.empty": "未找到样品。创建您的第一个样品开始使用。", "samples.actions.view": "查看", "samples.actions.edit": "编辑", "samples.actions.delete": "删除", "samples.actions.approve": "审批", "samples.actions.reject": "拒绝", "samples.filters.search": "按名称、编码或备注搜索样品...", "samples.filters.status.all": "所有状态", "samples.filters.status.pending": "待审批", "samples.filters.status.approved": "已审批", "samples.filters.status.rejected": "已拒绝", "samples.filters.type.all": "所有类型", "samples.filters.type.development": "开发", "samples.filters.type.production": "生产", "samples.filters.type.quality": "质量", "samples.filters.type.prototype": "原型", "samples.filters.direction.all": "所有方向", "samples.filters.direction.outbound": "出库", "samples.filters.direction.inbound": "入库", "samples.filters.direction.internal": "内部", "samples.filters.advanced": "高级", "samples.filters.clear": "清除筛选", "samples.delete.success.title": "样品已删除", "samples.delete.success.description": "样品 '{name}' 已成功删除", "samples.delete.error.title": "删除失败", "samples.delete.error.description": "删除样品失败，请重试。", "samples.delete.dialog.title": "删除样品", "samples.delete.dialog.description": "您确定要删除此样品吗？此操作无法撤销。", "samples.delete.dialog.warning": "此操作是永久性的，无法撤销。", "samples.delete.dialog.confirm": "删除样品", "samples.delete.dialog.deleting": "删除中...", "samples.fields.code": "样品编码", "samples.fields.name": "样品名称", "samples.fields.date": "日期", "samples.fields.status": "状态", "samples.fields.priority": "优先级", "samples.fields.type": "样品类型", "samples.fields.direction": "方向", "samples.fields.purpose": "用途", "samples.fields.customer": "客户", "samples.fields.supplier": "供应商", "samples.fields.product": "产品", "samples.fields.quantity": "数量", "samples.fields.unit": "单位", "samples.fields.cost": "成本", "samples.fields.currency": "货币", "samples.fields.delivery_date": "交付日期", "samples.fields.specifications": "技术规格", "samples.fields.quality_requirements": "质量要求", "samples.fields.notes": "备注", "samples.create.title": "创建样品", "samples.create.description": "创建新的样品记录用于跟踪和审批", "samples.create.basic_info": "基本信息", "samples.create.workflow": "样品流程", "samples.create.relationships": "业务关系", "samples.create.specifications": "规格和详情", "samples.create.success.title": "样品已创建", "samples.create.success.description": "样品已成功创建", "samples.create.error.title": "创建失败", "samples.create.error.description": "创建样品失败，请重试。", "samples.create.cancel": "取消", "samples.create.save": "创建样品", "samples.create.saving": "创建中...", "samples.view.back": "返回样品", "samples.view.pending_request": "待处理请求", "samples.view.approved": "已审批", "samples.view.rejected": "已拒绝", "samples.view.edit": "编辑", "samples.view.sample_info": "样品信息", "samples.view.sample_type": "样品类型", "samples.view.priority": "优先级", "samples.view.sample_date": "样品日期", "samples.view.quantity": "数量", "samples.view.delivery_date": "交付日期", "samples.view.cost": "成本", "samples.view.relationships": "业务关系", "samples.view.customer": "客户", "samples.view.contact": "联系人", "samples.view.email": "邮箱", "samples.view.product": "产品", "samples.view.sku": "SKU", "samples.view.supplier": "供应商", "samples.view.specifications": "规格和备注", "samples.view.technical_specs": "技术规格", "samples.view.quality_requirements": "质量要求", "samples.view.notes": "备注", "samples.view.approval_history": "审批历史", "samples.view.created": "已创建", "samples.view.revised": "已修订", "samples.view.pending": "待审批", "samples.view.revision_required": "需要修订", "samples.view.by_system": "由系统", "samples.view.by_current_user": "由当前用户", "samples.view.sample_created": "样品已创建并提交审批", "samples.view.sample_processed": "样品已处理", "samples.view.metadata": "元数据", "samples.view.created_date": "创建时间", "samples.view.approved_by": "审批人", "samples.view.approved_date": "审批日期", "samples.view.status.created": "已创建", "samples.view.status.pending": "待审批", "samples.view.status.approved": "已审批", "samples.view.status.rejected": "已拒绝", "samples.view.status.revised": "已修订", "samples.view.status.revision_required": "需要修订", "samples.view.actions.sample_created": "样品已创建并提交审批", "samples.view.actions.sample_processed": "样品已处理", "samples.view.actions.sample_approved": "样品已审批", "samples.view.actions.sample_rejected": "样品已拒绝", "samples.view.actions.revision_requested": "要求修订", "samples.view.by_system_on": "由系统于", "samples.view.by_current_user_on": "由当前用户于", "samples.view.by_user_on": "由 {user} 于", "samples.edit.title": "编辑样品", "samples.edit.description": "更新样品信息和规格", "samples.edit.loading": "正在加载样品数据...", "samples.edit.success.title": "样品已更新", "samples.edit.success.description": "样品已成功更新", "samples.edit.error.title": "更新失败", "samples.edit.error.description": "更新样品失败，请重试。", "samples.edit.error.load": "加载样品数据失败，请重试。", "samples.edit.back": "返回样品", "samples.edit.cancel": "取消", "samples.edit.save": "更新样品", "samples.edit.saving": "更新中...", "samples.edit.validation.name": "样品名称为必填项", "samples.edit.code.readonly": "样品编码无法更改", "samples.edit.basic_info": "基本信息", "samples.edit.basic_info_desc": "更新基本样品信息", "samples.edit.sample_code": "样品编码", "samples.edit.sample_name": "样品名称", "samples.edit.sample_name_placeholder": "输入样品名称", "samples.edit.sample_type": "样品类型", "samples.edit.priority": "优先级", "samples.edit.relationships": "业务关系", "samples.edit.relationships_desc": "将此样品与客户、产品和供应商关联", "samples.edit.customer": "客户", "samples.edit.customer_placeholder": "搜索客户...", "samples.edit.product": "产品", "samples.edit.product_placeholder": "搜索产品...", "samples.edit.supplier": "供应商", "samples.edit.supplier_placeholder": "搜索供应商...", "samples.edit.specifications": "规格和详情", "samples.edit.specifications_desc": "添加技术规格和其他详情", "samples.edit.quantity": "数量", "samples.edit.unit": "单位", "samples.edit.cost": "成本", "samples.edit.currency": "货币", "samples.edit.delivery_date": "交付日期", "samples.edit.technical_specs": "技术规格", "samples.edit.technical_specs_placeholder": "输入技术规格...", "samples.edit.quality_requirements": "质量要求", "samples.edit.quality_requirements_placeholder": "输入质量要求...", "samples.edit.notes": "备注", "samples.edit.notes_placeholder": "输入其他备注...", "samples.edit.search.no_results": "未找到结果", "samples.edit.search.add_new_customer": "添加新客户", "samples.edit.search.add_new_product": "添加新产品", "samples.edit.search.add_new_supplier": "添加新供应商", "samples.edit.search.loading": "加载中...", "samples.edit.search.type_to_search": "输入以搜索...", "inventory.title": "库存管理", "inventory.subtitle": "管理库存水平、入库和出库操作", "inventory.tabs.inbound": "入库", "inventory.tabs.outbound": "出库", "inventory.tabs.stock": "库存", "inventory.tabs.transactions": "交易记录", "inventory.inbound.form.product": "产品", "inventory.inbound.form.qty": "数量", "inventory.inbound.form.location": "位置", "inventory.inbound.form.ref": "参考", "inventory.inbound.button": "收货", "inventory.outbound.form.product": "产品", "inventory.outbound.form.qty": "数量", "inventory.outbound.form.location": "位置", "inventory.outbound.form.ref": "参考", "inventory.outbound.button": "发货", "inventory.stock.loading": "正在加载库存...", "inventory.stock.error": "加载库存数据失败", "inventory.stock.retry": "重试", "inventory.stock.table.lot": "批次", "inventory.stock.table.location": "位置", "inventory.transactions.title": "交易记录", "inventory.transactions.desc": "库存移动历史", "inventory.transaction_forms": "交易表单", "inventory.transaction_history": "交易历史", "inventory.transaction_success": "交易成功", "inventory.transaction_error": "交易失败", "inventory.inbound": "入库", "inventory.outbound": "出库", "inventory.transfer": "调拨", "inventory.adjustment": "调整", "inventory.product": "产品", "inventory.quantity": "数量", "inventory.location": "位置", "inventory.source_location": "源位置", "inventory.destination_location": "目标位置", "inventory.adjustment_quantity": "调整数量", "inventory.reason_code": "原因代码", "inventory.notes": "备注", "inventory.reference": "参考", "inventory.status": "状态", "inventory.date": "日期", "inventory.type": "类型", "inventory.select_product": "选择产品", "inventory.select_location": "选择位置", "inventory.reference_placeholder": "采购单/销售单号、收货单号等", "inventory.notes_placeholder": "附加备注或说明", "inventory.transfer_notes_placeholder": "调拨原因", "inventory.adjustment_notes_placeholder": "说明调整原因", "inventory.positive_negative_allowed": "允许正负值", "inventory.process_inbound": "处理入库", "inventory.process_outbound": "处理出库", "inventory.process_transfer": "处理调拨", "inventory.process_adjustment": "处理调整", "inventory.adjustment_warning": "警告", "inventory.adjustment_warning_text": "调整会直接修改库存数量。请确保有适当的授权和文档记录。", "inventory.search_transactions": "搜索交易记录...", "inventory.filter_by_type": "按类型筛选", "inventory.filter_by_location": "按位置筛选", "inventory.all_types": "所有类型", "inventory.all_locations": "所有位置", "inventory.no_transactions": "未找到交易记录", "inventory.showing_transactions": "显示 {count} 条，共 {total} 条交易记录", "inventory.fetch_error": "数据加载失败", "inventory.adjustment_notes": "调整备注", "inventory.reason_receipt": "收货", "inventory.reason_shipment": "发货", "inventory.reason_transfer": "调拨", "inventory.reason_cycle_count": "盘点", "inventory.reason_damage": "损坏", "inventory.reason_obsolete": "报废", "inventory.reason_adjustment": "调整", "inventory.reason_return": "退货", "inventory.reason_sample": "样品", "inventory.status_pending": "待处理", "inventory.status_approved": "已批准", "inventory.status_rejected": "已拒绝", "inventory.finishedGoods": "成品库存", "inventory.rawMaterials": "原材料库存", "inventory.totalValue": "总价值", "company.profile.title": "公司资料", "company.profile.subtitle": "管理您的公司信息和设置", "company.profile.not_found": "未找到公司资料", "company.profile.not_found_desc": "看起来您还没有完成公司资料设置。", "company.profile.complete_setup": "完成公司设置", "company.profile.complete": "完整", "company.profile.incomplete": "不完整", "company.profile.edit": "编辑资料", "company.profile.save": "保存更改", "company.profile.cancel": "取消", "company.profile.tabs.basic": "基本信息", "company.profile.tabs.business": "业务详情", "company.profile.tabs.banking": "银行信息", "company.profile.tabs.export": "出口贸易", "company.profile.basic.description": "您公司的基本联系方式和地址信息", "company.profile.business.description": "业务注册和运营信息", "company.profile.banking.description": "银行和金融账户详情", "company.profile.export.description": "出口许可和贸易合规信息", "company.profile.success.updated": "公司资料更新成功！", "company.profile.error.update": "更新资料失败", "company.field.name": "公司名称", "company.field.legal_name": "法定公司名称", "company.field.email": "邮箱地址", "company.field.phone": "电话号码", "company.field.website": "网站", "company.field.country": "国家", "company.field.address_line1": "街道地址", "company.field.address_line2": "地址第二行", "company.field.city": "城市", "company.field.state_province": "省/州", "company.field.postal_code": "邮政编码", "company.field.industry": "行业", "company.field.business_type": "业务类型", "company.field.employee_count": "员工数量", "company.field.annual_revenue": "年收入", "company.field.registration_number": "注册号", "company.field.tax_id": "税务编号", "company.field.vat_number": "增值税号", "company.field.bank_name": "银行名称", "company.field.bank_account": "账户号码", "company.field.bank_swift": "SWIFT/BIC代码", "company.field.bank_address": "银行地址", "company.field.export_license": "出口许可证", "company.field.customs_code": "海关代码", "company.field.preferred_incoterms": "首选贸易条款", "company.field.preferred_payment_terms": "首选付款条件", "quality.title": "质量控制", "quality.subtitle": "管理质量检验和证书", "quality.metrics.title": "质量指标", "quality.metrics.pass_rate": "合格率", "quality.metrics.total_inspections": "总检验数", "quality.metrics.pending": "待检验数", "quality.metrics.defect_rate": "缺陷率", "quality.inspections.title": "最近检验", "quality.inspections.subtitle": "最新质量检验结果", "quality.defects.title": "缺陷跟踪", "quality.defects.subtitle": "跟踪和管理质量缺陷", "contract_templates.title": "合同模板", "contract_templates.subtitle": "管理销售和采购协议的可重用合同模板", "contract_templates.add": "添加模板", "contract_templates.table.name": "模板名称", "contract_templates.table.type": "类型", "contract_templates.table.language": "语言", "contract_templates.table.version": "版本", "contract_templates.table.status": "状态", "contract_templates.table.actions": "操作", "contracts.form.number": "合同编号", "contracts.form.customer": "客户", "contracts.form.supplier": "供应商", "contracts.form.currency": "货币", "contracts.form.template": "模板", "contracts.form.items": "合同项目", "contracts.form.product": "产品", "contracts.form.quantity": "数量", "contracts.form.price": "价格", "contracts.form.total": "总计", "contracts.form.add_item": "添加项目", "contracts.form.remove_item": "移除", "contracts.form.contract_info": "合同信息", "contracts.form.contract_info_desc": "基本合同详情和客户信息", "contracts.form.items_section": "合同项目", "contracts.form.items_section_desc": "此合同的产品和数量", "contracts.form.template_section": "合同模板", "contracts.form.template_section_desc": "选择合同文档生成模板", "contracts.view.back": "返回合同", "contracts.view.back_sales": "返回销售合同", "contracts.view.back_purchase": "返回采购合同", "contracts.view.edit_contract": "编辑合同", "contracts.view.export_pdf": "导出PDF", "contracts.view.loading": "正在加载合同文档...", "contracts.view.no_document": "无可用合同文档", "contracts.view.contract_summary": "合同摘要", "contracts.view.contract_document": "合同文档", "contracts.view.customer": "客户", "contracts.view.supplier": "供应商", "contracts.view.contract_date": "合同日期", "contracts.view.total_value": "合同总额", "contracts.view.template": "模板", "contracts.view.no_email": "无邮箱", "contracts.view.items_count": "{count} 项", "contracts.view.sales_template": "销售模板", "contracts.view.purchase_template": "采购模板", "contracts.edit.title_sales": "编辑销售合同", "contracts.edit.title_purchase": "编辑采购合同", "contracts.edit.subtitle": "更新合同 {number} 的详细信息", "contracts.edit.back": "返回合同", "contracts.edit.template_optional": "合同模板（可选）", "contracts.edit.template_desc": "销售合同模板", "contracts.edit.preview": "预览", "contracts.edit.items_title": "合同项目", "contracts.edit.add_item": "添加项目", "contracts.edit.product": "产品", "contracts.edit.quantity": "数量", "contracts.edit.unit_price": "单价", "contracts.edit.sku_label": "SKU: {sku}", "contracts.edit.unit_label": "单位: {unit}", "contracts.create.title_sales": "创建销售合同", "contracts.create.title_purchase": "创建采购合同", "contracts.create.subtitle_sales": "输入新销售合同的详细信息", "contracts.create.subtitle_purchase": "输入新采购合同的详细信息", "contracts.create.back_sales": "销售合同", "contracts.create.back_purchase": "采购合同", "contracts.create.add_new": "添加新合同", "contracts.create.contract_info": "合同信息", "contracts.create.contract_info_desc": "基本合同详情和客户信息", "contracts.create.contract_info_desc_purchase": "基本合同详情和供应商信息", "contracts.create.number": "合同编号", "contracts.create.number_placeholder": "例如：PC-2025-001", "contracts.create.supplier": "供应商", "contracts.create.supplier_placeholder": "选择供应商...", "contracts.create.customer": "客户", "contracts.create.customer_placeholder": "选择客户...", "contracts.create.currency": "货币", "contracts.create.currency_placeholder": "例如：USD", "contracts.create.template": "合同模板（可选）", "contracts.create.template_placeholder": "选择模板...", "contracts.create.items": "合同项目", "contracts.create.items_desc": "为此合同添加产品和数量", "contracts.create.add_item": "添加项目", "contracts.create.remove_item": "删除项目", "contracts.create.product": "产品", "contracts.create.product_placeholder": "选择产品...", "contracts.create.quantity": "数量", "contracts.create.quantity_placeholder": "0", "contracts.create.unit_price": "单价", "contracts.create.unit_price_placeholder": "0.00", "contracts.create.total": "总计", "contracts.create.cancel": "取消", "contracts.create.create": "创建合同", "contracts.create.creating": "创建中...", "contracts.create.success": "合同创建成功！", "contracts.create.error": "创建合同失败", "contracts.create.summary": "合同摘要", "contracts.create.summary_desc": "查看合同总价值和详细信息", "contracts.create.items_count": "项目数量:", "contracts.create.currency_label": "货币:", "contracts.create.total_value": "合同总价值:", "contracts.templates.page_title": "合同模板", "contracts.templates.page_desc": "管理销售和采购协议的合同模板", "contracts.templates.sales_section": "销售合同模板", "contracts.templates.sales_desc": "创建和管理销售合同模板", "contracts.templates.purchase_section": "采购合同模板", "contracts.templates.purchase_desc": "创建和管理采购合同模板", "contracts.templates.template_name": "模板名称", "contracts.templates.currency": "货币", "contracts.templates.payment_terms": "付款条款", "contracts.templates.delivery_terms": "交付条款", "contracts.templates.template_content": "模板内容", "contracts.templates.content_placeholder": "输入合同模板内容，使用占位符如 {{customer_name}}、{{product_name}} 等", "contracts.templates.content_placeholder_purchase": "输入合同模板内容，使用占位符如 {{supplier_name}}、{{material_name}} 等", "contracts.templates.create_template": "创建模板", "contracts.templates.existing_templates": "现有模板", "contracts.templates.name": "名称", "contracts.templates.actions": "操作", "contracts.templates.payment_30_days": "30天", "contracts.templates.payment_60_days": "60天", "contracts.templates.delivery_fob": "FOB", "contracts.templates.delivery_cif": "CIF", "contracts.templates.delivery_exw": "EXW", "contract_templates.sales.title": "销售合同模板", "contract_templates.sales.description": "创建和管理销售合同模板", "contract_templates.sales.sample": "示例模板", "contract_templates.sales.sample_title": "专业销售合同模板", "contract_templates.sales.sample_desc": "复制此专业模板并粘贴到下面的模板内容字段中。", "contract_templates.purchase.title": "采购合同模板", "contract_templates.purchase.description": "创建和管理采购合同模板", "contract_templates.purchase.sample": "示例模板", "contract_templates.purchase.sample_title": "专业采购合同模板", "contract_templates.purchase.sample_desc": "复制此专业模板并粘贴到下面的模板内容字段中。", "quality.attachments.documents.title": "文档附件", "quality.attachments.documents.upload": "上传文档", "quality.attachments.documents.formats": "支持格式：PDF、DOC、DOCX、XLS、XLSX、TXT", "quality.attachments.documents.none": "无文档附件", "quality.attachments.photos.title": "照片附件", "quality.attachments.photos.upload": "上传照片", "quality.attachments.photos.formats": "支持格式：JPG、PNG、GIF、WebP", "quality.attachments.photos.none": "无照片附件", "quality.attachments.preview": "预览", "quality.attachments.download": "下载", "quality.attachments.remove": "删除", "quality.attachments.uploading": "正在上传文件...", "quality.attachments.upload_success": "上传成功", "quality.attachments.upload_success_desc": "个文件上传成功", "quality.attachments.download_success": "下载完成", "quality.attachments.download_success_desc": "下载成功", "quality.attachments.download_failed": "下载失败", "quality.attachments.download_failed_desc": "下载文件失败，请重试。", "quality.attachments.remove_success": "文件已删除", "quality.attachments.remove_success_desc": "文件删除成功", "quality.attachments.remove_failed": "删除失败", "quality.attachments.remove_failed_desc": "删除文件失败，请重试。", "quality.status": "质量状态", "quality.status.pending": "待处理", "quality.status.approved": "已通过", "quality.status.quarantined": "已隔离", "quality.status.rejected": "已拒绝", "workOrder.title": "生产工单", "workOrder.number": "工单编号", "workOrder.status.completed": "已完成", "workOrder.status.pending": "待开始", "workOrder.status.in-progress": "进行中", "products.form.quality_requirements": "质量要求", "products.form.inspection_required": "需要质量检验", "products.form.inspection_required_desc": "如果产品需要质量检验才能通过，请启用此选项", "products.form.quality_tolerance": "质量公差", "products.form.quality_notes": "质量备注", "products.form.quality_notes_placeholder": "输入具体的质量要求、标准或检验标准...", "products.quality.not_required": "无需检验", "products.success.updated_desc": "产品质量要求已成功更新。", "products.success.created_desc": "已成功创建具有质量要求的产品。", "work_orders.quality_gate.title": "需要质量审批", "work_orders.quality_gate.description": "此工单需要完成所有必需的质量检验审批后才能完成。", "work_orders.quality_gate.work_order_info": "工单信息", "work_orders.quality_gate.inspections_status": "质量检验状态", "work_orders.quality_gate.no_inspections": "未找到质量检验记录。可能需要创建检验。", "work_orders.quality_gate.completion_status": "完成状态", "work_orders.quality_gate.can_complete": "所有质量要求已满足。工单可以完成。", "work_orders.quality_gate.cannot_complete": "完成前需要质量审批。", "work_orders.quality_gate.pending_inspections": "待处理检验", "work_orders.quality_gate.complete_inspections_first": "请先完成所有待处理的质量检验。", "work_orders.quality_gate.go_to_quality_control": "前往质量控制", "work_orders.quality_gate.complete_work_order": "完成工单", "quality.inspector": "检验员", "quality.inspection_types.final": "最终检验", "quality.inspection_types.incoming": "来料检验", "quality.inspection_types.in_process": "过程检验", "quality.status.passed": "通过", "quality.status.failed": "失败", "common.processing": "处理中..."}