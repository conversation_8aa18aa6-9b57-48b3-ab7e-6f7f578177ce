{"inventory.inbound.title": "Inbound", "inventory.inbound.desc": "Receive inventory", "inventory.outbound.title": "Outbound", "inventory.outbound.desc": "Ship inventory", "inventory.stock.title": "Stock", "inventory.stock.desc": "Current inventory levels", "inventory.stock.empty": "No stock lots.", "inventory.txns.title": "Stock Transactions", "inventory.txns.desc": "FIFO is applied when shipping.", "inventory.txns.empty": "No transactions.", "inventory.title": "Inventory Management", "inventory.subtitle": "Manage stock levels, inbound and outbound operations", "inventory.tabs.inbound": "Inbound", "inventory.tabs.outbound": "Outbound", "inventory.tabs.stock": "Stock", "inventory.tabs.transactions": "Transactions", "inventory.inbound.form.product": "Product", "inventory.inbound.form.qty": "Quantity", "inventory.inbound.form.location": "Location", "inventory.inbound.form.ref": "Reference", "inventory.inbound.button": "Receive", "inventory.outbound.form.product": "Product", "inventory.outbound.form.qty": "Quantity", "inventory.outbound.form.location": "Location", "inventory.outbound.form.ref": "Reference", "inventory.outbound.button": "Ship", "inventory.stock.loading": "Loading inventory...", "inventory.stock.error": "Failed to load inventory data", "inventory.stock.retry": "Try again", "inventory.stock.table.lot": "Lot", "inventory.stock.table.location": "Location", "inventory.transactions.title": "Transactions", "inventory.transactions.desc": "Inventory movement history", "inventory.transaction_forms": "Transaction Forms", "inventory.transaction_history": "Transaction History", "inventory.transaction_success": "Transaction Successful", "inventory.transaction_error": "Transaction Failed", "inventory.inbound": "Inbound", "inventory.outbound": "Outbound", "inventory.transfer": "Transfer", "inventory.adjustment": "Adjustment", "inventory.product": "Product", "inventory.quantity": "Quantity", "inventory.location": "Location", "inventory.source_location": "Source Location", "inventory.destination_location": "Destination Location", "inventory.adjustment_quantity": "Adjustment Quantity", "inventory.reason_code": "Reason Code", "inventory.notes": "Notes", "inventory.reference": "Reference", "inventory.status": "Status", "inventory.date": "Date", "inventory.type": "Type", "inventory.select_product": "Select Product", "inventory.select_location": "Select Location", "inventory.reference_placeholder": "PO/SO number, receipt number, etc.", "inventory.notes_placeholder": "Additional notes or comments", "inventory.transfer_notes_placeholder": "Reason for transfer", "inventory.adjustment_notes_placeholder": "Explain the reason for adjustment", "inventory.positive_negative_allowed": "Positive or negative values allowed", "inventory.process_inbound": "Process Inbound", "inventory.process_outbound": "Process Outbound", "inventory.process_transfer": "Process Transfer", "inventory.process_adjustment": "Process Adjustment", "inventory.adjustment_warning": "Warning", "inventory.adjustment_warning_text": "Adjustments directly modify inventory quantities. Ensure proper authorization and documentation.", "inventory.search_transactions": "Search transactions...", "inventory.filter_by_type": "Filter by Type", "inventory.filter_by_location": "Filter by Location", "inventory.all_types": "All Types", "inventory.all_locations": "All Locations", "inventory.no_transactions": "No transactions found", "inventory.showing_transactions": "Showing {count} of {total} transactions", "inventory.fetch_error": "Failed to Load Data", "inventory.adjustment_notes": "Adjustment Notes", "inventory.reason_receipt": "Receipt", "inventory.reason_shipment": "Shipment", "inventory.reason_transfer": "Transfer", "inventory.reason_cycle_count": "Cycle Count", "inventory.reason_damage": "Damage", "inventory.reason_obsolete": "Obsolete", "inventory.reason_adjustment": "Adjustment", "inventory.reason_return": "Return", "inventory.reason_sample": "<PERSON><PERSON>", "inventory.status_pending": "Pending", "inventory.status_approved": "Approved", "inventory.status_rejected": "Rejected", "inventory.finishedGoods": "Finished Goods", "inventory.rawMaterials": "Raw Materials", "inventory.totalValue": "Total Value"}