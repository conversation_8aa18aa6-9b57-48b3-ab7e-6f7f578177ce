{"app.name": "FC-CHINA", "nav.group.overview": "Overview", "nav.group.master-data": "Master Data", "nav.group.sales-purchasing": "Sales & Purchasing", "nav.group.sales-process": "Sales Process", "nav.group.production": "Production", "nav.group.production-planning": "Production Planning", "nav.group.inventory-logistics": "Inventory & Logistics", "nav.group.quality-inventory": "Quality & Inventory", "nav.group.shipping-export": "Shipping & Export", "nav.group.export-trade": "Export & Trade", "nav.group.finance-reporting": "Finance & Reporting", "nav.group.settings": "Settings", "nav.group.administration": "Administration", "nav.item.dashboard": "Dashboard", "nav.item.customers": "Customers", "nav.item.suppliers": "Suppliers", "nav.item.products": "Products", "nav.item.samples": "<PERSON><PERSON>", "nav.item.sales-contracts": "Sales Contracts", "nav.item.purchase-contracts": "Purchase Contracts", "nav.item.contract-templates": "Contract Templates", "nav.item.work-orders": "Work Orders", "nav.item.bom-management": "Bill of Materials", "nav.item.quality-control": "Quality Control", "nav.item.mrp-planning": "MRP Planning", "nav.item.inventory": "Inventory", "nav.item.raw-materials": "Raw Materials", "nav.item.locations": "Locations", "nav.item.shipping": "Shipping", "nav.item.export-declarations": "Export Declarations", "nav.item.trade-compliance": "Trade Compliance", "nav.item.documentation": "Documentation", "nav.item.accounting": "Accounting", "nav.item.financial-dashboard": "Financial Dashboard", "nav.item.accounts-receivable": "Accounts Receivable", "nav.item.accounts-payable": "Accounts Payable", "nav.item.reports": "Reports", "nav.item.company-profile": "Company Profile", "cmd.placeholder": "Search or jump to...", "home.title": "Export Manufacturing ERP", "home.subtitle": "One place to manage master data, contracts, production, inventory, export declarations, and finance.", "home.quick.master": "Add Master Data", "home.quick.contract": "Create Contract", "home.quick.stock": "Record Stock", "home.quick.declaration": "New Declaration", "kpi.customers": "Customers", "kpi.products": "Products", "kpi.suppliers": "Suppliers", "kpi.contracts": "Contracts", "kpi.suppliers.desc": "Active suppliers", "kpi.contracts.desc": "Sales & purchase contracts", "kpi.onhand": "On-hand Stock (sum)", "kpi.openWos": "Open Work Orders", "dashboard.quick_actions.title": "Quick Actions", "dashboard.quick_actions.subtitle": "Common tasks to get you started", "dashboard.quick_actions.manage_customers": "Manage Customers", "dashboard.quick_actions.view_products": "View Products", "dashboard.quick_actions.create_contract": "Create Sales Contract", "dashboard.quick_actions.update_profile": "Update Company Profile", "dashboard.system_status.title": "System Status", "dashboard.system_status.subtitle": "Your ERP system setup progress", "dashboard.system_status.company_profile": "Company Profile", "dashboard.system_status.customer_database": "Customer Database", "dashboard.system_status.product_catalog": "Product Catalog", "dashboard.system_status.first_contract": "First Sales Contract", "dashboard.system_status.inventory_setup": "Inventory Setup", "dashboard.system_status.complete": "Complete", "dashboard.system_status.active": "Active", "dashboard.system_status.ready": "Ready", "dashboard.system_status.pending": "Pending", "dashboard.getting_started.title": "🚀 Getting Started", "dashboard.getting_started.subtitle": "Complete these steps to get the most out of your Manufacturing ERP system", "dashboard.getting_started.step1.title": "1. Company Setup", "dashboard.getting_started.step1.desc": "Your company profile is complete and ready for business.", "dashboard.getting_started.step2.title": "2. Create Your First Contract", "dashboard.getting_started.step2.desc": "Start generating revenue by creating your first sales contract.", "dashboard.getting_started.step2.action": "Create Contract", "dashboard.getting_started.step3.title": "3. Set Up Inventory", "dashboard.getting_started.step3.desc": "Track your raw materials and finished goods inventory.", "dashboard.getting_started.step3.action": "Setup Inventory", "sample.title": "Sample Data", "sample.desc": "This workspace includes realistic sample data to demonstrate relationships across modules.", "sample.reset": "Reset sample data", "sample.clear": "Clear sample data", "alert.db.title": "Database not configured", "alert.db.desc": "Set the DATABASE_URL (Neon Postgres). The app runs a safe, one-time auto-migration at first load and seeds a minimal dataset. You can also run scripts/sql/001_init.sql and 002_seed.sql manually.", "alert.prep.title": "Preparing your workspace", "alert.prep.desc": "The schema is initializing. Refresh in a few seconds.", "field.code": "Code", "field.name": "Name", "field.spec": "Specification", "field.moq": "MOQ", "field.inStock": "In Stock", "field.contact": "Contact", "field.incoterm": "Incoterm", "field.paymentTerm": "Payment Term", "field.address": "Address", "field.sku": "SKU", "field.unit": "Unit", "field.hsCode": "HS Code", "field.origin": "Origin", "field.packaging": "Packaging", "field.currency": "<PERSON><PERSON><PERSON><PERSON>", "field.number": "Number", "field.customer": "Customer", "field.supplier": "Supplier", "field.product": "Product", "field.qty": "Quantity", "field.price": "Price", "field.total": "Total", "field.woNumber": "WO Number", "field.salesContract": "Sales Contract", "field.location": "Location", "field.note": "Note", "field.reference": "Reference", "field.declarationNo": "Declaration No.", "field.amount": "Amount", "field.received": "Received", "field.paid": "Paid", "field.invoiceNo": "Invoice No.", "table.actions": "Actions", "table.noData": "No data available.", "action.add": "Add", "action.addItem": "Add Item", "action.remove": "Remove", "action.delete": "Delete", "action.create": "Create", "action.createContract": "Create Contract", "action.createPO": "Create PO", "action.createWO": "Create WO", "action.addInbound": "Receive", "action.addOutbound": "Ship", "action.createDeclaration": "Create Declaration", "action.submit": "Submit", "action.createAR": "Create AR", "action.createAP": "Create AP", "cta.open": "Open", "basic.samples.title": "Sample Management Center", "basic.samples.desc": "Code, name, specs, MOQ, available from stock.", "basic.customers.title": "Customers", "basic.customers.desc": "Store customer profiles and trading terms.", "basic.suppliers.title": "Suppliers", "basic.suppliers.desc": "Approved suppliers and contacts.", "basic.products.title": "Products/SKUs", "basic.products.desc": "Units, HS codes, origin, packaging.", "contracts.sales.title": "Sales Contracts", "contracts.sales.desc": "Create basic sales contracts from products and customers.", "contracts.sales.empty": "No sales contracts.", "contracts.purchase.title": "Purchase Contracts", "contracts.purchase.desc": "Issue POs against suppliers.", "contracts.purchase.empty": "No purchase contracts.", "production.title": "Work Orders", "production.desc": "Track routing and operation progress.", "production.empty": "No work orders.", "inventory.inbound.title": "Inbound", "inventory.inbound.desc": "Receive inventory", "inventory.outbound.title": "Outbound", "inventory.outbound.desc": "Ship inventory", "inventory.stock.title": "Stock", "inventory.stock.desc": "Current inventory levels", "inventory.stock.empty": "No stock lots.", "inventory.txns.title": "Stock Transactions", "inventory.txns.desc": "FIFO is applied when shipping.", "inventory.txns.empty": "No transactions.", "export.title": "Export Declarations", "export.desc": "Validate HS codes and track submission.", "export.empty": "No declarations.", "finance.title": "Finance & Accounting", "finance.description": "Manage invoices, payments, and financial reporting", "finance.summary.totalAR": "Total Receivables", "finance.summary.outstandingAR": "Outstanding AR", "finance.summary.totalAP": "Total Payables", "finance.summary.netCashFlow": "Net Cash Flow", "finance.kpis.coreMetrics": "Core Financial Metrics", "finance.kpis.totalRevenue": "Total Revenue (YTD)", "finance.kpis.totalExpenses": "Total Expenses (YTD)", "finance.kpis.profitLoss": "Profit/Loss (YTD)", "finance.kpis.netCashFlow": "Net Cash Flow", "finance.kpis.overdueIntelligence": "Overdue Intelligence", "finance.kpis.overdueAR": "Overdue Receivables", "finance.kpis.overdueAP": "Overdue Payables", "finance.kpis.manufacturingIntelligence": "Manufacturing Intelligence", "finance.kpis.contractProfitability": "Contract Profitability", "finance.kpis.avgCollectionDays": "Avg Collection Days", "finance.kpis.manufacturingMargin": "Manufacturing Margin", "finance.kpis.activeContracts": "Active Contracts", "finance.ar.title": "Accounts Receivable", "finance.ar.description": "Track AR invoices and aging with contract integration", "finance.ar.invoiceNumber": "Invoice Number", "finance.ar.customer": "Customer", "finance.ar.salesContract": "Sales Contract", "finance.ar.amount": "Amount", "finance.ar.received": "Received", "finance.ar.currency": "<PERSON><PERSON><PERSON><PERSON>", "finance.ar.status": "Status", "finance.ar.invoiceDate": "Invoice Date", "finance.ar.dueDate": "Due Date", "finance.ar.paymentTerms": "Payment Terms", "finance.ar.aging": "Aging", "finance.ar.contract": "Contract", "finance.ar.createInvoice": "Create AR Invoice", "finance.ar.noInvoices": "No AR invoices found", "finance.ap.title": "Accounts Payable", "finance.ap.description": "Track AP invoices and payments with contract integration", "finance.ap.invoiceNumber": "Invoice Number", "finance.ap.supplier": "Supplier", "finance.ap.purchaseContract": "Purchase Contract", "finance.ap.amount": "Amount", "finance.ap.paid": "Paid", "finance.ap.currency": "<PERSON><PERSON><PERSON><PERSON>", "finance.ap.status": "Status", "finance.ap.invoiceDate": "Invoice Date", "finance.ap.dueDate": "Due Date", "finance.ap.paymentTerms": "Payment Terms", "finance.ap.aging": "Aging", "finance.ap.contract": "Contract", "finance.ap.createInvoice": "Create AP Invoice", "finance.ap.noInvoices": "No AP invoices found", "finance.paymentTerms.tt": "TT (Telegraphic Transfer)", "finance.paymentTerms.dp": "DP (Documents against Payment)", "finance.paymentTerms.lc": "LC (Letter of Credit)", "finance.paymentTerms.deposit": "Deposit (Advance Payment)", "finance.paymentTerms.depositTT": "30% Deposit + 70% TT", "finance.paymentTerms.depositLC": "50% Deposit + 50% LC", "finance.status.depositReceived": "<PERSON><PERSON><PERSON><PERSON> Received", "finance.status.partialPaid": "Partial Paid", "finance.status.depositPaid": "<PERSON><PERSON><PERSON><PERSON>", "finance.ar.desc": "Track AR and aging.", "finance.ar.empty": "No AR invoices.", "finance.ap.desc": "Track AP and payments.", "finance.ap.empty": "No AP invoices.", "docs.plan.title": "Reference Mind Maps", "docs.plan.desc": "Original Chinese and translated English plan visuals used to guide implementation.", "module.master.title": "Master Data", "module.master.desc": "Samples, customers, suppliers, products, and trading terms.", "module.contracts.title": "Contracts", "module.contracts.desc": "Create sales contracts and purchase orders from SKUs.", "module.production.title": "Production", "module.production.desc": "Generate work orders, track routing and QC.", "module.inventory.title": "Inventory", "module.inventory.desc": "Inbound/outbound, FIFO, and current lots.", "module.export.title": "Export", "module.export.desc": "Build declarations and validate HS codes.", "module.export.declarations": "Declarations", "module.finance.title": "Finance", "module.finance.desc": "Track receivables and payables with basic aging.", "landing.login": "<PERSON><PERSON>", "landing.getStarted": "Get Started", "landing.learnMore": "Learn More", "landing.badge": "Trusted by 500+ Export Manufacturers", "landing.hero.title": "All-in-One ERP for Textile Manufacturing & Export", "landing.hero.subtitle": "Streamline your textile manufacturing operations from production to export. Manage customers, products, quality control, and international trade compliance in one platform.", "landing.features.noCredit": "No credit card required", "landing.features.freeTrial": "30-day free trial", "landing.features.quickSetup": "Setup in minutes", "landing.features.title": "Everything You Need for Export Manufacturing", "landing.features.subtitle": "From raw materials to international shipping, manage your entire manufacturing workflow", "landing.features.crm.title": "Customer & Supplier Management", "landing.features.crm.description": "Comprehensive CRM for managing international customers and suppliers with contact details, payment terms, and trade history.", "landing.features.inventory.title": "Product Catalog & Inventory", "landing.features.inventory.description": "Detailed product management with SKUs, specifications, quality standards, and real-time inventory tracking.", "landing.features.production.title": "Production Management", "landing.features.production.description": "Work order management, production scheduling, and real-time tracking from cutting to packaging.", "landing.features.quality.title": "Quality Control", "landing.features.quality.description": "Integrated quality inspections, defect tracking, and compliance management for export standards.", "landing.features.export.title": "Export Documentation", "landing.features.export.description": "Automated export declarations, shipping documents, and international trade compliance management.", "landing.features.analytics.title": "Analytics & Reporting", "landing.features.analytics.description": "Real-time dashboards, production analytics, and comprehensive reporting for business insights.", "landing.benefits.title": "Why Choose Our Manufacturing ERP?", "landing.benefits.subtitle": "Built specifically for export-oriented textile manufacturers", "landing.benefits.speed.title": "50% Faster Operations", "landing.benefits.speed.description": "Streamlined workflows reduce manual work and accelerate production cycles", "landing.benefits.compliance.title": "100% Compliance", "landing.benefits.compliance.description": "Built-in export compliance ensures all international trade requirements are met", "landing.benefits.global.title": "Global Ready", "landing.benefits.global.description": "Multi-currency, multi-language support for international business operations", "landing.cta.title": "Ready to Transform Your Manufacturing?", "landing.cta.subtitle": "Join hundreds of manufacturers who have streamlined their operations with our ERP solution", "landing.cta.button": "Start Your Free Trial Today", "landing.cta.features": "No credit card required • 30-day free trial • Setup in minutes", "landing.footer.copyright": "© 2024 FC-CHINA. Built for export manufacturers worldwide.", "landing.hero.mock.last30days": "Last 30 days", "landing.hero.mock.onTimeShipments": "On-Time Shipments", "dashboard.loading": "Loading dashboard...", "dashboard.error.title": "Dashboard Error", "dashboard.error.description": "This may indicate you need to complete your company profile setup.", "dashboard.error.retry": "Retry", "dashboard.welcome": "Welcome back", "dashboard.subtitle": "Here's what's happening with your manufacturing operations today.", "dashboard.stats.customers.title": "Total Customers", "dashboard.stats.customers.description": "Active customer accounts", "dashboard.stats.products.title": "Products", "dashboard.stats.products.description": "Products in catalog", "dashboard.stats.suppliers.title": "Suppliers", "dashboard.stats.suppliers.description": "Active suppliers", "dashboard.stats.contracts.title": "Active Contracts", "dashboard.stats.contracts.description": "Sales and purchase contracts", "common.loading": "Loading...", "common.error": "Error", "common.success": "Success", "common.cancel": "Cancel", "common.save": "Save", "common.delete": "Delete", "common.edit": "Edit", "common.view": "View", "common.add": "Add", "common.create": "Create", "common.update": "Update", "common.search": "Search", "common.filter": "Filter", "common.actions": "Actions", "common.status": "Status", "common.name": "Name", "common.description": "Description", "common.date": "Date", "common.created": "Created", "common.updated": "Updated", "common.active": "Active", "common.inactive": "Inactive", "common.yes": "Yes", "common.no": "No", "common.confirm": "Confirm", "common.close": "Close", "common.retry": "Retry", "common.optional": "Optional", "status.active": "Active", "status.inactive": "Inactive", "status.draft": "Draft", "status.approved": "Approved", "status.pending": "Pending", "status.completed": "Completed", "status.cancelled": "Cancelled", "status.done": "Done", "header.wo": "WO", "header.operations": "Operations", "header.type": "Type", "header.time": "Time", "header.lot": "Lot", "loading.inventory": "Loading inventory...", "loading.try_again": "Try again", "field.email": "Email", "field.phone": "Phone", "field.company": "Company", "field.status": "Status", "field.type": "Type", "field.date": "Date", "field.notes": "Notes", "field.contract": "Contract", "field.template": "Template", "customers.title": "Customers", "customers.subtitle": "Manage your customer database and relationships", "customers.add": "Add Customer", "customers.add.title": "Add New Customer", "customers.add.description": "Create a new customer record for your business.", "customers.edit.title": "Edit Customer", "customers.edit.description": "Update customer information.", "customers.delete.title": "Delete Customer", "customers.delete.description": "Are you sure you want to delete this customer? This action cannot be undone.", "customers.form.company_name": "Company Name", "customers.form.contact_name": "Contact Person", "customers.form.contact_phone": "Phone Number", "customers.form.contact_email": "Email Address", "customers.form.address": "Address", "customers.form.tax_id": "Tax ID", "customers.form.bank": "Bank Details", "customers.form.incoterm": "Incoterm", "customers.form.payment_term": "Payment Terms", "customers.form.status": "Status", "customers.table.company_name": "Company Name", "customers.table.contact_person": "Contact Person", "customers.table.phone": "Phone", "customers.table.email": "Email", "customers.table.address": "Address", "customers.table.incoterm": "Incoterm", "customers.table.payment_terms": "Payment Terms", "customers.table.status": "Status", "customers.table.actions": "Actions", "customers.success.created": "Customer created successfully!", "customers.success.updated": "Customer updated successfully!", "customers.success.deleted": "Customer deleted successfully!", "customers.error.create": "Failed to create customer", "customers.error.update": "Failed to update customer", "customers.error.delete": "Failed to delete customer", "customers.empty": "No customers found", "customers.empty.description": "Get started by adding your first customer.", "products.title": "Products", "products.subtitle": "Manage your product catalog and inventory", "products.add": "Add Product", "products.add.title": "Add New Product", "products.add.description": "Create a new product in your catalog.", "products.edit.title": "Edit Product", "products.edit.description": "Update product information.", "products.delete.title": "Delete Product", "products.delete.description": "Are you sure you want to delete this product? This action cannot be undone.", "products.form.name": "Product Name", "products.form.sku": "SKU", "products.form.unit": "Unit", "products.form.hs_code": "HS Code", "products.form.origin": "Origin", "products.form.package": "Package", "products.form.status": "Status", "products.form.pricing_information": "Pricing Information", "products.form.base_price": "Base Price", "products.form.cost_price": "Cost Price", "products.form.margin_percentage": "Margin %", "products.form.currency": "<PERSON><PERSON><PERSON><PERSON>", "products.table.name": "Product Name", "products.table.sku": "SKU", "products.table.unit": "Unit", "products.table.hs_code": "HS Code", "products.table.origin": "Origin", "products.table.package": "Package", "products.table.price": "Price", "products.table.status": "Status", "products.table.actions": "Actions", "products.success.created": "Product created successfully!", "products.success.updated": "Product Updated", "products.success.deleted": "Product deleted successfully!", "products.error.create": "Failed to create product", "products.error.update": "Update Failed", "products.error.delete": "Failed to delete product", "products.empty": "No products found", "products.empty.description": "Get started by adding your first product.", "products.view.title": "Product Details", "products.view.description": "View product information (read-only).", "sales_contracts.title": "Sales Contracts", "sales_contracts.subtitle": "Manage your company's sales contracts.", "sales_contracts.add": "Add Contract", "sales_contracts.add.title": "Create Sales Contract", "sales_contracts.add.description": "Create a new sales contract for your customer.", "sales_contracts.edit.title": "Edit Sales Contract", "sales_contracts.edit.description": "Update sales contract information.", "sales_contracts.delete.title": "Are you sure?", "sales_contracts.delete.description": "This will permanently delete the contract. This action cannot be undone.", "sales_contracts.form.number": "Contract Number", "sales_contracts.form.customer": "Customer", "sales_contracts.form.template": "Template", "sales_contracts.form.currency": "<PERSON><PERSON><PERSON><PERSON>", "sales_contracts.form.items": "Items", "sales_contracts.table.contract_number": "Contract Number", "sales_contracts.table.number": "Contract #", "sales_contracts.table.customer": "Customer", "sales_contracts.table.date": "Date", "sales_contracts.table.currency": "<PERSON><PERSON><PERSON><PERSON>", "sales_contracts.table.items": "Items", "sales_contracts.table.total": "Total", "sales_contracts.table.status": "Status", "sales_contracts.table.created": "Created", "sales_contracts.table.actions": "Actions", "sales_contracts.search_placeholder": "Search contracts...", "sales_contracts.success.created": "Sales contract created successfully!", "sales_contracts.success.updated": "Sales contract updated successfully!", "sales_contracts.success.deleted": "Contract deleted successfully.", "sales_contracts.error.create": "Failed to create sales contract", "sales_contracts.error.update": "Failed to update sales contract", "sales_contracts.error.delete": "Failed to delete contract.", "sales_contracts.empty": "No sales contracts found", "sales_contracts.empty.description": "Create your first sales contract to get started.", "purchase_contracts.title": "Purchase Contracts", "purchase_contracts.subtitle": "Manage your company's purchase contracts.", "purchase_contracts.add": "Add Contract", "purchase_contracts.add.title": "Create Purchase Contract", "purchase_contracts.add.description": "Create a new purchase contract with your supplier.", "purchase_contracts.edit.title": "Edit Purchase Contract", "purchase_contracts.edit.description": "Update purchase contract information.", "purchase_contracts.delete.title": "Are you sure?", "purchase_contracts.delete.description": "This will permanently delete the contract. This action cannot be undone.", "purchase_contracts.form.number": "Contract Number", "purchase_contracts.form.supplier": "Supplier", "purchase_contracts.form.template": "Template", "purchase_contracts.form.currency": "<PERSON><PERSON><PERSON><PERSON>", "purchase_contracts.form.items": "Items", "purchase_contracts.table.contract_number": "Contract Number", "purchase_contracts.table.number": "Contract #", "purchase_contracts.table.supplier": "Supplier", "purchase_contracts.table.date": "Date", "purchase_contracts.table.currency": "<PERSON><PERSON><PERSON><PERSON>", "purchase_contracts.table.items": "Items", "purchase_contracts.table.total": "Total", "purchase_contracts.table.status": "Status", "purchase_contracts.table.created": "Created", "purchase_contracts.table.actions": "Actions", "purchase_contracts.search_placeholder": "Search contracts...", "purchase_contracts.success.created": "Purchase contract created successfully!", "purchase_contracts.success.updated": "Purchase contract updated successfully!", "purchase_contracts.success.deleted": "Contract deleted successfully.", "purchase_contracts.error.create": "Failed to create purchase contract", "purchase_contracts.error.update": "Failed to update purchase contract", "purchase_contracts.error.delete": "Failed to delete contract.", "purchase_contracts.empty": "No purchase contracts found", "purchase_contracts.empty.description": "Create your first purchase contract to get started.", "suppliers.title": "Suppliers", "suppliers.subtitle": "Manage your supplier database and relationships", "suppliers.add": "Add Supplier", "suppliers.add.title": "Add New Supplier", "suppliers.add.description": "Create a new supplier record for your business.", "suppliers.edit.title": "Edit Supplier", "suppliers.edit.description": "Update supplier information.", "suppliers.delete.title": "Delete Supplier", "suppliers.delete.description": "Are you sure you want to delete this supplier? This action cannot be undone.", "suppliers.delete.confirmation": "This will permanently delete the supplier \"{name}\" and remove their data from our servers.", "suppliers.form.name": "Supplier Name", "suppliers.form.contact_name": "Contact Person", "suppliers.form.contact_phone": "Phone Number", "suppliers.form.contact_email": "Email Address", "suppliers.form.address": "Address", "suppliers.form.bank": "Bank Details", "suppliers.form.tax_id": "Tax ID", "suppliers.form.status": "Status", "suppliers.table.company_name": "Company Name", "suppliers.table.name": "Supplier Name", "suppliers.table.contact_person": "Contact Person", "suppliers.table.phone": "Phone", "suppliers.table.email": "Email", "suppliers.table.address": "Address", "suppliers.table.bank": "Bank Details", "suppliers.table.status": "Status", "suppliers.table.actions": "Actions", "suppliers.success.created": "Supplier created successfully!", "suppliers.success.updated": "Supplier updated successfully!", "suppliers.success.deleted": "Supplier deleted successfully!", "suppliers.error.create": "Failed to create supplier", "suppliers.error.update": "Failed to update supplier", "suppliers.error.delete": "Failed to delete supplier", "suppliers.empty": "No suppliers found", "suppliers.empty.description": "Get started by adding your first supplier.", "suppliers.view.subtitle": "View supplier details and related purchase contracts", "suppliers.view.supplier_info": "Supplier Information", "suppliers.view.supplier_info_desc": "Basic supplier details and contact information", "suppliers.view.purchase_contracts": "Purchase Contracts", "suppliers.view.purchase_contracts_desc": "Related purchase contracts with this supplier", "suppliers.view.no_contracts": "No Purchase Contracts", "suppliers.view.no_contracts_desc": "This supplier doesn't have any purchase contracts yet.", "suppliers.view.create_contract": "Create Purchase Contract", "suppliers.view.view_all_contracts": "View All Contracts", "samples.title": "Sample Management", "samples.subtitle": "Manage product samples and approval workflow", "samples.add": "New Sample", "samples.refresh": "Refresh", "samples.loading": "Loading samples...", "samples.found": "{count} samples found", "samples.cards.outbound.title": "📤 Outbound Samples", "samples.cards.outbound.description": "We send to customers", "samples.cards.inbound.title": "📥 Inbound Samples", "samples.cards.inbound.description": "From customers & suppliers", "samples.cards.internal.title": "🏭 Internal Samples", "samples.cards.internal.description": "R&D and testing", "samples.cards.quality.title": "🧪 Quality Pipeline", "samples.cards.quality.description": "Awaiting QC approval", "samples.table.sample": "<PERSON><PERSON>", "samples.table.direction": "Direction", "samples.table.purpose": "Purpose", "samples.table.relationship": "Relationship", "samples.table.product": "Product", "samples.table.status": "Status", "samples.table.priority": "Priority", "samples.table.created": "Created", "samples.table.actions": "Actions", "samples.table.empty": "No samples found. Create your first sample to get started.", "samples.actions.view": "View", "samples.actions.edit": "Edit", "samples.actions.delete": "Delete", "samples.actions.approve": "Approve", "samples.actions.reject": "Reject", "samples.filters.search": "Search samples by name, code, or notes...", "samples.filters.status.all": "All Statuses", "samples.filters.status.pending": "Pending", "samples.filters.status.approved": "Approved", "samples.filters.status.rejected": "Rejected", "samples.filters.type.all": "All Types", "samples.filters.type.development": "Development", "samples.filters.type.production": "Production", "samples.filters.type.quality": "Quality", "samples.filters.type.prototype": "Prototype", "samples.filters.direction.all": "All Directions", "samples.filters.direction.outbound": "Outbound", "samples.filters.direction.inbound": "Inbound", "samples.filters.direction.internal": "Internal", "samples.filters.advanced": "Advanced", "samples.filters.clear": "Clear Filters", "samples.delete.success.title": "Sample Deleted", "samples.delete.success.description": "Sample '{name}' has been successfully deleted", "samples.delete.error.title": "Delete Failed", "samples.delete.error.description": "Failed to delete sample. Please try again.", "samples.delete.dialog.title": "Delete Sample", "samples.delete.dialog.description": "Are you sure you want to delete this sample? This action cannot be undone.", "samples.delete.dialog.warning": "This action is permanent and cannot be undone.", "samples.delete.dialog.confirm": "Delete Sample", "samples.delete.dialog.deleting": "Deleting...", "samples.fields.code": "Sample Code", "samples.fields.name": "Sample Name", "samples.fields.date": "Date", "samples.fields.status": "Status", "samples.fields.priority": "Priority", "samples.fields.type": "Sample Type", "samples.fields.direction": "Direction", "samples.fields.purpose": "Purpose", "samples.fields.customer": "Customer", "samples.fields.supplier": "Supplier", "samples.fields.product": "Product", "samples.fields.quantity": "Quantity", "samples.fields.unit": "Unit", "samples.fields.cost": "Cost", "samples.fields.currency": "<PERSON><PERSON><PERSON><PERSON>", "samples.fields.delivery_date": "Delivery Date", "samples.fields.specifications": "Technical Specifications", "samples.fields.quality_requirements": "Quality Requirements", "samples.fields.notes": "Notes", "samples.create.title": "Create Sample", "samples.create.description": "Create a new sample record for tracking and approval", "samples.create.basic_info": "Basic Information", "samples.create.workflow": "Sample Workflow", "samples.create.relationships": "Business Relationships", "samples.create.specifications": "Specifications & Details", "samples.create.success.title": "Sample Created", "samples.create.success.description": "Sample has been created successfully", "samples.create.error.title": "Creation Failed", "samples.create.error.description": "Failed to create sample. Please try again.", "samples.create.cancel": "Cancel", "samples.create.save": "Create Sample", "samples.create.saving": "Creating...", "samples.view.back": "Back to Samples", "samples.view.pending_request": "Pending Request", "samples.view.approved": "Approved", "samples.view.rejected": "Rejected", "samples.view.edit": "Edit", "samples.view.sample_info": "Sample Information", "samples.view.sample_type": "Sample Type", "samples.view.priority": "Priority", "samples.view.sample_date": "Sample Date", "samples.view.quantity": "Quantity", "samples.view.delivery_date": "Delivery Date", "samples.view.cost": "Cost", "samples.view.relationships": "Relationships", "samples.view.customer": "Customer", "samples.view.contact": "Contact", "samples.view.email": "Email", "samples.view.product": "Product", "samples.view.sku": "SKU", "samples.view.supplier": "Supplier", "samples.view.specifications": "Specifications & Notes", "samples.view.technical_specs": "Technical Specifications", "samples.view.quality_requirements": "Quality Requirements", "samples.view.notes": "Notes", "samples.view.approval_history": "Approval History", "samples.view.created": "Created", "samples.view.revised": "Revised", "samples.view.pending": "pending", "samples.view.revision_required": "revision_required", "samples.view.by_system": "by System", "samples.view.by_current_user": "by Current User", "samples.view.sample_created": "Sample created and submitted for approval", "samples.view.sample_processed": "Sample processed", "samples.view.metadata": "<PERSON><PERSON><PERSON>", "samples.view.created_date": "Created", "samples.view.approved_by": "Approved by", "samples.view.approved_date": "Approved Date", "samples.view.status.created": "Created", "samples.view.status.pending": "Pending", "samples.view.status.approved": "Approved", "samples.view.status.rejected": "Rejected", "samples.view.status.revised": "Revised", "samples.view.status.revision_required": "Revision Required", "samples.view.actions.sample_created": "Sample created and submitted for approval", "samples.view.actions.sample_processed": "Sample processed", "samples.view.actions.sample_approved": "<PERSON><PERSON> approved", "samples.view.actions.sample_rejected": "<PERSON><PERSON> rejected", "samples.view.actions.revision_requested": "Revision requested", "samples.view.by_system_on": "by System on", "samples.view.by_current_user_on": "by Current User on", "samples.view.by_user_on": "by {user} on", "samples.edit.title": "<PERSON>", "samples.edit.description": "Update sample information and specifications", "samples.edit.loading": "Loading sample data...", "samples.edit.success.title": "Sample Updated", "samples.edit.success.description": "Sample has been updated successfully", "samples.edit.error.title": "Update Failed", "samples.edit.error.description": "Failed to update sample. Please try again.", "samples.edit.error.load": "Failed to load sample data. Please try again.", "samples.edit.back": "Back to Sample", "samples.edit.cancel": "Cancel", "samples.edit.save": "Update Sample", "samples.edit.saving": "Updating...", "samples.edit.validation.name": "Sample name is required", "samples.edit.code.readonly": "Sample code cannot be changed", "samples.edit.basic_info": "Basic Information", "samples.edit.basic_info_desc": "Update the basic sample information", "samples.edit.sample_code": "Sample Code", "samples.edit.sample_name": "Sample Name", "samples.edit.sample_name_placeholder": "Enter sample name", "samples.edit.sample_type": "Sample Type", "samples.edit.priority": "Priority", "samples.edit.relationships": "Relationships", "samples.edit.relationships_desc": "Associate this sample with customers, products, and suppliers", "samples.edit.customer": "Customer", "samples.edit.customer_placeholder": "Search customers...", "samples.edit.product": "Product", "samples.edit.product_placeholder": "Search products...", "samples.edit.supplier": "Supplier", "samples.edit.supplier_placeholder": "Search suppliers...", "samples.edit.specifications": "Specifications & Details", "samples.edit.specifications_desc": "Add technical specifications and additional details", "samples.edit.quantity": "Quantity", "samples.edit.unit": "Unit", "samples.edit.cost": "Cost", "samples.edit.currency": "<PERSON><PERSON><PERSON><PERSON>", "samples.edit.delivery_date": "Delivery Date", "samples.edit.technical_specs": "Technical Specifications", "samples.edit.technical_specs_placeholder": "Enter technical specifications...", "samples.edit.quality_requirements": "Quality Requirements", "samples.edit.quality_requirements_placeholder": "Enter quality requirements...", "samples.edit.notes": "Notes", "samples.edit.notes_placeholder": "Enter additional notes...", "samples.edit.search.no_results": "No results found", "samples.edit.search.add_new_customer": "Add new customer", "samples.edit.search.add_new_product": "Add new product", "samples.edit.search.add_new_supplier": "Add new supplier", "samples.edit.search.loading": "Loading...", "samples.edit.search.type_to_search": "Type to search...", "inventory.title": "Inventory Management", "inventory.subtitle": "Manage stock levels, inbound and outbound operations", "inventory.tabs.inbound": "Inbound", "inventory.tabs.outbound": "Outbound", "inventory.tabs.stock": "Stock", "inventory.tabs.transactions": "Transactions", "inventory.inbound.form.product": "Product", "inventory.inbound.form.qty": "Quantity", "inventory.inbound.form.location": "Location", "inventory.inbound.form.ref": "Reference", "inventory.inbound.button": "Receive", "inventory.outbound.form.product": "Product", "inventory.outbound.form.qty": "Quantity", "inventory.outbound.form.location": "Location", "inventory.outbound.form.ref": "Reference", "inventory.outbound.button": "Ship", "inventory.stock.loading": "Loading inventory...", "inventory.stock.error": "Failed to load inventory data", "inventory.stock.retry": "Try again", "inventory.stock.table.lot": "Lot", "inventory.stock.table.location": "Location", "inventory.transactions.title": "Transactions", "inventory.transactions.desc": "Inventory movement history", "inventory.transaction_forms": "Transaction Forms", "inventory.transaction_history": "Transaction History", "inventory.transaction_success": "Transaction Successful", "inventory.transaction_error": "Transaction Failed", "inventory.inbound": "Inbound", "inventory.outbound": "Outbound", "inventory.transfer": "Transfer", "inventory.adjustment": "Adjustment", "inventory.product": "Product", "inventory.quantity": "Quantity", "inventory.location": "Location", "inventory.source_location": "Source Location", "inventory.destination_location": "Destination Location", "inventory.adjustment_quantity": "Adjustment Quantity", "inventory.reason_code": "Reason Code", "inventory.notes": "Notes", "inventory.reference": "Reference", "inventory.status": "Status", "inventory.date": "Date", "inventory.type": "Type", "inventory.select_product": "Select Product", "inventory.select_location": "Select Location", "inventory.reference_placeholder": "PO/SO number, receipt number, etc.", "inventory.notes_placeholder": "Additional notes or comments", "inventory.transfer_notes_placeholder": "Reason for transfer", "inventory.adjustment_notes_placeholder": "Explain the reason for adjustment", "inventory.positive_negative_allowed": "Positive or negative values allowed", "inventory.process_inbound": "Process Inbound", "inventory.process_outbound": "Process Outbound", "inventory.process_transfer": "Process Transfer", "inventory.process_adjustment": "Process Adjustment", "inventory.adjustment_warning": "Warning", "inventory.adjustment_warning_text": "Adjustments directly modify inventory quantities. Ensure proper authorization and documentation.", "inventory.search_transactions": "Search transactions...", "inventory.filter_by_type": "Filter by Type", "inventory.filter_by_location": "Filter by Location", "inventory.all_types": "All Types", "inventory.all_locations": "All Locations", "inventory.no_transactions": "No transactions found", "inventory.showing_transactions": "Showing {count} of {total} transactions", "inventory.fetch_error": "Failed to Load Data", "inventory.adjustment_notes": "Adjustment Notes", "inventory.reason_receipt": "Receipt", "inventory.reason_shipment": "Shipment", "inventory.reason_transfer": "Transfer", "inventory.reason_cycle_count": "Cycle Count", "inventory.reason_damage": "Damage", "inventory.reason_obsolete": "Obsolete", "inventory.reason_adjustment": "Adjustment", "inventory.reason_return": "Return", "inventory.reason_sample": "<PERSON><PERSON>", "inventory.status_pending": "Pending", "inventory.status_approved": "Approved", "inventory.status_rejected": "Rejected", "inventory.finishedGoods": "Finished Goods", "inventory.rawMaterials": "Raw Materials", "inventory.totalValue": "Total Value", "company.profile.title": "Company Profile", "company.profile.subtitle": "Manage your company information and settings", "company.profile.not_found": "No Company Profile Found", "company.profile.not_found_desc": "It looks like you haven't completed your company profile setup yet.", "company.profile.complete_setup": "Complete Company Setup", "company.profile.complete": "Complete", "company.profile.incomplete": "Incomplete", "company.profile.edit": "Edit Profile", "company.profile.save": "Save Changes", "company.profile.cancel": "Cancel", "company.profile.tabs.basic": "Basic Information", "company.profile.tabs.business": "Business Details", "company.profile.tabs.banking": "Banking", "company.profile.tabs.export": "Export & Trade", "company.profile.basic.description": "Your company's basic contact and address information", "company.profile.business.description": "Business registration and operational information", "company.profile.banking.description": "Banking and financial account details", "company.profile.export.description": "Export licensing and trade compliance information", "company.profile.success.updated": "Company profile updated successfully!", "company.profile.error.update": "Failed to update profile", "company.field.name": "Company Name", "company.field.legal_name": "Legal Company Name", "company.field.email": "Email Address", "company.field.phone": "Phone Number", "company.field.website": "Website", "company.field.country": "Country", "company.field.address_line1": "Street Address", "company.field.address_line2": "Address Line 2", "company.field.city": "City", "company.field.state_province": "State/Province", "company.field.postal_code": "Postal Code", "company.field.industry": "Industry", "company.field.business_type": "Business Type", "company.field.employee_count": "Employee Count", "company.field.annual_revenue": "Annual Revenue", "company.field.registration_number": "Registration Number", "company.field.tax_id": "Tax ID", "company.field.vat_number": "VAT Number", "company.field.bank_name": "Bank Name", "company.field.bank_account": "Account Number", "company.field.bank_swift": "SWIFT/BIC Code", "company.field.bank_address": "Bank Address", "company.field.export_license": "Export License", "company.field.customs_code": "Customs Code", "company.field.preferred_incoterms": "Preferred Incoterms", "company.field.preferred_payment_terms": "Preferred Payment Terms", "quality.title": "Quality Control", "quality.subtitle": "Manage quality inspections and certificates", "quality.metrics.title": "Quality Metrics", "quality.metrics.pass_rate": "Pass Rate", "quality.metrics.total_inspections": "Total Inspections", "quality.metrics.pending": "Pending Inspections", "quality.metrics.defect_rate": "Defect Rate", "quality.inspections.title": "Recent Inspections", "quality.inspections.subtitle": "Latest quality inspection results", "quality.defects.title": "Defect Tracking", "quality.defects.subtitle": "Track and manage quality defects", "contract_templates.title": "Contract Templates", "contract_templates.subtitle": "Manage reusable contract templates for sales and purchase agreements", "contract_templates.add": "Add Template", "contract_templates.table.name": "Template Name", "contract_templates.table.type": "Type", "contract_templates.table.language": "Language", "contract_templates.table.version": "Version", "contract_templates.table.status": "Status", "contract_templates.table.actions": "Actions", "contracts.form.number": "Contract Number", "contracts.form.customer": "Customer", "contracts.form.supplier": "Supplier", "contracts.form.currency": "<PERSON><PERSON><PERSON><PERSON>", "contracts.form.template": "Template", "contracts.form.items": "Contract Items", "contracts.form.product": "Product", "contracts.form.quantity": "Quantity", "contracts.form.price": "Price", "contracts.form.total": "Total", "contracts.form.add_item": "Add Item", "contracts.form.remove_item": "Remove", "contracts.form.contract_info": "Contract Information", "contracts.form.contract_info_desc": "Basic contract details and customer information", "contracts.form.items_section": "Contract Items", "contracts.form.items_section_desc": "Products and quantities for this contract", "contracts.form.template_section": "Contract Template", "contracts.form.template_section_desc": "Choose a template for contract document generation", "contracts.view.back": "Back to Contracts", "contracts.view.back_sales": "Back to Sales Contracts", "contracts.view.back_purchase": "Back to Purchase Contracts", "contracts.view.edit_contract": "Edit Contract", "contracts.view.export_pdf": "Export PDF", "contracts.view.loading": "Loading contract document...", "contracts.view.no_document": "No contract document available", "contracts.view.contract_summary": "Contract Summary", "contracts.view.contract_document": "Contract Document", "contracts.view.customer": "Customer", "contracts.view.supplier": "Supplier", "contracts.view.contract_date": "Contract Date", "contracts.view.total_value": "Total Value", "contracts.view.template": "Template", "contracts.view.no_email": "No email", "contracts.view.items_count": "{count} items", "contracts.view.sales_template": "sales template", "contracts.view.purchase_template": "purchase template", "contracts.edit.title_sales": "Edit Sales Contract", "contracts.edit.title_purchase": "Edit Purchase Contract", "contracts.edit.subtitle": "Update the details of contract {number}", "contracts.edit.back": "Back to Contracts", "contracts.edit.template_optional": "Contract Template (Optional)", "contracts.edit.template_desc": "sales contract template", "contracts.edit.preview": "Preview", "contracts.edit.items_title": "Contract Items", "contracts.edit.add_item": "Add Item", "contracts.edit.product": "Product", "contracts.edit.quantity": "Quantity", "contracts.edit.unit_price": "Unit Price", "contracts.edit.sku_label": "SKU: {sku}", "contracts.edit.unit_label": "Unit: {unit}", "contracts.create.title_sales": "Create Sales Contract", "contracts.create.title_purchase": "Create Purchase Contract", "contracts.create.subtitle_sales": "Enter the details for your new sales contract", "contracts.create.subtitle_purchase": "Enter the details for your new purchase contract", "contracts.create.back_sales": "Sales Contracts", "contracts.create.back_purchase": "Purchase Contracts", "contracts.create.add_new": "Add New Contract", "contracts.create.contract_info": "Contract Information", "contracts.create.contract_info_desc": "Basic contract details and customer information", "contracts.create.contract_info_desc_purchase": "Basic contract details and supplier information", "contracts.create.number": "Contract Number", "contracts.create.number_placeholder": "e.g., PC-2025-001", "contracts.create.supplier": "Supplier", "contracts.create.supplier_placeholder": "Select supplier...", "contracts.create.customer": "Customer", "contracts.create.customer_placeholder": "Select customer...", "contracts.create.currency": "<PERSON><PERSON><PERSON><PERSON>", "contracts.create.currency_placeholder": "e.g., USD", "contracts.create.template": "Contract Template (Optional)", "contracts.create.template_placeholder": "Select template...", "contracts.create.items": "Contract Items", "contracts.create.items_desc": "Add products and quantities for this contract", "contracts.create.add_item": "Add Item", "contracts.create.remove_item": "Remove Item", "contracts.create.product": "Product", "contracts.create.product_placeholder": "Select product...", "contracts.create.quantity": "Quantity", "contracts.create.quantity_placeholder": "0", "contracts.create.unit_price": "Unit Price", "contracts.create.unit_price_placeholder": "0.00", "contracts.create.total": "Total", "contracts.create.cancel": "Cancel", "contracts.create.create": "Create Contract", "contracts.create.creating": "Creating...", "contracts.create.success": "Contract created successfully!", "contracts.create.error": "Failed to create contract", "contracts.create.summary": "Contract Summary", "contracts.create.summary_desc": "Review the total contract value and details", "contracts.create.items_count": "Items:", "contracts.create.currency_label": "Currency:", "contracts.create.total_value": "Total Contract Value:", "contracts.templates.page_title": "Contract Templates", "contracts.templates.page_desc": "Manage contract templates for sales and purchase agreements", "contracts.templates.sales_section": "Sales Contract Templates", "contracts.templates.sales_desc": "Create and manage sales contract templates", "contracts.templates.purchase_section": "Purchase Contract Templates", "contracts.templates.purchase_desc": "Create and manage purchase contract templates", "contracts.templates.template_name": "Template Name", "contracts.templates.currency": "<PERSON><PERSON><PERSON><PERSON>", "contracts.templates.payment_terms": "Payment Terms", "contracts.templates.delivery_terms": "Delivery Terms", "contracts.templates.template_content": "Template Content", "contracts.templates.content_placeholder": "Enter contract template content with placeholders like {{customer_name}}, {{product_name}}, etc.", "contracts.templates.content_placeholder_purchase": "Enter contract template content with placeholders like {{supplier_name}}, {{material_name}}, etc.", "contracts.templates.create_template": "Create Template", "contracts.templates.existing_templates": "Existing Templates", "contracts.templates.name": "Name", "contracts.templates.actions": "Actions", "contracts.templates.payment_30_days": "30 days", "contracts.templates.payment_60_days": "60 days", "contracts.templates.delivery_fob": "FOB", "contracts.templates.delivery_cif": "CIF", "contracts.templates.delivery_exw": "EXW", "contract_templates.sales.title": "Sales Contract Templates", "contract_templates.sales.description": "Create and manage templates for sales contracts", "contract_templates.sales.sample": "Sample Templates", "contract_templates.sales.sample_title": "Professional Sales Contract Template", "contract_templates.sales.sample_desc": "Copy this professional template and paste it into the Template Content field below.", "contract_templates.purchase.title": "Purchase Contract Templates", "contract_templates.purchase.description": "Create and manage templates for purchase contracts", "contract_templates.purchase.sample": "Sample Templates", "contract_templates.purchase.sample_title": "Professional Purchase Contract Template", "contract_templates.purchase.sample_desc": "Copy this professional template and paste it into the Template Content field below.", "quality.attachments.documents.title": "Document Attachments", "quality.attachments.documents.upload": "Upload Documents", "quality.attachments.documents.formats": "Supported formats: PDF, DOC, DOCX, XLS, XLSX, TXT", "quality.attachments.documents.none": "No documents attached", "quality.attachments.photos.title": "Photo Attachments", "quality.attachments.photos.upload": "Upload Photos", "quality.attachments.photos.formats": "Supported formats: JPG, PNG, GIF, WebP", "quality.attachments.photos.none": "No photos attached", "quality.attachments.preview": "Preview", "quality.attachments.download": "Download", "quality.attachments.remove": "Remove", "quality.attachments.uploading": "Uploading files...", "quality.attachments.upload_success": "Upload Successful", "quality.attachments.upload_success_desc": "file(s) uploaded successfully", "quality.attachments.download_success": "Download Complete", "quality.attachments.download_success_desc": "downloaded successfully", "quality.attachments.download_failed": "Download Failed", "quality.attachments.download_failed_desc": "Failed to download file. Please try again.", "quality.attachments.remove_success": "File Removed", "quality.attachments.remove_success_desc": "File removed successfully", "quality.attachments.remove_failed": "Remove Failed", "quality.attachments.remove_failed_desc": "Failed to remove file. Please try again.", "quality.status": "Quality Status", "quality.status.pending": "Pending", "quality.status.approved": "Approved", "quality.status.quarantined": "Quarantined", "quality.status.rejected": "Rejected", "workOrder.title": "Work Order", "workOrder.number": "Work Order Number", "workOrder.status.completed": "Completed", "workOrder.status.pending": "Pending", "workOrder.status.in-progress": "In Progress", "products.form.quality_requirements": "Quality Requirements", "products.form.inspection_required": "Quality Inspection Required", "products.form.inspection_required_desc": "Enable this if the product requires quality inspection before approval", "products.form.quality_tolerance": "Quality Tolerance", "products.form.quality_notes": "Quality Notes", "products.form.quality_notes_placeholder": "Enter specific quality requirements, standards, or inspection criteria...", "products.quality.not_required": "Not Required", "products.success.updated_desc": "Product quality requirements have been updated successfully.", "products.success.created_desc": "Product with quality requirements has been created successfully.", "work_orders.quality_gate.title": "Quality Approval Required", "work_orders.quality_gate.description": "This work order cannot be completed until all required quality inspections are approved.", "work_orders.quality_gate.work_order_info": "Work Order Information", "work_orders.quality_gate.inspections_status": "Quality Inspections Status", "work_orders.quality_gate.no_inspections": "No quality inspections found. Inspection may need to be created.", "work_orders.quality_gate.completion_status": "Completion Status", "work_orders.quality_gate.can_complete": "All quality requirements met. Work order can be completed.", "work_orders.quality_gate.cannot_complete": "Quality approval required before completion.", "work_orders.quality_gate.pending_inspections": "Pending Inspections", "work_orders.quality_gate.complete_inspections_first": "Please complete all pending quality inspections before proceeding.", "work_orders.quality_gate.go_to_quality_control": "Go to Quality Control", "work_orders.quality_gate.complete_work_order": "Complete Work Order", "quality.inspector": "Inspector", "quality.inspection_types.final": "Final Inspection", "quality.inspection_types.incoming": "Incoming Inspection", "quality.inspection_types.in_process": "In-Process Inspection", "quality.status.passed": "Passed", "quality.status.failed": "Failed", "common.processing": "Processing..."}