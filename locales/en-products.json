{"products.title": "Products", "products.subtitle": "Manage your product catalog and inventory", "products.add": "Add Product", "products.add.title": "Add New Product", "products.add.description": "Create a new product in your catalog.", "products.edit.title": "Edit Product", "products.edit.description": "Update product information.", "products.delete.title": "Delete Product", "products.delete.description": "Are you sure you want to delete this product? This action cannot be undone.", "products.form.name": "Product Name", "products.form.sku": "SKU", "products.form.unit": "Unit", "products.form.hs_code": "HS Code", "products.form.origin": "Origin", "products.form.package": "Package", "products.form.status": "Status", "products.form.pricing_information": "Pricing Information", "products.form.base_price": "Base Price", "products.form.cost_price": "Cost Price", "products.form.margin_percentage": "Margin %", "products.form.currency": "<PERSON><PERSON><PERSON><PERSON>", "products.table.name": "Product Name", "products.table.sku": "SKU", "products.table.unit": "Unit", "products.table.hs_code": "HS Code", "products.table.origin": "Origin", "products.table.package": "Package", "products.table.price": "Price", "products.table.status": "Status", "products.table.actions": "Actions", "products.success.created": "Product created successfully!", "products.success.updated": "Product Updated", "products.success.deleted": "Product deleted successfully!", "products.error.create": "Failed to create product", "products.error.update": "Update Failed", "products.error.delete": "Failed to delete product", "products.empty": "No products found", "products.empty.description": "Get started by adding your first product.", "products.view.title": "Product Details", "products.view.description": "View product information (read-only).", "products.form.quality_requirements": "Quality Requirements", "products.form.inspection_required": "Quality Inspection Required", "products.form.inspection_required_desc": "Enable this if the product requires quality inspection before approval", "products.form.quality_tolerance": "Quality Tolerance", "products.form.quality_notes": "Quality Notes", "products.form.quality_notes_placeholder": "Enter specific quality requirements, standards, or inspection criteria...", "products.quality.not_required": "Not Required", "products.success.updated_desc": "Product quality requirements have been updated successfully.", "products.success.created_desc": "Product with quality requirements has been created successfully."}