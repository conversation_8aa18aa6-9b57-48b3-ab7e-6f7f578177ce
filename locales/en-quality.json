{"quality.title": "Quality Control", "quality.subtitle": "Manage quality inspections and certificates", "quality.metrics.title": "Quality Metrics", "quality.metrics.pass_rate": "Pass Rate", "quality.metrics.total_inspections": "Total Inspections", "quality.metrics.pending": "Pending Inspections", "quality.metrics.defect_rate": "Defect Rate", "quality.inspections.title": "Recent Inspections", "quality.inspections.subtitle": "Latest quality inspection results", "quality.defects.title": "Defect Tracking", "quality.defects.subtitle": "Track and manage quality defects", "quality.attachments.documents.title": "Document Attachments", "quality.attachments.documents.upload": "Upload Documents", "quality.attachments.documents.formats": "Supported formats: PDF, DOC, DOCX, XLS, XLSX, TXT", "quality.attachments.documents.none": "No documents attached", "quality.attachments.photos.title": "Photo Attachments", "quality.attachments.photos.upload": "Upload Photos", "quality.attachments.photos.formats": "Supported formats: JPG, PNG, GIF, WebP", "quality.attachments.photos.none": "No photos attached", "quality.attachments.preview": "Preview", "quality.attachments.download": "Download", "quality.attachments.remove": "Remove", "quality.attachments.uploading": "Uploading files...", "quality.attachments.upload_success": "Upload Successful", "quality.attachments.upload_success_desc": "file(s) uploaded successfully", "quality.attachments.download_success": "Download Complete", "quality.attachments.download_success_desc": "downloaded successfully", "quality.attachments.download_failed": "Download Failed", "quality.attachments.download_failed_desc": "Failed to download file. Please try again.", "quality.attachments.remove_success": "File Removed", "quality.attachments.remove_success_desc": "File removed successfully", "quality.attachments.remove_failed": "Remove Failed", "quality.attachments.remove_failed_desc": "Failed to remove file. Please try again.", "quality.status": "Quality Status", "quality.status.pending": "Pending", "quality.status.approved": "Approved", "quality.status.quarantined": "Quarantined", "quality.status.rejected": "Rejected", "quality.inspector": "Inspector", "quality.inspection_types.final": "Final Inspection", "quality.inspection_types.incoming": "Incoming Inspection", "quality.inspection_types.in_process": "In-Process Inspection", "quality.status.passed": "Passed", "quality.status.failed": "Failed"}