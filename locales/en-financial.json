{"finance.title": "Finance & Accounting", "finance.description": "Manage invoices, payments, and financial reporting", "finance.summary.totalAR": "Total Receivables", "finance.summary.outstandingAR": "Outstanding AR", "finance.summary.totalAP": "Total Payables", "finance.summary.netCashFlow": "Net Cash Flow", "finance.kpis.coreMetrics": "Core Financial Metrics", "finance.kpis.totalRevenue": "Total Revenue (YTD)", "finance.kpis.totalExpenses": "Total Expenses (YTD)", "finance.kpis.profitLoss": "Profit/Loss (YTD)", "finance.kpis.netCashFlow": "Net Cash Flow", "finance.kpis.overdueIntelligence": "Overdue Intelligence", "finance.kpis.overdueAR": "Overdue Receivables", "finance.kpis.overdueAP": "Overdue Payables", "finance.kpis.manufacturingIntelligence": "Manufacturing Intelligence", "finance.kpis.contractProfitability": "Contract Profitability", "finance.kpis.avgCollectionDays": "Avg Collection Days", "finance.kpis.manufacturingMargin": "Manufacturing Margin", "finance.kpis.activeContracts": "Active Contracts", "finance.ar.title": "Accounts Receivable", "finance.ar.description": "Track AR invoices and aging with contract integration", "finance.ar.invoiceNumber": "Invoice Number", "finance.ar.customer": "Customer", "finance.ar.salesContract": "Sales Contract", "finance.ar.amount": "Amount", "finance.ar.received": "Received", "finance.ar.currency": "<PERSON><PERSON><PERSON><PERSON>", "finance.ar.status": "Status", "finance.ar.invoiceDate": "Invoice Date", "finance.ar.dueDate": "Due Date", "finance.ar.paymentTerms": "Payment Terms", "finance.ar.aging": "Aging", "finance.ar.contract": "Contract", "finance.ar.createInvoice": "Create AR Invoice", "finance.ar.noInvoices": "No AR invoices found", "finance.ap.title": "Accounts Payable", "finance.ap.description": "Track AP invoices and payments with contract integration", "finance.ap.invoiceNumber": "Invoice Number", "finance.ap.supplier": "Supplier", "finance.ap.purchaseContract": "Purchase Contract", "finance.ap.amount": "Amount", "finance.ap.paid": "Paid", "finance.ap.currency": "<PERSON><PERSON><PERSON><PERSON>", "finance.ap.status": "Status", "finance.ap.invoiceDate": "Invoice Date", "finance.ap.dueDate": "Due Date", "finance.ap.paymentTerms": "Payment Terms", "finance.ap.aging": "Aging", "finance.ap.contract": "Contract", "finance.ap.createInvoice": "Create AP Invoice", "finance.ap.noInvoices": "No AP invoices found", "finance.paymentTerms.tt": "TT (Telegraphic Transfer)", "finance.paymentTerms.dp": "DP (Documents against Payment)", "finance.paymentTerms.lc": "LC (Letter of Credit)", "finance.paymentTerms.deposit": "Deposit (Advance Payment)", "finance.paymentTerms.depositTT": "30% Deposit + 70% TT", "finance.paymentTerms.depositLC": "50% Deposit + 50% LC", "finance.status.depositReceived": "<PERSON><PERSON><PERSON><PERSON> Received", "finance.status.partialPaid": "Partial Paid", "finance.status.depositPaid": "<PERSON><PERSON><PERSON><PERSON>", "finance.ar.desc": "Track AR and aging.", "finance.ar.empty": "No AR invoices.", "finance.ap.desc": "Track AP and payments.", "finance.ap.empty": "No AP invoices."}