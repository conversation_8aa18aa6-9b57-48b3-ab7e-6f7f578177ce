{"app.name": "FC-CHINA", "cmd.placeholder": "Search or jump to...", "home.title": "Export Manufacturing ERP", "home.subtitle": "One place to manage master data, contracts, production, inventory, export declarations, and finance.", "home.quick.master": "Add Master Data", "home.quick.contract": "Create Contract", "home.quick.stock": "Record Stock", "home.quick.declaration": "New Declaration", "kpi.customers": "Customers", "kpi.products": "Products", "kpi.suppliers": "Suppliers", "kpi.contracts": "Contracts", "kpi.suppliers.desc": "Active suppliers", "kpi.contracts.desc": "Sales & purchase contracts", "kpi.onhand": "On-hand Stock (sum)", "kpi.openWos": "Open Work Orders", "sample.title": "Sample Data", "sample.desc": "This workspace includes realistic sample data to demonstrate relationships across modules.", "sample.reset": "Reset sample data", "sample.clear": "Clear sample data", "alert.db.title": "Database not configured", "alert.db.desc": "Set the DATABASE_URL (Neon Postgres). The app runs a safe, one-time auto-migration at first load and seeds a minimal dataset. You can also run scripts/sql/001_init.sql and 002_seed.sql manually.", "alert.prep.title": "Preparing your workspace", "alert.prep.desc": "The schema is initializing. Refresh in a few seconds.", "field.code": "Code", "field.name": "Name", "field.spec": "Specification", "field.moq": "MOQ", "field.inStock": "In Stock", "field.contact": "Contact", "field.incoterm": "Incoterm", "field.paymentTerm": "Payment Term", "field.address": "Address", "field.sku": "SKU", "field.unit": "Unit", "field.hsCode": "HS Code", "field.origin": "Origin", "field.packaging": "Packaging", "field.currency": "<PERSON><PERSON><PERSON><PERSON>", "field.number": "Number", "field.customer": "Customer", "field.supplier": "Supplier", "field.product": "Product", "field.qty": "Quantity", "field.price": "Price", "field.total": "Total", "field.woNumber": "WO Number", "field.salesContract": "Sales Contract", "field.location": "Location", "field.note": "Note", "field.reference": "Reference", "field.declarationNo": "Declaration No.", "field.amount": "Amount", "field.received": "Received", "field.paid": "Paid", "field.invoiceNo": "Invoice No.", "table.actions": "Actions", "table.noData": "No data available.", "action.add": "Add", "action.addItem": "Add Item", "action.remove": "Remove", "action.delete": "Delete", "action.create": "Create", "action.createContract": "Create Contract", "action.createPO": "Create PO", "action.createWO": "Create WO", "action.addInbound": "Receive", "action.addOutbound": "Ship", "action.createDeclaration": "Create Declaration", "action.submit": "Submit", "action.createAR": "Create AR", "action.createAP": "Create AP", "cta.open": "Open", "basic.samples.title": "Sample Management Center", "basic.samples.desc": "Code, name, specs, MOQ, available from stock.", "basic.customers.title": "Customers", "basic.customers.desc": "Store customer profiles and trading terms.", "basic.suppliers.title": "Suppliers", "basic.suppliers.desc": "Approved suppliers and contacts.", "basic.products.title": "Products/SKUs", "basic.products.desc": "Units, HS codes, origin, packaging.", "production.title": "Work Orders", "production.desc": "Track routing and operation progress.", "production.empty": "No work orders.", "docs.plan.title": "Reference Mind Maps", "docs.plan.desc": "Original Chinese and translated English plan visuals used to guide implementation.", "module.master.title": "Master Data", "module.master.desc": "Samples, customers, suppliers, products, and trading terms.", "module.contracts.title": "Contracts", "module.contracts.desc": "Create sales contracts and purchase orders from SKUs.", "module.production.title": "Production", "module.production.desc": "Generate work orders, track routing and QC.", "module.inventory.title": "Inventory", "module.inventory.desc": "Inbound/outbound, FIFO, and current lots.", "module.export.title": "Export", "module.export.desc": "Build declarations and validate HS codes.", "module.export.declarations": "Declarations", "module.finance.title": "Finance", "module.finance.desc": "Track receivables and payables with basic aging.", "header.wo": "WO", "header.operations": "Operations", "header.type": "Type", "header.time": "Time", "header.lot": "Lot", "field.email": "Email", "field.phone": "Phone", "field.company": "Company", "field.status": "Status", "field.type": "Type", "field.date": "Date", "field.notes": "Notes", "field.contract": "Contract", "field.template": "Template", "sales_contracts.title": "Sales Contracts", "sales_contracts.subtitle": "Manage your company's sales contracts.", "sales_contracts.add": "Add Contract", "sales_contracts.add.title": "Create Sales Contract", "sales_contracts.add.description": "Create a new sales contract for your customer.", "sales_contracts.edit.title": "Edit Sales Contract", "sales_contracts.edit.description": "Update sales contract information.", "sales_contracts.delete.title": "Are you sure?", "sales_contracts.delete.description": "This will permanently delete the contract. This action cannot be undone.", "sales_contracts.form.number": "Contract Number", "sales_contracts.form.customer": "Customer", "sales_contracts.form.template": "Template", "sales_contracts.form.currency": "<PERSON><PERSON><PERSON><PERSON>", "sales_contracts.form.items": "Items", "sales_contracts.table.contract_number": "Contract Number", "sales_contracts.table.number": "Contract #", "sales_contracts.table.customer": "Customer", "sales_contracts.table.date": "Date", "sales_contracts.table.currency": "<PERSON><PERSON><PERSON><PERSON>", "sales_contracts.table.items": "Items", "sales_contracts.table.total": "Total", "sales_contracts.table.status": "Status", "sales_contracts.table.created": "Created", "sales_contracts.table.actions": "Actions", "sales_contracts.search_placeholder": "Search contracts...", "sales_contracts.success.created": "Sales contract created successfully!", "sales_contracts.success.updated": "Sales contract updated successfully!", "sales_contracts.success.deleted": "Contract deleted successfully.", "sales_contracts.empty": "No sales contracts found", "sales_contracts.empty.description": "Create your first sales contract to get started.", "purchase_contracts.title": "Purchase Contracts", "purchase_contracts.subtitle": "Manage your company's purchase contracts.", "purchase_contracts.add": "Add Contract", "purchase_contracts.add.title": "Create Purchase Contract", "purchase_contracts.add.description": "Create a new purchase contract with your supplier.", "purchase_contracts.edit.title": "Edit Purchase Contract", "purchase_contracts.edit.description": "Update purchase contract information.", "purchase_contracts.delete.title": "Are you sure?", "purchase_contracts.delete.description": "This will permanently delete the contract. This action cannot be undone.", "purchase_contracts.form.number": "Contract Number", "purchase_contracts.form.supplier": "Supplier", "purchase_contracts.form.template": "Template", "purchase_contracts.form.currency": "<PERSON><PERSON><PERSON><PERSON>", "purchase_contracts.form.items": "Items", "purchase_contracts.table.contract_number": "Contract Number", "purchase_contracts.table.number": "Contract #", "purchase_contracts.table.supplier": "Supplier", "purchase_contracts.table.date": "Date", "purchase_contracts.table.currency": "<PERSON><PERSON><PERSON><PERSON>", "purchase_contracts.table.items": "Items", "purchase_contracts.table.total": "Total", "purchase_contracts.table.status": "Status", "purchase_contracts.table.created": "Created", "purchase_contracts.table.actions": "Actions", "purchase_contracts.search_placeholder": "Search contracts...", "purchase_contracts.success.created": "Purchase contract created successfully!", "purchase_contracts.success.updated": "Purchase contract updated successfully!", "purchase_contracts.success.deleted": "Contract deleted successfully.", "purchase_contracts.empty": "No purchase contracts found", "purchase_contracts.empty.description": "Create your first purchase contract to get started.", "company.profile.title": "Company Profile", "company.profile.subtitle": "Manage your company information and settings", "company.profile.not_found": "No Company Profile Found", "company.profile.not_found_desc": "It looks like you haven't completed your company profile setup yet.", "company.profile.complete_setup": "Complete Company Setup", "company.profile.complete": "Complete", "company.profile.incomplete": "Incomplete", "company.profile.edit": "Edit Profile", "company.profile.save": "Save Changes", "company.profile.cancel": "Cancel", "company.profile.tabs.basic": "Basic Information", "company.profile.tabs.business": "Business Details", "company.profile.tabs.banking": "Banking", "company.profile.tabs.export": "Export & Trade", "company.profile.basic.description": "Your company's basic contact and address information", "company.profile.business.description": "Business registration and operational information", "company.profile.banking.description": "Banking and financial account details", "company.profile.export.description": "Export licensing and trade compliance information", "company.profile.success.updated": "Company profile updated successfully!", "company.field.name": "Company Name", "company.field.legal_name": "Legal Company Name", "company.field.email": "Email Address", "company.field.phone": "Phone Number", "company.field.website": "Website", "company.field.country": "Country", "company.field.address_line1": "Street Address", "company.field.address_line2": "Address Line 2", "company.field.city": "City", "company.field.state_province": "State/Province", "company.field.postal_code": "Postal Code", "company.field.industry": "Industry", "company.field.business_type": "Business Type", "company.field.employee_count": "Employee Count", "company.field.annual_revenue": "Annual Revenue", "company.field.registration_number": "Registration Number", "company.field.tax_id": "Tax ID", "company.field.vat_number": "VAT Number", "company.field.bank_name": "Bank Name", "company.field.bank_account": "Account Number", "company.field.bank_swift": "SWIFT/BIC Code", "company.field.bank_address": "Bank Address", "company.field.export_license": "Export License", "company.field.customs_code": "Customs Code", "company.field.preferred_incoterms": "Preferred Incoterms", "company.field.preferred_payment_terms": "Preferred Payment Terms", "contract_templates.title": "Contract Templates", "contract_templates.subtitle": "Manage reusable contract templates for sales and purchase agreements", "contract_templates.add": "Add Template", "contract_templates.table.name": "Template Name", "contract_templates.table.type": "Type", "contract_templates.table.language": "Language", "contract_templates.table.version": "Version", "contract_templates.table.status": "Status", "contract_templates.table.actions": "Actions", "contract_templates.sales.title": "Sales Contract Templates", "contract_templates.sales.description": "Create and manage templates for sales contracts", "contract_templates.sales.sample": "Sample Templates", "contract_templates.sales.sample_title": "Professional Sales Contract Template", "contract_templates.sales.sample_desc": "Copy this professional template and paste it into the Template Content field below.", "contract_templates.purchase.title": "Purchase Contract Templates", "contract_templates.purchase.description": "Create and manage templates for purchase contracts", "contract_templates.purchase.sample": "Sample Templates", "contract_templates.purchase.sample_title": "Professional Purchase Contract Template", "contract_templates.purchase.sample_desc": "Copy this professional template and paste it into the Template Content field below.", "workOrder.title": "Work Order", "workOrder.number": "Work Order Number", "workOrder.status.completed": "Completed", "workOrder.status.pending": "Pending", "workOrder.status.in-progress": "In Progress", "work_orders.quality_gate.title": "Quality Approval Required", "work_orders.quality_gate.description": "This work order cannot be completed until all required quality inspections are approved.", "work_orders.quality_gate.work_order_info": "Work Order Information", "work_orders.quality_gate.inspections_status": "Quality Inspections Status", "work_orders.quality_gate.no_inspections": "No quality inspections found. Inspection may need to be created.", "work_orders.quality_gate.completion_status": "Completion Status", "work_orders.quality_gate.can_complete": "All quality requirements met. Work order can be completed.", "work_orders.quality_gate.cannot_complete": "Quality approval required before completion.", "work_orders.quality_gate.pending_inspections": "Pending Inspections", "work_orders.quality_gate.complete_inspections_first": "Please complete all pending quality inspections before proceeding.", "work_orders.quality_gate.go_to_quality_control": "Go to Quality Control", "work_orders.quality_gate.complete_work_order": "Complete Work Order"}