{"samples.title": "Sample Management", "samples.subtitle": "Manage product samples and approval workflow", "samples.add": "New Sample", "samples.refresh": "Refresh", "samples.loading": "Loading samples...", "samples.found": "{count} samples found", "samples.cards.outbound.title": "📤 Outbound Samples", "samples.cards.outbound.description": "We send to customers", "samples.cards.inbound.title": "📥 Inbound Samples", "samples.cards.inbound.description": "From customers & suppliers", "samples.cards.internal.title": "🏭 Internal Samples", "samples.cards.internal.description": "R&D and testing", "samples.cards.quality.title": "🧪 Quality Pipeline", "samples.cards.quality.description": "Awaiting QC approval", "samples.table.sample": "<PERSON><PERSON>", "samples.table.direction": "Direction", "samples.table.purpose": "Purpose", "samples.table.relationship": "Relationship", "samples.table.product": "Product", "samples.table.status": "Status", "samples.table.priority": "Priority", "samples.table.created": "Created", "samples.table.actions": "Actions", "samples.table.empty": "No samples found. Create your first sample to get started.", "samples.actions.view": "View", "samples.actions.edit": "Edit", "samples.actions.delete": "Delete", "samples.actions.approve": "Approve", "samples.actions.reject": "Reject", "samples.filters.search": "Search samples by name, code, or notes...", "samples.filters.status.all": "All Statuses", "samples.filters.status.pending": "Pending", "samples.filters.status.approved": "Approved", "samples.filters.status.rejected": "Rejected", "samples.filters.type.all": "All Types", "samples.filters.type.development": "Development", "samples.filters.type.production": "Production", "samples.filters.type.quality": "Quality", "samples.filters.type.prototype": "Prototype", "samples.filters.direction.all": "All Directions", "samples.filters.direction.outbound": "Outbound", "samples.filters.direction.inbound": "Inbound", "samples.filters.direction.internal": "Internal", "samples.filters.advanced": "Advanced", "samples.filters.clear": "Clear Filters", "samples.delete.success.title": "Sample Deleted", "samples.delete.success.description": "Sample '{name}' has been successfully deleted", "samples.delete.error.title": "Delete Failed", "samples.delete.error.description": "Failed to delete sample. Please try again.", "samples.delete.dialog.title": "Delete Sample", "samples.delete.dialog.description": "Are you sure you want to delete this sample? This action cannot be undone.", "samples.delete.dialog.warning": "This action is permanent and cannot be undone.", "samples.delete.dialog.confirm": "Delete Sample", "samples.delete.dialog.deleting": "Deleting...", "samples.fields.code": "Sample Code", "samples.fields.name": "Sample Name", "samples.fields.date": "Date", "samples.fields.status": "Status", "samples.fields.priority": "Priority", "samples.fields.type": "Sample Type", "samples.fields.direction": "Direction", "samples.fields.purpose": "Purpose", "samples.fields.customer": "Customer", "samples.fields.supplier": "Supplier", "samples.fields.product": "Product", "samples.fields.quantity": "Quantity", "samples.fields.unit": "Unit", "samples.fields.cost": "Cost", "samples.fields.currency": "<PERSON><PERSON><PERSON><PERSON>", "samples.fields.delivery_date": "Delivery Date", "samples.fields.specifications": "Technical Specifications", "samples.fields.quality_requirements": "Quality Requirements", "samples.fields.notes": "Notes", "samples.create.title": "Create Sample", "samples.create.description": "Create a new sample record for tracking and approval", "samples.create.basic_info": "Basic Information", "samples.create.workflow": "Sample Workflow", "samples.create.relationships": "Business Relationships", "samples.create.specifications": "Specifications & Details", "samples.create.success.title": "Sample Created", "samples.create.success.description": "Sample has been created successfully", "samples.create.error.title": "Creation Failed", "samples.create.error.description": "Failed to create sample. Please try again.", "samples.create.cancel": "Cancel", "samples.create.save": "Create Sample", "samples.create.saving": "Creating...", "samples.view.back": "Back to Samples", "samples.view.pending_request": "Pending Request", "samples.view.approved": "Approved", "samples.view.rejected": "Rejected", "samples.view.edit": "Edit", "samples.view.sample_info": "Sample Information", "samples.view.sample_type": "Sample Type", "samples.view.priority": "Priority", "samples.view.sample_date": "Sample Date", "samples.view.quantity": "Quantity", "samples.view.delivery_date": "Delivery Date", "samples.view.cost": "Cost", "samples.view.relationships": "Relationships", "samples.view.customer": "Customer", "samples.view.contact": "Contact", "samples.view.email": "Email", "samples.view.product": "Product", "samples.view.sku": "SKU", "samples.view.supplier": "Supplier", "samples.view.specifications": "Specifications & Notes", "samples.view.technical_specs": "Technical Specifications", "samples.view.quality_requirements": "Quality Requirements", "samples.view.notes": "Notes", "samples.view.approval_history": "Approval History", "samples.view.created": "Created", "samples.view.revised": "Revised", "samples.view.pending": "pending", "samples.view.revision_required": "revision_required", "samples.view.by_system": "by System", "samples.view.by_current_user": "by Current User", "samples.view.sample_created": "Sample created and submitted for approval", "samples.view.sample_processed": "Sample processed", "samples.view.metadata": "<PERSON><PERSON><PERSON>", "samples.view.created_date": "Created", "samples.view.approved_by": "Approved by", "samples.view.approved_date": "Approved Date", "samples.view.status.created": "Created", "samples.view.status.pending": "Pending", "samples.view.status.approved": "Approved", "samples.view.status.rejected": "Rejected", "samples.view.status.revised": "Revised", "samples.view.status.revision_required": "Revision Required", "samples.view.actions.sample_created": "Sample created and submitted for approval", "samples.view.actions.sample_processed": "Sample processed", "samples.view.actions.sample_approved": "<PERSON><PERSON> approved", "samples.view.actions.sample_rejected": "<PERSON><PERSON> rejected", "samples.view.actions.revision_requested": "Revision requested", "samples.view.by_system_on": "by System on", "samples.view.by_current_user_on": "by Current User on", "samples.view.by_user_on": "by {user} on", "samples.edit.title": "<PERSON>", "samples.edit.description": "Update sample information and specifications", "samples.edit.loading": "Loading sample data...", "samples.edit.success.title": "Sample Updated", "samples.edit.success.description": "Sample has been updated successfully", "samples.edit.error.title": "Update Failed", "samples.edit.error.description": "Failed to update sample. Please try again.", "samples.edit.error.load": "Failed to load sample data. Please try again.", "samples.edit.back": "Back to Sample", "samples.edit.cancel": "Cancel", "samples.edit.save": "Update Sample", "samples.edit.saving": "Updating...", "samples.edit.validation.name": "Sample name is required", "samples.edit.code.readonly": "Sample code cannot be changed", "samples.edit.basic_info": "Basic Information", "samples.edit.basic_info_desc": "Update the basic sample information", "samples.edit.sample_code": "Sample Code", "samples.edit.sample_name": "Sample Name", "samples.edit.sample_name_placeholder": "Enter sample name", "samples.edit.sample_type": "Sample Type", "samples.edit.priority": "Priority", "samples.edit.relationships": "Relationships", "samples.edit.relationships_desc": "Associate this sample with customers, products, and suppliers", "samples.edit.customer": "Customer", "samples.edit.customer_placeholder": "Search customers...", "samples.edit.product": "Product", "samples.edit.product_placeholder": "Search products...", "samples.edit.supplier": "Supplier", "samples.edit.supplier_placeholder": "Search suppliers...", "samples.edit.specifications": "Specifications & Details", "samples.edit.specifications_desc": "Add technical specifications and additional details", "samples.edit.quantity": "Quantity", "samples.edit.unit": "Unit", "samples.edit.cost": "Cost", "samples.edit.currency": "<PERSON><PERSON><PERSON><PERSON>", "samples.edit.delivery_date": "Delivery Date", "samples.edit.technical_specs": "Technical Specifications", "samples.edit.technical_specs_placeholder": "Enter technical specifications...", "samples.edit.quality_requirements": "Quality Requirements", "samples.edit.quality_requirements_placeholder": "Enter quality requirements...", "samples.edit.notes": "Notes", "samples.edit.notes_placeholder": "Enter additional notes...", "samples.edit.search.no_results": "No results found", "samples.edit.search.add_new_customer": "Add new customer", "samples.edit.search.add_new_product": "Add new product", "samples.edit.search.add_new_supplier": "Add new supplier", "samples.edit.search.loading": "Loading...", "samples.edit.search.type_to_search": "Type to search..."}