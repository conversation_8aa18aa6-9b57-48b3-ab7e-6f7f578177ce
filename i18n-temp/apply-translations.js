#!/usr/bin/env node

/**
 * Manufacturing ERP Translation Application
 * 
 * Applies the translations we added to the i18n system by replacing
 * hardcoded strings with t() function calls in the actual code files.
 * 
 * ZERO BREAKING CHANGES: Safe replacement with backup capability.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const BACKUP_DIR = 'i18n-backup';

// Translation mappings from our improved CSV files
const DASHBOARD_TRANSLATIONS = {
  'Financial Performance': 'common.financial_performance',
  'Total Revenue': 'common.total_revenue',
  'Profit Margin': 'common.profit_margin',
  'Pending Receivables': 'common.pending_receivables',
  'Active Customers': 'common.active_customers',
  'Production Operations': 'common.production_operations',
  'Active Work Orders': 'common.active_work_orders',
  'Production Efficiency': 'common.production_efficiency',
  'Active Shipments': 'common.active_shipments',
  'On-Time Delivery': 'common.ontime_delivery',
  'Quality Control': 'common.quality_control',
  'Export Operations': 'common.export_operations',
  'Quick Access': 'common.quick_access',
  'Navigate to key manufacturing modules': 'common.navigate_to_key_manufacturing_modules',
  'Reports': 'common.reports',
  'Production': 'common.production',
  'Quality': 'common.quality',
  'Shipping': 'common.shipping',
  'Quality Pass Rate': 'common.quality_pass_rate',
  'Pending Inspections': 'common.pending_inspections',
  'Quality Score': 'common.quality_score',
  'Export Declarations': 'common.export_declarations',
  'Active Sales Contracts': 'common.active_sales_contracts',
  'Product Catalog': 'common.product_catalog',
  'Manufacturing quality metrics': 'common.manufacturing_quality_metrics',
  'International trade metrics': 'common.international_trade_metrics'
};

// Create backup
function createBackup(filePath) {
  console.log(`💾 Creating backup for ${filePath}...`);
  
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const fileName = path.basename(filePath);
  const backupFile = path.join(BACKUP_DIR, `${fileName}-${timestamp}.backup`);
  
  fs.copyFileSync(filePath, backupFile);
  console.log(`   ✅ Backup created: ${backupFile}`);
  
  return backupFile;
}

// Apply translations to a file
function applyTranslationsToFile(filePath, translations) {
  console.log(`🔄 Applying translations to ${filePath}...`);
  
  // Create backup first
  const backupFile = createBackup(filePath);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let replacements = 0;
  
  // Check if file already imports useI18n and has t function
  const hasI18nImport = content.includes('useI18n');
  const hasTFunction = content.includes('const { t } = useI18n()');
  
  if (!hasI18nImport) {
    console.log(`   ⚠️  File doesn't import useI18n, skipping: ${filePath}`);
    return { replacements: 0, backupFile };
  }
  
  if (!hasTFunction) {
    console.log(`   ⚠️  File doesn't extract t function, skipping: ${filePath}`);
    return { replacements: 0, backupFile };
  }
  
  // Apply translations
  Object.entries(translations).forEach(([englishText, translationKey]) => {
    // Look for the exact text in various contexts
    const patterns = [
      // CardTitle with className
      new RegExp(`<CardTitle className="[^"]*">${englishText}</CardTitle>`, 'g'),
      // CardTitle without className
      new RegExp(`<CardTitle>${englishText}</CardTitle>`, 'g'),
      // h2 with className
      new RegExp(`<h2[^>]*>${englishText}</h2>`, 'g'),
      // Simple text in JSX
      new RegExp(`>${englishText}<`, 'g'),
      // Text in quotes (for descriptions, etc.)
      new RegExp(`"${englishText}"`, 'g'),
    ];
    
    patterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          let replacement;
          
          if (match.includes('<CardTitle')) {
            if (match.includes('className=')) {
              replacement = match.replace(englishText, `{t("${translationKey}")}`);
            } else {
              replacement = match.replace(englishText, `{t("${translationKey}")}`);
            }
          } else if (match.includes('<h2')) {
            replacement = match.replace(englishText, `{t("${translationKey}")}`);
          } else if (match.startsWith('>') && match.endsWith('<')) {
            replacement = match.replace(englishText, `{t("${translationKey}")}`);
          } else if (match.startsWith('"') && match.endsWith('"')) {
            replacement = `{t("${translationKey}")}`;
          } else {
            replacement = match.replace(englishText, `{t("${translationKey}")}`);
          }
          
          content = content.replace(match, replacement);
          replacements++;
        });
      }
    });
  });
  
  // Write the updated content
  if (replacements > 0) {
    fs.writeFileSync(filePath, content);
    console.log(`   ✅ Applied ${replacements} translations`);
  } else {
    console.log(`   ℹ️  No translations applied`);
  }
  
  return { replacements, backupFile };
}

// Validate the updated file
function validateFile(filePath) {
  console.log(`✅ Validating ${filePath}...`);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Basic syntax checks
    const openBraces = (content.match(/\{/g) || []).length;
    const closeBraces = (content.match(/\}/g) || []).length;
    
    if (openBraces !== closeBraces) {
      throw new Error(`Unbalanced braces: ${openBraces} open, ${closeBraces} close`);
    }
    
    // Check for common JSX issues
    if (content.includes('{{t(')) {
      throw new Error('Double braces detected around t() function');
    }
    
    if (content.includes('t("")')) {
      throw new Error('Empty translation key detected');
    }
    
    console.log(`   ✅ File validation passed`);
    return true;
  } catch (error) {
    console.error(`   ❌ Validation failed: ${error.message}`);
    return false;
  }
}

// Main function
function main() {
  console.log('🚀 Manufacturing ERP Translation Application');
  console.log('⚠️  ZERO BREAKING CHANGES: Safe replacement with backup capability\n');
  
  const filesToProcess = [
    {
      path: 'app/dashboard/page.tsx',
      translations: DASHBOARD_TRANSLATIONS
    }
  ];
  
  let totalReplacements = 0;
  const backupFiles = [];
  
  filesToProcess.forEach(({ path: filePath, translations }) => {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }
    
    const result = applyTranslationsToFile(filePath, translations);
    totalReplacements += result.replacements;
    backupFiles.push(result.backupFile);
    
    // Validate the updated file
    if (result.replacements > 0) {
      if (!validateFile(filePath)) {
        console.log(`❌ Validation failed for ${filePath}, restoring from backup...`);
        fs.copyFileSync(result.backupFile, filePath);
      }
    }
  });
  
  console.log('\n📋 TRANSLATION APPLICATION COMPLETE');
  console.log('='.repeat(50));
  console.log(`Files processed: ${filesToProcess.length}`);
  console.log(`Total replacements: ${totalReplacements}`);
  console.log(`Backup files created: ${backupFiles.length}`);
  
  if (totalReplacements > 0) {
    console.log('\n📁 Backup Files:');
    backupFiles.forEach(backup => {
      console.log(`   💾 ${backup}`);
    });
    
    console.log('\n🎯 Next Steps:');
    console.log('   1. Test your application: npm run dev');
    console.log('   2. Check the dashboard for translated content');
    console.log('   3. Switch languages to verify translations work');
    console.log('   4. If issues occur, restore from backup files');
    
    console.log('\n✅ Dashboard translations should now be working!');
  } else {
    console.log('\n⚠️  No translations were applied. Check the translation mappings.');
  }
}

// CLI interface
if (require.main === module) {
  main();
}

module.exports = { applyTranslationsToFile, validateFile };
