{"summary": {"totalFiles": 466, "filesWithIssues": 182, "totalHardcodedStrings": 1808, "missingI18nImports": 69, "existingTranslations": 1050, "analysisDate": "2025-09-15"}, "priorityCategories": {"critical": {"description": "User-facing form elements requiring immediate attention", "count": 814, "examples": [{"file": "app/samples/create/page.tsx", "line": 233, "text": "Sample Direction *", "context": "<Label htmlFor=\"sample_direction\">Sample Direction *</Label>", "suggestedKey": "samples.form.direction.label", "suggestedTranslation": {"en": "Sample Direction *", "zh": "样品方向 *"}}, {"file": "components/forms/customer-select.tsx", "line": 155, "text": "Customer Name *", "context": "<FormLabel>Customer Name *</FormLabel>", "suggestedKey": "forms.customer.name.label", "suggestedTranslation": {"en": "Customer Name *", "zh": "客户名称 *"}}]}, "high": {"description": "Dashboard and analytics content", "count": 427, "examples": [{"file": "app/reports/quality-metrics/page.tsx", "line": 210, "text": "Overall Pass Rate", "context": "<CardTitle className=\"text-sm font-medium\">Overall Pass Rate</CardTitle>", "suggestedKey": "reports.quality.overallPassRate", "suggestedTranslation": {"en": "Overall Pass Rate", "zh": "总体通过率"}}]}, "medium": {"description": "Table headers and status values", "count": 415, "examples": [{"file": "app/reports/quality-metrics/page.tsx", "line": 376, "text": "Inspection Type", "context": "<TableHead>Inspection Type</TableHead>", "suggestedKey": "tables.headers.inspectionType", "suggestedTranslation": {"en": "Inspection Type", "zh": "检验类型"}}]}, "low": {"description": "Validation messages and technical strings", "count": 152, "examples": [{"file": "lib/validations.ts", "line": 58, "text": "Margin percentage must be a valid number between -100% and 1000%", "context": "message: \"Margin percentage must be a valid number between -100% and 1000%\"", "suggestedKey": "validation.marginPercentage", "suggestedTranslation": {"en": "Margin percentage must be a valid number between -100% and 1000%", "zh": "利润率必须是-100%到1000%之间的有效数字"}}]}}, "moduleBreakdown": {"samples": {"files": 4, "hardcodedStrings": 95, "priority": "critical", "files_affected": ["app/samples/create/page.tsx", "app/samples/[id]/edit/page.tsx", "app/samples/[id]/page.tsx", "components/samples/sample-detail.tsx"]}, "sales_contracts": {"files": 2, "hardcodedStrings": 78, "priority": "critical", "files_affected": ["app/sales-contracts/edit/[id]/edit-sales-contract-client.tsx", "app/sales-contracts/add/add-sales-contract-client.tsx"]}, "reports": {"files": 6, "hardcodedStrings": 156, "priority": "high", "files_affected": ["app/reports/quality-metrics/page.tsx", "app/reports/production-analytics/page.tsx", "app/reports/financial-performance/page.tsx", "app/reports/business-intelligence/page.tsx", "app/reports/inventory-intelligence/page.tsx", "app/reports/mrp-planning/page.tsx"]}, "forms": {"files": 3, "hardcodedStrings": 89, "priority": "critical", "files_affected": ["components/forms/customer-select.tsx", "components/forms/supplier-select.tsx", "components/forms/product-select.tsx"]}, "suppliers": {"files": 3, "hardcodedStrings": 67, "priority": "critical", "files_affected": ["app/suppliers/suppliers-client.tsx", "app/suppliers/edit-supplier-form.tsx", "app/suppliers/add-supplier-form.tsx"]}}, "missingI18nImports": {"critical": ["components/ui/loading.tsx", "components/ui/confirmation-dialog.tsx", "components/forms/template-select.tsx"], "high": ["components/export/create-declaration-form.tsx", "components/export/declaration-edit-form.tsx", "components/bom/bom-overview-client.tsx"], "medium": ["components/shipping/shipping-stats.tsx", "components/quality/inline-status-editor.tsx", "components/work-orders/inline-status-editor.tsx"]}, "implementationPlan": {"phase1": {"name": "Critical Forms & User Interface", "duration": "Week 1", "targets": ["samples", "sales_contracts", "forms", "suppliers"], "expectedStrings": 329, "approach": "Manual translation with AI assistance for technical terms"}, "phase2": {"name": "Reports & Analytics", "duration": "Week 2", "targets": ["reports", "dashboard_components"], "expectedStrings": 427, "approach": "AI-powered batch translation with manual review"}, "phase3": {"name": "System Integration", "duration": "Week 3", "targets": ["missing_imports", "remaining_strings"], "expectedStrings": 1052, "approach": "Automated import addition + comprehensive string detection"}, "phase4": {"name": "Quality Assurance", "duration": "Week 4", "targets": ["testing", "validation", "documentation"], "expectedStrings": 0, "approach": "Comprehensive testing and quality validation"}}, "automationScripts": {"stringDetection": "scripts/i18n-audit.js", "translationExtraction": "scripts/i18n-extract.js", "backupRestore": "scripts/i18n-backup.js", "aiTranslation": "scripts/i18n-ai-translate.js (to be created)", "syncTranslations": "scripts/i18n-sync.js (to be created)"}, "qualityGates": {"beforeImplementation": ["Complete backup created", "All existing functionality tested", "Development environment stable"], "duringImplementation": ["Each component tested individually", "Language switching validated", "No breaking changes introduced"], "afterImplementation": ["100% string coverage achieved", "Performance impact measured", "User acceptance testing completed"]}, "riskMitigation": {"zeroBreakingChanges": "All changes are additive - existing t() calls remain unchanged", "incrementalDeployment": "Changes can be deployed component by component", "rollbackCapability": "Complete restore script available at any time", "testingStrategy": "Each change tested in isolation before integration"}}