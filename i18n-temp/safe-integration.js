#!/usr/bin/env node

/**
 * Manufacturing ERP Safe Translation Integration
 * 
 * Safely integrates all improved translations into the existing i18n system.
 * ZERO BREAKING CHANGES: Preserves existing translations and adds new ones.
 * 
 * Features:
 * - Automatic backup creation
 * - Conflict detection and resolution
 * - Rollback capability
 * - Quality validation
 */

const fs = require('fs');
const path = require('path');

// Configuration
const I18N_FILE = 'components/i18n-provider.tsx';
const BACKUP_DIR = 'i18n-backup';
const CSV_DIR = 'i18n-temp/module-translations';

// Create backup before integration
function createBackup() {
  console.log('💾 Creating backup before integration...');
  
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFile = path.join(BACKUP_DIR, `i18n-provider-${timestamp}.tsx`);
  
  fs.copyFileSync(I18N_FILE, backupFile);
  console.log(`   ✅ Backup created: ${backupFile}`);
  
  return backupFile;
}

// Parse existing i18n file
function parseExistingTranslations() {
  console.log('📖 Parsing existing translations...');
  
  const content = fs.readFileSync(I18N_FILE, 'utf8');
  
  // Extract English translations
  const enMatch = content.match(/const en: Dict = \{([\s\S]*?)\}/);
  const zhMatch = content.match(/const zh: Dict = \{([\s\S]*?)\}/);
  
  if (!enMatch || !zhMatch) {
    throw new Error('Could not parse existing i18n file structure');
  }
  
  const existingEn = {};
  const existingZh = {};
  
  // Parse English translations
  const enContent = enMatch[1];
  const enLines = enContent.split('\n').filter(line => line.trim() && !line.trim().startsWith('//'));
  
  enLines.forEach(line => {
    const match = line.match(/["']([^"']+)["']:\s*["']([^"']*?)["']/);
    if (match) {
      existingEn[match[1]] = match[2];
    }
  });
  
  // Parse Chinese translations
  const zhContent = zhMatch[1];
  const zhLines = zhContent.split('\n').filter(line => line.trim() && !line.trim().startsWith('//'));
  
  zhLines.forEach(line => {
    const match = line.match(/["']([^"']+)["']:\s*["']([^"']*?)["']/);
    if (match) {
      existingZh[match[1]] = match[2];
    }
  });
  
  console.log(`   ✅ Parsed ${Object.keys(existingEn).length} existing English translations`);
  console.log(`   ✅ Parsed ${Object.keys(existingZh).length} existing Chinese translations`);
  
  return { existingEn, existingZh, originalContent: content };
}

// Load improved translations from CSV files
function loadImprovedTranslations() {
  console.log('📥 Loading improved translations...');
  
  const csvFiles = fs.readdirSync(CSV_DIR).filter(f => f.endsWith('-improved.csv'));
  const newTranslations = { en: {}, zh: {} };
  
  let totalLoaded = 0;
  let highQuality = 0;
  
  csvFiles.forEach(csvFile => {
    const filePath = path.join(CSV_DIR, csvFile);
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    // Skip header
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      if (!line.trim()) continue;
      
      const parts = line.split(',');
      if (parts.length < 9) continue;
      
      const key = parts[0];
      const english = parts[1].replace(/^"|"$/g, '').replace(/""/g, '"');
      const chinese = parts[2].replace(/^"|"$/g, '').replace(/""/g, '"');
      const needsReview = parts[8] === 'true';
      
      // Only include high-quality translations (not needing review)
      if (!needsReview && chinese && !chinese.includes('[需要翻译:')) {
        newTranslations.en[key] = english;
        newTranslations.zh[key] = chinese;
        totalLoaded++;
        highQuality++;
      }
    }
  });
  
  console.log(`   ✅ Loaded ${totalLoaded} new translations`);
  console.log(`   ✅ ${highQuality} high-quality translations ready for integration`);
  
  return newTranslations;
}

// Merge translations safely
function mergeTranslations(existing, newTranslations) {
  console.log('🔄 Merging translations safely...');
  
  const merged = {
    en: { ...existing.existingEn },
    zh: { ...existing.existingZh }
  };
  
  let added = 0;
  let conflicts = 0;
  let skipped = 0;
  
  // Add new translations
  Object.keys(newTranslations.en).forEach(key => {
    if (existing.existingEn[key]) {
      // Conflict detected - keep existing translation
      conflicts++;
      console.log(`   ⚠️  Conflict: ${key} (keeping existing)`);
    } else {
      // Safe to add
      merged.en[key] = newTranslations.en[key];
      merged.zh[key] = newTranslations.zh[key];
      added++;
    }
  });
  
  console.log(`   ✅ Added ${added} new translations`);
  console.log(`   ⚠️  ${conflicts} conflicts resolved (kept existing)`);
  
  return { merged, stats: { added, conflicts, skipped } };
}

// Generate new i18n file content
function generateNewContent(originalContent, mergedTranslations) {
  console.log('🔧 Generating new i18n file content...');
  
  // Sort translations for better organization
  const sortedEnKeys = Object.keys(mergedTranslations.en).sort();
  const sortedZhKeys = Object.keys(mergedTranslations.zh).sort();
  
  // Generate English translations
  const enLines = sortedEnKeys.map(key => {
    const value = mergedTranslations.en[key];
    return `  "${key}": "${value.replace(/"/g, '\\"')}",`;
  });
  
  // Generate Chinese translations
  const zhLines = sortedZhKeys.map(key => {
    const value = mergedTranslations.zh[key];
    return `  "${key}": "${value.replace(/"/g, '\\"')}",`;
  });
  
  // Replace in original content
  let newContent = originalContent;
  
  // Replace English translations
  newContent = newContent.replace(
    /const en: Dict = \{[\s\S]*?\}/,
    `const en: Dict = {\n${enLines.join('\n')}\n}`
  );
  
  // Replace Chinese translations
  newContent = newContent.replace(
    /const zh: Dict = \{[\s\S]*?\}/,
    `const zh: Dict = {\n${zhLines.join('\n')}\n}`
  );
  
  console.log(`   ✅ Generated content with ${sortedEnKeys.length} translations`);
  
  return newContent;
}

// Validate new content
function validateContent(newContent) {
  console.log('✅ Validating new content...');
  
  // Basic syntax validation
  try {
    // Check if the content has proper structure
    if (!newContent.includes('const en: Dict = {')) {
      throw new Error('Missing English translations structure');
    }
    
    if (!newContent.includes('const zh: Dict = {')) {
      throw new Error('Missing Chinese translations structure');
    }
    
    if (!newContent.includes('export function I18nProvider')) {
      throw new Error('Missing I18nProvider component');
    }
    
    console.log('   ✅ Content structure validation passed');
    
    // Count translations
    const enMatches = newContent.match(/const en: Dict = \{([\s\S]*?)\}/);
    const zhMatches = newContent.match(/const zh: Dict = \{([\s\S]*?)\}/);
    
    if (enMatches && zhMatches) {
      const enCount = (enMatches[1].match(/"/g) || []).length / 4; // Rough count
      const zhCount = (zhMatches[1].match(/"/g) || []).length / 4; // Rough count
      
      console.log(`   ✅ Estimated ${Math.floor(enCount)} English translations`);
      console.log(`   ✅ Estimated ${Math.floor(zhCount)} Chinese translations`);
    }
    
    return true;
  } catch (error) {
    console.error(`   ❌ Validation failed: ${error.message}`);
    return false;
  }
}

// Write new content safely
function writeNewContent(newContent, backupFile) {
  console.log('💾 Writing new content safely...');
  
  try {
    // Write to temporary file first
    const tempFile = I18N_FILE + '.tmp';
    fs.writeFileSync(tempFile, newContent);
    
    // Validate temporary file
    if (validateContent(fs.readFileSync(tempFile, 'utf8'))) {
      // Replace original file
      fs.renameSync(tempFile, I18N_FILE);
      console.log('   ✅ New content written successfully');
      return true;
    } else {
      // Remove temporary file
      fs.unlinkSync(tempFile);
      console.log('   ❌ Validation failed, keeping original file');
      return false;
    }
  } catch (error) {
    console.error(`   ❌ Write failed: ${error.message}`);
    return false;
  }
}

// Generate integration report
function generateReport(stats, backupFile) {
  const report = {
    timestamp: new Date().toISOString(),
    success: true,
    backup: backupFile,
    statistics: stats,
    summary: `Successfully integrated ${stats.added} new translations with ${stats.conflicts} conflicts resolved`
  };
  
  const reportFile = 'i18n-temp/integration-report.json';
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  
  console.log(`📋 Integration report saved: ${reportFile}`);
  return report;
}

// Main integration function
function main() {
  console.log('🚀 Manufacturing ERP Safe Translation Integration');
  console.log('⚠️  ZERO BREAKING CHANGES: Safe integration with rollback capability\n');
  
  try {
    // Step 1: Create backup
    const backupFile = createBackup();
    
    // Step 2: Parse existing translations
    const existing = parseExistingTranslations();
    
    // Step 3: Load improved translations
    const newTranslations = loadImprovedTranslations();
    
    // Step 4: Merge translations safely
    const { merged, stats } = mergeTranslations(existing, newTranslations);
    
    // Step 5: Generate new content
    const newContent = generateNewContent(existing.originalContent, merged);
    
    // Step 6: Validate and write
    if (validateContent(newContent)) {
      if (writeNewContent(newContent, backupFile)) {
        // Step 7: Generate report
        const report = generateReport(stats, backupFile);
        
        console.log('\n🎉 INTEGRATION SUCCESSFUL!');
        console.log('='.repeat(50));
        console.log(`✅ Added ${stats.added} new translations`);
        console.log(`⚠️  Resolved ${stats.conflicts} conflicts (kept existing)`);
        console.log(`💾 Backup available: ${backupFile}`);
        console.log(`📋 Report saved: i18n-temp/integration-report.json`);
        
        console.log('\n🎯 Next Steps:');
        console.log('   1. Test your application: npm run dev');
        console.log('   2. Verify language switching works correctly');
        console.log('   3. Check all modules for proper localization');
        console.log('   4. If issues occur, restore from backup');
        
        console.log('\n✅ Your Manufacturing ERP now has comprehensive localization!');
        
      } else {
        console.log('\n❌ Integration failed during write phase');
        console.log(`💾 Original file preserved, backup available: ${backupFile}`);
      }
    } else {
      console.log('\n❌ Integration failed during validation');
      console.log(`💾 Original file preserved, backup available: ${backupFile}`);
    }
    
  } catch (error) {
    console.error('\n❌ Integration failed:', error.message);
    console.log('💾 Original file preserved');
    process.exit(1);
  }
}

// CLI interface
if (require.main === module) {
  main();
}

module.exports = { main };
