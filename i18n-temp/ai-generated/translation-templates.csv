Priority,File,Line,English Text,Suggested Chinese,Suggested Key,Context
critical,app/samples/create/page.tsx,233,"Sample Direction *","样品方向 *",samples.form.direction.label,"<Label htmlFor=""sample_direction"">Sample Direction *</Label>"
critical,components/forms/customer-select.tsx,155,"Customer Name *","客户名称 *",forms.customer.name.label,"<FormLabel>Customer Name *</FormLabel>"
high,app/reports/quality-metrics/page.tsx,210,"Overall Pass Rate","总体通过率",reports.quality.overallPassRate,"<CardTitle className=""text-sm font-medium"">Overall Pass Rate</CardTitle>"
medium,app/reports/quality-metrics/page.tsx,376,"Inspection Type","检验类型",tables.headers.inspectionType,"<TableHead>Inspection Type</TableHead>"
low,lib/validations.ts,58,"Margin percentage must be a valid number between -100% and 1000%","利润率必须是-100%到1000%之间的有效数字",validation.marginPercentage,"message: ""Margin percentage must be a valid number between -100% and 1000%"""