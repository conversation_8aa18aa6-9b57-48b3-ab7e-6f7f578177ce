[{"file": "app/samples/create/page.tsx", "line": 233, "text": "Sample Direction *", "context": "<Label htmlFor=\"sample_direction\">Sample Direction *</Label>", "suggestedKey": "samples.form.direction.label", "englishText": "Sample Direction *", "chineseText": "样品方向 *", "needsReview": true, "category": "critical"}, {"file": "components/forms/customer-select.tsx", "line": 155, "text": "Customer Name *", "context": "<FormLabel>Customer Name *</FormLabel>", "suggestedKey": "forms.customer.name.label", "englishText": "Customer Name *", "chineseText": "客户名称 *", "needsReview": true, "category": "critical"}]