#!/usr/bin/env node

/**
 * Manufacturing ERP Translation Quality Improvement
 * 
 * Improves the quality of generated translations using professional
 * Manufacturing ERP terminology and business language standards.
 * 
 * ZERO BREAKING CHANGES: All improvements go to parallel workflow.
 */

const fs = require('fs');
const path = require('path');

// Professional Manufacturing ERP Translation Dictionary
const PROFESSIONAL_TRANSLATIONS = {
  // Dashboard & Analytics
  'Total Revenue': '总收入',
  'Profit Margin': '利润率',
  'Pending Receivables': '待收账款',
  'Active Customers': '活跃客户',
  'Active Work Orders': '活跃工作订单',
  'Production Efficiency': '生产效率',
  'Active Shipments': '活跃发货',
  'On-Time Delivery': '准时交付',
  'Completion Rate': '完成率',
  'Average Efficiency': '平均效率',
  'Quality Pass Rate': '质量合格率',
  'Production Status Breakdown': '生产状态分析',
  'Top Products by Volume': '产量排行',
  'Production Overview': '生产概览',
  'Efficiency Analysis by Product': '产品效率分析',
  
  // Core Manufacturing Terms
  'Product': '产品',
  'Progress': '进度',
  'Quality': '质量',
  'Stock': '库存',
  'Inventory': '库存',
  'Raw Materials': '原材料',
  'Finished Goods': '成品',
  'Work in Progress': '在制品',
  'Bill of Materials': '物料清单',
  'Material Requirements Planning': '物料需求计划',
  'Work Order': '工作订单',
  'Quality Control': '质量控制',
  'Quality Inspection': '质量检验',
  'Manufacturing Process': '制造工艺',
  'Production Schedule': '生产计划',
  
  // Status Terms
  'Pending': '待处理',
  'Approved': '已批准',
  'Rejected': '已拒绝',
  'In Progress': '进行中',
  'Completed': '已完成',
  'Cancelled': '已取消',
  'On Hold': '暂停',
  'Ready': '就绪',
  'Shipped': '已发货',
  'Delivered': '已交付',
  'Draft': '草稿',
  'Active': '活跃',
  'Inactive': '非活跃',
  
  // Action Terms
  'Create': '创建',
  'Edit': '编辑',
  'Delete': '删除',
  'Save': '保存',
  'Cancel': '取消',
  'Submit': '提交',
  'Approve': '批准',
  'Reject': '拒绝',
  'View': '查看',
  'Export': '导出',
  'Import': '导入',
  'Print': '打印',
  'Search': '搜索',
  'Filter': '筛选',
  'Sort': '排序',
  'Retry': '重试',
  'Refresh': '刷新',
  'Update': '更新',
  
  // Form Fields & Labels
  'Name': '名称',
  'Description': '描述',
  'Quantity': '数量',
  'Price': '价格',
  'Total': '总计',
  'Date': '日期',
  'Status': '状态',
  'Type': '类型',
  'Category': '类别',
  'Location': '位置',
  'Address': '地址',
  'Phone': '电话',
  'Email': '邮箱',
  'Contact': '联系人',
  'Notes': '备注',
  'Comments': '评论',
  'Reference': '参考',
  'Code': '代码',
  'ID': '编号',
  'Number': '编号',
  
  // Business Terms
  'Customer': '客户',
  'Supplier': '供应商',
  'Vendor': '供应商',
  'Purchase Order': '采购订单',
  'Sales Order': '销售订单',
  'Invoice': '发票',
  'Payment': '付款',
  'Receipt': '收据',
  'Delivery': '交付',
  'Shipping': '运输',
  'Export Declaration': '出口申报',
  'Customs': '海关',
  'Certificate': '证书',
  'Compliance': '合规',
  'Contract': '合同',
  'Agreement': '协议',
  
  // Financial Terms
  'Accounting': '会计',
  'Accounts Receivable': '应收账款',
  'Accounts Payable': '应付账款',
  'Balance': '余额',
  'Credit': '贷方',
  'Debit': '借方',
  'Revenue': '收入',
  'Expense': '费用',
  'Profit': '利润',
  'Loss': '亏损',
  'Budget': '预算',
  'Cost': '成本',
  
  // Time & Scheduling
  'Schedule': '计划',
  'Timeline': '时间线',
  'Deadline': '截止日期',
  'Due Date': '到期日期',
  'Start Date': '开始日期',
  'End Date': '结束日期',
  'Duration': '持续时间',
  'Lead Time': '交货期',
  
  // Measurements & Units
  'Unit': '单位',
  'Piece': '件',
  'Kilogram': '千克',
  'Meter': '米',
  'Liter': '升',
  'Hour': '小时',
  'Day': '天',
  'Week': '周',
  'Month': '月',
  'Year': '年',
  
  // Common UI Elements
  'Dashboard': '仪表板',
  'Overview': '概览',
  'Summary': '摘要',
  'Details': '详情',
  'Settings': '设置',
  'Configuration': '配置',
  'Preferences': '偏好设置',
  'Profile': '档案',
  'Account': '账户',
  'Login': '登录',
  'Logout': '退出',
  'Register': '注册',
  'Password': '密码',
  'Username': '用户名',
  
  // Navigation & Menu
  'Home': '首页',
  'Back': '返回',
  'Next': '下一步',
  'Previous': '上一步',
  'Continue': '继续',
  'Finish': '完成',
  'Close': '关闭',
  'Open': '打开',
  'Menu': '菜单',
  'Navigation': '导航',
  
  // Data & Reports
  'Report': '报告',
  'Reports': '报告',
  'Analytics': '分析',
  'Metrics': '指标',
  'Statistics': '统计',
  'Chart': '图表',
  'Graph': '图表',
  'Table': '表格',
  'List': '列表',
  'Grid': '网格',
  
  // Quality & Testing
  'Test': '测试',
  'Testing': '测试',
  'Inspection': '检验',
  'Audit': '审计',
  'Review': '审查',
  'Verification': '验证',
  'Validation': '验证',
  'Certification': '认证',
  'Standard': '标准',
  'Specification': '规格',
  
  // Common Placeholders
  'Search...': '搜索...',
  'Enter name': '输入名称',
  'Select option': '选择选项',
  'Choose file': '选择文件',
  'Upload': '上传',
  'Download': '下载',
  'Loading...': '加载中...',
  'Please wait...': '请稍候...',
  'No data': '无数据',
  'No results': '无结果',
  'Not found': '未找到',
  'Error': '错误',
  'Success': '成功',
  'Warning': '警告',
  'Information': '信息'
};

// Improve translation quality
function improveTranslation(originalText, currentTranslation) {
  // If it's a "needs review" translation, try to improve it
  if (currentTranslation.includes('[需要翻译:')) {
    const textToTranslate = originalText.trim();
    
    // Direct match
    if (PROFESSIONAL_TRANSLATIONS[textToTranslate]) {
      return PROFESSIONAL_TRANSLATIONS[textToTranslate];
    }
    
    // Partial matches and improvements
    let improved = textToTranslate;
    for (const [english, chinese] of Object.entries(PROFESSIONAL_TRANSLATIONS)) {
      if (textToTranslate.includes(english)) {
        improved = improved.replace(english, chinese);
      }
    }
    
    // If we made improvements, return them
    if (improved !== textToTranslate) {
      return improved;
    }
    
    // Return original if no improvement found
    return currentTranslation;
  }
  
  // For existing translations, check if we can improve them
  const textToTranslate = originalText.trim();
  if (PROFESSIONAL_TRANSLATIONS[textToTranslate]) {
    return PROFESSIONAL_TRANSLATIONS[textToTranslate];
  }
  
  // Return current translation if no improvement
  return currentTranslation;
}

// Process CSV file and improve translations
function improveCsvTranslations(csvFile) {
  console.log(`🔄 Improving translations in ${path.basename(csvFile)}...`);
  
  const content = fs.readFileSync(csvFile, 'utf8');
  const lines = content.split('\n');
  const header = lines[0];
  
  let improved = 0;
  let needsReview = 0;
  
  const improvedLines = [header];
  
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    if (!line.trim()) continue;
    
    // Parse CSV line (simple parsing for our format)
    const parts = line.split(',');
    if (parts.length < 3) continue;
    
    const key = parts[0];
    const english = parts[1].replace(/^"|"$/g, '').replace(/""/g, '"');
    const chinese = parts[2].replace(/^"|"$/g, '').replace(/""/g, '"');
    
    // Improve the translation
    const improvedChinese = improveTranslation(english, chinese);
    const wasImproved = improvedChinese !== chinese;
    const stillNeedsReview = improvedChinese.includes('[需要翻译:');
    
    if (wasImproved) improved++;
    if (stillNeedsReview) needsReview++;
    
    // Rebuild the line with improved translation
    parts[2] = `"${improvedChinese.replace(/"/g, '""')}"`;
    parts[8] = stillNeedsReview ? 'true' : 'false'; // Update "Needs Review" column
    
    improvedLines.push(parts.join(','));
  }
  
  // Write improved file
  const improvedFile = csvFile.replace('.csv', '-improved.csv');
  fs.writeFileSync(improvedFile, improvedLines.join('\n'));
  
  console.log(`   ✅ Improved ${improved} translations`);
  console.log(`   ⚠️  ${needsReview} still need manual review`);
  
  return { improved, needsReview, file: improvedFile };
}

// Main improvement process
function main() {
  console.log('🚀 Manufacturing ERP Translation Quality Improvement');
  console.log('⚠️  ZERO BREAKING CHANGES: Creating improved versions for review\n');
  
  const csvDir = 'i18n-temp/module-translations';
  if (!fs.existsSync(csvDir)) {
    console.log('❌ Module translations directory not found. Run manual-translation-template.js first.');
    process.exit(1);
  }
  
  const csvFiles = fs.readdirSync(csvDir).filter(f => f.endsWith('.csv') && !f.includes('-improved'));
  
  let totalImproved = 0;
  let totalNeedsReview = 0;
  const improvedFiles = [];
  
  csvFiles.forEach(csvFile => {
    const fullPath = path.join(csvDir, csvFile);
    const result = improveCsvTranslations(fullPath);
    
    totalImproved += result.improved;
    totalNeedsReview += result.needsReview;
    improvedFiles.push(result.file);
  });
  
  console.log('\n📋 TRANSLATION IMPROVEMENT COMPLETE');
  console.log('='.repeat(50));
  console.log(`Files processed: ${csvFiles.length}`);
  console.log(`Total improvements: ${totalImproved}`);
  console.log(`Still need review: ${totalNeedsReview}`);
  
  console.log('\n📁 Improved Files:');
  improvedFiles.forEach(file => {
    console.log(`   📄 ${path.basename(file)}`);
  });
  
  console.log('\n🎯 Next Steps:');
  console.log('   1. Review improved CSV files');
  console.log('   2. Manually edit remaining translations that need review');
  console.log('   3. Use integration script for safe deployment');
  console.log('   4. Test thoroughly in development environment');
  
  console.log('\n✅ Professional Manufacturing ERP translations ready for integration!');
}

// CLI interface
if (require.main === module) {
  main();
}

module.exports = { improveTranslation, improveCsvTranslations };
