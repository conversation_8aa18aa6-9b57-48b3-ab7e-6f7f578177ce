#!/usr/bin/env node

/**
 * Manufacturing ERP Precise Translation Integration
 * 
 * A more precise approach that only integrates clean, simple translations
 * to avoid syntax errors. Focuses on the most important UI elements first.
 * 
 * ZERO BREAKING CHANGES: Ultra-safe integration with validation.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const I18N_FILE = 'components/i18n-provider.tsx';
const BACKUP_DIR = 'i18n-backup';
const CSV_DIR = 'i18n-temp/module-translations';

// Create backup
function createBackup() {
  console.log('💾 Creating backup...');
  
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFile = path.join(BACKUP_DIR, `i18n-provider-precise-${timestamp}.tsx`);
  
  fs.copyFileSync(I18N_FILE, backupFile);
  console.log(`   ✅ Backup created: ${backupFile}`);
  
  return backupFile;
}

// Load only clean, simple translations
function loadCleanTranslations() {
  console.log('📥 Loading clean translations...');
  
  const csvFiles = fs.readdirSync(CSV_DIR).filter(f => f.endsWith('-improved.csv'));
  const cleanTranslations = { en: {}, zh: {} };
  
  let totalLoaded = 0;
  
  csvFiles.forEach(csvFile => {
    const filePath = path.join(CSV_DIR, csvFile);
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    // Skip header
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      if (!line.trim()) continue;
      
      try {
        const parts = line.split(',');
        if (parts.length < 9) continue;
        
        const key = parts[0];
        const english = parts[1].replace(/^"|"$/g, '').replace(/""/g, '"');
        const chinese = parts[2].replace(/^"|"$/g, '').replace(/""/g, '"');
        const needsReview = parts[8] === 'true';
        
        // Only include very clean translations
        if (!needsReview && 
            chinese && 
            !chinese.includes('[需要翻译:') &&
            !english.includes('<') && // No HTML
            !english.includes('>') && // No HTML
            !english.includes('{') && // No JSX
            !english.includes('}') && // No JSX
            !english.includes('()') && // No function calls
            !english.includes('className') && // No attributes
            english.length < 50 && // Keep it simple
            chinese.length < 50 && // Keep it simple
            key.length < 100 && // Reasonable key length
            !key.includes(' ') && // No spaces in keys
            /^[a-zA-Z0-9._]+$/.test(key) // Only valid key characters
        ) {
          cleanTranslations.en[key] = english;
          cleanTranslations.zh[key] = chinese;
          totalLoaded++;
        }
      } catch (error) {
        // Skip problematic lines
        continue;
      }
    }
  });
  
  console.log(`   ✅ Loaded ${totalLoaded} clean translations`);
  return cleanTranslations;
}

// Parse existing translations more carefully
function parseExistingTranslations() {
  console.log('📖 Parsing existing translations...');
  
  const content = fs.readFileSync(I18N_FILE, 'utf8');
  
  // Find the exact positions of the translation objects
  const enStart = content.indexOf('const en: Dict = {');
  const enEnd = content.indexOf('\n}', enStart) + 2;
  const zhStart = content.indexOf('const zh: Dict = {');
  const zhEnd = content.indexOf('\n}', zhStart) + 2;
  
  if (enStart === -1 || zhStart === -1) {
    throw new Error('Could not find translation objects');
  }
  
  const enSection = content.substring(enStart, enEnd);
  const zhSection = content.substring(zhStart, zhEnd);
  
  console.log(`   ✅ Found English section (${enSection.length} chars)`);
  console.log(`   ✅ Found Chinese section (${zhSection.length} chars)`);
  
  return { content, enSection, zhSection, enStart, enEnd, zhStart, zhEnd };
}

// Add translations to existing sections
function addTranslationsToSection(sectionContent, newTranslations, language) {
  console.log(`🔄 Adding translations to ${language} section...`);
  
  // Find the closing brace
  const closingBraceIndex = sectionContent.lastIndexOf('}');
  if (closingBraceIndex === -1) {
    throw new Error(`Could not find closing brace in ${language} section`);
  }
  
  // Prepare new translation lines
  const newLines = [];
  Object.entries(newTranslations).forEach(([key, value]) => {
    // Escape quotes properly
    const escapedValue = value.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
    newLines.push(`  "${key}": "${escapedValue}",`);
  });
  
  if (newLines.length === 0) {
    return sectionContent;
  }
  
  // Insert new translations before the closing brace
  const beforeClosing = sectionContent.substring(0, closingBraceIndex);
  const afterClosing = sectionContent.substring(closingBraceIndex);
  
  const updatedSection = beforeClosing + newLines.join('\n') + '\n' + afterClosing;
  
  console.log(`   ✅ Added ${newLines.length} translations to ${language} section`);
  return updatedSection;
}

// Create new content with added translations
function createNewContent(parsed, cleanTranslations) {
  console.log('🔧 Creating new content...');
  
  // Add translations to English section
  const newEnSection = addTranslationsToSection(
    parsed.enSection, 
    cleanTranslations.en, 
    'English'
  );
  
  // Add translations to Chinese section
  const newZhSection = addTranslationsToSection(
    parsed.zhSection, 
    cleanTranslations.zh, 
    'Chinese'
  );
  
  // Replace sections in original content
  let newContent = parsed.content;
  
  // Replace English section
  newContent = newContent.substring(0, parsed.enStart) + 
               newEnSection + 
               newContent.substring(parsed.enEnd);
  
  // Recalculate Chinese section position after English replacement
  const lengthDiff = newEnSection.length - parsed.enSection.length;
  const newZhStart = parsed.zhStart + lengthDiff;
  const newZhEnd = parsed.zhEnd + lengthDiff;
  
  // Replace Chinese section
  newContent = newContent.substring(0, newZhStart) + 
               newZhSection + 
               newContent.substring(newZhEnd);
  
  console.log('   ✅ New content created successfully');
  return newContent;
}

// Validate the new content
function validateNewContent(content) {
  console.log('✅ Validating new content...');
  
  try {
    // Check basic structure
    if (!content.includes('const en: Dict = {')) {
      throw new Error('Missing English translations structure');
    }
    
    if (!content.includes('const zh: Dict = {')) {
      throw new Error('Missing Chinese translations structure');
    }
    
    if (!content.includes('export function I18nProvider')) {
      throw new Error('Missing I18nProvider component');
    }
    
    // Check for syntax issues
    if (content.includes('""": "')) {
      throw new Error('Found malformed key-value pairs');
    }
    
    // Count braces to ensure they're balanced
    const openBraces = (content.match(/\{/g) || []).length;
    const closeBraces = (content.match(/\}/g) || []).length;
    
    if (openBraces !== closeBraces) {
      throw new Error(`Unbalanced braces: ${openBraces} open, ${closeBraces} close`);
    }
    
    console.log('   ✅ Content validation passed');
    return true;
  } catch (error) {
    console.error(`   ❌ Validation failed: ${error.message}`);
    return false;
  }
}

// Write new content safely
function writeNewContent(newContent) {
  console.log('💾 Writing new content...');
  
  try {
    // Write to temporary file first
    const tempFile = I18N_FILE + '.tmp';
    fs.writeFileSync(tempFile, newContent);
    
    // Validate temporary file
    if (validateNewContent(fs.readFileSync(tempFile, 'utf8'))) {
      // Replace original file
      fs.renameSync(tempFile, I18N_FILE);
      console.log('   ✅ New content written successfully');
      return true;
    } else {
      // Remove temporary file
      fs.unlinkSync(tempFile);
      console.log('   ❌ Validation failed, keeping original file');
      return false;
    }
  } catch (error) {
    console.error(`   ❌ Write failed: ${error.message}`);
    return false;
  }
}

// Main function
function main() {
  console.log('🚀 Manufacturing ERP Precise Translation Integration');
  console.log('⚠️  ULTRA-SAFE: Only clean, simple translations to avoid syntax errors\n');
  
  try {
    // Step 1: Create backup
    const backupFile = createBackup();
    
    // Step 2: Load only clean translations
    const cleanTranslations = loadCleanTranslations();
    
    if (Object.keys(cleanTranslations.en).length === 0) {
      console.log('❌ No clean translations found to integrate');
      return;
    }
    
    // Step 3: Parse existing translations
    const parsed = parseExistingTranslations();
    
    // Step 4: Create new content
    const newContent = createNewContent(parsed, cleanTranslations);
    
    // Step 5: Validate and write
    if (validateNewContent(newContent)) {
      if (writeNewContent(newContent)) {
        console.log('\n🎉 PRECISE INTEGRATION SUCCESSFUL!');
        console.log('='.repeat(50));
        console.log(`✅ Added ${Object.keys(cleanTranslations.en).length} clean translations`);
        console.log(`💾 Backup available: ${backupFile}`);
        
        console.log('\n🎯 Next Steps:');
        console.log('   1. Test your application: npm run dev');
        console.log('   2. Verify language switching works correctly');
        console.log('   3. Check key modules for improved localization');
        console.log('   4. If issues occur, restore from backup');
        
        console.log('\n✅ Your Manufacturing ERP has improved localization!');
        
      } else {
        console.log('\n❌ Integration failed during write phase');
        console.log(`💾 Original file preserved, backup available: ${backupFile}`);
      }
    } else {
      console.log('\n❌ Integration failed during validation');
      console.log(`💾 Original file preserved, backup available: ${backupFile}`);
    }
    
  } catch (error) {
    console.error('\n❌ Integration failed:', error.message);
    console.log('💾 Original file preserved');
    process.exit(1);
  }
}

// CLI interface
if (require.main === module) {
  main();
}

module.exports = { main };
