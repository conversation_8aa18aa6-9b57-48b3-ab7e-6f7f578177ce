#!/usr/bin/env node

/**
 * Manual Translation Template Generator
 * 
 * Creates comprehensive translation templates for all 1,763 hardcoded strings
 * organized by module and priority for systematic processing.
 * 
 * ZERO BREAKING CHANGES: All output goes to parallel workflow for safe integration.
 */

const fs = require('fs');
const path = require('path');

// Load detection results
const detectionFile = 'i18n-temp/last-detection-results.json';
if (!fs.existsSync(detectionFile)) {
  console.log('❌ Detection results not found. Run i18n-auto-detect.js first.');
  process.exit(1);
}

const detections = JSON.parse(fs.readFileSync(detectionFile, 'utf8'));

// Manufacturing ERP terminology mapping
const MANUFACTURING_TERMINOLOGY = {
  // Core Manufacturing Terms
  'Material Requirements Planning': '物料需求计划',
  'Bill of Materials': '物料清单',
  'Work Order': '工作订单',
  'Quality Control': '质量控制',
  'Raw Materials': '原材料',
  'Finished Goods': '成品',
  'Work in Progress': '在制品',
  'Inventory': '库存',
  'Stock Level': '库存水平',
  'Reorder Point': '再订货点',
  'Lead Time': '交货期',
  'Production Schedule': '生产计划',
  'Manufacturing Process': '制造工艺',
  'Quality Inspection': '质量检验',
  'Supplier': '供应商',
  'Customer': '客户',
  'Purchase Order': '采购订单',
  'Sales Order': '销售订单',
  'Delivery': '交付',
  'Shipping': '运输',
  'Export Declaration': '出口申报',
  'Customs': '海关',
  'Certificate': '证书',
  'Compliance': '合规',
  
  // Status Terms
  'Pending': '待处理',
  'Approved': '已批准',
  'Rejected': '已拒绝',
  'In Progress': '进行中',
  'Completed': '已完成',
  'Cancelled': '已取消',
  'On Hold': '暂停',
  'Ready': '就绪',
  'Shipped': '已发货',
  'Delivered': '已交付',
  
  // Action Terms
  'Create': '创建',
  'Edit': '编辑',
  'Delete': '删除',
  'Save': '保存',
  'Cancel': '取消',
  'Submit': '提交',
  'Approve': '批准',
  'Reject': '拒绝',
  'View': '查看',
  'Export': '导出',
  'Import': '导入',
  'Print': '打印',
  'Search': '搜索',
  'Filter': '筛选',
  'Sort': '排序',
  
  // Form Fields
  'Name': '名称',
  'Description': '描述',
  'Quantity': '数量',
  'Price': '价格',
  'Total': '总计',
  'Date': '日期',
  'Status': '状态',
  'Type': '类型',
  'Category': '类别',
  'Location': '位置',
  'Address': '地址',
  'Phone': '电话',
  'Email': '邮箱',
  'Contact': '联系人',
  'Notes': '备注',
  'Comments': '评论',
  
  // Dashboard Terms
  'Dashboard': '仪表板',
  'Overview': '概览',
  'Summary': '摘要',
  'Metrics': '指标',
  'Analytics': '分析',
  'Reports': '报告',
  'Charts': '图表',
  'Statistics': '统计',
  'Performance': '性能',
  'Trends': '趋势',
  
  // Financial Terms
  'Accounting': '会计',
  'Accounts Receivable': '应收账款',
  'Accounts Payable': '应付账款',
  'Invoice': '发票',
  'Payment': '付款',
  'Receipt': '收据',
  'Balance': '余额',
  'Credit': '贷方',
  'Debit': '借方',
  'Revenue': '收入',
  'Expense': '费用',
  'Profit': '利润',
  'Loss': '亏损'
};

// Generate translation key from text
function generateKey(text, category, context) {
  // Clean the text
  let key = text
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^_|_$/g, ''); // Remove leading/trailing underscores
  
  // Add category prefix
  const categoryPrefix = {
    'forms': 'forms.labels',
    'buttons': 'buttons',
    'navigation': 'nav',
    'tables': 'tables',
    'cards': 'cards',
    'status': 'status',
    'messages': 'messages',
    'placeholders': 'forms.placeholders'
  }[category] || 'common';
  
  return `${categoryPrefix}.${key}`;
}

// Smart translation using terminology mapping
function smartTranslate(text) {
  // Direct match
  if (MANUFACTURING_TERMINOLOGY[text]) {
    return MANUFACTURING_TERMINOLOGY[text];
  }
  
  // Partial matches
  let translation = text;
  for (const [english, chinese] of Object.entries(MANUFACTURING_TERMINOLOGY)) {
    if (text.includes(english)) {
      translation = translation.replace(english, chinese);
    }
  }
  
  // If no translation found, return placeholder
  if (translation === text) {
    return `[需要翻译: ${text}]`;
  }
  
  return translation;
}

// Process detections and create translation templates
function createTranslationTemplates() {
  console.log('🔄 Processing 1,763 hardcoded strings...');
  
  const templates = {
    dashboard: { translations: {}, count: 0 },
    mrp: { translations: {}, count: 0 },
    bom: { translations: {}, count: 0 },
    workOrders: { translations: {}, count: 0 },
    quality: { translations: {}, count: 0 },
    inventory: { translations: {}, count: 0 },
    rawMaterials: { translations: {}, count: 0 },
    locations: { translations: {}, count: 0 },
    shipping: { translations: {}, count: 0 },
    export: { translations: {}, count: 0 },
    accounting: { translations: {}, count: 0 },
    reports: { translations: {}, count: 0 },
    common: { translations: {}, count: 0 }
  };
  
  let totalProcessed = 0;
  
  detections.forEach(fileData => {
    const filePath = fileData.file;
    
    // Determine module based on file path
    let module = 'common';
    if (filePath.includes('/dashboard')) module = 'dashboard';
    else if (filePath.includes('/mrp')) module = 'mrp';
    else if (filePath.includes('/bom')) module = 'bom';
    else if (filePath.includes('/production') || filePath.includes('/work-order')) module = 'workOrders';
    else if (filePath.includes('/quality')) module = 'quality';
    else if (filePath.includes('/inventory')) module = 'inventory';
    else if (filePath.includes('/raw-material')) module = 'rawMaterials';
    else if (filePath.includes('/location')) module = 'locations';
    else if (filePath.includes('/shipping')) module = 'shipping';
    else if (filePath.includes('/export')) module = 'export';
    else if (filePath.includes('/finance') || filePath.includes('/accounting')) module = 'accounting';
    else if (filePath.includes('/report')) module = 'reports';
    
    fileData.detections.forEach(detection => {
      const text = detection.text;
      const category = detection.category || 'common';
      const key = generateKey(text, category, detection.context);
      const translation = smartTranslate(text);
      
      templates[module].translations[key] = {
        english: text,
        chinese: translation,
        context: detection.context,
        file: filePath,
        line: detection.line,
        category: category,
        priority: detection.priority || 'medium',
        needsReview: translation.includes('[需要翻译:')
      };
      
      templates[module].count++;
      totalProcessed++;
    });
  });
  
  console.log(`✅ Processed ${totalProcessed} strings across ${Object.keys(templates).length} modules`);
  return templates;
}

// Generate CSV files for each module
function generateModuleCSVs(templates) {
  const csvDir = 'i18n-temp/module-translations';
  if (!fs.existsSync(csvDir)) {
    fs.mkdirSync(csvDir, { recursive: true });
  }
  
  const csvFiles = [];
  
  Object.entries(templates).forEach(([module, data]) => {
    if (data.count === 0) return;
    
    const csvContent = [
      'Key,English,Chinese,Context,File,Line,Category,Priority,Needs Review,Comments'
    ];
    
    Object.entries(data.translations).forEach(([key, translation]) => {
      const row = [
        key,
        `"${translation.english.replace(/"/g, '""')}"`,
        `"${translation.chinese.replace(/"/g, '""')}"`,
        `"${translation.context.replace(/"/g, '""')}"`,
        translation.file,
        translation.line,
        translation.category,
        translation.priority,
        translation.needsReview ? 'true' : 'false',
        ''
      ].join(',');
      
      csvContent.push(row);
    });
    
    const csvFile = path.join(csvDir, `${module}-translations.csv`);
    fs.writeFileSync(csvFile, csvContent.join('\n'));
    csvFiles.push({ module, file: csvFile, count: data.count });
    
    console.log(`📄 Created ${module} CSV: ${data.count} translations`);
  });
  
  return csvFiles;
}

// Generate comprehensive translation JSON
function generateTranslationJSON(templates) {
  const englishTranslations = {};
  const chineseTranslations = {};
  
  Object.values(templates).forEach(moduleData => {
    Object.entries(moduleData.translations).forEach(([key, translation]) => {
      englishTranslations[key] = translation.english;
      chineseTranslations[key] = translation.chinese;
    });
  });
  
  const translationFile = 'i18n-temp/comprehensive-translations.json';
  const translationData = {
    metadata: {
      generatedAt: new Date().toISOString(),
      totalKeys: Object.keys(englishTranslations).length,
      modules: Object.keys(templates).filter(m => templates[m].count > 0),
      needsReview: Object.values(templates).reduce((sum, m) => 
        sum + Object.values(m.translations).filter(t => t.needsReview).length, 0
      )
    },
    english: englishTranslations,
    chinese: chineseTranslations
  };
  
  fs.writeFileSync(translationFile, JSON.stringify(translationData, null, 2));
  console.log(`💾 Saved comprehensive translations: ${translationFile}`);
  
  return translationFile;
}

// Generate integration script
function generateIntegrationScript(csvFiles, translationFile) {
  const integrationScript = `#!/usr/bin/env node

/**
 * Manufacturing ERP Comprehensive Translation Integration
 * 
 * Integrates all processed translations into the i18n system.
 * ZERO BREAKING CHANGES: Safe integration with rollback capability.
 */

const fs = require('fs');

console.log('🚀 Manufacturing ERP Translation Integration');
console.log('⚠️  ZERO BREAKING CHANGES: Safe integration with rollback capability');

// Load comprehensive translations
const translationData = JSON.parse(fs.readFileSync('${translationFile}', 'utf8'));

console.log(\`📊 Integration Summary:\`);
console.log(\`   Total translations: \${translationData.metadata.totalKeys}\`);
console.log(\`   Modules covered: \${translationData.metadata.modules.length}\`);
console.log(\`   Needs review: \${translationData.metadata.needsReview}\`);

// TODO: Implement safe integration logic
console.log('\\n🔄 Ready for safe integration with existing i18n system');
console.log('📋 Next steps:');
console.log('   1. Review translations in CSV files');
console.log('   2. Edit translations that need improvement');
console.log('   3. Run integration script when ready');
console.log('   4. Test thoroughly before deployment');

console.log('\\n📁 Generated files:');
${csvFiles.map(f => `console.log('   - ${f.module}: ${f.file} (${f.count} translations)');`).join('\n')}
`;

  const scriptFile = 'i18n-temp/integrate-translations.js';
  fs.writeFileSync(scriptFile, integrationScript);
  fs.chmodSync(scriptFile, '755');
  
  console.log(`🔧 Created integration script: ${scriptFile}`);
  return scriptFile;
}

// Main execution
function main() {
  console.log('🚀 Manufacturing ERP Manual Translation Template Generator');
  console.log('⚠️  ZERO BREAKING CHANGES: All output for safe review and integration\n');
  
  try {
    // Process all detections
    const templates = createTranslationTemplates();
    
    // Generate CSV files for each module
    const csvFiles = generateModuleCSVs(templates);
    
    // Generate comprehensive JSON
    const translationFile = generateTranslationJSON(templates);
    
    // Generate integration script
    const integrationScript = generateIntegrationScript(csvFiles, translationFile);
    
    // Summary
    console.log('\n📋 COMPREHENSIVE TRANSLATION TEMPLATE COMPLETE');
    console.log('='.repeat(60));
    console.log(`Total strings processed: ${detections.reduce((sum, f) => sum + f.detections.length, 0)}`);
    console.log(`Modules with translations: ${csvFiles.length}`);
    console.log(`Files generated: ${csvFiles.length + 2}`);
    
    console.log('\n📁 Generated Files:');
    csvFiles.forEach(f => {
      console.log(`   📄 ${f.module}: ${f.count} translations`);
    });
    console.log(`   💾 Comprehensive JSON: ${translationFile}`);
    console.log(`   🔧 Integration script: ${integrationScript}`);
    
    console.log('\n🎯 Next Steps:');
    console.log('   1. Review CSV files for each module');
    console.log('   2. Edit translations that need improvement');
    console.log('   3. Use integration script for safe deployment');
    console.log('   4. Test thoroughly in development environment');
    
    console.log('\n✅ Ready for comprehensive Manufacturing ERP localization!');
    
  } catch (error) {
    console.error('❌ Error generating translation templates:', error.message);
    process.exit(1);
  }
}

// CLI interface
if (require.main === module) {
  main();
}

module.exports = { createTranslationTemplates, generateModuleCSVs };
