{"metadata": {"generatedAt": "2025-09-15T09:09:35.601Z", "totalKeys": 1016, "modules": ["dashboard", "mrp", "bom", "workOrders", "quality", "inventory", "rawMaterials", "locations", "shipping", "export", "accounting", "reports", "common"], "needsReview": 756}, "english": {"common.windowlocationreload_classnamemt4_retry": " window.location.reload()} className=\"mt-4\">\n              Retry\n            ", "common.total_revenue": "Total Revenue", "common.profit_margin": "<PERSON><PERSON>", "common.pending_receivables": "Pending Receivables", "common.active_customers": "Active Customers", "common.active_work_orders": "Active Work Orders", "common.production_efficiency": "Production Efficiency", "common.active_shipments": "Active Shipments", "common.ontime_delivery": "On-Time Delivery", "common.active_forecasts": "Active Forecasts", "common.procurement_plans": "Procurement Plans", "common.average_margin": "Average Margin", "common.supplier_partners": "Supplier Partners", "forms.labels.raw_material": "Raw Material", "forms.labels.quantity_required": "Quantity Required *", "forms.labels.unit": "Unit", "forms.labels.waste_factor": "Waste Factor", "forms.labels.select_raw_material": "Select raw material", "forms.labels.eg_25": "e.g., 2.5", "forms.labels.eg_meters_kg_pieces": "e.g., meters, kg, pieces", "forms.labels.005_5": "0.05 (5%)", "common.setisadddialogopenfalse_cancel": " setIsAddDialogOpen(false)}>\n                    Cancel\n                  ", "common.setiseditdialogopenfalse_cancel": " setIsEditDialogOpen(false)}>\n                    Cancel\n                  ", "tables.material": "Material", "tables.qty_required": "Qty Required", "tables.unit": "Unit", "tables.waste_factor": "Waste Factor", "tables.cost": "Cost", "tables.status": "Status", "tables.actions": "Actions", "common.bill_of_materials": "Bill of Materials", "messages.please_fill_in_all_required_fields": "Please fill in all required fields", "messages.bom_item_added_successfully": "BOM item added successfully", "messages.network_error_occurred": "Network error occurred", "messages.bom_item_updated_successfully": "BOM item updated successfully", "messages.bom_item_removed_successfully": "BOM item removed successfully", "common.bom_updated_successfully": "BOM updated successfully", "common.bom_cleared_successfully": "BOM cleared successfully", "common.bom_item_deleted_successfully": "BOM item deleted successfully", "forms.labels.search_products": "Search products...", "forms.labels.filter_by_status": "Filter by status", "tables.product": "Product", "tables.bom_status": "BOM Status", "tables.materials": "Materials", "tables.categories": "Categories", "tables.material_cost": "Material Cost", "tables.selling_price": "Selling <PERSON>", "tables.profit": "Profit", "tables.margin": "Margin %", "tables.last_updated": "Last Updated", "common.total_products": "Total Products", "common.with_bom": "With BOM", "common.without_bom": "Without BOM", "common.total_value": "Total Value", "common.bom_overview": "BOM Overview", "common.all_products": "All Products", "common.complete_boms": "Complete BOMs", "common.incomplete_boms": "Incomplete BOMs", "common.no_bom": "No BOM", "tables.avg_efficiency": "Avg Efficiency", "tables.total_orders": "Total Orders", "tables.completed": "Completed", "tables.completion_rate": "Completion Rate", "tables.work_order": "Work Order", "tables.customer": "Customer", "tables.progress": "Progress", "tables.quality": "Quality", "tables.stock": "Stock", "common.completion_rate": "Completion Rate", "common.average_efficiency": "Average Efficiency", "common.quality_pass_rate": "Quality Pass Rate", "common.in_progress": "In Progress", "common.production_status_breakdown": "Production Status Breakdown", "common.top_products_by_volume": "Top Products by Volume", "common.production_overview": "Production Overview", "common.efficiency_analysis_by_product": "Efficiency Analysis by Product", "common.quality_integration_metrics": "Quality Integration Metrics", "common.recent_work_orders": "Recent Work Orders", "forms.labels.search_work_orders": "Search work orders...", "forms.labels.all_status": "All Status", "forms.labels.all_contracts": "All Contracts", "forms.labels.all_products": "All Products", "tables.contract_work_order": "Contract / Work Order", "tables.quantity": "Quantity", "tables.due_date": "Due Date", "tables.priority": "Priority", "tables.notes": "Notes", "common.total_orders": "Total Orders", "common.pending": "Pending", "common.completed": "Completed", "common.overdue": "Overdue", "common.high_priority": "High Priority", "forms.labels.work_order_number": "Work Order Number", "forms.labels.quantity": "Quantity", "forms.labels.priority": "Priority", "forms.labels.production_notes_optional": "Production Notes (Optional)", "forms.labels.wo241225abcd": "WO-241225-ABCD", "forms.labels.search_and_select_a_product": "Search and select a product...", "forms.labels.search_and_select_a_sales_contract": "Search and select a sales contract...", "forms.labels.add_any_special_instructions_or_notes_for_production": "Add any special instructions or notes for production...", "common.available_resources": "Available Resources", "common.work_order_tips": "Work Order Tips", "common.production_process": "Production Process", "forms.labels.sales_contract": "Sales Contract", "forms.labels.product": "Product", "forms.labels.customer": "Customer", "forms.labels.due_date": "Due Date", "forms.labels.status": "Status", "forms.labels.production_notes": "Production Notes", "forms.labels.enter_quantity": "Enter quantity", "forms.labels.add_production_notes_instructions_or_comments": "Add production notes, instructions, or comments...", "common.work_order_information": "Work Order Information", "common.edit_work_order_details": "Edit Work Order Details", "common.low": "Low", "common.normal": "Normal", "common.high": "High", "common.urgent": "<PERSON><PERSON>", "common.on_hold": "On Hold", "common.all_materials_are_available_production_can_proceed": "All materials are available. Production can proceed.", "forms.labels.add_production_notes": "Add production notes...", "common.request_contract_update": "\n                      Request Contract Update\n                    ", "common.notify_sales_team": "\n                      Notify Sales Team\n                    ", "common.mark_reviewed": "\n                      <PERSON> Reviewed\n                    ", "forms.labels.autocreate_quality_inspections": "\n                    Auto-create quality inspections\n                  ", "forms.labels.add_any_special_instructions_for_production": "Add any special instructions for production...", "common.setopenfalse_cancel": " setOpen(false)}>\n            Cancel\n          ", "common.work_orders_to_create": "Work Orders to Create", "common.generation_options": "Generation Options", "tables.inspection_type": "Inspection Type", "tables.total_inspections": "Total Inspections", "tables.pass_rate": "Pass Rate", "tables.performance": "Performance", "tables.inspections": "Inspections", "tables.quality_score": "Quality Score", "tables.inspection_date": "Inspection Date", "tables.type": "Type", "tables.inspector": "Inspector", "tables.defects": "Defects", "tables.certificates": "Certificates", "common.overall_pass_rate": "Overall Pass Rate", "common.defect_rate": "Defect Rate", "common.pending_inspections": "Pending Inspections", "common.certificate_validity": "Certificate Validity", "common.inspection_status_breakdown": "Inspection Status Breakdown", "common.defect_severity_analysis": "Defect Severity Analysis", "common.quality_inspections_by_type": "Quality Inspections by Type", "common.product_quality_performance": "Product Quality Performance", "common.quality_trends_last_6_months": "Quality Trends (Last 6 Months)", "common.recent_quality_inspections": "Recent Quality Inspections", "forms.labels.work_order": "Work Order *", "forms.labels.inspector": "Inspector *", "forms.labels.inspection_type": "Inspection Type", "forms.labels.scheduled_date": "Scheduled Date", "forms.labels.notes": "Notes", "forms.labels.select_work_order": "Select work order", "forms.labels.inspector_name": "Inspector name", "forms.labels.inspection_notes_or_requirements": "Inspection notes or requirements", "forms.labels.search_inspections": "Search inspections...", "common.setshowcreateformfalse_cancel": " setShowCreateForm(false)}>\n                Cancel\n              ", "tables.contract_inspection": "Contract / Inspection", "tables.count_progress": "Count / Progress", "tables.customer_work_order": "Customer / Work Order", "common.total_inspections": "Total Inspections", "common.pending_review": "Pending Review", "common.passed": "Passed", "common.failed": "Failed", "common.archived": "Archived", "common.create_quality_inspection": "Create Quality Inspection", "common.incoming_material": "Incoming Material", "common.inprocess": "In-Process", "common.final_inspection": "Final Inspection", "common.preshipment": "Pre-Shipment", "common.all_status": "All Status", "common.inspection_results": "Inspection Results", "forms.labels.completed_date": "Completed Date", "common.quality_inspection_created_successfully": "Quality inspection created successfully", "common.updated_successfully": "Updated successfully", "common.certificate_generated_successfully": "Certificate generated successfully", "common.inspector_assigned_successfully": "Inspector assigned successfully", "common.quality_approved_successfully": "Quality approved successfully", "common.inspection_scheduled_successfully": "Inspection scheduled successfully", "common.quality_metrics_refreshed_successfully": "Quality metrics refreshed successfully", "common.inspection_updated_successfully": "Inspection updated successfully", "common.invalid_quality_status": "Invalid quality status", "common.no_valid_quality_certificate_found_for_this_product": "No valid quality certificate found for this product", "forms.labels.explain_why_this_inspection_needs_to_be_unarchived_eg_customer_requested_reinspection_archive_was_done_in_error_quality_issue_requires_investigation": "Explain why this inspection needs to be unarchived (e.g., 'Customer requested re-inspection', 'Archive was done in error', 'Quality issue requires investigation')", "common.cancel": "\n              Cancel\n            ", "forms.labels.search": "Search...", "tables.report": "Report", "tables.created": "Created", "tables.records": "Records", "tables.certificate": "Certificate", "tables.issued": "Issued", "tables.expires": "Expires", "common.draft": "Draft", "common.approved": "Approved", "common.published": "Published", "common.active": "Active", "common.expired": "Expired", "common.pass_rate": "Pass Rate", "common.first_pass_yield": "First Pass Yield", "common.last_7_days": "Last 7 days", "common.last_30_days": "Last 30 days", "common.last_90_days": "Last 90 days", "common.last_year": "Last year", "forms.labels.archive_reason": "Archive Reason *", "forms.labels.additional_notes_optional": "Additional Notes (Optional)", "forms.labels.select_a_reason_for_archiving": "Select a reason for archiving", "forms.labels.provide_additional_context_for_archiving_this_inspection": "Provide additional context for archiving this inspection...", "tables.avg_cost": "Avg Cost", "tables.total_value": "Total Value", "tables.lots": "Lots", "tables.current_stock": "Current Stock", "tables.forecast_demand": "Forecast Demand", "tables.recommendation": "Recommendation", "tables.location": "Location", "tables.item_count": "<PERSON><PERSON>", "tables.total_quantity": "Total Quantity", "tables.utilization": "Utilization", "tables.movement_status": "Movement Status", "common.total_inventory_value": "Total Inventory Value", "common.stock_health_score": "Stock Health Score", "common.low_stock_alerts": "Low Stock Alerts", "common.storage_utilization": "Storage Utilization", "common.inventory_composition": "Inventory Composition", "common.cost_distribution_analysis": "Cost Distribution Analysis", "common.stock_levels_by_product": "Stock Levels by Product", "common.reorder_recommendations": "Reorder Recommendations", "common.inventory_by_location": "Inventory by Location", "common.inventory_turnover_analysis": "Inventory Turnover Analysis", "forms.labels.location": "Location", "forms.labels.reason": "Reason", "forms.labels.reference_optional": "Reference (Optional)", "forms.labels.notes_optional": "Notes (Optional)", "forms.labels.location_optional": "Location (Optional)", "forms.labels.from_location": "From Location", "forms.labels.to_location": "To Location", "forms.labels.adjustment_quantity": "Adjustment Quantity", "forms.labels.notes_required": "Notes (Required)", "forms.labels.reference": "Reference", "forms.labels.action": "Action", "forms.labels.adjustment_qty": "Adjustment Qty (±)", "forms.labels.transaction_type_filter": "Transaction Type Filter", "forms.labels.po_number_etc": "PO Number, etc.", "forms.labels.additional_notes": "Additional notes", "forms.labels.any_location": "Any location", "forms.labels.order_number_etc": "Order number, etc.", "forms.labels.select_source": "Select source", "forms.labels.select_destination": "Select destination", "forms.labels.transfer_reason": "Transfer reason", "forms.labels.0": "±0", "forms.labels.explain_the_adjustment_reason": "Explain the adjustment reason", "forms.labels.select": "Select", "forms.labels.po_number": "PO Number", "forms.labels.so_number": "SO Number", "forms.labels.or_amount": "+ or - amount", "forms.labels.explain_adjustment_reason": "Explain adjustment reason", "forms.labels.search_products_sku_lot_number_or_work_order": "Search products, SKU, lot number, or work order...", "forms.labels.quality": "Quality", "common.onopenchangefalse_cancel": " onOpenChange(false)}>\n              Cancel\n            ", "common.process_adjustment": "Process Adjustment", "tables.reference": "Reference", "tables.time": "Time", "tables.from_location": "From Location", "tables.transfer_route": "Transfer Route", "tables.locations": "Locations", "tables.quality_status": "Quality Status", "tables.lot_number": "Lot Number", "tables.product_details": "Product Details", "tables.workflow": "Workflow", "common.stock_analytics": "Stock Analytics", "common.recent_activity": "Recent Activity", "common.any_location": "Any location", "common.all_quality": "All Quality", "common.quarantined": "Quarantined", "common.rejected": "Rejected", "common.all_locations": "All Locations", "common.all_transactions": "All Transactions", "common.inbound_only": "Inbound Only", "common.outbound_only": "Outbound Only", "common.transfer_only": "Transfer Only", "common.adjustment_only": "Adjustment Only", "common.inventory_metrics_refreshed_successfully": "Inventory metrics refreshed successfully", "tables.shipment": "Shipment", "tables.total_qty": "Total Qty", "tables.inventory_impact": "Inventory Impact", "tables.risk_level": "Risk Level", "tables.recommended_action": "Recommended Action", "tables.expected_stock": "Expected Stock", "tables.discrepancy": "Discrepancy", "common.finished_goods_value": "Finished Goods Value", "common.finished_goods_units": "Finished Goods Units", "forms.labels.search_materials": "Search materials...", "forms.labels.filter_by_category": "Filter by category", "tables.material_name": "Material Name", "tables.category": "Category", "tables.primary_supplier": "Primary Supplier", "tables.standard_cost": "Standard Cost", "tables.stock_status": "Stock Status", "common.raw_materials_inventory": "Raw Materials Inventory", "common.all_categories": "All Categories", "common.yarn": "Yarn", "common.fabric": "<PERSON><PERSON><PERSON>", "common.dyes": "Dyes", "common.chemicals": "Chemicals", "common.accessories": "Accessories", "common.other": "Other", "common.inactive": "Inactive", "common.discontinued": "Discontinued", "common.raw_material_information": "Raw Material Information", "tables.supplier": "Supplier", "tables.unit_cost": "Unit Cost", "tables.received_date": "Received Date", "tables.quantity_required": "Quantity Required", "common.material_information": "Material Information", "common.inventory_settings": "Inventory Settings", "common.inventory_lots": "Inventory Lots", "common.bill_of_materials_usage": "Bill of Materials Usage", "common.lot_information": "Lot Information", "common.invalid_category": "Invalid category", "common.raw_material_status_changed_to_discontinued_you_can_now_delete_it_if_needed": "Raw material status changed to discontinued. You can now delete it if needed.", "common.raw_material_deleted_successfully": "Raw material deleted successfully", "forms.labels.sku": "SKU *", "forms.labels.material_name": "Material Name", "forms.labels.category": "Category", "forms.labels.primary_supplier": "Primary Supplier", "forms.labels.composition": "Composition", "forms.labels.quality_grade": "Quality Grade", "forms.labels.specifications": "Specifications", "forms.labels.standard_cost": "Standard Cost", "forms.labels.reorder_point": "Reorder Point", "forms.labels.lead_time_days": "Lead Time (Days)", "forms.labels.inspection_required": "Inspection Required", "forms.labels.quality_notes": "Quality Notes", "forms.labels.eg_yarncotton30s": "e.g., YARN-COTTON-30S", "forms.labels.eg_cotton_yarn_30s": "e.g., Cotton Yarn 30s", "forms.labels.select_category": "Select category", "forms.labels.eg_kg_meters_liters": "e.g., kg, meters, liters", "forms.labels.select_primary_supplier_optional": "Select primary supplier (optional)", "forms.labels.eg_100_cotton": "e.g., 100% Cotton", "forms.labels.eg_premium_standard": "e.g., Premium, Standard", "forms.labels.detailed_material_specifications": "Detailed material specifications...", "forms.labels.000": "0.00", "forms.labels.quality_control_notes_and_requirements": "Quality control notes and requirements...", "common.no": "No", "common.yes": "Yes", "forms.labels.lot_number": "Lot Number", "forms.labels.supplier": "Supplier", "forms.labels.unit_cost": "Unit Cost *", "forms.labels.currency": "<PERSON><PERSON><PERSON><PERSON>", "forms.labels.received_date": "Received Date *", "forms.labels.expiry_date": "Expiry Date", "forms.labels.quality_status": "Quality Status", "forms.labels.availability_status": "Availability Status", "forms.labels.storage_location": "Storage Location *", "forms.labels.batch_notes": "Batch Notes", "forms.labels.inspection_notes": "Inspection Notes", "forms.labels.eg_lot2024001": "e.g., LOT-2024-001", "forms.labels.select_supplier_optional": "Select supplier (optional)", "forms.labels.select_storage_location": "Select storage location", "forms.labels.notes_about_this_batchlot": "Notes about this batch/lot...", "forms.labels.quality_inspection_notes": "Quality inspection notes...", "common.available": "Available", "common.reserved": "Reserved", "common.consumed": "Consumed", "common.raw_materials_building_a": "Raw Materials - Building A", "common.raw_materials_outdoor_yard": "Raw Materials - Outdoor Yard", "common.main_finished_goods_warehouse": "Main Finished Goods Warehouse", "common.shanghai_distribution_center": "Shanghai Distribution Center", "common.beijing_distribution_center": "Beijing Distribution Center", "common.export_staging_area": "Export Staging Area", "common.quarantine_area": "Quarantine Area", "forms.labels.quality_tolerance": "Quality Tolerance", "forms.labels.eg_5": "e.g., ±5%", "tables.date": "Date", "tables.lot": "Lot", "tables.total_cost": "Total Cost", "common.error_loading_consumption_history": "Error Loading Consumption History", "common.consumption_history": "Consumption History", "forms.labels.location_name_id": "Location Name (ID)", "forms.labels.location_code": "Location Code", "forms.labels.description": "Description", "forms.labels.type": "Type", "forms.labels.capacity": "Capacity", "forms.labels.zone": "Zone", "forms.labels.location_name": "Location Name", "forms.labels.security_level": "Security Level", "forms.labels.temperature_controlled": "Temperature Controlled", "forms.labels.allows_mixed_products": "Allows Mixed Products", "forms.labels.eg_warehousenorth": "e.g., warehouse_north", "forms.labels.eg_wh001": "e.g., WH-001", "forms.labels.brief_description_of_the_location_purpose": "Brief description of the location purpose", "forms.labels.eg_a1_b2": "e.g., A1, B2", "forms.labels.enter_location_name": "Enter location name", "forms.labels.select_type": "Select type", "forms.labels.enter_location_description": "Enter location description", "forms.labels.eg_a1": "e.g., A1", "forms.labels.select_security_level": "Select security level", "common.setiscreatingfalse_cancel": " setIsCreating(false)}>\n                  Cancel\n                ", "common.seteditinglocationnull_cancel": " setEditingLocation(null)}>\n                    Cancel\n                  ", "common.update_location": "\n                    Update Location\n                  ", "tables.capacity": "Capacity", "tables.details": "Details", "common.location_directory": "Location Directory", "common.finished_goods": "Finished Goods", "common.raw_materials": "Raw Materials", "common.work_in_progress": "Work in Progress", "common.quality_control": "Quality Control", "common.shipping": "Shipping", "common.receiving": "Receiving", "common.returns": "Returns", "common.quarantine": "Quarantine", "common.medium": "Medium", "common.location_deleted_successfully": "Location deleted successfully", "forms.labels.select_pickup_location": "Select pickup location", "common.invalid_shipping_method": "Invalid shipping method", "common.shipment_cancelled_successfully": "Shipment cancelled successfully", "common.invalid_status": "Invalid status", "common.attention_required": "Attention Required", "common.shipping_overview": "Shipping Overview", "forms.labels.search_shipments_by_number_customer_or_tracking": "Search shipments by number, customer, or tracking...", "common.all_statuses": "All Statuses", "common.total_shipments": "Total Shipments", "common.avg_processing_time": "Avg Processing Time", "common.cost_per_shipment": "Cost per Shipment", "common.location_efficiency": "Location Efficiency", "common.inventory_accuracy": "Inventory Accuracy", "common.location_performance_metrics": "Location Performance Metrics", "common.location_distribution": "Location Distribution", "common.top_products": "Top Products", "common.capacity_utilization_analysis": "Capacity Utilization Analysis", "tables.method": "Method", "tables.items_qty": "Items & Qty", "tables.financial_impact": "Financial Impact", "tables.ship_date": "Ship Date", "tables.tracking": "Tracking", "forms.labels.unit_price": "Unit Price", "tables.unit_price": "Unit Price", "tables.total": "Total", "common.integration_by_shipment_status": "Integration by Shipment Status", "common.recent_shipments_integration_status": "Recent Shipments Integration Status", "forms.labels.shipping_method": "Shipping Method", "forms.labels.carrier": "Carrier", "forms.labels.tracking_number": "Tracking Number", "forms.labels.ship_date": "Ship Date", "forms.labels.estimated_delivery": "Estimated Delivery", "forms.labels.shipping_cost": "Shipping Cost", "forms.labels.insurance_cost": "Insurance Cost", "forms.labels.special_instructions": "Special Instructions", "forms.labels.select_a_customer": "Select a customer", "forms.labels.eg_dhl_fedex_maersk": "e.g., DHL, FedEx, Maersk", "forms.labels.enter_tracking_number": "Enter tracking number", "forms.labels.any_additional_notes_about_this_shipment": "Any additional notes about this shipment...", "forms.labels.special_handling_instructions_delivery_requirements_etc": "Special handling instructions, delivery requirements, etc.", "common.additional_information": "Additional Information", "forms.labels.carrier_optional": "Carrier (Optional)", "forms.labels.ship_date_optional": "Ship Date (Optional)", "forms.labels.estimated_delivery_optional": "Estimated Delivery (Optional)", "forms.labels.shipping_cost_optional": "Shipping Cost (Optional)", "forms.labels.insurance_cost_optional": "Insurance Cost (Optional)", "forms.labels.special_instructions_optional": "Special Instructions (Optional)", "forms.labels.search_contracts": "Search contracts...", "forms.labels.search_declarations": "Search declarations...", "tables.items": "Items", "common.export_declarations": "Export Declarations", "tables.hs_code": "HS Code", "common.declaration_items": "Declaration Items", "common.documents": "Documents", "messages.file_uploaded_successfully": "File uploaded successfully.", "messages.failed_to_upload_file": "Failed to upload file.", "messages.document_deleted_successfully": "Document deleted successfully.", "messages.failed_to_delete_document": "Failed to delete document.", "forms.labels.declaration_items": "Declaration Items", "forms.labels.000000": "0000.00", "common.total_declarations": "Total Declarations", "common.submitted": "Submitted", "common.cleared": "Cleared", "forms.labels.declaration_number": "Declaration Number *", "forms.labels.enter_declaration_number": "Enter declaration number", "forms.labels.select_status": "Select status", "forms.labels.exp2025001": "EXP-2025-001", "common.routerpushexport_cancel": " router.push('/export')}\n        >\n          Cancel\n        ", "forms.labels.sales_contract_optional": "Sales Contract (Optional)", "forms.labels.search_contracts_or_select_manual_entry": "Search contracts or select manual entry...", "common.retry": "\n            Retry\n          ", "tables.aging": "Aging", "forms.labels.search_invoices": "Search invoices...", "tables.invoice": "Invoice #", "tables.amount": "Amount", "tables.contract": "Contract", "common.invoice_list": "Invoice List", "common.sent": "<PERSON><PERSON>", "common.paid": "Paid", "forms.labels.invoice_number": "Invoice Number", "forms.labels.amount": "Amount *", "forms.labels.invoice_date": "Invoice Date", "forms.labels.payment_terms": "Payment Terms", "forms.labels.ar2024001": "AR-2024-001", "forms.labels.search_customers": "Search customers...", "forms.labels.select_payment_terms": "Select payment terms", "forms.labels.additional_notes_or_comments": "Additional notes or comments...", "common.invoice_details": "Invoice Details", "common.no_terms": "No Terms", "common.tt_telegraphic_transfer": "TT (Telegraphic Transfer)", "common.dp_documents_against_payment": "DP (Documents against Payment)", "common.lc_letter_of_credit": "LC (Letter of Credit)", "common.deposit": "<PERSON><PERSON><PERSON><PERSON>", "common.partial": "Partial", "common.cancelled": "Cancelled", "common.invoice_amount": "Invoice Amount", "common.amount_received": "Amount Received", "common.balance_due": "Balance Due", "common.aging": "Aging", "forms.labels.invoice_amount": "Invoice Amount", "forms.labels.amount_received": "Amount Received", "forms.labels.inv2024001": "INV-2024-001", "forms.labels.select_currency": "Select currency", "forms.labels.select_customer": "Select customer...", "forms.labels.select_sales_contract_optional": "Select sales contract (optional)", "forms.labels.enter_any_additional_notes_or_comments": "Enter any additional notes or comments...", "common.invoice_information": "Invoice Information", "common.financial_information": "Financial Information", "common.customer_and_contract": "Customer and Contract", "common.deposit_received": "<PERSON><PERSON><PERSON><PERSON> Received", "common.partial_paid": "Partial Paid", "common.usd_us_dollar": "USD - US Dollar", "common.eur_euro": "EUR - Euro", "common.cny_chinese_yuan": "CNY - Chinese Yuan", "common.gbp_british_pound": "GBP - British Pound", "common.jpy_japanese_yen": "JPY - Japanese Yen", "common.no_contract": "No Contract", "common.amount_must_be_a_valid_positive_number": "Amount must be a valid positive number", "common.received_amount_must_be_a_valid_nonnegative_number": "Received amount must be a valid non-negative number", "common.received": "Received", "forms.labels.purchase_contract_optional": "Purchase Contract (Optional)", "forms.labels.ap2024001": "AP-2024-001", "forms.labels.search_suppliers": "Search suppliers...", "common.amount_paid": "Amount <PERSON>", "forms.labels.amount_paid": "Amount <PERSON>", "forms.labels.bill2024001": "BILL-2024-001", "forms.labels.select_supplier": "Select supplier", "forms.labels.select_purchase_contract_optional": "Select purchase contract (optional)", "common.supplier_and_contract": "Supplier and Contract", "common.deposit_paid": "<PERSON><PERSON><PERSON><PERSON>", "common.paid_amount_must_be_a_valid_nonnegative_number": "Paid amount must be a valid non-negative number", "common.manufacturing_financial_kpis_retrieved_successfully": "Manufacturing financial KPIs retrieved successfully", "common.legacy_financial_summary_retrieved_successfully": "Legacy financial summary retrieved successfully", "common.aging_report_retrieved_successfully": "Aging report retrieved successfully", "common.ar_invoice_deleted_successfully": "AR invoice deleted successfully", "common.ap_invoice_deleted_successfully": "AP invoice deleted successfully", "forms.labels.search_and_select_sales_contract": "Search and select sales contract...", "forms.labels.search_and_select_customer": "Search and select customer...", "forms.labels.100000": "1000.00", "common.deposit_advance_payment": "Deposit (Advance Payment)", "common.30_deposit_70_tt": "30% Deposit + 70% TT", "common.50_deposit_50_lc": "50% Deposit + 50% LC", "forms.labels.search_and_select_purchase_contract": "Search and select purchase contract...", "forms.labels.search_and_select_supplier": "Search and select supplier...", "forms.labels.80000": "800.00", "common.inventory_value": "Inventory Value", "common.net_profit": "Net Profit", "common.outstanding_ar": "Outstanding AR", "common.profit_loss_summary": "Profit & Loss Summary", "common.recent_ar_invoices": "Recent AR Invoices", "common.accounts_receivable_aging": "Accounts Receivable Aging", "common.accounts_payable_aging": "Accounts Payable Aging", "common.cash_flow_analysis": "Cash Flow Analysis", "tables.contracts": "Contracts", "tables.total_revenue": "Total Revenue", "tables.last_activity": "Last Activity", "common.active_contracts": "Active Contracts", "common.customer_base": "Customer Base", "common.operational_efficiency": "Operational Efficiency", "common.financial_performance": "Financial Performance", "common.quality_operations": "Quality & Operations", "common.top_customers_by_revenue": "Top Customers by Revenue", "common.contract_performance_metrics": "Contract Performance Metrics", "common.revenue_trends_last_6_months": "Revenue Trends (Last 6 Months)", "common.operational_performance": "Operational Performance", "common.coming_soon": "\n                          Coming Soon\n                        ", "common.operating_cash_flow": "Operating Cash Flow", "common.investing_cash_flow": "Investing Cash Flow", "common.financing_cash_flow": "Financing Cash Flow", "forms.labels.eg_global_tech_supplies": "e.g. Global Tech Supplies", "forms.labels.eg_jane_smith": "e.g. <PERSON>", "forms.labels.eg_janesmithglobaltechcom": "e.g. <EMAIL>", "forms.labels.eg_1_5559876543": "e.g. ******-987-6543", "forms.labels.eg_456_industrial_ave_tech_city": "e.g. 456 Industrial Ave, Tech City", "forms.labels.eg_bank_of_innovation_*********": "e.g. Bank of Innovation, *********", "forms.labels.eg_vat_id_ein": "e.g. VAT ID, EIN", "common.name_must_be_at_least_2_characters": "Name must be at least 2 characters.", "common.invalid_email_address": "Invalid email address.", "forms.labels.search_samples_by_name_code_direction_status": "Search samples by name, code, direction, status...", "forms.labels.sample_direction": "Sample Direction", "forms.labels.sample_code": "Sample Code *", "forms.labels.sample_name": "Sample Name *", "forms.labels.sample_type": "Sample Type", "forms.labels.technical_specifications": "Technical specifications...", "forms.labels.quality_requirements": "Quality Requirements", "forms.labels.customer_receiving_sample": "Customer (Receiving Sample) *", "forms.labels.sample_purpose": "Sample Purpose", "forms.labels.delivery_instructions": "Delivery Instructions", "forms.labels.sample_sender_type": "Sample Sender Type *", "forms.labels.customer_sender": "Customer (Sender) *", "forms.labels.supplier_sender": "Supplier (Sender) *", "forms.labels.sample_condition_requirements": "Sample Condition & Requirements", "forms.labels.internal_purpose": "Internal Purpose", "forms.labels.internal_requirements": "Internal Requirements", "forms.labels.delivery_date": "Delivery Date", "forms.labels.sample_cost": "Sample Cost", "forms.labels.testing_status": "Testing Status", "forms.labels.testing_results": "Testing Results", "forms.labels.quote_requested": "Quote Requested", "forms.labels.quote_provided": "Quote Provided", "forms.labels.target_completion_date": "Target Completion Date", "forms.labels.eg_100": "e.g., 100", "forms.labels.eg_meters_pieces": "e.g., meters, pieces", "forms.labels.quality_standards": "Quality standards...", "forms.labels.search_customers_to_send_sample_to": "Search customers to send sample to...", "forms.labels.why_are_we_sending_this_sample": "Why are we sending this sample?", "forms.labels.special_delivery_instructions_customer_requirements": "Special delivery instructions, customer requirements...", "forms.labels.who_sent_us_this_sample": "Who sent us this sample?", "forms.labels.search_customer_who_sent_sample": "Search customer who sent sample...", "forms.labels.search_supplier_who_sent_sample": "Search supplier who sent sample...", "forms.labels.why_did_they_send_this_sample": "Why did they send this sample?", "forms.labels.sample_condition_when_received_analysis_requirements_customer_specifications": "Sample condition when received, analysis requirements, customer specifications...", "forms.labels.internal_testing_purpose": "Internal testing purpose", "forms.labels.internal_testing_requirements_project_details_department_specifications": "Internal testing requirements, project details, department specifications...", "forms.labels.eg_15000": "e.g. 150.00", "forms.labels.quality_analysis_material_properties_test_results": "Quality analysis, material properties, test results...", "common.product_information": "Product Information", "common.commercial_details": "Commercial Details", "common.outbound_we_send_to_customer": "📤 Outbound - We send to customer", "common.inbound_customersupplier_sends_to_us": "📥 Inbound - Customer/supplier sends to us", "common.internal_internal_testingrd": "🏭 Internal - Internal testing/R&D", "common.development": "Development", "common.production": "Production", "common.quality": "Quality", "common.prototype": "Prototype", "common.customer_evaluation": "Customer Evaluation", "common.marketing_demo": "Marketing Demo", "common.trade_show": "Trade Show", "common.sales_presentation": "Sales Presentation", "common.customer": "Customer", "common.supplier": "Supplier", "common.manufacturing_quote_request": "Manufacturing Quote Request", "common.material_testing": "Material Testing", "common.reverse_engineering": "Reverse Engineering", "common.quality_comparison": "Quality Comparison", "common.rd_testing": "R&D Testing", "common.process_improvement": "Process Improvement", "common.product_development": "Product Development", "common.not_started": "Not Started", "common.windowhistoryback_go_back": " window.history.back()}>\n                Go Back\n              ", "forms.labels.eg_pcs_kg_m": "e.g., pcs, kg, m", "forms.labels.eg_1050": "e.g., 10.50", "forms.labels.eg_sc2025001": "e.g., SC-2025-001", "forms.labels.eg_usd": "e.g., USD", "forms.labels.select_template": "Select template...", "forms.labels.select_product": "Select product...", "common.under_review": "Under Review", "messages.sales_contract_updated_successfully": "Sales contract updated successfully!", "messages.failed_to_update_sales_contract": "Failed to update sales contract", "messages.sales_contract_created_successfully": "Sales contract created successfully!", "messages.an_unexpected_error_occurred": "An unexpected error occurred", "forms.labels.eg_pc2025001": "e.g., PC-2025-001", "messages.purchase_contract_updated_successfully": "Purchase contract updated successfully!", "messages.failed_to_update_purchase_contract": "Failed to update purchase contract", "forms.labels.eg_highgrade_widget": "e.g. High-Grade Widget", "forms.labels.eg_widhg001": "e.g. WID-HG-001", "forms.labels.eg_pcs_kg_meters": "e.g. pcs, kg, meters", "forms.labels.eg_847990": "e.g. 847990", "forms.labels.eg_china": "e.g. China", "forms.labels.eg_carton_bag_pallet": "e.g. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "forms.labels.00": "0.0", "forms.labels.eg_5_01mm_tolerance": "e.g. ±5%, 0.1mm tolerance", "common.sku_must_be_at_least_2_characters": "SKU must be at least 2 characters.", "common.unit_is_required": "Unit is required.", "common.procurement_status": "Procurement Status", "common.action_required": "Action Required", "common.supplier_network": "Supplier Network", "common.avg_lead_time": "Avg Lead Time", "common.ready_to_order": "Ready to Order", "common.status": "Status", "common.priority": "Priority", "common.planned_quantity": "Planned Quantity", "common.estimated_cost": "Estimated Cost", "common.supplier_information": "Supplier Information", "common.notes": "Notes", "common.audit_information": "Audit Information", "forms.labels.search_forecasts": "Search forecasts...", "forms.labels.all_statuses": "All statuses", "forms.labels.all_methods": "All methods", "forms.labels.all_levels": "All levels", "tables.period": "Period", "tables.demand": "Demand", "tables.profit_margin": "<PERSON><PERSON>", "tables.confidence": "Confidence", "tables.supplier_prefs": "Supplier Prefs", "common.all_methods": "All Methods", "common.pipeline_analysis": "Pipeline Analysis", "common.manual_entry": "Manual Entry", "common.historical_data": "Historical Data", "common.hybrid_method": "Hybrid Method", "common.all_levels": "All Levels", "common.generated_procurement_plans": "Generated Procurement Plans", "common.forecast_details": "Forecast Details", "forms.labels.company_name": "Company Name *", "forms.labels.legal_company_name": "Legal Company Name", "forms.labels.email_address": "Email Address *", "forms.labels.phone_number": "Phone Number", "forms.labels.website": "Website", "forms.labels.address_line_1": "Address Line 1 *", "forms.labels.address_line_2": "Address Line 2", "forms.labels.city": "City", "forms.labels.stateprovince": "State/Province", "forms.labels.postal_code": "Postal Code", "forms.labels.country": "Country *", "forms.labels.industry": "Industry *", "forms.labels.business_type": "Business Type *", "forms.labels.number_of_employees": "Number of Employees *", "forms.labels.annual_revenue": "Annual Revenue", "forms.labels.business_registration_number": "Business registration number", "forms.labels.tax_id_ein": "Tax ID / EIN", "forms.labels.vat_number": "VAT Number", "forms.labels.bank_name": "Bank name", "forms.labels.bank_account_number": "Bank Account Number", "forms.labels.swiftbic_code": "SWIFT/BIC code", "forms.labels.bank_address": "Bank address", "forms.labels.export_license_number": "Export license number", "forms.labels.customs_code": "Customs Code", "forms.labels.preferred_incoterms": "Preferred Incoterms *", "forms.labels.preferred_payment_terms": "Preferred Payment Terms *", "forms.labels.enter_company_name": "Enter company name", "forms.labels.full_legal_name": "Full legal name", "forms.labels.companyexamplecom": "<EMAIL>", "forms.labels.1_555_1234567": "+1 (555) 123-4567", "forms.labels.street_address": "Street address", "forms.labels.apartment_suite_etc": "Apartment, suite, etc.", "forms.labels.state_or_province": "State or Province", "forms.labels.select_country": "Select country", "forms.labels.select_industry": "Select industry", "forms.labels.select_business_type": "Select business type", "forms.labels.select_employee_count": "Select employee count", "forms.labels.select_revenue_range": "Select revenue range", "forms.labels.registration_number": "Registration number", "forms.labels.tax_identification_number": "Tax identification number", "forms.labels.vat_registration_number": "VAT registration number", "forms.labels.name_of_your_bank": "Name of your bank", "forms.labels.account_number": "Account number", "forms.labels.swift_code": "SWIFT code", "forms.labels.banks_full_address": "Bank's full address", "forms.labels.customs_registration_code": "Customs registration code", "forms.labels.select_incoterms": "Select Incoterms", "common.united_states": "United States", "common.canada": "Canada", "common.china": "China", "common.india": "India", "common.bangladesh": "Bangladesh", "common.vietnam": "Vietnam", "common.turkey": "Turkey", "common.italy": "Italy", "common.germany": "Germany", "common.united_kingdom": "United Kingdom", "common.textile_manufacturing": "Textile Manufacturing", "common.apparel_fashion": "Apparel & Fashion", "common.home_textiles": "Home Textiles", "common.technical_textiles": "Technical Textiles", "common.leather_goods": "Leather Goods", "common.footwear": "Footwear", "common.other_manufacturing": "Other Manufacturing", "common.corporation": "Corporation", "common.limited_liability_company_llc": "Limited Liability Company (LLC)", "common.partnership": "Partnership", "common.sole_proprietorship": "Sole Proprietorship", "common.cooperative": "Cooperative", "common.110_employees": "1-10 employees", "common.1150_employees": "11-50 employees", "common.51200_employees": "51-200 employees", "common.201500_employees": "201-500 employees", "common.5011000_employees": "501-1000 employees", "common.1000_employees": "1000+ employees", "common.fob_free_on_board": "FOB - Free on Board", "common.cif_cost_insurance_freight": "CIF (Cost, Insurance & Freight)", "common.exw_ex_works": "EXW - Ex Works", "common.fca_free_carrier": "FCA - Free Carrier", "common.cpt_carriage_paid_to": "CPT - Carriage Paid To", "common.cip_carriage_insurance_paid_to": "CIP (Carriage & Insurance Paid To)", "common.dap_delivered_at_place": "DAP - Delivered at Place", "common.dpu_delivered_at_place_unloaded": "DPU - Delivered at Place Unloaded", "common.ddp_delivered_duty_paid": "DDP - Delivered Duty Paid", "common.net_30_days": "Net 30 days", "common.net_60_days": "Net 60 days", "common.net_90_days": "Net 90 days", "common.lc_at_sight": "L/C at sight", "common.lc_30_days": "L/C 30 days", "common.lc_60_days": "L/C 60 days", "common.tt_in_advance": "T/T in advance", "common.tt_30_advance_70_before_shipment": "T/T 30% advance, 70% before shipment", "common.tt_50_advance_50_before_shipment": "T/T 50% advance, 50% before shipment", "forms.labels.search_documents": "Search documents...", "forms.labels.filter_by_type": "Filter by type", "tables.document_name": "Document Name", "tables.export_declaration": "Export Declaration", "tables.created_date": "Created Date", "tables.size": "Size", "tables.created_by": "Created By", "tables.certificate_name": "Certificate Name", "tables.issuer": "Issuer", "tables.issue_date": "Issue Date", "tables.expiry_date": "Expiry Date", "tables.contract_name": "Contract Name", "tables.party": "Party", "tables.contract_date": "Contract Date", "tables.value": "Value", "tables.regulation": "Regulation", "tables.next_review": "Next Review", "common.all_types": "All Types", "common.commercial_invoice": "Commercial Invoice", "common.packing_list": "Packing List", "common.bill_of_lading": "Bill of Lading", "common.certificate_of_origin": "Certificate of Origin", "common.export_license": "Export License", "forms.labels.search_collections": "Search collections...", "forms.labels.season": "Season", "tables.design_id": "Design ID", "tables.name": "Name", "tables.collection": "Collection", "tables.fabric": "<PERSON><PERSON><PERSON>", "tables.colors": "Colors", "tables.costprice": "Cost/Price", "common.active_collections": "Active Collections", "common.total_designs": "Total Designs", "common.production_ready": "Production Ready", "common.color_palettes": "Color Palettes", "common.design_collections": "Design Collections", "common.individual_designs": "Individual Designs", "common.popular_categories": "Popular Categories", "common.seasonal_performance": "Seasonal Performance", "common.all_seasons": "All Seasons", "common.spring_2024": "Spring 2024", "common.summer_2024": "Summer 2024", "common.fall_2024": "Fall 2024", "common.winter_2024": "Winter 2024", "common.casual_wear": "<PERSON><PERSON>ual Wear", "common.formal_wear": "Formal Wear", "common.resort_wear": "Resort Wear", "common.activewear": "Activewear", "forms.labels.contact_person": "Contact Person", "forms.labels.phone": "Phone", "forms.labels.email": "Email", "forms.labels.address": "Address", "forms.labels.incoterm": "Incoterm", "forms.labels.abc_electronics_inc": "ABC Electronics Inc.", "forms.labels.sarah_johnson": "<PERSON>", "forms.labels.********": "******-0123", "forms.labels.sarahabcelectronicscom": "<EMAIL>", "forms.labels.1234_industrial_blvd_los_angeles_ca_90210_usa": "1234 Industrial Blvd, Los Angeles, CA 90210, USA", "forms.labels.ustax*********": "US-TAX-*********", "forms.labels.chase_bank_account_*********0": "Chase Bank - Account: *********0", "forms.labels.acme_corporation": "Acme Corporation", "forms.labels.john_smith": "<PERSON>", "forms.labels.johnacmecom": "<EMAIL>", "forms.labels.123_business_st_city_state_12345": "123 Business St, City, State 12345", "common.setshoweditdialogfalse_cancel": " setShowEditDialog(false)}>\n                    Cancel\n                  ", "common.update_customer": "\n                    Update Customer\n                  ", "common.30_days": "30 days", "common.60_days": "60 days", "common.90_days": "90 days", "common.cash": "Cash", "common.tt": "T/T", "forms.labels.customer_name": "Customer Name *", "forms.labels.contact_name": "Contact Name", "forms.labels.contact_email": "Contact Email", "forms.labels.contact_phone": "Contact Phone", "forms.labels.payment_term": "Payment Term", "forms.labels.eg_acme_inc": "e.g. Acme Inc.", "forms.labels.eg_john_doe": "e.g. <PERSON>", "forms.labels.eg_johndoeacmecom": "e.g. <EMAIL>", "forms.labels.eg_1_5551234567": "e.g. ******-123-4567", "forms.labels.eg_123_main_st_anytown_usa": "e.g. 123 Main St, Anytown USA", "forms.labels.eg_fob": "e.g. FOB", "forms.labels.eg_net_30": "e.g. Net 30", "messages.customer_updated_successfully": "Customer updated successfully.", "messages.failed_to_update_customer": "Failed to update customer.", "forms.labels.tax_id": "Tax ID", "forms.labels.bank_information": "Bank Information", "forms.labels.eg_ustax*********": "e.g. US-TAX-*********", "forms.labels.eg_chase_bank_account_*********0": "e.g. Chase Bank - Account: *********0", "forms.labels.select_incoterm": "Select incoterm", "messages.customer_created_successfully": "Customer created successfully.", "messages.failed_to_create_customer": "Failed to create customer.", "common.sales_contracts": "Sales Contracts", "forms.labels.standard_sales_contract": "Standard Sales Contract", "forms.labels.standard_purchase_contract": "Standard Purchase Contract", "forms.labels.select_annual_revenue": "Select annual revenue", "common.france": "France", "common.japan": "Japan", "common.brazil": "Brazil", "common.australia": "Australia", "common.textile_apparel": "Textile & Apparel", "common.electronics": "Electronics", "common.automotive": "Automotive", "common.food_beverage": "Food & Beverage", "common.machinery": "Machinery", "common.furniture": "Furniture", "common.500_employees": "500+ employees", "common.cip_carriage_and_insurance_paid_to": "CIP - Carriage and Insurance Paid To", "common.fas_free_alongside_ship": "FAS - Free Alongside Ship", "common.cfr_cost_and_freight": "CFR - Cost and Freight", "common.cif_cost_insurance_and_freight": "CIF - Cost, Insurance and Freight", "common.prepaid": "Prepaid", "common.net_15_days": "Net 15 days", "common.cash_on_delivery": "Cash on Delivery", "common.letter_of_credit": "Letter of Credit", "messages.cleanup_cancelled_confirmation_text_did_not_match": "Cleanup cancelled - confirmation text did not match", "messages.customer_cleanup_completed_successfully": "Customer cleanup completed successfully!", "messages.customer_cleanup_failed": "Customer cleanup failed: ", "messages.database_cleanup_completed_successfully": "Database cleanup completed successfully!", "messages.cleanup_failed": "Cleanup failed: ", "common.supplier_lead_time_updated_successfully": "Supplier lead time updated successfully", "common.this_feature_will_be_implemented_in_the_next_iteration": "This feature will be implemented in the next iteration", "common.lead_time_performance_updated_successfully": "Lead time performance updated successfully", "common.detailed_performance_history_tracking_not_yet_implemented": "Detailed performance history tracking not yet implemented", "common.market_benchmarking_functionality_not_yet_implemented": "Market benchmarking functionality not yet implemented", "common.individual_procurement_plan_creation_not_yet_implemented": "Individual procurement plan creation not yet implemented", "common.procurement_plans_generated_successfully_from_demand_forecast": "Procurement plans generated successfully from demand forecast", "common.procurement_plan_updated_successfully": "Procurement plan updated successfully", "common.purchase_order_creation_functionality_not_yet_implemented": "Purchase order creation functionality not yet implemented", "common.pipeline_forecast_generated_successfully": "Pipeline forecast generated successfully", "common.demand_forecast_updated_successfully": "Demand forecast updated successfully", "common.demand_forecast_deleted_successfully": "Demand forecast deleted successfully", "common.availability_checking_not_yet_implemented": "Availability checking not yet implemented", "common.procurement_plan_generation_not_yet_implemented": "Procurement plan generation not yet implemented", "common.container_optimization_not_yet_implemented": "Container optimization not yet implemented", "common.cannot_calculate_material_requirements_without_a_bom": "Cannot calculate material requirements without a BOM", "common.procurement_plan_structure_generated_implementation_pending": "Procurement plan structure generated (implementation pending)", "common.availability_checking_structure_generated_implementation_pending": "Availability checking structure generated (implementation pending)", "common.workflow_log_entry_created": "Workflow log entry created", "common.bom_data_seeded_successfully": "BOM data seeded successfully", "common.product_and_customer_data_cleanup_completed_successfully": "Product and customer data cleanup completed successfully", "common.customer_data_cleanup_completed_successfully": "Customer data cleanup completed successfully", "common.try_again": "\n                      Try Again\n                    ", "common.something_went_wrong": "Something went wrong", "common.page_error": "<PERSON> Error", "common.clear_all": "\n          Clear all\n        ", "forms.labels.approval_status": "Approval Status", "forms.labels.select_priority": "Select priority", "common.revision_required": "Revision Required", "common.all_suppliers": "All Suppliers", "common.all_priorities": "All Priorities", "common.created_date": "Created Date", "common.name": "Name", "common.code": "Code", "common.sample_date": "Sample Date", "common.approval_status": "Approval Status", "common.newest_first": "Newest First", "common.oldest_first": "Oldest First", "common.forecast_profitability": "Forecast Profitability", "forms.labels.search_materials_suppliers_or_skus": "Search materials, suppliers, or SKUs...", "common.handlebulkactionapprove_approve_selected": " handleBulkAction(\"approve\")}>\n                Approve Selected\n              ", "common.setselecteditems_clear_selection": " setSelectedItems([])}>\n                Clear Selection\n              ", "common.ordered": "Ordered", "common.all_priority": "All Priority", "forms.labels.material_sku": "Material SKU", "forms.labels.estimated_cost": "Estimated Cost", "forms.labels.estimated_lead_time": "Estimated Lead Time", "forms.labels.add_any_additional_notes_requirements_or_status_updates": "Add any additional notes, requirements, or status updates...", "common.plan_details": "Plan Details", "common.no_supplier_assigned": "No supplier assigned", "common.premium_fabric_mills_co": "Premium Fabric Mills Co", "common.global_textile_suppliers": "Global Textile Suppliers", "common.asia_pacific_materials": "Asia Pacific Materials", "forms.labels.supplier_optional": "Supplier (Optional)", "forms.labels.forecast_period": "Forecast Period", "forms.labels.forecasted_demand": "Forecasted Demand", "forms.labels.confidence_level": "Confidence Level", "forms.labels.forecast_method": "Forecast Method", "forms.labels.seasonality_adjustment": "Seasonality Adjustment", "forms.labels.trend_factor": "Trend Factor", "forms.labels.autoselect_best_supplier": "Auto-select best supplier", "forms.labels.select_period": "Select period", "forms.labels.10": "1.0", "forms.labels.add_any_additional_notes_or_assumptions": "Add any additional notes or assumptions...", "common.additional_notes": "Additional Notes", "common.no_adjustment": "No Adjustment", "common.apply_seasonality": "Apply Seasonality", "common.setshowpreviewfalse_close": " setShowPreview(false)}\n                  >\n                    Close\n                  ", "forms.labels.supplier_name": "Supplier Name *", "forms.labels.enter_supplier_name": "Enter supplier name", "forms.labels.contact_person_name": "Contact person name", "forms.labels.suppliercompanycom": "<EMAIL>", "forms.labels.supplier_address": "Supplier address", "messages.supplier_added_successfully": "Supplier added successfully!", "forms.labels.product_name": "Product Name *", "forms.labels.price": "Price", "forms.labels.enter_product_name": "Enter product name", "forms.labels.eg_silk001": "e.g., SILK-001", "forms.labels.eg_textiles_electronics": "e.g., Textiles, Electronics", "forms.labels.product_description_and_specifications": "Product description and specifications", "messages.product_added_successfully": "Product added successfully!", "forms.labels.enter_customer_name": "Enter customer name", "forms.labels.customercompanycom": "<EMAIL>", "forms.labels.customer_address": "Customer address", "messages.customer_added_successfully": "Customer added successfully!", "forms.labels.date_from": "Date From", "forms.labels.date_to": "Date To", "common.apply_filters": "\n                Apply Filters\n              ", "common.filters": "Filters", "common.total_margin": "Total Margin", "common.top_method": "Top Method", "common.optimization": "Optimization", "common.contract_margin_analysis": "Contract Margin Analysis", "common.method_performance": "Method Performance", "common.method_details": "Method Details", "common.cost_tracking_report": "Cost Tracking Report"}, "chinese": {"common.windowlocationreload_classnamemt4_retry": " window.location.reload()} class名称=\"mt-4\">\n              Retry\n            ", "common.total_revenue": "总计 收入", "common.profit_margin": "利润 Margin", "common.pending_receivables": "待处理 Receivables", "common.active_customers": "Active 客户s", "common.active_work_orders": "Active 工作订单s", "common.production_efficiency": "[需要翻译: Production Efficiency]", "common.active_shipments": "[需要翻译: Active Shipments]", "common.ontime_delivery": "On-Time 交付", "common.active_forecasts": "[需要翻译: Active Forecasts]", "common.procurement_plans": "[需要翻译: Procurement Plans]", "common.average_margin": "[需要翻译: Average Margin]", "common.supplier_partners": "供应商 Partners", "forms.labels.raw_material": "[需要翻译: Raw Material]", "forms.labels.quantity_required": "数量 Required *", "forms.labels.unit": "[需要翻译: Unit]", "forms.labels.waste_factor": "[需要翻译: Waste Factor]", "forms.labels.select_raw_material": "[需要翻译: Select raw material]", "forms.labels.eg_25": "[需要翻译: e.g., 2.5]", "forms.labels.eg_meters_kg_pieces": "[需要翻译: e.g., meters, kg, pieces]", "forms.labels.005_5": "[需要翻译: 0.05 (5%)]", "common.setisadddialogopenfalse_cancel": " setIsAddDialogOpen(false)}>\n                    取消\n                  ", "common.setiseditdialogopenfalse_cancel": " setIs编辑DialogOpen(false)}>\n                    取消\n                  ", "tables.material": "[需要翻译: Material]", "tables.qty_required": "[需要翻译: Qty Required]", "tables.unit": "[需要翻译: Unit]", "tables.waste_factor": "[需要翻译: Waste Factor]", "tables.cost": "[需要翻译: Cost]", "tables.status": "状态", "tables.actions": "[需要翻译: Actions]", "common.bill_of_materials": "物料清单", "messages.please_fill_in_all_required_fields": "[需要翻译: Please fill in all required fields]", "messages.bom_item_added_successfully": "[需要翻译: BOM item added successfully]", "messages.network_error_occurred": "[需要翻译: Network error occurred]", "messages.bom_item_updated_successfully": "[需要翻译: BOM item updated successfully]", "messages.bom_item_removed_successfully": "[需要翻译: BOM item removed successfully]", "common.bom_updated_successfully": "[需要翻译: BOM updated successfully]", "common.bom_cleared_successfully": "[需要翻译: BOM cleared successfully]", "common.bom_item_deleted_successfully": "[需要翻译: BOM item deleted successfully]", "forms.labels.search_products": "搜索 products...", "forms.labels.filter_by_status": "筛选 by status", "tables.product": "[需要翻译: Product]", "tables.bom_status": "BOM 状态", "tables.materials": "[需要翻译: Materials]", "tables.categories": "[需要翻译: Categories]", "tables.material_cost": "[需要翻译: Material Cost]", "tables.selling_price": "Selling 价格", "tables.profit": "利润", "tables.margin": "[需要翻译: Margin %]", "tables.last_updated": "[需要翻译: Last Updated]", "common.total_products": "总计 Products", "common.with_bom": "[需要翻译: With BOM]", "common.without_bom": "[需要翻译: Without BOM]", "common.total_value": "总计 Value", "common.bom_overview": "BOM 概览", "common.all_products": "[需要翻译: All Products]", "common.complete_boms": "[需要翻译: Complete BOMs]", "common.incomplete_boms": "[需要翻译: Incomplete BOMs]", "common.no_bom": "[需要翻译: No BOM]", "tables.avg_efficiency": "[需要翻译: Avg Efficiency]", "tables.total_orders": "总计 Orders", "tables.completed": "已完成", "tables.completion_rate": "[需要翻译: Completion Rate]", "tables.work_order": "工作订单", "tables.customer": "客户", "tables.progress": "[需要翻译: Progress]", "tables.quality": "[需要翻译: Quality]", "tables.stock": "[需要翻译: Stock]", "common.completion_rate": "[需要翻译: Completion Rate]", "common.average_efficiency": "[需要翻译: Average Efficiency]", "common.quality_pass_rate": "[需要翻译: Quality Pass Rate]", "common.in_progress": "进行中", "common.production_status_breakdown": "Production 状态 Breakdown", "common.top_products_by_volume": "[需要翻译: Top Products by Volume]", "common.production_overview": "Production 概览", "common.efficiency_analysis_by_product": "[需要翻译: Efficiency Analysis by Product]", "common.quality_integration_metrics": "Quality Integration 指标", "common.recent_work_orders": "Recent 工作订单s", "forms.labels.search_work_orders": "搜索 work orders...", "forms.labels.all_status": "All 状态", "forms.labels.all_contracts": "[需要翻译: All Contracts]", "forms.labels.all_products": "[需要翻译: All Products]", "tables.contract_work_order": "Contract / 工作订单", "tables.quantity": "数量", "tables.due_date": "Due 日期", "tables.priority": "[需要翻译: Priority]", "tables.notes": "备注", "common.total_orders": "总计 Orders", "common.pending": "待处理", "common.completed": "已完成", "common.overdue": "[需要翻译: Overdue]", "common.high_priority": "[需要翻译: High Priority]", "forms.labels.work_order_number": "工作订单 Number", "forms.labels.quantity": "数量", "forms.labels.priority": "[需要翻译: Priority]", "forms.labels.production_notes_optional": "Production 备注 (Optional)", "forms.labels.wo241225abcd": "[需要翻译: WO-241225-ABCD]", "forms.labels.search_and_select_a_product": "搜索 and select a product...", "forms.labels.search_and_select_a_sales_contract": "搜索 and select a sales contract...", "forms.labels.add_any_special_instructions_or_notes_for_production": "[需要翻译: Add any special instructions or notes for production...]", "common.available_resources": "[需要翻译: Available Resources]", "common.work_order_tips": "工作订单 Tips", "common.production_process": "[需要翻译: Production Process]", "forms.labels.sales_contract": "[需要翻译: Sales Contract]", "forms.labels.product": "[需要翻译: Product]", "forms.labels.customer": "客户", "forms.labels.due_date": "Due 日期", "forms.labels.status": "状态", "forms.labels.production_notes": "Production 备注", "forms.labels.enter_quantity": "[需要翻译: Enter quantity]", "forms.labels.add_production_notes_instructions_or_comments": "[需要翻译: Add production notes, instructions, or comments...]", "common.work_order_information": "工作订单 Information", "common.edit_work_order_details": "编辑 工作订单 Details", "common.low": "[需要翻译: Low]", "common.normal": "[需要翻译: Normal]", "common.high": "[需要翻译: High]", "common.urgent": "[需要翻译: Urgent]", "common.on_hold": "暂停", "common.all_materials_are_available_production_can_proceed": "[需要翻译: All materials are available. Production can proceed.]", "forms.labels.add_production_notes": "[需要翻译: Add production notes...]", "common.request_contract_update": "[需要翻译: \n                      Request Contract Update\n                    ]", "common.notify_sales_team": "[需要翻译: \n                      Notify Sales Team\n                    ]", "common.mark_reviewed": "[需要翻译: \n                      Mark Reviewed\n                    ]", "forms.labels.autocreate_quality_inspections": "[需要翻译: \n                    Auto-create quality inspections\n                  ]", "forms.labels.add_any_special_instructions_for_production": "[需要翻译: Add any special instructions for production...]", "common.setopenfalse_cancel": " setOpen(false)}>\n            取消\n          ", "common.work_orders_to_create": "工作订单s to 创建", "common.generation_options": "[需要翻译: Generation Options]", "tables.inspection_type": "Inspection 类型", "tables.total_inspections": "总计 Inspections", "tables.pass_rate": "[需要翻译: Pass Rate]", "tables.performance": "性能", "tables.inspections": "[需要翻译: Inspections]", "tables.quality_score": "[需要翻译: Quality Score]", "tables.inspection_date": "Inspection 日期", "tables.type": "类型", "tables.inspector": "[需要翻译: Inspector]", "tables.defects": "[需要翻译: Defects]", "tables.certificates": "证书s", "common.overall_pass_rate": "[需要翻译: Overall Pass Rate]", "common.defect_rate": "[需要翻译: Defect Rate]", "common.pending_inspections": "待处理 Inspections", "common.certificate_validity": "证书 Validity", "common.inspection_status_breakdown": "Inspection 状态 Breakdown", "common.defect_severity_analysis": "[需要翻译: Defect Severity Analysis]", "common.quality_inspections_by_type": "质量检验s by 类型", "common.product_quality_performance": "Product Quality 性能", "common.quality_trends_last_6_months": "Quality 趋势 (Last 6 Months)", "common.recent_quality_inspections": "Recent 质量检验s", "forms.labels.work_order": "工作订单 *", "forms.labels.inspector": "[需要翻译: Inspector *]", "forms.labels.inspection_type": "Inspection 类型", "forms.labels.scheduled_date": "Scheduled 日期", "forms.labels.notes": "备注", "forms.labels.select_work_order": "[需要翻译: Select work order]", "forms.labels.inspector_name": "[需要翻译: Inspector name]", "forms.labels.inspection_notes_or_requirements": "[需要翻译: Inspection notes or requirements]", "forms.labels.search_inspections": "搜索 inspections...", "common.setshowcreateformfalse_cancel": " setShow创建Form(false)}>\n                取消\n              ", "tables.contract_inspection": "[需要翻译: Contract / Inspection]", "tables.count_progress": "[需要翻译: Count / Progress]", "tables.customer_work_order": "客户 / 工作订单", "common.total_inspections": "总计 Inspections", "common.pending_review": "待处理 Review", "common.passed": "[需要翻译: Passed]", "common.failed": "[需要翻译: Failed]", "common.archived": "[需要翻译: Archived]", "common.create_quality_inspection": "创建 质量检验", "common.incoming_material": "[需要翻译: Incoming Material]", "common.inprocess": "[需要翻译: In-Process]", "common.final_inspection": "[需要翻译: Final Inspection]", "common.preshipment": "[需要翻译: Pre-Shipment]", "common.all_status": "All 状态", "common.inspection_results": "[需要翻译: Inspection Results]", "forms.labels.completed_date": "已完成 日期", "common.quality_inspection_created_successfully": "[需要翻译: Quality inspection created successfully]", "common.updated_successfully": "[需要翻译: Updated successfully]", "common.certificate_generated_successfully": "证书 generated successfully", "common.inspector_assigned_successfully": "[需要翻译: Inspector assigned successfully]", "common.quality_approved_successfully": "[需要翻译: Quality approved successfully]", "common.inspection_scheduled_successfully": "[需要翻译: Inspection scheduled successfully]", "common.quality_metrics_refreshed_successfully": "[需要翻译: Quality metrics refreshed successfully]", "common.inspection_updated_successfully": "[需要翻译: Inspection updated successfully]", "common.invalid_quality_status": "[需要翻译: Invalid quality status]", "common.no_valid_quality_certificate_found_for_this_product": "[需要翻译: No valid quality certificate found for this product]", "forms.labels.explain_why_this_inspection_needs_to_be_unarchived_eg_customer_requested_reinspection_archive_was_done_in_error_quality_issue_requires_investigation": "Explain why this inspection needs to be unarchived (e.g., '客户 requested re-inspection', 'Archive was done in error', 'Quality issue requires investigation')", "common.cancel": "\n              取消\n            ", "forms.labels.search": "搜索...", "tables.report": "[需要翻译: Report]", "tables.created": "创建d", "tables.records": "[需要翻译: Records]", "tables.certificate": "证书", "tables.issued": "[需要翻译: Issued]", "tables.expires": "[需要翻译: Expires]", "common.draft": "[需要翻译: Draft]", "common.approved": "已批准", "common.published": "[需要翻译: Published]", "common.active": "[需要翻译: Active]", "common.expired": "[需要翻译: Expired]", "common.pass_rate": "[需要翻译: Pass Rate]", "common.first_pass_yield": "[需要翻译: First Pass Yield]", "common.last_7_days": "[需要翻译: Last 7 days]", "common.last_30_days": "[需要翻译: Last 30 days]", "common.last_90_days": "[需要翻译: Last 90 days]", "common.last_year": "[需要翻译: Last year]", "forms.labels.archive_reason": "[需要翻译: Archive Reason *]", "forms.labels.additional_notes_optional": "Additional 备注 (Optional)", "forms.labels.select_a_reason_for_archiving": "[需要翻译: Select a reason for archiving]", "forms.labels.provide_additional_context_for_archiving_this_inspection": "[需要翻译: Provide additional context for archiving this inspection...]", "tables.avg_cost": "[需要翻译: Avg Cost]", "tables.total_value": "总计 Value", "tables.lots": "[需要翻译: Lots]", "tables.current_stock": "[需要翻译: Current Stock]", "tables.forecast_demand": "[需要翻译: Forecast Demand]", "tables.recommendation": "[需要翻译: Recommendation]", "tables.location": "位置", "tables.item_count": "[需要翻译: Item Count]", "tables.total_quantity": "总计 数量", "tables.utilization": "[需要翻译: Utilization]", "tables.movement_status": "Movement 状态", "common.total_inventory_value": "总计 库存 Value", "common.stock_health_score": "[需要翻译: Stock Health Score]", "common.low_stock_alerts": "[需要翻译: Low Stock Alerts]", "common.storage_utilization": "[需要翻译: Storage Utilization]", "common.inventory_composition": "库存 Composition", "common.cost_distribution_analysis": "[需要翻译: Cost Distribution Analysis]", "common.stock_levels_by_product": "库存水平s by Product", "common.reorder_recommendations": "[需要翻译: Reorder Recommendations]", "common.inventory_by_location": "库存 by 位置", "common.inventory_turnover_analysis": "库存 Turnover Analysis", "forms.labels.location": "位置", "forms.labels.reason": "[需要翻译: Reason]", "forms.labels.reference_optional": "[需要翻译: Reference (Optional)]", "forms.labels.notes_optional": "备注 (Optional)", "forms.labels.location_optional": "位置 (Optional)", "forms.labels.from_location": "From 位置", "forms.labels.to_location": "To 位置", "forms.labels.adjustment_quantity": "Adjustment 数量", "forms.labels.notes_required": "备注 (Required)", "forms.labels.reference": "[需要翻译: Reference]", "forms.labels.action": "[需要翻译: Action]", "forms.labels.adjustment_qty": "[需要翻译: Adjustment Qty (±)]", "forms.labels.transaction_type_filter": "Transaction 类型 筛选", "forms.labels.po_number_etc": "[需要翻译: PO Number, etc.]", "forms.labels.additional_notes": "[需要翻译: Additional notes]", "forms.labels.any_location": "[需要翻译: Any location]", "forms.labels.order_number_etc": "[需要翻译: Order number, etc.]", "forms.labels.select_source": "[需要翻译: Select source]", "forms.labels.select_destination": "[需要翻译: Select destination]", "forms.labels.transfer_reason": "[需要翻译: Transfer reason]", "forms.labels.0": "[需要翻译: ±0]", "forms.labels.explain_the_adjustment_reason": "[需要翻译: Explain the adjustment reason]", "forms.labels.select": "[需要翻译: Select]", "forms.labels.po_number": "[需要翻译: PO Number]", "forms.labels.so_number": "[需要翻译: SO Number]", "forms.labels.or_amount": "[需要翻译: + or - amount]", "forms.labels.explain_adjustment_reason": "[需要翻译: Explain adjustment reason]", "forms.labels.search_products_sku_lot_number_or_work_order": "搜索 products, SKU, lot number, or work order...", "forms.labels.quality": "[需要翻译: Quality]", "common.onopenchangefalse_cancel": " onOpenChange(false)}>\n              取消\n            ", "common.process_adjustment": "[需要翻译: Process Adjustment]", "tables.reference": "[需要翻译: Reference]", "tables.time": "[需要翻译: Time]", "tables.from_location": "From 位置", "tables.transfer_route": "[需要翻译: Transfer Route]", "tables.locations": "位置s", "tables.quality_status": "Quality 状态", "tables.lot_number": "[需要翻译: Lot Number]", "tables.product_details": "[需要翻译: Product Details]", "tables.workflow": "[需要翻译: Workflow]", "common.stock_analytics": "Stock 分析", "common.recent_activity": "[需要翻译: Recent Activity]", "common.any_location": "[需要翻译: Any location]", "common.all_quality": "[需要翻译: All Quality]", "common.quarantined": "[需要翻译: Quarantined]", "common.rejected": "已拒绝", "common.all_locations": "All 位置s", "common.all_transactions": "[需要翻译: All Transactions]", "common.inbound_only": "[需要翻译: Inbound Only]", "common.outbound_only": "[需要翻译: Outbound Only]", "common.transfer_only": "[需要翻译: Transfer Only]", "common.adjustment_only": "[需要翻译: Adjustment Only]", "common.inventory_metrics_refreshed_successfully": "库存 metrics refreshed successfully", "tables.shipment": "[需要翻译: Shipment]", "tables.total_qty": "总计 Qty", "tables.inventory_impact": "库存 Impact", "tables.risk_level": "[需要翻译: Risk Level]", "tables.recommended_action": "[需要翻译: Recommended Action]", "tables.expected_stock": "[需要翻译: Expected Stock]", "tables.discrepancy": "[需要翻译: Discrepancy]", "common.finished_goods_value": "成品 Value", "common.finished_goods_units": "成品 Units", "forms.labels.search_materials": "搜索 materials...", "forms.labels.filter_by_category": "筛选 by category", "tables.material_name": "Material 名称", "tables.category": "类别", "tables.primary_supplier": "Primary 供应商", "tables.standard_cost": "[需要翻译: Standard Cost]", "tables.stock_status": "Stock 状态", "common.raw_materials_inventory": "原材料 库存", "common.all_categories": "[需要翻译: All Categories]", "common.yarn": "[需要翻译: Yarn]", "common.fabric": "[需要翻译: Fabric]", "common.dyes": "[需要翻译: Dyes]", "common.chemicals": "[需要翻译: Chemicals]", "common.accessories": "[需要翻译: Accessories]", "common.other": "[需要翻译: Other]", "common.inactive": "[需要翻译: Inactive]", "common.discontinued": "[需要翻译: Discontinued]", "common.raw_material_information": "[需要翻译: Raw Material Information]", "tables.supplier": "供应商", "tables.unit_cost": "[需要翻译: Unit Cost]", "tables.received_date": "Received 日期", "tables.quantity_required": "数量 Required", "common.material_information": "[需要翻译: Material Information]", "common.inventory_settings": "库存 Settings", "common.inventory_lots": "库存 Lots", "common.bill_of_materials_usage": "物料清单 Usage", "common.lot_information": "[需要翻译: Lot Information]", "common.invalid_category": "[需要翻译: Invalid category]", "common.raw_material_status_changed_to_discontinued_you_can_now_delete_it_if_needed": "[需要翻译: Raw material status changed to discontinued. You can now delete it if needed.]", "common.raw_material_deleted_successfully": "[需要翻译: Raw material deleted successfully]", "forms.labels.sku": "[需要翻译: SKU *]", "forms.labels.material_name": "Material 名称", "forms.labels.category": "类别", "forms.labels.primary_supplier": "Primary 供应商", "forms.labels.composition": "[需要翻译: Composition]", "forms.labels.quality_grade": "[需要翻译: Quality Grade]", "forms.labels.specifications": "[需要翻译: Specifications]", "forms.labels.standard_cost": "[需要翻译: Standard Cost]", "forms.labels.reorder_point": "再订货点", "forms.labels.lead_time_days": "交货期 (Days)", "forms.labels.inspection_required": "[需要翻译: Inspection Required]", "forms.labels.quality_notes": "Quality 备注", "forms.labels.eg_yarncotton30s": "[需要翻译: e.g., YARN-COTTON-30S]", "forms.labels.eg_cotton_yarn_30s": "[需要翻译: e.g., Cotton Yarn 30s]", "forms.labels.select_category": "[需要翻译: Select category]", "forms.labels.eg_kg_meters_liters": "[需要翻译: e.g., kg, meters, liters]", "forms.labels.select_primary_supplier_optional": "[需要翻译: Select primary supplier (optional)]", "forms.labels.eg_100_cotton": "[需要翻译: e.g., 100% Cotton]", "forms.labels.eg_premium_standard": "[需要翻译: e.g., Premium, Standard]", "forms.labels.detailed_material_specifications": "[需要翻译: Detailed material specifications...]", "forms.labels.000": "[需要翻译: 0.00]", "forms.labels.quality_control_notes_and_requirements": "[需要翻译: Quality control notes and requirements...]", "common.no": "[需要翻译: No]", "common.yes": "[需要翻译: Yes]", "forms.labels.lot_number": "[需要翻译: Lot Number]", "forms.labels.supplier": "供应商", "forms.labels.unit_cost": "[需要翻译: Unit Cost *]", "forms.labels.currency": "[需要翻译: Currency]", "forms.labels.received_date": "Received 日期 *", "forms.labels.expiry_date": "Expiry 日期", "forms.labels.quality_status": "Quality 状态", "forms.labels.availability_status": "Availability 状态", "forms.labels.storage_location": "Storage 位置 *", "forms.labels.batch_notes": "Batch 备注", "forms.labels.inspection_notes": "Inspection 备注", "forms.labels.eg_lot2024001": "[需要翻译: e.g., LOT-2024-001]", "forms.labels.select_supplier_optional": "[需要翻译: Select supplier (optional)]", "forms.labels.select_storage_location": "[需要翻译: Select storage location]", "forms.labels.notes_about_this_batchlot": "备注 about this batch/lot...", "forms.labels.quality_inspection_notes": "[需要翻译: Quality inspection notes...]", "common.available": "[需要翻译: Available]", "common.reserved": "[需要翻译: Reserved]", "common.consumed": "[需要翻译: Consumed]", "common.raw_materials_building_a": "原材料 - Building A", "common.raw_materials_outdoor_yard": "原材料 - Outdoor Yard", "common.main_finished_goods_warehouse": "Main 成品 Warehouse", "common.shanghai_distribution_center": "[需要翻译: Shanghai Distribution Center]", "common.beijing_distribution_center": "[需要翻译: Beijing Distribution Center]", "common.export_staging_area": "导出 Staging Area", "common.quarantine_area": "[需要翻译: Quarantine Area]", "forms.labels.quality_tolerance": "[需要翻译: Quality Tolerance]", "forms.labels.eg_5": "[需要翻译: e.g., ±5%]", "tables.date": "日期", "tables.lot": "[需要翻译: Lot]", "tables.total_cost": "总计 Cost", "common.error_loading_consumption_history": "[需要翻译: Error Loading Consumption History]", "common.consumption_history": "[需要翻译: Consumption History]", "forms.labels.location_name_id": "位置 名称 (ID)", "forms.labels.location_code": "位置 Code", "forms.labels.description": "描述", "forms.labels.type": "类型", "forms.labels.capacity": "[需要翻译: Capacity]", "forms.labels.zone": "[需要翻译: Zone]", "forms.labels.location_name": "位置 名称", "forms.labels.security_level": "[需要翻译: Security Level]", "forms.labels.temperature_controlled": "[需要翻译: Temperature Controlled]", "forms.labels.allows_mixed_products": "[需要翻译: Allows Mixed Products]", "forms.labels.eg_warehousenorth": "[需要翻译: e.g., warehouse_north]", "forms.labels.eg_wh001": "[需要翻译: e.g., WH-001]", "forms.labels.brief_description_of_the_location_purpose": "[需要翻译: Brief description of the location purpose]", "forms.labels.eg_a1_b2": "[需要翻译: e.g., A1, B2]", "forms.labels.enter_location_name": "[需要翻译: Enter location name]", "forms.labels.select_type": "[需要翻译: Select type]", "forms.labels.enter_location_description": "[需要翻译: Enter location description]", "forms.labels.eg_a1": "[需要翻译: e.g., A1]", "forms.labels.select_security_level": "[需要翻译: Select security level]", "common.setiscreatingfalse_cancel": " setIsCreating(false)}>\n                  取消\n                ", "common.seteditinglocationnull_cancel": " set编辑ing位置(null)}>\n                    取消\n                  ", "common.update_location": "\n                    Update 位置\n                  ", "tables.capacity": "[需要翻译: Capacity]", "tables.details": "[需要翻译: Details]", "common.location_directory": "位置 Directory", "common.finished_goods": "成品", "common.raw_materials": "原材料", "common.work_in_progress": "在制品", "common.quality_control": "质量控制", "common.shipping": "运输", "common.receiving": "[需要翻译: Receiving]", "common.returns": "[需要翻译: Returns]", "common.quarantine": "[需要翻译: Quarantine]", "common.medium": "[需要翻译: Medium]", "common.location_deleted_successfully": "位置 deleted successfully", "forms.labels.select_pickup_location": "[需要翻译: Select pickup location]", "common.invalid_shipping_method": "[需要翻译: Invalid shipping method]", "common.shipment_cancelled_successfully": "[需要翻译: Shipment cancelled successfully]", "common.invalid_status": "[需要翻译: Invalid status]", "common.attention_required": "[需要翻译: Attention Required]", "common.shipping_overview": "运输 概览", "forms.labels.search_shipments_by_number_customer_or_tracking": "搜索 shipments by number, customer, or tracking...", "common.all_statuses": "All 状态es", "common.total_shipments": "总计 Shipments", "common.avg_processing_time": "[需要翻译: Avg Processing Time]", "common.cost_per_shipment": "[需要翻译: Cost per Shipment]", "common.location_efficiency": "位置 Efficiency", "common.inventory_accuracy": "库存 Accuracy", "common.location_performance_metrics": "位置 性能 指标", "common.location_distribution": "位置 Distribution", "common.top_products": "[需要翻译: Top Products]", "common.capacity_utilization_analysis": "[需要翻译: Capacity Utilization Analysis]", "tables.method": "[需要翻译: Method]", "tables.items_qty": "[需要翻译: Items & Qty]", "tables.financial_impact": "[需要翻译: Financial Impact]", "tables.ship_date": "Ship 日期", "tables.tracking": "[需要翻译: Tracking]", "forms.labels.unit_price": "Unit 价格", "tables.unit_price": "Unit 价格", "tables.total": "总计", "common.integration_by_shipment_status": "Integration by Shipment 状态", "common.recent_shipments_integration_status": "Recent Shipments Integration 状态", "forms.labels.shipping_method": "运输 Method", "forms.labels.carrier": "[需要翻译: Carrier]", "forms.labels.tracking_number": "[需要翻译: Tracking Number]", "forms.labels.ship_date": "Ship 日期", "forms.labels.estimated_delivery": "Estimated 交付", "forms.labels.shipping_cost": "运输 Cost", "forms.labels.insurance_cost": "[需要翻译: Insurance Cost]", "forms.labels.special_instructions": "[需要翻译: Special Instructions]", "forms.labels.select_a_customer": "[需要翻译: Select a customer]", "forms.labels.eg_dhl_fedex_maersk": "[需要翻译: e.g., DHL, FedEx, Maersk]", "forms.labels.enter_tracking_number": "[需要翻译: Enter tracking number]", "forms.labels.any_additional_notes_about_this_shipment": "[需要翻译: Any additional notes about this shipment...]", "forms.labels.special_handling_instructions_delivery_requirements_etc": "[需要翻译: Special handling instructions, delivery requirements, etc.]", "common.additional_information": "[需要翻译: Additional Information]", "forms.labels.carrier_optional": "[需要翻译: Carrier (Optional)]", "forms.labels.ship_date_optional": "Ship 日期 (Optional)", "forms.labels.estimated_delivery_optional": "Estimated 交付 (Optional)", "forms.labels.shipping_cost_optional": "运输 Cost (Optional)", "forms.labels.insurance_cost_optional": "[需要翻译: Insurance Cost (Optional)]", "forms.labels.special_instructions_optional": "[需要翻译: Special Instructions (Optional)]", "forms.labels.search_contracts": "搜索 contracts...", "forms.labels.search_declarations": "搜索 declarations...", "tables.items": "[需要翻译: Items]", "common.export_declarations": "出口申报s", "tables.hs_code": "[需要翻译: HS Code]", "common.declaration_items": "[需要翻译: Declaration Items]", "common.documents": "[需要翻译: Documents]", "messages.file_uploaded_successfully": "[需要翻译: File uploaded successfully.]", "messages.failed_to_upload_file": "[需要翻译: Failed to upload file.]", "messages.document_deleted_successfully": "[需要翻译: Document deleted successfully.]", "messages.failed_to_delete_document": "[需要翻译: Failed to delete document.]", "forms.labels.declaration_items": "[需要翻译: Declaration Items]", "forms.labels.000000": "[需要翻译: 0000.00]", "common.total_declarations": "总计 Declarations", "common.submitted": "提交ted", "common.cleared": "[需要翻译: Cleared]", "forms.labels.declaration_number": "[需要翻译: Declaration Number *]", "forms.labels.enter_declaration_number": "[需要翻译: Enter declaration number]", "forms.labels.select_status": "[需要翻译: Select status]", "forms.labels.exp2025001": "[需要翻译: EXP-2025-001]", "common.routerpushexport_cancel": " router.push('/export')}\n        >\n          取消\n        ", "forms.labels.sales_contract_optional": "[需要翻译: Sales Contract (Optional)]", "forms.labels.search_contracts_or_select_manual_entry": "搜索 contracts or select manual entry...", "common.retry": "[需要翻译: \n            Retry\n          ]", "tables.aging": "[需要翻译: Aging]", "forms.labels.search_invoices": "搜索 invoices...", "tables.invoice": "发票 #", "tables.amount": "[需要翻译: Amount]", "tables.contract": "[需要翻译: Contract]", "common.invoice_list": "发票 List", "common.sent": "[需要翻译: Sent]", "common.paid": "[需要翻译: Paid]", "forms.labels.invoice_number": "发票 Number", "forms.labels.amount": "[需要翻译: Amount *]", "forms.labels.invoice_date": "发票 日期", "forms.labels.payment_terms": "付款 Terms", "forms.labels.ar2024001": "[需要翻译: AR-2024-001]", "forms.labels.search_customers": "搜索 customers...", "forms.labels.select_payment_terms": "[需要翻译: Select payment terms]", "forms.labels.additional_notes_or_comments": "[需要翻译: Additional notes or comments...]", "common.invoice_details": "发票 Details", "common.no_terms": "[需要翻译: No Terms]", "common.tt_telegraphic_transfer": "[需要翻译: TT (Telegraphic Transfer)]", "common.dp_documents_against_payment": "DP (Documents against 付款)", "common.lc_letter_of_credit": "LC (Letter of 贷方)", "common.deposit": "[需要翻译: Deposit]", "common.partial": "[需要翻译: Partial]", "common.cancelled": "已取消", "common.invoice_amount": "发票 Amount", "common.amount_received": "[需要翻译: Amount Received]", "common.balance_due": "余额 Due", "common.aging": "[需要翻译: Aging]", "forms.labels.invoice_amount": "发票 Amount", "forms.labels.amount_received": "[需要翻译: Amount Received]", "forms.labels.inv2024001": "[需要翻译: INV-2024-001]", "forms.labels.select_currency": "[需要翻译: Select currency]", "forms.labels.select_customer": "[需要翻译: Select customer...]", "forms.labels.select_sales_contract_optional": "[需要翻译: Select sales contract (optional)]", "forms.labels.enter_any_additional_notes_or_comments": "[需要翻译: Enter any additional notes or comments...]", "common.invoice_information": "发票 Information", "common.financial_information": "[需要翻译: Financial Information]", "common.customer_and_contract": "客户 and Contract", "common.deposit_received": "[需要翻译: De<PERSON><PERSON><PERSON> Received]", "common.partial_paid": "[需要翻译: Partial Paid]", "common.usd_us_dollar": "[需要翻译: USD - US Dollar]", "common.eur_euro": "[需要翻译: EUR - Euro]", "common.cny_chinese_yuan": "[需要翻译: CNY - Chinese Yuan]", "common.gbp_british_pound": "[需要翻译: GBP - British Pound]", "common.jpy_japanese_yen": "[需要翻译: JPY - Japanese Yen]", "common.no_contract": "[需要翻译: No Contract]", "common.amount_must_be_a_valid_positive_number": "[需要翻译: Amount must be a valid positive number]", "common.received_amount_must_be_a_valid_nonnegative_number": "[需要翻译: Received amount must be a valid non-negative number]", "common.received": "[需要翻译: Received]", "forms.labels.purchase_contract_optional": "[需要翻译: Purchase Contract (Optional)]", "forms.labels.ap2024001": "[需要翻译: AP-2024-001]", "forms.labels.search_suppliers": "搜索 suppliers...", "common.amount_paid": "[需要翻译: Amount Paid]", "forms.labels.amount_paid": "[需要翻译: Amount Paid]", "forms.labels.bill2024001": "[需要翻译: BILL-2024-001]", "forms.labels.select_supplier": "[需要翻译: Select supplier]", "forms.labels.select_purchase_contract_optional": "[需要翻译: Select purchase contract (optional)]", "common.supplier_and_contract": "供应商 and Contract", "common.deposit_paid": "[需要翻译: De<PERSON><PERSON><PERSON>]", "common.paid_amount_must_be_a_valid_nonnegative_number": "[需要翻译: Paid amount must be a valid non-negative number]", "common.manufacturing_financial_kpis_retrieved_successfully": "[需要翻译: Manufacturing financial KPIs retrieved successfully]", "common.legacy_financial_summary_retrieved_successfully": "[需要翻译: Legacy financial summary retrieved successfully]", "common.aging_report_retrieved_successfully": "[需要翻译: Aging report retrieved successfully]", "common.ar_invoice_deleted_successfully": "[需要翻译: AR invoice deleted successfully]", "common.ap_invoice_deleted_successfully": "[需要翻译: AP invoice deleted successfully]", "forms.labels.search_and_select_sales_contract": "搜索 and select sales contract...", "forms.labels.search_and_select_customer": "搜索 and select customer...", "forms.labels.100000": "[需要翻译: 1000.00]", "common.deposit_advance_payment": "Deposit (Advance 付款)", "common.30_deposit_70_tt": "[需要翻译: 30% Deposit + 70% TT]", "common.50_deposit_50_lc": "[需要翻译: 50% Deposit + 50% LC]", "forms.labels.search_and_select_purchase_contract": "搜索 and select purchase contract...", "forms.labels.search_and_select_supplier": "搜索 and select supplier...", "forms.labels.80000": "[需要翻译: 800.00]", "common.inventory_value": "库存 Value", "common.net_profit": "Net 利润", "common.outstanding_ar": "[需要翻译: Outstanding AR]", "common.profit_loss_summary": "利润 & 亏损 摘要", "common.recent_ar_invoices": "Recent AR 发票s", "common.accounts_receivable_aging": "应收账款 Aging", "common.accounts_payable_aging": "应付账款 Aging", "common.cash_flow_analysis": "[需要翻译: Cash Flow Analysis]", "tables.contracts": "[需要翻译: Contracts]", "tables.total_revenue": "总计 收入", "tables.last_activity": "[需要翻译: Last Activity]", "common.active_contracts": "[需要翻译: Active Contracts]", "common.customer_base": "客户 Base", "common.operational_efficiency": "[需要翻译: Operational Efficiency]", "common.financial_performance": "Financial 性能", "common.quality_operations": "[需要翻译: Quality & Operations]", "common.top_customers_by_revenue": "Top 客户s by 收入", "common.contract_performance_metrics": "Contract 性能 指标", "common.revenue_trends_last_6_months": "收入 趋势 (Last 6 Months)", "common.operational_performance": "Operational 性能", "common.coming_soon": "[需要翻译: \n                          Coming Soon\n                        ]", "common.operating_cash_flow": "[需要翻译: Operating Cash Flow]", "common.investing_cash_flow": "[需要翻译: Investing Cash Flow]", "common.financing_cash_flow": "[需要翻译: Financing Cash Flow]", "forms.labels.eg_global_tech_supplies": "[需要翻译: e.g. Global Tech Supplies]", "forms.labels.eg_jane_smith": "[需要翻译: e.g. <PERSON>]", "forms.labels.eg_janesmithglobaltechcom": "[需要翻译: e.g. <EMAIL>]", "forms.labels.eg_1_5559876543": "[需要翻译: e.g. ******-987-6543]", "forms.labels.eg_456_industrial_ave_tech_city": "[需要翻译: e.g. 456 Industrial Ave, Tech City]", "forms.labels.eg_bank_of_innovation_*********": "[需要翻译: e.g. Bank of Innovation, *********]", "forms.labels.eg_vat_id_ein": "[需要翻译: e.g. VAT ID, EIN]", "common.name_must_be_at_least_2_characters": "名称 must be at least 2 characters.", "common.invalid_email_address": "[需要翻译: Invalid email address.]", "forms.labels.search_samples_by_name_code_direction_status": "搜索 samples by name, code, direction, status...", "forms.labels.sample_direction": "[需要翻译: Sample Direction]", "forms.labels.sample_code": "[需要翻译: Sample Code *]", "forms.labels.sample_name": "Sample 名称 *", "forms.labels.sample_type": "Sample 类型", "forms.labels.technical_specifications": "[需要翻译: Technical specifications...]", "forms.labels.quality_requirements": "[需要翻译: Quality Requirements]", "forms.labels.customer_receiving_sample": "客户 (Receiving Sample) *", "forms.labels.sample_purpose": "[需要翻译: Sample Purpose]", "forms.labels.delivery_instructions": "交付 Instructions", "forms.labels.sample_sender_type": "Sample Sender 类型 *", "forms.labels.customer_sender": "客户 (Sender) *", "forms.labels.supplier_sender": "供应商 (Sender) *", "forms.labels.sample_condition_requirements": "[需要翻译: Sample Condition & Requirements]", "forms.labels.internal_purpose": "[需要翻译: Internal Purpose]", "forms.labels.internal_requirements": "[需要翻译: Internal Requirements]", "forms.labels.delivery_date": "交付 日期", "forms.labels.sample_cost": "[需要翻译: Sample Cost]", "forms.labels.testing_status": "Testing 状态", "forms.labels.testing_results": "[需要翻译: Testing Results]", "forms.labels.quote_requested": "[需要翻译: Quote Requested]", "forms.labels.quote_provided": "[需要翻译: Quote Provided]", "forms.labels.target_completion_date": "Target Completion 日期", "forms.labels.eg_100": "[需要翻译: e.g., 100]", "forms.labels.eg_meters_pieces": "[需要翻译: e.g., meters, pieces]", "forms.labels.quality_standards": "[需要翻译: Quality standards...]", "forms.labels.search_customers_to_send_sample_to": "搜索 customers to send sample to...", "forms.labels.why_are_we_sending_this_sample": "[需要翻译: Why are we sending this sample?]", "forms.labels.special_delivery_instructions_customer_requirements": "[需要翻译: Special delivery instructions, customer requirements...]", "forms.labels.who_sent_us_this_sample": "[需要翻译: Who sent us this sample?]", "forms.labels.search_customer_who_sent_sample": "搜索 customer who sent sample...", "forms.labels.search_supplier_who_sent_sample": "搜索 supplier who sent sample...", "forms.labels.why_did_they_send_this_sample": "[需要翻译: Why did they send this sample?]", "forms.labels.sample_condition_when_received_analysis_requirements_customer_specifications": "[需要翻译: Sample condition when received, analysis requirements, customer specifications...]", "forms.labels.internal_testing_purpose": "[需要翻译: Internal testing purpose]", "forms.labels.internal_testing_requirements_project_details_department_specifications": "[需要翻译: Internal testing requirements, project details, department specifications...]", "forms.labels.eg_15000": "[需要翻译: e.g. 150.00]", "forms.labels.quality_analysis_material_properties_test_results": "[需要翻译: Quality analysis, material properties, test results...]", "common.product_information": "[需要翻译: Product Information]", "common.commercial_details": "[需要翻译: Commercial Details]", "common.outbound_we_send_to_customer": "[需要翻译: 📤 Outbound - We send to customer]", "common.inbound_customersupplier_sends_to_us": "📥 Inbound - 客户/supplier sends to us", "common.internal_internal_testingrd": "[需要翻译: 🏭 Internal - Internal testing/R&D]", "common.development": "[需要翻译: Development]", "common.production": "[需要翻译: Production]", "common.quality": "[需要翻译: Quality]", "common.prototype": "[需要翻译: Prototype]", "common.customer_evaluation": "客户 Evaluation", "common.marketing_demo": "[需要翻译: Marketing Demo]", "common.trade_show": "[需要翻译: Trade Show]", "common.sales_presentation": "[需要翻译: Sales Presentation]", "common.customer": "客户", "common.supplier": "供应商", "common.manufacturing_quote_request": "[需要翻译: Manufacturing Quote Request]", "common.material_testing": "[需要翻译: Material Testing]", "common.reverse_engineering": "[需要翻译: Reverse Engineering]", "common.quality_comparison": "[需要翻译: Quality Comparison]", "common.rd_testing": "[需要翻译: R&D Testing]", "common.process_improvement": "[需要翻译: Process Improvement]", "common.product_development": "[需要翻译: Product Development]", "common.not_started": "[需要翻译: Not Started]", "common.windowhistoryback_go_back": "[需要翻译:  window.history.back()}>\n                Go Back\n              ]", "forms.labels.eg_pcs_kg_m": "[需要翻译: e.g., pcs, kg, m]", "forms.labels.eg_1050": "[需要翻译: e.g., 10.50]", "forms.labels.eg_sc2025001": "[需要翻译: e.g., SC-2025-001]", "forms.labels.eg_usd": "[需要翻译: e.g., USD]", "forms.labels.select_template": "[需要翻译: Select template...]", "forms.labels.select_product": "[需要翻译: Select product...]", "common.under_review": "[需要翻译: Under Review]", "messages.sales_contract_updated_successfully": "[需要翻译: Sales contract updated successfully!]", "messages.failed_to_update_sales_contract": "[需要翻译: Failed to update sales contract]", "messages.sales_contract_created_successfully": "[需要翻译: Sales contract created successfully!]", "messages.an_unexpected_error_occurred": "[需要翻译: An unexpected error occurred]", "forms.labels.eg_pc2025001": "[需要翻译: e.g., PC-2025-001]", "messages.purchase_contract_updated_successfully": "[需要翻译: Purchase contract updated successfully!]", "messages.failed_to_update_purchase_contract": "[需要翻译: Failed to update purchase contract]", "forms.labels.eg_highgrade_widget": "[需要翻译: e.g. High-Grade Widget]", "forms.labels.eg_widhg001": "[需要翻译: e.g. WID-HG-001]", "forms.labels.eg_pcs_kg_meters": "[需要翻译: e.g. pcs, kg, meters]", "forms.labels.eg_847990": "[需要翻译: e.g. 847990]", "forms.labels.eg_china": "[需要翻译: e.g. China]", "forms.labels.eg_carton_bag_pallet": "[需要翻译: e.g. <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>]", "forms.labels.00": "[需要翻译: 0.0]", "forms.labels.eg_5_01mm_tolerance": "[需要翻译: e.g. ±5%, 0.1mm tolerance]", "common.sku_must_be_at_least_2_characters": "[需要翻译: SKU must be at least 2 characters.]", "common.unit_is_required": "[需要翻译: Unit is required.]", "common.procurement_status": "Procurement 状态", "common.action_required": "[需要翻译: Action Required]", "common.supplier_network": "供应商 Network", "common.avg_lead_time": "Avg 交货期", "common.ready_to_order": "就绪 to Order", "common.status": "状态", "common.priority": "[需要翻译: Priority]", "common.planned_quantity": "Planned 数量", "common.estimated_cost": "[需要翻译: Estimated Cost]", "common.supplier_information": "供应商 Information", "common.notes": "备注", "common.audit_information": "[需要翻译: Audit Information]", "forms.labels.search_forecasts": "搜索 forecasts...", "forms.labels.all_statuses": "[需要翻译: All statuses]", "forms.labels.all_methods": "[需要翻译: All methods]", "forms.labels.all_levels": "[需要翻译: All levels]", "tables.period": "[需要翻译: Period]", "tables.demand": "[需要翻译: Demand]", "tables.profit_margin": "利润 Margin", "tables.confidence": "[需要翻译: Confidence]", "tables.supplier_prefs": "供应商 Prefs", "common.all_methods": "[需要翻译: All Methods]", "common.pipeline_analysis": "[需要翻译: Pipeline Analysis]", "common.manual_entry": "[需要翻译: Manual Entry]", "common.historical_data": "[需要翻译: Historical Data]", "common.hybrid_method": "[需要翻译: Hybrid Method]", "common.all_levels": "[需要翻译: All Levels]", "common.generated_procurement_plans": "[需要翻译: Generated Procurement Plans]", "common.forecast_details": "[需要翻译: Forecast Details]", "forms.labels.company_name": "Company 名称 *", "forms.labels.legal_company_name": "Legal Company 名称", "forms.labels.email_address": "邮箱 地址 *", "forms.labels.phone_number": "电话 Number", "forms.labels.website": "[需要翻译: Website]", "forms.labels.address_line_1": "地址 Line 1 *", "forms.labels.address_line_2": "地址 Line 2", "forms.labels.city": "[需要翻译: City]", "forms.labels.stateprovince": "[需要翻译: State/Province]", "forms.labels.postal_code": "[需要翻译: Postal Code]", "forms.labels.country": "[需要翻译: Country *]", "forms.labels.industry": "[需要翻译: Industry *]", "forms.labels.business_type": "Business 类型 *", "forms.labels.number_of_employees": "[需要翻译: Number of Employees *]", "forms.labels.annual_revenue": "Annual 收入", "forms.labels.business_registration_number": "[需要翻译: Business registration number]", "forms.labels.tax_id_ein": "[需要翻译: Tax ID / EIN]", "forms.labels.vat_number": "[需要翻译: VAT Number]", "forms.labels.bank_name": "[需要翻译: Bank name]", "forms.labels.bank_account_number": "[需要翻译: Bank Account Number]", "forms.labels.swiftbic_code": "[需要翻译: SWIFT/BIC code]", "forms.labels.bank_address": "[需要翻译: Bank address]", "forms.labels.export_license_number": "导出 license number", "forms.labels.customs_code": "海关 Code", "forms.labels.preferred_incoterms": "[需要翻译: Preferred Incoterms *]", "forms.labels.preferred_payment_terms": "Preferred 付款 Terms *", "forms.labels.enter_company_name": "[需要翻译: Enter company name]", "forms.labels.full_legal_name": "[需要翻译: Full legal name]", "forms.labels.companyexamplecom": "[需要翻译: <EMAIL>]", "forms.labels.1_555_1234567": "[需要翻译: +1 (555) 123-4567]", "forms.labels.street_address": "[需要翻译: Street address]", "forms.labels.apartment_suite_etc": "[需要翻译: Apartment, suite, etc.]", "forms.labels.state_or_province": "[需要翻译: State or Province]", "forms.labels.select_country": "[需要翻译: Select country]", "forms.labels.select_industry": "[需要翻译: Select industry]", "forms.labels.select_business_type": "[需要翻译: Select business type]", "forms.labels.select_employee_count": "[需要翻译: Select employee count]", "forms.labels.select_revenue_range": "[需要翻译: Select revenue range]", "forms.labels.registration_number": "[需要翻译: Registration number]", "forms.labels.tax_identification_number": "[需要翻译: Tax identification number]", "forms.labels.vat_registration_number": "[需要翻译: VAT registration number]", "forms.labels.name_of_your_bank": "名称 of your bank", "forms.labels.account_number": "[需要翻译: Account number]", "forms.labels.swift_code": "[需要翻译: SWIFT code]", "forms.labels.banks_full_address": "[需要翻译: Bank's full address]", "forms.labels.customs_registration_code": "海关 registration code", "forms.labels.select_incoterms": "[需要翻译: Select Incoterms]", "common.united_states": "[需要翻译: United States]", "common.canada": "[需要翻译: Canada]", "common.china": "[需要翻译: China]", "common.india": "[需要翻译: India]", "common.bangladesh": "[需要翻译: Bangladesh]", "common.vietnam": "[需要翻译: Vietnam]", "common.turkey": "[需要翻译: Turkey]", "common.italy": "[需要翻译: Italy]", "common.germany": "[需要翻译: Germany]", "common.united_kingdom": "[需要翻译: United Kingdom]", "common.textile_manufacturing": "[需要翻译: Textile Manufacturing]", "common.apparel_fashion": "[需要翻译: Apparel & Fashion]", "common.home_textiles": "[需要翻译: Home Textiles]", "common.technical_textiles": "[需要翻译: Technical Textiles]", "common.leather_goods": "[需要翻译: Leather Goods]", "common.footwear": "[需要翻译: Footwear]", "common.other_manufacturing": "[需要翻译: Other Manufacturing]", "common.corporation": "[需要翻译: Corporation]", "common.limited_liability_company_llc": "[需要翻译: Limited Liability Company (LLC)]", "common.partnership": "[需要翻译: Partnership]", "common.sole_proprietorship": "[需要翻译: Sole Proprietorship]", "common.cooperative": "[需要翻译: Cooperative]", "common.110_employees": "[需要翻译: 1-10 employees]", "common.1150_employees": "[需要翻译: 11-50 employees]", "common.51200_employees": "[需要翻译: 51-200 employees]", "common.201500_employees": "[需要翻译: 201-500 employees]", "common.5011000_employees": "[需要翻译: 501-1000 employees]", "common.1000_employees": "[需要翻译: 1000+ employees]", "common.fob_free_on_board": "[需要翻译: FOB - Free on Board]", "common.cif_cost_insurance_freight": "[需要翻译: CIF (Cost, Insurance & Freight)]", "common.exw_ex_works": "[需要翻译: EXW - Ex Works]", "common.fca_free_carrier": "[需要翻译: FCA - Free Carrier]", "common.cpt_carriage_paid_to": "[需要翻译: CPT - Carriage Paid To]", "common.cip_carriage_insurance_paid_to": "[需要翻译: CIP (Carriage & Insurance Paid To)]", "common.dap_delivered_at_place": "DAP - 已交付 at Place", "common.dpu_delivered_at_place_unloaded": "DPU - 已交付 at Place Unloaded", "common.ddp_delivered_duty_paid": "DDP - 已交付 Duty Paid", "common.net_30_days": "[需要翻译: Net 30 days]", "common.net_60_days": "[需要翻译: Net 60 days]", "common.net_90_days": "[需要翻译: Net 90 days]", "common.lc_at_sight": "[需要翻译: L/C at sight]", "common.lc_30_days": "[需要翻译: L/C 30 days]", "common.lc_60_days": "[需要翻译: L/C 60 days]", "common.tt_in_advance": "[需要翻译: T/T in advance]", "common.tt_30_advance_70_before_shipment": "[需要翻译: T/T 30% advance, 70% before shipment]", "common.tt_50_advance_50_before_shipment": "[需要翻译: T/T 50% advance, 50% before shipment]", "forms.labels.search_documents": "搜索 documents...", "forms.labels.filter_by_type": "筛选 by type", "tables.document_name": "Document 名称", "tables.export_declaration": "出口申报", "tables.created_date": "创建d 日期", "tables.size": "[需要翻译: Size]", "tables.created_by": "创建d By", "tables.certificate_name": "证书 名称", "tables.issuer": "[需要翻译: Issuer]", "tables.issue_date": "Issue 日期", "tables.expiry_date": "Expiry 日期", "tables.contract_name": "Contract 名称", "tables.party": "[需要翻译: Party]", "tables.contract_date": "Contract 日期", "tables.value": "[需要翻译: Value]", "tables.regulation": "[需要翻译: Regulation]", "tables.next_review": "[需要翻译: Next Review]", "common.all_types": "All 类型s", "common.commercial_invoice": "Commercial 发票", "common.packing_list": "[需要翻译: Packing List]", "common.bill_of_lading": "[需要翻译: Bill of Lading]", "common.certificate_of_origin": "证书 of Origin", "common.export_license": "导出 License", "forms.labels.search_collections": "搜索 collections...", "forms.labels.season": "[需要翻译: Season]", "tables.design_id": "[需要翻译: Design ID]", "tables.name": "名称", "tables.collection": "[需要翻译: Collection]", "tables.fabric": "[需要翻译: Fabric]", "tables.colors": "[需要翻译: Colors]", "tables.costprice": "Cost/价格", "common.active_collections": "[需要翻译: Active Collections]", "common.total_designs": "总计 Designs", "common.production_ready": "Production 就绪", "common.color_palettes": "[需要翻译: Color Palettes]", "common.design_collections": "[需要翻译: Design Collections]", "common.individual_designs": "[需要翻译: Individual Designs]", "common.popular_categories": "[需要翻译: Popular Categories]", "common.seasonal_performance": "Seasonal 性能", "common.all_seasons": "[需要翻译: All Seasons]", "common.spring_2024": "[需要翻译: Spring 2024]", "common.summer_2024": "[需要翻译: Summer 2024]", "common.fall_2024": "[需要翻译: Fall 2024]", "common.winter_2024": "[需要翻译: Winter 2024]", "common.casual_wear": "[需要翻译: Casual Wear]", "common.formal_wear": "[需要翻译: Formal Wear]", "common.resort_wear": "[需要翻译: Resort Wear]", "common.activewear": "[需要翻译: Activewear]", "forms.labels.contact_person": "联系人 Person", "forms.labels.phone": "电话", "forms.labels.email": "邮箱", "forms.labels.address": "地址", "forms.labels.incoterm": "[需要翻译: Incoterm]", "forms.labels.abc_electronics_inc": "[需要翻译: ABC Electronics Inc.]", "forms.labels.sarah_johnson": "[需要翻译: <PERSON>]", "forms.labels.********": "[需要翻译: ******-0123]", "forms.labels.sarahabcelectronicscom": "[需要翻译: <EMAIL>]", "forms.labels.1234_industrial_blvd_los_angeles_ca_90210_usa": "[需要翻译: 1234 Industrial Blvd, Los Angeles, CA 90210, USA]", "forms.labels.ustax*********": "[需要翻译: US-TAX-*********]", "forms.labels.chase_bank_account_*********0": "[需要翻译: Chase Bank - Account: *********0]", "forms.labels.acme_corporation": "[需要翻译: Acme Corporation]", "forms.labels.john_smith": "[需要翻译: <PERSON>]", "forms.labels.johnacmecom": "[需要翻译: <EMAIL>]", "forms.labels.123_business_st_city_state_12345": "[需要翻译: 123 Business St, City, State 12345]", "common.setshoweditdialogfalse_cancel": " setShow编辑Dialog(false)}>\n                    取消\n                  ", "common.update_customer": "\n                    Update 客户\n                  ", "common.30_days": "[需要翻译: 30 days]", "common.60_days": "[需要翻译: 60 days]", "common.90_days": "[需要翻译: 90 days]", "common.cash": "[需要翻译: Cash]", "common.tt": "[需要翻译: T/T]", "forms.labels.customer_name": "客户 名称 *", "forms.labels.contact_name": "联系人 名称", "forms.labels.contact_email": "联系人 邮箱", "forms.labels.contact_phone": "联系人 电话", "forms.labels.payment_term": "付款 Term", "forms.labels.eg_acme_inc": "[需要翻译: e.g. Acme Inc.]", "forms.labels.eg_john_doe": "[需要翻译: e.g. <PERSON>]", "forms.labels.eg_johndoeacmecom": "[需要翻译: e.g. <EMAIL>]", "forms.labels.eg_1_5551234567": "[需要翻译: e.g. ******-123-4567]", "forms.labels.eg_123_main_st_anytown_usa": "[需要翻译: e.g. 123 Main St, Anytown USA]", "forms.labels.eg_fob": "[需要翻译: e.g. FOB]", "forms.labels.eg_net_30": "[需要翻译: e.g. Net 30]", "messages.customer_updated_successfully": "客户 updated successfully.", "messages.failed_to_update_customer": "[需要翻译: Failed to update customer.]", "forms.labels.tax_id": "[需要翻译: Tax ID]", "forms.labels.bank_information": "[需要翻译: Bank Information]", "forms.labels.eg_ustax*********": "[需要翻译: e.g. US-TAX-*********]", "forms.labels.eg_chase_bank_account_*********0": "[需要翻译: e.g. Chase Bank - Account: *********0]", "forms.labels.select_incoterm": "[需要翻译: Select incoterm]", "messages.customer_created_successfully": "客户 created successfully.", "messages.failed_to_create_customer": "[需要翻译: Failed to create customer.]", "common.sales_contracts": "[需要翻译: Sales Contracts]", "forms.labels.standard_sales_contract": "[需要翻译: Standard Sales Contract]", "forms.labels.standard_purchase_contract": "[需要翻译: Standard Purchase Contract]", "forms.labels.select_annual_revenue": "[需要翻译: Select annual revenue]", "common.france": "[需要翻译: France]", "common.japan": "[需要翻译: Japan]", "common.brazil": "[需要翻译: Brazil]", "common.australia": "[需要翻译: Australia]", "common.textile_apparel": "[需要翻译: Textile & Apparel]", "common.electronics": "[需要翻译: Electronics]", "common.automotive": "[需要翻译: Automotive]", "common.food_beverage": "[需要翻译: Food & Beverage]", "common.machinery": "[需要翻译: Machinery]", "common.furniture": "[需要翻译: Furniture]", "common.500_employees": "[需要翻译: 500+ employees]", "common.cip_carriage_and_insurance_paid_to": "[需要翻译: CIP - Carriage and Insurance Paid To]", "common.fas_free_alongside_ship": "[需要翻译: FAS - Free Alongside Ship]", "common.cfr_cost_and_freight": "[需要翻译: CFR - Cost and Freight]", "common.cif_cost_insurance_and_freight": "[需要翻译: CIF - Cost, Insurance and Freight]", "common.prepaid": "[需要翻译: Prepaid]", "common.net_15_days": "[需要翻译: Net 15 days]", "common.cash_on_delivery": "Cash on 交付", "common.letter_of_credit": "Letter of 贷方", "messages.cleanup_cancelled_confirmation_text_did_not_match": "[需要翻译: Cleanup cancelled - confirmation text did not match]", "messages.customer_cleanup_completed_successfully": "客户 cleanup completed successfully!", "messages.customer_cleanup_failed": "客户 cleanup failed: ", "messages.database_cleanup_completed_successfully": "[需要翻译: Database cleanup completed successfully!]", "messages.cleanup_failed": "[需要翻译: Cleanup failed: ]", "common.supplier_lead_time_updated_successfully": "供应商 lead time updated successfully", "common.this_feature_will_be_implemented_in_the_next_iteration": "[需要翻译: This feature will be implemented in the next iteration]", "common.lead_time_performance_updated_successfully": "[需要翻译: Lead time performance updated successfully]", "common.detailed_performance_history_tracking_not_yet_implemented": "[需要翻译: Detailed performance history tracking not yet implemented]", "common.market_benchmarking_functionality_not_yet_implemented": "[需要翻译: Market benchmarking functionality not yet implemented]", "common.individual_procurement_plan_creation_not_yet_implemented": "[需要翻译: Individual procurement plan creation not yet implemented]", "common.procurement_plans_generated_successfully_from_demand_forecast": "[需要翻译: Procurement plans generated successfully from demand forecast]", "common.procurement_plan_updated_successfully": "[需要翻译: Procurement plan updated successfully]", "common.purchase_order_creation_functionality_not_yet_implemented": "[需要翻译: Purchase order creation functionality not yet implemented]", "common.pipeline_forecast_generated_successfully": "[需要翻译: Pipeline forecast generated successfully]", "common.demand_forecast_updated_successfully": "[需要翻译: Demand forecast updated successfully]", "common.demand_forecast_deleted_successfully": "[需要翻译: Demand forecast deleted successfully]", "common.availability_checking_not_yet_implemented": "[需要翻译: Availability checking not yet implemented]", "common.procurement_plan_generation_not_yet_implemented": "[需要翻译: Procurement plan generation not yet implemented]", "common.container_optimization_not_yet_implemented": "[需要翻译: Container optimization not yet implemented]", "common.cannot_calculate_material_requirements_without_a_bom": "[需要翻译: Cannot calculate material requirements without a BOM]", "common.procurement_plan_structure_generated_implementation_pending": "[需要翻译: Procurement plan structure generated (implementation pending)]", "common.availability_checking_structure_generated_implementation_pending": "[需要翻译: Availability checking structure generated (implementation pending)]", "common.workflow_log_entry_created": "[需要翻译: Workflow log entry created]", "common.bom_data_seeded_successfully": "[需要翻译: BOM data seeded successfully]", "common.product_and_customer_data_cleanup_completed_successfully": "[需要翻译: Product and customer data cleanup completed successfully]", "common.customer_data_cleanup_completed_successfully": "客户 data cleanup completed successfully", "common.try_again": "[需要翻译: \n                      Try Again\n                    ]", "common.something_went_wrong": "[需要翻译: Something went wrong]", "common.page_error": "[需要翻译: Page Error]", "common.clear_all": "[需要翻译: \n          Clear all\n        ]", "forms.labels.approval_status": "Approval 状态", "forms.labels.select_priority": "[需要翻译: Select priority]", "common.revision_required": "[需要翻译: Revision Required]", "common.all_suppliers": "All 供应商s", "common.all_priorities": "[需要翻译: All Priorities]", "common.created_date": "创建d 日期", "common.name": "名称", "common.code": "[需要翻译: Code]", "common.sample_date": "Sample 日期", "common.approval_status": "Approval 状态", "common.newest_first": "[需要翻译: Newest First]", "common.oldest_first": "[需要翻译: Oldest First]", "common.forecast_profitability": "Forecast 利润ability", "forms.labels.search_materials_suppliers_or_skus": "搜索 materials, suppliers, or SKUs...", "common.handlebulkactionapprove_approve_selected": " handleBulkAction(\"approve\")}>\n                批准 Selected\n              ", "common.setselecteditems_clear_selection": "[需要翻译:  setSelectedItems([])}>\n                Clear Selection\n              ]", "common.ordered": "[需要翻译: Ordered]", "common.all_priority": "[需要翻译: All Priority]", "forms.labels.material_sku": "[需要翻译: Material SKU]", "forms.labels.estimated_cost": "[需要翻译: Estimated Cost]", "forms.labels.estimated_lead_time": "Estimated 交货期", "forms.labels.add_any_additional_notes_requirements_or_status_updates": "[需要翻译: Add any additional notes, requirements, or status updates...]", "common.plan_details": "[需要翻译: Plan Details]", "common.no_supplier_assigned": "[需要翻译: No supplier assigned]", "common.premium_fabric_mills_co": "[需要翻译: Premium Fabric Mills Co]", "common.global_textile_suppliers": "Global Textile 供应商s", "common.asia_pacific_materials": "[需要翻译: Asia Pacific Materials]", "forms.labels.supplier_optional": "供应商 (Optional)", "forms.labels.forecast_period": "[需要翻译: Forecast Period]", "forms.labels.forecasted_demand": "[需要翻译: Forecasted Demand]", "forms.labels.confidence_level": "[需要翻译: Confidence Level]", "forms.labels.forecast_method": "[需要翻译: Forecast Method]", "forms.labels.seasonality_adjustment": "[需要翻译: Seasonality Adjustment]", "forms.labels.trend_factor": "[需要翻译: Trend Factor]", "forms.labels.autoselect_best_supplier": "[需要翻译: Auto-select best supplier]", "forms.labels.select_period": "[需要翻译: Select period]", "forms.labels.10": "[需要翻译: 1.0]", "forms.labels.add_any_additional_notes_or_assumptions": "[需要翻译: Add any additional notes or assumptions...]", "common.additional_notes": "Additional 备注", "common.no_adjustment": "[需要翻译: No Adjustment]", "common.apply_seasonality": "[需要翻译: Apply Seasonality]", "common.setshowpreviewfalse_close": "[需要翻译:  setShowPreview(false)}\n                  >\n                    Close\n                  ]", "forms.labels.supplier_name": "供应商 名称 *", "forms.labels.enter_supplier_name": "[需要翻译: Enter supplier name]", "forms.labels.contact_person_name": "联系人 person name", "forms.labels.suppliercompanycom": "[需要翻译: <EMAIL>]", "forms.labels.supplier_address": "供应商 address", "messages.supplier_added_successfully": "供应商 added successfully!", "forms.labels.product_name": "Product 名称 *", "forms.labels.price": "价格", "forms.labels.enter_product_name": "[需要翻译: Enter product name]", "forms.labels.eg_silk001": "[需要翻译: e.g., SILK-001]", "forms.labels.eg_textiles_electronics": "[需要翻译: e.g., Textiles, Electronics]", "forms.labels.product_description_and_specifications": "[需要翻译: Product description and specifications]", "messages.product_added_successfully": "[需要翻译: Product added successfully!]", "forms.labels.enter_customer_name": "[需要翻译: Enter customer name]", "forms.labels.customercompanycom": "[需要翻译: <EMAIL>]", "forms.labels.customer_address": "客户 address", "messages.customer_added_successfully": "客户 added successfully!", "forms.labels.date_from": "日期 From", "forms.labels.date_to": "日期 To", "common.apply_filters": "\n                Apply 筛选s\n              ", "common.filters": "筛选s", "common.total_margin": "总计 Margin", "common.top_method": "[需要翻译: Top Method]", "common.optimization": "[需要翻译: Optimization]", "common.contract_margin_analysis": "[需要翻译: Contract Margin Analysis]", "common.method_performance": "Method 性能", "common.method_details": "[需要翻译: Method Details]", "common.cost_tracking_report": "[需要翻译: Cost Tracking Report]"}}