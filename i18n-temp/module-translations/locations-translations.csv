Key,English,Chinese,Context,File,Line,Category,Priority,Needs Review,Comments
forms.labels.location_name_id,"Location Name (ID)","位置 名称 (ID)","<Label>Location Name (ID)</Label>",app/locations/page.tsx,412,forms,high,false,
forms.labels.location_code,"Location Code","位置 Code","<Label htmlFor=""edit-location-code"">Location Code</Label>",app/locations/page.tsx,789,forms,high,false,
forms.labels.description,"Description","描述","<Label htmlFor=""edit-description"">Description</Label>",app/locations/page.tsx,764,forms,high,false,
forms.labels.type,"Type","类型","<Label htmlFor=""edit-type"">Type</Label>",app/locations/page.tsx,738,forms,high,false,
forms.labels.capacity,"Capacity","[需要翻译: Capacity]","<Label htmlFor=""edit-capacity"">Capacity</Label>",app/locations/page.tsx,777,forms,high,true,
forms.labels.zone,"Zone","[需要翻译: Zone]","<Label htmlFor=""edit-zone"">Zone</Label>",app/locations/page.tsx,800,forms,high,true,
forms.labels.location_name,"Location Name","位置 名称","<Label htmlFor=""edit-name"">Location Name</Label>",app/locations/page.tsx,727,forms,high,false,
forms.labels.security_level,"Security Level","[需要翻译: Security Level]","<Label htmlFor=""edit-security-level"">Security Level</Label>",app/locations/page.tsx,813,forms,high,true,
forms.labels.temperature_controlled,"Temperature Controlled","[需要翻译: Temperature Controlled]","<Label htmlFor=""edit-temperature-controlled"">Temperature Controlled</Label>",app/locations/page.tsx,842,forms,high,true,
forms.labels.allows_mixed_products,"Allows Mixed Products","[需要翻译: Allows Mixed Products]","<Label htmlFor=""edit-allows-mixed-products"">Allows Mixed Products</Label>",app/locations/page.tsx,853,forms,high,true,
forms.labels.eg_warehousenorth,"e.g., warehouse_north","[需要翻译: e.g., warehouse_north]","placeholder=""e.g., warehouse_north""",app/locations/page.tsx,416,forms,high,true,
forms.labels.eg_wh001,"e.g., WH-001","[需要翻译: e.g., WH-001]","placeholder=""e.g., WH-001""",app/locations/page.tsx,661,forms,high,true,
forms.labels.brief_description_of_the_location_purpose,"Brief description of the location purpose","[需要翻译: Brief description of the location purpose]","placeholder=""Brief description of the location purpose""",app/locations/page.tsx,434,forms,high,true,
forms.labels.eg_a1_b2,"e.g., A1, B2","[需要翻译: e.g., A1, B2]","placeholder=""e.g., A1, B2""",app/locations/page.tsx,471,forms,high,true,
forms.labels.enter_location_name,"Enter location name","[需要翻译: Enter location name]","placeholder=""Enter location name""",app/locations/page.tsx,617,forms,high,true,
forms.labels.select_type,"Select type","[需要翻译: Select type]","placeholder=""Select type""",app/locations/page.tsx,624,forms,high,true,
forms.labels.enter_location_description,"Enter location description","[需要翻译: Enter location description]","placeholder=""Enter location description""",app/locations/page.tsx,644,forms,high,true,
forms.labels.eg_a1,"e.g., A1","[需要翻译: e.g., A1]","placeholder=""e.g., A1""",app/locations/page.tsx,668,forms,high,true,
forms.labels.select_security_level,"Select security level","[需要翻译: Select security level]","placeholder=""Select security level""",app/locations/page.tsx,677,forms,high,true,
common.setiscreatingfalse_cancel," setIsCreating(false)}>
                  Cancel
                "," setIsCreating(false)}>
                  取消
                ","<Button variant=""outline"" onClick={() => setIsCreating(false)}>
                  Cancel
                </Button>",app/locations/page.tsx,699,actions,high,false,
common.seteditinglocationnull_cancel," setEditingLocation(null)}>
                    Cancel
                  "," set编辑ing位置(null)}>
                    取消
                  ","<Button variant=""outline"" onClick={() => setEditingLocation(null)}>
                    Cancel
                  </Button>",app/locations/page.tsx,858,actions,high,false,
common.update_location,"
                    Update Location
                  ","
                    Update 位置
                  ","<Button onClick={handleUpdateLocation}>
                    Update Location
                  </Button>",app/locations/page.tsx,861,actions,high,false,
tables.location,"Location","位置","<TableHead>Location</TableHead>",app/locations/page.tsx,502,tables,medium,false,
tables.type,"Type","类型","<TableHead>Type</TableHead>",app/locations/page.tsx,503,tables,medium,false,
tables.capacity,"Capacity","[需要翻译: Capacity]","<TableHead>Capacity</TableHead>",app/locations/page.tsx,504,tables,medium,true,
tables.utilization,"Utilization","[需要翻译: Utilization]","<TableHead>Utilization</TableHead>",app/locations/page.tsx,505,tables,medium,true,
tables.details,"Details","[需要翻译: Details]","<TableHead>Details</TableHead>",app/locations/page.tsx,506,tables,medium,true,
tables.status,"Status","状态","<TableHead>Status</TableHead>",app/locations/page.tsx,507,tables,medium,false,
tables.actions,"Actions","[需要翻译: Actions]","<TableHead>Actions</TableHead>",app/locations/page.tsx,508,tables,medium,true,
common.location_directory,"Location Directory","位置 Directory","<CardTitle>Location Directory</CardTitle>",app/locations/page.tsx,493,content,medium,false,
common.finished_goods,"Finished Goods","成品","<SelectItem value=""finished_goods"">Finished Goods</SelectItem>",app/locations/page.tsx,750,options,medium,false,
common.raw_materials,"Raw Materials","原材料","<SelectItem value=""raw_materials"">Raw Materials</SelectItem>",app/locations/page.tsx,751,options,medium,false,
common.work_in_progress,"Work in Progress","在制品","<SelectItem value=""work_in_progress"">Work in Progress</SelectItem>",app/locations/page.tsx,752,options,medium,false,
common.quality_control,"Quality Control","质量控制","<SelectItem value=""quality_control"">Quality Control</SelectItem>",app/locations/page.tsx,753,options,medium,false,
common.shipping,"Shipping","运输","<SelectItem value=""shipping"">Shipping</SelectItem>",app/locations/page.tsx,754,options,medium,false,
common.receiving,"Receiving","[需要翻译: Receiving]","<SelectItem value=""receiving"">Receiving</SelectItem>",app/locations/page.tsx,755,options,medium,true,
common.returns,"Returns","[需要翻译: Returns]","<SelectItem value=""returns"">Returns</SelectItem>",app/locations/page.tsx,756,options,medium,true,
common.quarantine,"Quarantine","[需要翻译: Quarantine]","<SelectItem value=""quarantine"">Quarantine</SelectItem>",app/locations/page.tsx,757,options,medium,true,
common.low,"Low","[需要翻译: Low]","<SelectItem value=""low"">Low</SelectItem>",app/locations/page.tsx,825,options,medium,true,
common.medium,"Medium","[需要翻译: Medium]","<SelectItem value=""medium"">Medium</SelectItem>",app/locations/page.tsx,826,options,medium,true,
common.high,"High","[需要翻译: High]","<SelectItem value=""high"">High</SelectItem>",app/locations/page.tsx,827,options,medium,true,
common.location_deleted_successfully,"Location deleted successfully","位置 deleted successfully","message: ""Location deleted successfully""",app/api/locations/[id]/route.ts,182,validation,low,false,
forms.labels.select_pickup_location,"Select pickup location","[需要翻译: Select pickup location]","placeholder=""Select pickup location""",components/shipping/location-selector.tsx,204,forms,high,true,