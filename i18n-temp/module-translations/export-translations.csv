Key,English,Chinese,Context,File,Line,Category,Priority,Needs Review,Comments
forms.labels.search_declarations,"Search declarations...","搜索 declarations...","placeholder=""Search declarations...""",app/export/page.tsx,174,forms,high,false,
tables.status,"Status","状态","<TableHead>Status</TableHead>",app/export/page.tsx,199,tables,medium,false,
tables.items,"Items","[需要翻译: Items]","<TableHead>Items</TableHead>",app/export/page.tsx,200,tables,medium,true,
tables.created,"Created","创建d","<TableHead>Created</TableHead>",app/export/page.tsx,201,tables,medium,false,
common.export_declarations,"Export Declarations","出口申报s","<CardTitle>Export Declarations</CardTitle>",app/export/page.tsx,185,content,medium,false,
tables.product,"Product","[需要翻译: Product]","<TableHead>Product</TableHead>",components/export/declaration-edit-form.tsx,167,tables,medium,true,
tables.quantity,"Quantity","数量","<TableHead>Quantity</TableHead>",components/export/declaration-edit-form.tsx,169,tables,medium,false,
tables.hs_code,"HS Code","[需要翻译: HS Code]","<TableHead>HS Code</TableHead>",components/export/declaration-edit-form.tsx,170,tables,medium,true,
common.declaration_items,"Declaration Items","[需要翻译: Declaration Items]","<CardTitle>Declaration Items</CardTitle>",components/export/declaration-edit-form.tsx,160,content,medium,true,
common.documents,"Documents","[需要翻译: Documents]","<CardTitle>Documents</CardTitle>",app/export/[id]/document-manager.tsx,94,content,medium,true,
messages.file_uploaded_successfully,"File uploaded successfully.","[需要翻译: File uploaded successfully.]","toast.success(""File uploaded successfully.""",app/export/[id]/document-manager.tsx,53,messages,low,true,
messages.failed_to_upload_file,"Failed to upload file.","[需要翻译: Failed to upload file.]","toast.error(""Failed to upload file.""",app/export/[id]/document-manager.tsx,59,messages,low,true,
messages.document_deleted_successfully,"Document deleted successfully.","[需要翻译: Document deleted successfully.]","toast.success(""Document deleted successfully.""",app/export/[id]/document-manager.tsx,77,messages,low,true,
messages.failed_to_delete_document,"Failed to delete document.","[需要翻译: Failed to delete document.]","toast.error(""Failed to delete document.""",app/export/[id]/document-manager.tsx,84,messages,low,true,
forms.labels.declaration_items,"Declaration Items","[需要翻译: Declaration Items]","<Label className=""text-base font-medium"">Declaration Items</Label>",components/export/product-inheritance.tsx,179,forms,high,true,
forms.labels.search_products,"Search products...","搜索 products...","placeholder=""Search products...""",components/export/product-inheritance.tsx,237,forms,high,false,
forms.labels.000000,"0000.00","[需要翻译: 0000.00]","placeholder=""0000.00""",components/export/product-inheritance.tsx,256,forms,high,true,
tables.quality_status,"Quality Status","Quality 状态","<TableHead>Quality Status</TableHead>",components/export/product-inheritance.tsx,225,tables,medium,false,
tables.actions,"Actions","[需要翻译: Actions]","<TableHead className=""w-[50px]"">Actions</TableHead>",components/export/product-inheritance.tsx,226,tables,medium,true,
common.attention_required,"Attention Required","[需要翻译: Attention Required]","<CardTitle className=""text-red-800"">Attention Required</CardTitle>",components/export/export-stats.tsx,96,content,medium,true,
common.total_declarations,"Total Declarations","总计 Declarations","<CardTitle className=""text-sm font-medium"">Total Declarations</CardTitle>",components/export/export-stats.tsx,133,content,medium,false,
common.draft,"Draft","[需要翻译: Draft]","<CardTitle className=""text-sm font-medium"">Draft</CardTitle>",components/export/export-stats.tsx,149,content,medium,true,
common.submitted,"Submitted","提交ted","<CardTitle className=""text-sm font-medium"">Submitted</CardTitle>",components/export/export-stats.tsx,165,content,medium,false,
common.approved,"Approved","已批准","<CardTitle className=""text-sm font-medium"">Approved</CardTitle>",components/export/export-stats.tsx,181,content,medium,false,
common.cleared,"Cleared","[需要翻译: Cleared]","<CardTitle className=""text-sm font-medium"">Cleared</CardTitle>",components/export/export-stats.tsx,197,content,medium,true,
forms.labels.declaration_number,"Declaration Number *","[需要翻译: Declaration Number *]","<Label htmlFor=""declaration-number"">Declaration Number *</Label>",components/export/create-declaration-form.tsx,181,forms,high,true,
forms.labels.status,"Status","状态","<Label>Status</Label>",components/export/create-declaration-form.tsx,191,forms,high,false,
forms.labels.enter_declaration_number,"Enter declaration number","[需要翻译: Enter declaration number]","placeholder=""Enter declaration number""",components/export/declaration-edit-form.tsx,110,forms,high,true,
forms.labels.select_status,"Select status","[需要翻译: Select status]","placeholder=""Select status""",components/export/declaration-edit-form.tsx,122,forms,high,true,
forms.labels.exp2025001,"EXP-2025-001","[需要翻译: EXP-2025-001]","placeholder=""EXP-2025-001""",components/export/create-declaration-form.tsx,186,forms,high,true,
common.routerpushexport_cancel," router.push('/export')}
        >
          Cancel
        "," router.push('/export')}
        >
          取消
        ","<Button
          type=""button""
          variant=""outline""
          onClick={() => router.push('/export')}
        >
          Cancel
        </Button>",components/export/create-declaration-form.tsx,225,actions,high,false,
forms.labels.sales_contract_optional,"Sales Contract (Optional)","[需要翻译: Sales Contract (Optional)]","<Label htmlFor=""contract-select"">Sales Contract (Optional)</Label>",components/export/contract-selector.tsx,154,forms,high,true,
forms.labels.search_contracts_or_select_manual_entry,"Search contracts or select manual entry...","搜索 contracts or select manual entry...","placeholder=""Search contracts or select manual entry...""",components/export/contract-selector.tsx,160,forms,high,false,
common.retry,"
            Retry
          ","[需要翻译: 
            Retry
          ]","<Button
            variant=""outline""
            size=""sm""
            onClick={loadContracts}
            className=""ml-2""
          >
            Retry
          </Button>",components/export/contract-selector.tsx,139,actions,high,true,