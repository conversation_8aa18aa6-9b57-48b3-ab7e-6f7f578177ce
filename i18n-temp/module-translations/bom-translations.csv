Key,English,Chinese,Context,File,Line,Category,Priority,Needs Review,Comments
forms.labels.raw_material,"Raw Material","[需要翻译: Raw Material]","<Label>Raw Material</Label>",app/products/[id]/bom-management.tsx,317,forms,high,true,
forms.labels.quantity_required,"Quantity Required *","数量 Required *","<Label htmlFor=""edit_qty_required"">Quantity Required *</Label>",app/products/[id]/bom-management.tsx,325,forms,high,false,
forms.labels.unit,"Unit *","[需要翻译: Unit *]","<Label htmlFor=""edit_unit"">Unit *</Label>",app/products/[id]/bom-management.tsx,337,forms,high,true,
forms.labels.waste_factor,"Waste Factor","[需要翻译: Waste Factor]","<Label htmlFor=""edit_waste_factor"">Waste Factor</Label>",app/products/[id]/bom-management.tsx,347,forms,high,true,
forms.labels.select_raw_material,"Select raw material","[需要翻译: Select raw material]","placeholder=""Select raw material""",app/products/[id]/bom-management.tsx,239,forms,high,true,
forms.labels.eg_25,"e.g., 2.5","[需要翻译: e.g., 2.5]","placeholder=""e.g., 2.5""",app/products/[id]/bom-management.tsx,330,forms,high,true,
forms.labels.eg_meters_kg_pieces,"e.g., meters, kg, pieces","[需要翻译: e.g., meters, kg, pieces]","placeholder=""e.g., meters, kg, pieces""",app/products/[id]/bom-management.tsx,340,forms,high,true,
forms.labels.005_5,"0.05 (5%)","[需要翻译: 0.05 (5%)]","placeholder=""0.05 (5%)""",app/products/[id]/bom-management.tsx,352,forms,high,true,
common.setisadddialogopenfalse_cancel," setIsAddDialogOpen(false)}>
                    Cancel
                  "," setIsAddDialogOpen(false)}>
                    取消
                  ","<Button variant=""outline"" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>",app/products/[id]/bom-management.tsx,295,actions,high,false,
common.setiseditdialogopenfalse_cancel," setIsEditDialogOpen(false)}>
                    Cancel
                  "," setIs编辑DialogOpen(false)}>
                    取消
                  ","<Button variant=""outline"" onClick={() => setIsEditDialogOpen(false)}>
                    Cancel
                  </Button>",app/products/[id]/bom-management.tsx,362,actions,high,false,
tables.material,"Material","[需要翻译: Material]","<TableHead>Material</TableHead>",app/products/[id]/bom-management.tsx,379,tables,medium,true,
tables.qty_required,"Qty Required","[需要翻译: Qty Required]","<TableHead>Qty Required</TableHead>",app/products/[id]/bom-management.tsx,381,tables,medium,true,
tables.unit,"Unit","[需要翻译: Unit]","<TableHead>Unit</TableHead>",app/products/[id]/bom-management.tsx,382,tables,medium,true,
tables.waste_factor,"Waste Factor","[需要翻译: Waste Factor]","<TableHead>Waste Factor</TableHead>",app/products/[id]/bom-management.tsx,383,tables,medium,true,
tables.cost,"Cost","[需要翻译: Cost]","<TableHead>Cost</TableHead>",app/products/[id]/bom-management.tsx,384,tables,medium,true,
tables.status,"Status","状态","<TableHead>Status</TableHead>",app/products/[id]/bom-management.tsx,385,tables,medium,false,
tables.actions,"Actions","[需要翻译: Actions]","<TableHead>Actions</TableHead>",components/bom/bom-overview-client.tsx,303,tables,medium,true,
common.bill_of_materials,"Bill of Materials","物料清单","<CardTitle>Bill of Materials</CardTitle>",app/products/[id]/bom-management.tsx,199,content,medium,false,
messages.please_fill_in_all_required_fields,"Please fill in all required fields","[需要翻译: Please fill in all required fields]","toast.error(""Please fill in all required fields""",app/products/[id]/bom-management.tsx,134,messages,low,true,
messages.bom_item_added_successfully,"BOM item added successfully","[需要翻译: BOM item added successfully]","toast.success(""BOM item added successfully""",app/products/[id]/bom-management.tsx,100,messages,low,true,
messages.network_error_occurred,"Network error occurred","[需要翻译: Network error occurred]","toast.error(""Network error occurred""",app/products/[id]/bom-management.tsx,190,messages,low,true,
messages.bom_item_updated_successfully,"BOM item updated successfully","[需要翻译: BOM item updated successfully]","toast.success(""BOM item updated successfully""",app/products/[id]/bom-management.tsx,151,messages,low,true,
messages.bom_item_removed_successfully,"BOM item removed successfully","[需要翻译: BOM item removed successfully]","toast.success(""BOM item removed successfully""",app/products/[id]/bom-management.tsx,183,messages,low,true,
common.bom_updated_successfully,"BOM updated successfully","[需要翻译: BOM updated successfully]","message: ""BOM updated successfully""",app/api/products/[id]/bom/route.ts,253,validation,low,true,
common.bom_cleared_successfully,"BOM cleared successfully","[需要翻译: BOM cleared successfully]","message: ""BOM cleared successfully""",app/api/products/[id]/bom/route.ts,295,validation,low,true,
common.bom_item_deleted_successfully,"BOM item deleted successfully","[需要翻译: BOM item deleted successfully]","message: ""BOM item deleted successfully""",app/api/products/[id]/bom/[bomId]/route.ts,141,validation,low,true,
forms.labels.search_products,"Search products...","搜索 products...","placeholder=""Search products...""",components/bom/bom-overview-client.tsx,264,forms,high,false,
forms.labels.filter_by_status,"Filter by status","筛选 by status","placeholder=""Filter by status""",components/bom/bom-overview-client.tsx,272,forms,high,false,
tables.product,"Product","[需要翻译: Product]","<TableHead>Product</TableHead>",components/bom/bom-overview-client.tsx,294,tables,medium,true,
tables.bom_status,"BOM Status","BOM 状态","<TableHead>BOM Status</TableHead>",components/bom/bom-overview-client.tsx,295,tables,medium,false,
tables.materials,"Materials","[需要翻译: Materials]","<TableHead>Materials</TableHead>",components/bom/bom-overview-client.tsx,296,tables,medium,true,
tables.categories,"Categories","[需要翻译: Categories]","<TableHead>Categories</TableHead>",components/bom/bom-overview-client.tsx,297,tables,medium,true,
tables.material_cost,"Material Cost","[需要翻译: Material Cost]","<TableHead>Material Cost</TableHead>",components/bom/bom-overview-client.tsx,298,tables,medium,true,
tables.selling_price,"Selling Price","Selling 价格","<TableHead>Selling Price</TableHead>",components/bom/bom-overview-client.tsx,299,tables,medium,false,
tables.profit,"Profit","利润","<TableHead>Profit</TableHead>",components/bom/bom-overview-client.tsx,300,tables,medium,false,
tables.margin,"Margin %","[需要翻译: Margin %]","<TableHead>Margin %</TableHead>",components/bom/bom-overview-client.tsx,301,tables,medium,true,
tables.last_updated,"Last Updated","[需要翻译: Last Updated]","<TableHead>Last Updated</TableHead>",components/bom/bom-overview-client.tsx,302,tables,medium,true,
common.total_products,"Total Products","总计 Products","<CardTitle className=""text-sm font-medium"">Total Products</CardTitle>",components/bom/bom-overview-client.tsx,199,content,medium,false,
common.with_bom,"With BOM","[需要翻译: With BOM]","<CardTitle className=""text-sm font-medium"">With BOM</CardTitle>",components/bom/bom-overview-client.tsx,210,content,medium,true,
common.without_bom,"Without BOM","[需要翻译: Without BOM]","<CardTitle className=""text-sm font-medium"">Without BOM</CardTitle>",components/bom/bom-overview-client.tsx,221,content,medium,true,
common.total_value,"Total Value","总计 Value","<CardTitle className=""text-sm font-medium"">Total Value</CardTitle>",components/bom/bom-overview-client.tsx,232,content,medium,false,
common.bom_overview,"BOM Overview","BOM 概览","<CardTitle>BOM Overview</CardTitle>",components/bom/bom-overview-client.tsx,248,content,medium,false,
common.all_products,"All Products","[需要翻译: All Products]","<SelectItem value=""all"">All Products</SelectItem>",components/bom/bom-overview-client.tsx,275,options,medium,true,
common.complete_boms,"Complete BOMs","[需要翻译: Complete BOMs]","<SelectItem value=""complete"">Complete BOMs</SelectItem>",components/bom/bom-overview-client.tsx,276,options,medium,true,
common.incomplete_boms,"Incomplete BOMs","[需要翻译: Incomplete BOMs]","<SelectItem value=""incomplete"">Incomplete BOMs</SelectItem>",components/bom/bom-overview-client.tsx,277,options,medium,true,
common.no_bom,"No BOM","[需要翻译: No BOM]","<SelectItem value=""empty"">No BOM</SelectItem>",components/bom/bom-overview-client.tsx,278,options,medium,true,