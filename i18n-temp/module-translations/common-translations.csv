Key,English,Chinese,Context,File,Line,Category,Priority,Needs Review,Comments
forms.labels.search_suppliers,"Search suppliers...","搜索 suppliers...","placeholder=""Search suppliers...""",app/suppliers/suppliers-client.tsx,113,forms,high,false,
forms.labels.eg_global_tech_supplies,"e.g. Global Tech Supplies","[需要翻译: e.g. Global Tech Supplies]","placeholder=""e.g. Global Tech Supplies""",app/suppliers/add-supplier-form.tsx,85,forms,high,true,
forms.labels.eg_jane_smith,"e.g. <PERSON>","[需要翻译: e.g. <PERSON>]","placeholder=""e.g. <PERSON>""",app/suppliers/add-supplier-form.tsx,98,forms,high,true,
forms.labels.eg_janesmithglobaltechcom,"e.g. <EMAIL>","[需要翻译: e.g. <EMAIL>]","placeholder=""e.g. <EMAIL>""",app/suppliers/add-supplier-form.tsx,111,forms,high,true,
forms.labels.eg_1_5559876543,"e.g. ******-987-6543","[需要翻译: e.g. ******-987-6543]","placeholder=""e.g. ******-987-6543""",app/suppliers/add-supplier-form.tsx,124,forms,high,true,
forms.labels.eg_456_industrial_ave_tech_city,"e.g. 456 Industrial Ave, Tech City","[需要翻译: e.g. 456 Industrial Ave, Tech City]","placeholder=""e.g. 456 Industrial Ave, Tech City""",app/suppliers/add-supplier-form.tsx,137,forms,high,true,
forms.labels.eg_bank_of_innovation_*********,"e.g. Bank of Innovation, *********","[需要翻译: e.g. Bank of Innovation, *********]","placeholder=""e.g. Bank of Innovation, *********""",app/suppliers/add-supplier-form.tsx,150,forms,high,true,
forms.labels.eg_vat_id_ein,"e.g. VAT ID, EIN","[需要翻译: e.g. VAT ID, EIN]","placeholder=""e.g. VAT ID, EIN""",app/suppliers/add-supplier-form.tsx,163,forms,high,true,
common.name_must_be_at_least_2_characters,"Name must be at least 2 characters.","名称 must be at least 2 characters.","message: ""Name must be at least 2 characters.""",app/crm/add-customer-form.tsx,16,validation,low,false,
common.invalid_email_address,"Invalid email address.","[需要翻译: Invalid email address.]","message: ""Invalid email address.""",app/crm/add-customer-form.tsx,19,validation,low,true,
forms.labels.search_samples_by_name_code_direction_status,"Search samples by name, code, direction, status...","搜索 samples by name, code, direction, status...","placeholder=""Search samples by name, code, direction, status...""",app/crm/[id]/customer-view-client.tsx,219,forms,high,false,
forms.labels.sample_direction,"Sample Direction","[需要翻译: Sample Direction]","placeholder=""Sample Direction""",components/samples/samples-filters.tsx,182,forms,high,true,
forms.labels.sample_code,"Sample Code *","[需要翻译: Sample Code *]","<Label htmlFor=""code"">Sample Code *</Label>",app/samples/create/page.tsx,251,forms,high,true,
forms.labels.sample_name,"Sample Name *","Sample 名称 *","<Label htmlFor=""name"">Sample Name *</Label>",app/samples/create/page.tsx,275,forms,high,false,
forms.labels.sample_type,"Sample Type","Sample 类型","placeholder=""Sample Type""",components/samples/samples-filters.tsx,164,forms,high,false,
forms.labels.priority,"Priority","[需要翻译: Priority]","placeholder=""Priority""",components/planning/procurement-planning-table.tsx,307,forms,high,true,
forms.labels.quantity,"Quantity","数量","<Label htmlFor=""quantity"">Quantity</Label>",app/samples/create/page.tsx,365,forms,high,false,
forms.labels.unit,"Unit","[需要翻译: Unit]","<FormLabel>Unit</FormLabel>",components/forms/product-select.tsx,234,forms,high,true,
forms.labels.technical_specifications,"Technical specifications...","[需要翻译: Technical specifications...]","placeholder=""Technical specifications...""",app/samples/create/page.tsx,390,forms,high,true,
forms.labels.quality_requirements,"Quality Requirements","[需要翻译: Quality Requirements]","<Label htmlFor=""quality_requirements"">Quality Requirements</Label>",app/samples/create/page.tsx,396,forms,high,true,
forms.labels.customer_receiving_sample,"Customer (Receiving Sample) *","客户 (Receiving Sample) *","<Label htmlFor=""customer_id"">Customer (Receiving Sample) *</Label>",app/samples/create/page.tsx,429,forms,high,false,
forms.labels.sample_purpose,"Sample Purpose","[需要翻译: Sample Purpose]","<Label htmlFor=""sample_purpose"">Sample Purpose</Label>",app/samples/create/page.tsx,530,forms,high,true,
forms.labels.delivery_instructions,"Delivery Instructions","交付 Instructions","<Label htmlFor=""notes"">Delivery Instructions</Label>",app/samples/create/page.tsx,458,forms,high,false,
forms.labels.sample_sender_type,"Sample Sender Type *","Sample Sender 类型 *","<Label htmlFor=""sender_type"">Sample Sender Type *</Label>",app/samples/create/page.tsx,474,forms,high,false,
forms.labels.customer_sender,"Customer (Sender) *","客户 (Sender) *","<Label htmlFor=""customer_id"">Customer (Sender) *</Label>",app/samples/create/page.tsx,500,forms,high,false,
forms.labels.supplier_sender,"Supplier (Sender) *","供应商 (Sender) *","<Label htmlFor=""supplier_id"">Supplier (Sender) *</Label>",app/samples/create/page.tsx,516,forms,high,false,
forms.labels.sample_condition_requirements,"Sample Condition & Requirements","[需要翻译: Sample Condition & Requirements]","<Label htmlFor=""notes"">Sample Condition & Requirements</Label>",app/samples/create/page.tsx,548,forms,high,true,
forms.labels.internal_purpose,"Internal Purpose","[需要翻译: Internal Purpose]","<Label htmlFor=""sample_purpose"">Internal Purpose</Label>",app/samples/create/page.tsx,564,forms,high,true,
forms.labels.internal_requirements,"Internal Requirements","[需要翻译: Internal Requirements]","<Label htmlFor=""notes"">Internal Requirements</Label>",app/samples/create/page.tsx,581,forms,high,true,
forms.labels.delivery_date,"Delivery Date","交付 日期","<Label htmlFor=""delivery_date"">Delivery Date</Label>",app/samples/create/page.tsx,608,forms,high,false,
forms.labels.sample_cost,"Sample Cost","[需要翻译: Sample Cost]","<Label htmlFor=""cost"">Sample Cost</Label>",app/samples/create/page.tsx,619,forms,high,true,
forms.labels.currency,"Currency","[需要翻译: Currency]","<Label htmlFor=""currency"">Currency</Label>",components/analytics/pricing-analytics-dashboard.tsx,275,forms,high,true,
forms.labels.testing_status,"Testing Status","Testing 状态","<Label htmlFor=""testing_status"">Testing Status</Label>",app/samples/create/page.tsx,652,forms,high,false,
forms.labels.testing_results,"Testing Results","[需要翻译: Testing Results]","<Label htmlFor=""testing_results"">Testing Results</Label>",app/samples/create/page.tsx,670,forms,high,true,
forms.labels.quote_requested,"Quote Requested","[需要翻译: Quote Requested]","<Label htmlFor=""quote_requested"">Quote Requested</Label>",app/samples/create/page.tsx,690,forms,high,true,
forms.labels.quote_provided,"Quote Provided","[需要翻译: Quote Provided]","<Label htmlFor=""quote_provided"">Quote Provided</Label>",app/samples/create/page.tsx,701,forms,high,true,
forms.labels.target_completion_date,"Target Completion Date","Target Completion 日期","<Label htmlFor=""delivery_date"">Target Completion Date</Label>",app/samples/create/page.tsx,710,forms,high,false,
forms.labels.eg_100,"e.g., 100","[需要翻译: e.g., 100]","placeholder=""e.g., 100""",app/samples/[id]/edit/page.tsx,460,forms,high,true,
forms.labels.eg_meters_pieces,"e.g., meters, pieces","[需要翻译: e.g., meters, pieces]","placeholder=""e.g., meters, pieces""",components/forms/product-select.tsx,236,forms,high,true,
forms.labels.quality_standards,"Quality standards...","[需要翻译: Quality standards...]","placeholder=""Quality standards...""",app/samples/create/page.tsx,401,forms,high,true,
forms.labels.search_customers_to_send_sample_to,"Search customers to send sample to...","搜索 customers to send sample to...","placeholder=""Search customers to send sample to...""",app/samples/create/page.tsx,434,forms,high,false,
forms.labels.why_are_we_sending_this_sample,"Why are we sending this sample?","[需要翻译: Why are we sending this sample?]","placeholder=""Why are we sending this sample?""",app/samples/create/page.tsx,447,forms,high,true,
forms.labels.special_delivery_instructions_customer_requirements,"Special delivery instructions, customer requirements...","[需要翻译: Special delivery instructions, customer requirements...]","placeholder=""Special delivery instructions, customer requirements...""",app/samples/create/page.tsx,463,forms,high,true,
forms.labels.who_sent_us_this_sample,"Who sent us this sample?","[需要翻译: Who sent us this sample?]","placeholder=""Who sent us this sample?""",app/samples/create/page.tsx,488,forms,high,true,
forms.labels.search_customer_who_sent_sample,"Search customer who sent sample...","搜索 customer who sent sample...","placeholder=""Search customer who sent sample...""",app/samples/create/page.tsx,505,forms,high,false,
forms.labels.search_supplier_who_sent_sample,"Search supplier who sent sample...","搜索 supplier who sent sample...","placeholder=""Search supplier who sent sample...""",app/samples/create/page.tsx,521,forms,high,false,
forms.labels.why_did_they_send_this_sample,"Why did they send this sample?","[需要翻译: Why did they send this sample?]","placeholder=""Why did they send this sample?""",app/samples/create/page.tsx,536,forms,high,true,
forms.labels.sample_condition_when_received_analysis_requirements_customer_specifications,"Sample condition when received, analysis requirements, customer specifications...","[需要翻译: Sample condition when received, analysis requirements, customer specifications...]","placeholder=""Sample condition when received, analysis requirements, customer specifications...""",app/samples/create/page.tsx,553,forms,high,true,
forms.labels.internal_testing_purpose,"Internal testing purpose","[需要翻译: Internal testing purpose]","placeholder=""Internal testing purpose""",app/samples/create/page.tsx,570,forms,high,true,
forms.labels.internal_testing_requirements_project_details_department_specifications,"Internal testing requirements, project details, department specifications...","[需要翻译: Internal testing requirements, project details, department specifications...]","placeholder=""Internal testing requirements, project details, department specifications...""",app/samples/create/page.tsx,586,forms,high,true,
forms.labels.eg_15000,"e.g. 150.00","[需要翻译: e.g. 150.00]","placeholder=""e.g. 150.00""",app/samples/create/page.tsx,624,forms,high,true,
forms.labels.quality_analysis_material_properties_test_results,"Quality analysis, material properties, test results...","[需要翻译: Quality analysis, material properties, test results...]","placeholder=""Quality analysis, material properties, test results...""",app/samples/create/page.tsx,675,forms,high,true,
common.product_information,"Product Information","[需要翻译: Product Information]","<CardTitle>Product Information</CardTitle>",app/samples/create/page.tsx,327,content,medium,true,
common.commercial_details,"Commercial Details","[需要翻译: Commercial Details]","<CardTitle>Commercial Details</CardTitle>",app/samples/create/page.tsx,598,content,medium,true,
common.outbound_we_send_to_customer,"📤 Outbound - We send to customer","[需要翻译: 📤 Outbound - We send to customer]","<SelectItem value=""outbound"">📤 Outbound - We send to customer</SelectItem>",app/samples/create/page.tsx,242,options,medium,true,
common.inbound_customersupplier_sends_to_us,"📥 Inbound - Customer/supplier sends to us","📥 Inbound - 客户/supplier sends to us","<SelectItem value=""inbound"">📥 Inbound - Customer/supplier sends to us</SelectItem>",app/samples/create/page.tsx,243,options,medium,false,
common.internal_internal_testingrd,"🏭 Internal - Internal testing/R&D","[需要翻译: 🏭 Internal - Internal testing/R&D]","<SelectItem value=""internal"">🏭 Internal - Internal testing/R&D</SelectItem>",app/samples/create/page.tsx,244,options,medium,true,
common.development,"Development","[需要翻译: Development]","<SelectItem value=""development"">Development</SelectItem>",app/samples/create/page.tsx,296,options,medium,true,
common.production,"Production","[需要翻译: Production]","<SelectItem value=""production"">Production</SelectItem>",app/samples/create/page.tsx,297,options,medium,true,
common.quality,"Quality","[需要翻译: Quality]","<SelectItem value=""quality"">Quality</SelectItem>",app/samples/create/page.tsx,298,options,medium,true,
common.prototype,"Prototype","[需要翻译: Prototype]","<SelectItem value=""prototype"">Prototype</SelectItem>",app/samples/create/page.tsx,299,options,medium,true,
common.low,"Low","[需要翻译: Low]","<SelectItem value=""low"">Low</SelectItem>",components/planning/procurement-plan-edit-client.tsx,264,options,medium,true,
common.normal,"Normal","[需要翻译: Normal]","<SelectItem value=""normal"">Normal</SelectItem>",components/planning/procurement-plan-edit-client.tsx,265,options,medium,true,
common.high,"High","[需要翻译: High]","<SelectItem value=""high"">High</SelectItem>",components/planning/procurement-plan-edit-client.tsx,266,options,medium,true,
common.urgent,"Urgent","[需要翻译: Urgent]","<SelectItem value=""urgent"">Urgent</SelectItem>",components/planning/procurement-plan-edit-client.tsx,267,options,medium,true,
common.customer_evaluation,"Customer Evaluation","客户 Evaluation","<SelectItem value=""customer_evaluation"">Customer Evaluation</SelectItem>",app/samples/create/page.tsx,450,options,medium,false,
common.marketing_demo,"Marketing Demo","[需要翻译: Marketing Demo]","<SelectItem value=""marketing_demo"">Marketing Demo</SelectItem>",app/samples/create/page.tsx,451,options,medium,true,
common.trade_show,"Trade Show","[需要翻译: Trade Show]","<SelectItem value=""trade_show"">Trade Show</SelectItem>",app/samples/create/page.tsx,452,options,medium,true,
common.sales_presentation,"Sales Presentation","[需要翻译: Sales Presentation]","<SelectItem value=""sales_presentation"">Sales Presentation</SelectItem>",app/samples/create/page.tsx,453,options,medium,true,
common.customer,"Customer","客户","<SelectItem value=""customer"">Customer</SelectItem>",app/samples/create/page.tsx,491,options,medium,false,
common.supplier,"Supplier","供应商","<SelectItem value=""supplier"">Supplier</SelectItem>",app/samples/create/page.tsx,492,options,medium,false,
common.manufacturing_quote_request,"Manufacturing Quote Request","[需要翻译: Manufacturing Quote Request]","<SelectItem value=""manufacturing_quote"">Manufacturing Quote Request</SelectItem>",app/samples/create/page.tsx,539,options,medium,true,
common.material_testing,"Material Testing","[需要翻译: Material Testing]","<SelectItem value=""material_testing"">Material Testing</SelectItem>",app/samples/create/page.tsx,540,options,medium,true,
common.reverse_engineering,"Reverse Engineering","[需要翻译: Reverse Engineering]","<SelectItem value=""reverse_engineering"">Reverse Engineering</SelectItem>",app/samples/create/page.tsx,541,options,medium,true,
common.quality_comparison,"Quality Comparison","[需要翻译: Quality Comparison]","<SelectItem value=""quality_comparison"">Quality Comparison</SelectItem>",app/samples/create/page.tsx,542,options,medium,true,
common.quality_control,"Quality Control","质量控制","<SelectItem value=""quality_control"">Quality Control</SelectItem>",app/samples/create/page.tsx,573,options,medium,false,
common.rd_testing,"R&D Testing","[需要翻译: R&D Testing]","<SelectItem value=""r_and_d"">R&D Testing</SelectItem>",app/samples/create/page.tsx,574,options,medium,true,
common.process_improvement,"Process Improvement","[需要翻译: Process Improvement]","<SelectItem value=""process_improvement"">Process Improvement</SelectItem>",app/samples/create/page.tsx,575,options,medium,true,
common.product_development,"Product Development","[需要翻译: Product Development]","<SelectItem value=""product_development"">Product Development</SelectItem>",app/samples/create/page.tsx,576,options,medium,true,
common.not_started,"Not Started","[需要翻译: Not Started]","<SelectItem value=""not_started"">Not Started</SelectItem>",app/samples/create/page.tsx,661,options,medium,true,
common.in_progress,"In Progress","进行中","<SelectItem value=""in_progress"">In Progress</SelectItem>",app/samples/create/page.tsx,662,options,medium,false,
common.completed,"Completed","已完成","<SelectItem value=""completed"">Completed</SelectItem>",app/purchase-contracts/edit/[id]/edit-purchase-contract-client.tsx,337,options,medium,false,
common.failed,"Failed","[需要翻译: Failed]","<SelectItem value=""failed"">Failed</SelectItem>",app/samples/create/page.tsx,664,options,medium,true,
common.windowhistoryback_go_back," window.history.back()}>
                Go Back
              ","[需要翻译:  window.history.back()}>
                Go Back
              ]","<Button variant=""outline"" onClick={() => window.history.back()}>
                Go Back
              </Button>",app/samples/[id]/page.tsx,124,actions,high,true,
forms.labels.eg_pcs_kg_m,"e.g., pcs, kg, m","[需要翻译: e.g., pcs, kg, m]","placeholder=""e.g., pcs, kg, m""",app/samples/[id]/edit/page.tsx,470,forms,high,true,
forms.labels.eg_1050,"e.g., 10.50","[需要翻译: e.g., 10.50]","placeholder=""e.g., 10.50""",app/samples/[id]/edit/page.tsx,480,forms,high,true,
forms.labels.eg_sc2025001,"e.g., SC-2025-001","[需要翻译: e.g., SC-2025-001]","placeholder=""e.g., SC-2025-001""",app/sales-contracts/edit/[id]/edit-sales-contract-client.tsx,278,forms,high,true,
forms.labels.select_customer,"Select customer...","[需要翻译: Select customer...]","placeholder=""Select customer...""",app/sales-contracts/edit/[id]/edit-sales-contract-client.tsx,298,forms,high,true,
forms.labels.eg_usd,"e.g., USD","[需要翻译: e.g., USD]","placeholder=""e.g., USD""",app/purchase-contracts/edit/[id]/edit-purchase-contract-client.tsx,307,forms,high,true,
forms.labels.select_status,"Select status","[需要翻译: Select status]","placeholder=""Select status""",components/planning/procurement-plan-edit-client.tsx,285,forms,high,true,
forms.labels.select_template,"Select template...","[需要翻译: Select template...]","placeholder=""Select template...""",app/purchase-contracts/edit/[id]/edit-purchase-contract-client.tsx,359,forms,high,true,
forms.labels.select_product,"Select product...","[需要翻译: Select product...]","placeholder=""Select product...""",app/purchase-contracts/edit/[id]/edit-purchase-contract-client.tsx,397,forms,high,true,
forms.labels.000,"0.00","[需要翻译: 0.00]","placeholder=""0.00""",components/forms/product-select.tsx,253,forms,high,true,
common.draft,"Draft","[需要翻译: Draft]","<SelectItem value=""draft"">Draft</SelectItem>",components/planning/procurement-plan-edit-client.tsx,288,options,medium,true,
common.under_review,"Under Review","[需要翻译: Under Review]","<SelectItem value=""review"">Under Review</SelectItem>",app/purchase-contracts/edit/[id]/edit-purchase-contract-client.tsx,334,options,medium,true,
common.approved,"Approved","已批准","<SelectItem value=""approved"">Approved</SelectItem>",components/planning/procurement-plan-edit-client.tsx,290,options,medium,false,
common.active,"Active","[需要翻译: Active]","<SelectItem value=""active"">Active</SelectItem>",components/samples/samples-filters.tsx,301,options,medium,true,
common.cancelled,"Cancelled","已取消","<SelectItem value=""cancelled"">Cancelled</SelectItem>",app/purchase-contracts/edit/[id]/edit-purchase-contract-client.tsx,338,options,medium,false,
messages.sales_contract_updated_successfully,"Sales contract updated successfully!","[需要翻译: Sales contract updated successfully!]","toast.success(""Sales contract updated successfully!""",app/sales-contracts/edit/[id]/edit-sales-contract-client.tsx,212,messages,low,true,
messages.failed_to_update_sales_contract,"Failed to update sales contract","[需要翻译: Failed to update sales contract]","toast.error(""Failed to update sales contract""",app/sales-contracts/edit/[id]/edit-sales-contract-client.tsx,221,messages,low,true,
common.usd_us_dollar,"USD - US Dollar","[需要翻译: USD - US Dollar]","<SelectItem value=""USD"">USD - US Dollar</SelectItem>",app/purchase-contracts/add/add-purchase-contract-client.tsx,291,options,medium,true,
common.eur_euro,"EUR - Euro","[需要翻译: EUR - Euro]","<SelectItem value=""EUR"">EUR - Euro</SelectItem>",app/purchase-contracts/add/add-purchase-contract-client.tsx,292,options,medium,true,
common.gbp_british_pound,"GBP - British Pound","[需要翻译: GBP - British Pound]","<SelectItem value=""GBP"">GBP - British Pound</SelectItem>",app/purchase-contracts/add/add-purchase-contract-client.tsx,294,options,medium,true,
common.cny_chinese_yuan,"CNY - Chinese Yuan","[需要翻译: CNY - Chinese Yuan]","<SelectItem value=""CNY"">CNY - Chinese Yuan</SelectItem>",app/purchase-contracts/add/add-purchase-contract-client.tsx,293,options,medium,true,
common.jpy_japanese_yen,"JPY - Japanese Yen","[需要翻译: JPY - Japanese Yen]","<SelectItem value=""JPY"">JPY - Japanese Yen</SelectItem>",app/sales-contracts/add/add-sales-contract-client.tsx,282,options,medium,true,
messages.sales_contract_created_successfully,"Sales contract created successfully!","[需要翻译: Sales contract created successfully!]","toast.success(""Sales contract created successfully!""",app/sales-contracts/add/add-sales-contract-client.tsx,109,messages,low,true,
messages.an_unexpected_error_occurred,"An unexpected error occurred","[需要翻译: An unexpected error occurred]","toast.error(""An unexpected error occurred""",components/forms/customer-select.tsx,108,messages,low,true,
forms.labels.eg_pc2025001,"e.g., PC-2025-001","[需要翻译: e.g., PC-2025-001]","placeholder=""e.g., PC-2025-001""",app/purchase-contracts/edit/[id]/edit-purchase-contract-client.tsx,270,forms,high,true,
forms.labels.select_supplier,"Select supplier","[需要翻译: Select supplier]","placeholder=""Select supplier""",components/samples/samples-filters.tsx,258,forms,high,true,
messages.purchase_contract_updated_successfully,"Purchase contract updated successfully!","[需要翻译: Purchase contract updated successfully!]","toast.success(""Purchase contract updated successfully!""",app/purchase-contracts/edit/[id]/edit-purchase-contract-client.tsx,204,messages,low,true,
messages.failed_to_update_purchase_contract,"Failed to update purchase contract","[需要翻译: Failed to update purchase contract]","toast.error(""Failed to update purchase contract""",app/purchase-contracts/edit/[id]/edit-purchase-contract-client.tsx,213,messages,low,true,
forms.labels.eg_highgrade_widget,"e.g. High-Grade Widget","[需要翻译: e.g. High-Grade Widget]","placeholder=""e.g. High-Grade Widget""",app/products/add-product-form.tsx,89,forms,high,true,
forms.labels.eg_widhg001,"e.g. WID-HG-001","[需要翻译: e.g. WID-HG-001]","placeholder=""e.g. WID-HG-001""",app/products/add-product-form.tsx,102,forms,high,true,
forms.labels.eg_pcs_kg_meters,"e.g. pcs, kg, meters","[需要翻译: e.g. pcs, kg, meters]","placeholder=""e.g. pcs, kg, meters""",app/products/add-product-form.tsx,115,forms,high,true,
forms.labels.eg_847990,"e.g. 847990","[需要翻译: e.g. 847990]","placeholder=""e.g. 847990""",app/products/add-product-form.tsx,128,forms,high,true,
forms.labels.eg_china,"e.g. China","[需要翻译: e.g. China]","placeholder=""e.g. China""",app/products/add-product-form.tsx,141,forms,high,true,
forms.labels.eg_carton_bag_pallet,"e.g. Carton, Bag, Pallet","[需要翻译: e.g. Carton, Bag, Pallet]","placeholder=""e.g. Carton, Bag, Pallet""",app/products/add-product-form.tsx,154,forms,high,true,
forms.labels.00,"0.0","[需要翻译: 0.0]","placeholder=""0.0""",app/products/add-product-form.tsx,214,forms,high,true,
forms.labels.eg_5_01mm_tolerance,"e.g. ±5%, 0.1mm tolerance","[需要翻译: e.g. ±5%, 0.1mm tolerance]","placeholder=""e.g. ±5%, 0.1mm tolerance""",app/products/add-product-form.tsx,279,forms,high,true,
common.sku_must_be_at_least_2_characters,"SKU must be at least 2 characters.","[需要翻译: SKU must be at least 2 characters.]","message: ""SKU must be at least 2 characters.""",app/products/edit-product-form.tsx,19,validation,low,true,
common.unit_is_required,"Unit is required.","[需要翻译: Unit is required.]","message: ""Unit is required.""",app/products/edit-product-form.tsx,20,validation,low,true,
common.active_forecasts,"Active Forecasts","[需要翻译: Active Forecasts]","<CardTitle className=""text-sm font-medium"">Active Forecasts</CardTitle>",app/planning/page.tsx,236,content,medium,true,
common.procurement_status,"Procurement Status","Procurement 状态","<CardTitle className=""text-sm font-medium"">Procurement Status</CardTitle>",app/planning/page.tsx,255,content,medium,false,
common.procurement_plans,"Procurement Plans","[需要翻译: Procurement Plans]","<CardTitle className=""text-sm font-medium"">Procurement Plans</CardTitle>",app/planning/page.tsx,281,content,medium,true,
common.action_required,"Action Required","[需要翻译: Action Required]","<CardTitle className=""text-sm font-medium"">Action Required</CardTitle>",app/planning/page.tsx,303,content,medium,true,
common.supplier_network,"Supplier Network","供应商 Network","<CardTitle className=""text-sm font-medium"">Supplier Network</CardTitle>",app/planning/page.tsx,332,content,medium,false,
common.avg_lead_time,"Avg Lead Time","Avg 交货期","<CardTitle className=""text-sm font-medium"">Avg Lead Time</CardTitle>",app/planning/page.tsx,345,content,medium,false,
common.ready_to_order,"Ready to Order","就绪 to Order","<CardTitle className=""text-sm font-medium"">Ready to Order</CardTitle>",app/planning/page.tsx,358,content,medium,false,
common.status,"Status","状态","<CardTitle className=""text-sm font-medium"">Status</CardTitle>",app/planning/procurement/[id]/page.tsx,195,content,medium,false,
common.priority,"Priority","[需要翻译: Priority]","<SelectItem value=""priority"">Priority</SelectItem>",components/samples/samples-filters.tsx,373,options,medium,true,
common.planned_quantity,"Planned Quantity","Planned 数量","<CardTitle className=""text-sm font-medium"">Planned Quantity</CardTitle>",app/planning/procurement/[id]/page.tsx,219,content,medium,false,
common.estimated_cost,"Estimated Cost","[需要翻译: Estimated Cost]","<CardTitle className=""text-sm font-medium"">Estimated Cost</CardTitle>",app/planning/procurement/[id]/page.tsx,230,content,medium,true,
common.material_information,"Material Information","[需要翻译: Material Information]","<CardTitle>Material Information</CardTitle>",app/planning/procurement/[id]/page.tsx,244,content,medium,true,
common.supplier_information,"Supplier Information","供应商 Information","<CardTitle>Supplier Information</CardTitle>",app/planning/procurement/[id]/page.tsx,276,content,medium,false,
common.notes,"Notes","备注","<CardTitle>Notes</CardTitle>",app/planning/procurement/[id]/page.tsx,301,content,medium,false,
common.audit_information,"Audit Information","[需要翻译: Audit Information]","<CardTitle>Audit Information</CardTitle>",app/planning/procurement/[id]/page.tsx,312,content,medium,true,
forms.labels.search_forecasts,"Search forecasts...","搜索 forecasts...","placeholder=""Search forecasts...""",app/planning/forecasting/page.tsx,172,forms,high,false,
forms.labels.all_statuses,"All statuses","[需要翻译: All statuses]","placeholder=""All statuses""",app/planning/forecasting/page.tsx,179,forms,high,true,
forms.labels.all_methods,"All methods","[需要翻译: All methods]","placeholder=""All methods""",app/planning/forecasting/page.tsx,194,forms,high,true,
forms.labels.all_levels,"All levels","[需要翻译: All levels]","placeholder=""All levels""",app/planning/forecasting/page.tsx,209,forms,high,true,
tables.product,"Product","[需要翻译: Product]","<TableHead>Product</TableHead>",app/planning/forecasting/page.tsx,240,tables,medium,true,
tables.period,"Period","[需要翻译: Period]","<TableHead>Period</TableHead>",app/planning/forecasting/page.tsx,241,tables,medium,true,
tables.demand,"Demand","[需要翻译: Demand]","<TableHead>Demand</TableHead>",app/planning/forecasting/page.tsx,242,tables,medium,true,
tables.profit_margin,"Profit Margin","利润 Margin","<TableHead>Profit Margin</TableHead>",app/planning/forecasting/page.tsx,243,tables,medium,false,
tables.confidence,"Confidence","[需要翻译: Confidence]","<TableHead>Confidence</TableHead>",app/planning/forecasting/page.tsx,244,tables,medium,true,
tables.method,"Method","[需要翻译: Method]","<TableHead>Method</TableHead>",app/planning/forecasting/page.tsx,245,tables,medium,true,
tables.supplier_prefs,"Supplier Prefs","供应商 Prefs","<TableHead>Supplier Prefs</TableHead>",app/planning/forecasting/page.tsx,246,tables,medium,false,
tables.status,"Status","状态","<TableHead>Status</TableHead>",components/planning/procurement-planning-table.tsx,382,tables,medium,false,
tables.created,"Created","创建d","<TableHead>Created</TableHead>",app/planning/forecasting/page.tsx,248,tables,medium,false,
tables.actions,"Actions","[需要翻译: Actions]","<TableHead className=""w-12"">Actions</TableHead>",components/planning/procurement-planning-table.tsx,383,tables,medium,true,
common.all_statuses,"All Statuses","All 状态es","<SelectItem value=""all"">All Statuses</SelectItem>",components/samples/samples-filters.tsx,300,options,medium,false,
common.pending,"Pending","待处理","<SelectItem value=""pending"">Pending</SelectItem>",components/planning/procurement-plan-edit-client.tsx,289,options,medium,false,
common.rejected,"Rejected","已拒绝","<SelectItem value=""rejected"">Rejected</SelectItem>",app/planning/forecasting/page.tsx,186,options,medium,false,
common.all_methods,"All Methods","[需要翻译: All Methods]","<SelectItem value=""all"">All Methods</SelectItem>",app/planning/forecasting/page.tsx,197,options,medium,true,
common.pipeline_analysis,"Pipeline Analysis","[需要翻译: Pipeline Analysis]","<SelectItem value=""pipeline"">Pipeline Analysis</SelectItem>",components/planning/demand-forecast-form.tsx,415,options,medium,true,
common.manual_entry,"Manual Entry","[需要翻译: Manual Entry]","<SelectItem value=""manual"">Manual Entry</SelectItem>",components/planning/demand-forecast-form.tsx,417,options,medium,true,
common.historical_data,"Historical Data","[需要翻译: Historical Data]","<SelectItem value=""historical"">Historical Data</SelectItem>",components/planning/demand-forecast-form.tsx,416,options,medium,true,
common.hybrid_method,"Hybrid Method","[需要翻译: Hybrid Method]","<SelectItem value=""hybrid"">Hybrid Method</SelectItem>",components/planning/demand-forecast-form.tsx,418,options,medium,true,
common.all_levels,"All Levels","[需要翻译: All Levels]","<SelectItem value=""all"">All Levels</SelectItem>",app/planning/forecasting/page.tsx,212,options,medium,true,
common.medium,"Medium","[需要翻译: Medium]","<SelectItem value=""medium"">Medium</SelectItem>",app/planning/forecasting/page.tsx,214,options,medium,true,
common.generated_procurement_plans,"Generated Procurement Plans","[需要翻译: Generated Procurement Plans]","<CardTitle>Generated Procurement Plans</CardTitle>",app/planning/forecasting/[id]/page.tsx,221,content,medium,true,
common.forecast_details,"Forecast Details","[需要翻译: Forecast Details]","<CardTitle>Forecast Details</CardTitle>",app/planning/forecasting/[id]/edit/page.tsx,105,content,medium,true,
forms.labels.company_name,"Company Name *","Company 名称 *","<Label htmlFor=""edit-name"">Company Name *</Label>",app/customers/page.tsx,348,forms,high,false,
forms.labels.legal_company_name,"Legal Company Name","Legal Company 名称","<Label htmlFor=""legal_name"">Legal Company Name</Label>",app/onboarding/page.tsx,314,forms,high,false,
forms.labels.email_address,"Email Address *","邮箱 地址 *","<Label htmlFor=""email"">Email Address *</Label>",app/onboarding/page.tsx,324,forms,high,false,
forms.labels.phone_number,"Phone Number","电话 Number","<Label htmlFor=""phone"">Phone Number</Label>",app/onboarding/page.tsx,335,forms,high,false,
forms.labels.website,"Website","[需要翻译: Website]","<Label htmlFor=""website"">Website</Label>",app/onboarding/page.tsx,345,forms,high,true,
forms.labels.address_line_1,"Address Line 1 *","地址 Line 1 *","<Label htmlFor=""address_line1"">Address Line 1 *</Label>",app/onboarding/page.tsx,355,forms,high,false,
forms.labels.address_line_2,"Address Line 2","地址 Line 2","<Label htmlFor=""address_line2"">Address Line 2</Label>",app/onboarding/page.tsx,365,forms,high,false,
forms.labels.city,"City","[需要翻译: City]","placeholder=""City""",app/company-profile/page.tsx,409,forms,high,true,
forms.labels.stateprovince,"State/Province","[需要翻译: State/Province]","<Label htmlFor=""state_province"">State/Province</Label>",app/onboarding/page.tsx,385,forms,high,true,
forms.labels.postal_code,"Postal Code","[需要翻译: Postal Code]","<Label htmlFor=""postal_code"">Postal Code</Label>",app/onboarding/page.tsx,395,forms,high,true,
forms.labels.country,"Country *","[需要翻译: Country *]","<Label htmlFor=""country"">Country *</Label>",app/onboarding/page.tsx,405,forms,high,true,
forms.labels.industry,"Industry *","[需要翻译: Industry *]","<Label htmlFor=""industry"">Industry *</Label>",app/onboarding/page.tsx,435,forms,high,true,
forms.labels.business_type,"Business Type *","Business 类型 *","<Label htmlFor=""business_type"">Business Type *</Label>",app/onboarding/page.tsx,453,forms,high,false,
forms.labels.number_of_employees,"Number of Employees *","[需要翻译: Number of Employees *]","<Label htmlFor=""employee_count"">Number of Employees *</Label>",app/onboarding/page.tsx,470,forms,high,true,
forms.labels.annual_revenue,"Annual Revenue","Annual 收入","<Label htmlFor=""annual_revenue"">Annual Revenue</Label>",app/onboarding/page.tsx,487,forms,high,false,
forms.labels.business_registration_number,"Business registration number","[需要翻译: Business registration number]","placeholder=""Business registration number""",app/company-profile/page.tsx,548,forms,high,true,
forms.labels.tax_id_ein,"Tax ID / EIN","[需要翻译: Tax ID / EIN]","<Label htmlFor=""tax_id"">Tax ID / EIN</Label>",app/onboarding/page.tsx,514,forms,high,true,
forms.labels.vat_number,"VAT Number","[需要翻译: VAT Number]","<Label htmlFor=""vat_number"">VAT Number</Label>",app/onboarding/page.tsx,524,forms,high,true,
forms.labels.bank_name,"Bank name","[需要翻译: Bank name]","placeholder=""Bank name""",app/company-profile/page.tsx,604,forms,high,true,
forms.labels.bank_account_number,"Bank Account Number","[需要翻译: Bank Account Number]","<Label htmlFor=""bank_account"">Bank Account Number</Label>",app/onboarding/page.tsx,553,forms,high,true,
forms.labels.swiftbic_code,"SWIFT/BIC code","[需要翻译: SWIFT/BIC code]","placeholder=""SWIFT/BIC code""",app/company-profile/page.tsx,632,forms,high,true,
forms.labels.bank_address,"Bank address","[需要翻译: Bank address]","placeholder=""Bank address""",app/company-profile/page.tsx,646,forms,high,true,
forms.labels.export_license_number,"Export license number","导出 license number","placeholder=""Export license number""",app/company-profile/page.tsx,675,forms,high,false,
forms.labels.customs_code,"Customs Code","海关 Code","<Label htmlFor=""customs_code"">Customs Code</Label>",app/onboarding/page.tsx,603,forms,high,false,
forms.labels.preferred_incoterms,"Preferred Incoterms *","[需要翻译: Preferred Incoterms *]","<Label htmlFor=""preferred_incoterms"">Preferred Incoterms *</Label>",app/onboarding/page.tsx,613,forms,high,true,
forms.labels.preferred_payment_terms,"Preferred Payment Terms *","Preferred 付款 Terms *","<Label htmlFor=""preferred_payment_terms"">Preferred Payment Terms *</Label>",app/onboarding/page.tsx,633,forms,high,false,
forms.labels.enter_company_name,"Enter company name","[需要翻译: Enter company name]","placeholder=""Enter company name""",app/company-profile/page.tsx,285,forms,high,true,
forms.labels.full_legal_name,"Full legal name","[需要翻译: Full legal name]","placeholder=""Full legal name""",app/company-profile/page.tsx,299,forms,high,true,
forms.labels.companyexamplecom,"<EMAIL>","[需要翻译: <EMAIL>]","placeholder=""<EMAIL>""",app/company-profile/page.tsx,314,forms,high,true,
forms.labels.1_555_1234567,"+1 (555) 123-4567","[需要翻译: +1 (555) 123-4567]","placeholder=""+1 (555) 123-4567""",components/forms/customer-select.tsx,189,forms,high,true,
forms.labels.street_address,"Street address","[需要翻译: Street address]","placeholder=""Street address""",app/company-profile/page.tsx,381,forms,high,true,
forms.labels.apartment_suite_etc,"Apartment, suite, etc.","[需要翻译: Apartment, suite, etc.]","placeholder=""Apartment, suite, etc.""",app/company-profile/page.tsx,395,forms,high,true,
forms.labels.state_or_province,"State or Province","[需要翻译: State or Province]","placeholder=""State or Province""",app/company-profile/page.tsx,423,forms,high,true,
forms.labels.select_country,"Select country","[需要翻译: Select country]","placeholder=""Select country""",app/company-profile/page.tsx,354,forms,high,true,
forms.labels.select_industry,"Select industry","[需要翻译: Select industry]","placeholder=""Select industry""",app/company-profile/page.tsx,463,forms,high,true,
forms.labels.select_business_type,"Select business type","[需要翻译: Select business type]","placeholder=""Select business type""",app/company-profile/page.tsx,486,forms,high,true,
forms.labels.select_employee_count,"Select employee count","[需要翻译: Select employee count]","placeholder=""Select employee count""",app/company-profile/page.tsx,506,forms,high,true,
forms.labels.select_revenue_range,"Select revenue range","[需要翻译: Select revenue range]","placeholder=""Select revenue range""",app/onboarding/page.tsx,490,forms,high,true,
forms.labels.registration_number,"Registration number","[需要翻译: Registration number]","placeholder=""Registration number""",app/onboarding/page.tsx,509,forms,high,true,
forms.labels.tax_identification_number,"Tax identification number","[需要翻译: Tax identification number]","placeholder=""Tax identification number""",app/company-profile/page.tsx,562,forms,high,true,
forms.labels.vat_registration_number,"VAT registration number","[需要翻译: VAT registration number]","placeholder=""VAT registration number""",app/company-profile/page.tsx,576,forms,high,true,
forms.labels.name_of_your_bank,"Name of your bank","名称 of your bank","placeholder=""Name of your bank""",app/onboarding/page.tsx,548,forms,high,false,
forms.labels.account_number,"Account number","[需要翻译: Account number]","placeholder=""Account number""",app/company-profile/page.tsx,618,forms,high,true,
forms.labels.swift_code,"SWIFT code","[需要翻译: SWIFT code]","placeholder=""SWIFT code""",app/onboarding/page.tsx,568,forms,high,true,
forms.labels.banks_full_address,"Bank's full address","[需要翻译: Bank's full address]","placeholder=""Bank's full address""",app/onboarding/page.tsx,578,forms,high,true,
forms.labels.customs_registration_code,"Customs registration code","海关 registration code","placeholder=""Customs registration code""",app/company-profile/page.tsx,689,forms,high,false,
forms.labels.select_incoterms,"Select Incoterms","[需要翻译: Select Incoterms]","placeholder=""Select Incoterms""",app/company-profile/page.tsx,701,forms,high,true,
forms.labels.select_payment_terms,"Select payment terms","[需要翻译: Select payment terms]","placeholder=""Select payment terms""",app/company-profile/page.tsx,727,forms,high,true,
common.united_states,"United States","[需要翻译: United States]","<SelectItem value=""US"">United States</SelectItem>",app/company-profile/page.tsx,357,options,medium,true,
common.canada,"Canada","[需要翻译: Canada]","<SelectItem value=""CA"">Canada</SelectItem>",app/company-profile/page.tsx,365,options,medium,true,
common.china,"China","[需要翻译: China]","<SelectItem value=""CN"">China</SelectItem>",app/company-profile/page.tsx,358,options,medium,true,
common.india,"India","[需要翻译: India]","<SelectItem value=""IN"">India</SelectItem>",app/company-profile/page.tsx,363,options,medium,true,
common.bangladesh,"Bangladesh","[需要翻译: Bangladesh]","<SelectItem value=""BD"">Bangladesh</SelectItem>",app/onboarding/page.tsx,415,options,medium,true,
common.vietnam,"Vietnam","[需要翻译: Vietnam]","<SelectItem value=""VN"">Vietnam</SelectItem>",app/onboarding/page.tsx,416,options,medium,true,
common.turkey,"Turkey","[需要翻译: Turkey]","<SelectItem value=""TR"">Turkey</SelectItem>",app/onboarding/page.tsx,417,options,medium,true,
common.italy,"Italy","[需要翻译: Italy]","<SelectItem value=""IT"">Italy</SelectItem>",app/onboarding/page.tsx,418,options,medium,true,
common.germany,"Germany","[需要翻译: Germany]","<SelectItem value=""DE"">Germany</SelectItem>",app/company-profile/page.tsx,360,options,medium,true,
common.united_kingdom,"United Kingdom","[需要翻译: United Kingdom]","<SelectItem value=""GB"">United Kingdom</SelectItem>",app/company-profile/page.tsx,359,options,medium,true,
common.textile_manufacturing,"Textile Manufacturing","[需要翻译: Textile Manufacturing]","<SelectItem value=""textile"">Textile Manufacturing</SelectItem>",app/onboarding/page.tsx,441,options,medium,true,
common.apparel_fashion,"Apparel & Fashion","[需要翻译: Apparel & Fashion]","<SelectItem value=""apparel"">Apparel & Fashion</SelectItem>",app/onboarding/page.tsx,442,options,medium,true,
common.home_textiles,"Home Textiles","[需要翻译: Home Textiles]","<SelectItem value=""home-textile"">Home Textiles</SelectItem>",app/onboarding/page.tsx,443,options,medium,true,
common.technical_textiles,"Technical Textiles","[需要翻译: Technical Textiles]","<SelectItem value=""technical-textile"">Technical Textiles</SelectItem>",app/onboarding/page.tsx,444,options,medium,true,
common.leather_goods,"Leather Goods","[需要翻译: Leather Goods]","<SelectItem value=""leather"">Leather Goods</SelectItem>",app/onboarding/page.tsx,445,options,medium,true,
common.footwear,"Footwear","[需要翻译: Footwear]","<SelectItem value=""footwear"">Footwear</SelectItem>",app/onboarding/page.tsx,446,options,medium,true,
common.other_manufacturing,"Other Manufacturing","[需要翻译: Other Manufacturing]","<SelectItem value=""other"">Other Manufacturing</SelectItem>",app/onboarding/page.tsx,447,options,medium,true,
common.corporation,"Corporation","[需要翻译: Corporation]","<SelectItem value=""corporation"">Corporation</SelectItem>",app/company-profile/page.tsx,490,options,medium,true,
common.limited_liability_company_llc,"Limited Liability Company (LLC)","[需要翻译: Limited Liability Company (LLC)]","<SelectItem value=""llc"">Limited Liability Company (LLC)</SelectItem>",app/onboarding/page.tsx,460,options,medium,true,
common.partnership,"Partnership","[需要翻译: Partnership]","<SelectItem value=""partnership"">Partnership</SelectItem>",app/company-profile/page.tsx,491,options,medium,true,
common.sole_proprietorship,"Sole Proprietorship","[需要翻译: Sole Proprietorship]","<SelectItem value=""sole_proprietorship"">Sole Proprietorship</SelectItem>",app/company-profile/page.tsx,492,options,medium,true,
common.cooperative,"Cooperative","[需要翻译: Cooperative]","<SelectItem value=""cooperative"">Cooperative</SelectItem>",app/onboarding/page.tsx,463,options,medium,true,
common.other,"Other","[需要翻译: Other]","<SelectItem value=""other"">Other</SelectItem>",app/company-profile/page.tsx,493,options,medium,true,
common.110_employees,"1-10 employees","[需要翻译: 1-10 employees]","<SelectItem value=""1-10"">1-10 employees</SelectItem>",app/company-profile/page.tsx,509,options,medium,true,
common.1150_employees,"11-50 employees","[需要翻译: 11-50 employees]","<SelectItem value=""11-50"">11-50 employees</SelectItem>",app/company-profile/page.tsx,510,options,medium,true,
common.51200_employees,"51-200 employees","[需要翻译: 51-200 employees]","<SelectItem value=""51-200"">51-200 employees</SelectItem>",app/company-profile/page.tsx,511,options,medium,true,
common.201500_employees,"201-500 employees","[需要翻译: 201-500 employees]","<SelectItem value=""201-500"">201-500 employees</SelectItem>",app/company-profile/page.tsx,512,options,medium,true,
common.5011000_employees,"501-1000 employees","[需要翻译: 501-1000 employees]","<SelectItem value=""501-1000"">501-1000 employees</SelectItem>",app/onboarding/page.tsx,480,options,medium,true,
common.1000_employees,"1000+ employees","[需要翻译: 1000+ employees]","<SelectItem value=""1000+"">1000+ employees</SelectItem>",app/onboarding/page.tsx,481,options,medium,true,
common.fob_free_on_board,"FOB - Free on Board","[需要翻译: FOB - Free on Board]","<SelectItem value=""FOB"">FOB - Free on Board</SelectItem>",app/company-profile/page.tsx,712,options,medium,true,
common.cif_cost_insurance_freight,"CIF (Cost, Insurance & Freight)","[需要翻译: CIF (Cost, Insurance & Freight)]","<SelectItem value=""CIF"">CIF (Cost, Insurance & Freight)</SelectItem>",app/onboarding/page.tsx,620,options,medium,true,
common.exw_ex_works,"EXW - Ex Works","[需要翻译: EXW - Ex Works]","<SelectItem value=""EXW"">EXW - Ex Works</SelectItem>",app/company-profile/page.tsx,704,options,medium,true,
common.fca_free_carrier,"FCA - Free Carrier","[需要翻译: FCA - Free Carrier]","<SelectItem value=""FCA"">FCA - Free Carrier</SelectItem>",app/company-profile/page.tsx,705,options,medium,true,
common.cpt_carriage_paid_to,"CPT - Carriage Paid To","[需要翻译: CPT - Carriage Paid To]","<SelectItem value=""CPT"">CPT - Carriage Paid To</SelectItem>",app/company-profile/page.tsx,706,options,medium,true,
common.cip_carriage_insurance_paid_to,"CIP (Carriage & Insurance Paid To)","[需要翻译: CIP (Carriage & Insurance Paid To)]","<SelectItem value=""CIP"">CIP (Carriage & Insurance Paid To)</SelectItem>",app/onboarding/page.tsx,624,options,medium,true,
common.dap_delivered_at_place,"DAP - Delivered at Place","DAP - 已交付 at Place","<SelectItem value=""DAP"">DAP - Delivered at Place</SelectItem>",app/company-profile/page.tsx,708,options,medium,false,
common.dpu_delivered_at_place_unloaded,"DPU - Delivered at Place Unloaded","DPU - 已交付 at Place Unloaded","<SelectItem value=""DPU"">DPU - Delivered at Place Unloaded</SelectItem>",app/company-profile/page.tsx,709,options,medium,false,
common.ddp_delivered_duty_paid,"DDP - Delivered Duty Paid","DDP - 已交付 Duty Paid","<SelectItem value=""DDP"">DDP - Delivered Duty Paid</SelectItem>",app/company-profile/page.tsx,710,options,medium,false,
common.net_30_days,"Net 30 days","[需要翻译: Net 30 days]","<SelectItem value=""net-30"">Net 30 days</SelectItem>",app/company-profile/page.tsx,732,options,medium,true,
common.net_60_days,"Net 60 days","[需要翻译: Net 60 days]","<SelectItem value=""net-60"">Net 60 days</SelectItem>",app/company-profile/page.tsx,733,options,medium,true,
common.net_90_days,"Net 90 days","[需要翻译: Net 90 days]","<SelectItem value=""net-90"">Net 90 days</SelectItem>",app/company-profile/page.tsx,734,options,medium,true,
common.lc_at_sight,"L/C at sight","[需要翻译: L/C at sight]","<SelectItem value=""L/C at sight"">L/C at sight</SelectItem>",app/onboarding/page.tsx,642,options,medium,true,
common.lc_30_days,"L/C 30 days","[需要翻译: L/C 30 days]","<SelectItem value=""L/C 30 days"">L/C 30 days</SelectItem>",app/onboarding/page.tsx,643,options,medium,true,
common.lc_60_days,"L/C 60 days","[需要翻译: L/C 60 days]","<SelectItem value=""L/C 60 days"">L/C 60 days</SelectItem>",app/onboarding/page.tsx,644,options,medium,true,
common.tt_in_advance,"T/T in advance","[需要翻译: T/T in advance]","<SelectItem value=""T/T advance"">T/T in advance</SelectItem>",app/onboarding/page.tsx,645,options,medium,true,
common.tt_30_advance_70_before_shipment,"T/T 30% advance, 70% before shipment","[需要翻译: T/T 30% advance, 70% before shipment]","<SelectItem value=""T/T 30% advance"">T/T 30% advance, 70% before shipment</SelectItem>",app/onboarding/page.tsx,646,options,medium,true,
common.tt_50_advance_50_before_shipment,"T/T 50% advance, 50% before shipment","[需要翻译: T/T 50% advance, 50% before shipment]","<SelectItem value=""T/T 50% advance"">T/T 50% advance, 50% before shipment</SelectItem>",app/onboarding/page.tsx,647,options,medium,true,
forms.labels.search_documents,"Search documents...","搜索 documents...","placeholder=""Search documents...""",app/docs/page.tsx,161,forms,high,false,
forms.labels.filter_by_type,"Filter by type","筛选 by type","placeholder=""Filter by type""",app/docs/page.tsx,170,forms,high,false,
forms.labels.status,"Status","状态","placeholder=""Status""",components/planning/procurement-planning-table.tsx,294,forms,high,false,
tables.document_name,"Document Name","Document 名称","<TableHead>Document Name</TableHead>",app/docs/page.tsx,543,tables,medium,false,
tables.type,"Type","类型","<TableHead>Type</TableHead>",app/docs/page.tsx,544,tables,medium,false,
tables.export_declaration,"Export Declaration","出口申报","<TableHead>Export Declaration</TableHead>",app/docs/page.tsx,199,tables,medium,false,
tables.created_date,"Created Date","创建d 日期","<TableHead>Created Date</TableHead>",app/docs/page.tsx,201,tables,medium,false,
tables.size,"Size","[需要翻译: Size]","<TableHead>Size</TableHead>",app/docs/page.tsx,549,tables,medium,true,
tables.created_by,"Created By","创建d By","<TableHead>Created By</TableHead>",app/docs/page.tsx,203,tables,medium,false,
tables.certificate_name,"Certificate Name","证书 名称","<TableHead>Certificate Name</TableHead>",app/docs/page.tsx,315,tables,medium,false,
tables.issuer,"Issuer","[需要翻译: Issuer]","<TableHead>Issuer</TableHead>",app/docs/page.tsx,317,tables,medium,true,
tables.issue_date,"Issue Date","Issue 日期","<TableHead>Issue Date</TableHead>",app/docs/page.tsx,318,tables,medium,false,
tables.expiry_date,"Expiry Date","Expiry 日期","<TableHead>Expiry Date</TableHead>",app/docs/page.tsx,319,tables,medium,false,
tables.contract_name,"Contract Name","Contract 名称","<TableHead>Contract Name</TableHead>",app/docs/page.tsx,429,tables,medium,false,
tables.party,"Party","[需要翻译: Party]","<TableHead>Party</TableHead>",app/docs/page.tsx,431,tables,medium,true,
tables.contract_date,"Contract Date","Contract 日期","<TableHead>Contract Date</TableHead>",app/docs/page.tsx,432,tables,medium,false,
tables.value,"Value","[需要翻译: Value]","<TableHead>Value</TableHead>",app/docs/page.tsx,433,tables,medium,true,
tables.regulation,"Regulation","[需要翻译: Regulation]","<TableHead>Regulation</TableHead>",app/docs/page.tsx,545,tables,medium,true,
tables.last_updated,"Last Updated","[需要翻译: Last Updated]","<TableHead>Last Updated</TableHead>",app/docs/page.tsx,546,tables,medium,true,
tables.next_review,"Next Review","[需要翻译: Next Review]","<TableHead>Next Review</TableHead>",app/docs/page.tsx,547,tables,medium,true,
common.all_types,"All Types","All 类型s","<SelectItem value="""">All Types</SelectItem>",app/docs/page.tsx,173,options,medium,false,
common.commercial_invoice,"Commercial Invoice","Commercial 发票","<SelectItem value=""Commercial Invoice"">Commercial Invoice</SelectItem>",app/docs/page.tsx,174,options,medium,false,
common.packing_list,"Packing List","[需要翻译: Packing List]","<SelectItem value=""Packing List"">Packing List</SelectItem>",app/docs/page.tsx,175,options,medium,true,
common.bill_of_lading,"Bill of Lading","[需要翻译: Bill of Lading]","<SelectItem value=""Bill of Lading"">Bill of Lading</SelectItem>",app/docs/page.tsx,176,options,medium,true,
common.certificate_of_origin,"Certificate of Origin","证书 of Origin","<SelectItem value=""Certificate of Origin"">Certificate of Origin</SelectItem>",app/docs/page.tsx,177,options,medium,false,
common.export_license,"Export License","导出 License","<SelectItem value=""Export License"">Export License</SelectItem>",app/docs/page.tsx,178,options,medium,false,
common.all_status,"All Status","All 状态","<SelectItem value=""all"">All Status</SelectItem>",components/planning/procurement-planning-table.tsx,297,options,medium,false,
forms.labels.search_collections,"Search collections...","搜索 collections...","placeholder=""Search collections...""",app/design-catalog/page.tsx,229,forms,high,false,
forms.labels.season,"Season","[需要翻译: Season]","placeholder=""Season""",app/design-catalog/page.tsx,238,forms,high,true,
forms.labels.category,"Category","类别","<FormLabel>Category</FormLabel>",components/forms/product-select.tsx,268,forms,high,false,
tables.design_id,"Design ID","[需要翻译: Design ID]","<TableHead>Design ID</TableHead>",app/design-catalog/page.tsx,327,tables,medium,true,
tables.name,"Name","名称","<TableHead>Name</TableHead>",app/design-catalog/page.tsx,328,tables,medium,false,
tables.collection,"Collection","[需要翻译: Collection]","<TableHead>Collection</TableHead>",app/design-catalog/page.tsx,329,tables,medium,true,
tables.category,"Category","类别","<TableHead>Category</TableHead>",app/design-catalog/page.tsx,330,tables,medium,false,
tables.fabric,"Fabric","[需要翻译: Fabric]","<TableHead>Fabric</TableHead>",app/design-catalog/page.tsx,331,tables,medium,true,
tables.colors,"Colors","[需要翻译: Colors]","<TableHead>Colors</TableHead>",app/design-catalog/page.tsx,332,tables,medium,true,
tables.costprice,"Cost/Price","Cost/价格","<TableHead>Cost/Price</TableHead>",app/design-catalog/page.tsx,334,tables,medium,false,
common.active_collections,"Active Collections","[需要翻译: Active Collections]","<CardTitle className=""text-sm font-medium"">Active Collections</CardTitle>",app/design-catalog/page.tsx,172,content,medium,true,
common.total_designs,"Total Designs","总计 Designs","<CardTitle className=""text-sm font-medium"">Total Designs</CardTitle>",app/design-catalog/page.tsx,182,content,medium,false,
common.production_ready,"Production Ready","Production 就绪","<CardTitle className=""text-sm font-medium"">Production Ready</CardTitle>",app/design-catalog/page.tsx,192,content,medium,false,
common.color_palettes,"Color Palettes","[需要翻译: Color Palettes]","<CardTitle>Color Palettes</CardTitle>",app/design-catalog/page.tsx,381,content,medium,true,
common.design_collections,"Design Collections","[需要翻译: Design Collections]","<CardTitle>Design Collections</CardTitle>",app/design-catalog/page.tsx,223,content,medium,true,
common.individual_designs,"Individual Designs","[需要翻译: Individual Designs]","<CardTitle>Individual Designs</CardTitle>",app/design-catalog/page.tsx,320,content,medium,true,
common.popular_categories,"Popular Categories","[需要翻译: Popular Categories]","<CardTitle>Popular Categories</CardTitle>",app/design-catalog/page.tsx,434,content,medium,true,
common.seasonal_performance,"Seasonal Performance","Seasonal 性能","<CardTitle>Seasonal Performance</CardTitle>",app/design-catalog/page.tsx,460,content,medium,false,
common.all_seasons,"All Seasons","[需要翻译: All Seasons]","<SelectItem value=""all"">All Seasons</SelectItem>",app/design-catalog/page.tsx,241,options,medium,true,
common.spring_2024,"Spring 2024","[需要翻译: Spring 2024]","<SelectItem value=""spring"">Spring 2024</SelectItem>",app/design-catalog/page.tsx,242,options,medium,true,
common.summer_2024,"Summer 2024","[需要翻译: Summer 2024]","<SelectItem value=""summer"">Summer 2024</SelectItem>",app/design-catalog/page.tsx,243,options,medium,true,
common.fall_2024,"Fall 2024","[需要翻译: Fall 2024]","<SelectItem value=""fall"">Fall 2024</SelectItem>",app/design-catalog/page.tsx,244,options,medium,true,
common.winter_2024,"Winter 2024","[需要翻译: Winter 2024]","<SelectItem value=""winter"">Winter 2024</SelectItem>",app/design-catalog/page.tsx,245,options,medium,true,
common.all_categories,"All Categories","[需要翻译: All Categories]","<SelectItem value=""all"">All Categories</SelectItem>",app/design-catalog/page.tsx,254,options,medium,true,
common.casual_wear,"Casual Wear","[需要翻译: Casual Wear]","<SelectItem value=""casual wear"">Casual Wear</SelectItem>",app/design-catalog/page.tsx,255,options,medium,true,
common.formal_wear,"Formal Wear","[需要翻译: Formal Wear]","<SelectItem value=""formal wear"">Formal Wear</SelectItem>",app/design-catalog/page.tsx,256,options,medium,true,
common.resort_wear,"Resort Wear","[需要翻译: Resort Wear]","<SelectItem value=""resort wear"">Resort Wear</SelectItem>",app/design-catalog/page.tsx,257,options,medium,true,
common.activewear,"Activewear","[需要翻译: Activewear]","<SelectItem value=""activewear"">Activewear</SelectItem>",app/design-catalog/page.tsx,258,options,medium,true,
forms.labels.contact_person,"Contact Person","联系人 Person","<Label htmlFor=""edit-contact_name"">Contact Person</Label>",app/customers/page.tsx,358,forms,high,false,
forms.labels.phone,"Phone","电话","<FormLabel>Phone</FormLabel>",components/forms/customer-select.tsx,187,forms,high,false,
forms.labels.email,"Email","邮箱","<FormLabel>Email</FormLabel>",components/forms/customer-select.tsx,169,forms,high,false,
forms.labels.address,"Address","地址","<FormLabel>Address</FormLabel>",components/forms/customer-select.tsx,201,forms,high,false,
forms.labels.incoterm,"Incoterm","[需要翻译: Incoterm]","<FormLabel>Incoterm</FormLabel>",app/crm/add-customer-form.tsx,168,forms,high,true,
forms.labels.payment_terms,"Payment Terms","付款 Terms","<FormLabel>Payment Terms</FormLabel>",app/crm/add-customer-form.tsx,191,forms,high,false,
forms.labels.abc_electronics_inc,"ABC Electronics Inc.","[需要翻译: ABC Electronics Inc.]","placeholder=""ABC Electronics Inc.""",app/customers/page.tsx,235,forms,high,true,
forms.labels.sarah_johnson,"Sarah Johnson","[需要翻译: Sarah Johnson]","placeholder=""Sarah Johnson""",app/customers/page.tsx,245,forms,high,true,
forms.labels.15550123,"******-0123","[需要翻译: ******-0123]","placeholder=""******-0123""",app/customers/page.tsx,254,forms,high,true,
forms.labels.sarahabcelectronicscom,"<EMAIL>","[需要翻译: <EMAIL>]","placeholder=""<EMAIL>""",app/customers/page.tsx,264,forms,high,true,
forms.labels.1234_industrial_blvd_los_angeles_ca_90210_usa,"1234 Industrial Blvd, Los Angeles, CA 90210, USA","[需要翻译: 1234 Industrial Blvd, Los Angeles, CA 90210, USA]","placeholder=""1234 Industrial Blvd, Los Angeles, CA 90210, USA""",app/customers/page.tsx,273,forms,high,true,
forms.labels.ustax*********,"US-TAX-*********","[需要翻译: US-TAX-*********]","placeholder=""US-TAX-*********""",app/customers/page.tsx,282,forms,high,true,
forms.labels.chase_bank_account_*********0,"Chase Bank - Account: *********0","[需要翻译: Chase Bank - Account: *********0]","placeholder=""Chase Bank - Account: *********0""",app/customers/page.tsx,291,forms,high,true,
forms.labels.acme_corporation,"Acme Corporation","[需要翻译: Acme Corporation]","placeholder=""Acme Corporation""",app/customers/page.tsx,353,forms,high,true,
forms.labels.john_smith,"John Smith","[需要翻译: John Smith]","placeholder=""John Smith""",app/customers/page.tsx,363,forms,high,true,
forms.labels.johnacmecom,"<EMAIL>","[需要翻译: <EMAIL>]","placeholder=""<EMAIL>""",app/customers/page.tsx,382,forms,high,true,
forms.labels.123_business_st_city_state_12345,"123 Business St, City, State 12345","[需要翻译: 123 Business St, City, State 12345]","placeholder=""123 Business St, City, State 12345""",app/customers/page.tsx,392,forms,high,true,
forms.labels.search_customers,"Search customers...","搜索 customers...","placeholder=""Search customers...""",components/samples/samples-filters.tsx,231,forms,high,false,
common.setshoweditdialogfalse_cancel," setShowEditDialog(false)}>
                    Cancel
                  "," setShow编辑Dialog(false)}>
                    取消
                  ","<Button type=""button"" variant=""outline"" onClick={() => setShowEditDialog(false)}>
                    Cancel
                  </Button>",app/customers/page.tsx,427,actions,high,false,
common.update_customer,"
                    Update Customer
                  ","
                    Update 客户
                  ","<Button type=""submit"">
                    Update Customer
                  </Button>",app/customers/page.tsx,430,actions,high,false,
common.30_days,"30 days","[需要翻译: 30 days]","<SelectItem value=""30 days"">30 days</SelectItem>",app/crm/add-customer-form.tsx,199,options,medium,true,
common.60_days,"60 days","[需要翻译: 60 days]","<SelectItem value=""60 days"">60 days</SelectItem>",app/crm/add-customer-form.tsx,200,options,medium,true,
common.90_days,"90 days","[需要翻译: 90 days]","<SelectItem value=""90 days"">90 days</SelectItem>",app/crm/add-customer-form.tsx,201,options,medium,true,
common.cash,"Cash","[需要翻译: Cash]","<SelectItem value=""Cash"">Cash</SelectItem>",app/crm/add-customer-form.tsx,202,options,medium,true,
common.tt,"T/T","[需要翻译: T/T]","<SelectItem value=""T/T"">T/T</SelectItem>",app/crm/add-customer-form.tsx,203,options,medium,true,
forms.labels.customer_name,"Customer Name *","客户 名称 *","<FormLabel>Customer Name *</FormLabel>",components/forms/customer-select.tsx,155,forms,high,false,
forms.labels.contact_name,"Contact Name","联系人 名称","<FormLabel>Contact Name</FormLabel>",components/forms/supplier-select.tsx,172,forms,high,false,
forms.labels.contact_email,"Contact Email","联系人 邮箱","<FormLabel>Contact Email</FormLabel>",app/crm/add-customer-form.tsx,103,forms,high,false,
forms.labels.contact_phone,"Contact Phone","联系人 电话","<FormLabel>Contact Phone</FormLabel>",app/crm/add-customer-form.tsx,116,forms,high,false,
forms.labels.payment_term,"Payment Term","付款 Term","<FormLabel>Payment Term</FormLabel>",app/crm/edit-customer-form.tsx,167,forms,high,false,
forms.labels.eg_acme_inc,"e.g. Acme Inc.","[需要翻译: e.g. Acme Inc.]","placeholder=""e.g. Acme Inc.""",app/crm/add-customer-form.tsx,79,forms,high,true,
forms.labels.eg_john_doe,"e.g. John Doe","[需要翻译: e.g. John Doe]","placeholder=""e.g. John Doe""",app/crm/add-customer-form.tsx,92,forms,high,true,
forms.labels.eg_johndoeacmecom,"e.g. <EMAIL>","[需要翻译: e.g. <EMAIL>]","placeholder=""e.g. <EMAIL>""",app/crm/add-customer-form.tsx,105,forms,high,true,
forms.labels.eg_1_5551234567,"e.g. ******-123-4567","[需要翻译: e.g. ******-123-4567]","placeholder=""e.g. ******-123-4567""",app/crm/add-customer-form.tsx,118,forms,high,true,
forms.labels.eg_123_main_st_anytown_usa,"e.g. 123 Main St, Anytown USA","[需要翻译: e.g. 123 Main St, Anytown USA]","placeholder=""e.g. 123 Main St, Anytown USA""",app/crm/add-customer-form.tsx,131,forms,high,true,
forms.labels.eg_fob,"e.g. FOB","[需要翻译: e.g. FOB]","placeholder=""e.g. FOB""",app/crm/edit-customer-form.tsx,156,forms,high,true,
forms.labels.eg_net_30,"e.g. Net 30","[需要翻译: e.g. Net 30]","placeholder=""e.g. Net 30""",app/crm/edit-customer-form.tsx,169,forms,high,true,
messages.customer_updated_successfully,"Customer updated successfully.","客户 updated successfully.","toast.success(""Customer updated successfully.""",app/crm/edit-customer-form.tsx,63,messages,low,false,
messages.failed_to_update_customer,"Failed to update customer.","[需要翻译: Failed to update customer.]","toast.error(""Failed to update customer.""",app/crm/edit-customer-form.tsx,75,messages,low,true,
forms.labels.tax_id,"Tax ID","[需要翻译: Tax ID]","<FormLabel>Tax ID</FormLabel>",app/crm/add-customer-form.tsx,142,forms,high,true,
forms.labels.bank_information,"Bank Information","[需要翻译: Bank Information]","<FormLabel>Bank Information</FormLabel>",app/crm/add-customer-form.tsx,155,forms,high,true,
forms.labels.eg_ustax*********,"e.g. US-TAX-*********","[需要翻译: e.g. US-TAX-*********]","placeholder=""e.g. US-TAX-*********""",app/crm/add-customer-form.tsx,144,forms,high,true,
forms.labels.eg_chase_bank_account_*********0,"e.g. Chase Bank - Account: *********0","[需要翻译: e.g. Chase Bank - Account: *********0]","placeholder=""e.g. Chase Bank - Account: *********0""",app/crm/add-customer-form.tsx,157,forms,high,true,
forms.labels.select_incoterm,"Select incoterm","[需要翻译: Select incoterm]","placeholder=""Select incoterm""",app/crm/add-customer-form.tsx,172,forms,high,true,
messages.customer_created_successfully,"Customer created successfully.","客户 created successfully.","toast.success(""Customer created successfully.""",app/crm/add-customer-form.tsx,56,messages,low,false,
messages.failed_to_create_customer,"Failed to create customer.","[需要翻译: Failed to create customer.]","toast.error(""Failed to create customer.""",app/crm/add-customer-form.tsx,63,messages,low,true,
common.sales_contracts,"Sales Contracts","[需要翻译: Sales Contracts]","<CardTitle>Sales Contracts</CardTitle>",app/crm/[id]/customer-view-client.tsx,176,content,medium,true,
forms.labels.select,"Select","[需要翻译: Select]","placeholder=""Select""",app/contracts/page.tsx,302,forms,high,true,
tables.items,"Items","[需要翻译: Items]","<TableHead>Items</TableHead>",app/contracts/page.tsx,358,tables,medium,true,
forms.labels.standard_sales_contract,"Standard Sales Contract","[需要翻译: Standard Sales Contract]","placeholder=""Standard Sales Contract""",app/contract-templates/page.tsx,396,forms,high,true,
forms.labels.standard_purchase_contract,"Standard Purchase Contract","[需要翻译: Standard Purchase Contract]","placeholder=""Standard Purchase Contract""",app/contract-templates/page.tsx,605,forms,high,true,
forms.labels.select_annual_revenue,"Select annual revenue","[需要翻译: Select annual revenue]","placeholder=""Select annual revenue""",app/company-profile/page.tsx,526,forms,high,true,
common.france,"France","[需要翻译: France]","<SelectItem value=""FR"">France</SelectItem>",app/company-profile/page.tsx,361,options,medium,true,
common.japan,"Japan","[需要翻译: Japan]","<SelectItem value=""JP"">Japan</SelectItem>",app/company-profile/page.tsx,362,options,medium,true,
common.brazil,"Brazil","[需要翻译: Brazil]","<SelectItem value=""BR"">Brazil</SelectItem>",app/company-profile/page.tsx,364,options,medium,true,
common.australia,"Australia","[需要翻译: Australia]","<SelectItem value=""AU"">Australia</SelectItem>",app/company-profile/page.tsx,366,options,medium,true,
common.textile_apparel,"Textile & Apparel","[需要翻译: Textile & Apparel]","<SelectItem value=""textile"">Textile & Apparel</SelectItem>",app/company-profile/page.tsx,466,options,medium,true,
common.electronics,"Electronics","[需要翻译: Electronics]","<SelectItem value=""electronics"">Electronics</SelectItem>",app/company-profile/page.tsx,467,options,medium,true,
common.automotive,"Automotive","[需要翻译: Automotive]","<SelectItem value=""automotive"">Automotive</SelectItem>",app/company-profile/page.tsx,468,options,medium,true,
common.food_beverage,"Food & Beverage","[需要翻译: Food & Beverage]","<SelectItem value=""food"">Food & Beverage</SelectItem>",app/company-profile/page.tsx,469,options,medium,true,
common.chemicals,"Chemicals","[需要翻译: Chemicals]","<SelectItem value=""chemicals"">Chemicals</SelectItem>",app/company-profile/page.tsx,470,options,medium,true,
common.machinery,"Machinery","[需要翻译: Machinery]","<SelectItem value=""machinery"">Machinery</SelectItem>",app/company-profile/page.tsx,471,options,medium,true,
common.furniture,"Furniture","[需要翻译: Furniture]","<SelectItem value=""furniture"">Furniture</SelectItem>",app/company-profile/page.tsx,472,options,medium,true,
common.500_employees,"500+ employees","[需要翻译: 500+ employees]","<SelectItem value=""500+"">500+ employees</SelectItem>",app/company-profile/page.tsx,513,options,medium,true,
common.cip_carriage_and_insurance_paid_to,"CIP - Carriage and Insurance Paid To","[需要翻译: CIP - Carriage and Insurance Paid To]","<SelectItem value=""CIP"">CIP - Carriage and Insurance Paid To</SelectItem>",app/company-profile/page.tsx,707,options,medium,true,
common.fas_free_alongside_ship,"FAS - Free Alongside Ship","[需要翻译: FAS - Free Alongside Ship]","<SelectItem value=""FAS"">FAS - Free Alongside Ship</SelectItem>",app/company-profile/page.tsx,711,options,medium,true,
common.cfr_cost_and_freight,"CFR - Cost and Freight","[需要翻译: CFR - Cost and Freight]","<SelectItem value=""CFR"">CFR - Cost and Freight</SelectItem>",app/company-profile/page.tsx,713,options,medium,true,
common.cif_cost_insurance_and_freight,"CIF - Cost, Insurance and Freight","[需要翻译: CIF - Cost, Insurance and Freight]","<SelectItem value=""CIF"">CIF - Cost, Insurance and Freight</SelectItem>",app/company-profile/page.tsx,714,options,medium,true,
common.prepaid,"Prepaid","[需要翻译: Prepaid]","<SelectItem value=""prepaid"">Prepaid</SelectItem>",app/company-profile/page.tsx,730,options,medium,true,
common.net_15_days,"Net 15 days","[需要翻译: Net 15 days]","<SelectItem value=""net-15"">Net 15 days</SelectItem>",app/company-profile/page.tsx,731,options,medium,true,
common.cash_on_delivery,"Cash on Delivery","Cash on 交付","<SelectItem value=""cod"">Cash on Delivery</SelectItem>",app/company-profile/page.tsx,735,options,medium,false,
common.letter_of_credit,"Letter of Credit","Letter of 贷方","<SelectItem value=""lc"">Letter of Credit</SelectItem>",app/company-profile/page.tsx,736,options,medium,false,
messages.cleanup_cancelled_confirmation_text_did_not_match,"Cleanup cancelled - confirmation text did not match","[需要翻译: Cleanup cancelled - confirmation text did not match]","toast.error(""Cleanup cancelled - confirmation text did not match""",app/admin/cleanup/page.tsx,78,messages,low,true,
messages.customer_cleanup_completed_successfully,"Customer cleanup completed successfully!","客户 cleanup completed successfully!","toast.success(""Customer cleanup completed successfully!""",app/admin/cleanup/page.tsx,54,messages,low,false,
messages.customer_cleanup_failed,"Customer cleanup failed: ","客户 cleanup failed: ","toast.error(""Customer cleanup failed: """,app/admin/cleanup/page.tsx,61,messages,low,false,
messages.database_cleanup_completed_successfully,"Database cleanup completed successfully!","[需要翻译: Database cleanup completed successfully!]","toast.success(""Database cleanup completed successfully!""",app/admin/cleanup/page.tsx,97,messages,low,true,
messages.cleanup_failed,"Cleanup failed: ","[需要翻译: Cleanup failed: ]","toast.error(""Cleanup failed: """,app/admin/cleanup/page.tsx,104,messages,low,true,
common.supplier_lead_time_updated_successfully,"Supplier lead time updated successfully","供应商 lead time updated successfully","message: ""Supplier lead time updated successfully""",app/api/planning/supplier-lead-times/[id]/route.ts,94,validation,low,false,
common.this_feature_will_be_implemented_in_the_next_iteration,"This feature will be implemented in the next iteration","[需要翻译: This feature will be implemented in the next iteration]","message: ""This feature will be implemented in the next iteration""",app/api/planning/demand-forecast/[id]/route.ts,329,validation,low,true,
common.lead_time_performance_updated_successfully,"Lead time performance updated successfully","[需要翻译: Lead time performance updated successfully]","message: ""Lead time performance updated successfully""",app/api/planning/supplier-lead-times/[id]/route.ts,210,validation,low,true,
common.detailed_performance_history_tracking_not_yet_implemented,"Detailed performance history tracking not yet implemented","[需要翻译: Detailed performance history tracking not yet implemented]","message: ""Detailed performance history tracking not yet implemented""",app/api/planning/supplier-lead-times/[id]/route.ts,281,validation,low,true,
common.market_benchmarking_functionality_not_yet_implemented,"Market benchmarking functionality not yet implemented","[需要翻译: Market benchmarking functionality not yet implemented]","message: ""Market benchmarking functionality not yet implemented""",app/api/planning/supplier-lead-times/[id]/route.ts,354,validation,low,true,
common.individual_procurement_plan_creation_not_yet_implemented,"Individual procurement plan creation not yet implemented","[需要翻译: Individual procurement plan creation not yet implemented]","message: ""Individual procurement plan creation not yet implemented""",app/api/planning/procurement/route.ts,164,validation,low,true,
common.procurement_plans_generated_successfully_from_demand_forecast,"Procurement plans generated successfully from demand forecast","[需要翻译: Procurement plans generated successfully from demand forecast]","message: ""Procurement plans generated successfully from demand forecast""",app/api/planning/procurement/generate-from-forecast/route.ts,60,validation,low,true,
common.procurement_plan_updated_successfully,"Procurement plan updated successfully","[需要翻译: Procurement plan updated successfully]","message: ""Procurement plan updated successfully""",app/api/planning/procurement/[id]/route.ts,138,validation,low,true,
common.purchase_order_creation_functionality_not_yet_implemented,"Purchase order creation functionality not yet implemented","[需要翻译: Purchase order creation functionality not yet implemented]","message: ""Purchase order creation functionality not yet implemented""",app/api/planning/procurement/[id]/route.ts,354,validation,low,true,
common.pipeline_forecast_generated_successfully,"Pipeline forecast generated successfully","[需要翻译: Pipeline forecast generated successfully]","message: ""Pipeline forecast generated successfully""",app/api/planning/demand-forecast/route.ts,208,validation,low,true,
common.demand_forecast_updated_successfully,"Demand forecast updated successfully","[需要翻译: Demand forecast updated successfully]","message: ""Demand forecast updated successfully""",app/api/planning/demand-forecast/[id]/route.ts,130,validation,low,true,
common.demand_forecast_deleted_successfully,"Demand forecast deleted successfully","[需要翻译: Demand forecast deleted successfully]","message: ""Demand forecast deleted successfully""",app/api/planning/demand-forecast/[id]/route.ts,190,validation,low,true,
common.availability_checking_not_yet_implemented,"Availability checking not yet implemented","[需要翻译: Availability checking not yet implemented]","message: ""Availability checking not yet implemented""",app/api/planning/demand-forecast/[id]/material-requirements/route.ts,80,validation,low,true,
common.procurement_plan_generation_not_yet_implemented,"Procurement plan generation not yet implemented","[需要翻译: Procurement plan generation not yet implemented]","message: ""Procurement plan generation not yet implemented""",app/api/planning/demand-forecast/[id]/material-requirements/route.ts,89,validation,low,true,
common.container_optimization_not_yet_implemented,"Container optimization not yet implemented","[需要翻译: Container optimization not yet implemented]","message: ""Container optimization not yet implemented""",app/api/planning/demand-forecast/[id]/material-requirements/route.ts,98,validation,low,true,
common.cannot_calculate_material_requirements_without_a_bom,"Cannot calculate material requirements without a BOM","[需要翻译: Cannot calculate material requirements without a BOM]","message: ""Cannot calculate material requirements without a BOM""",app/api/planning/demand-forecast/[id]/material-requirements/route.ts,119,validation,low,true,
common.procurement_plan_structure_generated_implementation_pending,"Procurement plan structure generated (implementation pending)","[需要翻译: Procurement plan structure generated (implementation pending)]","message: ""Procurement plan structure generated (implementation pending)""",app/api/planning/demand-forecast/[id]/material-requirements/route.ts,198,validation,low,true,
common.availability_checking_structure_generated_implementation_pending,"Availability checking structure generated (implementation pending)","[需要翻译: Availability checking structure generated (implementation pending)]","message: ""Availability checking structure generated (implementation pending)""",app/api/planning/demand-forecast/[id]/material-requirements/route.ts,276,validation,low,true,
common.workflow_log_entry_created,"Workflow log entry created","[需要翻译: Workflow log entry created]","message: ""Workflow log entry created""",app/api/logs/workflow/route.ts,154,validation,low,true,
common.bom_data_seeded_successfully,"BOM data seeded successfully","[需要翻译: BOM data seeded successfully]","message: ""BOM data seeded successfully""",app/api/admin/seed-bom-data/route.ts,191,validation,low,true,
common.product_and_customer_data_cleanup_completed_successfully,"Product and customer data cleanup completed successfully","[需要翻译: Product and customer data cleanup completed successfully]","message: ""Product and customer data cleanup completed successfully""",app/api/admin/cleanup-products/route.ts,110,validation,low,true,
common.customer_data_cleanup_completed_successfully,"Customer data cleanup completed successfully","客户 data cleanup completed successfully","message: ""Customer data cleanup completed successfully""",app/api/admin/cleanup-customers/route.ts,56,validation,low,false,
common.try_again,"
                      Try Again
                    ","[需要翻译: 
                      Try Again
                    ]","<Button
                      variant=""outline""
                      size=""sm""
                      onClick={loadPDFPreview}
                      className=""mt-2""
                    >
                      Try Again
                    </Button>",components/invoice-pdf-preview-modal.tsx,197,actions,high,true,
common.something_went_wrong,"Something went wrong","[需要翻译: Something went wrong]","<CardTitle className=""text-red-700"">Something went wrong</CardTitle>",components/error-boundary.tsx,61,content,medium,true,
common.page_error,"Page Error","[需要翻译: Page Error]","<CardTitle className=""text-red-700"">Page Error</CardTitle>",components/error-boundary.tsx,169,content,medium,true,
common.clear_all,"
          Clear all
        ","[需要翻译: 
          Clear all
        ]","<Button
          variant=""ghost""
          size=""sm""
          onClick={clearFilters}
          className=""h-6 px-2 text-xs""
        >
          Clear all
        </Button>",components/ui/advanced-search.tsx,362,actions,high,true,
forms.labels.approval_status,"Approval Status","Approval 状态","placeholder=""Approval Status""",components/samples/samples-filters.tsx,146,forms,high,false,
forms.labels.search_products,"Search products...","搜索 products...","placeholder=""Search products...""",components/samples/samples-filters.tsx,244,forms,high,false,
forms.labels.select_priority,"Select priority","[需要翻译: Select priority]","placeholder=""Select priority""",components/planning/procurement-plan-edit-client.tsx,261,forms,high,true,
common.revision_required,"Revision Required","[需要翻译: Revision Required]","<SelectItem value=""revision_required"">Revision Required</SelectItem>",components/samples/samples-filters.tsx,153,options,medium,true,
common.all_suppliers,"All Suppliers","All 供应商s","<SelectItem value=""all"">All Suppliers</SelectItem>",components/samples/samples-filters.tsx,261,options,medium,false,
common.all_priorities,"All Priorities","[需要翻译: All Priorities]","<SelectItem value=""all"">All Priorities</SelectItem>",components/samples/samples-filters.tsx,281,options,medium,true,
common.inactive,"Inactive","[需要翻译: Inactive]","<SelectItem value=""inactive"">Inactive</SelectItem>",components/samples/samples-filters.tsx,302,options,medium,true,
common.archived,"Archived","[需要翻译: Archived]","<SelectItem value=""archived"">Archived</SelectItem>",components/samples/samples-filters.tsx,303,options,medium,true,
common.created_date,"Created Date","创建d 日期","<SelectItem value=""created_at"">Created Date</SelectItem>",components/samples/samples-filters.tsx,368,options,medium,false,
common.name,"Name","名称","<SelectItem value=""name"">Name</SelectItem>",components/samples/samples-filters.tsx,369,options,medium,false,
common.code,"Code","[需要翻译: Code]","<SelectItem value=""code"">Code</SelectItem>",components/samples/samples-filters.tsx,370,options,medium,true,
common.sample_date,"Sample Date","Sample 日期","<SelectItem value=""date"">Sample Date</SelectItem>",components/samples/samples-filters.tsx,371,options,medium,false,
common.approval_status,"Approval Status","Approval 状态","<SelectItem value=""approval_status"">Approval Status</SelectItem>",components/samples/samples-filters.tsx,372,options,medium,false,
common.newest_first,"Newest First","[需要翻译: Newest First]","<SelectItem value=""desc"">Newest First</SelectItem>",components/samples/samples-filters.tsx,385,options,medium,true,
common.oldest_first,"Oldest First","[需要翻译: Oldest First]","<SelectItem value=""asc"">Oldest First</SelectItem>",components/samples/samples-filters.tsx,386,options,medium,true,
common.forecast_profitability,"Forecast Profitability","Forecast 利润ability","<CardTitle className=""text-sm font-medium"">Forecast Profitability</CardTitle>",components/planning/profit-margin-display.tsx,255,content,medium,false,
forms.labels.search_materials_suppliers_or_skus,"Search materials, suppliers, or SKUs...","搜索 materials, suppliers, or SKUs...","placeholder=""Search materials, suppliers, or SKUs...""",components/planning/procurement-planning-table.tsx,285,forms,high,false,
common.handlebulkactionapprove_approve_selected," handleBulkAction(""approve"")}>
                Approve Selected
              "," handleBulkAction(""approve"")}>
                批准 Selected
              ","<Button size=""sm"" variant=""outline"" onClick={() => handleBulkAction(""approve"")}>
                Approve Selected
              </Button>",components/planning/procurement-planning-table.tsx,328,actions,high,false,
common.setselecteditems_clear_selection," setSelectedItems([])}>
                Clear Selection
              ","[需要翻译:  setSelectedItems([])}>
                Clear Selection
              ]","<Button size=""sm"" variant=""outline"" onClick={() => setSelectedItems([])}>
                Clear Selection
              </Button>",components/planning/procurement-planning-table.tsx,335,actions,high,true,
tables.supplier,"Supplier","供应商","<TableHead>Supplier</TableHead>",components/planning/procurement-planning-table.tsx,374,tables,medium,false,
tables.priority,"Priority","[需要翻译: Priority]","<TableHead>Priority</TableHead>",components/planning/procurement-planning-table.tsx,381,tables,medium,true,
common.ordered,"Ordered","[需要翻译: Ordered]","<SelectItem value=""ordered"">Ordered</SelectItem>",components/planning/procurement-plan-edit-client.tsx,291,options,medium,true,
common.received,"Received","[需要翻译: Received]","<SelectItem value=""received"">Received</SelectItem>",components/planning/procurement-plan-edit-client.tsx,292,options,medium,true,
common.all_priority,"All Priority","[需要翻译: All Priority]","<SelectItem value=""all"">All Priority</SelectItem>",components/planning/procurement-planning-table.tsx,310,options,medium,true,
forms.labels.material_name,"Material Name","Material 名称","<Label>Material Name</Label>",components/planning/procurement-plan-edit-client.tsx,192,forms,high,false,
forms.labels.material_sku,"Material SKU","[需要翻译: Material SKU]","<Label>Material SKU</Label>",components/planning/procurement-plan-edit-client.tsx,201,forms,high,true,
forms.labels.estimated_cost,"Estimated Cost","[需要翻译: Estimated Cost]","<Label>Estimated Cost</Label>",components/planning/procurement-plan-edit-client.tsx,335,forms,high,true,
forms.labels.estimated_lead_time,"Estimated Lead Time","Estimated 交货期","<Label>Estimated Lead Time</Label>",components/planning/procurement-plan-edit-client.tsx,344,forms,high,false,
forms.labels.select_supplier_optional,"Select supplier (optional)","[需要翻译: Select supplier (optional)]","placeholder=""Select supplier (optional)""",components/planning/procurement-plan-edit-client.tsx,313,forms,high,true,
forms.labels.add_any_additional_notes_requirements_or_status_updates,"Add any additional notes, requirements, or status updates...","[需要翻译: Add any additional notes, requirements, or status updates...]","placeholder=""Add any additional notes, requirements, or status updates...""",components/planning/procurement-plan-edit-client.tsx,362,forms,high,true,
common.plan_details,"Plan Details","[需要翻译: Plan Details]","<CardTitle>Plan Details</CardTitle>",components/planning/procurement-plan-edit-client.tsx,185,content,medium,true,
common.no_supplier_assigned,"No supplier assigned","[需要翻译: No supplier assigned]","<SelectItem value="""">No supplier assigned</SelectItem>",components/planning/procurement-plan-edit-client.tsx,316,options,medium,true,
common.premium_fabric_mills_co,"Premium Fabric Mills Co","[需要翻译: Premium Fabric Mills Co]","<SelectItem value=""supplier-1"">Premium Fabric Mills Co</SelectItem>",components/planning/procurement-plan-edit-client.tsx,318,options,medium,true,
common.global_textile_suppliers,"Global Textile Suppliers","Global Textile 供应商s","<SelectItem value=""supplier-2"">Global Textile Suppliers</SelectItem>",components/planning/procurement-plan-edit-client.tsx,319,options,medium,false,
common.asia_pacific_materials,"Asia Pacific Materials","[需要翻译: Asia Pacific Materials]","<SelectItem value=""supplier-3"">Asia Pacific Materials</SelectItem>",components/planning/procurement-plan-edit-client.tsx,320,options,medium,true,
forms.labels.product,"Product","[需要翻译: Product]","<FormLabel>Product</FormLabel>",components/planning/demand-forecast-form.tsx,236,forms,high,true,
forms.labels.supplier_optional,"Supplier (Optional)","供应商 (Optional)","<FormLabel>Supplier (Optional)</FormLabel>",components/planning/demand-forecast-form.tsx,268,forms,high,false,
forms.labels.forecast_period,"Forecast Period","[需要翻译: Forecast Period]","<FormLabel>Forecast Period</FormLabel>",components/planning/demand-forecast-form.tsx,321,forms,high,true,
forms.labels.forecasted_demand,"Forecasted Demand","[需要翻译: Forecasted Demand]","<FormLabel>Forecasted Demand</FormLabel>",components/planning/demand-forecast-form.tsx,346,forms,high,true,
forms.labels.confidence_level,"Confidence Level","[需要翻译: Confidence Level]","<FormLabel>Confidence Level</FormLabel>",components/planning/demand-forecast-form.tsx,369,forms,high,true,
forms.labels.forecast_method,"Forecast Method","[需要翻译: Forecast Method]","<FormLabel>Forecast Method</FormLabel>",components/planning/demand-forecast-form.tsx,407,forms,high,true,
forms.labels.seasonality_adjustment,"Seasonality Adjustment","[需要翻译: Seasonality Adjustment]","<FormLabel>Seasonality Adjustment</FormLabel>",components/planning/demand-forecast-form.tsx,446,forms,high,true,
forms.labels.trend_factor,"Trend Factor","[需要翻译: Trend Factor]","<FormLabel>Trend Factor</FormLabel>",components/planning/demand-forecast-form.tsx,468,forms,high,true,
forms.labels.search_and_select_a_product,"Search and select a product...","搜索 and select a product...","placeholder=""Search and select a product...""",components/planning/demand-forecast-form.tsx,242,forms,high,false,
forms.labels.autoselect_best_supplier,"Auto-select best supplier","[需要翻译: Auto-select best supplier]","placeholder=""Auto-select best supplier""",components/planning/demand-forecast-form.tsx,272,forms,high,true,
forms.labels.select_period,"Select period","[需要翻译: Select period]","placeholder=""Select period""",components/planning/demand-forecast-form.tsx,325,forms,high,true,
forms.labels.enter_quantity,"Enter quantity","[需要翻译: Enter quantity]","placeholder=""Enter quantity""",components/planning/demand-forecast-form.tsx,350,forms,high,true,
forms.labels.10,"1.0","[需要翻译: 1.0]","placeholder=""1.0""",components/planning/demand-forecast-form.tsx,473,forms,high,true,
forms.labels.add_any_additional_notes_or_assumptions,"Add any additional notes or assumptions...","[需要翻译: Add any additional notes or assumptions...]","placeholder=""Add any additional notes or assumptions...""",components/planning/demand-forecast-form.tsx,502,forms,high,true,
common.cancel,"
              Cancel
            ","
              取消
            ","<Button
              type=""button""
              variant=""outline""
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>",components/planning/demand-forecast-form.tsx,520,actions,high,false,
common.additional_notes,"Additional Notes","Additional 备注","<CardTitle>Additional Notes</CardTitle>",components/planning/demand-forecast-form.tsx,492,content,medium,false,
common.no_adjustment,"No Adjustment","[需要翻译: No Adjustment]","<SelectItem value=""false"">No Adjustment</SelectItem>",components/planning/demand-forecast-form.tsx,454,options,medium,true,
common.apply_seasonality,"Apply Seasonality","[需要翻译: Apply Seasonality]","<SelectItem value=""true"">Apply Seasonality</SelectItem>",components/planning/demand-forecast-form.tsx,455,options,medium,true,
common.setshowpreviewfalse_close," setShowPreview(false)}
                  >
                    Close
                  ","[需要翻译:  setShowPreview(false)}
                  >
                    Close
                  ]","<Button
                    variant=""outline""
                    onClick={() => setShowPreview(false)}
                  >
                    Close
                  </Button>",components/forms/template-select.tsx,180,actions,high,true,
forms.labels.supplier_name,"Supplier Name *","供应商 名称 *","<FormLabel>Supplier Name *</FormLabel>",components/forms/supplier-select.tsx,158,forms,high,false,
forms.labels.enter_supplier_name,"Enter supplier name","[需要翻译: Enter supplier name]","placeholder=""Enter supplier name""",components/forms/supplier-select.tsx,160,forms,high,true,
forms.labels.contact_person_name,"Contact person name","联系人 person name","placeholder=""Contact person name""",components/forms/supplier-select.tsx,174,forms,high,false,
forms.labels.suppliercompanycom,"<EMAIL>","[需要翻译: <EMAIL>]","placeholder=""<EMAIL>""",components/forms/supplier-select.tsx,190,forms,high,true,
forms.labels.supplier_address,"Supplier address","供应商 address","placeholder=""Supplier address""",components/forms/supplier-select.tsx,220,forms,high,false,
messages.supplier_added_successfully,"Supplier added successfully!","供应商 added successfully!","toast.success(""Supplier added successfully!""",components/forms/supplier-select.tsx,97,messages,low,false,
forms.labels.product_name,"Product Name *","Product 名称 *","<FormLabel>Product Name *</FormLabel>",components/forms/product-select.tsx,206,forms,high,false,
forms.labels.price,"Price","价格","<FormLabel>Price</FormLabel>",components/forms/product-select.tsx,248,forms,high,false,
forms.labels.description,"Description","描述","<FormLabel>Description</FormLabel>",components/forms/product-select.tsx,283,forms,high,false,
forms.labels.enter_product_name,"Enter product name","[需要翻译: Enter product name]","placeholder=""Enter product name""",components/forms/product-select.tsx,208,forms,high,true,
forms.labels.eg_silk001,"e.g., SILK-001","[需要翻译: e.g., SILK-001]","placeholder=""e.g., SILK-001""",components/forms/product-select.tsx,222,forms,high,true,
forms.labels.eg_textiles_electronics,"e.g., Textiles, Electronics","[需要翻译: e.g., Textiles, Electronics]","placeholder=""e.g., Textiles, Electronics""",components/forms/product-select.tsx,270,forms,high,true,
forms.labels.product_description_and_specifications,"Product description and specifications","[需要翻译: Product description and specifications]","placeholder=""Product description and specifications""",components/forms/product-select.tsx,286,forms,high,true,
messages.product_added_successfully,"Product added successfully!","[需要翻译: Product added successfully!]","toast.success(""Product added successfully!""",components/forms/product-select.tsx,111,messages,low,true,
forms.labels.enter_customer_name,"Enter customer name","[需要翻译: Enter customer name]","placeholder=""Enter customer name""",components/forms/customer-select.tsx,157,forms,high,true,
forms.labels.customercompanycom,"<EMAIL>","[需要翻译: <EMAIL>]","placeholder=""<EMAIL>""",components/forms/customer-select.tsx,173,forms,high,true,
forms.labels.customer_address,"Customer address","客户 address","placeholder=""Customer address""",components/forms/customer-select.tsx,203,forms,high,false,
messages.customer_added_successfully,"Customer added successfully!","客户 added successfully!","toast.success(""Customer added successfully!""",components/forms/customer-select.tsx,94,messages,low,false,
forms.labels.date_from,"Date From","日期 From","<Label htmlFor=""date-from"">Date From</Label>",components/analytics/pricing-analytics-dashboard.tsx,257,forms,high,false,
forms.labels.date_to,"Date To","日期 To","<Label htmlFor=""date-to"">Date To</Label>",components/analytics/pricing-analytics-dashboard.tsx,266,forms,high,false,
common.apply_filters,"
                Apply Filters
              ","
                Apply 筛选s
              ","<Button onClick={loadAnalyticsData} disabled={loading}>
                Apply Filters
              </Button>",components/analytics/pricing-analytics-dashboard.tsx,288,actions,high,false,
common.filters,"Filters","筛选s","<CardTitle>Filters</CardTitle>",components/analytics/pricing-analytics-dashboard.tsx,252,content,medium,false,
common.total_revenue,"Total Revenue","总计 收入","<CardTitle className=""text-sm font-medium"">Total Revenue</CardTitle>",components/analytics/pricing-analytics-dashboard.tsx,300,content,medium,false,
common.total_margin,"Total Margin","总计 Margin","<CardTitle className=""text-sm font-medium"">Total Margin</CardTitle>",components/analytics/pricing-analytics-dashboard.tsx,315,content,medium,false,
common.top_method,"Top Method","[需要翻译: Top Method]","<CardTitle className=""text-sm font-medium"">Top Method</CardTitle>",components/analytics/pricing-analytics-dashboard.tsx,330,content,medium,true,
common.optimization,"Optimization","[需要翻译: Optimization]","<CardTitle className=""text-sm font-medium"">Optimization</CardTitle>",components/analytics/pricing-analytics-dashboard.tsx,345,content,medium,true,
common.contract_margin_analysis,"Contract Margin Analysis","[需要翻译: Contract Margin Analysis]","<CardTitle>Contract Margin Analysis</CardTitle>",components/analytics/pricing-analytics-dashboard.tsx,371,content,medium,true,
common.method_performance,"Method Performance","Method 性能","<CardTitle>Method Performance</CardTitle>",components/analytics/pricing-analytics-dashboard.tsx,406,content,medium,false,
common.method_details,"Method Details","[需要翻译: Method Details]","<CardTitle>Method Details</CardTitle>",components/analytics/pricing-analytics-dashboard.tsx,433,content,medium,true,
common.cost_tracking_report,"Cost Tracking Report","[需要翻译: Cost Tracking Report]","<CardTitle>Cost Tracking Report</CardTitle>",components/analytics/pricing-analytics-dashboard.tsx,464,content,medium,true,