[{"key": "app.name", "english": "FC-CHINA", "chinese": "FC-CHINA", "category": "app", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.overview", "english": "Overview", "chinese": "总览", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.master-data", "english": "Master Data", "chinese": "主数据", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.sales-purchasing", "english": "Sales & Purchasing", "chinese": "销售采购", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.sales-process", "english": "Sales Process", "chinese": "销售流程", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.production", "english": "Production", "chinese": "生产管理", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.production-planning", "english": "Production Planning", "chinese": "生产计划", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.inventory-logistics", "english": "Inventory & Logistics", "chinese": "库存物流", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.quality-inventory", "english": "Quality & Inventory", "chinese": "质量库存", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.shipping-export", "english": "Shipping & Export", "chinese": "运输出口", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.export-trade", "english": "Export & Trade", "chinese": "出口贸易", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.finance-reporting", "english": "Finance & Reporting", "chinese": "财务报表", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.settings", "english": "Settings", "chinese": "设置", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.group.administration", "english": "Administration", "chinese": "系统管理", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.dashboard", "english": "Dashboard", "chinese": "仪表盘", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.customers", "english": "Customers", "chinese": "客户管理", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.suppliers", "english": "Suppliers", "chinese": "供应商", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.products", "english": "Products", "chinese": "产品管理", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.samples", "english": "<PERSON><PERSON>", "chinese": "样品管理", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.sales-contracts", "english": "Sales Contracts", "chinese": "销售合同", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.purchase-contracts", "english": "Purchase Contracts", "chinese": "采购合同", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.contract-templates", "english": "Contract Templates", "chinese": "合同模板", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.work-orders", "english": "Work Orders", "chinese": "生产工单", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.bom-management", "english": "Bill of Materials", "chinese": "物料清单", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.quality-control", "english": "Quality Control", "chinese": "质量控制", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.mrp-planning", "english": "MRP Planning", "chinese": "MRP计划", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.inventory", "english": "Inventory", "chinese": "库存管理", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.raw-materials", "english": "Raw Materials", "chinese": "原材料", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.locations", "english": "Locations", "chinese": "仓库位置", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.shipping", "english": "Shipping", "chinese": "物流运输", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.export-declarations", "english": "Export Declarations", "chinese": "出口报关", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.trade-compliance", "english": "Trade Compliance", "chinese": "贸易合规", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.documentation", "english": "Documentation", "chinese": "文档管理", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.accounting", "english": "Accounting", "chinese": "财务会计", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.financial-dashboard", "english": "Financial Dashboard", "chinese": "财务仪表盘", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.accounts-receivable", "english": "Accounts Receivable", "chinese": "应收账款", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.accounts-payable", "english": "Accounts Payable", "chinese": "应付账款", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.reports", "english": "Reports", "chinese": "报表分析", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "nav.item.company-profile", "english": "Company Profile", "chinese": "公司资料", "category": "nav", "hasTranslation": true, "needsReview": false}, {"key": "cmd.placeholder", "english": "Search or jump to...", "chinese": "搜索或快速跳转...", "category": "cmd", "hasTranslation": true, "needsReview": false}, {"key": "home.title", "english": "Export Manufacturing ERP", "chinese": "外贸制造 ERP", "category": "home", "hasTranslation": true, "needsReview": false}, {"key": "home.subtitle", "english": "One place to manage master data, contracts, production, inventory, export declarations, and finance.", "chinese": "统一管理主数据、合同、生产、库存、出口报关与财务。", "category": "home", "hasTranslation": true, "needsReview": false}, {"key": "home.quick.master", "english": "Add Master Data", "chinese": "添加主数据", "category": "home", "hasTranslation": true, "needsReview": false}, {"key": "home.quick.contract", "english": "Create Contract", "chinese": "创建合同", "category": "home", "hasTranslation": true, "needsReview": false}, {"key": "home.quick.stock", "english": "Record Stock", "chinese": "记录出入库", "category": "home", "hasTranslation": true, "needsReview": false}, {"key": "home.quick.declaration", "english": "New Declaration", "chinese": "新建报关单", "category": "home", "hasTranslation": true, "needsReview": false}, {"key": "kpi.customers", "english": "Customers", "chinese": "客户数", "category": "kpi", "hasTranslation": true, "needsReview": false}, {"key": "kpi.products", "english": "Products", "chinese": "产品数", "category": "kpi", "hasTranslation": true, "needsReview": false}, {"key": "kpi.suppliers", "english": "Suppliers", "chinese": "供应商", "category": "kpi", "hasTranslation": true, "needsReview": false}, {"key": "kpi.contracts", "english": "Contracts", "chinese": "合同", "category": "kpi", "hasTranslation": true, "needsReview": false}, {"key": "kpi.suppliers.desc", "english": "Active suppliers", "chinese": "活跃供应商", "category": "kpi", "hasTranslation": true, "needsReview": false}, {"key": "kpi.contracts.desc", "english": "Sales & purchase contracts", "chinese": "销售和采购合同", "category": "kpi", "hasTranslation": true, "needsReview": false}, {"key": "kpi.onhand", "english": "On-hand Stock (sum)", "chinese": "在库数量（合计）", "category": "kpi", "hasTranslation": true, "needsReview": false}, {"key": "kpi.openWos", "english": "Open Work Orders", "chinese": "未完成工单", "category": "kpi", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.quick_actions.title", "english": "Quick Actions", "chinese": "快速操作", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.quick_actions.subtitle", "english": "Common tasks to get you started", "chinese": "常用任务帮助您快速开始", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.quick_actions.manage_customers", "english": "Manage Customers", "chinese": "管理客户", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.quick_actions.view_products", "english": "View Products", "chinese": "查看产品", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.quick_actions.create_contract", "english": "Create Sales Contract", "chinese": "创建销售合同", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.quick_actions.update_profile", "english": "Update Company Profile", "chinese": "更新公司资料", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.title", "english": "System Status", "chinese": "系统状态", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.subtitle", "english": "Your ERP system setup progress", "chinese": "您的ERP系统设置进度", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.company_profile", "english": "Company Profile", "chinese": "公司资料", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.customer_database", "english": "Customer Database", "chinese": "客户数据库", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.product_catalog", "english": "Product Catalog", "chinese": "产品目录", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.first_contract", "english": "First Sales Contract", "chinese": "首个销售合同", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.inventory_setup", "english": "Inventory Setup", "chinese": "库存设置", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.complete", "english": "Complete", "chinese": "完成", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.active", "english": "Active", "chinese": "活跃", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.ready", "english": "Ready", "chinese": "就绪", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.system_status.pending", "english": "Pending", "chinese": "待处理", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.getting_started.title", "english": "🚀 Getting Started", "chinese": "🚀 快速开始", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.getting_started.subtitle", "english": "Complete these steps to get the most out of your Manufacturing ERP system", "chinese": "完成这些步骤以充分利用您的制造业ERP系统", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.getting_started.step1.title", "english": "1. Company Setup", "chinese": "1. 公司设置", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.getting_started.step1.desc", "english": "Your company profile is complete and ready for business.", "chinese": "您的公司资料已完成，可以开始业务。", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.getting_started.step2.title", "english": "2. Create Your First Contract", "chinese": "2. 创建您的第一个合同", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.getting_started.step2.desc", "english": "Start generating revenue by creating your first sales contract.", "chinese": "通过创建您的第一个销售合同开始创收。", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.getting_started.step2.action", "english": "Create Contract", "chinese": "创建合同", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.getting_started.step3.title", "english": "3. Set Up Inventory", "chinese": "3. 设置库存", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.getting_started.step3.desc", "english": "Track your raw materials and finished goods inventory.", "chinese": "跟踪您的原材料和成品库存。", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.getting_started.step3.action", "english": "Setup Inventory", "chinese": "设置库存", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "sample.title", "english": "Sample Data", "chinese": "示例数据", "category": "sample", "hasTranslation": true, "needsReview": false}, {"key": "sample.desc", "english": "This workspace includes realistic sample data to demonstrate relationships across modules.", "chinese": "此工作区内置了贴近真实的示例数据，便于理解各模块之间的关联。", "category": "sample", "hasTranslation": true, "needsReview": false}, {"key": "sample.reset", "english": "Reset sample data", "chinese": "重置示例数据", "category": "sample", "hasTranslation": true, "needsReview": false}, {"key": "sample.clear", "english": "Clear sample data", "chinese": "清空示例数据", "category": "sample", "hasTranslation": true, "needsReview": false}, {"key": "alert.db.title", "english": "Database not configured", "chinese": "数据库未配置", "category": "alert", "hasTranslation": true, "needsReview": false}, {"key": "alert.db.desc", "english": "Set the DATABASE_URL (Neon Postgres). The app runs a safe, one-time auto-migration at first load and seeds a minimal dataset. You can also run scripts/sql/001_init.sql and 002_seed.sql manually.", "chinese": "请设置 DATABASE_URL（Neon Postgres）。应用将在首次加载时安全地自动迁移并种子最小数据。你也可以手动执行 scripts/sql/001_init.sql 和 002_seed.sql。", "category": "alert", "hasTranslation": true, "needsReview": false}, {"key": "alert.prep.title", "english": "Preparing your workspace", "chinese": "正在准备工作区", "category": "alert", "hasTranslation": true, "needsReview": false}, {"key": "alert.prep.desc", "english": "The schema is initializing. Refresh in a few seconds.", "chinese": "正在初始化数据库结构，请稍后刷新。", "category": "alert", "hasTranslation": true, "needsReview": false}, {"key": "field.code", "english": "Code", "chinese": "编码", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.name", "english": "Name", "chinese": "名称", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.spec", "english": "Specification", "chinese": "规格", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.moq", "english": "MOQ", "chinese": "起订量", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.inStock", "english": "In Stock", "chinese": "现货", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.contact", "english": "Contact", "chinese": "联系人", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.incoterm", "english": "Incoterm", "chinese": "贸易术语", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.paymentTerm", "english": "Payment Term", "chinese": "付款条款", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.address", "english": "Address", "chinese": "地址", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.sku", "english": "SKU", "chinese": "SKU", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.unit", "english": "Unit", "chinese": "单位", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.hsCode", "english": "HS Code", "chinese": "HS 编码", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.origin", "english": "Origin", "chinese": "原产地", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.packaging", "english": "Packaging", "chinese": "包装", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.number", "english": "Number", "chinese": "编号", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.customer", "english": "Customer", "chinese": "客户", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.supplier", "english": "Supplier", "chinese": "供应商", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.product", "english": "Product", "chinese": "产品", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.qty", "english": "Quantity", "chinese": "数量", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.price", "english": "Price", "chinese": "价格", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.total", "english": "Total", "chinese": "总计", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.woNumber", "english": "WO Number", "chinese": "工单号", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.salesContract", "english": "Sales Contract", "chinese": "销售合同", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.location", "english": "Location", "chinese": "位置", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.note", "english": "Note", "chinese": "备注", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.reference", "english": "Reference", "chinese": "参考", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.declarationNo", "english": "Declaration No.", "chinese": "报关单号", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.amount", "english": "Amount", "chinese": "金额", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.received", "english": "Received", "chinese": "已收", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.paid", "english": "Paid", "chinese": "已付", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.invoiceNo", "english": "Invoice No.", "chinese": "发票号", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "table.actions", "english": "Actions", "chinese": "操作", "category": "table", "hasTranslation": true, "needsReview": false}, {"key": "table.noData", "english": "No data available.", "chinese": "暂无数据", "category": "table", "hasTranslation": true, "needsReview": false}, {"key": "action.add", "english": "Add", "chinese": "添加", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.addItem", "english": "Add Item", "chinese": "添加明细", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.remove", "english": "Remove", "chinese": "移除", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.delete", "english": "Delete", "chinese": "删除", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.create", "english": "Create", "chinese": "创建", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.createContract", "english": "Create Contract", "chinese": "创建合同", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.createPO", "english": "Create PO", "chinese": "创建采购单", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.createWO", "english": "Create WO", "chinese": "创建工单", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.addInbound", "english": "Receive", "chinese": "收货", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.addOutbound", "english": "Ship", "chinese": "发货", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.createDeclaration", "english": "Create Declaration", "chinese": "创建报关单", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.submit", "english": "Submit", "chinese": "提交", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.createAR", "english": "Create AR", "chinese": "创建应收", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "action.createAP", "english": "Create AP", "chinese": "创建应付", "category": "action", "hasTranslation": true, "needsReview": false}, {"key": "cta.open", "english": "Open", "chinese": "进入", "category": "cta", "hasTranslation": true, "needsReview": false}, {"key": "basic.samples.title", "english": "Sample Management Center", "chinese": "样品中心", "category": "basic", "hasTranslation": true, "needsReview": false}, {"key": "basic.samples.desc", "english": "Code, name, specs, MOQ, available from stock.", "chinese": "编码、名称、规格、起订量、是否现货。", "category": "basic", "hasTranslation": true, "needsReview": false}, {"key": "basic.customers.title", "english": "Customers", "chinese": "客户", "category": "basic", "hasTranslation": true, "needsReview": false}, {"key": "basic.customers.desc", "english": "Store customer profiles and trading terms.", "chinese": "客户档案与贸易条款。", "category": "basic", "hasTranslation": true, "needsReview": false}, {"key": "basic.suppliers.title", "english": "Suppliers", "chinese": "供应商", "category": "basic", "hasTranslation": true, "needsReview": false}, {"key": "basic.suppliers.desc", "english": "Approved suppliers and contacts.", "chinese": "合格供应商与联系方式。", "category": "basic", "hasTranslation": true, "needsReview": false}, {"key": "basic.products.title", "english": "Products/SKUs", "chinese": "产品/SKU", "category": "basic", "hasTranslation": true, "needsReview": false}, {"key": "basic.products.desc", "english": "Units, HS codes, origin, packaging.", "chinese": "单位、HS 编码、原产地、包装。", "category": "basic", "hasTranslation": true, "needsReview": false}, {"key": "contracts.sales.title", "english": "Sales Contracts", "chinese": "销售合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.sales.desc", "english": "Create basic sales contracts from products and customers.", "chinese": "根据产品与客户创建销售合同。", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.sales.empty", "english": "No sales contracts.", "chinese": "暂无销售合同。", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.purchase.title", "english": "Purchase Contracts", "chinese": "采购合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.purchase.desc", "english": "Issue POs against suppliers.", "chinese": "向供应商下达采购订单。", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.purchase.empty", "english": "No purchase contracts.", "chinese": "暂无采购合同。", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "production.title", "english": "Work Orders", "chinese": "生产工单", "category": "production", "hasTranslation": true, "needsReview": false}, {"key": "production.desc", "english": "Track routing and operation progress.", "chinese": "跟踪工序进度与路由。", "category": "production", "hasTranslation": true, "needsReview": false}, {"key": "production.empty", "english": "No work orders.", "chinese": "暂无工单。", "category": "production", "hasTranslation": true, "needsReview": false}, {"key": "inventory.inbound.title", "english": "Inbound", "chinese": "入库", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.inbound.desc", "english": "Receive inventory", "chinese": "接收库存", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.outbound.title", "english": "Outbound", "chinese": "出库", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.outbound.desc", "english": "Ship inventory", "chinese": "发货库存", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.stock.title", "english": "Stock", "chinese": "库存", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.stock.desc", "english": "Current inventory levels", "chinese": "当前库存水平", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.stock.empty", "english": "No stock lots.", "chinese": "暂无库存批次。", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.txns.title", "english": "Stock Transactions", "chinese": "库存交易", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.txns.desc", "english": "FIFO is applied when shipping.", "chinese": "出库时应用先进先出（FIFO）。", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.txns.empty", "english": "No transactions.", "chinese": "暂无交易记录。", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "export.title", "english": "Export Declarations", "chinese": "出口报关", "category": "export", "hasTranslation": true, "needsReview": false}, {"key": "export.desc", "english": "Validate HS codes and track submission.", "chinese": "校验 HS 编码并跟踪报关状态。", "category": "export", "hasTranslation": true, "needsReview": false}, {"key": "export.empty", "english": "No declarations.", "chinese": "暂无报关单。", "category": "export", "hasTranslation": true, "needsReview": false}, {"key": "finance.title", "english": "Finance & Accounting", "chinese": "财务会计", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.description", "english": "Manage invoices, payments, and financial reporting", "chinese": "管理发票、付款和财务报表", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.summary.totalAR", "english": "Total Receivables", "chinese": "应收总额", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.summary.outstandingAR", "english": "Outstanding AR", "chinese": "未收应收", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.summary.totalAP", "english": "Total Payables", "chinese": "应付总额", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.summary.netCashFlow", "english": "Net Cash Flow", "chinese": "净现金流", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.coreMetrics", "english": "Core Financial Metrics", "chinese": "核心财务指标", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.totalRevenue", "english": "Total Revenue (YTD)", "chinese": "总收入（年度）", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.totalExpenses", "english": "Total Expenses (YTD)", "chinese": "总支出（年度）", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.profitLoss", "english": "Profit/Loss (YTD)", "chinese": "损益（年度）", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.netCashFlow", "english": "Net Cash Flow", "chinese": "净现金流", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.overdueIntelligence", "english": "Overdue Intelligence", "chinese": "逾期智能分析", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.overdueAR", "english": "Overdue Receivables", "chinese": "逾期应收", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.overdueAP", "english": "Overdue Payables", "chinese": "逾期应付", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.manufacturingIntelligence", "english": "Manufacturing Intelligence", "chinese": "制造业智能分析", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.contractProfitability", "english": "Contract Profitability", "chinese": "合同盈利能力", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.avgCollectionDays", "english": "Avg Collection Days", "chinese": "平均收款天数", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.manufacturingMargin", "english": "Manufacturing Margin", "chinese": "制造业利润率", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.kpis.activeContracts", "english": "Active Contracts", "chinese": "活跃合同", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.title", "english": "Accounts Receivable", "chinese": "应收账款", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.description", "english": "Track AR invoices and aging with contract integration", "chinese": "跟踪应收发票和账龄，集成合同管理", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.invoiceNumber", "english": "Invoice Number", "chinese": "发票号码", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.customer", "english": "Customer", "chinese": "客户", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.salesContract", "english": "Sales Contract", "chinese": "销售合同", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.amount", "english": "Amount", "chinese": "金额", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.received", "english": "Received", "chinese": "已收款", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.status", "english": "Status", "chinese": "状态", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.invoiceDate", "english": "Invoice Date", "chinese": "发票日期", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.dueDate", "english": "Due Date", "chinese": "到期日期", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.paymentTerms", "english": "Payment Terms", "chinese": "付款条件", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.aging", "english": "Aging", "chinese": "账龄", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.contract", "english": "Contract", "chinese": "合同", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.createInvoice", "english": "Create AR Invoice", "chinese": "创建应收发票", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.noInvoices", "english": "No AR invoices found", "chinese": "未找到应收发票", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.title", "english": "Accounts Payable", "chinese": "应付账款", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.description", "english": "Track AP invoices and payments with contract integration", "chinese": "跟踪应付发票和付款，集成合同管理", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.invoiceNumber", "english": "Invoice Number", "chinese": "发票号码", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.supplier", "english": "Supplier", "chinese": "供应商", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.purchaseContract", "english": "Purchase Contract", "chinese": "采购合同", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.amount", "english": "Amount", "chinese": "金额", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.paid", "english": "Paid", "chinese": "已付款", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.status", "english": "Status", "chinese": "状态", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.invoiceDate", "english": "Invoice Date", "chinese": "发票日期", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.dueDate", "english": "Due Date", "chinese": "到期日期", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.paymentTerms", "english": "Payment Terms", "chinese": "付款条件", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.aging", "english": "Aging", "chinese": "账龄", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.contract", "english": "Contract", "chinese": "合同", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.createInvoice", "english": "Create AP Invoice", "chinese": "创建应付发票", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.noInvoices", "english": "No AP invoices found", "chinese": "未找到应付发票", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.paymentTerms.tt", "english": "TT (Telegraphic Transfer)", "chinese": "TT（电汇）", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.paymentTerms.dp", "english": "DP (Documents against Payment)", "chinese": "DP（付款交单）", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.paymentTerms.lc", "english": "LC (Letter of Credit)", "chinese": "LC（信用证）", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.paymentTerms.deposit", "english": "Deposit (Advance Payment)", "chinese": "定金（预付款）", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.paymentTerms.depositTT", "english": "30% Deposit + 70% TT", "chinese": "30%定金 + 70%电汇", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.paymentTerms.depositLC", "english": "50% Deposit + 50% LC", "chinese": "50%定金 + 50%信用证", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.status.depositReceived", "english": "<PERSON><PERSON><PERSON><PERSON> Received", "chinese": "已收定金", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.status.partialPaid", "english": "Partial Paid", "chinese": "部分付款", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.status.depositPaid", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "已付定金", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.desc", "english": "Track AR and aging.", "chinese": "跟踪应收与账龄。", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ar.empty", "english": "No AR invoices.", "chinese": "暂无应收发票。", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.desc", "english": "Track AP and payments.", "chinese": "跟踪应付与付款。", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "finance.ap.empty", "english": "No AP invoices.", "chinese": "暂无应付发票。", "category": "finance", "hasTranslation": true, "needsReview": false}, {"key": "docs.plan.title", "english": "Reference Mind Maps", "chinese": "参考脑图", "category": "docs", "hasTranslation": true, "needsReview": false}, {"key": "docs.plan.desc", "english": "Original Chinese and translated English plan visuals used to guide implementation.", "chinese": "用于指导实施的中文原稿与英文译稿。", "category": "docs", "hasTranslation": true, "needsReview": false}, {"key": "module.master.title", "english": "Master Data", "chinese": "主数据", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.master.desc", "english": "Samples, customers, suppliers, products, and trading terms.", "chinese": "样品、客户、供应商、产品及贸易条款。", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.contracts.title", "english": "Contracts", "chinese": "合同", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.contracts.desc", "english": "Create sales contracts and purchase orders from SKUs.", "chinese": "基于 SKU 创建销售合同与采购订单。", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.production.title", "english": "Production", "chinese": "生产", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.production.desc", "english": "Generate work orders, track routing and QC.", "chinese": "生成工单并跟踪工序与质检。", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.inventory.title", "english": "Inventory", "chinese": "库存", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.inventory.desc", "english": "Inbound/outbound, FIFO, and current lots.", "chinese": "入库/出库、先进先出及当前库位。", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.export.title", "english": "Export", "chinese": "出口", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.export.desc", "english": "Build declarations and validate HS codes.", "chinese": "创建报关单并校验 HS 编码。", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.export.declarations", "english": "Declarations", "chinese": "报关单", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.finance.title", "english": "Finance", "chinese": "财务", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "module.finance.desc", "english": "Track receivables and payables with basic aging.", "chinese": "跟踪应收与应付及基础账龄。", "category": "module", "hasTranslation": true, "needsReview": false}, {"key": "landing.login", "english": "<PERSON><PERSON>", "chinese": "登录", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.getStarted", "english": "Get Started", "chinese": "开始使用", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.learnMore", "english": "Learn More", "chinese": "了解更多", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.badge", "english": "Trusted by 500+ Export Manufacturers", "chinese": "500+ 出口制造商信赖之选", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.hero.title", "english": "All-in-One ERP for Textile Manufacturing & Export", "chinese": "一体化纺织制造与出口 ERP", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.hero.subtitle", "english": "Streamline your textile manufacturing operations from production to export. Manage customers, products, quality control, and international trade compliance in one platform.", "chinese": "从生产到出口，简化您的纺织制造业务流程。在一个平台上管理客户、产品、质量控制和国际贸易合规。", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.noCredit", "english": "No credit card required", "chinese": "无需信用卡", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.freeTrial", "english": "30-day free trial", "chinese": "30天免费试用", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.quickSetup", "english": "Setup in minutes", "chinese": "几分钟完成设置", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.title", "english": "Everything You Need for Export Manufacturing", "chinese": "出口制造所需的一切功能", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.subtitle", "english": "From raw materials to international shipping, manage your entire manufacturing workflow", "chinese": "从原材料到国际运输，管理您的整个制造工作流程", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.crm.title", "english": "Customer & Supplier Management", "chinese": "客户与供应商管理", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.crm.description", "english": "Comprehensive CRM for managing international customers and suppliers with contact details, payment terms, and trade history.", "chinese": "全面的客户关系管理系统，管理国际客户和供应商的联系信息、付款条款和贸易历史。", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.inventory.title", "english": "Product Catalog & Inventory", "chinese": "产品目录与库存", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.inventory.description", "english": "Detailed product management with SKUs, specifications, quality standards, and real-time inventory tracking.", "chinese": "详细的产品管理，包括SKU、规格、质量标准和实时库存跟踪。", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.production.title", "english": "Production Management", "chinese": "生产管理", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.production.description", "english": "Work order management, production scheduling, and real-time tracking from cutting to packaging.", "chinese": "工单管理、生产调度，从裁剪到包装的实时跟踪。", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.quality.title", "english": "Quality Control", "chinese": "质量控制", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.quality.description", "english": "Integrated quality inspections, defect tracking, and compliance management for export standards.", "chinese": "集成质量检验、缺陷跟踪和出口标准合规管理。", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.export.title", "english": "Export Documentation", "chinese": "出口文档", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.export.description", "english": "Automated export declarations, shipping documents, and international trade compliance management.", "chinese": "自动化出口报关、运输文件和国际贸易合规管理。", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.analytics.title", "english": "Analytics & Reporting", "chinese": "分析与报告", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.features.analytics.description", "english": "Real-time dashboards, production analytics, and comprehensive reporting for business insights.", "chinese": "实时仪表板、生产分析和全面的业务洞察报告。", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.benefits.title", "english": "Why Choose Our Manufacturing ERP?", "chinese": "为什么选择我们的制造 ERP？", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.benefits.subtitle", "english": "Built specifically for export-oriented textile manufacturers", "chinese": "专为出口导向的纺织制造商打造", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.benefits.speed.title", "english": "50% Faster Operations", "chinese": "提升 50% 运营效率", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.benefits.speed.description", "english": "Streamlined workflows reduce manual work and accelerate production cycles", "chinese": "简化的工作流程减少手工作业，加速生产周期", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.benefits.compliance.title", "english": "100% Compliance", "chinese": "100% 合规保障", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.benefits.compliance.description", "english": "Built-in export compliance ensures all international trade requirements are met", "chinese": "内置出口合规功能，确保满足所有国际贸易要求", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.benefits.global.title", "english": "Global Ready", "chinese": "全球化就绪", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.benefits.global.description", "english": "Multi-currency, multi-language support for international business operations", "chinese": "多币种、多语言支持，满足国际业务运营需求", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.cta.title", "english": "Ready to Transform Your Manufacturing?", "chinese": "准备好转型您的制造业务了吗？", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.cta.subtitle", "english": "Join hundreds of manufacturers who have streamlined their operations with our ERP solution", "chinese": "加入数百家已通过我们的 ERP 解决方案简化运营的制造商", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.cta.button", "english": "Start Your Free Trial Today", "chinese": "立即开始免费试用", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.cta.features", "english": "No credit card required • 30-day free trial • Setup in minutes", "chinese": "无需信用卡 • 30天免费试用 • 几分钟完成设置", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.footer.copyright", "english": "© 2024 FC-CHINA. Built for export manufacturers worldwide.", "chinese": "© 2024 FC-CHINA. 为全球出口制造商而建。", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.hero.mock.last30days", "english": "Last 30 days", "chinese": "近30天", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "landing.hero.mock.onTimeShipments", "english": "On-Time Shipments", "chinese": "准时发货率", "category": "landing", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.loading", "english": "Loading dashboard...", "chinese": "正在加载仪表板...", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.error.title", "english": "Dashboard Error", "chinese": "仪表板错误", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.error.description", "english": "This may indicate you need to complete your company profile setup.", "chinese": "这可能表示您需要完成公司资料设置。", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.error.retry", "english": "Retry", "chinese": "重试", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.welcome", "english": "Welcome back", "chinese": "欢迎回来", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.subtitle", "english": "Here's what's happening with your manufacturing operations today.", "chinese": "这是您今天制造业务的最新情况。", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.stats.customers.title", "english": "Total Customers", "chinese": "客户总数", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.stats.customers.description", "english": "Active customer accounts", "chinese": "活跃客户账户", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.stats.products.title", "english": "Products", "chinese": "产品", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.stats.products.description", "english": "Products in catalog", "chinese": "目录中的产品", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.stats.suppliers.title", "english": "Suppliers", "chinese": "供应商", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.stats.suppliers.description", "english": "Active suppliers", "chinese": "活跃供应商", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.stats.contracts.title", "english": "Active Contracts", "chinese": "活跃合同", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "dashboard.stats.contracts.description", "english": "Sales and purchase contracts", "chinese": "销售和采购合同", "category": "dashboard", "hasTranslation": true, "needsReview": false}, {"key": "common.loading", "english": "Loading...", "chinese": "加载中...", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.error", "english": "Error", "chinese": "错误", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.success", "english": "Success", "chinese": "成功", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.cancel", "english": "Cancel", "chinese": "取消", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.save", "english": "Save", "chinese": "保存", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.delete", "english": "Delete", "chinese": "删除", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.edit", "english": "Edit", "chinese": "编辑", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.view", "english": "View", "chinese": "查看", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.add", "english": "Add", "chinese": "添加", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.create", "english": "Create", "chinese": "创建", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.update", "english": "Update", "chinese": "更新", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.search", "english": "Search", "chinese": "搜索", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.filter", "english": "Filter", "chinese": "筛选", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.actions", "english": "Actions", "chinese": "操作", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.status", "english": "Status", "chinese": "状态", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.name", "english": "Name", "chinese": "名称", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.description", "english": "Description", "chinese": "描述", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.date", "english": "Date", "chinese": "日期", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.created", "english": "Created", "chinese": "创建时间", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.updated", "english": "Updated", "chinese": "更新时间", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.active", "english": "Active", "chinese": "活跃", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.inactive", "english": "Inactive", "chinese": "非活跃", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.yes", "english": "Yes", "chinese": "是", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.no", "english": "No", "chinese": "否", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.confirm", "english": "Confirm", "chinese": "确认", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.close", "english": "Close", "chinese": "关闭", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.retry", "english": "Retry", "chinese": "重试", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "common.optional", "english": "Optional", "chinese": "可选", "category": "common", "hasTranslation": true, "needsReview": false}, {"key": "status.active", "english": "Active", "chinese": "活跃", "category": "status", "hasTranslation": true, "needsReview": false}, {"key": "status.inactive", "english": "Inactive", "chinese": "非活跃", "category": "status", "hasTranslation": true, "needsReview": false}, {"key": "status.draft", "english": "Draft", "chinese": "草稿", "category": "status", "hasTranslation": true, "needsReview": false}, {"key": "status.approved", "english": "Approved", "chinese": "已审批", "category": "status", "hasTranslation": true, "needsReview": false}, {"key": "status.pending", "english": "Pending", "chinese": "待处理", "category": "status", "hasTranslation": true, "needsReview": false}, {"key": "status.completed", "english": "Completed", "chinese": "已完成", "category": "status", "hasTranslation": true, "needsReview": false}, {"key": "status.cancelled", "english": "Cancelled", "chinese": "已取消", "category": "status", "hasTranslation": true, "needsReview": false}, {"key": "status.done", "english": "Done", "chinese": "完成", "category": "status", "hasTranslation": true, "needsReview": false}, {"key": "header.wo", "english": "WO", "chinese": "工单", "category": "header", "hasTranslation": true, "needsReview": false}, {"key": "header.operations", "english": "Operations", "chinese": "工序", "category": "header", "hasTranslation": true, "needsReview": false}, {"key": "header.type", "english": "Type", "chinese": "类型", "category": "header", "hasTranslation": true, "needsReview": false}, {"key": "header.time", "english": "Time", "chinese": "时间", "category": "header", "hasTranslation": true, "needsReview": false}, {"key": "header.lot", "english": "Lot", "chinese": "批次", "category": "header", "hasTranslation": true, "needsReview": false}, {"key": "loading.inventory", "english": "Loading inventory...", "chinese": "正在加载库存...", "category": "loading", "hasTranslation": true, "needsReview": false}, {"key": "loading.try_again", "english": "Try again", "chinese": "重试", "category": "loading", "hasTranslation": true, "needsReview": false}, {"key": "field.email", "english": "Email", "chinese": "邮箱", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.phone", "english": "Phone", "chinese": "电话", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.company", "english": "Company", "chinese": "公司", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.status", "english": "Status", "chinese": "状态", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.type", "english": "Type", "chinese": "类型", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.date", "english": "Date", "chinese": "日期", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.notes", "english": "Notes", "chinese": "备注", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.contract", "english": "Contract", "chinese": "合同", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "field.template", "english": "Template", "chinese": "模板", "category": "field", "hasTranslation": true, "needsReview": false}, {"key": "customers.title", "english": "Customers", "chinese": "客户管理", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.subtitle", "english": "Manage your customer database and relationships", "chinese": "管理您的客户数据库和关系", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.add", "english": "Add Customer", "chinese": "添加客户", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.add.title", "english": "Add New Customer", "chinese": "添加新客户", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.add.description", "english": "Create a new customer record for your business.", "chinese": "为您的业务创建新的客户记录。", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.edit.title", "english": "Edit Customer", "chinese": "编辑客户", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.edit.description", "english": "Update customer information.", "chinese": "更新客户信息。", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.delete.title", "english": "Delete Customer", "chinese": "删除客户", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.delete.description", "english": "Are you sure you want to delete this customer? This action cannot be undone.", "chinese": "您确定要删除此客户吗？此操作无法撤销。", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.form.company_name", "english": "Company Name", "chinese": "公司名称", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.form.contact_name", "english": "Contact Person", "chinese": "联系人", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.form.contact_phone", "english": "Phone Number", "chinese": "电话号码", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.form.contact_email", "english": "Email Address", "chinese": "邮箱地址", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.form.address", "english": "Address", "chinese": "地址", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.form.tax_id", "english": "Tax ID", "chinese": "税号", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.form.bank", "english": "Bank Details", "chinese": "银行信息", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.form.incoterm", "english": "Incoterm", "chinese": "贸易条款", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.form.payment_term", "english": "Payment Terms", "chinese": "付款条件", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.form.status", "english": "Status", "chinese": "状态", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.table.company_name", "english": "Company Name", "chinese": "公司名称", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.table.contact_person", "english": "Contact Person", "chinese": "联系人", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.table.phone", "english": "Phone", "chinese": "电话", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.table.email", "english": "Email", "chinese": "邮箱", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.table.address", "english": "Address", "chinese": "地址", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.table.incoterm", "english": "Incoterm", "chinese": "贸易条款", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.table.payment_terms", "english": "Payment Terms", "chinese": "付款条件", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.table.status", "english": "Status", "chinese": "状态", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.table.actions", "english": "Actions", "chinese": "操作", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.success.created", "english": "Customer created successfully!", "chinese": "客户创建成功！", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.success.updated", "english": "Customer updated successfully!", "chinese": "客户更新成功！", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.success.deleted", "english": "Customer deleted successfully!", "chinese": "客户删除成功！", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.error.create", "english": "Failed to create customer", "chinese": "创建客户失败", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.error.update", "english": "Failed to update customer", "chinese": "更新客户失败", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.error.delete", "english": "Failed to delete customer", "chinese": "删除客户失败", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.empty", "english": "No customers found", "chinese": "未找到客户", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "customers.empty.description", "english": "Get started by adding your first customer.", "chinese": "添加您的第一个客户开始使用。", "category": "customers", "hasTranslation": true, "needsReview": false}, {"key": "products.title", "english": "Products", "chinese": "产品管理", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.subtitle", "english": "Manage your product catalog and inventory", "chinese": "管理您的产品目录和库存", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.add", "english": "Add Product", "chinese": "添加产品", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.add.title", "english": "Add New Product", "chinese": "添加新产品", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.add.description", "english": "Create a new product in your catalog.", "chinese": "在您的目录中创建新产品。", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.edit.title", "english": "Edit Product", "chinese": "编辑产品", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.edit.description", "english": "Update product information.", "chinese": "更新产品信息。", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.delete.title", "english": "Delete Product", "chinese": "删除产品", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.delete.description", "english": "Are you sure you want to delete this product? This action cannot be undone.", "chinese": "您确定要删除此产品吗？此操作无法撤销。", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.name", "english": "Product Name", "chinese": "产品名称", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.sku", "english": "SKU", "chinese": "SKU", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.unit", "english": "Unit", "chinese": "单位", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.hs_code", "english": "HS Code", "chinese": "HS编码", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.origin", "english": "Origin", "chinese": "原产地", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.package", "english": "Package", "chinese": "包装", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.status", "english": "Status", "chinese": "状态", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.pricing_information", "english": "Pricing Information", "chinese": "价格信息", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.base_price", "english": "Base Price", "chinese": "基础价格", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.cost_price", "english": "Cost Price", "chinese": "成本价格", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.margin_percentage", "english": "Margin %", "chinese": "利润率 %", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.table.name", "english": "Product Name", "chinese": "产品名称", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.table.sku", "english": "SKU", "chinese": "SKU", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.table.unit", "english": "Unit", "chinese": "单位", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.table.hs_code", "english": "HS Code", "chinese": "HS编码", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.table.origin", "english": "Origin", "chinese": "原产地", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.table.package", "english": "Package", "chinese": "包装", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.table.price", "english": "Price", "chinese": "价格", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.table.status", "english": "Status", "chinese": "状态", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.table.actions", "english": "Actions", "chinese": "操作", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.success.created", "english": "Product created successfully!", "chinese": "产品创建成功！", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.success.updated", "english": "Product Updated", "chinese": "产品已更新", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.success.deleted", "english": "Product deleted successfully!", "chinese": "产品删除成功！", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.error.create", "english": "Failed to create product", "chinese": "创建产品失败", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.error.update", "english": "Update Failed", "chinese": "更新失败", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.error.delete", "english": "Failed to delete product", "chinese": "删除产品失败", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.empty", "english": "No products found", "chinese": "未找到产品", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.empty.description", "english": "Get started by adding your first product.", "chinese": "添加您的第一个产品开始使用。", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.view.title", "english": "Product Details", "chinese": "产品详情", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.view.description", "english": "View product information (read-only).", "chinese": "查看产品信息（只读）。", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.title", "english": "Sales Contracts", "chinese": "销售合同", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.subtitle", "english": "Manage your company's sales contracts.", "chinese": "管理您公司的销售合同。", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.add", "english": "Add Contract", "chinese": "添加合同", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.add.title", "english": "Create Sales Contract", "chinese": "创建销售合同", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.add.description", "english": "Create a new sales contract for your customer.", "chinese": "为您的客户创建新的销售合同。", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.edit.title", "english": "Edit Sales Contract", "chinese": "编辑销售合同", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.edit.description", "english": "Update sales contract information.", "chinese": "更新销售合同信息。", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.delete.title", "english": "Are you sure?", "chinese": "您确定吗？", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.delete.description", "english": "This will permanently delete the contract. This action cannot be undone.", "chinese": "这将永久删除该合同。此操作无法撤销。", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.form.number", "english": "Contract Number", "chinese": "合同编号", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.form.customer", "english": "Customer", "chinese": "客户", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.form.template", "english": "Template", "chinese": "模板", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.form.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.form.items", "english": "Items", "chinese": "明细", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.table.contract_number", "english": "Contract Number", "chinese": "合同编号", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.table.number", "english": "Contract #", "chinese": "合同编号", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.table.customer", "english": "Customer", "chinese": "客户", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.table.date", "english": "Date", "chinese": "日期", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.table.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.table.items", "english": "Items", "chinese": "明细", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.table.total", "english": "Total", "chinese": "总计", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.table.status", "english": "Status", "chinese": "状态", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.table.created", "english": "Created", "chinese": "创建时间", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.table.actions", "english": "Actions", "chinese": "操作", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.search_placeholder", "english": "Search contracts...", "chinese": "搜索合同...", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.success.created", "english": "Sales contract created successfully!", "chinese": "销售合同创建成功！", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.success.updated", "english": "Sales contract updated successfully!", "chinese": "销售合同更新成功！", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.success.deleted", "english": "Contract deleted successfully.", "chinese": "合同删除成功。", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.error.create", "english": "Failed to create sales contract", "chinese": "创建销售合同失败", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.error.update", "english": "Failed to update sales contract", "chinese": "更新销售合同失败", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.error.delete", "english": "Failed to delete contract.", "chinese": "删除合同失败。", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.empty", "english": "No sales contracts found", "chinese": "未找到销售合同", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "sales_contracts.empty.description", "english": "Create your first sales contract to get started.", "chinese": "创建您的第一个销售合同开始使用。", "category": "sales_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.title", "english": "Purchase Contracts", "chinese": "采购合同", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.subtitle", "english": "Manage your company's purchase contracts.", "chinese": "管理您公司的采购合同。", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.add", "english": "Add Contract", "chinese": "添加合同", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.add.title", "english": "Create Purchase Contract", "chinese": "创建采购合同", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.add.description", "english": "Create a new purchase contract with your supplier.", "chinese": "与您的供应商创建新的采购合同。", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.edit.title", "english": "Edit Purchase Contract", "chinese": "编辑采购合同", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.edit.description", "english": "Update purchase contract information.", "chinese": "更新采购合同信息。", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.delete.title", "english": "Are you sure?", "chinese": "您确定吗？", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.delete.description", "english": "This will permanently delete the contract. This action cannot be undone.", "chinese": "这将永久删除该合同。此操作无法撤销。", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.form.number", "english": "Contract Number", "chinese": "合同编号", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.form.supplier", "english": "Supplier", "chinese": "供应商", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.form.template", "english": "Template", "chinese": "模板", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.form.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.form.items", "english": "Items", "chinese": "明细", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.table.contract_number", "english": "Contract Number", "chinese": "合同编号", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.table.number", "english": "Contract #", "chinese": "合同编号", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.table.supplier", "english": "Supplier", "chinese": "供应商", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.table.date", "english": "Date", "chinese": "日期", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.table.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.table.items", "english": "Items", "chinese": "明细", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.table.total", "english": "Total", "chinese": "总计", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.table.status", "english": "Status", "chinese": "状态", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.table.created", "english": "Created", "chinese": "创建时间", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.table.actions", "english": "Actions", "chinese": "操作", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.search_placeholder", "english": "Search contracts...", "chinese": "搜索合同...", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.success.created", "english": "Purchase contract created successfully!", "chinese": "采购合同创建成功！", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.success.updated", "english": "Purchase contract updated successfully!", "chinese": "采购合同更新成功！", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.success.deleted", "english": "Contract deleted successfully.", "chinese": "合同删除成功。", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.error.create", "english": "Failed to create purchase contract", "chinese": "创建采购合同失败", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.error.update", "english": "Failed to update purchase contract", "chinese": "更新采购合同失败", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.error.delete", "english": "Failed to delete contract.", "chinese": "删除合同失败。", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.empty", "english": "No purchase contracts found", "chinese": "未找到采购合同", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "purchase_contracts.empty.description", "english": "Create your first purchase contract to get started.", "chinese": "创建您的第一个采购合同开始使用。", "category": "purchase_contracts", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.title", "english": "Suppliers", "chinese": "供应商管理", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.subtitle", "english": "Manage your supplier database and relationships", "chinese": "管理您的供应商数据库和关系", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.add", "english": "Add Supplier", "chinese": "添加供应商", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.add.title", "english": "Add New Supplier", "chinese": "添加新供应商", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.add.description", "english": "Create a new supplier record for your business.", "chinese": "为您的业务创建新的供应商记录。", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.edit.title", "english": "Edit Supplier", "chinese": "编辑供应商", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.edit.description", "english": "Update supplier information.", "chinese": "更新供应商信息。", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.delete.title", "english": "Delete Supplier", "chinese": "删除供应商", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.delete.description", "english": "Are you sure you want to delete this supplier? This action cannot be undone.", "chinese": "您确定要删除此供应商吗？此操作无法撤销。", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.delete.confirmation", "english": "This will permanently delete the supplier \"{name}\" and remove their data from our servers.", "chinese": "这将永久删除供应商\"{name}\"并从我们的服务器中删除其数据。", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.form.name", "english": "Supplier Name", "chinese": "供应商名称", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.form.contact_name", "english": "Contact Person", "chinese": "联系人", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.form.contact_phone", "english": "Phone Number", "chinese": "电话号码", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.form.contact_email", "english": "Email Address", "chinese": "邮箱地址", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.form.address", "english": "Address", "chinese": "地址", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.form.bank", "english": "Bank Details", "chinese": "银行详情", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.form.tax_id", "english": "Tax ID", "chinese": "税务编号", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.form.status", "english": "Status", "chinese": "状态", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.table.company_name", "english": "Company Name", "chinese": "公司名称", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.table.name", "english": "Supplier Name", "chinese": "供应商名称", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.table.contact_person", "english": "Contact Person", "chinese": "联系人", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.table.phone", "english": "Phone", "chinese": "电话", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.table.email", "english": "Email", "chinese": "邮箱", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.table.address", "english": "Address", "chinese": "地址", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.table.bank", "english": "Bank Details", "chinese": "银行详情", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.table.status", "english": "Status", "chinese": "状态", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.table.actions", "english": "Actions", "chinese": "操作", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.success.created", "english": "Supplier created successfully!", "chinese": "供应商创建成功！", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.success.updated", "english": "Supplier updated successfully!", "chinese": "供应商更新成功！", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.success.deleted", "english": "Supplier deleted successfully!", "chinese": "供应商删除成功！", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.error.create", "english": "Failed to create supplier", "chinese": "创建供应商失败", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.error.update", "english": "Failed to update supplier", "chinese": "更新供应商失败", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.error.delete", "english": "Failed to delete supplier", "chinese": "删除供应商失败", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.empty", "english": "No suppliers found", "chinese": "未找到供应商", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.empty.description", "english": "Get started by adding your first supplier.", "chinese": "添加您的第一个供应商开始使用。", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.view.subtitle", "english": "View supplier details and related purchase contracts", "chinese": "查看供应商详情和相关采购合同", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.view.supplier_info", "english": "Supplier Information", "chinese": "供应商信息", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.view.supplier_info_desc", "english": "Basic supplier details and contact information", "chinese": "基本供应商详情和联系信息", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.view.purchase_contracts", "english": "Purchase Contracts", "chinese": "采购合同", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.view.purchase_contracts_desc", "english": "Related purchase contracts with this supplier", "chinese": "与此供应商相关的采购合同", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.view.no_contracts", "english": "No Purchase Contracts", "chinese": "无采购合同", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.view.no_contracts_desc", "english": "This supplier doesn't have any purchase contracts yet.", "chinese": "此供应商尚未有任何采购合同。", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.view.create_contract", "english": "Create Purchase Contract", "chinese": "创建采购合同", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "suppliers.view.view_all_contracts", "english": "View All Contracts", "chinese": "查看所有合同", "category": "suppliers", "hasTranslation": true, "needsReview": false}, {"key": "samples.title", "english": "Sample Management", "chinese": "样品管理", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.subtitle", "english": "Manage product samples and approval workflow", "chinese": "管理产品样品和审批流程", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.add", "english": "New Sample", "chinese": "新建样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.refresh", "english": "Refresh", "chinese": "刷新", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.loading", "english": "Loading samples...", "chinese": "正在加载样品...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.found", "english": "{count} samples found", "chinese": "找到 {count} 个样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.cards.outbound.title", "english": "📤 Outbound Samples", "chinese": "📤 出库样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.cards.outbound.description", "english": "We send to customers", "chinese": "我们发送给客户", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.cards.inbound.title", "english": "📥 Inbound Samples", "chinese": "📥 入库样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.cards.inbound.description", "english": "From customers & suppliers", "chinese": "来自客户和供应商", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.cards.internal.title", "english": "🏭 Internal Samples", "chinese": "🏭 内部样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.cards.internal.description", "english": "R&D and testing", "chinese": "研发和测试", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.cards.quality.title", "english": "🧪 Quality Pipeline", "chinese": "🧪 质量流水线", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.cards.quality.description", "english": "Awaiting QC approval", "chinese": "等待质检审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.table.sample", "english": "<PERSON><PERSON>", "chinese": "样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.table.direction", "english": "Direction", "chinese": "方向", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.table.purpose", "english": "Purpose", "chinese": "用途", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.table.relationship", "english": "Relationship", "chinese": "业务关系", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.table.product", "english": "Product", "chinese": "产品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.table.status", "english": "Status", "chinese": "状态", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.table.priority", "english": "Priority", "chinese": "优先级", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.table.created", "english": "Created", "chinese": "创建时间", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.table.actions", "english": "Actions", "chinese": "操作", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.table.empty", "english": "No samples found. Create your first sample to get started.", "chinese": "未找到样品。创建您的第一个样品开始使用。", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.actions.view", "english": "View", "chinese": "查看", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.actions.edit", "english": "Edit", "chinese": "编辑", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.actions.delete", "english": "Delete", "chinese": "删除", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.actions.approve", "english": "Approve", "chinese": "审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.actions.reject", "english": "Reject", "chinese": "拒绝", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.search", "english": "Search samples by name, code, or notes...", "chinese": "按名称、编码或备注搜索样品...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.status.all", "english": "All Statuses", "chinese": "所有状态", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.status.pending", "english": "Pending", "chinese": "待审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.status.approved", "english": "Approved", "chinese": "已审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.status.rejected", "english": "Rejected", "chinese": "已拒绝", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.type.all", "english": "All Types", "chinese": "所有类型", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.type.development", "english": "Development", "chinese": "开发", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.type.production", "english": "Production", "chinese": "生产", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.type.quality", "english": "Quality", "chinese": "质量", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.type.prototype", "english": "Prototype", "chinese": "原型", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.direction.all", "english": "All Directions", "chinese": "所有方向", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.direction.outbound", "english": "Outbound", "chinese": "出库", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.direction.inbound", "english": "Inbound", "chinese": "入库", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.direction.internal", "english": "Internal", "chinese": "内部", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.advanced", "english": "Advanced", "chinese": "高级", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.filters.clear", "english": "Clear Filters", "chinese": "清除筛选", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.delete.success.title", "english": "Sample Deleted", "chinese": "样品已删除", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.delete.success.description", "english": "Sample '{name}' has been successfully deleted", "chinese": "样品 '{name}' 已成功删除", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.delete.error.title", "english": "Delete Failed", "chinese": "删除失败", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.delete.error.description", "english": "Failed to delete sample. Please try again.", "chinese": "删除样品失败，请重试。", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.delete.dialog.title", "english": "Delete Sample", "chinese": "删除样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.delete.dialog.description", "english": "Are you sure you want to delete this sample? This action cannot be undone.", "chinese": "您确定要删除此样品吗？此操作无法撤销。", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.delete.dialog.warning", "english": "This action is permanent and cannot be undone.", "chinese": "此操作是永久性的，无法撤销。", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.delete.dialog.confirm", "english": "Delete Sample", "chinese": "删除样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.delete.dialog.deleting", "english": "Deleting...", "chinese": "删除中...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.code", "english": "Sample Code", "chinese": "样品编码", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.name", "english": "Sample Name", "chinese": "样品名称", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.date", "english": "Date", "chinese": "日期", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.status", "english": "Status", "chinese": "状态", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.priority", "english": "Priority", "chinese": "优先级", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.type", "english": "Sample Type", "chinese": "样品类型", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.direction", "english": "Direction", "chinese": "方向", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.purpose", "english": "Purpose", "chinese": "用途", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.customer", "english": "Customer", "chinese": "客户", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.supplier", "english": "Supplier", "chinese": "供应商", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.product", "english": "Product", "chinese": "产品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.quantity", "english": "Quantity", "chinese": "数量", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.unit", "english": "Unit", "chinese": "单位", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.cost", "english": "Cost", "chinese": "成本", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.delivery_date", "english": "Delivery Date", "chinese": "交付日期", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.specifications", "english": "Technical Specifications", "chinese": "技术规格", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.quality_requirements", "english": "Quality Requirements", "chinese": "质量要求", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.fields.notes", "english": "Notes", "chinese": "备注", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.title", "english": "Create Sample", "chinese": "创建样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.description", "english": "Create a new sample record for tracking and approval", "chinese": "创建新的样品记录用于跟踪和审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.basic_info", "english": "Basic Information", "chinese": "基本信息", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.workflow", "english": "Sample Workflow", "chinese": "样品流程", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.relationships", "english": "Business Relationships", "chinese": "业务关系", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.specifications", "english": "Specifications & Details", "chinese": "规格和详情", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.success.title", "english": "Sample Created", "chinese": "样品已创建", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.success.description", "english": "Sample has been created successfully", "chinese": "样品已成功创建", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.error.title", "english": "Creation Failed", "chinese": "创建失败", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.error.description", "english": "Failed to create sample. Please try again.", "chinese": "创建样品失败，请重试。", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.cancel", "english": "Cancel", "chinese": "取消", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.save", "english": "Create Sample", "chinese": "创建样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.create.saving", "english": "Creating...", "chinese": "创建中...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.back", "english": "Back to Samples", "chinese": "返回样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.pending_request", "english": "Pending Request", "chinese": "待处理请求", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.approved", "english": "Approved", "chinese": "已审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.rejected", "english": "Rejected", "chinese": "已拒绝", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.edit", "english": "Edit", "chinese": "编辑", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.sample_info", "english": "Sample Information", "chinese": "样品信息", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.sample_type", "english": "Sample Type", "chinese": "样品类型", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.priority", "english": "Priority", "chinese": "优先级", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.sample_date", "english": "Sample Date", "chinese": "样品日期", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.quantity", "english": "Quantity", "chinese": "数量", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.delivery_date", "english": "Delivery Date", "chinese": "交付日期", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.cost", "english": "Cost", "chinese": "成本", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.relationships", "english": "Relationships", "chinese": "业务关系", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.customer", "english": "Customer", "chinese": "客户", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.contact", "english": "Contact", "chinese": "联系人", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.email", "english": "Email", "chinese": "邮箱", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.product", "english": "Product", "chinese": "产品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.sku", "english": "SKU", "chinese": "SKU", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.supplier", "english": "Supplier", "chinese": "供应商", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.specifications", "english": "Specifications & Notes", "chinese": "规格和备注", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.technical_specs", "english": "Technical Specifications", "chinese": "技术规格", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.quality_requirements", "english": "Quality Requirements", "chinese": "质量要求", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.notes", "english": "Notes", "chinese": "备注", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.approval_history", "english": "Approval History", "chinese": "审批历史", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.created", "english": "Created", "chinese": "已创建", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.revised", "english": "Revised", "chinese": "已修订", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.pending", "english": "pending", "chinese": "待审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.revision_required", "english": "revision_required", "chinese": "需要修订", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.by_system", "english": "by System", "chinese": "由系统", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.by_current_user", "english": "by Current User", "chinese": "由当前用户", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.sample_created", "english": "Sample created and submitted for approval", "chinese": "样品已创建并提交审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.sample_processed", "english": "Sample processed", "chinese": "样品已处理", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.metadata", "english": "<PERSON><PERSON><PERSON>", "chinese": "元数据", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.created_date", "english": "Created", "chinese": "创建时间", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.approved_by", "english": "Approved by", "chinese": "审批人", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.approved_date", "english": "Approved Date", "chinese": "审批日期", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.status.created", "english": "Created", "chinese": "已创建", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.status.pending", "english": "Pending", "chinese": "待审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.status.approved", "english": "Approved", "chinese": "已审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.status.rejected", "english": "Rejected", "chinese": "已拒绝", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.status.revised", "english": "Revised", "chinese": "已修订", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.status.revision_required", "english": "Revision Required", "chinese": "需要修订", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.actions.sample_created", "english": "Sample created and submitted for approval", "chinese": "样品已创建并提交审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.actions.sample_processed", "english": "Sample processed", "chinese": "样品已处理", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.actions.sample_approved", "english": "<PERSON><PERSON> approved", "chinese": "样品已审批", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.actions.sample_rejected", "english": "<PERSON><PERSON> rejected", "chinese": "样品已拒绝", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.actions.revision_requested", "english": "Revision requested", "chinese": "要求修订", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.by_system_on", "english": "by System on", "chinese": "由系统于", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.by_current_user_on", "english": "by Current User on", "chinese": "由当前用户于", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.view.by_user_on", "english": "by {user} on", "chinese": "由 {user} 于", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.title", "english": "<PERSON>", "chinese": "编辑样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.description", "english": "Update sample information and specifications", "chinese": "更新样品信息和规格", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.loading", "english": "Loading sample data...", "chinese": "正在加载样品数据...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.success.title", "english": "Sample Updated", "chinese": "样品已更新", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.success.description", "english": "Sample has been updated successfully", "chinese": "样品已成功更新", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.error.title", "english": "Update Failed", "chinese": "更新失败", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.error.description", "english": "Failed to update sample. Please try again.", "chinese": "更新样品失败，请重试。", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.error.load", "english": "Failed to load sample data. Please try again.", "chinese": "加载样品数据失败，请重试。", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.back", "english": "Back to Sample", "chinese": "返回样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.cancel", "english": "Cancel", "chinese": "取消", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.save", "english": "Update Sample", "chinese": "更新样品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.saving", "english": "Updating...", "chinese": "更新中...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.validation.name", "english": "Sample name is required", "chinese": "样品名称为必填项", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.code.readonly", "english": "Sample code cannot be changed", "chinese": "样品编码无法更改", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.basic_info", "english": "Basic Information", "chinese": "基本信息", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.basic_info_desc", "english": "Update the basic sample information", "chinese": "更新基本样品信息", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.sample_code", "english": "Sample Code", "chinese": "样品编码", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.sample_name", "english": "Sample Name", "chinese": "样品名称", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.sample_name_placeholder", "english": "Enter sample name", "chinese": "输入样品名称", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.sample_type", "english": "Sample Type", "chinese": "样品类型", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.priority", "english": "Priority", "chinese": "优先级", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.relationships", "english": "Relationships", "chinese": "业务关系", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.relationships_desc", "english": "Associate this sample with customers, products, and suppliers", "chinese": "将此样品与客户、产品和供应商关联", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.customer", "english": "Customer", "chinese": "客户", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.customer_placeholder", "english": "Search customers...", "chinese": "搜索客户...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.product", "english": "Product", "chinese": "产品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.product_placeholder", "english": "Search products...", "chinese": "搜索产品...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.supplier", "english": "Supplier", "chinese": "供应商", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.supplier_placeholder", "english": "Search suppliers...", "chinese": "搜索供应商...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.specifications", "english": "Specifications & Details", "chinese": "规格和详情", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.specifications_desc", "english": "Add technical specifications and additional details", "chinese": "添加技术规格和其他详情", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.quantity", "english": "Quantity", "chinese": "数量", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.unit", "english": "Unit", "chinese": "单位", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.cost", "english": "Cost", "chinese": "成本", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.delivery_date", "english": "Delivery Date", "chinese": "交付日期", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.technical_specs", "english": "Technical Specifications", "chinese": "技术规格", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.technical_specs_placeholder", "english": "Enter technical specifications...", "chinese": "输入技术规格...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.quality_requirements", "english": "Quality Requirements", "chinese": "质量要求", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.quality_requirements_placeholder", "english": "Enter quality requirements...", "chinese": "输入质量要求...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.notes", "english": "Notes", "chinese": "备注", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.notes_placeholder", "english": "Enter additional notes...", "chinese": "输入其他备注...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.search.no_results", "english": "No results found", "chinese": "未找到结果", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.search.add_new_customer", "english": "Add new customer", "chinese": "添加新客户", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.search.add_new_product", "english": "Add new product", "chinese": "添加新产品", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.search.add_new_supplier", "english": "Add new supplier", "chinese": "添加新供应商", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.search.loading", "english": "Loading...", "chinese": "加载中...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "samples.edit.search.type_to_search", "english": "Type to search...", "chinese": "输入以搜索...", "category": "samples", "hasTranslation": true, "needsReview": false}, {"key": "inventory.title", "english": "Inventory Management", "chinese": "库存管理", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.subtitle", "english": "Manage stock levels, inbound and outbound operations", "chinese": "管理库存水平、入库和出库操作", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.tabs.inbound", "english": "Inbound", "chinese": "入库", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.tabs.outbound", "english": "Outbound", "chinese": "出库", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.tabs.stock", "english": "Stock", "chinese": "库存", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.tabs.transactions", "english": "Transactions", "chinese": "交易记录", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.inbound.form.product", "english": "Product", "chinese": "产品", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.inbound.form.qty", "english": "Quantity", "chinese": "数量", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.inbound.form.location", "english": "Location", "chinese": "位置", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.inbound.form.ref", "english": "Reference", "chinese": "参考", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.inbound.button", "english": "Receive", "chinese": "收货", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.outbound.form.product", "english": "Product", "chinese": "产品", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.outbound.form.qty", "english": "Quantity", "chinese": "数量", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.outbound.form.location", "english": "Location", "chinese": "位置", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.outbound.form.ref", "english": "Reference", "chinese": "参考", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.outbound.button", "english": "Ship", "chinese": "发货", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.stock.loading", "english": "Loading inventory...", "chinese": "正在加载库存...", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.stock.error", "english": "Failed to load inventory data", "chinese": "加载库存数据失败", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.stock.retry", "english": "Try again", "chinese": "重试", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.stock.table.lot", "english": "Lot", "chinese": "批次", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.stock.table.location", "english": "Location", "chinese": "位置", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.transactions.title", "english": "Transactions", "chinese": "交易记录", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.transactions.desc", "english": "Inventory movement history", "chinese": "库存移动历史", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.transaction_forms", "english": "Transaction Forms", "chinese": "交易表单", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.transaction_history", "english": "Transaction History", "chinese": "交易历史", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.transaction_success", "english": "Transaction Successful", "chinese": "交易成功", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.transaction_error", "english": "Transaction Failed", "chinese": "交易失败", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.inbound", "english": "Inbound", "chinese": "入库", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.outbound", "english": "Outbound", "chinese": "出库", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.transfer", "english": "Transfer", "chinese": "调拨", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.adjustment", "english": "Adjustment", "chinese": "调整", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.product", "english": "Product", "chinese": "产品", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.quantity", "english": "Quantity", "chinese": "数量", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.location", "english": "Location", "chinese": "位置", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.source_location", "english": "Source Location", "chinese": "源位置", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.destination_location", "english": "Destination Location", "chinese": "目标位置", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.adjustment_quantity", "english": "Adjustment Quantity", "chinese": "调整数量", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reason_code", "english": "Reason Code", "chinese": "原因代码", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.notes", "english": "Notes", "chinese": "备注", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reference", "english": "Reference", "chinese": "参考", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.status", "english": "Status", "chinese": "状态", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.date", "english": "Date", "chinese": "日期", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.type", "english": "Type", "chinese": "类型", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.select_product", "english": "Select Product", "chinese": "选择产品", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.select_location", "english": "Select Location", "chinese": "选择位置", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reference_placeholder", "english": "PO/SO number, receipt number, etc.", "chinese": "采购单/销售单号、收货单号等", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.notes_placeholder", "english": "Additional notes or comments", "chinese": "附加备注或说明", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.transfer_notes_placeholder", "english": "Reason for transfer", "chinese": "调拨原因", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.adjustment_notes_placeholder", "english": "Explain the reason for adjustment", "chinese": "说明调整原因", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.positive_negative_allowed", "english": "Positive or negative values allowed", "chinese": "允许正负值", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.process_inbound", "english": "Process Inbound", "chinese": "处理入库", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.process_outbound", "english": "Process Outbound", "chinese": "处理出库", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.process_transfer", "english": "Process Transfer", "chinese": "处理调拨", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.process_adjustment", "english": "Process Adjustment", "chinese": "处理调整", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.adjustment_warning", "english": "Warning", "chinese": "警告", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.adjustment_warning_text", "english": "Adjustments directly modify inventory quantities. Ensure proper authorization and documentation.", "chinese": "调整会直接修改库存数量。请确保有适当的授权和文档记录。", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.search_transactions", "english": "Search transactions...", "chinese": "搜索交易记录...", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.filter_by_type", "english": "Filter by Type", "chinese": "按类型筛选", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.filter_by_location", "english": "Filter by Location", "chinese": "按位置筛选", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.all_types", "english": "All Types", "chinese": "所有类型", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.all_locations", "english": "All Locations", "chinese": "所有位置", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.no_transactions", "english": "No transactions found", "chinese": "未找到交易记录", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.showing_transactions", "english": "Showing {count} of {total} transactions", "chinese": "显示 {count} 条，共 {total} 条交易记录", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.fetch_error", "english": "Failed to Load Data", "chinese": "数据加载失败", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.adjustment_notes", "english": "Adjustment Notes", "chinese": "调整备注", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reason_receipt", "english": "Receipt", "chinese": "收货", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reason_shipment", "english": "Shipment", "chinese": "发货", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reason_transfer", "english": "Transfer", "chinese": "调拨", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reason_cycle_count", "english": "Cycle Count", "chinese": "盘点", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reason_damage", "english": "Damage", "chinese": "损坏", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reason_obsolete", "english": "Obsolete", "chinese": "报废", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reason_adjustment", "english": "Adjustment", "chinese": "调整", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reason_return", "english": "Return", "chinese": "退货", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.reason_sample", "english": "<PERSON><PERSON>", "chinese": "样品", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.status_pending", "english": "Pending", "chinese": "待处理", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.status_approved", "english": "Approved", "chinese": "已批准", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.status_rejected", "english": "Rejected", "chinese": "已拒绝", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.finishedGoods", "english": "Finished Goods", "chinese": "成品库存", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.rawMaterials", "english": "Raw Materials", "chinese": "原材料库存", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "inventory.totalValue", "english": "Total Value", "chinese": "总价值", "category": "inventory", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.title", "english": "Company Profile", "chinese": "公司资料", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.subtitle", "english": "Manage your company information and settings", "chinese": "管理您的公司信息和设置", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.not_found", "english": "No Company Profile Found", "chinese": "未找到公司资料", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.not_found_desc", "english": "It looks like you haven't completed your company profile setup yet.", "chinese": "看起来您还没有完成公司资料设置。", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.complete_setup", "english": "Complete Company Setup", "chinese": "完成公司设置", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.complete", "english": "Complete", "chinese": "完整", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.incomplete", "english": "Incomplete", "chinese": "不完整", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.edit", "english": "Edit Profile", "chinese": "编辑资料", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.save", "english": "Save Changes", "chinese": "保存更改", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.cancel", "english": "Cancel", "chinese": "取消", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.tabs.basic", "english": "Basic Information", "chinese": "基本信息", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.tabs.business", "english": "Business Details", "chinese": "业务详情", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.tabs.banking", "english": "Banking", "chinese": "银行信息", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.tabs.export", "english": "Export & Trade", "chinese": "出口贸易", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.basic.description", "english": "Your company's basic contact and address information", "chinese": "您公司的基本联系方式和地址信息", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.business.description", "english": "Business registration and operational information", "chinese": "业务注册和运营信息", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.banking.description", "english": "Banking and financial account details", "chinese": "银行和金融账户详情", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.export.description", "english": "Export licensing and trade compliance information", "chinese": "出口许可和贸易合规信息", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.success.updated", "english": "Company profile updated successfully!", "chinese": "公司资料更新成功！", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.profile.error.update", "english": "Failed to update profile", "chinese": "更新资料失败", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.name", "english": "Company Name", "chinese": "公司名称", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.legal_name", "english": "Legal Company Name", "chinese": "法定公司名称", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.email", "english": "Email Address", "chinese": "邮箱地址", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.phone", "english": "Phone Number", "chinese": "电话号码", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.website", "english": "Website", "chinese": "网站", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.country", "english": "Country", "chinese": "国家", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.address_line1", "english": "Street Address", "chinese": "街道地址", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.address_line2", "english": "Address Line 2", "chinese": "地址第二行", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.city", "english": "City", "chinese": "城市", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.state_province", "english": "State/Province", "chinese": "省/州", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.postal_code", "english": "Postal Code", "chinese": "邮政编码", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.industry", "english": "Industry", "chinese": "行业", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.business_type", "english": "Business Type", "chinese": "业务类型", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.employee_count", "english": "Employee Count", "chinese": "员工数量", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.annual_revenue", "english": "Annual Revenue", "chinese": "年收入", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.registration_number", "english": "Registration Number", "chinese": "注册号", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.tax_id", "english": "Tax ID", "chinese": "税务编号", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.vat_number", "english": "VAT Number", "chinese": "增值税号", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.bank_name", "english": "Bank Name", "chinese": "银行名称", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.bank_account", "english": "Account Number", "chinese": "账户号码", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.bank_swift", "english": "SWIFT/BIC Code", "chinese": "SWIFT/BIC代码", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.bank_address", "english": "Bank Address", "chinese": "银行地址", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.export_license", "english": "Export License", "chinese": "出口许可证", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.customs_code", "english": "Customs Code", "chinese": "海关代码", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.preferred_incoterms", "english": "Preferred Incoterms", "chinese": "首选贸易条款", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "company.field.preferred_payment_terms", "english": "Preferred Payment Terms", "chinese": "首选付款条件", "category": "company", "hasTranslation": true, "needsReview": false}, {"key": "quality.title", "english": "Quality Control", "chinese": "质量控制", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.subtitle", "english": "Manage quality inspections and certificates", "chinese": "管理质量检验和证书", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.metrics.title", "english": "Quality Metrics", "chinese": "质量指标", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.metrics.pass_rate", "english": "Pass Rate", "chinese": "合格率", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.metrics.total_inspections", "english": "Total Inspections", "chinese": "总检验数", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.metrics.pending", "english": "Pending Inspections", "chinese": "待检验数", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.metrics.defect_rate", "english": "Defect Rate", "chinese": "缺陷率", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.inspections.title", "english": "Recent Inspections", "chinese": "最近检验", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.inspections.subtitle", "english": "Latest quality inspection results", "chinese": "最新质量检验结果", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.defects.title", "english": "Defect Tracking", "chinese": "缺陷跟踪", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.defects.subtitle", "english": "Track and manage quality defects", "chinese": "跟踪和管理质量缺陷", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.title", "english": "Contract Templates", "chinese": "合同模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.subtitle", "english": "Manage reusable contract templates for sales and purchase agreements", "chinese": "管理销售和采购协议的可重用合同模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.add", "english": "Add Template", "chinese": "添加模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.table.name", "english": "Template Name", "chinese": "模板名称", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.table.type", "english": "Type", "chinese": "类型", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.table.language", "english": "Language", "chinese": "语言", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.table.version", "english": "Version", "chinese": "版本", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.table.status", "english": "Status", "chinese": "状态", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.table.actions", "english": "Actions", "chinese": "操作", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.number", "english": "Contract Number", "chinese": "合同编号", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.customer", "english": "Customer", "chinese": "客户", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.supplier", "english": "Supplier", "chinese": "供应商", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.template", "english": "Template", "chinese": "模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.items", "english": "Contract Items", "chinese": "合同项目", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.product", "english": "Product", "chinese": "产品", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.quantity", "english": "Quantity", "chinese": "数量", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.price", "english": "Price", "chinese": "价格", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.total", "english": "Total", "chinese": "总计", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.add_item", "english": "Add Item", "chinese": "添加项目", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.remove_item", "english": "Remove", "chinese": "移除", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.contract_info", "english": "Contract Information", "chinese": "合同信息", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.contract_info_desc", "english": "Basic contract details and customer information", "chinese": "基本合同详情和客户信息", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.items_section", "english": "Contract Items", "chinese": "合同项目", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.items_section_desc", "english": "Products and quantities for this contract", "chinese": "此合同的产品和数量", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.template_section", "english": "Contract Template", "chinese": "合同模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.form.template_section_desc", "english": "Choose a template for contract document generation", "chinese": "选择合同文档生成模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.back", "english": "Back to Contracts", "chinese": "返回合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.back_sales", "english": "Back to Sales Contracts", "chinese": "返回销售合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.back_purchase", "english": "Back to Purchase Contracts", "chinese": "返回采购合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.edit_contract", "english": "Edit Contract", "chinese": "编辑合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.export_pdf", "english": "Export PDF", "chinese": "导出PDF", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.loading", "english": "Loading contract document...", "chinese": "正在加载合同文档...", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.no_document", "english": "No contract document available", "chinese": "无可用合同文档", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.contract_summary", "english": "Contract Summary", "chinese": "合同摘要", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.contract_document", "english": "Contract Document", "chinese": "合同文档", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.customer", "english": "Customer", "chinese": "客户", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.supplier", "english": "Supplier", "chinese": "供应商", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.contract_date", "english": "Contract Date", "chinese": "合同日期", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.total_value", "english": "Total Value", "chinese": "合同总额", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.template", "english": "Template", "chinese": "模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.no_email", "english": "No email", "chinese": "无邮箱", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.items_count", "english": "{count} items", "chinese": "{count} 项", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.sales_template", "english": "sales template", "chinese": "销售模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.view.purchase_template", "english": "purchase template", "chinese": "采购模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.title_sales", "english": "Edit Sales Contract", "chinese": "编辑销售合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.title_purchase", "english": "Edit Purchase Contract", "chinese": "编辑采购合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.subtitle", "english": "Update the details of contract {number}", "chinese": "更新合同 {number} 的详细信息", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.back", "english": "Back to Contracts", "chinese": "返回合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.template_optional", "english": "Contract Template (Optional)", "chinese": "合同模板（可选）", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.template_desc", "english": "sales contract template", "chinese": "销售合同模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.preview", "english": "Preview", "chinese": "预览", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.items_title", "english": "Contract Items", "chinese": "合同项目", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.add_item", "english": "Add Item", "chinese": "添加项目", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.product", "english": "Product", "chinese": "产品", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.quantity", "english": "Quantity", "chinese": "数量", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.unit_price", "english": "Unit Price", "chinese": "单价", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.sku_label", "english": "SKU: {sku}", "chinese": "SKU: {sku}", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.edit.unit_label", "english": "Unit: {unit}", "chinese": "单位: {unit}", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.title_sales", "english": "Create Sales Contract", "chinese": "创建销售合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.title_purchase", "english": "Create Purchase Contract", "chinese": "创建采购合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.subtitle_sales", "english": "Enter the details for your new sales contract", "chinese": "输入新销售合同的详细信息", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.subtitle_purchase", "english": "Enter the details for your new purchase contract", "chinese": "输入新采购合同的详细信息", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.back_sales", "english": "Sales Contracts", "chinese": "销售合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.back_purchase", "english": "Purchase Contracts", "chinese": "采购合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.add_new", "english": "Add New Contract", "chinese": "添加新合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.contract_info", "english": "Contract Information", "chinese": "合同信息", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.contract_info_desc", "english": "Basic contract details and customer information", "chinese": "基本合同详情和客户信息", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.contract_info_desc_purchase", "english": "Basic contract details and supplier information", "chinese": "基本合同详情和供应商信息", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.number", "english": "Contract Number", "chinese": "合同编号", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.number_placeholder", "english": "e.g., PC-2025-001", "chinese": "例如：PC-2025-001", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.supplier", "english": "Supplier", "chinese": "供应商", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.supplier_placeholder", "english": "Select supplier...", "chinese": "选择供应商...", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.customer", "english": "Customer", "chinese": "客户", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.customer_placeholder", "english": "Select customer...", "chinese": "选择客户...", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.currency_placeholder", "english": "e.g., USD", "chinese": "例如：USD", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.template", "english": "Contract Template (Optional)", "chinese": "合同模板（可选）", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.template_placeholder", "english": "Select template...", "chinese": "选择模板...", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.items", "english": "Contract Items", "chinese": "合同项目", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.items_desc", "english": "Add products and quantities for this contract", "chinese": "为此合同添加产品和数量", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.add_item", "english": "Add Item", "chinese": "添加项目", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.remove_item", "english": "Remove Item", "chinese": "删除项目", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.product", "english": "Product", "chinese": "产品", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.product_placeholder", "english": "Select product...", "chinese": "选择产品...", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.quantity", "english": "Quantity", "chinese": "数量", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.quantity_placeholder", "english": "0", "chinese": "0", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.unit_price", "english": "Unit Price", "chinese": "单价", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.unit_price_placeholder", "english": "0.00", "chinese": "0.00", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.total", "english": "Total", "chinese": "总计", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.cancel", "english": "Cancel", "chinese": "取消", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.create", "english": "Create Contract", "chinese": "创建合同", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.creating", "english": "Creating...", "chinese": "创建中...", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.success", "english": "Contract created successfully!", "chinese": "合同创建成功！", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.error", "english": "Failed to create contract", "chinese": "创建合同失败", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.summary", "english": "Contract Summary", "chinese": "合同摘要", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.summary_desc", "english": "Review the total contract value and details", "chinese": "查看合同总价值和详细信息", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.items_count", "english": "Items:", "chinese": "项目数量:", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.currency_label", "english": "Currency:", "chinese": "货币:", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.create.total_value", "english": "Total Contract Value:", "chinese": "合同总价值:", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.page_title", "english": "Contract Templates", "chinese": "合同模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.page_desc", "english": "Manage contract templates for sales and purchase agreements", "chinese": "管理销售和采购协议的合同模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.sales_section", "english": "Sales Contract Templates", "chinese": "销售合同模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.sales_desc", "english": "Create and manage sales contract templates", "chinese": "创建和管理销售合同模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.purchase_section", "english": "Purchase Contract Templates", "chinese": "采购合同模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.purchase_desc", "english": "Create and manage purchase contract templates", "chinese": "创建和管理采购合同模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.template_name", "english": "Template Name", "chinese": "模板名称", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.currency", "english": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "货币", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.payment_terms", "english": "Payment Terms", "chinese": "付款条款", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.delivery_terms", "english": "Delivery Terms", "chinese": "交付条款", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.template_content", "english": "Template Content", "chinese": "模板内容", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.content_placeholder", "english": "Enter contract template content with placeholders like {{customer_name}}, {{product_name}}, etc.", "chinese": "输入合同模板内容，使用占位符如 {{customer_name}}、{{product_name}} 等", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.content_placeholder_purchase", "english": "Enter contract template content with placeholders like {{supplier_name}}, {{material_name}}, etc.", "chinese": "输入合同模板内容，使用占位符如 {{supplier_name}}、{{material_name}} 等", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.create_template", "english": "Create Template", "chinese": "创建模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.existing_templates", "english": "Existing Templates", "chinese": "现有模板", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.name", "english": "Name", "chinese": "名称", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.actions", "english": "Actions", "chinese": "操作", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.payment_30_days", "english": "30 days", "chinese": "30天", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.payment_60_days", "english": "60 days", "chinese": "60天", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.delivery_fob", "english": "FOB", "chinese": "FOB", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.delivery_cif", "english": "CIF", "chinese": "CIF", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contracts.templates.delivery_exw", "english": "EXW", "chinese": "EXW", "category": "contracts", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.sales.title", "english": "Sales Contract Templates", "chinese": "销售合同模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.sales.description", "english": "Create and manage templates for sales contracts", "chinese": "创建和管理销售合同模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.sales.sample", "english": "Sample Templates", "chinese": "示例模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.sales.sample_title", "english": "Professional Sales Contract Template", "chinese": "专业销售合同模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.sales.sample_desc", "english": "Copy this professional template and paste it into the Template Content field below.", "chinese": "复制此专业模板并粘贴到下面的模板内容字段中。", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.purchase.title", "english": "Purchase Contract Templates", "chinese": "采购合同模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.purchase.description", "english": "Create and manage templates for purchase contracts", "chinese": "创建和管理采购合同模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.purchase.sample", "english": "Sample Templates", "chinese": "示例模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.purchase.sample_title", "english": "Professional Purchase Contract Template", "chinese": "专业采购合同模板", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "contract_templates.purchase.sample_desc", "english": "Copy this professional template and paste it into the Template Content field below.", "chinese": "复制此专业模板并粘贴到下面的模板内容字段中。", "category": "contract_templates", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.documents.title", "english": "Document Attachments", "chinese": "文档附件", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.documents.upload", "english": "Upload Documents", "chinese": "上传文档", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.documents.formats", "english": "Supported formats: PDF, DOC, DOCX, XLS, XLSX, TXT", "chinese": "支持格式：PDF、DOC、DOCX、XLS、XLSX、TXT", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.documents.none", "english": "No documents attached", "chinese": "无文档附件", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.photos.title", "english": "Photo Attachments", "chinese": "照片附件", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.photos.upload", "english": "Upload Photos", "chinese": "上传照片", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.photos.formats", "english": "Supported formats: JPG, PNG, GIF, WebP", "chinese": "支持格式：JPG、PNG、GIF、WebP", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.photos.none", "english": "No photos attached", "chinese": "无照片附件", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.preview", "english": "Preview", "chinese": "预览", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.download", "english": "Download", "chinese": "下载", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.remove", "english": "Remove", "chinese": "删除", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.uploading", "english": "Uploading files...", "chinese": "正在上传文件...", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.upload_success", "english": "Upload Successful", "chinese": "上传成功", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.upload_success_desc", "english": "file(s) uploaded successfully", "chinese": "个文件上传成功", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.download_success", "english": "Download Complete", "chinese": "下载完成", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.download_success_desc", "english": "downloaded successfully", "chinese": "下载成功", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.download_failed", "english": "Download Failed", "chinese": "下载失败", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.download_failed_desc", "english": "Failed to download file. Please try again.", "chinese": "下载文件失败，请重试。", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.remove_success", "english": "File Removed", "chinese": "文件已删除", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.remove_success_desc", "english": "File removed successfully", "chinese": "文件删除成功", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.remove_failed", "english": "Remove Failed", "chinese": "删除失败", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.attachments.remove_failed_desc", "english": "Failed to remove file. Please try again.", "chinese": "删除文件失败，请重试。", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.status", "english": "Quality Status", "chinese": "质量状态", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.status.pending", "english": "Pending", "chinese": "待处理", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.status.approved", "english": "Approved", "chinese": "已通过", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.status.quarantined", "english": "Quarantined", "chinese": "已隔离", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.status.rejected", "english": "Rejected", "chinese": "已拒绝", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "workOrder.title", "english": "Work Order", "chinese": "生产工单", "category": "workOrder", "hasTranslation": true, "needsReview": false}, {"key": "workOrder.number", "english": "Work Order Number", "chinese": "工单编号", "category": "workOrder", "hasTranslation": true, "needsReview": false}, {"key": "workOrder.status.completed", "english": "Completed", "chinese": "已完成", "category": "workOrder", "hasTranslation": true, "needsReview": false}, {"key": "workOrder.status.pending", "english": "Pending", "chinese": "待开始", "category": "workOrder", "hasTranslation": true, "needsReview": false}, {"key": "workOrder.status.in-progress", "english": "In Progress", "chinese": "进行中", "category": "workOrder", "hasTranslation": true, "needsReview": false}, {"key": "products.form.quality_requirements", "english": "Quality Requirements", "chinese": "质量要求", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.inspection_required", "english": "Quality Inspection Required", "chinese": "需要质量检验", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.inspection_required_desc", "english": "Enable this if the product requires quality inspection before approval", "chinese": "如果产品需要质量检验才能通过，请启用此选项", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.quality_tolerance", "english": "Quality Tolerance", "chinese": "质量公差", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.quality_notes", "english": "Quality Notes", "chinese": "质量备注", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.form.quality_notes_placeholder", "english": "Enter specific quality requirements, standards, or inspection criteria...", "chinese": "输入具体的质量要求、标准或检验标准...", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.quality.not_required", "english": "Not Required", "chinese": "无需检验", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.success.updated_desc", "english": "Product quality requirements have been updated successfully.", "chinese": "产品质量要求已成功更新。", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "products.success.created_desc", "english": "Product with quality requirements has been created successfully.", "chinese": "已成功创建具有质量要求的产品。", "category": "products", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.title", "english": "Quality Approval Required", "chinese": "需要质量审批", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.description", "english": "This work order cannot be completed until all required quality inspections are approved.", "chinese": "此工单需要完成所有必需的质量检验审批后才能完成。", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.work_order_info", "english": "Work Order Information", "chinese": "工单信息", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.inspections_status", "english": "Quality Inspections Status", "chinese": "质量检验状态", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.no_inspections", "english": "No quality inspections found. Inspection may need to be created.", "chinese": "未找到质量检验记录。可能需要创建检验。", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.completion_status", "english": "Completion Status", "chinese": "完成状态", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.can_complete", "english": "All quality requirements met. Work order can be completed.", "chinese": "所有质量要求已满足。工单可以完成。", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.cannot_complete", "english": "Quality approval required before completion.", "chinese": "完成前需要质量审批。", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.pending_inspections", "english": "Pending Inspections", "chinese": "待处理检验", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.complete_inspections_first", "english": "Please complete all pending quality inspections before proceeding.", "chinese": "请先完成所有待处理的质量检验。", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.go_to_quality_control", "english": "Go to Quality Control", "chinese": "前往质量控制", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "work_orders.quality_gate.complete_work_order", "english": "Complete Work Order", "chinese": "完成工单", "category": "work_orders", "hasTranslation": true, "needsReview": false}, {"key": "quality.inspector", "english": "Inspector", "chinese": "检验员", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.inspection_types.final", "english": "Final Inspection", "chinese": "最终检验", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.inspection_types.incoming", "english": "Incoming Inspection", "chinese": "来料检验", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.inspection_types.in_process", "english": "In-Process Inspection", "chinese": "过程检验", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.status.passed", "english": "Passed", "chinese": "通过", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "quality.status.failed", "english": "Failed", "chinese": "失败", "category": "quality", "hasTranslation": true, "needsReview": false}, {"key": "common.processing", "english": "Processing...", "chinese": "处理中...", "category": "common", "hasTranslation": true, "needsReview": false}]