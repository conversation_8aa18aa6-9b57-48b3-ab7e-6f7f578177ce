#!/usr/bin/env node

/**
 * Manufacturing ERP i18n Quality Check for Vercel
 * 
 * Integrates i18n quality checks into Vercel build process without
 * disrupting deployment. Runs as part of build command.
 * 
 * ZERO BREAKING CHANGES: Non-blocking quality checks with reporting.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Vercel-specific configuration
const VERCEL_CONFIG = {
  // Never fail Vercel builds
  policy: 'warn',
  
  // Lightweight checks for build performance
  maxNewStrings: 30,
  timeoutMs: 30000, // 30 seconds max
  
  // Vercel environment detection
  isVercelBuild: !!process.env.VERCEL,
  isPreview: process.env.VERCEL_ENV === 'preview',
  isProduction: process.env.VERCEL_ENV === 'production',
  
  // Build context
  commitSha: process.env.VERCEL_GIT_COMMIT_SHA,
  branch: process.env.VERCEL_GIT_COMMIT_REF,
  buildUrl: process.env.VERCEL_URL
};

// Lightweight i18n check for Vercel
async function runVercelI18nCheck() {
  console.log('🌐 Manufacturing ERP i18n Quality Check (Vercel)');
  console.log('⚠️  ZERO BREAKING CHANGES: Non-blocking quality checks\n');
  
  if (!VERCEL_CONFIG.isVercelBuild) {
    console.log('ℹ️  Not running in Vercel environment - skipping');
    return { success: true, skipped: true };
  }
  
  const startTime = Date.now();
  const results = {
    success: true,
    environment: VERCEL_CONFIG.isProduction ? 'production' : 'preview',
    branch: VERCEL_CONFIG.branch,
    commitSha: VERCEL_CONFIG.commitSha?.substring(0, 8),
    checks: {
      detection: null,
      validation: null
    },
    summary: {
      newStrings: 0,
      issues: [],
      warnings: []
    }
  };
  
  try {
    // Quick environment setup
    console.log('🔧 Setting up i18n environment...');
    
    const requiredDirs = ['i18n-temp', 'i18n-cicd'];
    requiredDirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
    
    // 1. Quick hardcoded string detection
    console.log('🔍 Running hardcoded string detection...');
    
    try {
      const detectionCmd = 'node scripts/i18n-auto-detect.js';
      const detectionOutput = execSync(detectionCmd, {
        encoding: 'utf8',
        timeout: VERCEL_CONFIG.timeoutMs,
        stdio: 'pipe'
      });
      
      // Parse detection results
      const newStringsMatch = detectionOutput.match(/(\d+)\s+new\s+hardcoded\s+strings/i);
      const newStrings = newStringsMatch ? parseInt(newStringsMatch[1]) : 0;
      
      results.checks.detection = {
        success: true,
        newStrings,
        output: detectionOutput.substring(0, 500) // Truncate for Vercel logs
      };
      
      results.summary.newStrings = newStrings;
      
      if (newStrings > VERCEL_CONFIG.maxNewStrings) {
        results.summary.warnings.push(`High number of new hardcoded strings: ${newStrings}`);
      }
      
      console.log(`   ✅ Detection completed: ${newStrings} new strings found`);
      
    } catch (error) {
      results.checks.detection = {
        success: false,
        error: error.message.substring(0, 200)
      };
      results.summary.warnings.push('String detection failed');
      console.log('   ⚠️  Detection failed (non-blocking)');
    }
    
    // 2. Quick validation check (only if pending translations exist)
    console.log('🔍 Checking for pending translations...');
    
    const pendingDir = 'i18n-parallel/pending';
    if (fs.existsSync(pendingDir)) {
      const pendingFiles = fs.readdirSync(pendingDir).filter(f => f.endsWith('.json'));
      
      if (pendingFiles.length > 0) {
        console.log(`   Found ${pendingFiles.length} pending translation files`);
        
        try {
          const validationCmd = `node scripts/i18n-translation-validator.js validate ${pendingFiles[0]}`;
          const validationOutput = execSync(validationCmd, {
            encoding: 'utf8',
            timeout: VERCEL_CONFIG.timeoutMs / 2,
            stdio: 'pipe'
          });
          
          results.checks.validation = {
            success: true,
            filesChecked: pendingFiles.length,
            output: validationOutput.substring(0, 300)
          };
          
          console.log(`   ✅ Validation completed: ${pendingFiles.length} files checked`);
          
        } catch (error) {
          results.checks.validation = {
            success: false,
            error: error.message.substring(0, 200)
          };
          results.summary.warnings.push('Translation validation failed');
          console.log('   ⚠️  Validation failed (non-blocking)');
        }
      } else {
        console.log('   ℹ️  No pending translations to validate');
      }
    } else {
      console.log('   ℹ️  No pending translations directory');
    }
    
    // Generate lightweight report
    const executionTime = Date.now() - startTime;
    const report = {
      timestamp: new Date().toISOString(),
      platform: 'vercel',
      environment: results.environment,
      executionTime,
      results,
      summary: {
        status: results.summary.issues.length === 0 ? 'passed' : 'attention-needed',
        newStrings: results.summary.newStrings,
        warnings: results.summary.warnings.length,
        recommendations: generateVercelRecommendations(results)
      }
    };
    
    // Save compact report
    const reportFile = `i18n-cicd/vercel-report-${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    // Display summary
    console.log('\n📊 VERCEL I18N CHECK SUMMARY');
    console.log('='.repeat(40));
    console.log(`Status: ${report.summary.status.toUpperCase()}`);
    console.log(`New Strings: ${report.summary.newStrings}`);
    console.log(`Warnings: ${report.summary.warnings}`);
    console.log(`Execution Time: ${executionTime}ms`);
    
    if (report.summary.recommendations.length > 0) {
      console.log('\n🎯 RECOMMENDATIONS:');
      report.summary.recommendations.forEach(rec => console.log(`   - ${rec}`));
    }
    
    console.log(`\n📋 Report saved: ${reportFile}`);
    console.log('✅ i18n quality check completed successfully');
    console.log('🚀 Vercel build continues normally\n');
    
    return {
      success: true,
      report,
      executionTime
    };
    
  } catch (error) {
    console.error('❌ Vercel i18n check failed:', error.message);
    console.log('🚀 Build continues normally (non-blocking)\n');
    
    return {
      success: false,
      error: error.message,
      executionTime: Date.now() - startTime
    };
  }
}

// Generate Vercel-specific recommendations
function generateVercelRecommendations(results) {
  const recommendations = [];
  
  if (results.summary.newStrings > 20) {
    recommendations.push('Consider processing detected hardcoded strings with i18n workflow');
  }
  
  if (results.checks.detection && !results.checks.detection.success) {
    recommendations.push('Review string detection issues in development environment');
  }
  
  if (results.checks.validation && !results.checks.validation.success) {
    recommendations.push('Address translation validation issues before deployment');
  }
  
  if (results.summary.newStrings === 0) {
    recommendations.push('Excellent! No new hardcoded strings detected');
  }
  
  if (results.summary.warnings.length === 0) {
    recommendations.push('All i18n quality checks passed - continue current practices');
  }
  
  return recommendations;
}

// Integration with Vercel build process
function integrateWithVercelBuild() {
  // This function can be called from package.json build script
  // Example: "build": "node vercel-i18n-check.js && next build"
  
  return runVercelI18nCheck()
    .then(result => {
      if (result.success) {
        console.log('✅ i18n quality check passed - proceeding with build');
        process.exit(0);
      } else {
        console.log('⚠️  i18n quality check had issues - build continues anyway');
        process.exit(0); // Never fail Vercel builds
      }
    })
    .catch(error => {
      console.error('❌ i18n quality check error:', error.message);
      console.log('🚀 Build continues normally (non-blocking)');
      process.exit(0); // Never fail Vercel builds
    });
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'check':
      runVercelI18nCheck();
      break;
      
    case 'integrate':
      integrateWithVercelBuild();
      break;
      
    case 'info':
      console.log('📊 Vercel Environment Info:');
      console.log(`   Is Vercel Build: ${VERCEL_CONFIG.isVercelBuild}`);
      console.log(`   Environment: ${process.env.VERCEL_ENV || 'local'}`);
      console.log(`   Branch: ${VERCEL_CONFIG.branch || 'unknown'}`);
      console.log(`   Commit: ${VERCEL_CONFIG.commitSha || 'unknown'}`);
      break;
      
    default:
      console.log('📖 Manufacturing ERP i18n Quality Check for Vercel');
      console.log('');
      console.log('Commands:');
      console.log('  check     - Run i18n quality check');
      console.log('  integrate - Integrate with Vercel build process');
      console.log('  info      - Show Vercel environment info');
      console.log('');
      console.log('Integration:');
      console.log('  Add to package.json build script:');
      console.log('  "build": "node vercel-i18n-check.js integrate && next build"');
      console.log('');
      console.log('Features:');
      console.log('  - Non-blocking quality checks');
      console.log('  - Fast execution (< 30 seconds)');
      console.log('  - Automatic environment detection');
      console.log('  - Comprehensive reporting');
  }
}

module.exports = { runVercelI18nCheck, integrateWithVercelBuild };
