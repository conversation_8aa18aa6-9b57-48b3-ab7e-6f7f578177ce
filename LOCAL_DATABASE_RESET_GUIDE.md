# 🗄️ Manufacturing ERP - Local Database Reset Guide

## 📋 **OVERVIEW**

This guide provides step-by-step instructions to safely reset your local PostgreSQL database while preserving system structure and essential data. Follow this guide before comprehensive end-to-end testing.

**Target Database**: `localhost:5432/manufacturing_erp`
**Approach**: Zero breaking changes - preserve structure, clear business data only

---

## ⚠️ **CRITICAL SAFETY CHECKS**

### **Before You Begin**

1. **Verify Database Connection**:
   ```bash
   psql -h localhost -p 5432 -d manufacturing_erp -U [your_username]
   ```

2. **Confirm Local Environment**:
   ```sql
   SELECT current_database(), inet_server_addr();
   ```
   **Expected**: `manufacturing_erp`, `NULL` (indicates localhost)

3. **Backup Current State** (Optional):
   ```bash
   pg_dump -h localhost -p 5432 -d manufacturing_erp > backup_before_reset.sql
   ```

---

## 🔧 **STEP 1: EXECUTE DATABASE RESET**

### **Method 1: Using SQL Script (Recommended)**

1. **Navigate to Project Root**:
   ```bash
   cd /Users/<USER>/V0-Silk-Jhon
   ```

2. **Execute Reset Script**:
   ```bash
   psql -h localhost -p 5432 -d manufacturing_erp -f scripts/local-database-reset.sql
   ```

3. **Review Output**: Verify all steps completed successfully

### **Method 2: Manual Execution**

1. **Connect to Database**:
   ```bash
   psql -h localhost -p 5432 -d manufacturing_erp
   ```

2. **Copy and Execute** the SQL commands from `scripts/local-database-reset.sql`

---

## ✅ **STEP 2: VERIFICATION**

### **2.1 Verify Business Data Cleared**

Execute this verification query:

```sql
-- Check all business tables are empty
SELECT 
  'customers' as table_name, COUNT(*) as record_count FROM customers
UNION ALL SELECT 'suppliers', COUNT(*) FROM suppliers
UNION ALL SELECT 'products', COUNT(*) FROM products
UNION ALL SELECT 'samples', COUNT(*) FROM samples
UNION ALL SELECT 'sales_contracts', COUNT(*) FROM sales_contracts
UNION ALL SELECT 'purchase_contracts', COUNT(*) FROM purchase_contracts
UNION ALL SELECT 'work_orders', COUNT(*) FROM work_orders
UNION ALL SELECT 'quality_inspections', COUNT(*) FROM quality_inspections
UNION ALL SELECT 'stock_lots', COUNT(*) FROM stock_lots
UNION ALL SELECT 'shipments', COUNT(*) FROM shipments
UNION ALL SELECT 'declarations', COUNT(*) FROM declarations
UNION ALL SELECT 'ar_invoices', COUNT(*) FROM ar_invoices
UNION ALL SELECT 'ap_invoices', COUNT(*) FROM ap_invoices
ORDER BY table_name;
```

**Expected Result**: All counts should be `0`

### **2.2 Verify System Data Preserved**

```sql
-- Check companies table still has data
SELECT COUNT(*) as company_count, 
       string_agg(name, ', ') as company_names 
FROM companies;
```

**Expected Result**: Should show your company data (NOT 0)

### **2.3 Verify Database Structure Intact**

```sql
-- Check table structure is preserved
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE'
ORDER BY table_name;
```

**Expected Result**: All Manufacturing ERP tables should be listed

---

## 🚀 **STEP 3: START LOCAL DEVELOPMENT SERVER**

### **3.1 Start Development Environment**

```bash
# Navigate to project root
cd /Users/<USER>/V0-Silk-Jhon

# Start development server
npm run dev
```

### **3.2 Verify Local Application**

1. **Open Browser**: http://localhost:3000
2. **Login**: Use your Auth0 credentials
3. **Check Dashboard**: Should show all KPIs as "0"
4. **Verify Modules**: All modules should be accessible but empty

---

## 📊 **STEP 4: EXPECTED CLEAN STATE**

After successful reset, your local application should show:

### **4.1 Dashboard Metrics**
```
Customers: 0
Suppliers: 0
Products: 0
Samples: 0
Sales Contracts: 0
Work Orders: 0
Quality Inspections: 0
Shipments: 0
Export Declarations: 0
AR Invoices: 0
```

### **4.2 Financial Metrics**
```
Total Revenue: $0
Total Expenses: $0
Profit/Loss: $0
Cash Flow: $0
Collection Rate: 0%
```

### **4.3 Module States**
- **All Tables**: Show "No records found"
- **All Forms**: Ready for new data entry
- **All Dropdowns**: Empty (customers, suppliers, products)
- **All Analytics**: Zero values

---

## 🧪 **STEP 5: BEGIN COMPREHENSIVE TESTING**

### **5.1 Follow Testing Guide**

Now you're ready to use the comprehensive testing guide:

```bash
# Open the testing guide
open COMPREHENSIVE_ERP_TESTING_GUIDE.md
```

### **5.2 Testing Sequence**

1. **Start with Section 2**: Fresh Start Setup
2. **Create Foundation Data**: Customer → Supplier → Product
3. **Follow Complete Workflow**: Sample → Contract → Work Order → Quality → Inventory → Shipping → Invoice
4. **Verify Integrations**: Check data flows between modules
5. **Validate Metrics**: Confirm KPIs update correctly

---

## 🔍 **TROUBLESHOOTING**

### **Common Issues**

#### **Issue 1: Foreign Key Constraint Errors**
```
ERROR: update or delete on table "..." violates foreign key constraint
```

**Solution**: Tables weren't cleared in correct dependency order
```sql
-- Add CASCADE to truncate commands
TRUNCATE TABLE table_name CASCADE;
```

#### **Issue 2: Permission Denied**
```
ERROR: permission denied for table ...
```

**Solution**: Ensure you have proper database permissions
```bash
# Connect as database owner
psql -h localhost -p 5432 -d manufacturing_erp -U postgres
```

#### **Issue 3: Database Connection Failed**
```
psql: error: connection to server at "localhost" ... failed
```

**Solution**: Ensure PostgreSQL is running
```bash
# Start PostgreSQL (macOS with Homebrew)
brew services start postgresql

# Check status
brew services list | grep postgresql
```

#### **Issue 4: Companies Table Empty After Reset**
```
Company count: 0
```

**Solution**: This indicates the companies table was accidentally cleared
```sql
-- You'll need to recreate your company record or restore from backup
-- Check if you have a backup file to restore
```

---

## 📋 **RESET COMPLETION CHECKLIST**

- [ ] Database connection verified (localhost)
- [ ] Reset script executed successfully
- [ ] All business tables show 0 records
- [ ] Companies table still has data
- [ ] Database structure intact (all tables exist)
- [ ] Local development server starts without errors
- [ ] Application loads with empty dashboard
- [ ] All modules accessible but empty
- [ ] Ready to begin comprehensive testing

---

## 🎯 **SUCCESS CRITERIA**

**✅ SUCCESSFUL RESET**:
- All business data cleared
- System data preserved
- Database structure intact
- Application runs without errors
- Ready for fresh testing

**❌ FAILED RESET**:
- Foreign key constraint errors
- Missing tables or structure
- Companies table empty
- Application won't start
- Database connection issues

---

## 📞 **NEXT STEPS**

After successful local database reset:

1. **Begin Testing**: Use `COMPREHENSIVE_ERP_TESTING_GUIDE.md`
2. **Create Test Data**: Follow the fresh start setup section
3. **Validate Workflow**: Complete the full business cycle
4. **Document Issues**: Note any problems for fixing
5. **Prepare for Production**: Once local testing passes, consider production reset

---

**Estimated Time**: 10-15 minutes for complete reset and verification

*This guide ensures a clean, safe database reset for comprehensive Manufacturing ERP testing while maintaining zero breaking changes approach.*
