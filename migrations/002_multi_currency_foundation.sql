-- Manufacturing ERP - Multi-Currency Foundation Migration
-- Phase 1B: Enhanced Financial Integration
-- 
-- This migration creates comprehensive multi-currency support for the Manufacturing ERP system.
-- Includes currency management, exchange rate tracking, and enhanced financial tables.
--
-- Author: Professional ERP Developer
-- Version: 1.0.0 - Phase 1B Multi-Currency Foundation

-- ============================================================================
-- CURRENCIES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS currencies (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    code TEXT NOT NULL, -- ISO 4217 currency code (USD, EUR, CNY, etc.)
    name TEXT NOT NULL, -- Full currency name
    symbol TEXT NOT NULL, -- Currency symbol ($, €, ¥, etc.)
    decimal_places INTEGER NOT NULL DEFAULT 2, -- Number of decimal places
    is_base_currency TEXT NOT NULL DEFAULT 'false' CHECK (is_base_currency IN ('true', 'false')),
    is_active TEXT NOT NULL DEFAULT 'true' CHECK (is_active IN ('true', 'false')),
    exchange_rate TEXT DEFAULT '1.0', -- Current exchange rate to base currency
    last_rate_update TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, code)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_currencies_company_id ON currencies(company_id);
CREATE INDEX IF NOT EXISTS idx_currencies_code ON currencies(code);
CREATE INDEX IF NOT EXISTS idx_currencies_base ON currencies(company_id, is_base_currency);
CREATE INDEX IF NOT EXISTS idx_currencies_active ON currencies(company_id, is_active);

-- ============================================================================
-- EXCHANGE RATE HISTORY TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS exchange_rate_history (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    from_currency_code TEXT NOT NULL,
    to_currency_code TEXT NOT NULL,
    exchange_rate TEXT NOT NULL, -- Stored as string for precision
    effective_date TEXT NOT NULL, -- ISO date string
    rate_source TEXT DEFAULT 'manual', -- manual, api, bank, etc.
    rate_type TEXT DEFAULT 'spot' CHECK (rate_type IN ('spot', 'forward', 'average')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_exchange_rate_history_company_id ON exchange_rate_history(company_id);
CREATE INDEX IF NOT EXISTS idx_exchange_rate_history_currencies ON exchange_rate_history(from_currency_code, to_currency_code);
CREATE INDEX IF NOT EXISTS idx_exchange_rate_history_date ON exchange_rate_history(effective_date);
CREATE INDEX IF NOT EXISTS idx_exchange_rate_history_source ON exchange_rate_history(rate_source);

-- ============================================================================
-- CURRENCY CONVERSION CACHE TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS currency_conversion_cache (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    from_currency_code TEXT NOT NULL,
    to_currency_code TEXT NOT NULL,
    exchange_rate TEXT NOT NULL,
    amount_from TEXT NOT NULL,
    amount_to TEXT NOT NULL,
    conversion_date TEXT NOT NULL, -- ISO date string
    cache_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, from_currency_code, to_currency_code, amount_from, conversion_date)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_currency_conversion_cache_company_id ON currency_conversion_cache(company_id);
CREATE INDEX IF NOT EXISTS idx_currency_conversion_cache_currencies ON currency_conversion_cache(from_currency_code, to_currency_code);
CREATE INDEX IF NOT EXISTS idx_currency_conversion_cache_expires ON currency_conversion_cache(cache_expires_at);

-- ============================================================================
-- FINANCIAL TRANSACTIONS TABLE (NEW)
-- ============================================================================
CREATE TABLE IF NOT EXISTS financial_transactions (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('ar_invoice', 'ap_invoice', 'payment', 'adjustment', 'exchange_gain_loss')),
    reference_id TEXT, -- ID of the related record (invoice, payment, etc.)
    reference_type TEXT, -- Type of the related record
    account_type TEXT NOT NULL CHECK (account_type IN ('receivable', 'payable', 'cash', 'bank', 'expense', 'revenue')),
    
    -- Amount information
    amount TEXT NOT NULL, -- Transaction amount in original currency
    currency_code TEXT NOT NULL DEFAULT 'USD',
    base_amount TEXT, -- Amount converted to base currency
    base_currency_code TEXT, -- Company's base currency
    exchange_rate TEXT DEFAULT '1.0', -- Exchange rate used for conversion
    
    -- Transaction details
    transaction_date TEXT NOT NULL, -- ISO date string
    description TEXT,
    notes TEXT,
    
    -- Status and workflow
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'reconciled', 'cancelled')),
    created_by TEXT NOT NULL,
    approved_by TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_financial_transactions_company_id ON financial_transactions(company_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_type ON financial_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_reference ON financial_transactions(reference_id, reference_type);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_account ON financial_transactions(account_type);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_currency ON financial_transactions(currency_code);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_date ON financial_transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_status ON financial_transactions(status);

-- ============================================================================
-- CURRENCY EXPOSURE TRACKING TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS currency_exposure (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    currency_code TEXT NOT NULL,
    exposure_type TEXT NOT NULL CHECK (exposure_type IN ('receivable', 'payable', 'cash', 'inventory', 'contract')),
    
    -- Exposure amounts
    total_amount TEXT NOT NULL DEFAULT '0', -- Total exposure in foreign currency
    base_amount TEXT NOT NULL DEFAULT '0', -- Equivalent in base currency
    unrealized_gain_loss TEXT DEFAULT '0', -- Unrealized gain/loss
    
    -- Risk metrics
    risk_level TEXT DEFAULT 'medium' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    hedge_ratio TEXT DEFAULT '0', -- Percentage hedged (0-1)
    
    -- Tracking information
    calculation_date TEXT NOT NULL, -- ISO date string
    next_revaluation_date TEXT, -- When to recalculate
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, currency_code, exposure_type, calculation_date)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_currency_exposure_company_id ON currency_exposure(company_id);
CREATE INDEX IF NOT EXISTS idx_currency_exposure_currency ON currency_exposure(currency_code);
CREATE INDEX IF NOT EXISTS idx_currency_exposure_type ON currency_exposure(exposure_type);
CREATE INDEX IF NOT EXISTS idx_currency_exposure_risk ON currency_exposure(risk_level);
CREATE INDEX IF NOT EXISTS idx_currency_exposure_date ON currency_exposure(calculation_date);

-- ============================================================================
-- UPDATE TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- ============================================================================

-- Currencies update trigger
CREATE OR REPLACE FUNCTION update_currencies_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_currencies_updated_at
    BEFORE UPDATE ON currencies
    FOR EACH ROW
    EXECUTE FUNCTION update_currencies_updated_at();

-- Financial Transactions update trigger
CREATE OR REPLACE FUNCTION update_financial_transactions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_financial_transactions_updated_at
    BEFORE UPDATE ON financial_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_financial_transactions_updated_at();

-- Currency Exposure update trigger
CREATE OR REPLACE FUNCTION update_currency_exposure_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_currency_exposure_updated_at
    BEFORE UPDATE ON currency_exposure
    FOR EACH ROW
    EXECUTE FUNCTION update_currency_exposure_updated_at();

-- ============================================================================
-- SAMPLE CURRENCY DATA FOR MANUFACTURING ERP
-- ============================================================================

-- Note: This section will be populated after ensuring proper company data exists
-- Sample currencies for export manufacturing companies

/*
-- Sample base currencies for different companies
INSERT INTO currencies (
    id, company_id, code, name, symbol, decimal_places, is_base_currency, is_active, exchange_rate
) VALUES 
    ('curr-usd-001', 'company-1', 'USD', 'US Dollar', '$', 2, 'true', 'true', '1.0'),
    ('curr-eur-001', 'company-1', 'EUR', 'Euro', '€', 2, 'false', 'true', '0.85'),
    ('curr-cny-001', 'company-1', 'CNY', 'Chinese Yuan', '¥', 2, 'false', 'true', '7.25'),
    ('curr-gbp-001', 'company-1', 'GBP', 'British Pound', '£', 2, 'false', 'true', '0.75'),
    ('curr-jpy-001', 'company-1', 'JPY', 'Japanese Yen', '¥', 0, 'false', 'true', '150.0')
ON CONFLICT (company_id, code) DO NOTHING;

-- Sample exchange rate history
INSERT INTO exchange_rate_history (
    id, company_id, from_currency_code, to_currency_code, exchange_rate, effective_date, rate_source
) VALUES 
    ('exr-001', 'company-1', 'EUR', 'USD', '1.18', '2025-01-01', 'api'),
    ('exr-002', 'company-1', 'CNY', 'USD', '0.138', '2025-01-01', 'api'),
    ('exr-003', 'company-1', 'GBP', 'USD', '1.33', '2025-01-01', 'api'),
    ('exr-004', 'company-1', 'JPY', 'USD', '0.0067', '2025-01-01', 'api')
ON CONFLICT DO NOTHING;
*/

-- ============================================================================
-- MIGRATION COMPLETION
-- ============================================================================

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'Multi-Currency Foundation Migration Completed Successfully';
    RAISE NOTICE 'Tables Created: currencies, exchange_rate_history, currency_conversion_cache, financial_transactions, currency_exposure';
    RAISE NOTICE 'Indexes Created: Performance indexes for all tables';
    RAISE NOTICE 'Triggers Created: Automatic timestamp update triggers';
    RAISE NOTICE 'Ready for Multi-Currency Operations';
END $$;
