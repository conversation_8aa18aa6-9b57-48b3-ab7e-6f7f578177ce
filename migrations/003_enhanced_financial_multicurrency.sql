-- Manufacturing ERP - Enhanced Financial Multi-Currency Migration
-- Phase 1B: Export Financial Features
-- 
-- This migration enhances existing financial tables with comprehensive multi-currency support,
-- adds export-specific financial tracking, and creates professional financial analytics.
--
-- Author: Professional ERP Developer
-- Version: 1.0.0 - Phase 1B Export Financial Features

-- ============================================================================
-- ENHANCE EXISTING FINANCIAL TABLES WITH MULTI-CURRENCY SUPPORT
-- ============================================================================

-- Enhance AR Invoices with multi-currency support
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS base_amount TEXT; -- Amount in company base currency
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS base_currency_code TEXT; -- Company base currency
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS exchange_rate TEXT DEFAULT '1.0'; -- Exchange rate used
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS exchange_rate_date TEXT; -- Date of exchange rate
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS currency_gain_loss TEXT DEFAULT '0'; -- Realized gain/loss
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS export_declaration_id TEXT; -- Link to export declaration

-- Enhance AP Invoices with multi-currency support
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS base_amount TEXT; -- Amount in company base currency
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS base_currency_code TEXT; -- Company base currency
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS exchange_rate TEXT DEFAULT '1.0'; -- Exchange rate used
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS exchange_rate_date TEXT; -- Date of exchange rate
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS currency_gain_loss TEXT DEFAULT '0'; -- Realized gain/loss
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS import_reference TEXT; -- Import/customs reference

-- Enhance Sales Contracts with multi-currency support
ALTER TABLE sales_contracts ADD COLUMN IF NOT EXISTS base_amount TEXT; -- Total in base currency
ALTER TABLE sales_contracts ADD COLUMN IF NOT EXISTS base_currency_code TEXT; -- Company base currency
ALTER TABLE sales_contracts ADD COLUMN IF NOT EXISTS exchange_rate TEXT DEFAULT '1.0'; -- Exchange rate used
ALTER TABLE sales_contracts ADD COLUMN IF NOT EXISTS currency_risk_level TEXT DEFAULT 'medium'; -- Currency risk assessment

-- Enhance Purchase Contracts with multi-currency support
ALTER TABLE purchase_contracts ADD COLUMN IF NOT EXISTS base_amount TEXT; -- Total in base currency
ALTER TABLE purchase_contracts ADD COLUMN IF NOT EXISTS base_currency_code TEXT; -- Company base currency
ALTER TABLE purchase_contracts ADD COLUMN IF NOT EXISTS exchange_rate TEXT DEFAULT '1.0'; -- Exchange rate used
ALTER TABLE purchase_contracts ADD COLUMN IF NOT EXISTS currency_risk_level TEXT DEFAULT 'medium'; -- Currency risk assessment

-- ============================================================================
-- EXPORT FINANCIAL ANALYTICS TABLES
-- ============================================================================

-- Export Revenue Analytics
CREATE TABLE IF NOT EXISTS export_revenue_analytics (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Time period
    period_type TEXT NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    period_start TEXT NOT NULL, -- ISO date string
    period_end TEXT NOT NULL, -- ISO date string
    
    -- Revenue metrics by currency
    total_revenue_usd TEXT NOT NULL DEFAULT '0',
    total_revenue_local TEXT NOT NULL DEFAULT '0',
    local_currency_code TEXT NOT NULL DEFAULT 'USD',
    
    -- Export-specific metrics
    export_volume_containers INTEGER DEFAULT 0,
    export_volume_weight TEXT DEFAULT '0', -- Total weight in kg
    export_destinations_count INTEGER DEFAULT 0,
    top_export_destination TEXT,
    
    -- Currency breakdown
    currency_breakdown JSONB, -- {"USD": 50000, "EUR": 30000, "CNY": 20000}
    exchange_rate_impact TEXT DEFAULT '0', -- Impact of exchange rate changes
    
    -- Performance metrics
    average_order_value TEXT DEFAULT '0',
    revenue_per_container TEXT DEFAULT '0',
    profit_margin_percentage TEXT DEFAULT '0',
    
    -- Risk metrics
    currency_exposure_risk TEXT DEFAULT 'medium' CHECK (currency_exposure_risk IN ('low', 'medium', 'high', 'critical')),
    concentration_risk TEXT DEFAULT 'medium' CHECK (concentration_risk IN ('low', 'medium', 'high', 'critical')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_export_revenue_analytics_company_id ON export_revenue_analytics(company_id);
CREATE INDEX IF NOT EXISTS idx_export_revenue_analytics_period ON export_revenue_analytics(period_type, period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_export_revenue_analytics_currency ON export_revenue_analytics(local_currency_code);

-- ============================================================================
-- CONTAINER SHIPPING COST ALLOCATION TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS container_cost_allocation (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Container information
    container_id TEXT NOT NULL, -- Reference to shipping container
    container_type TEXT NOT NULL CHECK (container_type IN ('20ft', '40ft', '40ft_hc', '45ft')),
    container_number TEXT,
    
    -- Shipment details
    shipment_id TEXT, -- Reference to shipment
    export_declaration_id TEXT, -- Reference to export declaration
    sales_contract_id TEXT REFERENCES sales_contracts(id),
    
    -- Cost breakdown
    base_shipping_cost TEXT NOT NULL DEFAULT '0',
    fuel_surcharge TEXT DEFAULT '0',
    port_charges TEXT DEFAULT '0',
    customs_fees TEXT DEFAULT '0',
    insurance_cost TEXT DEFAULT '0',
    handling_fees TEXT DEFAULT '0',
    documentation_fees TEXT DEFAULT '0',
    other_charges TEXT DEFAULT '0',
    
    -- Currency information
    cost_currency TEXT NOT NULL DEFAULT 'USD',
    base_cost_amount TEXT, -- Total cost in base currency
    base_currency_code TEXT, -- Company base currency
    exchange_rate TEXT DEFAULT '1.0',
    
    -- Allocation details
    total_container_weight TEXT DEFAULT '0', -- Total weight in kg
    total_container_volume TEXT DEFAULT '0', -- Total volume in m³
    utilization_percentage TEXT DEFAULT '0', -- Container utilization %
    
    -- Cost allocation method
    allocation_method TEXT DEFAULT 'weight' CHECK (allocation_method IN ('weight', 'volume', 'value', 'equal')),
    allocated_products JSONB, -- Product allocation details
    
    -- Dates
    shipping_date TEXT NOT NULL, -- ISO date string
    arrival_date TEXT, -- ISO date string
    cost_date TEXT NOT NULL, -- Cost calculation date
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_container_cost_allocation_company_id ON container_cost_allocation(company_id);
CREATE INDEX IF NOT EXISTS idx_container_cost_allocation_container ON container_cost_allocation(container_id);
CREATE INDEX IF NOT EXISTS idx_container_cost_allocation_shipment ON container_cost_allocation(shipment_id);
CREATE INDEX IF NOT EXISTS idx_container_cost_allocation_contract ON container_cost_allocation(sales_contract_id);
CREATE INDEX IF NOT EXISTS idx_container_cost_allocation_date ON container_cost_allocation(shipping_date);

-- ============================================================================
-- CASH FLOW FORECASTING TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS cash_flow_forecasts (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Forecast period
    forecast_date TEXT NOT NULL, -- ISO date string
    forecast_type TEXT NOT NULL CHECK (forecast_type IN ('daily', 'weekly', 'monthly')),
    forecast_horizon_days INTEGER NOT NULL, -- How many days ahead
    
    -- Cash inflows (in base currency)
    expected_ar_collections TEXT DEFAULT '0',
    expected_contract_payments TEXT DEFAULT '0',
    expected_export_receipts TEXT DEFAULT '0',
    other_inflows TEXT DEFAULT '0',
    total_inflows TEXT DEFAULT '0',
    
    -- Cash outflows (in base currency)
    expected_ap_payments TEXT DEFAULT '0',
    expected_payroll TEXT DEFAULT '0',
    expected_operating_expenses TEXT DEFAULT '0',
    expected_shipping_costs TEXT DEFAULT '0',
    expected_raw_material_costs TEXT DEFAULT '0',
    other_outflows TEXT DEFAULT '0',
    total_outflows TEXT DEFAULT '0',
    
    -- Net cash flow
    net_cash_flow TEXT DEFAULT '0',
    cumulative_cash_flow TEXT DEFAULT '0',
    
    -- Currency risk factors
    currency_risk_adjustment TEXT DEFAULT '0', -- Adjustment for currency risk
    exchange_rate_sensitivity JSONB, -- Sensitivity analysis by currency
    
    -- Confidence and risk
    confidence_level TEXT DEFAULT 'medium' CHECK (confidence_level IN ('low', 'medium', 'high')),
    risk_factors JSONB, -- Array of risk factors
    
    -- Scenario analysis
    best_case_scenario TEXT DEFAULT '0',
    worst_case_scenario TEXT DEFAULT '0',
    most_likely_scenario TEXT DEFAULT '0',
    
    -- Metadata
    created_by TEXT NOT NULL,
    forecast_method TEXT DEFAULT 'historical' CHECK (forecast_method IN ('historical', 'regression', 'manual')),
    last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_cash_flow_forecasts_company_id ON cash_flow_forecasts(company_id);
CREATE INDEX IF NOT EXISTS idx_cash_flow_forecasts_date ON cash_flow_forecasts(forecast_date);
CREATE INDEX IF NOT EXISTS idx_cash_flow_forecasts_type ON cash_flow_forecasts(forecast_type);
CREATE INDEX IF NOT EXISTS idx_cash_flow_forecasts_horizon ON cash_flow_forecasts(forecast_horizon_days);

-- ============================================================================
-- CURRENCY RISK ASSESSMENT TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS currency_risk_assessments (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Assessment details
    assessment_date TEXT NOT NULL, -- ISO date string
    assessment_type TEXT NOT NULL CHECK (assessment_type IN ('daily', 'weekly', 'monthly', 'ad_hoc')),
    base_currency_code TEXT NOT NULL,
    
    -- Risk metrics by currency
    currency_exposures JSONB NOT NULL, -- {"EUR": {"exposure": 100000, "risk": "high"}}
    total_exposure_base_currency TEXT DEFAULT '0',
    
    -- Risk calculations
    value_at_risk_1day TEXT DEFAULT '0', -- 1-day VaR
    value_at_risk_1week TEXT DEFAULT '0', -- 1-week VaR
    value_at_risk_1month TEXT DEFAULT '0', -- 1-month VaR
    
    -- Risk levels
    overall_risk_level TEXT DEFAULT 'medium' CHECK (overall_risk_level IN ('low', 'medium', 'high', 'critical')),
    highest_risk_currency TEXT,
    risk_concentration_ratio TEXT DEFAULT '0', -- Concentration in single currency
    
    -- Hedging information
    hedged_exposure_percentage TEXT DEFAULT '0',
    unhedged_exposure TEXT DEFAULT '0',
    hedging_effectiveness TEXT DEFAULT '0',
    
    -- Recommendations
    risk_mitigation_recommendations JSONB, -- Array of recommendations
    hedging_recommendations JSONB, -- Hedging strategy recommendations
    
    -- Metadata
    created_by TEXT NOT NULL,
    assessment_method TEXT DEFAULT 'historical' CHECK (assessment_method IN ('historical', 'monte_carlo', 'parametric')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_currency_risk_assessments_company_id ON currency_risk_assessments(company_id);
CREATE INDEX IF NOT EXISTS idx_currency_risk_assessments_date ON currency_risk_assessments(assessment_date);
CREATE INDEX IF NOT EXISTS idx_currency_risk_assessments_risk ON currency_risk_assessments(overall_risk_level);
CREATE INDEX IF NOT EXISTS idx_currency_risk_assessments_currency ON currency_risk_assessments(base_currency_code);

-- ============================================================================
-- UPDATE TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- ============================================================================

-- Export Revenue Analytics update trigger
CREATE OR REPLACE FUNCTION update_export_revenue_analytics_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_export_revenue_analytics_updated_at
    BEFORE UPDATE ON export_revenue_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_export_revenue_analytics_updated_at();

-- Container Cost Allocation update trigger
CREATE OR REPLACE FUNCTION update_container_cost_allocation_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_container_cost_allocation_updated_at
    BEFORE UPDATE ON container_cost_allocation
    FOR EACH ROW
    EXECUTE FUNCTION update_container_cost_allocation_updated_at();

-- Currency Risk Assessments update trigger
CREATE OR REPLACE FUNCTION update_currency_risk_assessments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_currency_risk_assessments_updated_at
    BEFORE UPDATE ON currency_risk_assessments
    FOR EACH ROW
    EXECUTE FUNCTION update_currency_risk_assessments_updated_at();

-- ============================================================================
-- MIGRATION COMPLETION
-- ============================================================================

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'Enhanced Financial Multi-Currency Migration Completed Successfully';
    RAISE NOTICE 'Enhanced Tables: ar_invoices, ap_invoices, sales_contracts, purchase_contracts';
    RAISE NOTICE 'New Tables: export_revenue_analytics, container_cost_allocation, cash_flow_forecasts, currency_risk_assessments';
    RAISE NOTICE 'Indexes Created: Performance indexes for all new tables and columns';
    RAISE NOTICE 'Triggers Created: Automatic timestamp update triggers';
    RAISE NOTICE 'Ready for Export Financial Operations';
END $$;
