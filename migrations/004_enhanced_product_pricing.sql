-- ============================================================================
-- MANUFACTURING ERP - ENHANCED PRODUCT PRICING SYSTEM
-- Migration 004: Add comprehensive pricing fields to products table
-- 
-- ✅ ZERO BREAKING CHANGES APPROACH:
-- - All new fields are optional (NULL allowed)
-- - Existing 'price' field is preserved unchanged
-- - Backward compatibility maintained 100%
-- - Existing functionality continues to work
-- 
-- <AUTHOR> ERP Developer
-- @version 1.0.0 - Product Pricing Enhancement
-- @date 2025-01-06
-- ============================================================================

-- ✅ STEP 1: ADD NEW PRICING FIELDS TO PRODUCTS TABLE
-- All fields are optional to maintain backward compatibility
ALTER TABLE products ADD COLUMN IF NOT EXISTS base_price TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS cost_price TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS margin_percentage TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'USD';
ALTER TABLE products ADD COLUMN IF NOT EXISTS price_updated_at TIMESTAMP WITH TIME ZONE;

-- ✅ STEP 2: ADD PERFORMANCE INDEXES FOR PRICING QUERIES
-- Index for price-based queries and analytics
CREATE INDEX IF NOT EXISTS idx_products_base_price ON products(base_price) WHERE base_price IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_products_cost_price ON products(cost_price) WHERE cost_price IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_products_currency ON products(currency);
CREATE INDEX IF NOT EXISTS idx_products_price_updated ON products(price_updated_at);

-- ✅ STEP 3: CREATE PRICING AUDIT TABLE FOR PRICE CHANGE TRACKING
CREATE TABLE IF NOT EXISTS product_pricing_history (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    
    -- Price change details
    old_price TEXT,
    new_price TEXT,
    old_base_price TEXT,
    new_base_price TEXT,
    old_cost_price TEXT,
    new_cost_price TEXT,
    old_margin_percentage TEXT,
    new_margin_percentage TEXT,
    
    -- Change metadata
    change_reason TEXT, -- 'manual', 'contract_update', 'cost_adjustment', 'market_update'
    changed_by TEXT, -- User ID who made the change
    change_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ✅ STEP 4: ADD INDEXES FOR PRICING HISTORY
CREATE INDEX IF NOT EXISTS idx_pricing_history_company ON product_pricing_history(company_id);
CREATE INDEX IF NOT EXISTS idx_pricing_history_product ON product_pricing_history(product_id);
CREATE INDEX IF NOT EXISTS idx_pricing_history_date ON product_pricing_history(created_at);

-- ✅ STEP 5: MIGRATE EXISTING PRICE DATA (NON-DESTRUCTIVE)
-- Copy existing price values to base_price for products that have prices
UPDATE products 
SET 
    base_price = price,
    price_updated_at = created_at,
    currency = 'USD'
WHERE 
    price IS NOT NULL 
    AND price != '' 
    AND CAST(price AS DECIMAL) > 0
    AND base_price IS NULL; -- Only update if base_price is not already set

-- ✅ STEP 6: ADD COMMENTS FOR DOCUMENTATION
COMMENT ON COLUMN products.price IS 'Legacy price field - maintained for backward compatibility';
COMMENT ON COLUMN products.base_price IS 'Standard selling price for the product';
COMMENT ON COLUMN products.cost_price IS 'Cost basis used for margin calculations';
COMMENT ON COLUMN products.margin_percentage IS 'Target profit margin percentage';
COMMENT ON COLUMN products.currency IS 'Currency code for pricing (ISO 4217)';
COMMENT ON COLUMN products.price_updated_at IS 'Timestamp of last price update';

COMMENT ON TABLE product_pricing_history IS 'Audit trail for product price changes';

-- ✅ STEP 7: VERIFY MIGRATION SUCCESS
-- This query should return the count of products with pricing data
-- SELECT 
--     COUNT(*) as total_products,
--     COUNT(price) as products_with_legacy_price,
--     COUNT(base_price) as products_with_base_price,
--     COUNT(cost_price) as products_with_cost_price
-- FROM products;

-- ✅ MIGRATION COMPLETE
-- All existing functionality preserved
-- New pricing features available for implementation
-- Zero breaking changes achieved
