-- ============================================================================
-- MANUFACTURING ERP - PHASE 1 CRITICAL ENHANCEMENTS MIGRATION
-- ============================================================================
-- 
-- This migration implements Phase 1 critical enhancements for enterprise-grade
-- ERP functionality including stock transaction cost tracking and declaration
-- items value fields for customs compliance.
--
-- FEATURES:
-- ✅ Stock Transaction Cost Tracking - Accurate inventory valuation
-- ✅ Declaration Items Value Fields - Export customs compliance
-- ✅ Performance Indexes - Optimized query performance
-- ✅ Zero Breaking Changes - Backward compatible additive changes
--
-- <AUTHOR> ERP Developer
-- @version 1.0.0 - Phase 1 Critical Enhancements
-- @date 2024-01-XX
-- ============================================================================

-- ✅ STEP 1: ADD COST TRACKING FIELDS TO STOCK TRANSACTIONS
-- Enable accurate inventory valuation and cost accounting
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS unit_cost TEXT;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS total_value TEXT;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS cost_method TEXT DEFAULT 'standard';
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS cost_currency TEXT DEFAULT 'USD';

-- ✅ STEP 2: ADD VALUE DECLARATION FIELDS TO EXPORT DECLARATION ITEMS
-- Ensure customs compliance for export documentation
ALTER TABLE declaration_items ADD COLUMN IF NOT EXISTS unit_value TEXT;
ALTER TABLE declaration_items ADD COLUMN IF NOT EXISTS total_value TEXT;
ALTER TABLE declaration_items ADD COLUMN IF NOT EXISTS value_currency TEXT DEFAULT 'USD';
ALTER TABLE declaration_items ADD COLUMN IF NOT EXISTS value_method TEXT;

-- ✅ STEP 3: CREATE PERFORMANCE INDEXES FOR COST TRACKING
-- Optimize queries for inventory valuation and cost analysis
CREATE INDEX IF NOT EXISTS idx_stock_txns_unit_cost ON stock_txns(unit_cost) WHERE unit_cost IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_stock_txns_cost_method ON stock_txns(cost_method);
CREATE INDEX IF NOT EXISTS idx_stock_txns_cost_currency ON stock_txns(cost_currency);

-- ✅ STEP 4: CREATE PERFORMANCE INDEXES FOR VALUE DECLARATIONS
-- Optimize queries for customs compliance and export reporting
CREATE INDEX IF NOT EXISTS idx_declaration_items_total_value ON declaration_items(total_value) WHERE total_value IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_declaration_items_value_method ON declaration_items(value_method);
CREATE INDEX IF NOT EXISTS idx_declaration_items_value_currency ON declaration_items(value_currency);

-- ✅ STEP 5: ADD DOCUMENTATION COMMENTS
-- Document the purpose and usage of new fields
COMMENT ON COLUMN stock_txns.unit_cost IS 'Cost per unit at transaction time for accurate inventory valuation';
COMMENT ON COLUMN stock_txns.total_value IS 'Total transaction value (qty * unit_cost) for cost accounting';
COMMENT ON COLUMN stock_txns.cost_method IS 'Costing method: standard, fifo, lifo, average, actual';
COMMENT ON COLUMN stock_txns.cost_currency IS 'Currency code for cost values (ISO 4217)';

COMMENT ON COLUMN declaration_items.unit_value IS 'Declared value per unit for customs compliance';
COMMENT ON COLUMN declaration_items.total_value IS 'Total declared value (qty * unit_value) for export documentation';
COMMENT ON COLUMN declaration_items.value_currency IS 'Currency code for declared values (ISO 4217)';
COMMENT ON COLUMN declaration_items.value_method IS 'Value determination method: product_price, contract_price, declared, market_value';

-- ✅ STEP 6: POPULATE DEFAULT VALUES FOR EXISTING RECORDS
-- Ensure existing data has sensible defaults for new fields

-- Set default cost method for existing stock transactions
UPDATE stock_txns 
SET cost_method = 'standard', cost_currency = 'USD'
WHERE cost_method IS NULL OR cost_currency IS NULL;

-- Set default value currency for existing declaration items
UPDATE declaration_items 
SET value_currency = 'USD'
WHERE value_currency IS NULL;

-- ✅ STEP 7: VERIFY MIGRATION SUCCESS
-- These queries can be used to verify the migration completed successfully

-- Verify stock_txns schema changes
-- SELECT 
--     column_name, 
--     data_type, 
--     is_nullable, 
--     column_default
-- FROM information_schema.columns 
-- WHERE table_name = 'stock_txns' 
--   AND column_name IN ('unit_cost', 'total_value', 'cost_method', 'cost_currency')
-- ORDER BY column_name;

-- Verify declaration_items schema changes
-- SELECT 
--     column_name, 
--     data_type, 
--     is_nullable, 
--     column_default
-- FROM information_schema.columns 
-- WHERE table_name = 'declaration_items' 
--   AND column_name IN ('unit_value', 'total_value', 'value_currency', 'value_method')
-- ORDER BY column_name;

-- Verify indexes were created
-- SELECT 
--     indexname, 
--     tablename, 
--     indexdef
-- FROM pg_indexes 
-- WHERE tablename IN ('stock_txns', 'declaration_items')
--   AND indexname LIKE '%cost%' OR indexname LIKE '%value%'
-- ORDER BY tablename, indexname;

-- ✅ MIGRATION COMPLETE
-- Phase 1 Critical Enhancements successfully implemented
-- - Stock transaction cost tracking enabled
-- - Declaration items value fields added
-- - Performance indexes created
-- - Zero breaking changes maintained
-- - Backward compatibility preserved

-- ============================================================================
-- ROLLBACK INSTRUCTIONS (IF NEEDED)
-- ============================================================================
-- 
-- To rollback this migration (use with caution):
-- 
-- -- Drop indexes
-- DROP INDEX IF EXISTS idx_stock_txns_unit_cost;
-- DROP INDEX IF EXISTS idx_stock_txns_cost_method;
-- DROP INDEX IF EXISTS idx_stock_txns_cost_currency;
-- DROP INDEX IF EXISTS idx_declaration_items_total_value;
-- DROP INDEX IF EXISTS idx_declaration_items_value_method;
-- DROP INDEX IF EXISTS idx_declaration_items_value_currency;
-- 
-- -- Remove columns (WARNING: This will delete data)
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS unit_cost;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS total_value;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS cost_method;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS cost_currency;
-- ALTER TABLE declaration_items DROP COLUMN IF EXISTS unit_value;
-- ALTER TABLE declaration_items DROP COLUMN IF EXISTS total_value;
-- ALTER TABLE declaration_items DROP COLUMN IF EXISTS value_currency;
-- ALTER TABLE declaration_items DROP COLUMN IF EXISTS value_method;
-- 
-- ============================================================================
