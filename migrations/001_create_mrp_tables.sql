-- Manufacturing ERP - MRP System Database Migration
-- Phase 1A: Advanced MRP System Implementation
-- 
-- This migration creates the core MRP tables for demand forecasting,
-- procurement planning, supplier lead time management, and container optimization
--
-- Author: Professional ERP Developer
-- Version: 1.0.0 - Phase 1A MRP Implementation

-- ============================================================================
-- DEMAND FORECASTS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS demand_forecasts (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    forecast_period TEXT NOT NULL, -- "2025-Q1", "2025-02", "2025-W12"
    forecasted_demand TEXT NOT NULL, -- Stored as string for precision
    confidence_level TEXT NOT NULL DEFAULT 'medium' CHECK (confidence_level IN ('low', 'medium', 'high')),
    forecast_method TEXT NOT NULL DEFAULT 'pipeline' CHECK (forecast_method IN ('pipeline', 'historical', 'manual', 'hybrid')),
    base_data_source TEXT, -- JSON string with source data details
    seasonality_applied TEXT DEFAULT 'false' CHECK (seasonality_applied IN ('true', 'false')),
    trend_factor_applied TEXT DEFAULT '1.0',
    approval_status TEXT NOT NULL DEFAULT 'draft' CHECK (approval_status IN ('draft', 'pending', 'approved', 'rejected')),
    notes TEXT,
    created_by TEXT NOT NULL,
    approved_by TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_demand_forecasts_company_id ON demand_forecasts(company_id);
CREATE INDEX IF NOT EXISTS idx_demand_forecasts_product_id ON demand_forecasts(product_id);
CREATE INDEX IF NOT EXISTS idx_demand_forecasts_period ON demand_forecasts(forecast_period);
CREATE INDEX IF NOT EXISTS idx_demand_forecasts_status ON demand_forecasts(approval_status);
CREATE INDEX IF NOT EXISTS idx_demand_forecasts_created_at ON demand_forecasts(created_at);

-- ============================================================================
-- FORECAST PARAMETERS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS forecast_parameters (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    seasonality_factor TEXT DEFAULT '1.0',
    trend_factor TEXT DEFAULT '1.0',
    lead_time_buffer_days TEXT DEFAULT '14',
    safety_stock_percentage TEXT DEFAULT '0.15',
    container_optimization_enabled TEXT DEFAULT 'true' CHECK (container_optimization_enabled IN ('true', 'false')),
    preferred_container_size TEXT DEFAULT '40ft' CHECK (preferred_container_size IN ('20ft', '40ft', '40ft-hc', '45ft')),
    minimum_order_efficiency TEXT DEFAULT '0.8',
    historical_periods_to_analyze TEXT DEFAULT '12',
    outlier_detection_enabled TEXT DEFAULT 'true' CHECK (outlier_detection_enabled IN ('true', 'false')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, product_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_forecast_parameters_company_id ON forecast_parameters(company_id);
CREATE INDEX IF NOT EXISTS idx_forecast_parameters_product_id ON forecast_parameters(product_id);

-- ============================================================================
-- PROCUREMENT PLANS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS procurement_plans (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    raw_material_id TEXT NOT NULL REFERENCES raw_materials(id) ON DELETE CASCADE,
    demand_forecast_id TEXT REFERENCES demand_forecasts(id) ON DELETE SET NULL,
    planned_qty TEXT NOT NULL, -- Stored as string for precision
    target_date TEXT NOT NULL, -- ISO date string
    supplier_id TEXT REFERENCES suppliers(id) ON DELETE SET NULL,
    estimated_cost TEXT DEFAULT '0',
    estimated_lead_time TEXT DEFAULT '30',
    container_optimization TEXT, -- JSON string with optimization details
    priority TEXT NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'pending', 'approved', 'ordered', 'received')),
    notes TEXT,
    created_by TEXT NOT NULL,
    approved_by TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_procurement_plans_company_id ON procurement_plans(company_id);
CREATE INDEX IF NOT EXISTS idx_procurement_plans_raw_material_id ON procurement_plans(raw_material_id);
CREATE INDEX IF NOT EXISTS idx_procurement_plans_demand_forecast_id ON procurement_plans(demand_forecast_id);
CREATE INDEX IF NOT EXISTS idx_procurement_plans_supplier_id ON procurement_plans(supplier_id);
CREATE INDEX IF NOT EXISTS idx_procurement_plans_target_date ON procurement_plans(target_date);
CREATE INDEX IF NOT EXISTS idx_procurement_plans_status ON procurement_plans(status);
CREATE INDEX IF NOT EXISTS idx_procurement_plans_priority ON procurement_plans(priority);

-- ============================================================================
-- SUPPLIER LEAD TIMES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS supplier_lead_times (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    supplier_id TEXT NOT NULL REFERENCES suppliers(id) ON DELETE CASCADE,
    raw_material_id TEXT REFERENCES raw_materials(id) ON DELETE CASCADE, -- NULL for generic lead times
    lead_time_days TEXT NOT NULL,
    minimum_order_qty TEXT DEFAULT '0',
    maximum_order_qty TEXT,
    unit_cost TEXT,
    currency TEXT DEFAULT 'USD',
    reliability TEXT NOT NULL DEFAULT 'good' CHECK (reliability IN ('excellent', 'good', 'fair', 'poor')),
    performance_metrics TEXT, -- JSON string with performance data
    notes TEXT,
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, supplier_id, raw_material_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_supplier_lead_times_company_id ON supplier_lead_times(company_id);
CREATE INDEX IF NOT EXISTS idx_supplier_lead_times_supplier_id ON supplier_lead_times(supplier_id);
CREATE INDEX IF NOT EXISTS idx_supplier_lead_times_raw_material_id ON supplier_lead_times(raw_material_id);
CREATE INDEX IF NOT EXISTS idx_supplier_lead_times_reliability ON supplier_lead_times(reliability);
CREATE INDEX IF NOT EXISTS idx_supplier_lead_times_lead_time ON supplier_lead_times(lead_time_days);

-- ============================================================================
-- UPDATE TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- ============================================================================

-- Demand Forecasts update trigger
CREATE OR REPLACE FUNCTION update_demand_forecasts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_demand_forecasts_updated_at
    BEFORE UPDATE ON demand_forecasts
    FOR EACH ROW
    EXECUTE FUNCTION update_demand_forecasts_updated_at();

-- Forecast Parameters update trigger
CREATE OR REPLACE FUNCTION update_forecast_parameters_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_forecast_parameters_updated_at
    BEFORE UPDATE ON forecast_parameters
    FOR EACH ROW
    EXECUTE FUNCTION update_forecast_parameters_updated_at();

-- Procurement Plans update trigger
CREATE OR REPLACE FUNCTION update_procurement_plans_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_procurement_plans_updated_at
    BEFORE UPDATE ON procurement_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_procurement_plans_updated_at();

-- Supplier Lead Times update trigger
CREATE OR REPLACE FUNCTION update_supplier_lead_times_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_supplier_lead_times_updated_at
    BEFORE UPDATE ON supplier_lead_times
    FOR EACH ROW
    EXECUTE FUNCTION update_supplier_lead_times_updated_at();

-- ============================================================================
-- SAMPLE DATA FOR TESTING (OPTIONAL)
-- ============================================================================

-- Note: Sample data should be inserted after ensuring proper company and product data exists
-- This section can be uncommented and customized based on existing data

/*
-- Sample forecast parameters for existing products
INSERT INTO forecast_parameters (
    id, company_id, product_id, seasonality_factor, trend_factor, 
    lead_time_buffer_days, safety_stock_percentage
) VALUES 
    ('fp-001', 'company-1', 'product-1', '1.2', '1.1', '21', '0.20'),
    ('fp-002', 'company-1', 'product-2', '0.9', '1.0', '14', '0.15')
ON CONFLICT (company_id, product_id) DO NOTHING;

-- Sample supplier lead times
INSERT INTO supplier_lead_times (
    id, company_id, supplier_id, raw_material_id, lead_time_days,
    minimum_order_qty, unit_cost, currency, reliability, created_by
) VALUES 
    ('slt-001', 'company-1', 'supplier-1', 'material-1', '30', '100', '25.50', 'USD', 'good', 'system'),
    ('slt-002', 'company-1', 'supplier-2', 'material-2', '21', '50', '15.75', 'USD', 'excellent', 'system')
ON CONFLICT (company_id, supplier_id, raw_material_id) DO NOTHING;
*/

-- ============================================================================
-- MIGRATION COMPLETION
-- ============================================================================

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'MRP System Migration Completed Successfully';
    RAISE NOTICE 'Tables Created: demand_forecasts, forecast_parameters, procurement_plans, supplier_lead_times';
    RAISE NOTICE 'Indexes Created: Performance indexes for all tables';
    RAISE NOTICE 'Triggers Created: Automatic timestamp update triggers';
    RAISE NOTICE 'Ready for MRP System Operations';
END $$;
