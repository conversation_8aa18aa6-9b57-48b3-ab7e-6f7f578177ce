-- Migration: Add supplier_preferences column to demand_forecasts table
-- Date: 2025-01-11
-- Description: Add optional supplier preferences JSON field to support enhanced MRP supplier integration

-- Add supplier_preferences column to demand_forecasts table
ALTER TABLE demand_forecasts 
ADD COLUMN supplier_preferences TEXT;

-- Add comment to document the column purpose
COMMENT ON COLUMN demand_forecasts.supplier_preferences IS 'JSON field storing optional supplier preferences: { preferredSuppliers: [], excludedSuppliers: [], maxLeadTime: number }';

-- Verify the column was added successfully
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'demand_forecasts' 
AND column_name = 'supplier_preferences';
