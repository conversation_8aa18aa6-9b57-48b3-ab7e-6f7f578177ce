-- ============================================================================
-- LOCATIONS TABLE MIGRATION
-- Manufacturing ERP - Professional Location Management System
-- ============================================================================

-- Create locations table with proper ERP schema
CREATE TABLE IF NOT EXISTS locations (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Basic Location Information
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN (
        'warehouse', 'raw_materials', 'finished_goods', 'work_in_progress',
        'quality_control', 'shipping', 'receiving', 'quarantine', 'returns'
    )),
    description TEXT,
    
    -- Capacity and Operational Data
    capacity INTEGER DEFAULT 0,
    current_utilization INTEGER DEFAULT 0,
    
    -- Location Attributes
    is_active BOOLEAN DEFAULT true,
    is_temperature_controlled BOOLEAN DEFAULT false,
    security_level TEXT DEFAULT 'medium' CHECK (security_level IN ('low', 'medium', 'high')),
    
    -- Hierarchy and Organization
    parent_location_id TEXT REFERENCES locations(id) ON DELETE SET NULL,
    location_code TEXT, -- Short code for easy reference
    
    -- Address and Physical Details
    address TEXT,
    floor_level INTEGER,
    zone TEXT,
    
    -- Operational Settings
    allows_mixed_products BOOLEAN DEFAULT true,
    requires_quality_check BOOLEAN DEFAULT false,
    automated_handling BOOLEAN DEFAULT false,
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique location codes within company
    UNIQUE(company_id, location_code)
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Multi-tenant isolation index (CRITICAL)
CREATE INDEX IF NOT EXISTS locations_company_id_idx ON locations(company_id);

-- Location type filtering
CREATE INDEX IF NOT EXISTS locations_type_idx ON locations(type);

-- Active locations filtering
CREATE INDEX IF NOT EXISTS locations_active_idx ON locations(is_active);

-- Hierarchy support
CREATE INDEX IF NOT EXISTS locations_parent_idx ON locations(parent_location_id);

-- Location code lookup
CREATE INDEX IF NOT EXISTS locations_code_idx ON locations(company_id, location_code);

-- ============================================================================
-- TRIGGERS FOR UPDATED_AT
-- ============================================================================

-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_locations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS locations_updated_at_trigger ON locations;
CREATE TRIGGER locations_updated_at_trigger
    BEFORE UPDATE ON locations
    FOR EACH ROW
    EXECUTE FUNCTION update_locations_updated_at();

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify table creation
SELECT 'locations table created successfully' as status;

-- Show table structure
\d locations;

-- Verify indexes
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'locations' 
ORDER BY indexname;
