-- Migration: Add Shipment Reference Fields to AR Invoices
-- Purpose: Enable traceability from AR invoices back to originating shipments
-- Phase: 1.1.3 - Shipment-to-Invoice Linking
-- Date: 2025-09-08

-- ============================================================================
-- MIGRATION: ADD SHIPMENT REFERENCE FIELDS
-- ============================================================================

-- Add shipment reference fields to ar_invoices table
-- These fields are optional (nullable) to maintain zero-breaking-changes
ALTER TABLE ar_invoices 
ADD COLUMN IF NOT EXISTS shipment_reference_id TEXT,
ADD COLUMN IF NOT EXISTS shipment_reference_number TEXT;

-- Add comment for documentation
COMMENT ON COLUMN ar_invoices.shipment_reference_id IS 'Reference to shipment that generated this invoice (Phase 1 Enhancement)';
COMMENT ON COLUMN ar_invoices.shipment_reference_number IS 'Human-readable shipment number for audit trail';

-- Create index for performance on shipment reference lookups
CREATE INDEX IF NOT EXISTS idx_ar_invoices_shipment_ref 
ON ar_invoices(shipment_reference_id) 
WHERE shipment_reference_id IS NOT NULL;

-- Create index for shipment number lookups
CREATE INDEX IF NOT EXISTS idx_ar_invoices_shipment_number 
ON ar_invoices(shipment_reference_number) 
WHERE shipment_reference_number IS NOT NULL;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify the columns were added
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'ar_invoices' 
  AND column_name IN ('shipment_reference_id', 'shipment_reference_number')
ORDER BY column_name;

-- Verify the indexes were created
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'ar_invoices' 
  AND indexname LIKE '%shipment%'
ORDER BY indexname;

-- Show sample of updated table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'ar_invoices'
ORDER BY ordinal_position;

-- ============================================================================
-- ROLLBACK SCRIPT (if needed)
-- ============================================================================

/*
-- To rollback this migration (ONLY if necessary):

-- Drop indexes
DROP INDEX IF EXISTS idx_ar_invoices_shipment_ref;
DROP INDEX IF EXISTS idx_ar_invoices_shipment_number;

-- Drop columns
ALTER TABLE ar_invoices 
DROP COLUMN IF EXISTS shipment_reference_id,
DROP COLUMN IF EXISTS shipment_reference_number;

*/

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Log migration completion
INSERT INTO migration_log (migration_name, applied_at, description) 
VALUES (
  'add-shipment-reference-to-ar-invoices', 
  NOW(), 
  'Added shipment_reference_id and shipment_reference_number fields to ar_invoices table for Phase 1.1.3 enhancement'
) ON CONFLICT (migration_name) DO NOTHING;

SELECT 'Migration completed successfully: Shipment reference fields added to AR invoices' as result;
