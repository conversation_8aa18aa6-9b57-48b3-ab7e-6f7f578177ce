/**
 * Manufacturing ERP i18n-ai Configuration
 * 
 * This configuration is specifically tailored for the Manufacturing ERP system
 * with comprehensive context for textile manufacturing, export trade, and
 * enterprise resource planning terminology.
 * 
 * ZERO BREAKING CHANGES: This configuration works alongside existing i18n system.
 */

module.exports = {
  // AI Provider Configuration
  provider: 'openai',
  model: 'gpt-4',
  
  // Source and target languages
  sourceLanguage: 'en',
  targetLanguages: ['zh'],
  
  // File patterns to scan for hardcoded strings
  include: [
    'app/**/*.{ts,tsx}',
    'components/**/*.{ts,tsx}',
    'lib/**/*.{ts,tsx}'
  ],
  
  // Files to exclude from scanning
  exclude: [
    'node_modules/**',
    '.next/**',
    'dist/**',
    'build/**',
    'components/i18n-provider.tsx', // Preserve existing i18n system
    'scripts/**',
    '**/*.d.ts'
  ],
  
  // Output configuration
  output: {
    // Directory for generated translation files
    directory: 'i18n-temp/ai-generated',
    
    // Format for translation files
    format: 'json',
    
    // Naming convention for translation keys
    keyNaming: 'camelCase',
    
    // Whether to organize by file structure
    organizeByFile: true
  },
  
  // Manufacturing ERP Context for AI Translation
  context: {
    domain: 'Manufacturing ERP System',
    industry: 'Textile Manufacturing & Export Trade',
    audience: 'Enterprise Business Users',
    tone: 'Professional, Technical, Precise',
    
    // Specific terminology guidelines
    terminology: {
      // Manufacturing Terms
      'Work Order': '工单',
      'Bill of Materials': '物料清单',
      'BOM': 'BOM',
      'Quality Control': '质量控制',
      'Quality Inspection': '质量检验',
      'Raw Materials': '原材料',
      'Finished Goods': '成品',
      'Production Planning': '生产计划',
      'Manufacturing': '制造',
      'Production': '生产',
      
      // Export Trade Terms
      'Export Declaration': '出口申报',
      'HS Code': 'HS编码',
      'Customs': '海关',
      'Shipping': '运输',
      'Freight': '货运',
      'Container': '集装箱',
      'Bill of Lading': '提单',
      'Certificate of Origin': '原产地证书',
      
      // Financial Terms
      'Accounts Receivable': '应收账款',
      'Accounts Payable': '应付账款',
      'Invoice': '发票',
      'Payment Terms': '付款条件',
      'TT (Telegraphic Transfer)': 'TT (电汇)',
      'LC (Letter of Credit)': 'LC (信用证)',
      'DP (Documents against Payment)': 'DP (付款交单)',
      
      // Business Terms
      'Customer': '客户',
      'Supplier': '供应商',
      'Contract': '合同',
      'Purchase Order': '采购订单',
      'Sales Order': '销售订单',
      'Inventory': '库存',
      'Stock': '库存',
      'Warehouse': '仓库'
    },
    
    // Context-specific instructions
    instructions: [
      'Maintain professional business terminology appropriate for enterprise users',
      'Use standard manufacturing and export trade terminology',
      'Keep technical terms consistent with industry standards',
      'Preserve formatting markers like asterisks (*) for required fields',
      'Maintain placeholder text structure (e.g., "e.g., example")',
      'Use formal tone appropriate for business applications',
      'Ensure translations are concise and clear for UI elements'
    ]
  },
  
  // Pattern matching for different types of strings
  patterns: {
    // Form labels (high priority)
    formLabels: {
      regex: /<(?:Label|FormLabel)[^>]*>([^<]+)<\/(?:Label|FormLabel)>/g,
      priority: 'high',
      keyPrefix: 'forms.labels.'
    },
    
    // Placeholders (high priority)
    placeholders: {
      regex: /placeholder="([^"]+)"/g,
      priority: 'high',
      keyPrefix: 'forms.placeholders.'
    },
    
    // Button text (high priority)
    buttons: {
      regex: /<Button[^>]*>([^<{]+)<\/Button>/g,
      priority: 'high',
      keyPrefix: 'buttons.'
    },
    
    // Table headers (medium priority)
    tableHeaders: {
      regex: /<TableHead[^>]*>([^<]+)<\/TableHead>/g,
      priority: 'medium',
      keyPrefix: 'tables.headers.'
    },
    
    // Card titles (medium priority)
    cardTitles: {
      regex: /<CardTitle[^>]*>([^<]+)<\/CardTitle>/g,
      priority: 'medium',
      keyPrefix: 'cards.titles.'
    },
    
    // Select options (medium priority)
    selectOptions: {
      regex: /<SelectItem[^>]*value="[^"]*">([^<]+)<\/SelectItem>/g,
      priority: 'medium',
      keyPrefix: 'options.'
    },
    
    // Toast messages (low priority)
    toastMessages: {
      regex: /toast\.(?:error|success|warning|info)\("([^"]+)"/g,
      priority: 'low',
      keyPrefix: 'messages.'
    },
    
    // Validation messages (low priority)
    validationMessages: {
      regex: /message:\s*"([^"]+)"/g,
      priority: 'low',
      keyPrefix: 'validation.'
    }
  },
  
  // Quality assurance settings
  quality: {
    // Minimum confidence score for auto-acceptance
    minConfidence: 0.8,
    
    // Whether to require manual review for technical terms
    requireReviewForTechnical: true,
    
    // Maximum length for translation keys
    maxKeyLength: 50,
    
    // Whether to validate translation consistency
    validateConsistency: true
  },
  
  // Integration settings
  integration: {
    // Whether to preserve existing translation structure
    preserveExisting: true,
    
    // Format for integration with existing i18n system
    outputFormat: 'i18n-provider-compatible',
    
    // Whether to generate backup before changes
    createBackup: true,
    
    // Batch size for processing
    batchSize: 50
  },
  
  // Advanced AI settings
  ai: {
    // Temperature for creativity vs consistency
    temperature: 0.1, // Low temperature for consistent translations
    
    // Maximum tokens per request
    maxTokens: 2000,
    
    // Whether to use context from previous translations
    useContext: true,
    
    // Custom prompt template
    promptTemplate: `
You are a professional translator specializing in Manufacturing ERP systems for textile export companies.

Context: {context}
Industry: Textile Manufacturing & Export Trade
Audience: Enterprise business users
Tone: Professional, technical, precise

Translate the following English text to Chinese (Simplified), maintaining:
1. Professional business terminology
2. Technical accuracy for manufacturing terms
3. Consistency with provided terminology glossary
4. Appropriate formality for enterprise software
5. Concise clarity for UI elements

Text to translate: "{text}"

Provide only the translation without explanations.
    `
  }
};
