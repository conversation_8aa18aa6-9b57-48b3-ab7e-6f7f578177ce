#!/usr/bin/env node

/**
 * Parallel Translation Manager
 * 
 * Manages translations in parallel workflow without touching existing system.
 * ZERO BREAKING CHANGES: Works alongside existing i18n-provider.tsx
 */

const fs = require('fs');
const path = require('path');

class ParallelTranslationManager {
  constructor() {
    this.pendingDir = 'i18n-parallel/pending';
    this.approvedDir = 'i18n-parallel/approved';
    this.integratedDir = 'i18n-parallel/integrated';
    this.existingI18n = 'components/i18n-provider.tsx';
  }

  // Add new translations to pending queue (SAFE - no system changes)
  addToPending(translations) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `pending-translations-${timestamp}.json`;
      const filepath = path.join(this.pendingDir, filename);
      
      fs.writeFileSync(filepath, JSON.stringify(translations, null, 2));
      console.log(`✅ Added ${Object.keys(translations).length} translations to pending: ${filename}`);
      
      return filepath;
    } catch (error) {
      console.error('❌ Failed to add translations to pending:', error.message);
      return null;
    }
  }

  // Move translations from pending to approved (SAFE - no system changes)
  approvePending(pendingFile) {
    try {
      const pendingPath = path.join(this.pendingDir, pendingFile);
      const approvedPath = path.join(this.approvedDir, pendingFile.replace('pending-', 'approved-'));
      
      if (!fs.existsSync(pendingPath)) {
        console.error(`❌ Pending file not found: ${pendingFile}`);
        return false;
      }
      
      // Copy to approved (don't delete pending yet for safety)
      const content = fs.readFileSync(pendingPath, 'utf8');
      fs.writeFileSync(approvedPath, content);
      
      console.log(`✅ Approved translations: ${pendingFile}`);
      return approvedPath;
    } catch (error) {
      console.error('❌ Failed to approve translations:', error.message);
      return false;
    }
  }

  // List all pending translations (SAFE - read-only)
  listPending() {
    try {
      const files = fs.readdirSync(this.pendingDir)
        .filter(file => file.endsWith('.json'))
        .map(file => {
          const filepath = path.join(this.pendingDir, file);
          const stats = fs.statSync(filepath);
          const content = JSON.parse(fs.readFileSync(filepath, 'utf8'));
          
          return {
            file,
            created: stats.birthtime,
            size: Object.keys(content).length,
            path: filepath
          };
        });
      
      return files;
    } catch (error) {
      console.error('❌ Failed to list pending translations:', error.message);
      return [];
    }
  }

  // Preview integration (SAFE - no actual changes)
  previewIntegration(approvedFile) {
    try {
      const approvedPath = path.join(this.approvedDir, approvedFile);
      
      if (!fs.existsSync(approvedPath)) {
        console.error(`❌ Approved file not found: ${approvedFile}`);
        return null;
      }
      
      const newTranslations = JSON.parse(fs.readFileSync(approvedPath, 'utf8'));
      const existingContent = fs.readFileSync(this.existingI18n, 'utf8');
      
      // Analyze what would be added (SAFE - no actual changes)
      const analysis = {
        newKeys: Object.keys(newTranslations),
        totalNew: Object.keys(newTranslations).length,
        conflicts: [], // Check for existing keys
        preview: newTranslations
      };
      
      // Check for potential conflicts (SAFE - read-only analysis)
      analysis.newKeys.forEach(key => {
        if (existingContent.includes(`"${key}":`)) {
          analysis.conflicts.push(key);
        }
      });
      
      console.log(`📊 Integration Preview for ${approvedFile}:`);
      console.log(`   New keys: ${analysis.totalNew}`);
      console.log(`   Conflicts: ${analysis.conflicts.length}`);
      
      return analysis;
    } catch (error) {
      console.error('❌ Failed to preview integration:', error.message);
      return null;
    }
  }

  // Get system status (SAFE - read-only)
  getStatus() {
    try {
      const pending = this.listPending();
      const approved = fs.readdirSync(this.approvedDir).filter(f => f.endsWith('.json'));
      const integrated = fs.readdirSync(this.integratedDir).filter(f => f.endsWith('.json'));
      
      return {
        pending: pending.length,
        approved: approved.length,
        integrated: integrated.length,
        pendingFiles: pending,
        lastUpdate: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Failed to get status:', error.message);
      return null;
    }
  }
}

// CLI Interface
if (require.main === module) {
  const manager = new ParallelTranslationManager();
  const command = process.argv[2];
  
  switch (command) {
    case 'status':
      const status = manager.getStatus();
      if (status) {
        console.log('📊 Parallel Translation Status:');
        console.log(`   Pending: ${status.pending} files`);
        console.log(`   Approved: ${status.approved} files`);
        console.log(`   Integrated: ${status.integrated} files`);
      }
      break;
      
    case 'list':
      const pending = manager.listPending();
      console.log('📋 Pending Translations:');
      pending.forEach((file, index) => {
        console.log(`   ${index + 1}. ${file.file} (${file.size} keys, ${file.created.toLocaleDateString()})`);
      });
      break;
      
    case 'preview':
      const filename = process.argv[3];
      if (!filename) {
        console.error('❌ Please provide approved filename to preview');
        process.exit(1);
      }
      manager.previewIntegration(filename);
      break;
      
    default:
      console.log('📖 Parallel Translation Manager Commands:');
      console.log('   node parallel-translation-manager.js status   - Show system status');
      console.log('   node parallel-translation-manager.js list     - List pending translations');
      console.log('   node parallel-translation-manager.js preview <file> - Preview integration');
  }
}

module.exports = ParallelTranslationManager;
