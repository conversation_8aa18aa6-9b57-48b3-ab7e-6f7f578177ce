#!/usr/bin/env node

/**
 * Safe Integration Validator
 * 
 * Validates translations before integration to ensure ZERO BREAKING CHANGES.
 * All checks are read-only and safe.
 */

const fs = require('fs');
const path = require('path');

class SafeIntegrationValidator {
  constructor() {
    this.existingI18n = 'components/i18n-provider.tsx';
  }

  // Validate translation file (SAFE - read-only)
  validateTranslationFile(filepath) {
    try {
      if (!fs.existsSync(filepath)) {
        return { valid: false, error: 'File not found' };
      }
      
      const content = fs.readFileSync(filepath, 'utf8');
      const translations = JSON.parse(content);
      
      const validation = {
        valid: true,
        warnings: [],
        errors: [],
        stats: {
          totalKeys: Object.keys(translations).length,
          englishKeys: 0,
          chineseKeys: 0
        }
      };
      
      // Check translation structure (SAFE)
      Object.entries(translations).forEach(([key, value]) => {
        if (typeof value !== 'string') {
          validation.errors.push(`Invalid value type for key "${key}": expected string`);
        }
        
        if (key.length > 100) {
          validation.warnings.push(`Long key name: "${key}" (${key.length} chars)`);
        }
        
        if (value.length === 0) {
          validation.warnings.push(`Empty translation for key: "${key}"`);
        }
      });
      
      validation.stats.totalKeys = Object.keys(translations).length;
      
      if (validation.errors.length > 0) {
        validation.valid = false;
      }
      
      return validation;
    } catch (error) {
      return { 
        valid: false, 
        error: `Failed to validate: ${error.message}` 
      };
    }
  }

  // Check for conflicts with existing system (SAFE - read-only)
  checkConflicts(newTranslations) {
    try {
      const existingContent = fs.readFileSync(this.existingI18n, 'utf8');
      const conflicts = [];
      
      Object.keys(newTranslations).forEach(key => {
        if (existingContent.includes(`"${key}":`)) {
          conflicts.push({
            key,
            action: 'skip', // Default safe action
            reason: 'Key already exists in system'
          });
        }
      });
      
      return {
        hasConflicts: conflicts.length > 0,
        conflicts,
        safeToIntegrate: conflicts.length === 0
      };
    } catch (error) {
      return {
        hasConflicts: true,
        error: error.message,
        safeToIntegrate: false
      };
    }
  }

  // Comprehensive safety check (SAFE - read-only)
  performSafetyCheck(translationFile) {
    console.log(`🔍 Performing safety check on: ${translationFile}`);
    
    const validation = this.validateTranslationFile(translationFile);
    if (!validation.valid) {
      console.log('❌ Validation failed:', validation.error);
      return false;
    }
    
    const translations = JSON.parse(fs.readFileSync(translationFile, 'utf8'));
    const conflictCheck = this.checkConflicts(translations);
    
    console.log('📊 Safety Check Results:');
    console.log(`   Total keys: ${validation.stats.totalKeys}`);
    console.log(`   Warnings: ${validation.warnings.length}`);
    console.log(`   Conflicts: ${conflictCheck.conflicts.length}`);
    console.log(`   Safe to integrate: ${conflictCheck.safeToIntegrate ? '✅ YES' : '❌ NO'}`);
    
    if (validation.warnings.length > 0) {
      console.log('⚠️  Warnings:');
      validation.warnings.forEach(warning => console.log(`   - ${warning}`));
    }
    
    if (conflictCheck.conflicts.length > 0) {
      console.log('⚠️  Conflicts (will be skipped):');
      conflictCheck.conflicts.forEach(conflict => console.log(`   - ${conflict.key}`));
    }
    
    return conflictCheck.safeToIntegrate;
  }
}

// CLI Interface
if (require.main === module) {
  const validator = new SafeIntegrationValidator();
  const command = process.argv[2];
  const filename = process.argv[3];
  
  if (command === 'check' && filename) {
    validator.performSafetyCheck(filename);
  } else {
    console.log('📖 Safe Integration Validator Commands:');
    console.log('   node safe-integration-validator.js check <filepath> - Perform safety check');
  }
}

module.exports = SafeIntegrationValidator;
