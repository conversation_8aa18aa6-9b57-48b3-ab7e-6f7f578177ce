{"validationDate": "2025-09-15T08:31:57.075Z", "testBackup": {"location": "i18n-parallel/rollback-tests/test-backup-2025-09-15T08-31-56-035Z", "checksum": "4734884c", "fileSize": 117468}, "testResults": {"systemRestore": {"passed": true, "checks": ["✅ Test modification applied successfully", "✅ Restore completed in 7ms", "✅ File integrity verified after restore", "✅ File size verified after restore"], "warnings": [], "errors": [], "restoreTime": 7, "integrityVerified": true}, "backupIntegrity": {"passed": false, "checks": ["✅ Found 1 backup directories", "✅ Valid backups: 0"], "warnings": ["Missing metadata in backup-2025-09-15T08-02-56-773Z"], "errors": ["Missing backup file in backup-2025-09-15T08-02-56-773Z", "Found 1 corrupt backups", "No valid backups found"], "backupsFound": 1, "validBackups": 0, "corruptBackups": 1}, "rollbackSpeed": {"passed": true, "checks": ["✅ Completed 3 restore speed tests", "✅ Average restore time: 5ms", "✅ Fastest restore: 4ms", "✅ Slowest restore: 6ms"], "warnings": [], "errors": [], "averageRestoreTime": 5, "fastestRestore": 4, "slowestRestore": 6, "testRuns": 3}, "systemFunctionality": {"passed": true, "checks": ["✅ i18n provider file exists", "✅ i18n provider file is readable", "✅ i18n provider content appears valid"], "warnings": ["TypeScript syntax validation had issues (may be unrelated)", "3/4 functionality tests passed"], "errors": [], "functionalityTests": {"fileExists": true, "fileReadable": true, "contentValid": true, "syntaxValid": false}}}, "summary": {"totalTests": 4, "passedTests": 3, "overallPassed": false, "successRate": 75, "rollbackCapability": "ISSUES_FOUND"}, "recommendations": ["CRITICAL: Address backup integrity issues", "Create at least one verified backup before proceeding", "Address failing tests before proceeding with system changes"]}