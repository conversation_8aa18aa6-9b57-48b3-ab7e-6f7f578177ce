{"batchNumber": 1, "processedAt": "2025-09-15T09:06:50.992Z", "totalStrings": 3, "method": "terminology_based", "quality": "manual_review_recommended", "exportedAt": "2025-09-15T09:07:00.621Z", "sourceFile": "i18n-parallel/pending/batch-1-2025-09-15T09-06-50-996Z.json", "csvFile": "i18n-parallel/csv/translations-2025-09-15T09-07-00-620Z.csv", "totalTranslations": 3, "instructions": {"editing": "Edit the Chinese column to provide better translations", "review": "Set Needs Review to false when translation is approved", "comments": "Add comments for complex translations or context", "import": "Use: node i18n-csv-workflow.js import translations-2025-09-15T09-07-00-620Z.csv"}}