ALTER TABLE "inventory_transactions" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
DROP TABLE "inventory_transactions" CASCADE;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "transaction_type" text DEFAULT 'manual' NOT NULL;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "from_location" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "to_location" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "reason_code" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "approval_status" text DEFAULT 'approved' NOT NULL;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "approved_by" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "approved_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "created_by" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now();--> statement-breakpoint
CREATE INDEX "stock_txns_transaction_type_idx" ON "stock_txns" USING btree ("transaction_type");--> statement-breakpoint
CREATE INDEX "stock_txns_approval_status_idx" ON "stock_txns" USING btree ("approval_status");--> statement-breakpoint
CREATE INDEX "stock_txns_from_location_idx" ON "stock_txns" USING btree ("from_location");--> statement-breakpoint
CREATE INDEX "stock_txns_to_location_idx" ON "stock_txns" USING btree ("to_location");--> statement-breakpoint
CREATE INDEX "stock_txns_created_by_idx" ON "stock_txns" USING btree ("created_by");