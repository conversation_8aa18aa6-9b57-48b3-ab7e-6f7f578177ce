CREATE TABLE "shipment_items" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"shipment_id" text NOT NULL,
	"product_id" text NOT NULL,
	"stock_lot_id" text,
	"quantity" text NOT NULL,
	"unit_price" text,
	"total_price" text,
	"weight_per_unit" text,
	"volume_per_unit" text,
	"quality_certificate_id" text,
	"batch_number" text,
	"lot_number" text,
	"status" text DEFAULT 'pending',
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "shipments" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"shipment_number" text NOT NULL,
	"sales_contract_id" text,
	"customer_id" text NOT NULL,
	"shipping_method" text NOT NULL,
	"carrier" text,
	"service_type" text,
	"tracking_number" text,
	"pickup_address" jsonb,
	"delivery_address" jsonb,
	"status" text DEFAULT 'preparing',
	"ship_date" text,
	"estimated_delivery" text,
	"actual_delivery" text,
	"shipping_cost" text,
	"insurance_cost" text,
	"total_value" text,
	"currency" text DEFAULT 'USD',
	"total_weight" text,
	"total_volume" text,
	"package_count" text DEFAULT '1',
	"notes" text,
	"special_instructions" text,
	"customs_declaration" text,
	"requires_certificate" boolean DEFAULT false,
	"certificate_attached" boolean DEFAULT false,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"created_by" text,
	"updated_by" text
);
--> statement-breakpoint
CREATE TABLE "shipping_documents" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"shipment_id" text NOT NULL,
	"document_type" text NOT NULL,
	"document_number" text,
	"document_name" text NOT NULL,
	"file_path" text,
	"file_size" text,
	"file_type" text,
	"status" text DEFAULT 'draft',
	"description" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"created_by" text
);
--> statement-breakpoint
CREATE TABLE "shipping_tracking" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"shipment_id" text NOT NULL,
	"status" text NOT NULL,
	"location" text,
	"description" text NOT NULL,
	"carrier_status" text,
	"carrier_location" text,
	"timestamp" timestamp with time zone NOT NULL,
	"estimated_delivery" text,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"created_by" text
);
--> statement-breakpoint
ALTER TABLE "shipment_items" ADD CONSTRAINT "shipment_items_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipment_items" ADD CONSTRAINT "shipment_items_shipment_id_shipments_id_fk" FOREIGN KEY ("shipment_id") REFERENCES "public"."shipments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipment_items" ADD CONSTRAINT "shipment_items_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipment_items" ADD CONSTRAINT "shipment_items_stock_lot_id_stock_lots_id_fk" FOREIGN KEY ("stock_lot_id") REFERENCES "public"."stock_lots"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipment_items" ADD CONSTRAINT "shipment_items_quality_certificate_id_quality_certificates_id_fk" FOREIGN KEY ("quality_certificate_id") REFERENCES "public"."quality_certificates"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipments" ADD CONSTRAINT "shipments_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipments" ADD CONSTRAINT "shipments_sales_contract_id_sales_contracts_id_fk" FOREIGN KEY ("sales_contract_id") REFERENCES "public"."sales_contracts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipments" ADD CONSTRAINT "shipments_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipping_documents" ADD CONSTRAINT "shipping_documents_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipping_documents" ADD CONSTRAINT "shipping_documents_shipment_id_shipments_id_fk" FOREIGN KEY ("shipment_id") REFERENCES "public"."shipments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipping_tracking" ADD CONSTRAINT "shipping_tracking_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "shipping_tracking" ADD CONSTRAINT "shipping_tracking_shipment_id_shipments_id_fk" FOREIGN KEY ("shipment_id") REFERENCES "public"."shipments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "shipment_items_company_id_idx" ON "shipment_items" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "shipment_items_shipment_id_idx" ON "shipment_items" USING btree ("shipment_id");--> statement-breakpoint
CREATE INDEX "shipment_items_product_id_idx" ON "shipment_items" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "shipment_items_stock_lot_id_idx" ON "shipment_items" USING btree ("stock_lot_id");--> statement-breakpoint
CREATE INDEX "shipments_company_id_idx" ON "shipments" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "shipments_status_idx" ON "shipments" USING btree ("status");--> statement-breakpoint
CREATE INDEX "shipments_ship_date_idx" ON "shipments" USING btree ("ship_date");--> statement-breakpoint
CREATE INDEX "shipments_customer_id_idx" ON "shipments" USING btree ("customer_id");--> statement-breakpoint
CREATE INDEX "shipments_contract_id_idx" ON "shipments" USING btree ("sales_contract_id");--> statement-breakpoint
CREATE INDEX "shipments_tracking_idx" ON "shipments" USING btree ("tracking_number");--> statement-breakpoint
CREATE INDEX "shipping_documents_company_id_idx" ON "shipping_documents" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "shipping_documents_shipment_id_idx" ON "shipping_documents" USING btree ("shipment_id");--> statement-breakpoint
CREATE INDEX "shipping_documents_type_idx" ON "shipping_documents" USING btree ("document_type");--> statement-breakpoint
CREATE INDEX "shipping_tracking_company_id_idx" ON "shipping_tracking" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "shipping_tracking_shipment_id_idx" ON "shipping_tracking" USING btree ("shipment_id");--> statement-breakpoint
CREATE INDEX "shipping_tracking_timestamp_idx" ON "shipping_tracking" USING btree ("timestamp");