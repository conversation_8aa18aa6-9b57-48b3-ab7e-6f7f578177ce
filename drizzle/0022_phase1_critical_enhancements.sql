-- ============================================================================
-- MANUFACTURING ERP - PHASE 1 CRITICAL ENHANCEMENTS MIGRATION
-- ============================================================================
-- 
-- This migration implements Phase 1 critical enhancements for enterprise-grade
-- ERP functionality including stock transaction cost tracking and declaration
-- items value fields for customs compliance.
--
-- FEATURES:
-- ✅ Stock Transaction Cost Tracking - Accurate inventory valuation
-- ✅ Declaration Items Value Fields - Export customs compliance
-- ✅ Performance Indexes - Optimized query performance
-- ✅ Zero Breaking Changes - Backward compatible additive changes
--
-- <AUTHOR> ERP Developer
-- @version 1.0.0 - Phase 1 Critical Enhancements
-- @date 2024-01-XX
-- ============================================================================

-- ✅ STEP 1: ADD COST TRACKING FIELDS TO STOCK TRANSACTIONS
-- Enable accurate inventory valuation and cost accounting
ALTER TABLE "stock_txns" ADD COLUMN IF NOT EXISTS "unit_cost" text;
ALTER TABLE "stock_txns" ADD COLUMN IF NOT EXISTS "total_value" text;
ALTER TABLE "stock_txns" ADD COLUMN IF NOT EXISTS "cost_method" text DEFAULT 'standard';
ALTER TABLE "stock_txns" ADD COLUMN IF NOT EXISTS "cost_currency" text DEFAULT 'USD';

-- ✅ STEP 2: ADD VALUE DECLARATION FIELDS TO EXPORT DECLARATION ITEMS
-- Ensure customs compliance for export documentation
ALTER TABLE "declaration_items" ADD COLUMN IF NOT EXISTS "unit_value" text;
ALTER TABLE "declaration_items" ADD COLUMN IF NOT EXISTS "total_value" text;
ALTER TABLE "declaration_items" ADD COLUMN IF NOT EXISTS "value_currency" text DEFAULT 'USD';
ALTER TABLE "declaration_items" ADD COLUMN IF NOT EXISTS "value_method" text;

-- ✅ STEP 3: CREATE PERFORMANCE INDEXES FOR COST TRACKING
-- Optimize queries for inventory valuation and cost analysis
CREATE INDEX IF NOT EXISTS "stock_txns_unit_cost_idx" ON "stock_txns" USING btree ("unit_cost");
CREATE INDEX IF NOT EXISTS "stock_txns_cost_method_idx" ON "stock_txns" USING btree ("cost_method");

-- ✅ STEP 4: CREATE PERFORMANCE INDEXES FOR VALUE DECLARATIONS
-- Optimize queries for customs compliance and export reporting
CREATE INDEX IF NOT EXISTS "declaration_items_total_value_idx" ON "declaration_items" USING btree ("total_value");
CREATE INDEX IF NOT EXISTS "declaration_items_value_method_idx" ON "declaration_items" USING btree ("value_method");
CREATE INDEX IF NOT EXISTS "declaration_items_value_currency_idx" ON "declaration_items" USING btree ("value_currency");

-- ✅ STEP 5: POPULATE DEFAULT VALUES FOR EXISTING RECORDS
-- Ensure existing data has sensible defaults for new fields

-- Set default cost method for existing stock transactions
UPDATE "stock_txns" 
SET "cost_method" = 'standard', "cost_currency" = 'USD'
WHERE "cost_method" IS NULL OR "cost_currency" IS NULL;

-- Set default value currency for existing declaration items
UPDATE "declaration_items" 
SET "value_currency" = 'USD'
WHERE "value_currency" IS NULL;

-- ✅ MIGRATION COMPLETE
-- Phase 1 Critical Enhancements successfully implemented
-- - Stock transaction cost tracking enabled
-- - Declaration items value fields added
-- - Performance indexes created
-- - Zero breaking changes maintained
-- - Backward compatibility preserved
