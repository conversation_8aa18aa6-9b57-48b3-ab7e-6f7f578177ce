{"id": "e613e545-056d-4e1f-ac45-354f981aa985", "prevId": "8abe9855-57a3-46ce-8771-95942712384f", "version": "7", "dialect": "postgresql", "tables": {"public.ap_invoices": {"name": "ap_invoices", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "supplier_id": {"name": "supplier_id", "type": "text", "primaryKey": false, "notNull": true}, "purchase_contract_id": {"name": "purchase_contract_id", "type": "text", "primaryKey": false, "notNull": false}, "contract_reference": {"name": "contract_reference", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "text", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "text", "primaryKey": false, "notNull": true}, "paid": {"name": "paid", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "payment_terms": {"name": "payment_terms", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"ap_invoices_company_id_idx": {"name": "ap_invoices_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ap_invoices_contract_id_idx": {"name": "ap_invoices_contract_id_idx", "columns": [{"expression": "purchase_contract_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ap_invoices_company_id_companies_id_fk": {"name": "ap_invoices_company_id_companies_id_fk", "tableFrom": "ap_invoices", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ap_invoices_supplier_id_suppliers_id_fk": {"name": "ap_invoices_supplier_id_suppliers_id_fk", "tableFrom": "ap_invoices", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ap_invoices_purchase_contract_id_purchase_contracts_id_fk": {"name": "ap_invoices_purchase_contract_id_purchase_contracts_id_fk", "tableFrom": "ap_invoices", "tableTo": "purchase_contracts", "columnsFrom": ["purchase_contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ar_invoices": {"name": "ar_invoices", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": true}, "sales_contract_id": {"name": "sales_contract_id", "type": "text", "primaryKey": false, "notNull": false}, "contract_reference": {"name": "contract_reference", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "text", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "text", "primaryKey": false, "notNull": true}, "received": {"name": "received", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "payment_terms": {"name": "payment_terms", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"ar_invoices_company_id_idx": {"name": "ar_invoices_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ar_invoices_contract_id_idx": {"name": "ar_invoices_contract_id_idx", "columns": [{"expression": "sales_contract_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ar_invoices_company_id_companies_id_fk": {"name": "ar_invoices_company_id_companies_id_fk", "tableFrom": "ar_invoices", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ar_invoices_customer_id_customers_id_fk": {"name": "ar_invoices_customer_id_customers_id_fk", "tableFrom": "ar_invoices", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ar_invoices_sales_contract_id_sales_contracts_id_fk": {"name": "ar_invoices_sales_contract_id_sales_contracts_id_fk", "tableFrom": "ar_invoices", "tableTo": "sales_contracts", "columnsFrom": ["sales_contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bill_of_materials": {"name": "bill_of_materials", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "raw_material_id": {"name": "raw_material_id", "type": "text", "primaryKey": false, "notNull": true}, "qty_required": {"name": "qty_required", "type": "text", "primaryKey": false, "notNull": true}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": true}, "waste_factor": {"name": "waste_factor", "type": "text", "primaryKey": false, "notNull": false, "default": "'0.05'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"bill_of_materials_company_id_idx": {"name": "bill_of_materials_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bill_of_materials_product_idx": {"name": "bill_of_materials_product_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bill_of_materials_company_id_companies_id_fk": {"name": "bill_of_materials_company_id_companies_id_fk", "tableFrom": "bill_of_materials", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "bill_of_materials_product_id_products_id_fk": {"name": "bill_of_materials_product_id_products_id_fk", "tableFrom": "bill_of_materials", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "bill_of_materials_raw_material_id_raw_materials_id_fk": {"name": "bill_of_materials_raw_material_id_raw_materials_id_fk", "tableFrom": "bill_of_materials", "tableTo": "raw_materials", "columnsFrom": ["raw_material_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cash_flow_forecasts": {"name": "cash_flow_forecasts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "forecast_date": {"name": "forecast_date", "type": "text", "primaryKey": false, "notNull": true}, "forecast_type": {"name": "forecast_type", "type": "text", "primaryKey": false, "notNull": true}, "forecast_horizon_days": {"name": "forecast_horizon_days", "type": "text", "primaryKey": false, "notNull": true}, "expected_ar_collections": {"name": "expected_ar_collections", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "expected_contract_payments": {"name": "expected_contract_payments", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "expected_export_receipts": {"name": "expected_export_receipts", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "other_inflows": {"name": "other_inflows", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_inflows": {"name": "total_inflows", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "expected_ap_payments": {"name": "expected_ap_payments", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "expected_payroll": {"name": "expected_payroll", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "expected_operating_expenses": {"name": "expected_operating_expenses", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "expected_shipping_costs": {"name": "expected_shipping_costs", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "expected_raw_material_costs": {"name": "expected_raw_material_costs", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "other_outflows": {"name": "other_outflows", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_outflows": {"name": "total_outflows", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "net_cash_flow": {"name": "net_cash_flow", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "cumulative_cash_flow": {"name": "cumulative_cash_flow", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "currency_risk_adjustment": {"name": "currency_risk_adjustment", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "exchange_rate_sensitivity": {"name": "exchange_rate_sensitivity", "type": "text", "primaryKey": false, "notNull": false}, "confidence_level": {"name": "confidence_level", "type": "text", "primaryKey": false, "notNull": false, "default": "'medium'"}, "risk_factors": {"name": "risk_factors", "type": "text", "primaryKey": false, "notNull": false}, "best_case_scenario": {"name": "best_case_scenario", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "worst_case_scenario": {"name": "worst_case_scenario", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "most_likely_scenario": {"name": "most_likely_scenario", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "forecast_method": {"name": "forecast_method", "type": "text", "primaryKey": false, "notNull": false, "default": "'historical'"}, "last_updated_at": {"name": "last_updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"cash_flow_forecasts_company_id_idx": {"name": "cash_flow_forecasts_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cash_flow_forecasts_date_idx": {"name": "cash_flow_forecasts_date_idx", "columns": [{"expression": "forecast_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cash_flow_forecasts_type_idx": {"name": "cash_flow_forecasts_type_idx", "columns": [{"expression": "forecast_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cash_flow_forecasts_horizon_idx": {"name": "cash_flow_forecasts_horizon_idx", "columns": [{"expression": "forecast_horizon_days", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"cash_flow_forecasts_company_id_companies_id_fk": {"name": "cash_flow_forecasts_company_id_companies_id_fk", "tableFrom": "cash_flow_forecasts", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.companies": {"name": "companies", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "auth0_user_id": {"name": "auth0_user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "legal_name": {"name": "legal_name", "type": "text", "primaryKey": false, "notNull": false}, "registration_number": {"name": "registration_number", "type": "text", "primaryKey": false, "notNull": false}, "tax_id": {"name": "tax_id", "type": "text", "primaryKey": false, "notNull": false}, "vat_number": {"name": "vat_number", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "address_line1": {"name": "address_line1", "type": "text", "primaryKey": false, "notNull": false}, "address_line2": {"name": "address_line2", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state_province": {"name": "state_province", "type": "text", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": false}, "business_type": {"name": "business_type", "type": "text", "primaryKey": false, "notNull": false}, "employee_count": {"name": "employee_count", "type": "text", "primaryKey": false, "notNull": false}, "annual_revenue": {"name": "annual_revenue", "type": "text", "primaryKey": false, "notNull": false}, "bank_name": {"name": "bank_name", "type": "text", "primaryKey": false, "notNull": false}, "bank_account": {"name": "bank_account", "type": "text", "primaryKey": false, "notNull": false}, "bank_swift": {"name": "bank_swift", "type": "text", "primaryKey": false, "notNull": false}, "bank_address": {"name": "bank_address", "type": "text", "primaryKey": false, "notNull": false}, "bank_iban": {"name": "bank_iban", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": false, "default": "'UTC'"}, "date_format": {"name": "date_format", "type": "text", "primaryKey": false, "notNull": false, "default": "'YYYY-MM-DD'"}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false, "default": "'en'"}, "export_license": {"name": "export_license", "type": "text", "primaryKey": false, "notNull": false}, "customs_code": {"name": "customs_code", "type": "text", "primaryKey": false, "notNull": false}, "preferred_incoterms": {"name": "preferred_incoterms", "type": "text", "primaryKey": false, "notNull": false, "default": "'FOB'"}, "preferred_payment_terms": {"name": "preferred_payment_terms", "type": "text", "primaryKey": false, "notNull": false, "default": "'30 days'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "onboarding_completed": {"name": "onboarding_completed", "type": "text", "primaryKey": false, "notNull": false, "default": "'false'"}, "onboarding_step": {"name": "onboarding_step", "type": "text", "primaryKey": false, "notNull": false, "default": "'basic_info'"}, "subscription_plan": {"name": "subscription_plan", "type": "text", "primaryKey": false, "notNull": false, "default": "'free'"}, "subscription_status": {"name": "subscription_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "trial_ends_at": {"name": "trial_ends_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "last_login_at": {"name": "last_login_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"companies_auth0_user_id_idx": {"name": "companies_auth0_user_id_idx", "columns": [{"expression": "auth0_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "companies_name_idx": {"name": "companies_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"companies_auth0_user_id_unique": {"name": "companies_auth0_user_id_unique", "nullsNotDistinct": false, "columns": ["auth0_user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.container_cost_allocation": {"name": "container_cost_allocation", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "container_id": {"name": "container_id", "type": "text", "primaryKey": false, "notNull": true}, "container_type": {"name": "container_type", "type": "text", "primaryKey": false, "notNull": true}, "container_number": {"name": "container_number", "type": "text", "primaryKey": false, "notNull": false}, "shipment_id": {"name": "shipment_id", "type": "text", "primaryKey": false, "notNull": false}, "export_declaration_id": {"name": "export_declaration_id", "type": "text", "primaryKey": false, "notNull": false}, "sales_contract_id": {"name": "sales_contract_id", "type": "text", "primaryKey": false, "notNull": false}, "base_shipping_cost": {"name": "base_shipping_cost", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "fuel_surcharge": {"name": "fuel_surcharge", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "port_charges": {"name": "port_charges", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "customs_fees": {"name": "customs_fees", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "insurance_cost": {"name": "insurance_cost", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "handling_fees": {"name": "handling_fees", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "documentation_fees": {"name": "documentation_fees", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "other_charges": {"name": "other_charges", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "cost_currency": {"name": "cost_currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "base_cost_amount": {"name": "base_cost_amount", "type": "text", "primaryKey": false, "notNull": false}, "base_currency_code": {"name": "base_currency_code", "type": "text", "primaryKey": false, "notNull": false}, "exchange_rate": {"name": "exchange_rate", "type": "text", "primaryKey": false, "notNull": false, "default": "'1.0'"}, "total_container_weight": {"name": "total_container_weight", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_container_volume": {"name": "total_container_volume", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "utilization_percentage": {"name": "utilization_percentage", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "allocation_method": {"name": "allocation_method", "type": "text", "primaryKey": false, "notNull": false, "default": "'weight'"}, "allocated_products": {"name": "allocated_products", "type": "text", "primaryKey": false, "notNull": false}, "shipping_date": {"name": "shipping_date", "type": "text", "primaryKey": false, "notNull": true}, "arrival_date": {"name": "arrival_date", "type": "text", "primaryKey": false, "notNull": false}, "cost_date": {"name": "cost_date", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"container_cost_allocation_company_id_idx": {"name": "container_cost_allocation_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "container_cost_allocation_container_idx": {"name": "container_cost_allocation_container_idx", "columns": [{"expression": "container_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "container_cost_allocation_shipment_idx": {"name": "container_cost_allocation_shipment_idx", "columns": [{"expression": "shipment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "container_cost_allocation_contract_idx": {"name": "container_cost_allocation_contract_idx", "columns": [{"expression": "sales_contract_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "container_cost_allocation_date_idx": {"name": "container_cost_allocation_date_idx", "columns": [{"expression": "shipping_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"container_cost_allocation_company_id_companies_id_fk": {"name": "container_cost_allocation_company_id_companies_id_fk", "tableFrom": "container_cost_allocation", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "container_cost_allocation_sales_contract_id_sales_contracts_id_fk": {"name": "container_cost_allocation_sales_contract_id_sales_contracts_id_fk", "tableFrom": "container_cost_allocation", "tableTo": "sales_contracts", "columnsFrom": ["sales_contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contract_templates": {"name": "contract_templates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "payment_terms": {"name": "payment_terms", "type": "text", "primaryKey": false, "notNull": false}, "delivery_terms": {"name": "delivery_terms", "type": "text", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false, "default": "'en'"}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": false, "default": "'1'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_default": {"name": "is_default", "type": "text", "primaryKey": false, "notNull": false, "default": "'false'"}, "is_active": {"name": "is_active", "type": "text", "primaryKey": false, "notNull": false, "default": "'true'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"contract_templates_company_id_idx": {"name": "contract_templates_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"contract_templates_company_id_companies_id_fk": {"name": "contract_templates_company_id_companies_id_fk", "tableFrom": "contract_templates", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.currencies": {"name": "currencies", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "text", "primaryKey": false, "notNull": true}, "decimal_places": {"name": "decimal_places", "type": "text", "primaryKey": false, "notNull": true, "default": "'2'"}, "is_base_currency": {"name": "is_base_currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'false'"}, "is_active": {"name": "is_active", "type": "text", "primaryKey": false, "notNull": true, "default": "'true'"}, "exchange_rate": {"name": "exchange_rate", "type": "text", "primaryKey": false, "notNull": false, "default": "'1.0'"}, "last_rate_update": {"name": "last_rate_update", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"currencies_company_id_idx": {"name": "currencies_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currencies_code_idx": {"name": "currencies_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currencies_base_idx": {"name": "currencies_base_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_base_currency", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currencies_active_idx": {"name": "currencies_active_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"currencies_company_id_companies_id_fk": {"name": "currencies_company_id_companies_id_fk", "tableFrom": "currencies", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"currencies_company_id_code_unique": {"name": "currencies_company_id_code_unique", "nullsNotDistinct": false, "columns": ["company_id", "code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.currency_conversion_cache": {"name": "currency_conversion_cache", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "from_currency_code": {"name": "from_currency_code", "type": "text", "primaryKey": false, "notNull": true}, "to_currency_code": {"name": "to_currency_code", "type": "text", "primaryKey": false, "notNull": true}, "exchange_rate": {"name": "exchange_rate", "type": "text", "primaryKey": false, "notNull": true}, "amount_from": {"name": "amount_from", "type": "text", "primaryKey": false, "notNull": true}, "amount_to": {"name": "amount_to", "type": "text", "primaryKey": false, "notNull": true}, "conversion_date": {"name": "conversion_date", "type": "text", "primaryKey": false, "notNull": true}, "cache_expires_at": {"name": "cache_expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"currency_conversion_cache_company_id_idx": {"name": "currency_conversion_cache_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currency_conversion_cache_currencies_idx": {"name": "currency_conversion_cache_currencies_idx", "columns": [{"expression": "from_currency_code", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "to_currency_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currency_conversion_cache_expires_idx": {"name": "currency_conversion_cache_expires_idx", "columns": [{"expression": "cache_expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"currency_conversion_cache_company_id_companies_id_fk": {"name": "currency_conversion_cache_company_id_companies_id_fk", "tableFrom": "currency_conversion_cache", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"currency_conversion_cache_company_id_from_currency_code_to_currency_code_amount_from_conversion_date_unique": {"name": "currency_conversion_cache_company_id_from_currency_code_to_currency_code_amount_from_conversion_date_unique", "nullsNotDistinct": false, "columns": ["company_id", "from_currency_code", "to_currency_code", "amount_from", "conversion_date"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.currency_exposure": {"name": "currency_exposure", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "currency_code": {"name": "currency_code", "type": "text", "primaryKey": false, "notNull": true}, "exposure_type": {"name": "exposure_type", "type": "text", "primaryKey": false, "notNull": true}, "total_amount": {"name": "total_amount", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "base_amount": {"name": "base_amount", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "unrealized_gain_loss": {"name": "unrealized_gain_loss", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "risk_level": {"name": "risk_level", "type": "text", "primaryKey": false, "notNull": false, "default": "'medium'"}, "hedge_ratio": {"name": "hedge_ratio", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "calculation_date": {"name": "calculation_date", "type": "text", "primaryKey": false, "notNull": true}, "next_revaluation_date": {"name": "next_revaluation_date", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"currency_exposure_company_id_idx": {"name": "currency_exposure_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currency_exposure_currency_idx": {"name": "currency_exposure_currency_idx", "columns": [{"expression": "currency_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currency_exposure_type_idx": {"name": "currency_exposure_type_idx", "columns": [{"expression": "exposure_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currency_exposure_risk_idx": {"name": "currency_exposure_risk_idx", "columns": [{"expression": "risk_level", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currency_exposure_date_idx": {"name": "currency_exposure_date_idx", "columns": [{"expression": "calculation_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"currency_exposure_company_id_companies_id_fk": {"name": "currency_exposure_company_id_companies_id_fk", "tableFrom": "currency_exposure", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"currency_exposure_company_id_currency_code_exposure_type_calculation_date_unique": {"name": "currency_exposure_company_id_currency_code_exposure_type_calculation_date_unique", "nullsNotDistinct": false, "columns": ["company_id", "currency_code", "exposure_type", "calculation_date"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.currency_risk_assessments": {"name": "currency_risk_assessments", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "assessment_date": {"name": "assessment_date", "type": "text", "primaryKey": false, "notNull": true}, "assessment_type": {"name": "assessment_type", "type": "text", "primaryKey": false, "notNull": true}, "base_currency_code": {"name": "base_currency_code", "type": "text", "primaryKey": false, "notNull": true}, "currency_exposures": {"name": "currency_exposures", "type": "text", "primaryKey": false, "notNull": true}, "total_exposure_base_currency": {"name": "total_exposure_base_currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "value_at_risk_1day": {"name": "value_at_risk_1day", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "value_at_risk_1week": {"name": "value_at_risk_1week", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "value_at_risk_1month": {"name": "value_at_risk_1month", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "overall_risk_level": {"name": "overall_risk_level", "type": "text", "primaryKey": false, "notNull": false, "default": "'medium'"}, "highest_risk_currency": {"name": "highest_risk_currency", "type": "text", "primaryKey": false, "notNull": false}, "risk_concentration_ratio": {"name": "risk_concentration_ratio", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "hedged_exposure_percentage": {"name": "hedged_exposure_percentage", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "unhedged_exposure": {"name": "unhedged_exposure", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "hedging_effectiveness": {"name": "hedging_effectiveness", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "risk_mitigation_recommendations": {"name": "risk_mitigation_recommendations", "type": "text", "primaryKey": false, "notNull": false}, "hedging_recommendations": {"name": "hedging_recommendations", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "assessment_method": {"name": "assessment_method", "type": "text", "primaryKey": false, "notNull": false, "default": "'historical'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"currency_risk_assessments_company_id_idx": {"name": "currency_risk_assessments_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currency_risk_assessments_date_idx": {"name": "currency_risk_assessments_date_idx", "columns": [{"expression": "assessment_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currency_risk_assessments_risk_idx": {"name": "currency_risk_assessments_risk_idx", "columns": [{"expression": "overall_risk_level", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currency_risk_assessments_currency_idx": {"name": "currency_risk_assessments_currency_idx", "columns": [{"expression": "base_currency_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"currency_risk_assessments_company_id_companies_id_fk": {"name": "currency_risk_assessments_company_id_companies_id_fk", "tableFrom": "currency_risk_assessments", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customers": {"name": "customers", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "contact_name": {"name": "contact_name", "type": "text", "primaryKey": false, "notNull": false}, "contact_phone": {"name": "contact_phone", "type": "text", "primaryKey": false, "notNull": false}, "contact_email": {"name": "contact_email", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "tax_id": {"name": "tax_id", "type": "text", "primaryKey": false, "notNull": false}, "bank": {"name": "bank", "type": "text", "primaryKey": false, "notNull": false}, "incoterm": {"name": "incoterm", "type": "text", "primaryKey": false, "notNull": false}, "payment_term": {"name": "payment_term", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"customers_company_id_idx": {"name": "customers_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customers_company_id_companies_id_fk": {"name": "customers_company_id_companies_id_fk", "tableFrom": "customers", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.declaration_items": {"name": "declaration_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "declaration_id": {"name": "declaration_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "hs_code": {"name": "hs_code", "type": "text", "primaryKey": false, "notNull": false}, "unit_value": {"name": "unit_value", "type": "text", "primaryKey": false, "notNull": false}, "total_value": {"name": "total_value", "type": "text", "primaryKey": false, "notNull": false}, "value_currency": {"name": "value_currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "value_method": {"name": "value_method", "type": "text", "primaryKey": false, "notNull": false}, "quality_inspection_id": {"name": "quality_inspection_id", "type": "text", "primaryKey": false, "notNull": false}, "quality_status": {"name": "quality_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "quality_notes": {"name": "quality_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"declaration_items_total_value_idx": {"name": "declaration_items_total_value_idx", "columns": [{"expression": "total_value", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "declaration_items_value_method_idx": {"name": "declaration_items_value_method_idx", "columns": [{"expression": "value_method", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "declaration_items_value_currency_idx": {"name": "declaration_items_value_currency_idx", "columns": [{"expression": "value_currency", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"declaration_items_declaration_id_declarations_id_fk": {"name": "declaration_items_declaration_id_declarations_id_fk", "tableFrom": "declaration_items", "tableTo": "declarations", "columnsFrom": ["declaration_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "declaration_items_product_id_products_id_fk": {"name": "declaration_items_product_id_products_id_fk", "tableFrom": "declaration_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "declaration_items_quality_inspection_id_quality_inspections_id_fk": {"name": "declaration_items_quality_inspection_id_quality_inspections_id_fk", "tableFrom": "declaration_items", "tableTo": "quality_inspections", "columnsFrom": ["quality_inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.declarations": {"name": "declarations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "sales_contract_id": {"name": "sales_contract_id", "type": "text", "primaryKey": false, "notNull": false}, "contract_reference": {"name": "contract_reference", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"declarations_company_id_idx": {"name": "declarations_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "declarations_sales_contract_id_idx": {"name": "declarations_sales_contract_id_idx", "columns": [{"expression": "sales_contract_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"declarations_company_id_companies_id_fk": {"name": "declarations_company_id_companies_id_fk", "tableFrom": "declarations", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "declarations_sales_contract_id_sales_contracts_id_fk": {"name": "declarations_sales_contract_id_sales_contracts_id_fk", "tableFrom": "declarations", "tableTo": "sales_contracts", "columnsFrom": ["sales_contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.demand_forecasts": {"name": "demand_forecasts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "forecast_period": {"name": "forecast_period", "type": "text", "primaryKey": false, "notNull": true}, "forecasted_demand": {"name": "forecasted_demand", "type": "text", "primaryKey": false, "notNull": true}, "confidence_level": {"name": "confidence_level", "type": "text", "primaryKey": false, "notNull": false, "default": "'medium'"}, "forecast_method": {"name": "forecast_method", "type": "text", "primaryKey": false, "notNull": false, "default": "'pipeline'"}, "base_data_source": {"name": "base_data_source", "type": "text", "primaryKey": false, "notNull": false}, "seasonality_applied": {"name": "seasonality_applied", "type": "text", "primaryKey": false, "notNull": false, "default": "'false'"}, "trend_factor_applied": {"name": "trend_factor_applied", "type": "text", "primaryKey": false, "notNull": false, "default": "'1.0'"}, "supplier_preferences": {"name": "supplier_preferences", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "approved_by": {"name": "approved_by", "type": "text", "primaryKey": false, "notNull": false}, "approval_status": {"name": "approval_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"demand_forecasts_company_id_idx": {"name": "demand_forecasts_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "demand_forecasts_product_id_idx": {"name": "demand_forecasts_product_id_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "demand_forecasts_period_idx": {"name": "demand_forecasts_period_idx", "columns": [{"expression": "forecast_period", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "demand_forecasts_approval_status_idx": {"name": "demand_forecasts_approval_status_idx", "columns": [{"expression": "approval_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"demand_forecasts_company_id_companies_id_fk": {"name": "demand_forecasts_company_id_companies_id_fk", "tableFrom": "demand_forecasts", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "demand_forecasts_product_id_products_id_fk": {"name": "demand_forecasts_product_id_products_id_fk", "tableFrom": "demand_forecasts", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "declaration_id": {"name": "declaration_id", "type": "text", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "filetype": {"name": "filetype", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"documents_company_id_idx": {"name": "documents_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documents_company_id_companies_id_fk": {"name": "documents_company_id_companies_id_fk", "tableFrom": "documents", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "documents_declaration_id_declarations_id_fk": {"name": "documents_declaration_id_declarations_id_fk", "tableFrom": "documents", "tableTo": "declarations", "columnsFrom": ["declaration_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.exchange_rate_history": {"name": "exchange_rate_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "from_currency_code": {"name": "from_currency_code", "type": "text", "primaryKey": false, "notNull": true}, "to_currency_code": {"name": "to_currency_code", "type": "text", "primaryKey": false, "notNull": true}, "exchange_rate": {"name": "exchange_rate", "type": "text", "primaryKey": false, "notNull": true}, "effective_date": {"name": "effective_date", "type": "text", "primaryKey": false, "notNull": true}, "rate_source": {"name": "rate_source", "type": "text", "primaryKey": false, "notNull": false, "default": "'manual'"}, "rate_type": {"name": "rate_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'spot'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"exchange_rate_history_company_id_idx": {"name": "exchange_rate_history_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "exchange_rate_history_currencies_idx": {"name": "exchange_rate_history_currencies_idx", "columns": [{"expression": "from_currency_code", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "to_currency_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "exchange_rate_history_date_idx": {"name": "exchange_rate_history_date_idx", "columns": [{"expression": "effective_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "exchange_rate_history_source_idx": {"name": "exchange_rate_history_source_idx", "columns": [{"expression": "rate_source", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"exchange_rate_history_company_id_companies_id_fk": {"name": "exchange_rate_history_company_id_companies_id_fk", "tableFrom": "exchange_rate_history", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.export_revenue_analytics": {"name": "export_revenue_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "period_type": {"name": "period_type", "type": "text", "primaryKey": false, "notNull": true}, "period_start": {"name": "period_start", "type": "text", "primaryKey": false, "notNull": true}, "period_end": {"name": "period_end", "type": "text", "primaryKey": false, "notNull": true}, "total_revenue_usd": {"name": "total_revenue_usd", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "total_revenue_local": {"name": "total_revenue_local", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "local_currency_code": {"name": "local_currency_code", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "export_volume_containers": {"name": "export_volume_containers", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "export_volume_weight": {"name": "export_volume_weight", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "export_destinations_count": {"name": "export_destinations_count", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "top_export_destination": {"name": "top_export_destination", "type": "text", "primaryKey": false, "notNull": false}, "currency_breakdown": {"name": "currency_breakdown", "type": "text", "primaryKey": false, "notNull": false}, "exchange_rate_impact": {"name": "exchange_rate_impact", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "average_order_value": {"name": "average_order_value", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "revenue_per_container": {"name": "revenue_per_container", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "profit_margin_percentage": {"name": "profit_margin_percentage", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "currency_exposure_risk": {"name": "currency_exposure_risk", "type": "text", "primaryKey": false, "notNull": false, "default": "'medium'"}, "concentration_risk": {"name": "concentration_risk", "type": "text", "primaryKey": false, "notNull": false, "default": "'medium'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"export_revenue_analytics_company_id_idx": {"name": "export_revenue_analytics_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "export_revenue_analytics_period_idx": {"name": "export_revenue_analytics_period_idx", "columns": [{"expression": "period_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "period_start", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "period_end", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "export_revenue_analytics_currency_idx": {"name": "export_revenue_analytics_currency_idx", "columns": [{"expression": "local_currency_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"export_revenue_analytics_company_id_companies_id_fk": {"name": "export_revenue_analytics_company_id_companies_id_fk", "tableFrom": "export_revenue_analytics", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.financial_transactions": {"name": "financial_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "transaction_type": {"name": "transaction_type", "type": "text", "primaryKey": false, "notNull": true}, "reference_id": {"name": "reference_id", "type": "text", "primaryKey": false, "notNull": false}, "reference_type": {"name": "reference_type", "type": "text", "primaryKey": false, "notNull": false}, "account_type": {"name": "account_type", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "text", "primaryKey": false, "notNull": true}, "currency_code": {"name": "currency_code", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "base_amount": {"name": "base_amount", "type": "text", "primaryKey": false, "notNull": false}, "base_currency_code": {"name": "base_currency_code", "type": "text", "primaryKey": false, "notNull": false}, "exchange_rate": {"name": "exchange_rate", "type": "text", "primaryKey": false, "notNull": false, "default": "'1.0'"}, "transaction_date": {"name": "transaction_date", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "approved_by": {"name": "approved_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"financial_transactions_company_id_idx": {"name": "financial_transactions_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "financial_transactions_type_idx": {"name": "financial_transactions_type_idx", "columns": [{"expression": "transaction_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "financial_transactions_reference_idx": {"name": "financial_transactions_reference_idx", "columns": [{"expression": "reference_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "reference_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "financial_transactions_account_idx": {"name": "financial_transactions_account_idx", "columns": [{"expression": "account_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "financial_transactions_currency_idx": {"name": "financial_transactions_currency_idx", "columns": [{"expression": "currency_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "financial_transactions_date_idx": {"name": "financial_transactions_date_idx", "columns": [{"expression": "transaction_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "financial_transactions_status_idx": {"name": "financial_transactions_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"financial_transactions_company_id_companies_id_fk": {"name": "financial_transactions_company_id_companies_id_fk", "tableFrom": "financial_transactions", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forecast_parameters": {"name": "forecast_parameters", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "seasonality_factor": {"name": "seasonality_factor", "type": "text", "primaryKey": false, "notNull": false, "default": "'1.0'"}, "trend_factor": {"name": "trend_factor", "type": "text", "primaryKey": false, "notNull": false, "default": "'1.0'"}, "lead_time_buffer_days": {"name": "lead_time_buffer_days", "type": "text", "primaryKey": false, "notNull": false, "default": "'14'"}, "safety_stock_percentage": {"name": "safety_stock_percentage", "type": "text", "primaryKey": false, "notNull": false, "default": "'0.15'"}, "container_optimization_enabled": {"name": "container_optimization_enabled", "type": "text", "primaryKey": false, "notNull": false, "default": "'true'"}, "preferred_container_size": {"name": "preferred_container_size", "type": "text", "primaryKey": false, "notNull": false, "default": "'40ft'"}, "minimum_order_efficiency": {"name": "minimum_order_efficiency", "type": "text", "primaryKey": false, "notNull": false, "default": "'0.8'"}, "historical_periods_to_analyze": {"name": "historical_periods_to_analyze", "type": "text", "primaryKey": false, "notNull": false, "default": "'12'"}, "outlier_detection_enabled": {"name": "outlier_detection_enabled", "type": "text", "primaryKey": false, "notNull": false, "default": "'true'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"forecast_parameters_company_id_idx": {"name": "forecast_parameters_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forecast_parameters_product_id_idx": {"name": "forecast_parameters_product_id_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forecast_parameters_unique_product": {"name": "forecast_parameters_unique_product", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forecast_parameters_company_id_companies_id_fk": {"name": "forecast_parameters_company_id_companies_id_fk", "tableFrom": "forecast_parameters", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "forecast_parameters_product_id_products_id_fk": {"name": "forecast_parameters_product_id_products_id_fk", "tableFrom": "forecast_parameters", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.inspection_results": {"name": "inspection_results", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "inspection_id": {"name": "inspection_id", "type": "text", "primaryKey": false, "notNull": true}, "standard_id": {"name": "standard_id", "type": "text", "primaryKey": false, "notNull": false}, "measured_value": {"name": "measured_value", "type": "text", "primaryKey": false, "notNull": false}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"inspection_results_inspection_id_quality_inspections_id_fk": {"name": "inspection_results_inspection_id_quality_inspections_id_fk", "tableFrom": "inspection_results", "tableTo": "quality_inspections", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inspection_results_standard_id_quality_standards_id_fk": {"name": "inspection_results_standard_id_quality_standards_id_fk", "tableFrom": "inspection_results", "tableTo": "quality_standards", "columnsFrom": ["standard_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.locations": {"name": "locations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "current_utilization": {"name": "current_utilization", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_temperature_controlled": {"name": "is_temperature_controlled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "security_level": {"name": "security_level", "type": "text", "primaryKey": false, "notNull": false, "default": "'medium'"}, "parent_location_id": {"name": "parent_location_id", "type": "text", "primaryKey": false, "notNull": false}, "location_code": {"name": "location_code", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "floor_level": {"name": "floor_level", "type": "integer", "primaryKey": false, "notNull": false}, "zone": {"name": "zone", "type": "text", "primaryKey": false, "notNull": false}, "allows_mixed_products": {"name": "allows_mixed_products", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "requires_quality_check": {"name": "requires_quality_check", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "automated_handling": {"name": "automated_handling", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"locations_company_id_idx": {"name": "locations_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "locations_type_idx": {"name": "locations_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "locations_active_idx": {"name": "locations_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "locations_parent_idx": {"name": "locations_parent_idx", "columns": [{"expression": "parent_location_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "locations_code_idx": {"name": "locations_code_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "location_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"locations_company_id_companies_id_fk": {"name": "locations_company_id_companies_id_fk", "tableFrom": "locations", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "locations_parent_location_id_locations_id_fk": {"name": "locations_parent_location_id_locations_id_fk", "tableFrom": "locations", "tableTo": "locations", "columnsFrom": ["parent_location_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"locations_company_code_unique": {"name": "locations_company_code_unique", "nullsNotDistinct": false, "columns": ["company_id", "location_code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.material_consumption": {"name": "material_consumption", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "work_order_id": {"name": "work_order_id", "type": "text", "primaryKey": false, "notNull": true}, "raw_material_lot_id": {"name": "raw_material_lot_id", "type": "text", "primaryKey": false, "notNull": true}, "qty_consumed": {"name": "qty_consumed", "type": "text", "primaryKey": false, "notNull": true}, "unit_cost": {"name": "unit_cost", "type": "text", "primaryKey": false, "notNull": true}, "total_cost": {"name": "total_cost", "type": "text", "primaryKey": false, "notNull": true}, "consumed_date": {"name": "consumed_date", "type": "text", "primaryKey": false, "notNull": true}, "consumed_by": {"name": "consumed_by", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"material_consumption_company_id_idx": {"name": "material_consumption_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "material_consumption_work_order_idx": {"name": "material_consumption_work_order_idx", "columns": [{"expression": "work_order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"material_consumption_company_id_companies_id_fk": {"name": "material_consumption_company_id_companies_id_fk", "tableFrom": "material_consumption", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "material_consumption_work_order_id_work_orders_id_fk": {"name": "material_consumption_work_order_id_work_orders_id_fk", "tableFrom": "material_consumption", "tableTo": "work_orders", "columnsFrom": ["work_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "material_consumption_raw_material_lot_id_raw_material_lots_id_fk": {"name": "material_consumption_raw_material_lot_id_raw_material_lots_id_fk", "tableFrom": "material_consumption", "tableTo": "raw_material_lots", "columnsFrom": ["raw_material_lot_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.procurement_plans": {"name": "procurement_plans", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "raw_material_id": {"name": "raw_material_id", "type": "text", "primaryKey": false, "notNull": true}, "demand_forecast_id": {"name": "demand_forecast_id", "type": "text", "primaryKey": false, "notNull": false}, "planned_qty": {"name": "planned_qty", "type": "text", "primaryKey": false, "notNull": true}, "target_date": {"name": "target_date", "type": "text", "primaryKey": false, "notNull": true}, "supplier_id": {"name": "supplier_id", "type": "text", "primaryKey": false, "notNull": false}, "estimated_cost": {"name": "estimated_cost", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "estimated_lead_time": {"name": "estimated_lead_time", "type": "text", "primaryKey": false, "notNull": false, "default": "'30'"}, "container_optimization": {"name": "container_optimization", "type": "text", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true, "default": "'normal'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'draft'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "approved_by": {"name": "approved_by", "type": "text", "primaryKey": false, "notNull": false}, "approved_at": {"name": "approved_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"procurement_plans_company_id_idx": {"name": "procurement_plans_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "procurement_plans_raw_material_id_idx": {"name": "procurement_plans_raw_material_id_idx", "columns": [{"expression": "raw_material_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "procurement_plans_status_idx": {"name": "procurement_plans_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "procurement_plans_target_date_idx": {"name": "procurement_plans_target_date_idx", "columns": [{"expression": "target_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "procurement_plans_priority_idx": {"name": "procurement_plans_priority_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"procurement_plans_company_id_companies_id_fk": {"name": "procurement_plans_company_id_companies_id_fk", "tableFrom": "procurement_plans", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "procurement_plans_raw_material_id_raw_materials_id_fk": {"name": "procurement_plans_raw_material_id_raw_materials_id_fk", "tableFrom": "procurement_plans", "tableTo": "raw_materials", "columnsFrom": ["raw_material_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "procurement_plans_demand_forecast_id_demand_forecasts_id_fk": {"name": "procurement_plans_demand_forecast_id_demand_forecasts_id_fk", "tableFrom": "procurement_plans", "tableTo": "demand_forecasts", "columnsFrom": ["demand_forecast_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "procurement_plans_supplier_id_suppliers_id_fk": {"name": "procurement_plans_supplier_id_suppliers_id_fk", "tableFrom": "procurement_plans", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.product_pricing_history": {"name": "product_pricing_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "old_price": {"name": "old_price", "type": "text", "primaryKey": false, "notNull": false}, "new_price": {"name": "new_price", "type": "text", "primaryKey": false, "notNull": false}, "old_base_price": {"name": "old_base_price", "type": "text", "primaryKey": false, "notNull": false}, "new_base_price": {"name": "new_base_price", "type": "text", "primaryKey": false, "notNull": false}, "old_cost_price": {"name": "old_cost_price", "type": "text", "primaryKey": false, "notNull": false}, "new_cost_price": {"name": "new_cost_price", "type": "text", "primaryKey": false, "notNull": false}, "old_margin_percentage": {"name": "old_margin_percentage", "type": "text", "primaryKey": false, "notNull": false}, "new_margin_percentage": {"name": "new_margin_percentage", "type": "text", "primaryKey": false, "notNull": false}, "change_reason": {"name": "change_reason", "type": "text", "primaryKey": false, "notNull": false}, "changed_by": {"name": "changed_by", "type": "text", "primaryKey": false, "notNull": false}, "change_notes": {"name": "change_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"pricing_history_company_id_idx": {"name": "pricing_history_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pricing_history_product_id_idx": {"name": "pricing_history_product_id_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pricing_history_created_at_idx": {"name": "pricing_history_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"product_pricing_history_company_id_companies_id_fk": {"name": "product_pricing_history_company_id_companies_id_fk", "tableFrom": "product_pricing_history", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "product_pricing_history_product_id_products_id_fk": {"name": "product_pricing_history_product_id_products_id_fk", "tableFrom": "product_pricing_history", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": true}, "hs_code": {"name": "hs_code", "type": "text", "primaryKey": false, "notNull": false}, "origin": {"name": "origin", "type": "text", "primaryKey": false, "notNull": false}, "package": {"name": "package", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": false}, "base_price": {"name": "base_price", "type": "text", "primaryKey": false, "notNull": false}, "cost_price": {"name": "cost_price", "type": "text", "primaryKey": false, "notNull": false}, "margin_percentage": {"name": "margin_percentage", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "price_updated_at": {"name": "price_updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "inspection_required": {"name": "inspection_required", "type": "text", "primaryKey": false, "notNull": false, "default": "'false'"}, "quality_tolerance": {"name": "quality_tolerance", "type": "text", "primaryKey": false, "notNull": false}, "quality_notes": {"name": "quality_notes", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"products_company_id_idx": {"name": "products_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_sku_company_idx": {"name": "products_sku_company_idx", "columns": [{"expression": "sku", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"products_company_id_companies_id_fk": {"name": "products_company_id_companies_id_fk", "tableFrom": "products", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.purchase_contract_items": {"name": "purchase_contract_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "contract_id": {"name": "contract_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "pricing_method": {"name": "pricing_method", "type": "text", "primaryKey": false, "notNull": false}, "cost_basis": {"name": "cost_basis", "type": "text", "primaryKey": false, "notNull": false}, "price_currency": {"name": "price_currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}}, "indexes": {"purchase_contract_items_pricing_method_idx": {"name": "purchase_contract_items_pricing_method_idx", "columns": [{"expression": "pricing_method", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"purchase_contract_items_contract_id_purchase_contracts_id_fk": {"name": "purchase_contract_items_contract_id_purchase_contracts_id_fk", "tableFrom": "purchase_contract_items", "tableTo": "purchase_contracts", "columnsFrom": ["contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.purchase_contracts": {"name": "purchase_contracts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "supplier_id": {"name": "supplier_id", "type": "text", "primaryKey": false, "notNull": true}, "template_id": {"name": "template_id", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"purchase_contracts_company_id_idx": {"name": "purchase_contracts_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"purchase_contracts_company_id_companies_id_fk": {"name": "purchase_contracts_company_id_companies_id_fk", "tableFrom": "purchase_contracts", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "purchase_contracts_supplier_id_suppliers_id_fk": {"name": "purchase_contracts_supplier_id_suppliers_id_fk", "tableFrom": "purchase_contracts", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "purchase_contracts_template_id_contract_templates_id_fk": {"name": "purchase_contracts_template_id_contract_templates_id_fk", "tableFrom": "purchase_contracts", "tableTo": "contract_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quality_certificates": {"name": "quality_certificates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "inspection_id": {"name": "inspection_id", "type": "text", "primaryKey": false, "notNull": false}, "certificate_number": {"name": "certificate_number", "type": "text", "primaryKey": false, "notNull": true}, "certificate_type": {"name": "certificate_type", "type": "text", "primaryKey": false, "notNull": true}, "issued_date": {"name": "issued_date", "type": "text", "primaryKey": false, "notNull": true}, "valid_until": {"name": "valid_until", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"quality_certificates_company_id_idx": {"name": "quality_certificates_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quality_certificates_company_id_companies_id_fk": {"name": "quality_certificates_company_id_companies_id_fk", "tableFrom": "quality_certificates", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_certificates_inspection_id_quality_inspections_id_fk": {"name": "quality_certificates_inspection_id_quality_inspections_id_fk", "tableFrom": "quality_certificates", "tableTo": "quality_inspections", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quality_defects": {"name": "quality_defects", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "inspection_id": {"name": "inspection_id", "type": "text", "primaryKey": false, "notNull": false}, "work_order_id": {"name": "work_order_id", "type": "text", "primaryKey": false, "notNull": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false}, "defect_type": {"name": "defect_type", "type": "text", "primaryKey": false, "notNull": true}, "severity": {"name": "severity", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "quantity_affected": {"name": "quantity_affected", "type": "text", "primaryKey": false, "notNull": false}, "corrective_action": {"name": "corrective_action", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"quality_defects_company_id_idx": {"name": "quality_defects_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quality_defects_company_id_companies_id_fk": {"name": "quality_defects_company_id_companies_id_fk", "tableFrom": "quality_defects", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_defects_inspection_id_quality_inspections_id_fk": {"name": "quality_defects_inspection_id_quality_inspections_id_fk", "tableFrom": "quality_defects", "tableTo": "quality_inspections", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_defects_work_order_id_work_orders_id_fk": {"name": "quality_defects_work_order_id_work_orders_id_fk", "tableFrom": "quality_defects", "tableTo": "work_orders", "columnsFrom": ["work_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_defects_product_id_products_id_fk": {"name": "quality_defects_product_id_products_id_fk", "tableFrom": "quality_defects", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quality_inspections": {"name": "quality_inspections", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "work_order_id": {"name": "work_order_id", "type": "text", "primaryKey": false, "notNull": false}, "inspection_type": {"name": "inspection_type", "type": "text", "primaryKey": false, "notNull": true}, "inspector": {"name": "inspector", "type": "text", "primaryKey": false, "notNull": true}, "inspection_date": {"name": "inspection_date", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false}, "photos": {"name": "photos", "type": "text", "primaryKey": false, "notNull": false}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "archived_at": {"name": "archived_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "archived_by": {"name": "archived_by", "type": "text", "primaryKey": false, "notNull": false}, "archive_reason": {"name": "archive_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"quality_inspections_company_id_idx": {"name": "quality_inspections_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quality_inspections_company_id_companies_id_fk": {"name": "quality_inspections_company_id_companies_id_fk", "tableFrom": "quality_inspections", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_inspections_work_order_id_work_orders_id_fk": {"name": "quality_inspections_work_order_id_work_orders_id_fk", "tableFrom": "quality_inspections", "tableTo": "work_orders", "columnsFrom": ["work_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quality_standards": {"name": "quality_standards", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false}, "standard_name": {"name": "standard_name", "type": "text", "primaryKey": false, "notNull": true}, "specification": {"name": "specification", "type": "text", "primaryKey": false, "notNull": true}, "tolerance": {"name": "tolerance", "type": "text", "primaryKey": false, "notNull": false}, "test_method": {"name": "test_method", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"quality_standards_company_id_idx": {"name": "quality_standards_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quality_standards_company_id_companies_id_fk": {"name": "quality_standards_company_id_companies_id_fk", "tableFrom": "quality_standards", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_standards_product_id_products_id_fk": {"name": "quality_standards_product_id_products_id_fk", "tableFrom": "quality_standards", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.raw_material_lots": {"name": "raw_material_lots", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "raw_material_id": {"name": "raw_material_id", "type": "text", "primaryKey": false, "notNull": true}, "lot_number": {"name": "lot_number", "type": "text", "primaryKey": false, "notNull": false}, "supplier_id": {"name": "supplier_id", "type": "text", "primaryKey": false, "notNull": true}, "purchase_contract_id": {"name": "purchase_contract_id", "type": "text", "primaryKey": false, "notNull": false}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "unit_cost": {"name": "unit_cost", "type": "text", "primaryKey": false, "notNull": true}, "total_cost": {"name": "total_cost", "type": "text", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "received_date": {"name": "received_date", "type": "text", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "text", "primaryKey": false, "notNull": false}, "quality_status": {"name": "quality_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "inspection_id": {"name": "inspection_id", "type": "text", "primaryKey": false, "notNull": false}, "quality_approved_date": {"name": "quality_approved_date", "type": "text", "primaryKey": false, "notNull": false}, "quality_approved_by": {"name": "quality_approved_by", "type": "text", "primaryKey": false, "notNull": false}, "quality_notes": {"name": "quality_notes", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'available'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"raw_material_lots_company_id_idx": {"name": "raw_material_lots_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "raw_material_lots_material_idx": {"name": "raw_material_lots_material_idx", "columns": [{"expression": "raw_material_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "raw_material_lots_status_idx": {"name": "raw_material_lots_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"raw_material_lots_company_id_companies_id_fk": {"name": "raw_material_lots_company_id_companies_id_fk", "tableFrom": "raw_material_lots", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "raw_material_lots_raw_material_id_raw_materials_id_fk": {"name": "raw_material_lots_raw_material_id_raw_materials_id_fk", "tableFrom": "raw_material_lots", "tableTo": "raw_materials", "columnsFrom": ["raw_material_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "raw_material_lots_supplier_id_suppliers_id_fk": {"name": "raw_material_lots_supplier_id_suppliers_id_fk", "tableFrom": "raw_material_lots", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "raw_material_lots_purchase_contract_id_purchase_contracts_id_fk": {"name": "raw_material_lots_purchase_contract_id_purchase_contracts_id_fk", "tableFrom": "raw_material_lots", "tableTo": "purchase_contracts", "columnsFrom": ["purchase_contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "raw_material_lots_inspection_id_quality_inspections_id_fk": {"name": "raw_material_lots_inspection_id_quality_inspections_id_fk", "tableFrom": "raw_material_lots", "tableTo": "quality_inspections", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.raw_materials": {"name": "raw_materials", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": true}, "primary_supplier_id": {"name": "primary_supplier_id", "type": "text", "primaryKey": false, "notNull": false}, "composition": {"name": "composition", "type": "text", "primaryKey": false, "notNull": false}, "quality_grade": {"name": "quality_grade", "type": "text", "primaryKey": false, "notNull": false}, "specifications": {"name": "specifications", "type": "text", "primaryKey": false, "notNull": false}, "standard_cost": {"name": "standard_cost", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "reorder_point": {"name": "reorder_point", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "max_stock_level": {"name": "max_stock_level", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "lead_time_days": {"name": "lead_time_days", "type": "text", "primaryKey": false, "notNull": false, "default": "'7'"}, "inspection_required": {"name": "inspection_required", "type": "text", "primaryKey": false, "notNull": false, "default": "'false'"}, "quality_tolerance": {"name": "quality_tolerance", "type": "text", "primaryKey": false, "notNull": false}, "quality_notes": {"name": "quality_notes", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"raw_materials_company_id_idx": {"name": "raw_materials_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "raw_materials_category_idx": {"name": "raw_materials_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "raw_materials_supplier_idx": {"name": "raw_materials_supplier_idx", "columns": [{"expression": "primary_supplier_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"raw_materials_company_id_companies_id_fk": {"name": "raw_materials_company_id_companies_id_fk", "tableFrom": "raw_materials", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "raw_materials_primary_supplier_id_suppliers_id_fk": {"name": "raw_materials_primary_supplier_id_suppliers_id_fk", "tableFrom": "raw_materials", "tableTo": "suppliers", "columnsFrom": ["primary_supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sales_contract_items": {"name": "sales_contract_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "contract_id": {"name": "contract_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "pricing_method": {"name": "pricing_method", "type": "text", "primaryKey": false, "notNull": false}, "margin_percentage": {"name": "margin_percentage", "type": "text", "primaryKey": false, "notNull": false}, "cost_basis": {"name": "cost_basis", "type": "text", "primaryKey": false, "notNull": false}, "price_currency": {"name": "price_currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}}, "indexes": {"sales_contract_items_pricing_method_idx": {"name": "sales_contract_items_pricing_method_idx", "columns": [{"expression": "pricing_method", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sales_contract_items_margin_idx": {"name": "sales_contract_items_margin_idx", "columns": [{"expression": "margin_percentage", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sales_contract_items_contract_id_sales_contracts_id_fk": {"name": "sales_contract_items_contract_id_sales_contracts_id_fk", "tableFrom": "sales_contract_items", "tableTo": "sales_contracts", "columnsFrom": ["contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sales_contract_items_product_id_products_id_fk": {"name": "sales_contract_items_product_id_products_id_fk", "tableFrom": "sales_contract_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sales_contracts": {"name": "sales_contracts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": true}, "template_id": {"name": "template_id", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"sales_contracts_company_id_idx": {"name": "sales_contracts_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sales_contracts_company_id_companies_id_fk": {"name": "sales_contracts_company_id_companies_id_fk", "tableFrom": "sales_contracts", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sales_contracts_customer_id_customers_id_fk": {"name": "sales_contracts_customer_id_customers_id_fk", "tableFrom": "sales_contracts", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sales_contracts_template_id_contract_templates_id_fk": {"name": "sales_contracts_template_id_contract_templates_id_fk", "tableFrom": "sales_contracts", "tableTo": "contract_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.samples": {"name": "samples", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "sample_direction": {"name": "sample_direction", "type": "text", "primaryKey": false, "notNull": true, "default": "'outbound'"}, "sample_purpose": {"name": "sample_purpose", "type": "text", "primaryKey": false, "notNull": false}, "sender_type": {"name": "sender_type", "type": "text", "primaryKey": false, "notNull": false}, "receiver_type": {"name": "receiver_type", "type": "text", "primaryKey": false, "notNull": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false}, "supplier_id": {"name": "supplier_id", "type": "text", "primaryKey": false, "notNull": false}, "sample_type": {"name": "sample_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'development'"}, "approval_status": {"name": "approval_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "approved_by": {"name": "approved_by", "type": "text", "primaryKey": false, "notNull": false}, "approved_date": {"name": "approved_date", "type": "text", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "text", "primaryKey": false, "notNull": false}, "received_date": {"name": "received_date", "type": "text", "primaryKey": false, "notNull": false}, "testing_status": {"name": "testing_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'not_started'"}, "testing_results": {"name": "testing_results", "type": "text", "primaryKey": false, "notNull": false}, "quote_requested": {"name": "quote_requested", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "quote_provided": {"name": "quote_provided", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "text", "primaryKey": false, "notNull": false}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": false}, "specifications": {"name": "specifications", "type": "text", "primaryKey": false, "notNull": false}, "quality_requirements": {"name": "quality_requirements", "type": "text", "primaryKey": false, "notNull": false}, "delivery_date": {"name": "delivery_date", "type": "text", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": false, "default": "'normal'"}, "cost": {"name": "cost", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"samples_company_id_idx": {"name": "samples_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_customer_id_idx": {"name": "samples_customer_id_idx", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_product_id_idx": {"name": "samples_product_id_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_supplier_id_idx": {"name": "samples_supplier_id_idx", "columns": [{"expression": "supplier_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_direction_idx": {"name": "samples_direction_idx", "columns": [{"expression": "sample_direction", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_approval_status_idx": {"name": "samples_approval_status_idx", "columns": [{"expression": "approval_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_testing_status_idx": {"name": "samples_testing_status_idx", "columns": [{"expression": "testing_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_sample_type_idx": {"name": "samples_sample_type_idx", "columns": [{"expression": "sample_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_priority_idx": {"name": "samples_priority_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"samples_company_id_companies_id_fk": {"name": "samples_company_id_companies_id_fk", "tableFrom": "samples", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "samples_customer_id_customers_id_fk": {"name": "samples_customer_id_customers_id_fk", "tableFrom": "samples", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "samples_product_id_products_id_fk": {"name": "samples_product_id_products_id_fk", "tableFrom": "samples", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "samples_supplier_id_suppliers_id_fk": {"name": "samples_supplier_id_suppliers_id_fk", "tableFrom": "samples", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.shipment_items": {"name": "shipment_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "shipment_id": {"name": "shipment_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "stock_lot_id": {"name": "stock_lot_id", "type": "text", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "text", "primaryKey": false, "notNull": true}, "unit_price": {"name": "unit_price", "type": "text", "primaryKey": false, "notNull": false}, "total_price": {"name": "total_price", "type": "text", "primaryKey": false, "notNull": false}, "weight_per_unit": {"name": "weight_per_unit", "type": "text", "primaryKey": false, "notNull": false}, "volume_per_unit": {"name": "volume_per_unit", "type": "text", "primaryKey": false, "notNull": false}, "quality_certificate_id": {"name": "quality_certificate_id", "type": "text", "primaryKey": false, "notNull": false}, "batch_number": {"name": "batch_number", "type": "text", "primaryKey": false, "notNull": false}, "lot_number": {"name": "lot_number", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"shipment_items_company_id_idx": {"name": "shipment_items_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipment_items_shipment_id_idx": {"name": "shipment_items_shipment_id_idx", "columns": [{"expression": "shipment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipment_items_product_id_idx": {"name": "shipment_items_product_id_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipment_items_stock_lot_id_idx": {"name": "shipment_items_stock_lot_id_idx", "columns": [{"expression": "stock_lot_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"shipment_items_company_id_companies_id_fk": {"name": "shipment_items_company_id_companies_id_fk", "tableFrom": "shipment_items", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "shipment_items_shipment_id_shipments_id_fk": {"name": "shipment_items_shipment_id_shipments_id_fk", "tableFrom": "shipment_items", "tableTo": "shipments", "columnsFrom": ["shipment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "shipment_items_product_id_products_id_fk": {"name": "shipment_items_product_id_products_id_fk", "tableFrom": "shipment_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "shipment_items_stock_lot_id_stock_lots_id_fk": {"name": "shipment_items_stock_lot_id_stock_lots_id_fk", "tableFrom": "shipment_items", "tableTo": "stock_lots", "columnsFrom": ["stock_lot_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "shipment_items_quality_certificate_id_quality_certificates_id_fk": {"name": "shipment_items_quality_certificate_id_quality_certificates_id_fk", "tableFrom": "shipment_items", "tableTo": "quality_certificates", "columnsFrom": ["quality_certificate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.shipments": {"name": "shipments", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "shipment_number": {"name": "shipment_number", "type": "text", "primaryKey": false, "notNull": true}, "sales_contract_id": {"name": "sales_contract_id", "type": "text", "primaryKey": false, "notNull": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": true}, "shipping_method": {"name": "shipping_method", "type": "text", "primaryKey": false, "notNull": true}, "carrier": {"name": "carrier", "type": "text", "primaryKey": false, "notNull": false}, "service_type": {"name": "service_type", "type": "text", "primaryKey": false, "notNull": false}, "tracking_number": {"name": "tracking_number", "type": "text", "primaryKey": false, "notNull": false}, "pickup_address": {"name": "pickup_address", "type": "jsonb", "primaryKey": false, "notNull": false}, "delivery_address": {"name": "delivery_address", "type": "jsonb", "primaryKey": false, "notNull": false}, "pickup_location_id": {"name": "pickup_location_id", "type": "text", "primaryKey": false, "notNull": false}, "staging_location_id": {"name": "staging_location_id", "type": "text", "primaryKey": false, "notNull": false}, "pickup_instructions": {"name": "pickup_instructions", "type": "jsonb", "primaryKey": false, "notNull": false}, "location_validated": {"name": "location_validated", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'preparing'"}, "ship_date": {"name": "ship_date", "type": "text", "primaryKey": false, "notNull": false}, "estimated_delivery": {"name": "estimated_delivery", "type": "text", "primaryKey": false, "notNull": false}, "actual_delivery": {"name": "actual_delivery", "type": "text", "primaryKey": false, "notNull": false}, "shipping_cost": {"name": "shipping_cost", "type": "text", "primaryKey": false, "notNull": false}, "insurance_cost": {"name": "insurance_cost", "type": "text", "primaryKey": false, "notNull": false}, "total_value": {"name": "total_value", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "total_weight": {"name": "total_weight", "type": "text", "primaryKey": false, "notNull": false}, "total_volume": {"name": "total_volume", "type": "text", "primaryKey": false, "notNull": false}, "package_count": {"name": "package_count", "type": "text", "primaryKey": false, "notNull": false, "default": "'1'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "special_instructions": {"name": "special_instructions", "type": "text", "primaryKey": false, "notNull": false}, "customs_declaration": {"name": "customs_declaration", "type": "text", "primaryKey": false, "notNull": false}, "requires_certificate": {"name": "requires_certificate", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "certificate_attached": {"name": "certificate_attached", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"shipments_company_id_idx": {"name": "shipments_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipments_status_idx": {"name": "shipments_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipments_ship_date_idx": {"name": "shipments_ship_date_idx", "columns": [{"expression": "ship_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipments_customer_id_idx": {"name": "shipments_customer_id_idx", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipments_contract_id_idx": {"name": "shipments_contract_id_idx", "columns": [{"expression": "sales_contract_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipments_tracking_idx": {"name": "shipments_tracking_idx", "columns": [{"expression": "tracking_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipments_pickup_location_idx": {"name": "shipments_pickup_location_idx", "columns": [{"expression": "pickup_location_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipments_staging_location_idx": {"name": "shipments_staging_location_idx", "columns": [{"expression": "staging_location_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"shipments_company_id_companies_id_fk": {"name": "shipments_company_id_companies_id_fk", "tableFrom": "shipments", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "shipments_sales_contract_id_sales_contracts_id_fk": {"name": "shipments_sales_contract_id_sales_contracts_id_fk", "tableFrom": "shipments", "tableTo": "sales_contracts", "columnsFrom": ["sales_contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "shipments_customer_id_customers_id_fk": {"name": "shipments_customer_id_customers_id_fk", "tableFrom": "shipments", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.shipping_documents": {"name": "shipping_documents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "shipment_id": {"name": "shipment_id", "type": "text", "primaryKey": false, "notNull": true}, "document_type": {"name": "document_type", "type": "text", "primaryKey": false, "notNull": true}, "document_number": {"name": "document_number", "type": "text", "primaryKey": false, "notNull": false}, "document_name": {"name": "document_name", "type": "text", "primaryKey": false, "notNull": true}, "file_path": {"name": "file_path", "type": "text", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "text", "primaryKey": false, "notNull": false}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"shipping_documents_company_id_idx": {"name": "shipping_documents_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipping_documents_shipment_id_idx": {"name": "shipping_documents_shipment_id_idx", "columns": [{"expression": "shipment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipping_documents_type_idx": {"name": "shipping_documents_type_idx", "columns": [{"expression": "document_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"shipping_documents_company_id_companies_id_fk": {"name": "shipping_documents_company_id_companies_id_fk", "tableFrom": "shipping_documents", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "shipping_documents_shipment_id_shipments_id_fk": {"name": "shipping_documents_shipment_id_shipments_id_fk", "tableFrom": "shipping_documents", "tableTo": "shipments", "columnsFrom": ["shipment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.shipping_tracking": {"name": "shipping_tracking", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "shipment_id": {"name": "shipment_id", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "carrier_status": {"name": "carrier_status", "type": "text", "primaryKey": false, "notNull": false}, "carrier_location": {"name": "carrier_location", "type": "text", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "estimated_delivery": {"name": "estimated_delivery", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"shipping_tracking_company_id_idx": {"name": "shipping_tracking_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipping_tracking_shipment_id_idx": {"name": "shipping_tracking_shipment_id_idx", "columns": [{"expression": "shipment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "shipping_tracking_timestamp_idx": {"name": "shipping_tracking_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"shipping_tracking_company_id_companies_id_fk": {"name": "shipping_tracking_company_id_companies_id_fk", "tableFrom": "shipping_tracking", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "shipping_tracking_shipment_id_shipments_id_fk": {"name": "shipping_tracking_shipment_id_shipments_id_fk", "tableFrom": "shipping_tracking", "tableTo": "shipments", "columnsFrom": ["shipment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stock_lots": {"name": "stock_lots", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "lot_number": {"name": "lot_number", "type": "text", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'available'"}, "quality_status": {"name": "quality_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "inspection_id": {"name": "inspection_id", "type": "text", "primaryKey": false, "notNull": false}, "quality_approved_date": {"name": "quality_approved_date", "type": "text", "primaryKey": false, "notNull": false}, "quality_approved_by": {"name": "quality_approved_by", "type": "text", "primaryKey": false, "notNull": false}, "quality_notes": {"name": "quality_notes", "type": "text", "primaryKey": false, "notNull": false}, "work_order_id": {"name": "work_order_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"stock_lots_company_id_idx": {"name": "stock_lots_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_lots_quality_status_idx": {"name": "stock_lots_quality_status_idx", "columns": [{"expression": "quality_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_lots_inspection_id_idx": {"name": "stock_lots_inspection_id_idx", "columns": [{"expression": "inspection_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_lots_work_order_id_idx": {"name": "stock_lots_work_order_id_idx", "columns": [{"expression": "work_order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"stock_lots_company_id_companies_id_fk": {"name": "stock_lots_company_id_companies_id_fk", "tableFrom": "stock_lots", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "stock_lots_product_id_products_id_fk": {"name": "stock_lots_product_id_products_id_fk", "tableFrom": "stock_lots", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "stock_lots_inspection_id_quality_inspections_id_fk": {"name": "stock_lots_inspection_id_quality_inspections_id_fk", "tableFrom": "stock_lots", "tableTo": "quality_inspections", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "stock_lots_work_order_id_work_orders_id_fk": {"name": "stock_lots_work_order_id_work_orders_id_fk", "tableFrom": "stock_lots", "tableTo": "work_orders", "columnsFrom": ["work_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stock_txns": {"name": "stock_txns", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "transaction_type": {"name": "transaction_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'manual'"}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "reference": {"name": "reference", "type": "text", "primaryKey": false, "notNull": false}, "from_location": {"name": "from_location", "type": "text", "primaryKey": false, "notNull": false}, "to_location": {"name": "to_location", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "reason_code": {"name": "reason_code", "type": "text", "primaryKey": false, "notNull": false}, "approval_status": {"name": "approval_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'approved'"}, "approved_by": {"name": "approved_by", "type": "text", "primaryKey": false, "notNull": false}, "approved_at": {"name": "approved_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "workflow_trigger": {"name": "workflow_trigger", "type": "text", "primaryKey": false, "notNull": false}, "reference_id": {"name": "reference_id", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "unit_cost": {"name": "unit_cost", "type": "text", "primaryKey": false, "notNull": false}, "total_value": {"name": "total_value", "type": "text", "primaryKey": false, "notNull": false}, "cost_method": {"name": "cost_method", "type": "text", "primaryKey": false, "notNull": false, "default": "'standard'"}, "cost_currency": {"name": "cost_currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}}, "indexes": {"stock_txns_company_id_idx": {"name": "stock_txns_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_txns_transaction_type_idx": {"name": "stock_txns_transaction_type_idx", "columns": [{"expression": "transaction_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_txns_approval_status_idx": {"name": "stock_txns_approval_status_idx", "columns": [{"expression": "approval_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_txns_workflow_trigger_idx": {"name": "stock_txns_workflow_trigger_idx", "columns": [{"expression": "workflow_trigger", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_txns_reference_id_idx": {"name": "stock_txns_reference_id_idx", "columns": [{"expression": "reference_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_txns_from_location_idx": {"name": "stock_txns_from_location_idx", "columns": [{"expression": "from_location", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_txns_to_location_idx": {"name": "stock_txns_to_location_idx", "columns": [{"expression": "to_location", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_txns_created_by_idx": {"name": "stock_txns_created_by_idx", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_txns_unit_cost_idx": {"name": "stock_txns_unit_cost_idx", "columns": [{"expression": "unit_cost", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "stock_txns_cost_method_idx": {"name": "stock_txns_cost_method_idx", "columns": [{"expression": "cost_method", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"stock_txns_company_id_companies_id_fk": {"name": "stock_txns_company_id_companies_id_fk", "tableFrom": "stock_txns", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "stock_txns_product_id_products_id_fk": {"name": "stock_txns_product_id_products_id_fk", "tableFrom": "stock_txns", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.supplier_lead_times": {"name": "supplier_lead_times", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "supplier_id": {"name": "supplier_id", "type": "text", "primaryKey": false, "notNull": true}, "raw_material_id": {"name": "raw_material_id", "type": "text", "primaryKey": false, "notNull": false}, "lead_time_days": {"name": "lead_time_days", "type": "text", "primaryKey": false, "notNull": true}, "minimum_order_qty": {"name": "minimum_order_qty", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "maximum_order_qty": {"name": "maximum_order_qty", "type": "text", "primaryKey": false, "notNull": false}, "unit_cost": {"name": "unit_cost", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "reliability": {"name": "reliability", "type": "text", "primaryKey": false, "notNull": true, "default": "'good'"}, "performance_metrics": {"name": "performance_metrics", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"supplier_lead_times_company_id_idx": {"name": "supplier_lead_times_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "supplier_lead_times_supplier_id_idx": {"name": "supplier_lead_times_supplier_id_idx", "columns": [{"expression": "supplier_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "supplier_lead_times_raw_material_id_idx": {"name": "supplier_lead_times_raw_material_id_idx", "columns": [{"expression": "raw_material_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "supplier_lead_times_reliability_idx": {"name": "supplier_lead_times_reliability_idx", "columns": [{"expression": "reliability", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "supplier_lead_times_lead_time_idx": {"name": "supplier_lead_times_lead_time_idx", "columns": [{"expression": "lead_time_days", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"supplier_lead_times_company_id_companies_id_fk": {"name": "supplier_lead_times_company_id_companies_id_fk", "tableFrom": "supplier_lead_times", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "supplier_lead_times_supplier_id_suppliers_id_fk": {"name": "supplier_lead_times_supplier_id_suppliers_id_fk", "tableFrom": "supplier_lead_times", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "supplier_lead_times_raw_material_id_raw_materials_id_fk": {"name": "supplier_lead_times_raw_material_id_raw_materials_id_fk", "tableFrom": "supplier_lead_times", "tableTo": "raw_materials", "columnsFrom": ["raw_material_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.suppliers": {"name": "suppliers", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "contact_name": {"name": "contact_name", "type": "text", "primaryKey": false, "notNull": false}, "contact_phone": {"name": "contact_phone", "type": "text", "primaryKey": false, "notNull": false}, "contact_email": {"name": "contact_email", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "tax_id": {"name": "tax_id", "type": "text", "primaryKey": false, "notNull": false}, "bank": {"name": "bank", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"suppliers_company_id_idx": {"name": "suppliers_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"suppliers_company_id_companies_id_fk": {"name": "suppliers_company_id_companies_id_fk", "tableFrom": "suppliers", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_operations": {"name": "work_operations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "work_order_id": {"name": "work_order_id", "type": "text", "primaryKey": false, "notNull": true}, "operation_name": {"name": "operation_name", "type": "text", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "start_time": {"name": "start_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"work_operations_company_id_idx": {"name": "work_operations_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "work_operations_work_order_id_idx": {"name": "work_operations_work_order_id_idx", "columns": [{"expression": "work_order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"work_operations_company_id_companies_id_fk": {"name": "work_operations_company_id_companies_id_fk", "tableFrom": "work_operations", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_operations_work_order_id_work_orders_id_fk": {"name": "work_operations_work_order_id_work_orders_id_fk", "tableFrom": "work_operations", "tableTo": "work_orders", "columnsFrom": ["work_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_orders": {"name": "work_orders", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "sales_contract_id": {"name": "sales_contract_id", "type": "text", "primaryKey": false, "notNull": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": false, "default": "'normal'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "material_cost": {"name": "material_cost", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "labor_cost": {"name": "labor_cost", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "overhead_cost": {"name": "overhead_cost", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_cost": {"name": "total_cost", "type": "text", "primaryKey": false, "notNull": false, "default": "'0'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"work_orders_company_id_idx": {"name": "work_orders_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"work_orders_company_id_companies_id_fk": {"name": "work_orders_company_id_companies_id_fk", "tableFrom": "work_orders", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_orders_sales_contract_id_sales_contracts_id_fk": {"name": "work_orders_sales_contract_id_sales_contracts_id_fk", "tableFrom": "work_orders", "tableTo": "sales_contracts", "columnsFrom": ["sales_contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_orders_product_id_products_id_fk": {"name": "work_orders_product_id_products_id_fk", "tableFrom": "work_orders", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}