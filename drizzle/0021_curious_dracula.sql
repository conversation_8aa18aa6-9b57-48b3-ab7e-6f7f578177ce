CREATE TABLE "product_pricing_history" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"product_id" text NOT NULL,
	"old_price" text,
	"new_price" text,
	"old_base_price" text,
	"new_base_price" text,
	"old_cost_price" text,
	"new_cost_price" text,
	"old_margin_percentage" text,
	"new_margin_percentage" text,
	"change_reason" text,
	"changed_by" text,
	"change_notes" text,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "declaration_items" ADD COLUMN "unit_value" text;--> statement-breakpoint
ALTER TABLE "declaration_items" ADD COLUMN "total_value" text;--> statement-breakpoint
ALTER TABLE "declaration_items" ADD COLUMN "value_currency" text DEFAULT 'USD';--> statement-breakpoint
ALTER TABLE "declaration_items" ADD COLUMN "value_method" text;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "base_price" text;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "cost_price" text;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "margin_percentage" text;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "currency" text DEFAULT 'USD';--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "price_updated_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "unit_cost" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "total_value" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "cost_method" text DEFAULT 'standard';--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "cost_currency" text DEFAULT 'USD';--> statement-breakpoint
ALTER TABLE "product_pricing_history" ADD CONSTRAINT "product_pricing_history_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "product_pricing_history" ADD CONSTRAINT "product_pricing_history_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "pricing_history_company_id_idx" ON "product_pricing_history" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "pricing_history_product_id_idx" ON "product_pricing_history" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "pricing_history_created_at_idx" ON "product_pricing_history" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "declaration_items_total_value_idx" ON "declaration_items" USING btree ("total_value");--> statement-breakpoint
CREATE INDEX "declaration_items_value_method_idx" ON "declaration_items" USING btree ("value_method");--> statement-breakpoint
CREATE INDEX "declaration_items_value_currency_idx" ON "declaration_items" USING btree ("value_currency");--> statement-breakpoint
CREATE INDEX "stock_txns_unit_cost_idx" ON "stock_txns" USING btree ("unit_cost");--> statement-breakpoint
CREATE INDEX "stock_txns_cost_method_idx" ON "stock_txns" USING btree ("cost_method");