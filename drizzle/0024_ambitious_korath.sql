CREATE TABLE "locations" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"name" text NOT NULL,
	"type" text NOT NULL,
	"description" text,
	"capacity" integer DEFAULT 0,
	"current_utilization" integer DEFAULT 0,
	"is_active" boolean DEFAULT true,
	"is_temperature_controlled" boolean DEFAULT false,
	"security_level" text DEFAULT 'medium',
	"parent_location_id" text,
	"location_code" text,
	"address" text,
	"floor_level" integer,
	"zone" text,
	"allows_mixed_products" boolean DEFAULT true,
	"requires_quality_check" boolean DEFAULT false,
	"automated_handling" boolean DEFAULT false,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "locations_company_code_unique" UNIQUE("company_id","location_code")
);
--> statement-breakpoint
ALTER TABLE "purchase_contract_items" DROP CONSTRAINT "purchase_contract_items_product_id_products_id_fk";
--> statement-breakpoint
ALTER TABLE "demand_forecasts" ADD COLUMN "supplier_preferences" text;--> statement-breakpoint
ALTER TABLE "procurement_plans" ADD COLUMN "approved_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "locations" ADD CONSTRAINT "locations_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "locations" ADD CONSTRAINT "locations_parent_location_id_locations_id_fk" FOREIGN KEY ("parent_location_id") REFERENCES "public"."locations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "locations_company_id_idx" ON "locations" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "locations_type_idx" ON "locations" USING btree ("type");--> statement-breakpoint
CREATE INDEX "locations_active_idx" ON "locations" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "locations_parent_idx" ON "locations" USING btree ("parent_location_id");--> statement-breakpoint
CREATE INDEX "locations_code_idx" ON "locations" USING btree ("company_id","location_code");