CREATE TABLE "cash_flow_forecasts" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"forecast_date" text NOT NULL,
	"forecast_type" text NOT NULL,
	"forecast_horizon_days" text NOT NULL,
	"expected_ar_collections" text DEFAULT '0',
	"expected_contract_payments" text DEFAULT '0',
	"expected_export_receipts" text DEFAULT '0',
	"other_inflows" text DEFAULT '0',
	"total_inflows" text DEFAULT '0',
	"expected_ap_payments" text DEFAULT '0',
	"expected_payroll" text DEFAULT '0',
	"expected_operating_expenses" text DEFAULT '0',
	"expected_shipping_costs" text DEFAULT '0',
	"expected_raw_material_costs" text DEFAULT '0',
	"other_outflows" text DEFAULT '0',
	"total_outflows" text DEFAULT '0',
	"net_cash_flow" text DEFAULT '0',
	"cumulative_cash_flow" text DEFAULT '0',
	"currency_risk_adjustment" text DEFAULT '0',
	"exchange_rate_sensitivity" text,
	"confidence_level" text DEFAULT 'medium',
	"risk_factors" text,
	"best_case_scenario" text DEFAULT '0',
	"worst_case_scenario" text DEFAULT '0',
	"most_likely_scenario" text DEFAULT '0',
	"created_by" text NOT NULL,
	"forecast_method" text DEFAULT 'historical',
	"last_updated_at" timestamp with time zone DEFAULT now(),
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "container_cost_allocation" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"container_id" text NOT NULL,
	"container_type" text NOT NULL,
	"container_number" text,
	"shipment_id" text,
	"export_declaration_id" text,
	"sales_contract_id" text,
	"base_shipping_cost" text DEFAULT '0' NOT NULL,
	"fuel_surcharge" text DEFAULT '0',
	"port_charges" text DEFAULT '0',
	"customs_fees" text DEFAULT '0',
	"insurance_cost" text DEFAULT '0',
	"handling_fees" text DEFAULT '0',
	"documentation_fees" text DEFAULT '0',
	"other_charges" text DEFAULT '0',
	"cost_currency" text DEFAULT 'USD' NOT NULL,
	"base_cost_amount" text,
	"base_currency_code" text,
	"exchange_rate" text DEFAULT '1.0',
	"total_container_weight" text DEFAULT '0',
	"total_container_volume" text DEFAULT '0',
	"utilization_percentage" text DEFAULT '0',
	"allocation_method" text DEFAULT 'weight',
	"allocated_products" text,
	"shipping_date" text NOT NULL,
	"arrival_date" text,
	"cost_date" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "currencies" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"code" text NOT NULL,
	"name" text NOT NULL,
	"symbol" text NOT NULL,
	"decimal_places" text DEFAULT '2' NOT NULL,
	"is_base_currency" text DEFAULT 'false' NOT NULL,
	"is_active" text DEFAULT 'true' NOT NULL,
	"exchange_rate" text DEFAULT '1.0',
	"last_rate_update" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "currencies_company_id_code_unique" UNIQUE("company_id","code")
);
--> statement-breakpoint
CREATE TABLE "currency_conversion_cache" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"from_currency_code" text NOT NULL,
	"to_currency_code" text NOT NULL,
	"exchange_rate" text NOT NULL,
	"amount_from" text NOT NULL,
	"amount_to" text NOT NULL,
	"conversion_date" text NOT NULL,
	"cache_expires_at" timestamp with time zone NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "currency_conversion_cache_company_id_from_currency_code_to_currency_code_amount_from_conversion_date_unique" UNIQUE("company_id","from_currency_code","to_currency_code","amount_from","conversion_date")
);
--> statement-breakpoint
CREATE TABLE "currency_exposure" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"currency_code" text NOT NULL,
	"exposure_type" text NOT NULL,
	"total_amount" text DEFAULT '0' NOT NULL,
	"base_amount" text DEFAULT '0' NOT NULL,
	"unrealized_gain_loss" text DEFAULT '0',
	"risk_level" text DEFAULT 'medium',
	"hedge_ratio" text DEFAULT '0',
	"calculation_date" text NOT NULL,
	"next_revaluation_date" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "currency_exposure_company_id_currency_code_exposure_type_calculation_date_unique" UNIQUE("company_id","currency_code","exposure_type","calculation_date")
);
--> statement-breakpoint
CREATE TABLE "currency_risk_assessments" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"assessment_date" text NOT NULL,
	"assessment_type" text NOT NULL,
	"base_currency_code" text NOT NULL,
	"currency_exposures" text NOT NULL,
	"total_exposure_base_currency" text DEFAULT '0',
	"value_at_risk_1day" text DEFAULT '0',
	"value_at_risk_1week" text DEFAULT '0',
	"value_at_risk_1month" text DEFAULT '0',
	"overall_risk_level" text DEFAULT 'medium',
	"highest_risk_currency" text,
	"risk_concentration_ratio" text DEFAULT '0',
	"hedged_exposure_percentage" text DEFAULT '0',
	"unhedged_exposure" text DEFAULT '0',
	"hedging_effectiveness" text DEFAULT '0',
	"risk_mitigation_recommendations" text,
	"hedging_recommendations" text,
	"created_by" text NOT NULL,
	"assessment_method" text DEFAULT 'historical',
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "demand_forecasts" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"product_id" text NOT NULL,
	"forecast_period" text NOT NULL,
	"forecasted_demand" text NOT NULL,
	"confidence_level" text DEFAULT 'medium',
	"forecast_method" text DEFAULT 'pipeline',
	"base_data_source" text,
	"seasonality_applied" text DEFAULT 'false',
	"trend_factor_applied" text DEFAULT '1.0',
	"created_by" text NOT NULL,
	"approved_by" text,
	"approval_status" text DEFAULT 'draft',
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "exchange_rate_history" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"from_currency_code" text NOT NULL,
	"to_currency_code" text NOT NULL,
	"exchange_rate" text NOT NULL,
	"effective_date" text NOT NULL,
	"rate_source" text DEFAULT 'manual',
	"rate_type" text DEFAULT 'spot',
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "export_revenue_analytics" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"period_type" text NOT NULL,
	"period_start" text NOT NULL,
	"period_end" text NOT NULL,
	"total_revenue_usd" text DEFAULT '0' NOT NULL,
	"total_revenue_local" text DEFAULT '0' NOT NULL,
	"local_currency_code" text DEFAULT 'USD' NOT NULL,
	"export_volume_containers" text DEFAULT '0',
	"export_volume_weight" text DEFAULT '0',
	"export_destinations_count" text DEFAULT '0',
	"top_export_destination" text,
	"currency_breakdown" text,
	"exchange_rate_impact" text DEFAULT '0',
	"average_order_value" text DEFAULT '0',
	"revenue_per_container" text DEFAULT '0',
	"profit_margin_percentage" text DEFAULT '0',
	"currency_exposure_risk" text DEFAULT 'medium',
	"concentration_risk" text DEFAULT 'medium',
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "financial_transactions" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"transaction_type" text NOT NULL,
	"reference_id" text,
	"reference_type" text,
	"account_type" text NOT NULL,
	"amount" text NOT NULL,
	"currency_code" text DEFAULT 'USD' NOT NULL,
	"base_amount" text,
	"base_currency_code" text,
	"exchange_rate" text DEFAULT '1.0',
	"transaction_date" text NOT NULL,
	"description" text,
	"notes" text,
	"status" text DEFAULT 'pending' NOT NULL,
	"created_by" text NOT NULL,
	"approved_by" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "forecast_parameters" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"product_id" text NOT NULL,
	"seasonality_factor" text DEFAULT '1.0',
	"trend_factor" text DEFAULT '1.0',
	"lead_time_buffer_days" text DEFAULT '14',
	"safety_stock_percentage" text DEFAULT '0.15',
	"container_optimization_enabled" text DEFAULT 'true',
	"preferred_container_size" text DEFAULT '40ft',
	"minimum_order_efficiency" text DEFAULT '0.8',
	"historical_periods_to_analyze" text DEFAULT '12',
	"outlier_detection_enabled" text DEFAULT 'true',
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "procurement_plans" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"raw_material_id" text NOT NULL,
	"demand_forecast_id" text,
	"planned_qty" text NOT NULL,
	"target_date" text NOT NULL,
	"supplier_id" text,
	"estimated_cost" text DEFAULT '0',
	"estimated_lead_time" text DEFAULT '30',
	"container_optimization" text,
	"priority" text DEFAULT 'normal' NOT NULL,
	"status" text DEFAULT 'draft' NOT NULL,
	"notes" text,
	"created_by" text NOT NULL,
	"approved_by" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "supplier_lead_times" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"supplier_id" text NOT NULL,
	"raw_material_id" text,
	"lead_time_days" text NOT NULL,
	"minimum_order_qty" text DEFAULT '0',
	"maximum_order_qty" text,
	"unit_cost" text,
	"currency" text DEFAULT 'USD',
	"reliability" text DEFAULT 'good' NOT NULL,
	"performance_metrics" text,
	"notes" text,
	"created_by" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "cash_flow_forecasts" ADD CONSTRAINT "cash_flow_forecasts_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "container_cost_allocation" ADD CONSTRAINT "container_cost_allocation_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "container_cost_allocation" ADD CONSTRAINT "container_cost_allocation_sales_contract_id_sales_contracts_id_fk" FOREIGN KEY ("sales_contract_id") REFERENCES "public"."sales_contracts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "currencies" ADD CONSTRAINT "currencies_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "currency_conversion_cache" ADD CONSTRAINT "currency_conversion_cache_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "currency_exposure" ADD CONSTRAINT "currency_exposure_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "currency_risk_assessments" ADD CONSTRAINT "currency_risk_assessments_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "demand_forecasts" ADD CONSTRAINT "demand_forecasts_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "demand_forecasts" ADD CONSTRAINT "demand_forecasts_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "exchange_rate_history" ADD CONSTRAINT "exchange_rate_history_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "export_revenue_analytics" ADD CONSTRAINT "export_revenue_analytics_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "financial_transactions" ADD CONSTRAINT "financial_transactions_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "forecast_parameters" ADD CONSTRAINT "forecast_parameters_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "forecast_parameters" ADD CONSTRAINT "forecast_parameters_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "procurement_plans" ADD CONSTRAINT "procurement_plans_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "procurement_plans" ADD CONSTRAINT "procurement_plans_raw_material_id_raw_materials_id_fk" FOREIGN KEY ("raw_material_id") REFERENCES "public"."raw_materials"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "procurement_plans" ADD CONSTRAINT "procurement_plans_demand_forecast_id_demand_forecasts_id_fk" FOREIGN KEY ("demand_forecast_id") REFERENCES "public"."demand_forecasts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "procurement_plans" ADD CONSTRAINT "procurement_plans_supplier_id_suppliers_id_fk" FOREIGN KEY ("supplier_id") REFERENCES "public"."suppliers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "supplier_lead_times" ADD CONSTRAINT "supplier_lead_times_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "supplier_lead_times" ADD CONSTRAINT "supplier_lead_times_supplier_id_suppliers_id_fk" FOREIGN KEY ("supplier_id") REFERENCES "public"."suppliers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "supplier_lead_times" ADD CONSTRAINT "supplier_lead_times_raw_material_id_raw_materials_id_fk" FOREIGN KEY ("raw_material_id") REFERENCES "public"."raw_materials"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "cash_flow_forecasts_company_id_idx" ON "cash_flow_forecasts" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "cash_flow_forecasts_date_idx" ON "cash_flow_forecasts" USING btree ("forecast_date");--> statement-breakpoint
CREATE INDEX "cash_flow_forecasts_type_idx" ON "cash_flow_forecasts" USING btree ("forecast_type");--> statement-breakpoint
CREATE INDEX "cash_flow_forecasts_horizon_idx" ON "cash_flow_forecasts" USING btree ("forecast_horizon_days");--> statement-breakpoint
CREATE INDEX "container_cost_allocation_company_id_idx" ON "container_cost_allocation" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "container_cost_allocation_container_idx" ON "container_cost_allocation" USING btree ("container_id");--> statement-breakpoint
CREATE INDEX "container_cost_allocation_shipment_idx" ON "container_cost_allocation" USING btree ("shipment_id");--> statement-breakpoint
CREATE INDEX "container_cost_allocation_contract_idx" ON "container_cost_allocation" USING btree ("sales_contract_id");--> statement-breakpoint
CREATE INDEX "container_cost_allocation_date_idx" ON "container_cost_allocation" USING btree ("shipping_date");--> statement-breakpoint
CREATE INDEX "currencies_company_id_idx" ON "currencies" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "currencies_code_idx" ON "currencies" USING btree ("code");--> statement-breakpoint
CREATE INDEX "currencies_base_idx" ON "currencies" USING btree ("company_id","is_base_currency");--> statement-breakpoint
CREATE INDEX "currencies_active_idx" ON "currencies" USING btree ("company_id","is_active");--> statement-breakpoint
CREATE INDEX "currency_conversion_cache_company_id_idx" ON "currency_conversion_cache" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "currency_conversion_cache_currencies_idx" ON "currency_conversion_cache" USING btree ("from_currency_code","to_currency_code");--> statement-breakpoint
CREATE INDEX "currency_conversion_cache_expires_idx" ON "currency_conversion_cache" USING btree ("cache_expires_at");--> statement-breakpoint
CREATE INDEX "currency_exposure_company_id_idx" ON "currency_exposure" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "currency_exposure_currency_idx" ON "currency_exposure" USING btree ("currency_code");--> statement-breakpoint
CREATE INDEX "currency_exposure_type_idx" ON "currency_exposure" USING btree ("exposure_type");--> statement-breakpoint
CREATE INDEX "currency_exposure_risk_idx" ON "currency_exposure" USING btree ("risk_level");--> statement-breakpoint
CREATE INDEX "currency_exposure_date_idx" ON "currency_exposure" USING btree ("calculation_date");--> statement-breakpoint
CREATE INDEX "currency_risk_assessments_company_id_idx" ON "currency_risk_assessments" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "currency_risk_assessments_date_idx" ON "currency_risk_assessments" USING btree ("assessment_date");--> statement-breakpoint
CREATE INDEX "currency_risk_assessments_risk_idx" ON "currency_risk_assessments" USING btree ("overall_risk_level");--> statement-breakpoint
CREATE INDEX "currency_risk_assessments_currency_idx" ON "currency_risk_assessments" USING btree ("base_currency_code");--> statement-breakpoint
CREATE INDEX "demand_forecasts_company_id_idx" ON "demand_forecasts" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "demand_forecasts_product_id_idx" ON "demand_forecasts" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "demand_forecasts_period_idx" ON "demand_forecasts" USING btree ("forecast_period");--> statement-breakpoint
CREATE INDEX "demand_forecasts_approval_status_idx" ON "demand_forecasts" USING btree ("approval_status");--> statement-breakpoint
CREATE INDEX "exchange_rate_history_company_id_idx" ON "exchange_rate_history" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "exchange_rate_history_currencies_idx" ON "exchange_rate_history" USING btree ("from_currency_code","to_currency_code");--> statement-breakpoint
CREATE INDEX "exchange_rate_history_date_idx" ON "exchange_rate_history" USING btree ("effective_date");--> statement-breakpoint
CREATE INDEX "exchange_rate_history_source_idx" ON "exchange_rate_history" USING btree ("rate_source");--> statement-breakpoint
CREATE INDEX "export_revenue_analytics_company_id_idx" ON "export_revenue_analytics" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "export_revenue_analytics_period_idx" ON "export_revenue_analytics" USING btree ("period_type","period_start","period_end");--> statement-breakpoint
CREATE INDEX "export_revenue_analytics_currency_idx" ON "export_revenue_analytics" USING btree ("local_currency_code");--> statement-breakpoint
CREATE INDEX "financial_transactions_company_id_idx" ON "financial_transactions" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "financial_transactions_type_idx" ON "financial_transactions" USING btree ("transaction_type");--> statement-breakpoint
CREATE INDEX "financial_transactions_reference_idx" ON "financial_transactions" USING btree ("reference_id","reference_type");--> statement-breakpoint
CREATE INDEX "financial_transactions_account_idx" ON "financial_transactions" USING btree ("account_type");--> statement-breakpoint
CREATE INDEX "financial_transactions_currency_idx" ON "financial_transactions" USING btree ("currency_code");--> statement-breakpoint
CREATE INDEX "financial_transactions_date_idx" ON "financial_transactions" USING btree ("transaction_date");--> statement-breakpoint
CREATE INDEX "financial_transactions_status_idx" ON "financial_transactions" USING btree ("status");--> statement-breakpoint
CREATE INDEX "forecast_parameters_company_id_idx" ON "forecast_parameters" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "forecast_parameters_product_id_idx" ON "forecast_parameters" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "forecast_parameters_unique_product" ON "forecast_parameters" USING btree ("company_id","product_id");--> statement-breakpoint
CREATE INDEX "procurement_plans_company_id_idx" ON "procurement_plans" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "procurement_plans_raw_material_id_idx" ON "procurement_plans" USING btree ("raw_material_id");--> statement-breakpoint
CREATE INDEX "procurement_plans_status_idx" ON "procurement_plans" USING btree ("status");--> statement-breakpoint
CREATE INDEX "procurement_plans_target_date_idx" ON "procurement_plans" USING btree ("target_date");--> statement-breakpoint
CREATE INDEX "procurement_plans_priority_idx" ON "procurement_plans" USING btree ("priority");--> statement-breakpoint
CREATE INDEX "supplier_lead_times_company_id_idx" ON "supplier_lead_times" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "supplier_lead_times_supplier_id_idx" ON "supplier_lead_times" USING btree ("supplier_id");--> statement-breakpoint
CREATE INDEX "supplier_lead_times_raw_material_id_idx" ON "supplier_lead_times" USING btree ("raw_material_id");--> statement-breakpoint
CREATE INDEX "supplier_lead_times_reliability_idx" ON "supplier_lead_times" USING btree ("reliability");--> statement-breakpoint
CREATE INDEX "supplier_lead_times_lead_time_idx" ON "supplier_lead_times" USING btree ("lead_time_days");