ALTER TABLE "purchase_contract_items" ADD COLUMN "pricing_method" text;--> statement-breakpoint
ALTER TABLE "purchase_contract_items" ADD COLUMN "cost_basis" text;--> statement-breakpoint
ALTER TABLE "purchase_contract_items" ADD COLUMN "price_currency" text DEFAULT 'USD';--> statement-breakpoint
ALTER TABLE "sales_contract_items" ADD COLUMN "pricing_method" text;--> statement-breakpoint
ALTER TABLE "sales_contract_items" ADD COLUMN "margin_percentage" text;--> statement-breakpoint
ALTER TABLE "sales_contract_items" ADD COLUMN "cost_basis" text;--> statement-breakpoint
ALTER TABLE "sales_contract_items" ADD COLUMN "price_currency" text DEFAULT 'USD';--> statement-breakpoint
CREATE INDEX "purchase_contract_items_pricing_method_idx" ON "purchase_contract_items" USING btree ("pricing_method");--> statement-breakpoint
CREATE INDEX "sales_contract_items_pricing_method_idx" ON "sales_contract_items" USING btree ("pricing_method");--> statement-breakpoint
CREATE INDEX "sales_contract_items_margin_idx" ON "sales_contract_items" USING btree ("margin_percentage");