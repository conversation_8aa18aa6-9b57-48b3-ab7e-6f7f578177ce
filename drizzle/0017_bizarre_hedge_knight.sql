ALTER TABLE "declarations" ADD COLUMN "sales_contract_id" text;--> statement-breakpoint
ALTER TABLE "declarations" ADD COLUMN "contract_reference" text;--> statement-breakpoint
ALTER TABLE "declarations" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now();--> statement-breakpoint
ALTER TABLE "declarations" ADD CONSTRAINT "declarations_sales_contract_id_sales_contracts_id_fk" FOREIGN KEY ("sales_contract_id") REFERENCES "public"."sales_contracts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "declarations_sales_contract_id_idx" ON "declarations" USING btree ("sales_contract_id");