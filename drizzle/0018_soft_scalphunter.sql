CREATE TABLE "bill_of_materials" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"product_id" text NOT NULL,
	"raw_material_id" text NOT NULL,
	"qty_required" text NOT NULL,
	"unit" text NOT NULL,
	"waste_factor" text DEFAULT '0.05',
	"status" text DEFAULT 'active',
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "material_consumption" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"work_order_id" text NOT NULL,
	"raw_material_lot_id" text NOT NULL,
	"qty_consumed" text NOT NULL,
	"unit_cost" text NOT NULL,
	"total_cost" text NOT NULL,
	"consumed_date" text NOT NULL,
	"consumed_by" text,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "raw_material_lots" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"raw_material_id" text NOT NULL,
	"lot_number" text,
	"supplier_id" text NOT NULL,
	"purchase_contract_id" text,
	"qty" text NOT NULL,
	"location" text NOT NULL,
	"unit_cost" text NOT NULL,
	"total_cost" text NOT NULL,
	"currency" text DEFAULT 'USD',
	"received_date" text,
	"expiry_date" text,
	"quality_status" text DEFAULT 'pending',
	"inspection_id" text,
	"quality_approved_date" text,
	"quality_approved_by" text,
	"quality_notes" text,
	"status" text DEFAULT 'available',
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "raw_materials" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"sku" text NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"unit" text NOT NULL,
	"primary_supplier_id" text,
	"composition" text,
	"quality_grade" text,
	"specifications" text,
	"standard_cost" text,
	"currency" text DEFAULT 'USD',
	"reorder_point" text DEFAULT '0',
	"max_stock_level" text DEFAULT '0',
	"lead_time_days" text DEFAULT '7',
	"inspection_required" text DEFAULT 'false',
	"quality_tolerance" text,
	"quality_notes" text,
	"status" text DEFAULT 'active',
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "ap_invoices" ADD COLUMN "purchase_contract_id" text;--> statement-breakpoint
ALTER TABLE "ap_invoices" ADD COLUMN "contract_reference" text;--> statement-breakpoint
ALTER TABLE "ap_invoices" ADD COLUMN "due_date" text;--> statement-breakpoint
ALTER TABLE "ap_invoices" ADD COLUMN "paid" text DEFAULT '0';--> statement-breakpoint
ALTER TABLE "ap_invoices" ADD COLUMN "currency" text DEFAULT 'USD';--> statement-breakpoint
ALTER TABLE "ap_invoices" ADD COLUMN "payment_terms" text;--> statement-breakpoint
ALTER TABLE "ap_invoices" ADD COLUMN "notes" text;--> statement-breakpoint
ALTER TABLE "ap_invoices" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now();--> statement-breakpoint
ALTER TABLE "ar_invoices" ADD COLUMN "sales_contract_id" text;--> statement-breakpoint
ALTER TABLE "ar_invoices" ADD COLUMN "contract_reference" text;--> statement-breakpoint
ALTER TABLE "ar_invoices" ADD COLUMN "due_date" text;--> statement-breakpoint
ALTER TABLE "ar_invoices" ADD COLUMN "received" text DEFAULT '0';--> statement-breakpoint
ALTER TABLE "ar_invoices" ADD COLUMN "currency" text DEFAULT 'USD';--> statement-breakpoint
ALTER TABLE "ar_invoices" ADD COLUMN "payment_terms" text;--> statement-breakpoint
ALTER TABLE "ar_invoices" ADD COLUMN "notes" text;--> statement-breakpoint
ALTER TABLE "ar_invoices" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now();--> statement-breakpoint
ALTER TABLE "bill_of_materials" ADD CONSTRAINT "bill_of_materials_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bill_of_materials" ADD CONSTRAINT "bill_of_materials_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bill_of_materials" ADD CONSTRAINT "bill_of_materials_raw_material_id_raw_materials_id_fk" FOREIGN KEY ("raw_material_id") REFERENCES "public"."raw_materials"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "material_consumption" ADD CONSTRAINT "material_consumption_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "material_consumption" ADD CONSTRAINT "material_consumption_work_order_id_work_orders_id_fk" FOREIGN KEY ("work_order_id") REFERENCES "public"."work_orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "material_consumption" ADD CONSTRAINT "material_consumption_raw_material_lot_id_raw_material_lots_id_fk" FOREIGN KEY ("raw_material_lot_id") REFERENCES "public"."raw_material_lots"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raw_material_lots" ADD CONSTRAINT "raw_material_lots_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raw_material_lots" ADD CONSTRAINT "raw_material_lots_raw_material_id_raw_materials_id_fk" FOREIGN KEY ("raw_material_id") REFERENCES "public"."raw_materials"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raw_material_lots" ADD CONSTRAINT "raw_material_lots_supplier_id_suppliers_id_fk" FOREIGN KEY ("supplier_id") REFERENCES "public"."suppliers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raw_material_lots" ADD CONSTRAINT "raw_material_lots_purchase_contract_id_purchase_contracts_id_fk" FOREIGN KEY ("purchase_contract_id") REFERENCES "public"."purchase_contracts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raw_material_lots" ADD CONSTRAINT "raw_material_lots_inspection_id_quality_inspections_id_fk" FOREIGN KEY ("inspection_id") REFERENCES "public"."quality_inspections"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raw_materials" ADD CONSTRAINT "raw_materials_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raw_materials" ADD CONSTRAINT "raw_materials_primary_supplier_id_suppliers_id_fk" FOREIGN KEY ("primary_supplier_id") REFERENCES "public"."suppliers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "bill_of_materials_company_id_idx" ON "bill_of_materials" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "bill_of_materials_product_idx" ON "bill_of_materials" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "material_consumption_company_id_idx" ON "material_consumption" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "material_consumption_work_order_idx" ON "material_consumption" USING btree ("work_order_id");--> statement-breakpoint
CREATE INDEX "raw_material_lots_company_id_idx" ON "raw_material_lots" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "raw_material_lots_material_idx" ON "raw_material_lots" USING btree ("raw_material_id");--> statement-breakpoint
CREATE INDEX "raw_material_lots_status_idx" ON "raw_material_lots" USING btree ("status");--> statement-breakpoint
CREATE INDEX "raw_materials_company_id_idx" ON "raw_materials" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "raw_materials_category_idx" ON "raw_materials" USING btree ("category");--> statement-breakpoint
CREATE INDEX "raw_materials_supplier_idx" ON "raw_materials" USING btree ("primary_supplier_id");--> statement-breakpoint
ALTER TABLE "ap_invoices" ADD CONSTRAINT "ap_invoices_purchase_contract_id_purchase_contracts_id_fk" FOREIGN KEY ("purchase_contract_id") REFERENCES "public"."purchase_contracts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ar_invoices" ADD CONSTRAINT "ar_invoices_sales_contract_id_sales_contracts_id_fk" FOREIGN KEY ("sales_contract_id") REFERENCES "public"."sales_contracts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "ap_invoices_contract_id_idx" ON "ap_invoices" USING btree ("purchase_contract_id");--> statement-breakpoint
CREATE INDEX "ar_invoices_contract_id_idx" ON "ar_invoices" USING btree ("sales_contract_id");