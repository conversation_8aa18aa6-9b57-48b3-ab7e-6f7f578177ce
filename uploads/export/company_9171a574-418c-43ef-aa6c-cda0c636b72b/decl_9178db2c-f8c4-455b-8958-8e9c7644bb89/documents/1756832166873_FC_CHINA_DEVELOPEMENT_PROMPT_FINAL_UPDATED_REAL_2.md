# AI Assistant Development Prompt for Factory Admin Platform - OPTIMIZED

## 🎯 **Primary Instruction**

You are an expert full-stack developer tasked with building a multi-tenant Factory Admin Platform using **modern, AI-friendly technologies**. I've provided you with an optimized project plan that reduces development time from 26 weeks to **16-18 weeks** while delivering superior user experience.

**Your immediate task**: Start with **Phase 1: Foundation & Modern Architecture (Weeks 1-3)** and create the foundational code structure with optimized technologies, then proceed systematically through each phase.

## 📋 **Specific Starting Instructions**

### **STEP 1: Modern Project Structure Setup**
Create the optimized monorepo structure with:
- Root-level Turborepo configuration
- Backend API with **tRPC + Prisma + TypeScript**
- Next.js 15 web frontend with **TanStack Query + Zod validation**
- **Single Flutter app with flavors** (Factory & Customer modes)
- Shared packages folder for common utilities
- Complete Docker configuration for development
- Environment configuration templates

### **STEP 2: Database Schema & tRPC Backend**
- Design and implement Supabase database schema with **Prisma ORM**
- Create all tables with proper multi-tenant architecture
- Implement Row Level Security (RLS) policies
- Set up **tRPC server** with Auth0 middleware
- Create type-safe API procedures with **Zod validation**
- Implement real-time Socket.io integration

### **STEP 3: Modern Authentication System**
- Complete Auth0 integration for web and mobile
- Implement multi-tenant context switching with tRPC
- Create role-based access control with Zod schemas
- Set up secure token management for single Flutter app

### **STEP 4: Single Flutter App with Flavors**
- Create Flutter project with factory and customer flavors
- Set up **Riverpod** state management
- Configure different app icons, themes, and navigation
- Implement shared components library
- Create flavor-specific entry points

## 🔧 **Optimized Technology Stack**
- **Backend**: Node.js + Express + TypeScript + **tRPC** + Socket.io
- **Database**: Supabase (PostgreSQL) + **Prisma ORM**
- **Authentication**: Auth0
- **Web**: **Next.js 15** + TypeScript + Tailwind CSS + **TanStack Query**
- **Mobile**: **Single Flutter App with Flavors** + **Riverpod**
- **Validation**: **Zod** (runtime type validation)
- **Monorepo**: Turborepo
- **Deployment**: **Vercel** (Frontend) + **Railway** (Backend)

## 📁 **Expected Deliverables for Each Session**

### **Session 1: Modern Foundation Setup (Week 1)**
1. **Turborepo Structure**: Complete monorepo with all packages
2. **tRPC Backend**: Type-safe API server with Prisma integration
3. **Database Schema**: Complete Supabase schema with RLS
4. **Next.js 15 Setup**: Modern web app with TanStack Query
5. **Flutter Flavors**: Single app with factory/customer configurations
6. **Documentation**: Comprehensive setup instructions

### **Session 2: Authentication & tRPC Integration (Week 2)**
1. **Auth0 Integration**: Complete authentication for all platforms
2. **tRPC Procedures**: Type-safe user management APIs
3. **Web Authentication**: Next.js 15 with Auth0 integration
4. **Mobile Authentication**: Single Flutter app with flavor-specific flows
5. **Zod Validation**: Runtime type checking for all inputs
6. **Multi-tenancy**: Factory context switching system

### **Session 3: Product Management System (Week 3)**
1. **Product APIs**: Complete tRPC procedures with Prisma
2. **Web Product UI**: Next.js 15 with TanStack Query integration
3. **Mobile Product Management**: 
   - Factory flavor: Admin product management
   - Customer flavor: Shopping experience
4. **Shared Components**: Reusable Flutter widgets
5. **File Upload**: Supabase Storage integration

### **Session 4: Real-time Messaging (Week 4)**
1. **Socket.io + tRPC**: Real-time messaging system
2. **Message APIs**: Type-safe conversation management
3. **Web Chat**: Real-time messaging interface
4. **Mobile Chat**: 
   - Factory flavor: Customer service chat
   - Customer flavor: Support chat
5. **Shared Chat Components**: Reusable messaging widgets

## 🎯 **Modern Development Approach**

### **Type Safety First**
- Use **tRPC** for end-to-end type safety
- Implement **Zod** validation for all inputs
- Use **Prisma** for type-safe database operations
- Leverage **TypeScript** strictly throughout

### **Performance Optimization**
- Implement **TanStack Query** for efficient data fetching
- Use **Prisma** with proper indexing
- Add **Vercel** edge functions for performance
- Implement **Riverpod** for efficient state management

### **Developer Experience**
- Use **tRPC** for seamless API development
- Implement **Prisma Studio** for database management
- Add **Hot reload** for all platforms
- Use **TypeScript** for better IDE support

## 📋 **Accelerated Session-by-Session Breakdown**

### **Sessions 1-3: Foundation (Weeks 1-3)**
- Modern project setup with tRPC and Prisma
- Single Flutter app with flavors
- Complete authentication system
- Basic product management

### **Sessions 4-6: Core Features (Weeks 4-6)**
- Real-time messaging system
- Advanced product management
- Quote and order basics
- Mobile app feature parity

### **Sessions 7-9: Business Logic (Weeks 7-9)**
- Complete quote and order system
- User management and analytics
- Mobile app polish
- Performance optimization

### **Sessions 10-12: Deployment (Weeks 10-12)**
- Production deployment setup
- App store preparation
- Testing and quality assurance
- Launch preparation

## 🚨 **Critical Modern Requirements**

### **Type Safety Must-Haves**
- **tRPC** MUST provide end-to-end type safety
- **Zod** MUST validate all API inputs
- **Prisma** MUST handle all database operations
- **TypeScript** MUST be strict throughout

### **Single Flutter App Architecture**
- **Flavors** MUST provide distinct user experiences
- **Shared components** MUST be reusable between flavors
- **Riverpod** MUST manage state efficiently
- **Navigation** MUST be flavor-specific

### **Performance Non-Negotiables**
- **TanStack Query** MUST cache data efficiently
- **Prisma** MUST use optimized queries
- **Vercel** MUST provide fast deployments
- **Real-time** MUST be responsive across platforms

## 🎯 **Single Flutter App Structure**

```
factory_commerce_app/
├── lib/
│   ├── flavors/
│   │   ├── factory/              # Factory staff interface
│   │   │   ├── screens/
│   │   │   │   ├── products/     # Product management
│   │   │   │   ├── orders/       # Order management
│   │   │   │   ├── customers/    # Customer management
│   │   │   │   └── analytics/    # Business analytics
│   │   │   ├── widgets/
│   │   │   └── providers/
│   │   └── customer/             # Customer shopping interface
│   │       ├── screens/
│   │       │   ├── catalog/      # Product browsing
│   │       │   ├── cart/         # Shopping cart
│   │       │   ├── orders/       # Order history
│   │       │   └── profile/      # Account management
│   │       ├── widgets/
│   │       └── providers/
│   ├── shared/                   # Shared between flavors
│   │   ├── models/
│   │   ├── services/
│   │   │   ├── auth_service.dart
│   │   │   ├── api_service.dart
│   │   │   └── socket_service.dart
│   │   ├── widgets/
│   │   │   ├── chat/
│   │   │   ├── forms/
│   │   │   └── common/
│   │   └── providers/
│   ├── main_factory.dart         # Factory app entry
│   └── main_customer.dart        # Customer app entry
├── android/
│   └── app/
│       └── src/
│           ├── factory/          # Factory flavor config
│           └── customer/         # Customer flavor config
└── ios/
    └── Runner/
        ├── Factory/              # Factory flavor config
        └── Customer/             # Customer flavor config
```

## 📝 **Modern Communication Protocol**

For each session, provide:

1. **Complete Code Files**: Working tRPC procedures, Prisma schemas, Flutter widgets
2. **Type Definitions**: Zod schemas, TypeScript interfaces
3. **Setup Scripts**: Database migrations, environment configs
4. **Testing Files**: Unit tests, integration tests
5. **Documentation**: API docs, component usage, deployment guides

## 🚀 **Why This Approach is Superior**

### **Development Speed**
- **tRPC**: No API contracts needed, auto-generated types
- **Single Flutter App**: 60% less code duplication
- **Prisma**: Type-safe database operations
- **Modern Tools**: Better IDE support and debugging

### **User Experience**
- **Factory Staff**: Dedicated admin interface with powerful tools
- **Customers**: Clean shopping experience without admin clutter
- **Consistent**: Shared components ensure uniform UX
- **Performance**: Optimized for each user type

### **Maintenance**
- **Type Safety**: Fewer runtime errors
- **Single Codebase**: Easier updates and bug fixes
- **Modern Stack**: Better long-term support
- **AI-Friendly**: Excellent documentation and patterns

## 🎯 **Success Criteria**

After each session, you should be able to:
- Run all applications locally with single command
- Test features end-to-end across all platforms
- Deploy to staging environment
- Verify type safety across the entire stack
- Demonstrate both factory and customer user flows

## 🚀 **Getting Started**

Please confirm you understand this optimized approach and begin with **Session 1: Modern Foundation Setup**.

**Key Focus Areas:**
1. **tRPC + Prisma** backend with type safety
2. **Next.js 15** with modern features
3. **Single Flutter app** with factory/customer flavors
4. **Zod validation** throughout
5. **Modern deployment** with Vercel/Railway

**Important Questions:**
1. Are you ready to start with the optimized single Flutter app approach?
2. Do you understand the tRPC + Prisma integration requirements?
3. Should I prioritize any specific flavor (factory/customer) during development?
4. Are you comfortable with the accelerated 16-18 week timeline?

This approach will deliver a superior product in 8-10 weeks less time while providing better user experiences for both factory staff and customers.