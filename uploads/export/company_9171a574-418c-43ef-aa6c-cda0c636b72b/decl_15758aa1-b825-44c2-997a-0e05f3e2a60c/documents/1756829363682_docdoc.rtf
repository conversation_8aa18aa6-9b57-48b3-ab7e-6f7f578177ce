{\rtf1\ansi\ansicpg1252\cocoartf2761
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fnil\fcharset0 Menlo-Regular;\f1\fnil\fcharset0 Menlo-Italic;}
{\colortbl;\red255\green255\blue255;\red205\green204\blue213;\red19\green19\blue19;\red218\green124\blue212;
\red114\green201\blue195;\red233\green160\blue109;\red115\green207\blue184;\red245\green188\blue80;\red90\green90\blue90;
\red153\green132\blue242;\red229\green189\blue123;\red131\green178\blue248;}
{\*\expandedcolortbl;;\cssrgb\c83922\c83922\c86667;\cssrgb\c9412\c9412\c9412;\cssrgb\c89020\c58039\c86275;
\cssrgb\c50980\c82353\c80784;\cssrgb\c93725\c69020\c50196;\cssrgb\c51373\c83922\c77255;\cssrgb\c97255\c78039\c38431;\cssrgb\c42745\c42745\c42745;
\cssrgb\c66667\c60784\c96078;\cssrgb\c92157\c78431\c55294;\cssrgb\c58039\c75686\c98039;}
\paperw11900\paperh16840\margl1440\margr1440\vieww11520\viewh8400\viewkind0
\deftab720
\pard\pardeftab720\partightenfactor0

\f0\fs24 \cf2 \cb3 \expnd0\expndtw0\kerning0
\outl0\strokewidth0 \strokec2 # Reader Architecture Documentation\cb1 \
\
\cb3 ## Text Display Components\cb1 \
\
\cb3 ### 1. Core Components\cb1 \
\
\cb3 #### Reader Component (\cf4 \strokec4 `Reader.tsx`\cf2 \strokec2 )\cb1 \
\cb3 - Main container for the reading experience\cb1 \
\cb3 - Handles user interactions and navigation\cb1 \
\cb3 - Manages the iframe for content display\cb1 \
\cb3 - Implements touch and keyboard controls\cb1 \
\cb3 - Handles text selection and annotations\cb1 \
\
\cb3 #### Typography System\cb1 \
\cb3 The reader uses a flexible typography system controlled through \cf4 \strokec4 `TypographyConfiguration`\cf2 \strokec2 :\cb1 \
\cb3 ```typescript\cb1 \
\pard\pardeftab720\partightenfactor0
\cf5 \cb3 \strokec5 interface\cf2 \strokec2  \cf6 \strokec6 TypographyConfiguration\cf2 \strokec2  \{\cb1 \
\pard\pardeftab720\partightenfactor0
\cf2 \cb3   fontFamily\cf7 \strokec7 ?\cf2 \strokec2 : \cf5 \strokec5 string\cf2 \strokec2 ;\cb1 \
\cb3   fontSize\cf7 \strokec7 ?\cf2 \strokec2 : \cf5 \strokec5 string\cf2 \strokec2 ;\cb1 \
\cb3   fontWeight\cf7 \strokec7 ?\cf2 \strokec2 : \cf5 \strokec5 number\cf2 \strokec2 ;\cb1 \
\cb3   lineHeight\cf7 \strokec7 ?\cf2 \strokec2 : \cf5 \strokec5 number\cf2 \strokec2 ;\cb1 \
\cb3   zoom\cf7 \strokec7 ?\cf2 \strokec2 : \cf5 \strokec5 number\cf2 \strokec2 ;\cb1 \
\cb3   spread\cf7 \strokec7 ?\cf2 \strokec2 : \cf6 \strokec6 RenditionSpread\cf2 \strokec2 ;\cb1 \
\cb3 \}\cb1 \
\cb3 ```\cb1 \
\
\cb3 ### 2. Layout System\cb1 \
\
\cb3 #### Layout Component (\cf4 \strokec4 `Layout.tsx`\cf2 \strokec2 )\cb1 \
\cb3 - Implements mobile-first design\cb1 \
\cb3 - Manages responsive layout transitions\cb1 \
\cb3 - Handles sidebar and navigation visibility\cb1 \
\cb3 - Uses SplitView for pane management\cb1 \
\
\cb3 #### SplitView System\cb1 \
\cb3 - Enables flexible pane layouts\cb1 \
\cb3 - Supports both horizontal and vertical splits\cb1 \
\cb3 - Manages resizable panels\cb1 \
\cb3 - Handles mobile and desktop layouts differently\cb1 \
\
\cb3 ### 3. Text Display Features\cb1 \
\
\cb3 #### Typography Controls\cb1 \
\cb3 - Font family selection (default, sans-serif, serif)\cb1 \
\cb3 - Font size adjustment (14px - 28px)\cb1 \
\cb3 - Font weight control (100-900)\cb1 \
\cb3 - Line height customization\cb1 \
\cb3 - Page spread options (single/double page)\cb1 \
\
\cb3 #### Mobile Optimizations\cb1 \
\cb3 - Touch-based navigation\cb1 \
\cb3 - Responsive typography scaling\cb1 \
\cb3 - Optimized layout for smaller screens\cb1 \
\cb3 - Performance-focused rendering\cb1 \
\
\cb3 ## Best Practices\cb1 \
\
\cb3 1. \cf8 \strokec8 **Mobile-First Design**\cf2 \cb1 \strokec2 \
\cb3    - No scrolling in paged content\cb1 \
\cb3    - Use pagination for navigation\cb1 \
\cb3    - Touch-optimized controls\cb1 \
\cb3    - Responsive typography\cb1 \
\
\cb3 2. \cf8 \strokec8 **Performance**\cf2 \cb1 \strokec2 \
\cb3    - Efficient text rendering\cb1 \
\cb3    - Optimized layout calculations\cb1 \
\cb3    - Memory-conscious content management\cb1 \
\cb3    - Smooth page transitions\cb1 \
\
\cb3 3. \cf8 \strokec8 **Typography**\cf2 \cb1 \strokec2 \
\cb3    - Consistent text styling\cb1 \
\cb3    - Accessible font sizes\cb1 \
\cb3    - Clear hierarchy\cb1 \
\cb3    - Proper character spacing\cb1 \
\
\cb3 4. \cf8 \strokec8 **Layout**\cf2 \cb1 \strokec2 \
\cb3    - Clean component structure\cb1 \
\cb3    - Flexible pane management\cb1 \
\cb3    - Responsive design patterns\cb1 \
\cb3    - Touch-friendly interactions\cb1 \
\
\cb3 ## Implementation Guidelines\cb1 \
\
\cb3 1. \cf8 \strokec8 **Text Display**\cf2 \cb1 \strokec2 \
\cb3    ```typescript\cb1 \
\cb3    
\f1\i \cf9 \strokec9 // Example typography configuration
\f0\i0 \cf2 \cb1 \strokec2 \
\cb3    \cf5 \strokec5 const\cf2 \strokec2  \cf10 \strokec10 typography\cf2 \strokec2  = \{\cb1 \
\cb3      fontFamily: \cf4 \strokec4 'serif'\cf2 \strokec2 ,\cb1 \
\cb3      fontSize: \cf4 \strokec4 '18px'\cf2 \strokec2 ,\cb1 \
\cb3      fontWeight: \cf11 \strokec11 400\cf2 \strokec2 ,\cb1 \
\cb3      lineHeight: \cf11 \strokec11 1.5\cf2 \strokec2 ,\cb1 \
\cb3      spread: RenditionSpread.\cf10 \strokec10 Auto\cf2 \cb1 \strokec2 \
\cb3    \};\cb1 \
\cb3    ```\cb1 \
\
\cb3 2. \cf8 \strokec8 **Layout Structure**\cf2 \cb1 \strokec2 \
\cb3    ```typescript\cb1 \
\cb3    
\f1\i \cf9 \strokec9 // Basic layout structure
\f0\i0 \cf2 \cb1 \strokec2 \
\cb3    <\cf12 \strokec12 Layout\cf2 \strokec2 >\cb1 \
\cb3      <\cf12 \strokec12 SplitView\cf2 \strokec2 >\cb1 \
\cb3        <\cf12 \strokec12 NavigationPane\cf2 \strokec2  />\cb1 \
\cb3        <\cf12 \strokec12 ReaderContent\cf2 \strokec2  />\cb1 \
\cb3        <\cf12 \strokec12 ControlPane\cf2 \strokec2  />\cb1 \
\cb3      </\cf12 \strokec12 SplitView\cf2 \strokec2 >\cb1 \
\cb3    </\cf12 \strokec12 Layout\cf2 \strokec2 >\cb1 \
\cb3    ```\cb1 \
\
\cb3 3. \cf8 \strokec8 **Mobile Considerations**\cf2 \cb1 \strokec2 \
\cb3    ```typescript\cb1 \
\cb3    
\f1\i \cf9 \strokec9 // Mobile-specific layout
\f0\i0 \cf2 \cb1 \strokec2 \
\cb3    \cf5 \strokec5 const\cf2 \strokec2  \cf10 \strokec10 mobile\cf2 \strokec2  = \cf6 \strokec6 useMobile\cf2 \strokec2 ();\cb1 \
\cb3    \cf7 \strokec7 return\cf2 \strokec2  (\cb1 \
\cb3      <\cf12 \strokec12 div\cf2 \strokec2  \cf12 \strokec12 className\cf2 \strokec2 =\{\cf6 \strokec6 clsx\cf2 \strokec2 (\cb1 \
\cb3        \cf4 \strokec4 'Reader'\cf2 \strokec2 ,\cb1 \
\cb3        
\f1\i mobile
\f0\i0  \cf7 \strokec7 ?\cf2 \strokec2  \cf4 \strokec4 'mobile-layout'\cf2 \strokec2  : \cf4 \strokec4 'desktop-layout'\cf2 \cb1 \strokec2 \
\cb3      )\}>\cb1 \
\cb3      \{
\f1\i \cf9 \strokec9 /* Content */
\f0\i0 \cf2 \strokec2 \}\cb1 \
\cb3      </\cf12 \strokec12 div\cf2 \strokec2 >\cb1 \
\cb3    );\cb1 \
\cb3    ```\cb1 \
\
\cb3 ## Component Relationships\cb1 \
\
\cb3 ```mermaid\cb1 \
\cb3 graph TD\cb1 \
\cb3     A[Layout] --> B[Reader]\cb1 \
\cb3     B --> C[PagedContent]\cb1 \
\cb3     B --> D[Typography]\cb1 \
\cb3     C --> E[Text Display]\cb1 \
\cb3     D --> E\cb1 \
\cb3     B --> F[Navigation]\cb1 \
\cb3     B --> G[Controls]\cb1 \
\cb3 ```\cb1 \
\
\cb3 ## Key Considerations\cb1 \
\
\cb3 1. \cf8 \strokec8 **Accessibility**\cf2 \cb1 \strokec2 \
\cb3    - Proper text scaling\cb1 \
\cb3    - Keyboard navigation\cb1 \
\cb3    - Screen reader support\cb1 \
\cb3    - High contrast options\cb1 \
\
\cb3 2. \cf8 \strokec8 **Performance**\cf2 \cb1 \strokec2 \
\cb3    - Efficient text rendering\cb1 \
\cb3    - Optimized layout updates\cb1 \
\cb3    - Memory management\cb1 \
\cb3    - Smooth animations\cb1 \
\
\cb3 3. \cf8 \strokec8 **User Experience**\cf2 \cb1 \strokec2 \
\cb3    - Intuitive navigation\cb1 \
\cb3    - Consistent behavior\cb1 \
\cb3    - Responsive controls\cb1 \
\cb3    - Clear visual feedback\cb1 \
\
\cb3 4. \cf8 \strokec8 **Maintainability**\cf2 \cb1 \strokec2 \
\cb3    - Clean component structure\cb1 \
\cb3    - Clear dependencies\cb1 \
\cb3    - Type safety\cb1 \
\cb3    - Documentation \cb1 \
}