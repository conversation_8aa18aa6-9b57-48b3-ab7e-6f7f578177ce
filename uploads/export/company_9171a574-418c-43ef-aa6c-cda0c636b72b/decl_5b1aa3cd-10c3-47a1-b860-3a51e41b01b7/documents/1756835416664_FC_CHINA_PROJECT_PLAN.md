**FC App: Technical & Product Plan**
====================================

**Core Features (Private B2B Model)**
-------------------------------------

*   **Private Showrooms:** Each factory has an isolated product catalog and showroom, viewable only by its authenticated customers. Users can browse products, specifications, and pricing per factory.
    
*   **Customer Accounts & Multi-Factory Login:** Customers receive unique credentials from each factory. The app provides a unified dashboard to add factory accounts and switch contexts, so a user with multiple factory logins can easily toggle between them.
    
*   **Inquiry / Quote Workflow:** Customers can submit product inquiries or request quotes directly from the showroom items. Factories manage and respond to quotes. This workflow replaces shopping-cart checkout with a B2B quote negotiation process.
    
*   **Messaging/Communication:** Real-time messaging channels (or comment threads) between factory sales reps and customers are provided. Notifications alert users to new messages or quote replies. (Push notifications can use Firebase Cloud Messaging or similar.)
    
*   **Order Tracking:** Once an order is placed (manually tracked or via factory systems), customers can view order status updates and shipment progress in-app. The app shows tracking numbers and real-time updates for logistics.
    
*   **Shipment Tracking Integration:** The system pulls tracking updates from external shipping APIs. We recommend services like AfterShip or TrackingMore for broad carrier coverage (including China carriers) .
    
*   **Factory Dashboard:** Factories have admin interfaces to manage their product catalog, pricing, customer accounts, and respond to inquiries. Each factory manages its own data and user accounts.
    

**Multi-Tenancy & Access Control**
----------------------------------

*   **Tenant Concept (Factory as Tenant):**  Each factory is treated as a “tenant” in a multi-tenant architecture. We can use a **shared-database, shared-schema** model where every table includes a factory\_id (tenant ID) column to segregate data . For example, a products table would include the owning factory’s ID. This is cost-effective and simple, but requires strict filtering (to avoid cross-tenant data leaks) .
    
*   **RBAC (Roles & Scopes):**  Implement role-based access control. Two primary roles exist: _Factory Users_ (e.g. sales reps or admins) and _Customer Users_. Factory users can only view and modify their own factory’s data (products, inquiries, orders). Customer users can only see data linked to their login (the showroom, quotes, and orders for that factory). We can enforce this via middleware that checks the user’s role and factory ID on every request. For B2B multi-tenancy, best practice is to group users by organization and apply per-group RBAC (e.g. Auth0 Organizations). Each factory’s group has its own permissions, ensuring strict isolation.
    
*   **Authentication:**  Use a token-based system (e.g. OAuth2/JWT). Each login issues a JWT containing the user role and factory ID. Switching accounts in the UI simply means obtaining a new token for the other factory account. Storing multiple tokens (one per factory) in the app lets the user switch contexts without re-logging in.
    

**Backend Architecture & Database Schema**
------------------------------------------

*   **API Layer:** The backend exposes RESTful (or GraphQL) APIs consumed by Flutter. Endpoints are namespaced by tenant; e.g. /api/v1/{factoryId}/products. All requests are authenticated and include the user’s factory ID for authorization checks.
    
*   Each table includes factory\_id to isolate tenant data . Queries always filter on this, enforced by backend logic (or PostgreSQL RLS policies). This “shared schema” model is simplest to implement .
    
    *   **Factories:** (factory\_id PK, name, contact\_info, settings…)
        
    *   **FactoryUsers:** (user\_id PK, factory\_id FK, name, email, password\_hash, role) – includes factory admins/sales reps.
        
    *   **Customers:** (customer\_id PK, name, contact\_info, optional global profile) – optional if unifying customer data.
        
    *   **FactoryCustomers:** (account\_id PK, factory\_id FK, customer\_name, email, username, password\_hash, status) – stores credentials for factory-specific customer accounts.
        
    *   **Products:** (product\_id PK, factory\_id FK, name, description, price, stock, images, etc.) – each row is scoped to a factory.
        
    *   **Quotes/Inquiries:** (quote\_id PK, factory\_id FK, account\_id FK, product\_id FK, quantity, status, comments, created\_at) – tracks requests for pricing.
        
    *   **Messages:** (message\_id PK, factory\_id FK, from\_user\_id, to\_user\_id, body, timestamp) – supports chat threads between factory users and customer accounts.
        
    *   **Orders:** (order\_id PK, factory\_id FK, account\_id FK, status, total, created\_at) – after a quote is accepted, an order record can be created.
        
    *   **Shipments:** (shipment\_id PK, order\_id FK, carrier, tracking\_number, status, last\_update) – tracks shipments for orders.
        
*   **Schema Alternatives:**  For stronger isolation, PostgreSQL schemas per factory can be used (Pattern 2), or even separate databases per factory (Pattern 3) . However, the latter is complex to manage at scale. We recommend starting with the shared-schema model for rapid MVP development, and consider schema-per-tenant only if strict isolation/regulatory compliance demands it .
    

**Technology Stack Recommendations**
------------------------------------

*   **Mobile App:** Flutter (given). Use standard packages for state management (e.g. Provider, Bloc), HTTP/GraphQL communication, and local storage for caching. Use Flutter’s official flutter\_localizations and intl packages for i18n .
    
*   **Database:** PostgreSQL is recommended for structured data (products, orders). Firebase/Firestore (NoSQL) works if using Firebase. MongoDB is an option (with Parse or Node), but relational queries (joins, transactions) favor SQL.**Messaging & Notifications:** Use WebSockets (Socket.IO) or push notifications (Firebase Cloud Messaging) for real-time chat and status updates.Overall, for fastest integration with Flutter, **Firebase or Supabase** are strong candidates (great Flutter support ). For full control and SQL, a Node.js (NestJS) + PostgreSQL stack is solid.
    
    *   **Firebase:** Provides Auth, Firestore (or Realtime Database), Cloud Functions, and push notifications in one suite. Excellent Flutter support . Its real-time DB and hosting can speed up MVP launch . However, complex queries (joins across data) can be harder, and costs can rise.
        
    *   **Supabase (Postgres):** An open-source Firebase alternative with PostgreSQL. Offers Auth, Realtime (on Postgres), and REST/GraphQL. Easy Flutter integration via supabase-flutter. Supports SQL for complex queries. Good middle ground (BaaS + SQL).
        
    *   **Node.js (NestJS/Express):** A popular choice. NestJS (TypeScript) provides structured modularity and built-in support for WebSockets (for messaging). Easy to deploy on Node environments. Pair with an ORM (TypeORM or Prisma) for Postgres or MongoDB.
        
    *   **Python (Django/DRF):** Rapid development with Django ORM and REST Framework. Mature but less real-time by default (can use Django Channels).
        
    *   **AWS Amplify:** Offers Auth, AppSync (GraphQL), DynamoDB or RDS, and more. Very scalable and robust , but has a steeper learning curve and may be overkill for an MVP.
        
    *   **Parse Server / Back4App:** Open-source backend with MongoDB/Postgres support, Real-time & push. Can self-host or use Back4App PaaS. Provides many features out-of-the-box (live queries, cloud functions) .
        

**Shipment Tracking Integration**
---------------------------------

For live shipment updates (domestic and international, including China carriers), we recommend API aggregators with generous free tiers and broad coverage:

*   **TrackingMore:** Supports 1,300+ carriers worldwide (including China Post) and offers a free plan (100 shipments/month) . It provides real-time tracking and webhooks. The free tier supports 1200+ carriers .
    
*   **AfterShip:** Offers APIs for 700+ global couriers (UPS, DHL, USPS) and China Post/EMS/ePacket. Developers get a free API key (with limited usage) and 99.9% uptime . AfterShip is widely used for end-to-end tracking and has SDKs/webhooks.
    
*   **EasyPost:** Provides tracking for dozens of carriers (USPS, UPS, DHL, China Post via partnerships) and includes up to 3,000 free shipments per month (tracking included with each label) . It is very easy to integrate with Flutter (REST API).
    
*   **17Track (Self-Hosted):** (Note: 17Track is a popular Chinese tracking service, but it has no official free API. Using it may require scraping or unofficial methods.)
    

**Recommendation:** Use a combination: e.g. pull domestic carriers via EasyPost and China Post via AfterShip. Or use a universal solution like TrackingMore which already includes China Post. These services simplify multi-carrier tracking and reduce WISMO (where-is-my-order) inquiries.

**Infrastructure & Deployment**
-------------------------------

*   **Cloud Hosting:** Use a cloud platform (AWS, GCP, or Azure) for reliability and scalability. For example, run the backend on AWS (ECS/EKS, or Elastic Beanstalk) or GCP App Engine/Cloud Run for containers. Use managed database services (Amazon RDS or Cloud SQL for PostgreSQL) for ease of maintenance and automatic backups. Cloud deployment ensures high availability and global access. AWS/GCP also offer CDN, auto-scaling, and monitoring out of the box .
    
*   **Containerization:** Package backend as Docker containers for consistent environments. Use Kubernetes or ECS for container orchestration to manage multiple factories (tenants) workload.
    
*   **CI/CD Pipeline:** Implement Continuous Integration/Continuous Deployment (e.g. GitHub Actions, GitLab CI). Automate testing and deployment on code push. This speeds up staged rollouts and quick fixes.
    
*   **Security & Compliance:** Encrypt data at rest (managed DB does this by default) and in transit (HTTPS everywhere). Apply principle of least privilege on infrastructure. Regularly update dependencies.
    
*   **Global Access:** Since Chinese users are involved, deploy servers in Asia (e.g., AWS Singapore/Shanghai region) to reduce latency. Use CDN for static assets.
    

**Localization & Internationalization**
---------------------------------------

*   **Flutter i18n:** Leverage Flutter’s built-in internationalization support. Add the flutter\_localizations and intl packages (as per Flutter docs) and configure localizationsDelegates and supportedLocales in MaterialApp . For example:
    

Plain textANTLR4BashCC#CSSCoffeeScriptCMakeDartDjangoDockerEJSErlangGitGoGraphQLGroovyHTMLJavaJavaScriptJSONJSXKotlinLaTeXLessLuaMakefileMarkdownMATLABMarkupObjective-CPerlPHPPowerShell.propertiesProtocol BuffersPythonRRubySass (Sass)Sass (Scss)SchemeSQLShellSwiftSVGTSXTypeScriptWebAssemblyYAMLXML`   supportedLocales: [    Locale('en'), // English    Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hans'), // Chinese (Simplified)    Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hant'), // Chinese (Traditional)    // Include specific country variants if needed (e.g. zh_Hans_CN, zh_Hant_TW) [oai_citation:25‡docs.flutter.dev](https://docs.flutter.dev/ui/accessibility-and-internationalization/internationalization#:~:text=supportedLocales: ,fromSubtags( languageCode: 'zh', scriptCode: 'Hant).  ],   `

*   The Flutter docs recommend explicitly listing Chinese variants to cover Simplified (China) and Traditional (Taiwan/HK) scripts .
    
*   **Resource Files:** Store localized strings in ARB or JSON files. Use Flutter’s intl tooling (flutter gen-l10n) to generate localization code. Ensure all user-facing text (buttons, messages, labels) is translated.
    
*   **Fonts & Layout:** Use fonts that support Chinese characters (e.g. Noto Sans CJK). Test layouts for text length differences.
    
*   **Right-to-Left:** Not needed for English/Chinese, but Flutter handles RTL automatically if needed.
    

**MVP Roadmap (Staged Rollout)**
--------------------------------

Focus on core value with minimal effort, then iterate :

1.  *   Authentication (factory and customer accounts).
        
    *   Private showroom view (product catalog per factory).
        
    *   Basic inquiry/quote submission from customer side.
        
    *   Simple messaging channel (in-app chat or messaging).
        
    *   Order placement placeholder (orders can be created from quotes).
        
    *   Minimal order status (e.g. “Pending”, “Processed”).
        
    *   This phase tests the core workflow and gives immediate value with minimal features .
        
2.  *   Full quote workflow: customers can receive, review, and accept quotes.
        
    *   Order tracking: link shipments to orders.
        
    *   Integration with one shipment API (e.g. TrackingMore) to fetch tracking updates.
        
    *   Notifications: push/email alerts for status changes (quote replies, new messages, shipments).
        
    *   UI improvements and multi-account switching.
        
3.  *   Add multilingual UI (English/Chinese) and locale-specific formatting.
        
    *   Implement additional shipment APIs (cover more carriers, auto-detect).
        
    *   Analytics & reporting for factories (e.g. inquiry conversion).
        
    *   Performance optimizations and caching.
        
    *   Hardening: security audit, scalability tests.
        
4.  *   Additional nice-to-haves: exporting quotes/orders, bulk product upload, deeper integrations (e.g. factory ERP sync), AI-powered product search or Q&A.
        
    *   Complete documentation and onboarding flows.
        

At each stage, gather user feedback and iterate. The MVP goal is to **validate the product’s core value** (private factory-customer commerce) with minimal effort before adding complexity.

**Sources:** The above plan draws on best practices for Flutter apps, multi-tenant architectures, and B2B e-commerce features. For example, Flutter’s official docs outline localization setup ; multi-tenant designs are discussed in industry guides ; and tracking API recommendations come from provider documentation . These informed the technical choices above.