/**
 * Manufacturing ERP - Export Trade Statistics Component
 * Professional clickable filter dashboard following established patterns
 */

"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  FileText,
  Send,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  TrendingUp,
  ArrowRight,

  Activity,
  Target,
  Plane,
  Filter,
  X,
  Package
} from "lucide-react"

interface ExportStatsProps {
  stats: {
    total: number
    draft: number
    submitted: number
    processing: number
    approved: number
    cleared: number
    rejected: number
  }
}

export function ExportStats({ stats }: ExportStatsProps) {

  const [currentTime, setCurrentTime] = useState<string>("")
  const [isClient, setIsClient] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const currentFilter = searchParams.get('status')

  const activeDeclarations = stats.submitted + stats.processing + stats.approved
  const completionRate = stats.total > 0 ? Math.round((stats.cleared / stats.total) * 100) : 0

  // Fix hydration mismatch by only showing time on client
  useEffect(() => {
    setIsClient(true)
    setCurrentTime(new Date().toLocaleTimeString())
  }, [])

  // Filter handler for clickable cards
  const handleFilter = (status: string | null) => {
    const params = new URLSearchParams(searchParams.toString())

    if (status === null || status === currentFilter) {
      // Clear filter if clicking the same filter or "All"
      params.delete('status')
    } else {
      // Set new filter
      params.set('status', status)
    }

    // Navigate with new filter
    const newUrl = params.toString() ? `/export?${params.toString()}` : '/export'
    router.push(newUrl)
  }

  // Get card styling based on filter state
  const getCardStyle = (status: string | null) => {
    if (!currentFilter) return "cursor-pointer hover:shadow-md transition-all duration-200"

    if (status === currentFilter) {
      return "cursor-pointer ring-2 ring-blue-500 bg-blue-50 hover:shadow-md transition-all duration-200"
    }

    return "cursor-pointer opacity-60 hover:opacity-80 hover:shadow-md transition-all duration-200"
  }

  return (
    <div className="space-y-6">
      {/* Critical Alerts Section */}
      {(stats.rejected > 0) && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <CardTitle className="text-red-800">Attention Required</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
                  <div className="text-sm text-red-600">Rejected Declarations</div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="border-red-300 text-red-700 hover:bg-red-100"
                onClick={() => handleFilter('rejected')}
              >
                Review Issues
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Statistics Dashboard */}
      <Card>
        <CardContent className="space-y-6">
          {/* ✅ BEAUTIFUL CLICKABLE CARDS - FOLLOWING ERP STANDARDS */}
          <div className="grid gap-4 md:grid-cols-5">
            {/* Total Declarations Card */}
            <Card
              className={`cursor-pointer transition-all duration-200 hover:shadow-md ${!currentFilter ? "ring-2 ring-primary shadow-md" : ""}`}
              onClick={() => handleFilter(null)}
              title="Click to show all declarations"
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Declarations</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
                <p className="text-xs text-muted-foreground">Success Rate: {completionRate}%</p>
              </CardContent>
            </Card>

            {/* Draft Declarations Card */}
            <Card
              className={`cursor-pointer transition-all duration-200 hover:shadow-md ${currentFilter === 'draft' ? "ring-2 ring-gray-400 shadow-md bg-gray-50" : ""}`}
              onClick={() => handleFilter('draft')}
              title="Click to filter draft declarations"
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Draft</CardTitle>
                <FileText className="h-4 w-4 text-gray-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-700">{stats.draft}</div>
                <p className="text-xs text-gray-500">Pending submission</p>
              </CardContent>
            </Card>

            {/* Submitted Declarations Card */}
            <Card
              className={`cursor-pointer transition-all duration-200 hover:shadow-md ${currentFilter === 'submitted' ? "ring-2 ring-yellow-400 shadow-md bg-yellow-50" : ""}`}
              onClick={() => handleFilter('submitted')}
              title="Click to filter submitted declarations"
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Submitted</CardTitle>
                <Send className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-700">{stats.submitted}</div>
                <p className="text-xs text-yellow-600">Under review</p>
              </CardContent>
            </Card>

            {/* Approved Declarations Card */}
            <Card
              className={`cursor-pointer transition-all duration-200 hover:shadow-md ${currentFilter === 'approved' ? "ring-2 ring-blue-400 shadow-md bg-blue-50" : ""}`}
              onClick={() => handleFilter('approved')}
              title="Click to filter approved declarations"
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Approved</CardTitle>
                <CheckCircle className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-700">{stats.approved}</div>
                <p className="text-xs text-blue-600">Ready for clearance</p>
              </CardContent>
            </Card>

            {/* Cleared Declarations Card */}
            <Card
              className={`cursor-pointer transition-all duration-200 hover:shadow-md ${currentFilter === 'cleared' ? "ring-2 ring-green-400 shadow-md bg-green-50" : ""}`}
              onClick={() => handleFilter('cleared')}
              title="Click to filter cleared declarations"
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Cleared</CardTitle>
                <Plane className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-700">{stats.cleared}</div>
                <p className="text-xs text-green-600">Export completed</p>
              </CardContent>
            </Card>
          </div>



          {/* Clear Filter Button */}
          {currentFilter && (
            <div className="flex justify-center pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleFilter(null)}
                className="text-gray-600 border-gray-300"
              >
                <X className="mr-2 h-4 w-4" />
                Clear Filter ({currentFilter})
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
