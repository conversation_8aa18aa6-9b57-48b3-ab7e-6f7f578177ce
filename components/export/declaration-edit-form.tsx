/**
 * Manufacturing ERP - Declaration Edit Form Component
 * Professional edit form with status management following ERP standards
 */

"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { Save, Package } from "lucide-react"

interface DeclarationEditFormProps {
  declaration: any
}

export function DeclarationEditForm({ declaration }: DeclarationEditFormProps) {
  const router = useRouter()
  const { toast } = useSafeToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    number: declaration.number || "",
    status: declaration.status || "draft",
  })

  // ✅ PROFESSIONAL: Status options following ERP standards
  const statusOptions = [
    { value: "draft", label: "Draft", color: "gray" },
    { value: "submitted", label: "Submitted", color: "yellow" },
    { value: "approved", label: "Approved", color: "blue" },
    { value: "cleared", label: "Cleared", color: "green" },
    { value: "rejected", label: "Rejected", color: "red" },
  ]

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "draft": return "secondary"
      case "submitted": return "outline"
      case "approved": return "default"
      case "cleared": return "default"
      case "rejected": return "destructive"
      default: return "secondary"
    }
  }

  // ✅ PROFESSIONAL: Form submission with proper error handling
  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch(`/api/export/declarations/${declaration.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to update declaration")
      }

      toast({
        title: "Success",
        description: "Declaration updated successfully",
      })

      router.push("/export")
      router.refresh()
    } catch (error) {
      console.error("Update error:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update declaration",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL: Declaration Details Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Declaration Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="number">Declaration Number</Label>
                <Input
                  id="number"
                  value={formData.number}
                  onChange={(e) => setFormData({ ...formData, number: e.target.value })}
                  placeholder="Enter declaration number"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => setFormData({ ...formData, status: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <Badge variant={getStatusVariant(option.value)}>
                            {option.label}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/export")}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                <Save className="mr-2 h-4 w-4" />
                {loading ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* ✅ PROFESSIONAL: Declaration Items Table */}
      <Card>
        <CardHeader>
          <CardTitle>Declaration Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>HS Code</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {declaration.items?.map((item: any) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{item.product?.name || "N/A"}</div>
                        {item.product?.description && (
                          <div className="text-sm text-muted-foreground">
                            {item.product.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {item.product?.sku || "N/A"}
                    </TableCell>
                    <TableCell>
                      {item.qty} {item.product?.unit || "units"}
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {item.hs_code || "N/A"}
                    </TableCell>
                  </TableRow>
                ))}
                {(!declaration.items || declaration.items.length === 0) && (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                      No items found for this declaration
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
