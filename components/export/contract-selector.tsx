/**
 * Manufacturing ERP - Contract Selector Component
 * 
 * Simple dropdown for selecting sales contracts when creating export declarations
 * Following established ERP patterns with zero breaking changes
 */

"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { FileText, User, Calendar } from "lucide-react"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"

interface SalesContract {
  id: string
  number: string
  status: string
  customer: {
    name: string
  }
  created_at: string
  items: Array<{
    id: string
    product: {
      id: string
      name: string
      hs_code?: string
    }
    qty: string
  }>
}

interface ContractSelectorProps {
  selectedContractId?: string
  onContractSelect: (contract: SalesContract | null) => void
  className?: string
}

export function ContractSelector({
  selectedContractId,
  onContractSelect,
  className,
}: ContractSelectorProps) {
  const [contracts, setContracts] = useState<SalesContract[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // ✅ PROFESSIONAL: Load available sales contracts
  useEffect(() => {
    loadContracts()
  }, [])

  const loadContracts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/contracts/sales')

      if (!response.ok) {
        throw new Error('Failed to load contracts')
      }

      const result = await response.json()

      // ✅ DEFENSIVE: Handle API response format { success: true, data: [...] }
      const contractsArray = Array.isArray(result.data) ? result.data : []

      // ✅ BUSINESS LOGIC: Only show approved/active contracts
      const eligibleContracts = contractsArray.filter((contract: SalesContract) =>
        contract.status === 'approved' || contract.status === 'active'
      )

      setContracts(eligibleContracts)
    } catch (error) {
      console.error('Error loading contracts:', error)
      setError(error instanceof Error ? error.message : 'Failed to load contracts')
    } finally {
      setLoading(false)
    }
  }

  // ✅ PROFESSIONAL: Handle contract selection
  const handleContractChange = (contractId: string) => {
    if (contractId === 'none') {
      onContractSelect(null)
      return
    }

    const selectedContract = contracts.find(c => c.id === contractId)
    if (selectedContract) {
      onContractSelect(selectedContract)
    }
  }

  // ✅ PROFESSIONAL: Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'approved': return 'default'
      case 'active': return 'secondary'
      default: return 'outline'
    }
  }

  // ✅ SEARCHABLE OPTIONS: Convert contracts to searchable options
  const contractOptions: SearchableSelectOption[] = [
    {
      value: 'none',
      label: 'No contract - Manual entry',
      subtitle: 'Create declaration without linking to a contract',
      description: '📝 Manual product entry',
    },
    ...contracts.map((contract) => ({
      value: contract.id,
      label: contract.number,
      subtitle: `${contract.customer.name} • ${new Date(contract.created_at).toLocaleDateString()}`,
      description: `${contract.items.length} items • ${contract.status}`,
    }))
  ]

  if (loading) {
    return (
      <div className={className}>
        <Label>Sales Contract (Optional)</Label>
        <div className="mt-1 p-3 border rounded-md text-sm text-muted-foreground">
          Loading contracts...
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={className}>
        <Label>Sales Contract (Optional)</Label>
        <div className="mt-1 p-3 border rounded-md text-sm text-red-600">
          Error: {error}
          <Button
            variant="outline"
            size="sm"
            onClick={loadContracts}
            className="ml-2"
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <Label htmlFor="contract-select">Sales Contract (Optional)</Label>
      <div className="mt-1">
        <SearchableSelect
          options={contractOptions}
          value={selectedContractId || 'none'}
          onValueChange={handleContractChange}
          placeholder="Search contracts or select manual entry..."
          searchPlaceholder="Search by contract number or customer..."
          emptyMessage="No contracts found."
          allowClear={false}
        />
      </div>

      {contracts.length === 0 && (
        <div className="mt-2 text-sm text-muted-foreground">
          No approved sales contracts available. You can still create the declaration manually.
        </div>
      )}
    </div>
  )
}
