/**
 * Manufacturing ERP - Inline Status Editor for Export Declarations
 * Professional inline editing following ERP standards
 */

"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { Check, X, Edit } from "lucide-react"
import { Button } from "@/components/ui/button"

interface InlineStatusEditorProps {
  declarationId: string
  currentStatus: string
  onStatusChange: (newStatus: string) => void
  declarationNumber?: string
}

export function InlineStatusEditor({
  declarationId,
  currentStatus,
  onStatusChange,
  declarationNumber,
}: InlineStatusEditorProps) {
  const { toast } = useSafeToast()
  const [isEditing, setIsEditing] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState(currentStatus)
  const [loading, setLoading] = useState(false)

  // ✅ PROFESSIONAL: Status options following ERP standards
  const statusOptions = [
    { value: "draft", label: "Draft", color: "gray" },
    { value: "submitted", label: "Submitted", color: "yellow" },
    { value: "approved", label: "Approved", color: "blue" },
    { value: "cleared", label: "Cleared", color: "green" },
    { value: "rejected", label: "Rejected", color: "red" },
  ]

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "draft": return "secondary"
      case "submitted": return "outline"
      case "approved": return "default"
      case "cleared": return "default"
      case "rejected": return "destructive"
      default: return "secondary"
    }
  }

  const handleSave = async () => {
    if (selectedStatus === currentStatus) {
      setIsEditing(false)
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/export/declarations/${declarationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: selectedStatus }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to update status")
      }

      onStatusChange(selectedStatus)
      setIsEditing(false)
      
      toast({
        title: "Status Updated",
        description: `Declaration ${declarationNumber || declarationId} status changed to ${selectedStatus}`,
      })
    } catch (error) {
      console.error("Status update error:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update status",
        variant: "destructive",
      })
      setSelectedStatus(currentStatus) // Reset to original
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setSelectedStatus(currentStatus)
    setIsEditing(false)
  }

  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <Select
          value={selectedStatus}
          onValueChange={setSelectedStatus}
          disabled={loading}
        >
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                <Badge variant={getStatusVariant(option.value)}>
                  {option.label}
                </Badge>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={handleSave}
            disabled={loading}
            className="h-6 w-6 p-0 text-green-600 hover:text-green-700"
          >
            <Check className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleCancel}
            disabled={loading}
            className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2">
      <Badge variant={getStatusVariant(currentStatus)}>
        {statusOptions.find(opt => opt.value === currentStatus)?.label || currentStatus}
      </Badge>
      <Button
        size="sm"
        variant="ghost"
        onClick={() => setIsEditing(true)}
        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <Edit className="h-3 w-3" />
      </Button>
    </div>
  )
}
