"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useSafeToast } from "@/hooks/use-safe-toast"
import {
  AlertTriangle,
  CheckCircle,
  XCircle,
  Search,
  RefreshCw,
  Package,
  TrendingDown,
  TrendingUp,
  Eye,
  FileText,
  Settings
} from "lucide-react"

interface DiscrepancyAnalysis {
  productId: string
  productName: string
  productSku: string
  currentStock: number
  expectedStock: number
  discrepancy: number
  discrepancyType: 'shortage' | 'overage' | 'none'
  recentShipments: Array<{
    shipmentNumber: string
    quantity: number
    date: string
    inventoryReduced: boolean
  }>
  recentTransactions: Array<{
    type: string
    quantity: number
    date: string
    reference?: string
  }>
  riskLevel: 'high' | 'medium' | 'low'
  recommendedAction: string
}

export function InventoryDiscrepancyAnalyzer() {
  const [analysis, setAnalysis] = useState<DiscrepancyAnalysis[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useSafeToast()

  const runAnalysis = async () => {
    setLoading(true)
    setError(null)

    try {
      // Fetch current inventory
      const inventoryResponse = await fetch('/api/inventory/lots')
      if (!inventoryResponse.ok) {
        throw new Error(`Failed to fetch inventory: ${inventoryResponse.status}`)
      }
      const inventoryResult = await inventoryResponse.json()

      // Handle different inventory response formats
      let inventoryData = []
      if (Array.isArray(inventoryResult)) {
        inventoryData = inventoryResult
      } else if (inventoryResult.data && Array.isArray(inventoryResult.data)) {
        inventoryData = inventoryResult.data
      } else if (inventoryResult.lots && Array.isArray(inventoryResult.lots)) {
        inventoryData = inventoryResult.lots
      } else {
        console.warn('Inventory API returned unexpected format:', inventoryResult)
        inventoryData = []
      }

      // Fetch recent shipments (last 90 days)
      const shipmentsResponse = await fetch('/api/shipping/shipments')
      let shipmentsData = []
      if (shipmentsResponse.ok) {
        const shipmentsResult = await shipmentsResponse.json()
        // Handle different response formats
        if (Array.isArray(shipmentsResult)) {
          shipmentsData = shipmentsResult
        } else if (shipmentsResult.data && Array.isArray(shipmentsResult.data)) {
          shipmentsData = shipmentsResult.data
        } else if (shipmentsResult.shipments && Array.isArray(shipmentsResult.shipments)) {
          shipmentsData = shipmentsResult.shipments
        } else {
          console.warn('Shipments API returned unexpected format:', shipmentsResult)
          shipmentsData = []
        }

        // Filter to last 90 days if we have data
        if (shipmentsData.length > 0) {
          const ninetyDaysAgo = new Date()
          ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90)
          shipmentsData = shipmentsData.filter((s: any) =>
            s.created_at && new Date(s.created_at) >= ninetyDaysAgo
          )
        }
      } else {
        console.warn(`Failed to fetch shipments (${shipmentsResponse.status}), continuing without shipment data`)
      }

      // Always use inventory-only analysis for now since it's most reliable
      const analysisResults = await analyzeInventoryOnly(inventoryData)

      console.log(`Analysis complete: ${analysisResults.length} products analyzed`)

      setAnalysis(analysisResults)

      const highRiskCount = analysisResults.filter(a => a.riskLevel === 'high').length
      const mediumRiskCount = analysisResults.filter(a => a.riskLevel === 'medium').length
      const totalIssues = analysisResults.filter(a => a.discrepancyType !== 'none').length

      toast({
        title: "Analysis Complete",
        description: `Found ${totalIssues} issues: ${highRiskCount} high risk, ${mediumRiskCount} medium risk`,
        variant: highRiskCount > 0 ? "destructive" : mediumRiskCount > 0 ? "default" : "default"
      })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Analysis failed'
      setError(errorMessage)
      toast({
        title: "Analysis Failed",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const analyzeDiscrepancies = async (
    inventory: any[],
    shipments: any[],
    transactions: any[]
  ): Promise<DiscrepancyAnalysis[]> => {
    // Group inventory by product
    const productInventory = new Map()

    inventory.forEach(lot => {
      const productId = lot.product_id
      if (!productInventory.has(productId)) {
        productInventory.set(productId, {
          productId,
          productName: lot.product?.name || 'Unknown',
          productSku: lot.product?.sku || productId,
          currentStock: 0,
          lots: []
        })
      }

      const product = productInventory.get(productId)
      product.currentStock += parseFloat(lot.qty || 0)
      product.lots.push(lot)
    })

    // Analyze each product
    const results: DiscrepancyAnalysis[] = []

    for (const [productId, productData] of productInventory) {
      // Get recent shipments for this product
      const productShipments = shipments
        .filter((s: any) => s.items?.some((item: any) => item.product_id === productId))
        .flatMap((s: any) =>
          s.items
            .filter((item: any) => item.product_id === productId)
            .map((item: any) => ({
              shipmentNumber: s.number,
              quantity: parseFloat(item.quantity || 0),
              date: s.created_at,
              inventoryReduced: s.status === 'shipped' // Assume shipped means inventory was reduced
            }))
        )

      // Get recent transactions for this product
      const productTransactions = transactions
        .filter((t: any) => t.product_id === productId)
        .map((t: any) => ({
          type: t.transaction_type || t.type,
          quantity: parseFloat(t.qty || 0),
          date: t.created_at,
          reference: t.reference
        }))

      // Calculate expected stock based on shipments that should have reduced inventory
      const shippedButNotReduced = productShipments
        .filter(s => s.inventoryReduced)
        .reduce((sum, s) => sum + s.quantity, 0)

      const expectedStock = productData.currentStock + shippedButNotReduced
      const discrepancy = productData.currentStock - expectedStock

      let discrepancyType: 'shortage' | 'overage' | 'none' = 'none'
      let riskLevel: 'high' | 'medium' | 'low' = 'low'
      let recommendedAction = 'No action needed'

      if (Math.abs(discrepancy) > 0.01) { // Account for floating point precision
        discrepancyType = discrepancy < 0 ? 'shortage' : 'overage'

        const discrepancyPercentage = Math.abs(discrepancy) / Math.max(expectedStock, 1) * 100

        if (discrepancyPercentage > 50 || Math.abs(discrepancy) > 1000) {
          riskLevel = 'high'
          recommendedAction = discrepancyType === 'shortage'
            ? 'Urgent: Investigate missing inventory and adjust stock'
            : 'Review: Investigate excess inventory source'
        } else if (discrepancyPercentage > 10 || Math.abs(discrepancy) > 100) {
          riskLevel = 'medium'
          recommendedAction = 'Review transactions and consider adjustment'
        } else {
          riskLevel = 'low'
          recommendedAction = 'Monitor for trends'
        }
      }

      results.push({
        productId,
        productName: productData.productName,
        productSku: productData.productSku,
        currentStock: productData.currentStock,
        expectedStock,
        discrepancy,
        discrepancyType,
        recentShipments: productShipments.slice(0, 5), // Last 5 shipments
        recentTransactions: productTransactions.slice(0, 5), // Last 5 transactions
        riskLevel,
        recommendedAction
      })
    }

    return results.sort((a, b) => {
      // Sort by risk level first, then by discrepancy magnitude
      const riskOrder = { high: 3, medium: 2, low: 1 }
      if (riskOrder[a.riskLevel] !== riskOrder[b.riskLevel]) {
        return riskOrder[b.riskLevel] - riskOrder[a.riskLevel]
      }
      return Math.abs(b.discrepancy) - Math.abs(a.discrepancy)
    })
  }

  // Enhanced analysis with actionable insights
  const analyzeInventoryOnly = async (inventory: any[]): Promise<DiscrepancyAnalysis[]> => {
    // Group inventory by product
    const productInventory = new Map()

    inventory.forEach(lot => {
      const productId = lot.product_id
      if (!productInventory.has(productId)) {
        productInventory.set(productId, {
          productId,
          productName: lot.product?.name || 'Unknown',
          productSku: lot.product?.sku || productId,
          currentStock: 0,
          lots: [],
          locations: new Set(),
          qualityStatuses: new Set()
        })
      }

      const product = productInventory.get(productId)
      product.currentStock += parseFloat(lot.qty || 0)
      product.lots.push(lot)
      product.locations.add(lot.location)
      product.qualityStatuses.add(lot.quality_status || 'pending')
    })

    const results: DiscrepancyAnalysis[] = []

    for (const [productId, productData] of productInventory) {
      let riskLevel: 'high' | 'medium' | 'low' = 'low'
      let recommendedAction = 'No action needed - stock levels are normal'
      let discrepancyType: 'shortage' | 'overage' | 'none' = 'none'
      let detailedAnalysis = []

      // Enhanced analysis with detailed insights
      if (productData.currentStock === 0) {
        riskLevel = 'high'
        discrepancyType = 'shortage'
        recommendedAction = '🚨 URGENT: Zero inventory detected'
        detailedAnalysis = [
          '• Check recent shipments - inventory may not have been reduced properly',
          '• Review shipping records for this product',
          '• Consider inventory adjustment if items were actually shipped',
          '• Investigate if this is a data sync issue'
        ]
      } else if (productData.currentStock < 10) {
        riskLevel = 'medium'
        discrepancyType = 'shortage'
        recommendedAction = '⚠️ Low stock alert - monitor closely'
        detailedAnalysis = [
          `• Current stock: ${productData.currentStock} units (below 10 unit threshold)`,
          '• Review recent demand patterns',
          '• Consider reordering if this is an active product',
          '• Set up low stock alerts for future monitoring'
        ]
      } else if (productData.currentStock > 10000) {
        riskLevel = 'medium'
        discrepancyType = 'overage'
        recommendedAction = '📊 High inventory levels - review demand'
        detailedAnalysis = [
          `• Current stock: ${productData.currentStock.toLocaleString()} units (above 10,000 threshold)`,
          '• Review demand forecasting accuracy',
          '• Consider if this is seasonal inventory buildup',
          '• Evaluate storage costs vs. demand patterns'
        ]
      } else {
        // Normal stock levels - but provide insights
        detailedAnalysis = [
          `• Current stock: ${productData.currentStock.toLocaleString()} units`,
          `• Stored in ${productData.locations.size} location(s)`,
          `• Quality status: ${Array.from(productData.qualityStatuses).join(', ')}`,
          '• Stock levels appear normal'
        ]
      }

      // Add location and quality insights
      if (productData.locations.size > 3) {
        detailedAnalysis.push(`• ⚠️ Stock scattered across ${productData.locations.size} locations - consider consolidation`)
        if (riskLevel === 'low') riskLevel = 'medium'
      }

      if (productData.qualityStatuses.has('pending') && productData.qualityStatuses.size > 1) {
        detailedAnalysis.push('• ⚠️ Mixed quality statuses - some inventory pending approval')
        if (riskLevel === 'low') riskLevel = 'medium'
      }

      results.push({
        productId,
        productName: productData.productName,
        productSku: productData.productSku,
        currentStock: productData.currentStock,
        expectedStock: productData.currentStock,
        discrepancy: 0,
        discrepancyType,
        recentShipments: [],
        recentTransactions: [],
        riskLevel,
        recommendedAction: recommendedAction + '\n\n' + detailedAnalysis.join('\n')
      })
    }

    return results.sort((a, b) => {
      const riskOrder = { high: 3, medium: 2, low: 1 }
      if (riskOrder[a.riskLevel] !== riskOrder[b.riskLevel]) {
        return riskOrder[b.riskLevel] - riskOrder[a.riskLevel]
      }
      return b.currentStock - a.currentStock
    })
  }

  const getRiskBadge = (riskLevel: string) => {
    const config = {
      high: { variant: "destructive" as const, icon: XCircle },
      medium: { variant: "secondary" as const, icon: AlertTriangle },
      low: { variant: "default" as const, icon: CheckCircle }
    }

    const { variant, icon: Icon } = config[riskLevel as keyof typeof config]

    return (
      <Badge variant={variant}>
        <Icon className="mr-1 h-3 w-3" />
        {riskLevel.toUpperCase()}
      </Badge>
    )
  }

  const getDiscrepancyBadge = (type: string, value: number) => {
    if (type === 'none') return <Badge variant="outline">No Issues</Badge>

    const isShortage = type === 'shortage'
    return (
      <Badge variant={isShortage ? "destructive" : "secondary"}>
        {isShortage ? <TrendingDown className="mr-1 h-3 w-3" /> : <TrendingUp className="mr-1 h-3 w-3" />}
        {isShortage ? 'Shortage' : 'Overage'}: {Math.abs(value).toLocaleString()}
      </Badge>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Inventory Discrepancy Analysis
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Compare current inventory with expected levels based on shipments and transactions
            </p>
          </div>
          <Button
            onClick={runAnalysis}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Analyzing...' : 'Run Analysis'}
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {analysis.length > 0 && (
          <>
            {/* Analysis Summary */}
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <div className="font-medium">Analysis Results Summary:</div>
                  <div className="text-sm space-y-1">
                    <div>• <strong>High Risk:</strong> Products needing immediate attention (zero stock, quality issues)</div>
                    <div>• <strong>Medium Risk:</strong> Products to monitor (low stock, high inventory, scattered locations)</div>
                    <div>• <strong>Low Risk:</strong> Products with normal stock levels</div>
                  </div>
                  <div className="text-xs text-muted-foreground mt-2">
                    💡 Click "View Details" in the Recommended Action column for specific guidance on each product.
                  </div>
                </div>
              </AlertDescription>
            </Alert>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Products</p>
                      <p className="text-2xl font-bold">{analysis.length}</p>
                    </div>
                    <Package className="h-8 w-8 text-blue-600 opacity-80" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">High Risk</p>
                      <p className="text-2xl font-bold text-red-600">
                        {analysis.filter(a => a.riskLevel === 'high').length}
                      </p>
                    </div>
                    <XCircle className="h-8 w-8 text-red-600 opacity-80" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Shortages</p>
                      <p className="text-2xl font-bold text-orange-600">
                        {analysis.filter(a => a.discrepancyType === 'shortage').length}
                      </p>
                    </div>
                    <TrendingDown className="h-8 w-8 text-orange-600 opacity-80" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Overages</p>
                      <p className="text-2xl font-bold text-green-600">
                        {analysis.filter(a => a.discrepancyType === 'overage').length}
                      </p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-600 opacity-80" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Analysis Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead className="text-right">Current Stock</TableHead>
                    <TableHead className="text-right">Expected Stock</TableHead>
                    <TableHead>Discrepancy</TableHead>
                    <TableHead>Risk Level</TableHead>
                    <TableHead>Recommended Action</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {analysis.map((item) => (
                    <TableRow key={item.productId}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{item.productName}</div>
                          <div className="text-sm text-muted-foreground font-mono">{item.productSku}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {item.currentStock.toLocaleString()}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {item.expectedStock.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {getDiscrepancyBadge(item.discrepancyType, item.discrepancy)}
                      </TableCell>
                      <TableCell>
                        {getRiskBadge(item.riskLevel)}
                      </TableCell>
                      <TableCell className="max-w-md">
                        <div className="text-sm">
                          <div className="font-medium mb-1">
                            {item.recommendedAction.split('\n\n')[0]}
                          </div>
                          {item.recommendedAction.includes('\n\n') && (
                            <details className="mt-2">
                              <summary className="cursor-pointer text-blue-600 hover:text-blue-800 text-xs">
                                View Details
                              </summary>
                              <div className="mt-2 text-xs text-muted-foreground whitespace-pre-line">
                                {item.recommendedAction.split('\n\n')[1]}
                              </div>
                            </details>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {item.riskLevel === 'high' && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              onClick={() => {
                                // Navigate to inventory adjustment
                                window.location.href = '/inventory'
                              }}
                            >
                              <Settings className="h-4 w-4 mr-1" />
                              Adjust
                            </Button>
                          )}
                          <Button variant="ghost" size="sm" title="View Details">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </>
        )}

        {analysis.length === 0 && !loading && !error && (
          <div className="text-center py-8">
            <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
            <p className="text-muted-foreground">Click "Run Analysis" to check for inventory discrepancies</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
