"use client"

/**
 * Inventory Transaction Forms Component
 * 
 * Professional forms for all transaction types (inbound, outbound, transfer, adjustment)
 * Following established design patterns with comprehensive validation
 */

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import {
  inboundTransactionSchema,
  outboundTransactionSchema,
  transferTransactionSchema,
  adjustmentTransactionSchema,
  reasonCodeEnum
} from "@/lib/validations"
import {
  Package,
  ArrowDown,
  ArrowUp,
  ArrowLeftRight,
  Settings,
  Loader2
} from "lucide-react"

interface TransactionFormsProps {
  products: Array<{
    id: string
    name: string
    sku: string
    unit: string
  }>
  locations: string[]
  onTransactionComplete?: () => void
}

export function TransactionForms({
  products,
  locations,
  onTransactionComplete
}: TransactionFormsProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState("inbound")

  // Inbound form
  const inboundForm = useForm<z.infer<typeof inboundTransactionSchema>>({
    resolver: zodResolver(inboundTransactionSchema),
    defaultValues: {
      transaction_type: "inbound",
      qty: 0,
      to_location: "",
      product_id: "",
      reason_code: "receipt",
      notes: "",
    },
  })

  // Outbound form
  const outboundForm = useForm<z.infer<typeof outboundTransactionSchema>>({
    resolver: zodResolver(outboundTransactionSchema),
    defaultValues: {
      transaction_type: "outbound",
      qty: 0,
      from_location: "",
      product_id: "",
      reason_code: "shipment",
      notes: "",
    },
  })

  // Transfer form
  const transferForm = useForm<z.infer<typeof transferTransactionSchema>>({
    resolver: zodResolver(transferTransactionSchema),
    defaultValues: {
      transaction_type: "transfer",
      qty: 0,
      from_location: "",
      to_location: "",
      product_id: "",
      reason_code: "transfer",
      notes: "",
    },
  })

  // Adjustment form
  const adjustmentForm = useForm<z.infer<typeof adjustmentTransactionSchema>>({
    resolver: zodResolver(adjustmentTransactionSchema),
    defaultValues: {
      transaction_type: "adjustment",
      qty: 0,
      location: "",
      product_id: "",
      reason_code: "cycle_count",
      notes: "",
    },
  })

  const handleSubmit = async (data: any, endpoint: string) => {
    setIsSubmitting(true)
    try {
      const response = await fetch(`/api/inventory/transactions/${endpoint}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Transaction failed")
      }

      const result = await response.json()

      toast({
        title: t("inventory.transaction_success"),
        description: result.message,
      })

      // Reset form and notify parent
      if (endpoint === "inbound") inboundForm.reset()
      if (endpoint === "outbound") outboundForm.reset()
      if (endpoint === "transfer") transferForm.reset()
      if (endpoint === "adjustment") adjustmentForm.reset()

      onTransactionComplete?.()
    } catch (error) {
      toast({
        title: t("inventory.transaction_error"),
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const reasonCodeOptions = [
    { value: "receipt", label: t("inventory.reason_receipt") },
    { value: "shipment", label: t("inventory.reason_shipment") },
    { value: "transfer", label: t("inventory.reason_transfer") },
    { value: "cycle_count", label: t("inventory.reason_cycle_count") },
    { value: "damage", label: t("inventory.reason_damage") },
    { value: "obsolete", label: t("inventory.reason_obsolete") },
    { value: "adjustment", label: t("inventory.reason_adjustment") },
    { value: "return", label: t("inventory.reason_return") },
    { value: "sample", label: t("inventory.reason_sample") },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          {t("inventory.transaction_forms")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="inbound" className="flex items-center gap-2">
              <ArrowDown className="h-4 w-4" />
              {t("inventory.inbound")}
            </TabsTrigger>
            <TabsTrigger value="outbound" className="flex items-center gap-2">
              <ArrowUp className="h-4 w-4" />
              {t("inventory.outbound")}
            </TabsTrigger>
            <TabsTrigger value="transfer" className="flex items-center gap-2">
              <ArrowLeftRight className="h-4 w-4" />
              {t("inventory.transfer")}
            </TabsTrigger>
            <TabsTrigger value="adjustment" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              {t("inventory.adjustment")}
            </TabsTrigger>
          </TabsList>

          {/* Inbound Transaction Form */}
          <TabsContent value="inbound">
            <Form {...inboundForm}>
              <form onSubmit={inboundForm.handleSubmit((data) => handleSubmit(data, "inbound"))} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={inboundForm.control}
                    name="product_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.product")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("inventory.select_product")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {products.map((product) => (
                              <SelectItem key={product.id} value={product.id}>
                                {product.sku} - {product.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={inboundForm.control}
                    name="qty"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.quantity")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0.01"
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={inboundForm.control}
                    name="to_location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.destination_location")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("inventory.select_location")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {locations.map((location) => (
                              <SelectItem key={location} value={location}>
                                {location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={inboundForm.control}
                    name="reason_code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.reason_code")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {reasonCodeOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={inboundForm.control}
                  name="reference"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("inventory.reference")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("inventory.reference_placeholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={inboundForm.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("inventory.notes")}</FormLabel>
                      <FormControl>
                        <Textarea placeholder={t("inventory.notes_placeholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" disabled={isSubmitting} className="w-full">
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {t("inventory.process_inbound")}
                </Button>
              </form>
            </Form>
          </TabsContent>

          {/* Outbound Transaction Form */}
          <TabsContent value="outbound">
            <Form {...outboundForm}>
              <form onSubmit={outboundForm.handleSubmit((data) => handleSubmit(data, "outbound"))} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={outboundForm.control}
                    name="product_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.product")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("inventory.select_product")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {products.map((product) => (
                              <SelectItem key={product.id} value={product.id}>
                                {product.sku} - {product.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={outboundForm.control}
                    name="qty"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.quantity")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0.01"
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={outboundForm.control}
                    name="from_location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.source_location")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("inventory.select_location")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {locations.map((location) => (
                              <SelectItem key={location} value={location}>
                                {location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={outboundForm.control}
                    name="reason_code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.reason_code")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {reasonCodeOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={outboundForm.control}
                  name="reference"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("inventory.reference")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("inventory.reference_placeholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={outboundForm.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("inventory.notes")}</FormLabel>
                      <FormControl>
                        <Textarea placeholder={t("inventory.notes_placeholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" disabled={isSubmitting} className="w-full">
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {t("inventory.process_outbound")}
                </Button>
              </form>
            </Form>
          </TabsContent>

          {/* Transfer Transaction Form */}
          <TabsContent value="transfer">
            <Form {...transferForm}>
              <form onSubmit={transferForm.handleSubmit((data) => handleSubmit(data, "transfer"))} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={transferForm.control}
                    name="product_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.product")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("inventory.select_product")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {products.map((product) => (
                              <SelectItem key={product.id} value={product.id}>
                                {product.sku} - {product.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={transferForm.control}
                    name="qty"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.quantity")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0.01"
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={transferForm.control}
                    name="from_location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.source_location")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("inventory.select_location")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {locations.map((location) => (
                              <SelectItem key={location} value={location}>
                                {location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={transferForm.control}
                    name="to_location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.destination_location")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("inventory.select_location")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {locations.map((location) => (
                              <SelectItem key={location} value={location}>
                                {location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={transferForm.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("inventory.notes")}</FormLabel>
                      <FormControl>
                        <Textarea placeholder={t("inventory.transfer_notes_placeholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" disabled={isSubmitting} className="w-full">
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {t("inventory.process_transfer")}
                </Button>
              </form>
            </Form>
          </TabsContent>

          {/* Adjustment Transaction Form */}
          <TabsContent value="adjustment">
            <Form {...adjustmentForm}>
              <form onSubmit={adjustmentForm.handleSubmit((data) => handleSubmit(data, "adjustment"))} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={adjustmentForm.control}
                    name="product_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.product")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("inventory.select_product")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {products.map((product) => (
                              <SelectItem key={product.id} value={product.id}>
                                {product.sku} - {product.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={adjustmentForm.control}
                    name="qty"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.adjustment_quantity")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={t("inventory.positive_negative_allowed")}
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={adjustmentForm.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.location")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("inventory.select_location")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {locations.map((location) => (
                              <SelectItem key={location} value={location}>
                                {location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={adjustmentForm.control}
                    name="reason_code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("inventory.reason_code")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {reasonCodeOptions.filter(option =>
                              ["cycle_count", "damage", "obsolete", "adjustment"].includes(option.value)
                            ).map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={adjustmentForm.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("inventory.adjustment_notes")}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t("inventory.adjustment_notes_placeholder")}
                          {...field}
                          required
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-sm text-yellow-800">
                    <strong>{t("inventory.adjustment_warning")}:</strong> {t("inventory.adjustment_warning_text")}
                  </p>
                </div>

                <Button type="submit" disabled={isSubmitting} className="w-full">
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {t("inventory.process_adjustment")}
                </Button>
              </form>
            </Form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
