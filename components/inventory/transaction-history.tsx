"use client"

/**
 * Inventory Transaction History Component
 * 
 * Professional table displaying transaction history with filtering and search
 * Following established design patterns with comprehensive functionality
 */

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import {
  History,
  Search,
  Filter,
  ArrowDown,
  ArrowUp,
  ArrowLeftRight,
  Settings,
  Calendar,
  Package,
  MapPin,
  User,
  FileText
} from "lucide-react"
import { format } from "date-fns"

interface Transaction {
  id: string
  transaction_type: string
  product_id: string
  qty: string
  from_location?: string
  to_location?: string
  location?: string
  reason_code?: string
  approval_status: string
  reference?: string
  notes?: string
  created_by?: string
  created_at: string
  product?: {
    id: string
    name: string
    sku: string
    unit: string
  }
}

interface TransactionHistoryProps {
  refreshTrigger?: number
}

export function TransactionHistory({ refreshTrigger }: TransactionHistoryProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [locationFilter, setLocationFilter] = useState("all")

  const fetchTransactions = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (typeFilter !== "all") params.append("transaction_type", typeFilter)
      if (locationFilter !== "all") params.append("location", locationFilter)
      params.append("limit", "100")

      const response = await fetch(`/api/inventory/transactions?${params}`)
      if (!response.ok) throw new Error("Failed to fetch transactions")

      const data = await response.json()
      setTransactions(data)
    } catch (error) {
      toast({
        title: t("inventory.fetch_error"),
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTransactions()
  }, [refreshTrigger, typeFilter, locationFilter])

  // Filter transactions based on search term
  const filteredTransactions = transactions.filter(transaction => {
    if (!searchTerm) return true

    const searchLower = searchTerm.toLowerCase()
    return (
      transaction.product?.name?.toLowerCase().includes(searchLower) ||
      transaction.product?.sku?.toLowerCase().includes(searchLower) ||
      transaction.reference?.toLowerCase().includes(searchLower) ||
      transaction.notes?.toLowerCase().includes(searchLower) ||
      transaction.reason_code?.toLowerCase().includes(searchLower)
    )
  })

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case "inbound": return <ArrowDown className="h-4 w-4" />
      case "outbound": return <ArrowUp className="h-4 w-4" />
      case "transfer": return <ArrowLeftRight className="h-4 w-4" />
      case "adjustment": return <Settings className="h-4 w-4" />
      default: return <Package className="h-4 w-4" />
    }
  }

  const getTransactionTypeBadge = (type: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      inbound: "default",
      outbound: "secondary",
      transfer: "outline",
      adjustment: "destructive"
    }

    return (
      <Badge variant={variants[type] || "outline"} className="flex items-center gap-1">
        {getTransactionTypeIcon(type)}
        {t(`inventory.${type}`)}
      </Badge>
    )
  }

  const getApprovalStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      approved: "default",
      pending: "secondary",
      rejected: "destructive"
    }

    return (
      <Badge variant={variants[status] || "outline"}>
        {t(`inventory.status_${status}`)}
      </Badge>
    )
  }

  const getLocationDisplay = (transaction: Transaction) => {
    if (transaction.transaction_type === "transfer") {
      return `${transaction.from_location} → ${transaction.to_location}`
    }
    return transaction.location || transaction.from_location || transaction.to_location || "-"
  }

  // Get unique locations for filter
  const uniqueLocations = Array.from(new Set(
    transactions.flatMap(t => [
      t.location,
      t.from_location,
      t.to_location
    ]).filter(Boolean)
  ))

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            {t("inventory.transaction_history")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">{t("common.loading")}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          {t("inventory.transaction_history")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t("inventory.search_transactions")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder={t("inventory.filter_by_type")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("inventory.all_types")}</SelectItem>
              <SelectItem value="inbound">{t("inventory.inbound")}</SelectItem>
              <SelectItem value="outbound">{t("inventory.outbound")}</SelectItem>
              <SelectItem value="transfer">{t("inventory.transfer")}</SelectItem>
              <SelectItem value="adjustment">{t("inventory.adjustment")}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={locationFilter} onValueChange={setLocationFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder={t("inventory.filter_by_location")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("inventory.all_locations")}</SelectItem>
              {uniqueLocations.map((location) => (
                <SelectItem key={location} value={location}>
                  {location}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={fetchTransactions}>
            <Filter className="h-4 w-4 mr-2" />
            {t("common.refresh")}
          </Button>
        </div>

        {/* Transaction Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("inventory.date")}</TableHead>
                <TableHead>{t("inventory.type")}</TableHead>
                <TableHead>{t("inventory.product")}</TableHead>
                <TableHead className="text-right">{t("inventory.quantity")}</TableHead>
                <TableHead>{t("inventory.location")}</TableHead>
                <TableHead>{t("inventory.reason")}</TableHead>
                <TableHead>{t("inventory.status")}</TableHead>
                <TableHead>{t("inventory.reference")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTransactions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="text-muted-foreground">
                      <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>{t("inventory.no_transactions")}</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredTransactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          {format(new Date(transaction.created_at), "MMM dd, yyyy HH:mm")}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getTransactionTypeBadge(transaction.transaction_type)}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{transaction.product?.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {transaction.product?.sku}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-mono">
                        {parseFloat(transaction.qty).toLocaleString()} {transaction.product?.unit}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{getLocationDisplay(transaction)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {transaction.reason_code || "-"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {getApprovalStatusBadge(transaction.approval_status)}
                    </TableCell>
                    <TableCell>
                      <div className="max-w-32 truncate text-sm text-muted-foreground">
                        {transaction.reference || transaction.notes || "-"}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Summary */}
        <div className="mt-4 text-sm text-muted-foreground">
          {t("inventory.showing_transactions", {
            count: filteredTransactions.length,
            total: transactions.length
          })}
        </div>
      </CardContent>
    </Card>
  )
}
