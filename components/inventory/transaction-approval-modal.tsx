"use client"

/**
 * Transaction Approval Modal Component
 * 
 * Professional modal for high-value transaction approvals
 * Following established design patterns with comprehensive validation
 */

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import {
  AlertTriangle,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  Package,
  MapPin,
  DollarSign,
  FileText,
  Loader2
} from "lucide-react"
import { format } from "date-fns"

interface PendingTransaction {
  id: string
  transaction_type: string
  product_id: string
  qty: string
  from_location?: string
  to_location?: string
  location?: string
  reason_code?: string
  reference?: string
  notes?: string
  created_by?: string
  created_at: string
  estimated_value?: number
  product?: {
    id: string
    name: string
    sku: string
    unit: string
  }
}

interface TransactionApprovalModalProps {
  isOpen: boolean
  onClose: () => void
  transaction: PendingTransaction | null
  onApprove: (transactionId: string, approvalNotes?: string) => Promise<void>
  onReject: (transactionId: string, rejectionReason: string) => Promise<void>
}

export function TransactionApprovalModal({
  isOpen,
  onClose,
  transaction,
  onApprove,
  onReject
}: TransactionApprovalModalProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [isProcessing, setIsProcessing] = useState(false)
  const [approvalNotes, setApprovalNotes] = useState("")
  const [rejectionReason, setRejectionReason] = useState("")
  const [action, setAction] = useState<"approve" | "reject" | null>(null)

  if (!transaction) return null

  const handleApprove = async () => {
    if (!transaction) return

    setIsProcessing(true)
    setAction("approve")

    try {
      await onApprove(transaction.id, approvalNotes)
      toast({
        title: t("inventory.approval_success"),
        description: t("inventory.transaction_approved"),
      })
      onClose()
      setApprovalNotes("")
    } catch (error) {
      toast({
        title: t("inventory.approval_error"),
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
      setAction(null)
    }
  }

  const handleReject = async () => {
    if (!transaction || !rejectionReason.trim()) {
      toast({
        title: t("inventory.validation_error"),
        description: t("inventory.rejection_reason_required"),
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    setAction("reject")

    try {
      await onReject(transaction.id, rejectionReason)
      toast({
        title: t("inventory.rejection_success"),
        description: t("inventory.transaction_rejected"),
      })
      onClose()
      setRejectionReason("")
    } catch (error) {
      toast({
        title: t("inventory.rejection_error"),
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
      setAction(null)
    }
  }

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case "inbound": return <Package className="h-4 w-4 text-green-600" />
      case "outbound": return <Package className="h-4 w-4 text-blue-600" />
      case "transfer": return <MapPin className="h-4 w-4 text-purple-600" />
      case "adjustment": return <AlertTriangle className="h-4 w-4 text-orange-600" />
      default: return <Package className="h-4 w-4" />
    }
  }

  const getLocationDisplay = () => {
    if (transaction.transaction_type === "transfer") {
      return `${transaction.from_location} → ${transaction.to_location}`
    }
    return transaction.location || transaction.from_location || transaction.to_location || "-"
  }

  const isHighValue = transaction.estimated_value && transaction.estimated_value > 10000

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            {t("inventory.transaction_approval_required")}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* High Value Warning */}
          {isHighValue && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-orange-600" />
                <span className="font-medium text-orange-800">
                  {t("inventory.high_value_transaction")}
                </span>
              </div>
              <p className="text-sm text-orange-700 mt-1">
                {t("inventory.high_value_warning")}
              </p>
            </div>
          )}

          {/* Transaction Details */}
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Label className="text-sm font-medium">{t("inventory.type")}:</Label>
                    <div className="flex items-center gap-2">
                      {getTransactionTypeIcon(transaction.transaction_type)}
                      <Badge variant="outline">
                        {t(`inventory.${transaction.transaction_type}`)}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Label className="text-sm font-medium">{t("inventory.product")}:</Label>
                    <div>
                      <div className="font-medium">{transaction.product?.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {transaction.product?.sku}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Label className="text-sm font-medium">{t("inventory.quantity")}:</Label>
                    <span className="font-mono">
                      {parseFloat(transaction.qty).toLocaleString()} {transaction.product?.unit}
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <Label className="text-sm font-medium">{t("inventory.location")}:</Label>
                    <span>{getLocationDisplay()}</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <Label className="text-sm font-medium">{t("inventory.date")}:</Label>
                    <span className="text-sm">
                      {format(new Date(transaction.created_at), "MMM dd, yyyy HH:mm")}
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <Label className="text-sm font-medium">{t("inventory.created_by")}:</Label>
                    <span className="text-sm">{transaction.created_by || "System"}</span>
                  </div>

                  {transaction.reason_code && (
                    <div className="flex items-center gap-2">
                      <Label className="text-sm font-medium">{t("inventory.reason")}:</Label>
                      <Badge variant="outline" className="text-xs">
                        {transaction.reason_code}
                      </Badge>
                    </div>
                  )}

                  {transaction.estimated_value && (
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <Label className="text-sm font-medium">{t("inventory.estimated_value")}:</Label>
                      <span className="font-medium">
                        ${transaction.estimated_value.toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {(transaction.reference || transaction.notes) && (
                <div className="mt-4 pt-4 border-t">
                  {transaction.reference && (
                    <div className="mb-2">
                      <Label className="text-sm font-medium">{t("inventory.reference")}:</Label>
                      <p className="text-sm text-muted-foreground">{transaction.reference}</p>
                    </div>
                  )}
                  {transaction.notes && (
                    <div>
                      <Label className="text-sm font-medium">{t("inventory.notes")}:</Label>
                      <p className="text-sm text-muted-foreground">{transaction.notes}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Approval Notes */}
          <div className="space-y-2">
            <Label htmlFor="approval-notes">{t("inventory.approval_notes")}</Label>
            <Textarea
              id="approval-notes"
              placeholder={t("inventory.approval_notes_placeholder")}
              value={approvalNotes}
              onChange={(e) => setApprovalNotes(e.target.value)}
              rows={3}
            />
          </div>

          {/* Rejection Reason (shown when rejecting) */}
          {action === "reject" && (
            <div className="space-y-2">
              <Label htmlFor="rejection-reason" className="text-red-700">
                {t("inventory.rejection_reason")} *
              </Label>
              <Textarea
                id="rejection-reason"
                placeholder={t("inventory.rejection_reason_placeholder")}
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                rows={3}
                className="border-red-200 focus:border-red-500"
                required
              />
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose} disabled={isProcessing}>
            {t("common.cancel")}
          </Button>

          <Button
            variant="destructive"
            onClick={() => {
              if (action === "reject") {
                handleReject()
              } else {
                setAction("reject")
              }
            }}
            disabled={isProcessing}
          >
            {isProcessing && action === "reject" && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            <XCircle className="mr-2 h-4 w-4" />
            {action === "reject" ? t("inventory.confirm_reject") : t("inventory.reject")}
          </Button>

          <Button
            onClick={handleApprove}
            disabled={isProcessing}
            className="bg-green-600 hover:bg-green-700"
          >
            {isProcessing && action === "approve" && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            <CheckCircle className="mr-2 h-4 w-4" />
            {t("inventory.approve")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
