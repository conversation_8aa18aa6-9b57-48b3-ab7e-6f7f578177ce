"use client"

import React from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { 
  Package, 
  Package2, 
  Warehouse, 
  Factory, 
  Truck,
  MapPin,
  Building,
  Home
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export type LocationCategory = 
  | "finished_goods" 
  | "raw_materials" 
  | "work_in_progress" 
  | "shipping" 
  | "quality" 
  | "general"
  | "unknown"

export interface LocationInfo {
  id: string
  name: string
  category: LocationCategory
  capacity?: number
  utilization?: number
  status?: "active" | "inactive" | "maintenance"
}

interface LocationBadgeProps {
  location: string | LocationInfo
  showCategory?: boolean
  showIcon?: boolean
  showTooltip?: boolean
  variant?: "default" | "outline" | "secondary"
  size?: "sm" | "default" | "lg"
  className?: string
}

// ============================================================================
// LOCATION CATEGORIZATION LOGIC
// ============================================================================

const LOCATION_PATTERNS = {
  finished_goods: [
    /^fg[_\s-]/i,
    /finished[_\s-]goods/i,
    /main[_\s-]warehouse/i,
    /secondary[_\s-]warehouse/i,
    /warehouse[_\s-][a-z]/i,
    /^wh[_\s-]/i,
    /ready[_\s-]to[_\s-]ship/i,
    /packaging/i,
    /dispatch/i
  ],
  raw_materials: [
    /^rm[_\s-]/i,
    /raw[_\s-]material/i,
    /material[_\s-]storage/i,
    /building[_\s-][a-z]/i,
    /outdoor[_\s-]yard/i,
    /supplier[_\s-]dock/i,
    /receiving/i,
    /incoming/i
  ],
  work_in_progress: [
    /^wip[_\s-]/i,
    /work[_\s-]in[_\s-]progress/i,
    /production[_\s-]floor/i,
    /assembly/i,
    /manufacturing/i,
    /cutting/i,
    /sewing/i,
    /processing/i
  ],
  shipping: [
    /shipping[_\s-]dock/i,
    /loading[_\s-]bay/i,
    /export[_\s-]area/i,
    /container[_\s-]yard/i,
    /dispatch[_\s-]area/i,
    /outbound/i
  ],
  quality: [
    /^qa[_\s-]/i,
    /quality[_\s-]control/i,
    /inspection[_\s-]area/i,
    /testing[_\s-]lab/i,
    /quarantine/i,
    /hold[_\s-]area/i
  ]
}

function categorizeLocation(locationName: string): LocationCategory {
  const name = locationName.toLowerCase().trim()
  
  for (const [category, patterns] of Object.entries(LOCATION_PATTERNS)) {
    if (patterns.some(pattern => pattern.test(name))) {
      return category as LocationCategory
    }
  }
  
  return "unknown"
}

// ============================================================================
// CONFIGURATION
// ============================================================================

const CATEGORY_CONFIG = {
  finished_goods: {
    label: "Finished Goods",
    shortLabel: "FG",
    icon: Package,
    color: "bg-blue-100 text-blue-800 border-blue-200",
    outlineColor: "border-blue-300 text-blue-700",
    secondaryColor: "bg-blue-50 text-blue-600",
    description: "Finished goods warehouse - products ready for shipment"
  },
  raw_materials: {
    label: "Raw Materials",
    shortLabel: "RM",
    icon: Package2,
    color: "bg-green-100 text-green-800 border-green-200",
    outlineColor: "border-green-300 text-green-700",
    secondaryColor: "bg-green-50 text-green-600",
    description: "Raw materials storage - incoming materials and supplies"
  },
  work_in_progress: {
    label: "Work in Progress",
    shortLabel: "WIP",
    icon: Factory,
    color: "bg-orange-100 text-orange-800 border-orange-200",
    outlineColor: "border-orange-300 text-orange-700",
    secondaryColor: "bg-orange-50 text-orange-600",
    description: "Work in progress area - items currently being manufactured"
  },
  shipping: {
    label: "Shipping",
    shortLabel: "SHIP",
    icon: Truck,
    color: "bg-purple-100 text-purple-800 border-purple-200",
    outlineColor: "border-purple-300 text-purple-700",
    secondaryColor: "bg-purple-50 text-purple-600",
    description: "Shipping area - items ready for dispatch"
  },
  quality: {
    label: "Quality Control",
    shortLabel: "QC",
    icon: Building,
    color: "bg-yellow-100 text-yellow-800 border-yellow-200",
    outlineColor: "border-yellow-300 text-yellow-700",
    secondaryColor: "bg-yellow-50 text-yellow-600",
    description: "Quality control area - items under inspection"
  },
  general: {
    label: "General",
    shortLabel: "GEN",
    icon: Warehouse,
    color: "bg-gray-100 text-gray-800 border-gray-200",
    outlineColor: "border-gray-300 text-gray-700",
    secondaryColor: "bg-gray-50 text-gray-600",
    description: "General storage area"
  },
  unknown: {
    label: "Unknown",
    shortLabel: "?",
    icon: MapPin,
    color: "bg-gray-100 text-gray-600 border-gray-200",
    outlineColor: "border-gray-300 text-gray-500",
    secondaryColor: "bg-gray-50 text-gray-500",
    description: "Location category not determined"
  }
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function LocationBadge({
  location,
  showCategory = true,
  showIcon = true,
  showTooltip = true,
  variant = "default",
  size = "default",
  className = ""
}: LocationBadgeProps) {
  // Extract location info
  const locationInfo: LocationInfo = typeof location === "string" 
    ? {
        id: location,
        name: location,
        category: categorizeLocation(location)
      }
    : location

  const config = CATEGORY_CONFIG[locationInfo.category]
  const Icon = config.icon

  // Get appropriate styling based on variant
  const getVariantClass = () => {
    switch (variant) {
      case "outline":
        return config.outlineColor
      case "secondary":
        return config.secondaryColor
      default:
        return config.color
    }
  }

  // Get size classes
  const getSizeClass = () => {
    switch (size) {
      case "sm":
        return "text-xs px-2 py-0.5"
      case "lg":
        return "text-sm px-3 py-1"
      default:
        return "text-xs px-2.5 py-0.5"
    }
  }

  const badgeContent = (
    <Badge 
      variant={variant}
      className={`
        ${getVariantClass()} 
        ${getSizeClass()}
        font-medium inline-flex items-center gap-1.5
        ${className}
      `}
    >
      {showIcon && <Icon className="h-3 w-3" />}
      <span className="truncate">
        {showCategory ? config.shortLabel : locationInfo.name}
      </span>
      {locationInfo.utilization && (
        <span className="text-xs opacity-75">
          {Math.round(locationInfo.utilization)}%
        </span>
      )}
    </Badge>
  )

  if (!showTooltip) {
    return badgeContent
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {badgeContent}
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1">
            <div className="font-medium">{locationInfo.name}</div>
            <div className="text-xs text-muted-foreground">
              {config.description}
            </div>
            {locationInfo.capacity && (
              <div className="text-xs">
                Capacity: {locationInfo.capacity.toLocaleString()} units
              </div>
            )}
            {locationInfo.utilization && (
              <div className="text-xs">
                Utilization: {Math.round(locationInfo.utilization)}%
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

// ============================================================================
// UTILITY COMPONENTS
// ============================================================================

export function LocationCategoryLegend() {
  const categories = Object.entries(CATEGORY_CONFIG).filter(
    ([key]) => key !== "unknown"
  )

  return (
    <div className="flex flex-wrap gap-2">
      {categories.map(([key, config]) => {
        const Icon = config.icon
        return (
          <div key={key} className="flex items-center gap-1.5 text-sm">
            <div className={`p-1 rounded ${config.color}`}>
              <Icon className="h-3 w-3" />
            </div>
            <span className="text-gray-700">{config.label}</span>
          </div>
        )
      })}
    </div>
  )
}

export function LocationCategoryFilter({
  selectedCategories,
  onCategoryChange,
  className = ""
}: {
  selectedCategories: LocationCategory[]
  onCategoryChange: (categories: LocationCategory[]) => void
  className?: string
}) {
  const categories = Object.entries(CATEGORY_CONFIG).filter(
    ([key]) => key !== "unknown"
  )

  const toggleCategory = (category: LocationCategory) => {
    if (selectedCategories.includes(category)) {
      onCategoryChange(selectedCategories.filter(c => c !== category))
    } else {
      onCategoryChange([...selectedCategories, category])
    }
  }

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {categories.map(([key, config]) => {
        const category = key as LocationCategory
        const isSelected = selectedCategories.includes(category)
        const Icon = config.icon

        return (
          <button
            key={key}
            onClick={() => toggleCategory(category)}
            className={`
              inline-flex items-center gap-1.5 px-3 py-1.5 rounded-md text-sm font-medium
              transition-colors border
              ${isSelected 
                ? config.color + " shadow-sm"
                : "bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
              }
            `}
          >
            <Icon className="h-4 w-4" />
            {config.label}
          </button>
        )
      })}
    </div>
  )
}

// ============================================================================
// EXPORT UTILITIES
// ============================================================================

export { categorizeLocation, CATEGORY_CONFIG }
export type { LocationCategory, LocationInfo }
