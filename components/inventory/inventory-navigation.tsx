"use client"

import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useI18n } from "@/components/i18n-provider"
import {
  Package,
  Package2,
  BarChart3,
  Warehouse,
  TrendingUp,
  AlertTriangle,
  ArrowRight,
  Factory,
  Layers
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface NavigationItem {
  id: string
  title: string
  description: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  count?: string
  status?: "active" | "warning" | "success" | "default"
  badge?: string
}

interface InventoryNavigationProps {
  className?: string
  showCounts?: boolean
  variant?: "cards" | "list"
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function InventoryNavigation({ 
  className = "",
  showCounts = true,
  variant = "cards"
}: InventoryNavigationProps) {
  const { t } = useI18n()
  const pathname = usePathname()

  // ✅ NAVIGATION ITEMS CONFIGURATION
  const navigationItems: NavigationItem[] = [
    {
      id: "finished-goods",
      title: t("inventory.finishedGoods"),
      description: "Manage finished products ready for shipment",
      href: "/inventory",
      icon: Package,
      count: "12,976 units",
      status: "success",
      badge: "Ready to Ship"
    },
    {
      id: "raw-materials",
      title: t("inventory.rawMaterials"),
      description: "Manage raw materials and supplier inventory",
      href: "/raw-materials",
      icon: Package2,
      count: "397.50 meters",
      status: "active",
      badge: "Available"
    },
    {
      id: "locations",
      title: "Warehouse Locations",
      description: "Manage storage locations and capacity",
      href: "/locations",
      icon: Warehouse,
      count: "8 locations",
      status: "default"
    },
    {
      id: "analytics",
      title: "Combined Analytics",
      description: "Cross-category inventory insights and reports",
      href: "/inventory?tab=analytics",
      icon: BarChart3,
      count: "Live Data",
      status: "default"
    }
  ]

  // ✅ HELPER FUNCTIONS
  const isActive = (href: string) => {
    if (href === "/inventory") {
      return pathname === "/inventory" && !pathname.includes("tab=analytics")
    }
    return pathname === href || pathname.startsWith(href + "/")
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success": return "text-green-600 bg-green-50 border-green-200"
      case "warning": return "text-orange-600 bg-orange-50 border-orange-200"
      case "active": return "text-blue-600 bg-blue-50 border-blue-200"
      default: return "text-gray-600 bg-gray-50 border-gray-200"
    }
  }

  const getBadgeVariant = (status: string) => {
    switch (status) {
      case "success": return "default"
      case "warning": return "destructive"
      case "active": return "secondary"
      default: return "outline"
    }
  }

  // ✅ CARD VARIANT RENDER
  if (variant === "cards") {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {navigationItems.map((item) => {
          const Icon = item.icon
          const active = isActive(item.href)
          
          return (
            <Card 
              key={item.id} 
              className={`transition-all duration-200 hover:shadow-md ${
                active 
                  ? "ring-2 ring-blue-500 border-blue-200 bg-blue-50/50" 
                  : "hover:border-gray-300"
              }`}
            >
              <CardContent className="p-4">
                <Link href={item.href} className="block">
                  <div className="flex items-start justify-between mb-3">
                    <div className={`p-2 rounded-lg ${getStatusColor(item.status || "default")}`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    {active && (
                      <Badge variant="default" className="text-xs">
                        Current
                      </Badge>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="font-semibold text-gray-900 group-hover:text-blue-600">
                      {item.title}
                    </h3>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {item.description}
                    </p>
                    
                    {showCounts && item.count && (
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-gray-900">
                          {item.count}
                        </span>
                        {item.badge && (
                          <Badge variant={getBadgeVariant(item.status || "default")} className="text-xs">
                            {item.badge}
                          </Badge>
                        )}
                      </div>
                    )}
                    
                    <div className="flex items-center text-blue-600 text-sm font-medium pt-1">
                      View Details
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </div>
                  </div>
                </Link>
              </CardContent>
            </Card>
          )
        })}
      </div>
    )
  }

  // ✅ LIST VARIANT RENDER
  return (
    <div className={`space-y-2 ${className}`}>
      {navigationItems.map((item) => {
        const Icon = item.icon
        const active = isActive(item.href)
        
        return (
          <Card 
            key={item.id}
            className={`transition-all duration-200 ${
              active 
                ? "ring-2 ring-blue-500 border-blue-200 bg-blue-50/50" 
                : "hover:border-gray-300 hover:shadow-sm"
            }`}
          >
            <CardContent className="p-3">
              <Link href={item.href} className="block">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${getStatusColor(item.status || "default")}`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {item.title}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {item.description}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    {showCounts && item.count && (
                      <div className="text-right">
                        <div className="font-semibold text-gray-900">
                          {item.count}
                        </div>
                        {item.badge && (
                          <Badge variant={getBadgeVariant(item.status || "default")} className="text-xs">
                            {item.badge}
                          </Badge>
                        )}
                      </div>
                    )}
                    
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </Link>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}

// ============================================================================
// QUICK NAVIGATION COMPONENT
// ============================================================================

export function QuickInventoryNavigation() {
  const { t } = useI18n()
  
  return (
    <div className="flex items-center gap-2 flex-wrap">
      <Button variant="outline" size="sm" asChild>
        <Link href="/inventory" className="flex items-center gap-2">
          <Package className="h-4 w-4" />
          {t("inventory.finishedGoods")}
        </Link>
      </Button>
      
      <Button variant="outline" size="sm" asChild>
        <Link href="/raw-materials" className="flex items-center gap-2">
          <Package2 className="h-4 w-4" />
          {t("inventory.rawMaterials")}
        </Link>
      </Button>
      
      <Button variant="outline" size="sm" asChild>
        <Link href="/locations" className="flex items-center gap-2">
          <Warehouse className="h-4 w-4" />
          Locations
        </Link>
      </Button>
      
      <Button variant="outline" size="sm" asChild>
        <Link href="/inventory?tab=analytics" className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4" />
          Analytics
        </Link>
      </Button>
    </div>
  )
}

// ============================================================================
// INVENTORY CATEGORY SELECTOR
// ============================================================================

export function InventoryCategorySelector() {
  const pathname = usePathname()
  
  const categories = [
    {
      id: "finished-goods",
      label: "Finished Goods",
      href: "/inventory",
      icon: Package,
      active: pathname === "/inventory"
    },
    {
      id: "raw-materials", 
      label: "Raw Materials",
      href: "/raw-materials",
      icon: Package2,
      active: pathname.startsWith("/raw-materials")
    },
    {
      id: "locations",
      label: "Locations",
      href: "/locations", 
      icon: Warehouse,
      active: pathname.startsWith("/locations")
    }
  ]
  
  return (
    <div className="flex items-center gap-1 p-1 bg-gray-100 rounded-lg">
      {categories.map((category) => {
        const Icon = category.icon
        
        return (
          <Button
            key={category.id}
            variant={category.active ? "default" : "ghost"}
            size="sm"
            asChild
            className={category.active ? "shadow-sm" : ""}
          >
            <Link href={category.href} className="flex items-center gap-2">
              <Icon className="h-4 w-4" />
              {category.label}
            </Link>
          </Button>
        )
      })}
    </div>
  )
}
