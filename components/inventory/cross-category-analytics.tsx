"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from "recharts"
import {
  Package,
  Factory,
  TrendingUp,
  ArrowRight,
  Warehouse,
  DollarSign
} from "lucide-react"
import { useI18n } from "@/components/i18n-provider"

interface UnifiedInventoryData {
  summary: {
    finishedGoods: {
      totalUnits: number
      totalValue: number
      totalLots: number
      totalProducts: number
      locations: string[]
    }
    rawMaterials: {
      totalUnits: number
      totalValue: number
      totalLots: number
      totalMaterials: number
      locations: string[]
    }
    combined: {
      totalValue: number
      totalLocations: number
      valueDistribution: {
        finishedGoodsPercentage: number
        rawMaterialsPercentage: number
      }
    }
  }
  analytics: {
    locationUtilization: Array<{
      location: string
      category: 'finished_goods' | 'raw_materials'
      lotCount: number
      totalValue: number
      utilization: string
    }>
    valueAnalysis: {
      averageFinishedGoodsValue: number
      averageRawMaterialsValue: number
      highestValueLot: {
        type: 'finished_goods' | 'raw_materials'
        lotNumber: string
        value: number
      }
    }
  }
}

export function CrossCategoryAnalytics() {
  const { t } = useI18n()
  const [data, setData] = useState<UnifiedInventoryData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('/api/inventory/unified')
        if (response.ok) {
          const result = await response.json()
          setData(result)
        }
      } catch (error) {
        console.error('Error fetching cross-category analytics:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-32 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!data) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground text-center">
            Unable to load cross-category analytics data
          </p>
        </CardContent>
      </Card>
    )
  }

  // Prepare chart data
  const valueDistributionData = [
    {
      name: 'Finished Goods',
      value: data.summary.finishedGoods.totalValue,
      percentage: data.summary.combined.valueDistribution.finishedGoodsPercentage,
      color: '#3b82f6'
    },
    {
      name: 'Raw Materials',
      value: data.summary.rawMaterials.totalValue,
      percentage: data.summary.combined.valueDistribution.rawMaterialsPercentage,
      color: '#10b981'
    }
  ]

  const locationComparisonData = data.analytics.locationUtilization.map(loc => ({
    location: loc.location.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    value: loc.totalValue,
    lots: loc.lotCount,
    category: loc.category === 'finished_goods' ? 'Finished Goods' : 'Raw Materials',
    utilization: loc.utilization
  }))

  const conversionRatio = data.summary.rawMaterials.totalUnits > 0
    ? (data.summary.finishedGoods.totalUnits / data.summary.rawMaterials.totalUnits).toFixed(2)
    : '0'

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Cross-Category Analytics</h2>
          <p className="text-muted-foreground">
            Material flow and conversion insights across inventory categories
          </p>
        </div>
        <Badge variant="outline" className="px-3 py-1">
          <TrendingUp className="w-4 h-4 mr-1" />
          Live Data
        </Badge>
      </div>

      {/* Key Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Factory className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Conversion Ratio</p>
                <p className="text-2xl font-bold">{conversionRatio}:1</p>
                <p className="text-xs text-muted-foreground">FG:RM Units</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">Value Efficiency</p>
                <p className="text-2xl font-bold">
                  {data.summary.combined.valueDistribution.finishedGoodsPercentage}%
                </p>
                <p className="text-xs text-muted-foreground">FG Value Share</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Warehouse className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Location Spread</p>
                <p className="text-2xl font-bold">{data.summary.combined.totalLocations}</p>
                <p className="text-xs text-muted-foreground">Active Locations</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Package className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Avg Lot Value</p>
                <p className="text-2xl font-bold">
                  ${Math.round((data.summary.finishedGoods.totalValue + data.summary.rawMaterials.totalValue) /
                    (data.summary.finishedGoods.totalLots + data.summary.rawMaterials.totalLots)).toLocaleString()}
                </p>
                <p className="text-xs text-muted-foreground">Combined Average</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Value Distribution Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Inventory Value Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={valueDistributionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${name}: ${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {valueDistributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => [`$${value.toLocaleString()}`, 'Value']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Location Comparison Bar Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Warehouse className="w-5 h-5" />
              Location Value Comparison
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={locationComparisonData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="location"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    name === 'value' ? `$${value.toLocaleString()}` : value,
                    name === 'value' ? 'Value' : 'Lots'
                  ]}
                />
                <Bar dataKey="value" fill="#3b82f6" name="value" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Material Flow Visualization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Factory className="w-5 h-5" />
            Material Flow Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-2">
                <Package className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="font-semibold">Raw Materials</h3>
              <p className="text-2xl font-bold text-green-600">
                {data.summary.rawMaterials.totalUnits.toLocaleString()}
              </p>
              <p className="text-sm text-muted-foreground">Units Available</p>
              <Badge variant="outline" className="mt-2">
                ${data.summary.rawMaterials.totalValue.toLocaleString()}
              </Badge>
            </div>

            <div className="flex items-center space-x-4">
              <ArrowRight className="w-8 h-8 text-gray-400" />
              <div className="text-center">
                <Factory className="w-12 h-12 text-blue-600 mx-auto mb-2" />
                <p className="text-sm font-medium">Production</p>
                <p className="text-xs text-muted-foreground">Conversion Process</p>
              </div>
              <ArrowRight className="w-8 h-8 text-gray-400" />
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-2">
                <Package className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-semibold">Finished Goods</h3>
              <p className="text-2xl font-bold text-blue-600">
                {data.summary.finishedGoods.totalUnits.toLocaleString()}
              </p>
              <p className="text-sm text-muted-foreground">Units Ready</p>
              <Badge variant="outline" className="mt-2">
                ${data.summary.finishedGoods.totalValue.toLocaleString()}
              </Badge>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Material Efficiency</p>
              <p className="text-lg font-bold">
                {((data.summary.finishedGoods.totalValue / data.summary.rawMaterials.totalValue) * 100).toFixed(1)}%
              </p>
              <p className="text-xs text-muted-foreground">Value Multiplication</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Production Scale</p>
              <p className="text-lg font-bold">{conversionRatio}x</p>
              <p className="text-xs text-muted-foreground">Unit Conversion</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Highest Value Lot</p>
              <p className="text-lg font-bold">
                ${data.analytics.valueAnalysis.highestValueLot.value.toLocaleString()}
              </p>
              <p className="text-xs text-muted-foreground">
                {data.analytics.valueAnalysis.highestValueLot.type.replace('_', ' ')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
