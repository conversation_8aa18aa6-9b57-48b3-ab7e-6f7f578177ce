/**
 * Financial Impact Card Component
 * 
 * Displays real-time financial impact in operational views.
 * Integrates seamlessly with existing UI without breaking changes.
 */

"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Package,
  Ship,
  AlertCircle,
  RefreshCw,
  Eye,
  EyeOff
} from "lucide-react"
import { safeJson } from "@/lib/safe-fetch"

export interface FinancialImpactData {
  revenue: number
  cogs: number
  grossProfit: number
  profitMargin: number
  currency: string
  invoiceGenerated?: boolean
  profitPerUnit?: number
  utilizationRate?: number
}

interface FinancialImpactCardProps {
  title: string
  type: 'shipment' | 'inventory' | 'container'
  entityId: string
  className?: string
  compact?: boolean
  collapsible?: boolean
}

export function FinancialImpactCard({
  title,
  type,
  entityId,
  className = "",
  compact = false,
  collapsible = false
}: FinancialImpactCardProps) {
  const [data, setData] = useState<FinancialImpactData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [collapsed, setCollapsed] = useState(collapsible)

  const loadFinancialImpact = async () => {
    try {
      setLoading(true)
      setError(null)

      let endpoint = ''
      switch (type) {
        case 'shipment':
          endpoint = `/api/operational/financial-impact/shipment/${entityId}`
          break
        case 'inventory':
          endpoint = `/api/operational/financial-impact/inventory/${entityId}`
          break
        case 'container':
          endpoint = `/api/operational/financial-impact/container/${entityId}`
          break
      }

      const response = await safeJson(endpoint, {})

      // Validate and normalize the response data
      if (response && typeof response === 'object') {
        const normalizedData = {
          revenue: Number(response.revenue) || 0,
          cogs: Number(response.cogs) || 0,
          grossProfit: Number(response.grossProfit) || 0,
          profitMargin: Number(response.profitMargin) || 0,
          currency: response.currency || 'USD',
          invoiceGenerated: Boolean(response.invoiceGenerated),
          profitPerUnit: Number(response.profitPerUnit) || 0,
          utilizationRate: Number(response.utilizationRate) || 0
        }
        setData(normalizedData)
      } else {
        throw new Error("Invalid response format")
      }
    } catch (error) {
      console.error(`Failed to load financial impact for ${type}:`, error)
      setError("Failed to load financial data")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (entityId) {
      loadFinancialImpact()
    }
  }, [entityId, type])

  if (loading) {
    return (
      <Card className={`${className} border-blue-200 bg-blue-50/50`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
            <span className="text-sm text-blue-600">Loading financial impact...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={`${className} border-red-200 bg-red-50/50`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-600">{error}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={loadFinancialImpact}
              className="text-red-600 hover:text-red-700"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data) return null

  const isProfit = data.grossProfit > 0
  const profitColor = isProfit ? "text-green-600" : "text-red-600"
  const profitIcon = isProfit ? TrendingUp : TrendingDown

  if (compact) {
    return (
      <div className={`${className} flex items-center gap-4 p-3 bg-blue-50/50 rounded-lg border border-blue-200`}>
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium">Revenue: ${data.revenue.toLocaleString()}</span>
        </div>
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-orange-600" />
          <span className="text-sm font-medium">COGS: ${data.cogs.toLocaleString()}</span>
        </div>
        <div className={`flex items-center gap-2 ${profitColor}`}>
          {React.createElement(profitIcon, { className: "h-4 w-4" })}
          <span className="text-sm font-medium">
            Profit: ${data.grossProfit.toLocaleString()} ({data.profitMargin.toFixed(1)}%)
          </span>
        </div>
        {data.invoiceGenerated && (
          <Badge variant="secondary" className="text-xs">
            Invoice Generated
          </Badge>
        )}
      </div>
    )
  }

  return (
    <Card className={`${className} border-blue-200 bg-blue-50/50`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-blue-600" />
            {title}
          </CardTitle>
          {collapsible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCollapsed(!collapsed)}
              className="h-6 w-6 p-0"
            >
              {collapsed ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
            </Button>
          )}
        </div>
      </CardHeader>

      {!collapsed && (
        <CardContent className="pt-0">
          <div className="grid grid-cols-2 gap-4">
            {/* Revenue */}
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3 text-green-600" />
                <span className="text-xs text-muted-foreground">Revenue</span>
              </div>
              <div className="text-lg font-bold text-green-600">
                ${data.revenue.toLocaleString()}
              </div>
            </div>

            {/* COGS */}
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <Package className="h-3 w-3 text-orange-600" />
                <span className="text-xs text-muted-foreground">COGS</span>
              </div>
              <div className="text-lg font-bold text-orange-600">
                ${data.cogs.toLocaleString()}
              </div>
            </div>

            {/* Gross Profit */}
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                {React.createElement(profitIcon, { className: `h-3 w-3 ${profitColor}` })}
                <span className="text-xs text-muted-foreground">Gross Profit</span>
              </div>
              <div className={`text-lg font-bold ${profitColor}`}>
                ${data.grossProfit.toLocaleString()}
              </div>
            </div>

            {/* Profit Margin */}
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <span className="text-xs text-muted-foreground">Margin</span>
              </div>
              <div className={`text-lg font-bold ${profitColor}`}>
                {data.profitMargin.toFixed(1)}%
              </div>
            </div>
          </div>

          {/* Additional Metrics */}
          <div className="mt-4 pt-3 border-t border-blue-200">
            <div className="flex items-center justify-between text-xs">
              {data.profitPerUnit !== undefined && (
                <span className="text-muted-foreground">
                  Profit/Unit: <span className={`font-medium ${profitColor}`}>
                    ${data.profitPerUnit.toFixed(2)}
                  </span>
                </span>
              )}

              {data.utilizationRate !== undefined && (
                <span className="text-muted-foreground">
                  Container: <span className="font-medium">
                    {data.utilizationRate.toFixed(1)}%
                  </span>
                </span>
              )}

              {data.invoiceGenerated && (
                <Badge variant="secondary" className="text-xs">
                  <Ship className="mr-1 h-3 w-3" />
                  Invoice Generated
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
