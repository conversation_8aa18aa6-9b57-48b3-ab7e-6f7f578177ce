/**
 * Compact Financial Impact Component
 * 
 * Displays financial impact data in a compact table format.
 * Perfect for showing profit margins at a glance in data tables.
 */

"use client"

import React, { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, TrendingDown, DollarSign, AlertCircle } from "lucide-react"

export interface CompactFinancialData {
  revenue: number
  cogs: number
  grossProfit: number
  profitMargin: number
  currency: string
  profitPerUnit?: number
  utilizationRate?: number
}

interface CompactFinancialImpactProps {
  shipmentId: string
  className?: string
}

export function CompactFinancialImpact({
  shipmentId,
  className = ""
}: CompactFinancialImpactProps) {
  const [data, setData] = useState<CompactFinancialData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchFinancialData() {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/operational/financial-impact/shipment/${shipmentId}`)

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`API Error: ${response.status} - ${errorText}`)
        }

        const result = await response.json()
        console.log('Financial API Response:', result) // Debug log

        // Handle the API response structure
        if (result && typeof result === 'object') {
          setData({
            revenue: result.revenue || 0,
            cogs: result.cogs || 0,
            grossProfit: result.grossProfit || 0,
            profitMargin: result.profitMargin || 0,
            currency: result.currency || 'USD',
            profitPerUnit: result.profitPerUnit,
            utilizationRate: result.utilizationRate
          })
        } else {
          throw new Error('Invalid API response format')
        }
      } catch (err) {
        console.error('Financial data fetch error:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    if (shipmentId) {
      fetchFinancialData()
    }
  }, [shipmentId])

  if (loading) {
    return (
      <div className={`text-xs text-muted-foreground ${className}`}>
        Loading...
      </div>
    )
  }

  if (error) {
    return (
      <div className={`text-xs text-red-500 flex items-center gap-1 ${className}`}>
        <AlertCircle className="h-3 w-3" />
        Error
      </div>
    )
  }

  if (!data) {
    return (
      <div className={`text-xs text-muted-foreground ${className}`}>
        No data
      </div>
    )
  }

  const formatCurrency = (amount: number) => {
    return `$${Math.abs(amount).toLocaleString()}`
  }

  const getMarginColor = (margin: number) => {
    if (margin >= 25) return "text-green-600"
    if (margin >= 15) return "text-blue-600"
    if (margin >= 5) return "text-yellow-600"
    return "text-red-600"
  }

  const getMarginIcon = (margin: number) => {
    return margin >= 0 ? (
      <TrendingUp className="h-3 w-3" />
    ) : (
      <TrendingDown className="h-3 w-3" />
    )
  }

  return (
    <div className={`space-y-1 ${className}`}>
      {/* Revenue & COGS */}
      <div className="text-xs">
        <span className="text-muted-foreground">Rev:</span>{" "}
        <span className="font-medium text-green-600">
          {formatCurrency(data.revenue)}
        </span>
        {" | "}
        <span className="text-muted-foreground">COGS:</span>{" "}
        <span className="font-medium text-orange-600">
          {formatCurrency(data.cogs)}
        </span>
      </div>

      {/* Profit & Margin */}
      <div className="text-xs flex items-center gap-1">
        <span className="text-muted-foreground">Profit:</span>
        <span className={`font-medium ${data.grossProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {formatCurrency(data.grossProfit)}
        </span>
        <span className={`font-medium flex items-center gap-1 ${getMarginColor(data.profitMargin)}`}>
          {getMarginIcon(data.profitMargin)}
          {Math.abs(data.profitMargin).toFixed(1)}%
        </span>
      </div>

      {/* Container Utilization (if available) */}
      {data.utilizationRate && (
        <div className="text-xs">
          <Badge variant="outline" className="text-xs px-1 py-0">
            Container: {data.utilizationRate.toFixed(1)}%
          </Badge>
        </div>
      )}
    </div>
  )
}
