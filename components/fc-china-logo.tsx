"use client"

import { Factory } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface FCChinaLogoProps {
  size?: "sm" | "md" | "lg"
  variant?: "default" | "white" | "minimal"
  showSubtitle?: boolean
  href?: string
  className?: string
}

export function FCChinaLogo({
  size = "md",
  variant = "default",
  showSubtitle = true,
  href = "/",
  className
}: FCChinaLogoProps) {
  const sizeClasses = {
    sm: {
      container: "gap-1.5",
      icon: "h-6 w-6 rounded",
      iconInner: "h-3.5 w-3.5",
      title: "text-sm font-bold",
      subtitle: "text-xs -mt-0.5"
    },
    md: {
      container: "gap-2",
      icon: "h-8 w-8 rounded-lg",
      iconInner: "h-5 w-5",
      title: "text-xl font-bold",
      subtitle: "text-xs -mt-0.5"
    },
    lg: {
      container: "gap-3",
      icon: "h-10 w-10 rounded-lg",
      iconInner: "h-6 w-6",
      title: "text-2xl font-bold",
      subtitle: "text-sm -mt-1"
    }
  }

  const variantClasses = {
    default: {
      icon: "bg-slate-900",
      iconInner: "text-white",
      title: "text-slate-900 dark:text-white",
      subtitle: "text-slate-600 dark:text-slate-300"
    },
    white: {
      icon: "bg-white",
      iconInner: "text-slate-900",
      title: "text-white",
      subtitle: "text-slate-300"
    },
    minimal: {
      icon: "bg-slate-100",
      iconInner: "text-slate-700",
      title: "text-slate-900",
      subtitle: "text-slate-600"
    }
  }

  const sizes = sizeClasses[size]
  const variants = variantClasses[variant]

  const LogoContent = () => (
    <div className={cn("flex items-center", sizes.container, className)}>
      <div className={cn(
        "flex items-center justify-center transition-all duration-200 group-hover:shadow-md",
        sizes.icon,
        variants.icon
      )}>
        <Factory className={cn(sizes.iconInner, variants.iconInner, "transition-transform duration-200 group-hover:scale-110")} />
      </div>
      <div className="flex flex-col">
        <span className={cn(sizes.title, variants.title)}>
          FC-CHINA
        </span>
        {showSubtitle && (
          <span className={cn(
            "font-medium tracking-wider",
            sizes.subtitle,
            variants.subtitle
          )}>
            Silk Road
          </span>
        )}
      </div>
    </div>
  )

  if (href) {
    return (
      <Link href={href} className="group transition-all duration-200 hover:scale-105">
        <LogoContent />
      </Link>
    )
  }

  return <LogoContent />
}
