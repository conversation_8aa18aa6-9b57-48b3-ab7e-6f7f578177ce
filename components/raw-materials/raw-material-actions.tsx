"use client"

/**
 * Manufacturing ERP - Raw Material Actions Component
 * Client-side component for handling raw material actions (view, edit, delete)
 */

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Eye, Edit, Trash2, Loader2 } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface RawMaterialActionsProps {
  materialId: string
  materialName: string
  materialSku: string
}

export function RawMaterialActions({ materialId, materialName, materialSku }: RawMaterialActionsProps) {
  const router = useRouter()
  const { toast } = useSafeToast()
  const [isDeleting, setIsDeleting] = useState(false)
  const [showErrorDialog, setShowErrorDialog] = useState(false)
  const [errorMessage, setErrorMessage] = useState("")

  const handleDelete = async () => {
    setIsDeleting(true)

    try {
      const response = await fetch(`/api/raw-materials/${materialId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const error = await response.json()
        console.log("Delete API Error:", error) // Debug log

        // Show error dialog instead of throwing error
        setErrorMessage(error.message || "Failed to delete material")
        setShowErrorDialog(true)
        setIsDeleting(false)
        return
      }

      const result = await response.json()

      // Show different messages based on the action taken
      if (result.action === "discontinued") {
        toast({
          title: "Status Changed",
          description: result.message,
          variant: "default",
        })
      } else {
        toast({
          title: "Success",
          description: result.message,
        })
      }

      // Refresh the page to update the list
      router.refresh()
    } catch (error) {
      console.error("Delete material error:", error)
      // Show error dialog for network or other errors
      setErrorMessage("Network error occurred. Please try again.")
      setShowErrorDialog(true)
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <div className="flex items-center gap-2">
      {/* View Button */}
      <Button variant="ghost" size="sm" asChild>
        <Link href={`/raw-materials/${materialId}`}>
          <Eye className="h-4 w-4" />
        </Link>
      </Button>

      {/* Edit Button */}
      <Button variant="ghost" size="sm" asChild>
        <Link href={`/raw-materials/${materialId}/edit`}>
          <Edit className="h-4 w-4" />
        </Link>
      </Button>

      {/* Delete Button with Confirmation Dialog */}
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700 hover:bg-red-50">
            <Trash2 className="h-4 w-4" />
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Raw Material</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the raw material <strong>"{materialName}"</strong> (SKU: {materialSku})?
              <br /><br />
              <span className="text-blue-600">
                <strong>How it works:</strong>
                <br />• If material is <strong>active</strong> → Status will be set to "discontinued"
                <br />• If material is <strong>discontinued</strong> with no dependencies → Will be permanently deleted
                <br />• Materials with active lots/BOMs cannot be deleted
              </span>
              <br /><br />
              <span className="text-red-600">
                <strong>Warning:</strong> Permanent deletion cannot be undone.
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Material
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Error Dialog Modal */}
      <AlertDialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600">Cannot Delete Material</AlertDialogTitle>
            <AlertDialogDescription>
              <strong>{materialName}</strong> (SKU: {materialSku}) cannot be deleted.
              <br /><br />
              <strong>Reason:</strong> {errorMessage}
              <br /><br />
              <span className="text-blue-600">
                <strong>To delete this material, you need to:</strong>
                <br />• Go to the material detail page
                <br />• Consume or remove all inventory lots
                <br />• Remove from any product BOMs
                <br />• Then try deleting again
              </span>
              <br /><br />
              <span className="text-amber-600">
                <strong>Alternative:</strong> Keep the material as "Discontinued" to preserve data integrity.
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => setShowErrorDialog(false)}>
              I Understand
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
