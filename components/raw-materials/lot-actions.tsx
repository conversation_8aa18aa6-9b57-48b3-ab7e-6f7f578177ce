"use client"

/**
 * Manufacturing ERP - Raw Material Lot Actions Component
 * Client-side component for handling lot actions (consume, delete)
 */

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Trash2, Loader2, Package } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface LotActionsProps {
  lotId: string
  lotNumber: string
  materialId: string
  materialName: string
  quantity: string
  unit: string
}

export function LotActions({ 
  lotId, 
  lotNumber, 
  materialId, 
  materialName, 
  quantity, 
  unit 
}: LotActionsProps) {
  const router = useRouter()
  const { toast } = useSafeToast()
  const [isDeleting, setIsDeleting] = useState(false)
  const [showErrorDialog, setShowErrorDialog] = useState(false)
  const [errorMessage, setErrorMessage] = useState("")

  const handleDeleteLot = async () => {
    setIsDeleting(true)
    
    try {
      const response = await fetch(`/api/raw-materials/${materialId}/lots/${lotId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const error = await response.json()
        console.log("Delete Lot API Error:", error)
        
        setErrorMessage(error.message || "Failed to delete lot")
        setShowErrorDialog(true)
        setIsDeleting(false)
        return
      }

      const result = await response.json()
      
      toast({
        title: "Success",
        description: result.message || "Inventory lot deleted successfully",
      })

      // Refresh the page to update the lot list
      router.refresh()
    } catch (error) {
      console.error("Delete lot error:", error)
      setErrorMessage("Network error occurred. Please try again.")
      setShowErrorDialog(true)
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <>
      <div className="flex items-center gap-2">
        {/* Delete Lot Button */}
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="text-red-600">Delete Inventory Lot</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete lot <strong>{lotNumber}</strong> from <strong>{materialName}</strong>?
                <br /><br />
                <span className="text-blue-600">
                  <strong>Lot Details:</strong>
                  <br />• Quantity: {parseFloat(quantity).toFixed(2)} {unit}
                  <br />• This will permanently remove this inventory lot
                </span>
                <br /><br />
                <span className="text-red-600">
                  <strong>Warning:</strong> This action cannot be undone. All consumption history for this lot will be preserved.
                </span>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteLot}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Lot
                  </>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      {/* Error Dialog Modal */}
      <AlertDialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600">Cannot Delete Lot</AlertDialogTitle>
            <AlertDialogDescription>
              Lot <strong>{lotNumber}</strong> cannot be deleted.
              <br /><br />
              <strong>Reason:</strong> {errorMessage}
              <br /><br />
              <span className="text-blue-600">
                <strong>Alternative:</strong> You can consume the lot by creating consumption transactions 
                or adjust the quantity to zero through inventory adjustments.
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => setShowErrorDialog(false)}>
              I Understand
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
