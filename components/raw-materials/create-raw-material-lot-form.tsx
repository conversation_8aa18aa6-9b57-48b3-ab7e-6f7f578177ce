"use client"

/**
 * Manufacturing ERP - Create Raw Material Lot Form
 * Professional form component for adding inventory lots to raw materials
 */

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Save, Loader2 } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface Supplier {
  id: string
  name: string
  email: string | null
}

interface RawMaterial {
  id: string
  sku: string
  name: string
  unit: string
}

interface CreateRawMaterialLotFormProps {
  material: RawMaterial
  suppliers: Supplier[]
}

export function CreateRawMaterialLotForm({ material, suppliers }: CreateRawMaterialLotFormProps) {
  const router = useRouter()
  const { toast } = useSafeToast()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    lot_number: "",
    supplier_id: "",
    purchase_contract_id: "",
    qty: "",
    unit_cost: "",
    currency: "USD",
    received_date: new Date().toISOString().split('T')[0], // Today's date
    expiry_date: "",
    quality_status: "pending",
    status: "available",
    location: "rm_building_a", // Default to Raw Materials - Building A
    batch_notes: "",
    inspection_notes: "",
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      console.log("Submitting lot data:", formData) // Debug log
      const response = await fetch(`/api/raw-materials/${material.id}/lots`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to create lot")
      }

      const result = await response.json()

      toast({
        title: "Success",
        description: "Inventory lot created successfully",
      })

      // Redirect back to the material detail page
      router.push(`/raw-materials/${material.id}`)
    } catch (error) {
      console.error("Create lot error:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create lot",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Material Information */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Material</p>
              <p className="font-medium">{material.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">SKU</p>
              <p className="font-medium">{material.sku}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Unit</p>
              <p className="font-medium">{material.unit}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lot Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="lot_number">Lot Number</Label>
          <Input
            id="lot_number"
            value={formData.lot_number}
            onChange={(e) => handleInputChange("lot_number", e.target.value)}
            placeholder="e.g., LOT-2024-001"
          />
          <p className="text-xs text-muted-foreground">
            Leave empty to auto-generate
          </p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="supplier">Supplier</Label>
          <Select value={formData.supplier_id || undefined} onValueChange={(value) => handleInputChange("supplier_id", value || "")}>
            <SelectTrigger>
              <SelectValue placeholder="Select supplier (optional)" />
            </SelectTrigger>
            <SelectContent>
              {suppliers.map((supplier) => (
                <SelectItem key={supplier.id} value={supplier.id}>
                  {supplier.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Quantity and Cost */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="qty">Quantity *</Label>
          <Input
            id="qty"
            type="number"
            step="0.01"
            value={formData.qty}
            onChange={(e) => handleInputChange("qty", e.target.value)}
            placeholder="0.00"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="unit_cost">Unit Cost *</Label>
          <Input
            id="unit_cost"
            type="number"
            step="0.01"
            value={formData.unit_cost}
            onChange={(e) => handleInputChange("unit_cost", e.target.value)}
            placeholder="0.00"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="currency">Currency</Label>
          <Select value={formData.currency} onValueChange={(value) => handleInputChange("currency", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="USD">USD</SelectItem>
              <SelectItem value="EUR">EUR</SelectItem>
              <SelectItem value="CNY">CNY</SelectItem>
              <SelectItem value="GBP">GBP</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Dates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="received_date">Received Date *</Label>
          <Input
            id="received_date"
            type="date"
            value={formData.received_date}
            onChange={(e) => handleInputChange("received_date", e.target.value)}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="expiry_date">Expiry Date</Label>
          <Input
            id="expiry_date"
            type="date"
            value={formData.expiry_date}
            onChange={(e) => handleInputChange("expiry_date", e.target.value)}
          />
        </div>
      </div>

      {/* Status and Location */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="quality_status">Quality Status</Label>
          <Select value={formData.quality_status} onValueChange={(value) => handleInputChange("quality_status", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
              <SelectItem value="quarantined">Quarantined</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="status">Availability Status</Label>
          <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="available">Available</SelectItem>
              <SelectItem value="reserved">Reserved</SelectItem>
              <SelectItem value="consumed">Consumed</SelectItem>
              <SelectItem value="expired">Expired</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="location">Storage Location *</Label>
          <Select value={formData.location} onValueChange={(value) => handleInputChange("location", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select storage location" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="rm_building_a">Raw Materials - Building A</SelectItem>
              <SelectItem value="rm_outdoor_yard">Raw Materials - Outdoor Yard</SelectItem>
              <SelectItem value="fg_main_warehouse">Main Finished Goods Warehouse</SelectItem>
              <SelectItem value="dist_shanghai">Shanghai Distribution Center</SelectItem>
              <SelectItem value="dist_beijing">Beijing Distribution Center</SelectItem>
              <SelectItem value="export_staging">Export Staging Area</SelectItem>
              <SelectItem value="quarantine">Quarantine Area</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Notes */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="batch_notes">Batch Notes</Label>
          <Textarea
            id="batch_notes"
            value={formData.batch_notes}
            onChange={(e) => handleInputChange("batch_notes", e.target.value)}
            placeholder="Notes about this batch/lot..."
            rows={2}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="inspection_notes">Inspection Notes</Label>
          <Textarea
            id="inspection_notes"
            value={formData.inspection_notes}
            onChange={(e) => handleInputChange("inspection_notes", e.target.value)}
            placeholder="Quality inspection notes..."
            rows={2}
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex items-center gap-4">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Create Lot
            </>
          )}
        </Button>
        <Button type="button" variant="outline" asChild>
          <Link href={`/raw-materials/${material.id}`}>Cancel</Link>
        </Button>
      </div>
    </form>
  )
}
