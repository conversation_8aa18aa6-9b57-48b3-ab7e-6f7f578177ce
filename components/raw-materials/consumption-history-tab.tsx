"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { RefreshCw, Calendar, Package, User, DollarSign } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface ConsumptionRecord {
  id: string
  qty_consumed: string
  unit_cost: string
  total_cost: string
  consumed_date: string
  notes?: string
  workOrder?: {
    id: string
    number: string
    product?: {
      name: string
      sku: string
    }
    salesContract?: {
      customer?: {
        name: string
      }
    }
  }
  rawMaterialLot?: {
    lot_number?: string
    supplier?: {
      name: string
    }
  }
}

interface ConsumptionHistoryTabProps {
  materialId: string
}

export function ConsumptionHistoryTab({ materialId }: ConsumptionHistoryTabProps) {
  const [consumptions, setConsumptions] = useState<ConsumptionRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [summary, setSummary] = useState({
    totalConsumptions: 0,
    totalCost: 0,
    totalQty: 0,
  })

  const fetchConsumptionHistory = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/raw-materials/consumption?material_id=${materialId}&limit=100`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch consumption history: ${response.status}`)
      }

      const data = await response.json()
      console.log("Consumption history data:", data)
      
      setConsumptions(data.consumptions || [])
      setSummary(data.summary || { totalConsumptions: 0, totalCost: 0, totalQty: 0 })
      
    } catch (err) {
      console.error("Error fetching consumption history:", err)
      setError(err instanceof Error ? err.message : "Failed to load consumption history")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchConsumptionHistory()
  }, [materialId])

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString()
    } catch {
      return dateString
    }
  }

  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(num || 0)
  }

  const formatQuantity = (qty: string | number) => {
    const num = typeof qty === 'string' ? parseFloat(qty) : qty
    return num.toFixed(2)
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            Loading Consumption History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Consumption History</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">{error}</p>
          <Button variant="outline" onClick={fetchConsumptionHistory}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Summary Cards */}
      {summary.totalConsumptions > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Consumed</p>
                  <p className="text-lg font-semibold">{formatQuantity(summary.totalQty)} units</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Cost</p>
                  <p className="text-lg font-semibold">{formatCurrency(summary.totalCost)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-purple-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Transactions</p>
                  <p className="text-lg font-semibold">{summary.totalConsumptions}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Consumption History Table */}
      <Card>
        <CardHeader>
          <CardTitle>Consumption History</CardTitle>
        </CardHeader>
        <CardContent>
          {consumptions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No consumption history found</p>
              <p className="text-sm">Material consumption will appear here when work orders are completed</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Work Order</TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead>Lot</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit Cost</TableHead>
                    <TableHead>Total Cost</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {consumptions.map((consumption) => (
                    <TableRow key={consumption.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          {formatDate(consumption.consumed_date)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {consumption.workOrder ? (
                          <Link
                            href={`/production/${consumption.workOrder.id}`}
                            className="text-blue-600 hover:underline font-medium"
                          >
                            {consumption.workOrder.number}
                          </Link>
                        ) : (
                          <span className="text-muted-foreground">N/A</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {consumption.workOrder?.product ? (
                          <div>
                            <p className="font-medium">{consumption.workOrder.product.name}</p>
                            <p className="text-sm text-muted-foreground">{consumption.workOrder.product.sku}</p>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">N/A</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">
                            {consumption.rawMaterialLot?.lot_number || `LOT-${consumption.id.slice(-8)}`}
                          </p>
                          {consumption.rawMaterialLot?.supplier && (
                            <p className="text-sm text-muted-foreground">
                              {consumption.rawMaterialLot.supplier.name}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {formatQuantity(consumption.qty_consumed)}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatCurrency(consumption.unit_cost)}</TableCell>
                      <TableCell>
                        <span className="font-medium">{formatCurrency(consumption.total_cost)}</span>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm text-muted-foreground max-w-xs truncate">
                          {consumption.notes || "Auto-consumed"}
                        </p>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
