"use client"

// ============================================================================
// MANUFACTURING ERP - PRICING ANALYTICS DASHBOARD
// ============================================================================
//
// Comprehensive pricing analytics dashboard utilizing Phase 2 pricing context
// enhancements for advanced margin analysis, cost tracking, and pricing
// optimization visualization.
//
// FEATURES:
// ✅ Contract Margin Analysis - Visual margin performance insights
// ✅ Pricing Method Performance - Strategy effectiveness comparison
// ✅ Cost Tracking Visualization - Product cost variance analysis
// ✅ Pricing Optimization Recommendations - Data-driven insights
// ✅ Real-time Data Updates - Live pricing analytics
//
// <AUTHOR> ERP Developer
// @version 1.0.0 - Phase 3 Advanced Features
// @date 2024-01-XX
// ============================================================================

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts"
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Percent, 
  Target,
  AlertTriangle,
  CheckCircle,
  Download,
  RefreshCw
} from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface MarginAnalysisData {
  contractId: string
  contractNumber: string
  totalRevenue: number
  totalCost: number
  totalMargin: number
  marginPercentage: number
  currency: string
  itemCount: number
  averageMargin: number
  pricingMethods: Record<string, number>
}

interface PricingMethodData {
  method: string
  contractCount: number
  totalRevenue: number
  averageMargin: number
  marginRange: { min: number; max: number }
  currency: string
}

interface CostTrackingData {
  productId: string
  productName: string
  productSku: string
  currentCostPrice: number | null
  currentBasePrice: number | null
  averageContractPrice: number
  lastTransactionCost: number | null
  costVariance: number
  priceVariance: number
  currency: string
}

interface OptimizationData {
  underperformingProducts: Array<{
    productId: string
    productName: string
    currentMargin: number
    recommendedPrice: number
    potentialIncrease: number
  }>
  overperformingMethods: Array<{
    method: string
    averageMargin: number
    recommendation: string
  }>
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

export function PricingAnalyticsDashboard() {
  const [marginData, setMarginData] = useState<MarginAnalysisData[]>([])
  const [methodData, setMethodData] = useState<PricingMethodData[]>([])
  const [costData, setCostData] = useState<CostTrackingData[]>([])
  const [optimizationData, setOptimizationData] = useState<OptimizationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [dateFrom, setDateFrom] = useState("")
  const [dateTo, setDateTo] = useState("")
  const [currency, setCurrency] = useState("USD")
  const { toast } = useSafeToast()

  // Load analytics data
  const loadAnalyticsData = async () => {
    setLoading(true)
    try {
      const requests = [
        { type: "margins", date_from: dateFrom, date_to: dateTo, currency },
        { type: "methods", date_from: dateFrom, date_to: dateTo },
        { type: "costs" },
        { type: "optimization" }
      ]

      const response = await fetch("/api/analytics/pricing", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ requests }),
      })

      if (!response.ok) throw new Error("Failed to load analytics data")

      const result = await response.json()
      
      result.results.forEach((item: any) => {
        switch (item.type) {
          case "margins":
            setMarginData(item.data || [])
            break
          case "methods":
            setMethodData(item.data || [])
            break
          case "costs":
            setCostData(item.data || [])
            break
          case "optimization":
            setOptimizationData(item.data || null)
            break
        }
      })

    } catch (error) {
      console.error("Analytics loading error:", error)
      toast({
        title: "Error",
        description: "Failed to load pricing analytics data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Export analytics data
  const exportData = async (type: string) => {
    try {
      const params = new URLSearchParams({
        type,
        format: "csv",
        ...(dateFrom && { date_from: dateFrom }),
        ...(dateTo && { date_to: dateTo }),
        ...(currency && { currency }),
      })

      const response = await fetch(`/api/analytics/pricing?${params}`)
      if (!response.ok) throw new Error("Export failed")

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `pricing-${type}-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: "Success",
        description: `${type} data exported successfully`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export data",
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    loadAnalyticsData()
  }, [])

  // Calculate summary metrics
  const totalRevenue = marginData.reduce((sum, item) => sum + item.totalRevenue, 0)
  const totalMargin = marginData.reduce((sum, item) => sum + item.totalMargin, 0)
  const averageMarginPercentage = marginData.length > 0 
    ? marginData.reduce((sum, item) => sum + item.marginPercentage, 0) / marginData.length 
    : 0

  const topPerformingMethod = methodData.reduce((top, method) => 
    method.averageMargin > (top?.averageMargin || 0) ? method : top, 
    null as PricingMethodData | null
  )

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pricing Analytics</h1>
          <p className="text-muted-foreground">
            Advanced pricing insights and optimization recommendations
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadAnalyticsData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="date-from">Date From</Label>
              <Input
                id="date-from"
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="date-to">Date To</Label>
              <Input
                id="date-to"
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="currency">Currency</Label>
              <Select value={currency} onValueChange={setCurrency}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                  <SelectItem value="CNY">CNY</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={loadAnalyticsData} disabled={loading}>
                Apply Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${totalRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              From {marginData.length} contracts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Margin</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${totalMargin.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {averageMarginPercentage.toFixed(1)}% average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Method</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {topPerformingMethod?.method || "N/A"}
            </div>
            <p className="text-xs text-muted-foreground">
              {topPerformingMethod?.averageMargin.toFixed(1)}% margin
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Optimization</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {optimizationData?.overperformingMethods.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              High-performing methods
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="margins" className="space-y-4">
        <TabsList>
          <TabsTrigger value="margins">Contract Margins</TabsTrigger>
          <TabsTrigger value="methods">Pricing Methods</TabsTrigger>
          <TabsTrigger value="costs">Cost Tracking</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
        </TabsList>

        <TabsContent value="margins" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Contract Margin Analysis</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportData("margins")}
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={marginData.slice(0, 10)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="contractNumber" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value: number, name: string) => [
                      `$${value.toLocaleString()}`,
                      name === 'totalRevenue' ? 'Revenue' : 
                      name === 'totalMargin' ? 'Margin' : name
                    ]}
                  />
                  <Bar dataKey="totalRevenue" fill="#8884d8" name="Revenue" />
                  <Bar dataKey="totalMargin" fill="#82ca9d" name="Margin" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="methods" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Method Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={methodData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ method, averageMargin }) => `${method}: ${averageMargin.toFixed(1)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="totalRevenue"
                    >
                      {methodData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => [`$${value.toLocaleString()}`, 'Revenue']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Method Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {methodData.map((method, index) => (
                    <div key={method.method} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <div className="font-medium">{method.method}</div>
                        <div className="text-sm text-muted-foreground">
                          {method.contractCount} contracts
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">
                          {method.averageMargin.toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">
                          ${method.totalRevenue.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="costs" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Cost Tracking Report</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportData("costs")}
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {costData.slice(0, 10).map((product) => (
                  <div key={product.productId} className="flex items-center justify-between p-4 border rounded">
                    <div>
                      <div className="font-medium">{product.productName}</div>
                      <div className="text-sm text-muted-foreground">{product.productSku}</div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-right">
                      <div>
                        <div className="text-sm text-muted-foreground">Cost Price</div>
                        <div className="font-medium">
                          ${product.currentCostPrice?.toFixed(2) || 'N/A'}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Avg Contract</div>
                        <div className="font-medium">
                          ${product.averageContractPrice.toFixed(2)}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Variance</div>
                        <div className={`font-medium ${
                          product.priceVariance > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {product.priceVariance > 0 ? '+' : ''}{product.priceVariance.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  High-Performing Methods
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {optimizationData?.overperformingMethods.map((method, index) => (
                    <div key={index} className="p-3 bg-green-50 border border-green-200 rounded">
                      <div className="font-medium text-green-800">{method.method}</div>
                      <div className="text-sm text-green-600 mt-1">
                        {method.averageMargin.toFixed(1)}% average margin
                      </div>
                      <div className="text-sm text-green-700 mt-2">
                        {method.recommendation}
                      </div>
                    </div>
                  )) || <div className="text-muted-foreground">No high-performing methods identified</div>}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                  Improvement Opportunities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {optimizationData?.underperformingProducts.map((product, index) => (
                    <div key={index} className="p-3 bg-orange-50 border border-orange-200 rounded">
                      <div className="font-medium text-orange-800">{product.productName}</div>
                      <div className="text-sm text-orange-600 mt-1">
                        Current margin: {product.currentMargin.toFixed(1)}%
                      </div>
                      <div className="text-sm text-orange-700 mt-2">
                        Consider increasing price to ${product.recommendedPrice.toFixed(2)}
                      </div>
                    </div>
                  )) || <div className="text-muted-foreground">No underperforming products identified</div>}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
