"use client"

import { X, Plus, Eye, Edit, List, Home } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useTabContext, Tab } from "@/components/tab-context"

interface TabNavigationProps {
  className?: string
}

export function TabNavigation({ className }: TabNavigationProps) {
  const { tabs, activeTabId, closeTab, switchTab } = useTabContext()

  const getTabIcon = (tab: Tab) => {
    if (tab.icon) return tab.icon

    switch (tab.type) {
      case 'list':
        return <List className="h-4 w-4" />
      case 'add':
        return <Plus className="h-4 w-4" />
      case 'view':
        return <Eye className="h-4 w-4" />
      case 'edit':
        return <Edit className="h-4 w-4" />
      case 'dashboard':
        return <Home className="h-4 w-4" />
      default:
        return null
    }
  }

  const handleCloseTab = (tabId: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    closeTab(tabId)
  }

  return (
    <div className={cn("border-b bg-background", className)}>
      <div className="flex items-center overflow-x-auto scrollbar-hide">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            className={cn(
              "flex items-center gap-2 px-3 py-2 border-r cursor-pointer transition-colors min-w-0 group",
              "hover:bg-muted/50",
              activeTabId === tab.id
                ? "bg-background border-b-2 border-b-primary text-foreground"
                : "bg-muted/20 text-muted-foreground"
            )}
            onClick={() => switchTab(tab.id)}
          >
            {getTabIcon(tab)}
            <span className="text-sm font-medium truncate max-w-[150px]" title={tab.title}>
              {tab.title}
            </span>
            {tab.closeable && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 hover:bg-destructive/20 hover:text-destructive transition-opacity"
                onClick={(e) => handleCloseTab(tab.id, e)}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

// Export the useTabNavigation hook from tab-context for convenience
export { useTabNavigation } from "@/components/tab-context"
