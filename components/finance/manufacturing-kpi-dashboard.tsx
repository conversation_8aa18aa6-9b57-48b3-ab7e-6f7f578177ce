/**
 * Manufacturing ERP - Professional Financial KPI Dashboard
 * Enterprise-grade financial intelligence and decision support
 */

"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useI18n } from "@/components/i18n-provider"
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Zap
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ManufacturingKPIs {
  // Core Financial KPIs (Accrual Basis)
  totalRevenue: { mtd: number; ytd: number }
  totalExpenses: { mtd: number; ytd: number }
  profitLoss: { mtd: number; ytd: number }

  // ✅ ENHANCED: Cash Flow KPIs (Cash Basis)
  cashReceived: { mtd: number; ytd: number }
  pendingReceivables: { count: number; value: number }
  netCashFlow: number
  cashFlowTrend: 'positive' | 'negative' | 'stable'

  // ✅ ENHANCED: Collection Performance
  averageCollectionDays: number
  collectionRate: number
  overdueInvoices: { count: number; value: number }

  // AR/AP Intelligence
  overdueAR: { count: number; value: number }
  overdueAP: { count: number; value: number }

  // Manufacturing-Specific KPIs
  contractProfitability: number
  manufacturingMargin: number

  // Business Performance
  totalContracts: { active: number; completed: number }
  customerPaymentHealth: 'excellent' | 'good' | 'warning' | 'critical'
  supplierPaymentPerformance: 'excellent' | 'good' | 'warning' | 'critical'
}

interface KPIDashboardProps {
  kpis: ManufacturingKPIs
  loading?: boolean
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`
}

const getHealthColor = (health: string): string => {
  switch (health) {
    case 'excellent': return 'text-green-600 bg-green-100'
    case 'good': return 'text-blue-600 bg-blue-100'
    case 'warning': return 'text-yellow-600 bg-yellow-100'
    case 'critical': return 'text-red-600 bg-red-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

const getTrendIcon = (trend: string, value: number) => {
  if (trend === 'positive' || value > 0) {
    return <TrendingUp className="h-4 w-4 text-green-600" />
  } else if (trend === 'negative' || value < 0) {
    return <TrendingDown className="h-4 w-4 text-red-600" />
  }
  return <Activity className="h-4 w-4 text-blue-600" />
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function ManufacturingKPIDashboard({ kpis, loading = false }: KPIDashboardProps) {
  const { t } = useI18n()

  if (loading || !kpis) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Core Financial KPIs */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-blue-600" />
          {t("finance.kpis.coreMetrics")}
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Total Revenue */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t("finance.kpis.totalRevenue")}
                  </p>
                  <div className="space-y-1">
                    <p className="text-2xl font-bold">{formatCurrency(kpis.totalRevenue.ytd)}</p>
                    <p className="text-sm text-muted-foreground">
                      MTD: {formatCurrency(kpis.totalRevenue.mtd)}
                    </p>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Total Expenses */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t("finance.kpis.totalExpenses")}
                  </p>
                  <div className="space-y-1">
                    <p className="text-2xl font-bold">{formatCurrency(kpis.totalExpenses.ytd)}</p>
                    <p className="text-sm text-muted-foreground">
                      MTD: {formatCurrency(kpis.totalExpenses.mtd)}
                    </p>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-red-100 flex items-center justify-center">
                  <TrendingDown className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Profit/Loss */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t("finance.kpis.profitLoss")}
                  </p>
                  <div className="space-y-1">
                    <p className={`text-2xl font-bold ${kpis.profitLoss.ytd >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(kpis.profitLoss.ytd)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      MTD: {formatCurrency(kpis.profitLoss.mtd)}
                    </p>
                  </div>
                </div>
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${kpis.profitLoss.ytd >= 0 ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                  {getTrendIcon('', kpis.profitLoss.ytd)}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Net Cash Flow */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t("finance.kpis.netCashFlow")}
                  </p>
                  <div className="space-y-1">
                    <p className={`text-2xl font-bold ${kpis.netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(kpis.netCashFlow)}
                    </p>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(kpis.cashFlowTrend, kpis.netCashFlow)}
                      <span className="text-sm text-muted-foreground capitalize">
                        {kpis.cashFlowTrend}
                      </span>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      {kpis.netCashFlow === 0 ? "Balanced operations" :
                        kpis.netCashFlow > 0 ? "Building cash reserves" :
                          "Using more cash than receiving"}
                    </p>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Overdue Intelligence */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-600" />
          {t("finance.kpis.overdueIntelligence")}
        </h3>
        <div className="grid gap-4 md:grid-cols-2">
          {/* Overdue AR */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t("finance.kpis.overdueAR")}
                  </p>
                  <p className="text-2xl font-bold text-orange-600">
                    {formatCurrency(kpis.overdueAR.value)}
                  </p>
                </div>
                <Badge variant="outline" className="text-orange-600 border-orange-200">
                  {kpis.overdueAR.count} invoices
                </Badge>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Customer Payment Health</span>
                  <Badge className={getHealthColor(kpis.customerPaymentHealth)}>
                    {kpis.customerPaymentHealth}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Overdue AP */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t("finance.kpis.overdueAP")}
                  </p>
                  <p className="text-2xl font-bold text-red-600">
                    {formatCurrency(kpis.overdueAP.value)}
                  </p>
                </div>
                <Badge variant="outline" className="text-red-600 border-red-200">
                  {kpis.overdueAP.count} bills
                </Badge>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Payment Performance</span>
                  <Badge className={getHealthColor(kpis.supplierPaymentPerformance)}>
                    {kpis.supplierPaymentPerformance}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* ✅ ENHANCED: Cash Flow Performance */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <DollarSign className="h-5 w-5 text-blue-600" />
          Cash Flow Performance
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Cash Received */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Cash Received
                  </p>
                  <div className="space-y-1">
                    <p className="text-2xl font-bold text-green-600">{formatCurrency(kpis.cashReceived.ytd)}</p>
                    <p className="text-sm text-muted-foreground">
                      MTD: {formatCurrency(kpis.cashReceived.mtd)}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      {kpis.cashReceived.ytd === 0 ? "Recent invoices - payments pending" : "Actual cash collected"}
                    </p>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pending Receivables */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Pending Receivables
                  </p>
                  <div className="space-y-1">
                    <p className="text-2xl font-bold text-orange-600">{formatCurrency(kpis.pendingReceivables.value)}</p>
                    <p className="text-sm text-muted-foreground">
                      {kpis.pendingReceivables.count} invoices
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      {kpis.pendingReceivables.count > 0 ? `${kpis.pendingReceivables.count} invoices awaiting payment` : "All invoices paid"}
                    </p>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-orange-100 flex items-center justify-center">
                  <Clock className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Collection Rate */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Collection Rate
                  </p>
                  <div className="space-y-1">
                    <p className={`text-2xl font-bold ${kpis.collectionRate >= 80 ? 'text-green-600' : kpis.collectionRate >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                      {formatPercentage(kpis.collectionRate)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Cash / Revenue
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      {kpis.collectionRate === 0 ? "Will improve as customers pay" :
                        kpis.collectionRate >= 80 ? "Excellent collection efficiency" :
                          "Collection efficiency tracking"}
                    </p>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Overdue Invoices */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Overdue Invoices
                  </p>
                  <div className="space-y-1">
                    <p className={`text-2xl font-bold ${kpis.overdueInvoices.count === 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(kpis.overdueInvoices.value)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {kpis.overdueInvoices.count} overdue
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      {kpis.overdueInvoices.count === 0 ? "Excellent - nothing overdue" : "Requires immediate attention"}
                    </p>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-red-100 flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* ✅ IMPROVED: Business Performance Intelligence */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Target className="h-5 w-5 text-purple-600" />
          Business Performance Intelligence
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* ✅ IMPROVED: Contract Profitability */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Contract Profitability
                  </p>
                  <p className="text-2xl font-bold">{formatPercentage(kpis.contractProfitability)}</p>
                  <p className="text-xs text-purple-600">
                    Sales vs Purchase margin
                  </p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center">
                  <PieChart className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Average Collection Days */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t("finance.kpis.avgCollectionDays")}
                  </p>
                  <div className="space-y-1">
                    <p className="text-2xl font-bold">{kpis.averageCollectionDays} days</p>
                    <p className="text-xs text-blue-600">
                      {kpis.averageCollectionDays === 0 ? "Recent invoices" :
                        kpis.averageCollectionDays <= 30 ? "Excellent collection speed" :
                          "Collection time tracking"}
                    </p>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Manufacturing Margin */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t("finance.kpis.manufacturingMargin")}
                  </p>
                  <p className="text-2xl font-bold">{formatPercentage(kpis.manufacturingMargin)}</p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* ✅ IMPROVED: Total Contracts (Sales + Purchase) */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Total Contracts
                  </p>
                  <p className="text-2xl font-bold">{kpis.totalContracts.active}</p>
                  <p className="text-sm text-muted-foreground">
                    Sales + Purchase contracts
                  </p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-indigo-100 flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
