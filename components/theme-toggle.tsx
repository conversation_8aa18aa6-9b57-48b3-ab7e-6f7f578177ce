"use client"

import { useEffect, useState } from "react"
import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"
import { Moon, Sun } from "lucide-react"

export type AppTheme = "light" | "dark"

export function ThemeToggle({ className }: { className?: string }) {
  const { resolvedTheme, setTheme, theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // Avoid hydration mismatch by rendering only after mount
  useEffect(() => setMounted(true), [])

  if (!mounted) return null

  const isDark = (resolvedTheme ?? theme) === "dark"

  const nextTheme: AppTheme = isDark ? "light" : "dark"

  return (
    <Button
      variant="ghost"
      size="icon"
      aria-label={isDark ? "Switch to light mode" : "Switch to dark mode"}
      className={className}
      onClick={() => setTheme(nextTheme)}
      title={isDark ? "Light" : "Dark"}
    >
      {/* Show both icons to enable smooth transition; hide the inactive one */}
      <Sun className={`h-5 w-5 ${isDark ? "hidden" : ""}`} />
      <Moon className={`h-5 w-5 ${isDark ? "" : "hidden"}`} />
    </Button>
  )
}

