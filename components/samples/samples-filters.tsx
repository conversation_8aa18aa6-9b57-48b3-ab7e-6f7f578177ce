"use client"

import { useState, useEffect } from "react"
import { Search, Filter, X, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useI18n } from "@/components/i18n-provider"

// ✅ PROFESSIONAL BIDIRECTIONAL FILTER INTERFACE
export interface SamplesFilters {
  search: string
  customer_id: string
  product_id: string
  supplier_id: string
  approval_status: string
  sample_type: string
  priority: string
  status: string

  // ✅ BIDIRECTIONAL WORKFLOW FILTERS
  sample_direction: string
  sample_purpose: string
  testing_status: string

  date_from: string
  date_to: string
  created_from: string
  created_to: string
  sortBy: string
  sortOrder: "asc" | "desc"
}

interface SamplesFiltersProps {
  filters: SamplesFilters
  onFiltersChange: (filters: SamplesFilters) => void
  customers: Array<{ id: string; name: string }>
  products: Array<{ id: string; name: string; sku: string }>
  suppliers: Array<{ id: string; name: string }>
  loading?: boolean
}

export function SamplesFilters({
  filters,
  onFiltersChange,
  customers,
  products,
  suppliers,
  loading
}: SamplesFiltersProps) {
  const { t } = useI18n()
  const [showAdvanced, setShowAdvanced] = useState(false)

  // ✅ SEARCHABLE OPTIONS: Convert customers to searchable options
  const customerOptions: SearchableSelectOption[] = [
    {
      value: 'all',
      label: 'All Customers',
      subtitle: 'Show samples from all customers',
      description: '🏢 No customer filter',
    },
    ...customers.map((customer) => ({
      value: customer.id,
      label: customer.name,
      subtitle: 'Customer',
      description: '👤 Filter by this customer',
    }))
  ]

  // ✅ SEARCHABLE OPTIONS: Convert products to searchable options
  const productOptions: SearchableSelectOption[] = [
    {
      value: 'all',
      label: 'All Products',
      subtitle: 'Show samples for all products',
      description: '📦 No product filter',
    },
    ...products.map((product) => ({
      value: product.id,
      label: product.name,
      subtitle: `SKU: ${product.sku}`,
      description: '🏷️ Filter by this product',
    }))
  ]

  // ✅ HANDLE FILTER CHANGES
  const updateFilter = (key: keyof SamplesFilters, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    })
  }

  // ✅ CLEAR ALL FILTERS
  const clearFilters = () => {
    onFiltersChange({
      search: "",
      customer_id: "",
      product_id: "",
      supplier_id: "",
      approval_status: "",
      sample_type: "",
      priority: "",
      status: "",
      date_from: "",
      date_to: "",
      created_from: "",
      created_to: "",
      sortBy: "created_at",
      sortOrder: "desc",
    })
  }

  // ✅ COUNT ACTIVE FILTERS
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) =>
    value && key !== 'sortBy' && key !== 'sortOrder'
  ).length

  return (
    <div className="space-y-4">
      {/* ✅ MAIN SEARCH AND QUICK FILTERS */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder={t("samples.filters.search")}
            value={filters.search}
            onChange={(e) => updateFilter('search', e.target.value)}
            className="pl-10"
            disabled={loading}
          />
        </div>

        {/* Quick Status Filter */}
        <Select
          value={filters.approval_status || "all"}
          onValueChange={(value) => updateFilter('approval_status', value === "all" ? "" : value)}
          disabled={loading}
        >
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Approval Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("samples.filters.status.all")}</SelectItem>
            <SelectItem value="pending">{t("samples.filters.status.pending")}</SelectItem>
            <SelectItem value="approved">{t("samples.filters.status.approved")}</SelectItem>
            <SelectItem value="rejected">{t("samples.filters.status.rejected")}</SelectItem>
            <SelectItem value="revision_required">Revision Required</SelectItem>
          </SelectContent>
        </Select>

        {/* Quick Type Filter */}
        <Select
          value={filters.sample_type || "all"}
          onValueChange={(value) => updateFilter('sample_type', value === "all" ? "" : value)}
          disabled={loading}
        >
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Sample Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("samples.filters.type.all")}</SelectItem>
            <SelectItem value="development">{t("samples.filters.type.development")}</SelectItem>
            <SelectItem value="production">{t("samples.filters.type.production")}</SelectItem>
            <SelectItem value="quality">{t("samples.filters.type.quality")}</SelectItem>
            <SelectItem value="prototype">{t("samples.filters.type.prototype")}</SelectItem>
          </SelectContent>
        </Select>

        {/* ✅ PROFESSIONAL BIDIRECTIONAL DIRECTION FILTER */}
        <Select
          value={filters.sample_direction || "all"}
          onValueChange={(value) => updateFilter('sample_direction', value === "all" ? "" : value)}
          disabled={loading}
        >
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Sample Direction" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("samples.filters.direction.all")}</SelectItem>
            <SelectItem value="outbound">📤 {t("samples.filters.direction.outbound")}</SelectItem>
            <SelectItem value="inbound">📥 {t("samples.filters.direction.inbound")}</SelectItem>
            <SelectItem value="internal">🏭 {t("samples.filters.direction.internal")}</SelectItem>
          </SelectContent>
        </Select>

        {/* Advanced Filters Toggle */}
        <Popover open={showAdvanced} onOpenChange={setShowAdvanced}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="relative">
              <Filter className="h-4 w-4 mr-2" />
              {t("samples.filters.advanced")}
              {activeFiltersCount > 0 && (
                <Badge
                  variant="secondary"
                  className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                >
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-96" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">{t("samples.filters.advanced")}</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  disabled={activeFiltersCount === 0}
                >
                  <X className="h-4 w-4 mr-1" />
                  {t("samples.filters.clear")}
                </Button>
              </div>

              {/* ✅ RELATIONSHIP FILTERS */}
              <div className="grid grid-cols-1 gap-3">
                <div>
                  <label className="text-sm font-medium mb-1 block">Customer</label>
                  <SearchableSelect
                    options={customerOptions}
                    value={filters.customer_id || "all"}
                    onValueChange={(value) => updateFilter('customer_id', value === "all" ? "" : value)}
                    placeholder="Search customers..."
                    searchPlaceholder="Search by customer name..."
                    emptyMessage="No customers found."
                    allowClear={false}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-1 block">Product</label>
                  <SearchableSelect
                    options={productOptions}
                    value={filters.product_id || "all"}
                    onValueChange={(value) => updateFilter('product_id', value === "all" ? "" : value)}
                    placeholder="Search products..."
                    searchPlaceholder="Search by product name or SKU..."
                    emptyMessage="No products found."
                    allowClear={false}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-1 block">Supplier</label>
                  <Select
                    value={filters.supplier_id || "all"}
                    onValueChange={(value) => updateFilter('supplier_id', value === "all" ? "" : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Suppliers</SelectItem>
                      {suppliers.map((supplier) => (
                        <SelectItem key={supplier.id} value={supplier.id}>
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-1 block">Priority</label>
                  <Select
                    value={filters.priority || "all"}
                    onValueChange={(value) => updateFilter('priority', value === "all" ? "" : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priorities</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-1 block">Status</label>
                  <Select
                    value={filters.status || "all"}
                    onValueChange={(value) => updateFilter('status', value === "all" ? "" : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* ✅ DATE RANGE FILTERS */}
              <div className="space-y-3">
                <h5 className="text-sm font-medium">Date Ranges</h5>

                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-xs text-muted-foreground">Sample Date From</label>
                    <Input
                      type="date"
                      value={filters.date_from}
                      onChange={(e) => updateFilter('date_from', e.target.value)}
                      className="text-sm"
                    />
                  </div>
                  <div>
                    <label className="text-xs text-muted-foreground">Sample Date To</label>
                    <Input
                      type="date"
                      value={filters.date_to}
                      onChange={(e) => updateFilter('date_to', e.target.value)}
                      className="text-sm"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-xs text-muted-foreground">Created From</label>
                    <Input
                      type="date"
                      value={filters.created_from}
                      onChange={(e) => updateFilter('created_from', e.target.value)}
                      className="text-sm"
                    />
                  </div>
                  <div>
                    <label className="text-xs text-muted-foreground">Created To</label>
                    <Input
                      type="date"
                      value={filters.created_to}
                      onChange={(e) => updateFilter('created_to', e.target.value)}
                      className="text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* ✅ SORTING OPTIONS */}
              <div className="space-y-3">
                <h5 className="text-sm font-medium">Sorting</h5>
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={filters.sortBy}
                    onValueChange={(value) => updateFilter('sortBy', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="created_at">Created Date</SelectItem>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="code">Code</SelectItem>
                      <SelectItem value="date">Sample Date</SelectItem>
                      <SelectItem value="approval_status">Approval Status</SelectItem>
                      <SelectItem value="priority">Priority</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select
                    value={filters.sortOrder}
                    onValueChange={(value) => updateFilter('sortOrder', value as "asc" | "desc")}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="desc">Newest First</SelectItem>
                      <SelectItem value="asc">Oldest First</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* ✅ ACTIVE FILTERS DISPLAY */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              Search: {filters.search}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter('search', '')}
              />
            </Badge>
          )}
          {filters.approval_status && (
            <Badge variant="secondary" className="gap-1">
              Status: {filters.approval_status}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter('approval_status', '')}
              />
            </Badge>
          )}
          {filters.sample_type && (
            <Badge variant="secondary" className="gap-1">
              Type: {filters.sample_type}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter('sample_type', '')}
              />
            </Badge>
          )}
          {filters.customer_id && (
            <Badge variant="secondary" className="gap-1">
              Customer: {customers.find(c => c.id === filters.customer_id)?.name || 'Unknown'}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter('customer_id', '')}
              />
            </Badge>
          )}
          {/* Add more active filter badges as needed */}
        </div>
      )}
    </div>
  )
}
