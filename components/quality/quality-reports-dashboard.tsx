"use client"

/**
 * Manufacturing ERP - Quality Reports Dashboard Component
 * 
 * Professional quality reporting system with export capabilities, compliance reports,
 * and quality certificates management. Enhances existing quality control system.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 2 Quality Control Enhancement
 */

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import {
  FileText,
  Download,
  Calendar,
  Filter,
  Search,
  Plus,
  Eye,
  Award,
  AlertCircle,
  CheckCircle,
  Clock,
  RefreshCw,
  FileSpreadsheet,
  FilePdf,
  Printer
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface QualityReport {
  id: string
  reportType: 'inspection_summary' | 'compliance_audit' | 'defect_analysis' | 'supplier_quality' | 'certificate_batch'
  title: string
  description: string
  status: 'draft' | 'pending' | 'approved' | 'published'
  createdBy: string
  createdAt: string
  updatedAt: string
  parameters: {
    dateRange: { start: string; end: string }
    filters: Record<string, any>
    includeCharts: boolean
    format: 'pdf' | 'excel' | 'csv'
  }
  metadata: {
    recordCount: number
    generatedAt?: string
    fileSize?: string
    downloadUrl?: string
  }
}

interface QualityCertificate {
  id: string
  certificateNumber: string
  certificateType: 'iso_9001' | 'iso_14001' | 'fda_approval' | 'ce_marking' | 'custom'
  title: string
  issuedBy: string
  issuedDate: string
  expiryDate: string
  status: 'active' | 'expired' | 'pending_renewal' | 'suspended'
  scope: string
  attachmentUrl?: string
  relatedInspections: string[]
}

interface QualityReportsDashboardProps {
  companyId: string
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function QualityReportsDashboard({ companyId }: QualityReportsDashboardProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()

  // ✅ STATE MANAGEMENT
  const [activeTab, setActiveTab] = useState<'reports' | 'certificates'>('reports')
  const [reports, setReports] = useState<QualityReport[]>([])
  const [certificates, setCertificates] = useState<QualityCertificate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  // ✅ FETCH REPORTS AND CERTIFICATES
  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [reportsRes, certificatesRes] = await Promise.all([
        fetch('/api/quality/reports'),
        fetch('/api/quality/certificates')
      ])

      if (reportsRes.ok) {
        const reportsData = await reportsRes.json()
        setReports(reportsData.reports || [])
      }

      if (certificatesRes.ok) {
        const certificatesData = await certificatesRes.json()
        setCertificates(certificatesData.certificates || [])
      }

    } catch (error) {
      console.error("Error fetching quality reports data:", error)
      toast({
        title: "Error",
        description: "Failed to load quality reports data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // ✅ EFFECTS
  useEffect(() => {
    fetchData()
  }, [])

  // ✅ FILTER FUNCTIONS
  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || report.status === statusFilter
    const matchesType = typeFilter === 'all' || report.reportType === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const filteredCertificates = certificates.filter(cert => {
    const matchesSearch = cert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cert.certificateNumber.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || cert.status === statusFilter
    const matchesType = typeFilter === 'all' || cert.certificateType === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  // ✅ HELPER FUNCTIONS
  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'secondary',
      pending: 'outline',
      approved: 'default',
      published: 'default',
      active: 'default',
      expired: 'destructive',
      pending_renewal: 'outline',
      suspended: 'destructive'
    } as const

    const colors = {
      draft: 'text-gray-600',
      pending: 'text-yellow-600',
      approved: 'text-green-600',
      published: 'text-blue-600',
      active: 'text-green-600',
      expired: 'text-red-600',
      pending_renewal: 'text-yellow-600',
      suspended: 'text-red-600'
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        <span className={colors[status as keyof typeof colors] || 'text-gray-600'}>
          {status.replace('_', ' ').toUpperCase()}
        </span>
      </Badge>
    )
  }

  const getReportTypeLabel = (type: string) => {
    const labels = {
      inspection_summary: 'Inspection Summary',
      compliance_audit: 'Compliance Audit',
      defect_analysis: 'Defect Analysis',
      supplier_quality: 'Supplier Quality',
      certificate_batch: 'Certificate Batch'
    }
    return labels[type as keyof typeof labels] || type
  }

  const getCertificateTypeLabel = (type: string) => {
    const labels = {
      iso_9001: 'ISO 9001',
      iso_14001: 'ISO 14001',
      fda_approval: 'FDA Approval',
      ce_marking: 'CE Marking',
      custom: 'Custom Certificate'
    }
    return labels[type as keyof typeof labels] || type
  }

  // ✅ ACTION HANDLERS
  const handleGenerateReport = async (reportType: string) => {
    try {
      const response = await fetch('/api/quality/reports/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reportType,
          parameters: {
            dateRange: {
              start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
              end: new Date().toISOString().split('T')[0]
            },
            format: 'pdf',
            includeCharts: true
          }
        })
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Report generation started. You'll be notified when ready."
        })
        fetchData() // Refresh the list
      } else {
        throw new Error('Failed to generate report')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate report",
        variant: "destructive"
      })
    }
  }

  const handleDownloadReport = async (reportId: string) => {
    try {
      const response = await fetch(`/api/quality/reports/${reportId}/download`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `quality-report-${reportId}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download report",
        variant: "destructive"
      })
    }
  }

  // ✅ RENDER LOADING STATE
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* ✅ HEADER WITH CONTROLS */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Quality Reports</h2>
          <p className="text-muted-foreground">
            Generate and manage quality reports and certificates
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Generate Report
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Generate Quality Report</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-2">
                  <Button
                    variant="outline"
                    onClick={() => handleGenerateReport('inspection_summary')}
                    className="justify-start"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Inspection Summary
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleGenerateReport('compliance_audit')}
                    className="justify-start"
                  >
                    <Award className="h-4 w-4 mr-2" />
                    Compliance Audit
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleGenerateReport('defect_analysis')}
                    className="justify-start"
                  >
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Defect Analysis
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleGenerateReport('supplier_quality')}
                    className="justify-start"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Supplier Quality
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* ✅ TABS AND FILTERS */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant={activeTab === 'reports' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('reports')}
            >
              <FileText className="h-4 w-4 mr-2" />
              Reports
            </Button>
            <Button
              variant={activeTab === 'certificates' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('certificates')}
            >
              <Award className="h-4 w-4 mr-2" />
              Certificates
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-64"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              {activeTab === 'certificates' && (
                <>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                </>
              )}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* ✅ CONTENT BASED ON ACTIVE TAB */}
      {activeTab === 'reports' ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Quality Reports ({filteredReports.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Report</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Records</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{report.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {report.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {getReportTypeLabel(report.reportType)}
                      </Badge>
                    </TableCell>
                    <TableCell>{getStatusBadge(report.status)}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(report.createdAt).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        by {report.createdBy}
                      </div>
                    </TableCell>
                    <TableCell>{report.metadata.recordCount}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        {report.status === 'published' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownloadReport(report.id)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Quality Certificates ({filteredCertificates.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Certificate</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Issued</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCertificates.map((cert) => (
                  <TableRow key={cert.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{cert.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {cert.certificateNumber}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {getCertificateTypeLabel(cert.certificateType)}
                      </Badge>
                    </TableCell>
                    <TableCell>{getStatusBadge(cert.status)}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(cert.issuedDate).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        by {cert.issuedBy}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(cert.expiryDate).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        {cert.attachmentUrl && (
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
