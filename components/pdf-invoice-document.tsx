"use client"

import React from 'react'
import { Document, Page, Text, View, StyleSheet, pdf } from '@react-pdf/renderer'

// Professional PDF styles for invoices
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Helvetica',
    fontSize: 12,
    lineHeight: 1.6,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
    paddingBottom: 20,
    borderBottomWidth: 2,
    borderBottomColor: '#2563eb',
  },
  logo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoBox: {
    width: 50,
    height: 50,
    backgroundColor: '#2563eb',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  companyInfo: {
    flexDirection: 'column',
  },
  companyName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
  },
  companySubtitle: {
    fontSize: 12,
    color: '#6b7280',
  },
  documentInfo: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  invoiceTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 20,
    textAlign: 'center',
  },
  invoiceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  detailsSection: {
    flexDirection: 'column',
    width: '48%',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  detailLabel: {
    fontSize: 11,
    color: '#6b7280',
    width: '40%',
  },
  detailValue: {
    fontSize: 11,
    color: '#111827',
    fontWeight: 'bold',
    width: '60%',
  },
  addressSection: {
    marginBottom: 8,
  },
  addressText: {
    fontSize: 11,
    color: '#374151',
    marginBottom: 2,
  },
  itemsTable: {
    marginBottom: 30,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f3f4f6',
    padding: 10,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#d1d5db',
  },
  tableHeaderText: {
    fontSize: 11,
    fontWeight: 'bold',
    color: '#374151',
  },
  tableRow: {
    flexDirection: 'row',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  tableCell: {
    fontSize: 11,
    color: '#111827',
  },
  totalsSection: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    marginBottom: 30,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: 200,
    marginBottom: 5,
  },
  totalLabel: {
    fontSize: 12,
    color: '#6b7280',
  },
  totalValue: {
    fontSize: 12,
    color: '#111827',
    fontWeight: 'bold',
  },
  grandTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: 200,
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 2,
    borderTopColor: '#2563eb',
  },
  grandTotalLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2563eb',
  },
  grandTotalValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2563eb',
  },
  notesSection: {
    marginBottom: 30,
  },
  notesTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 8,
  },
  notesText: {
    fontSize: 11,
    color: '#6b7280',
    lineHeight: 1.5,
  },
  footer: {
    marginTop: 'auto',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  footerContent: {
    backgroundColor: '#f9fafb',
    padding: 20,
    borderRadius: 8,
    textAlign: 'center',
  },
  footerTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 8,
  },
  footerText: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 4,
  },
})

interface InvoiceData {
  // Invoice details
  number: string
  date: string
  due_date?: string
  amount: string
  received?: string
  paid?: string
  currency: string
  status: string
  payment_terms?: string
  notes?: string

  // Company details
  company: {
    name: string
    address?: string
    phone?: string
    email?: string
  }

  // Customer/Supplier details
  customer?: {
    name: string
    company?: string
    address?: string
    phone?: string
    email?: string
  }

  supplier?: {
    name: string
    company?: string
    address?: string
    phone?: string
    email?: string
  }

  // Contract details (optional)
  contract?: {
    number: string
    title?: string
  }
}

interface PDFInvoiceDocumentProps {
  invoiceData: InvoiceData
  invoiceType: 'ar' | 'ap' // Accounts Receivable or Accounts Payable
}

export const PDFInvoiceDocument: React.FC<PDFInvoiceDocumentProps> = ({
  invoiceData,
  invoiceType,
}) => {
  const isAR = invoiceType === 'ar'
  const clientEntity = isAR ? invoiceData.customer : invoiceData.supplier
  const clientLabel = isAR ? 'Bill To' : 'Vendor'
  const invoiceTitle = isAR ? 'INVOICE' : 'BILL'
  const amountPaid = isAR ? invoiceData.received : invoiceData.paid

  const formatCurrency = (amount: string, currency: string) => {
    const numAmount = parseFloat(amount || '0')
    return `${currency} ${numAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Professional Header */}
        <View style={styles.header}>
          <View style={styles.logo}>
            <View style={styles.logoBox}>
              <Text style={styles.logoText}>ERP</Text>
            </View>
            <View style={styles.companyInfo}>
              <Text style={styles.companyName}>Manufacturing ERP</Text>
              <Text style={styles.companySubtitle}>Professional Invoice Management System</Text>
            </View>
          </View>
          <View style={styles.documentInfo}>
            <Text style={{ fontWeight: 'bold' }}>Document: {invoiceData.number}</Text>
            <Text>{new Date().toLocaleDateString()}</Text>
          </View>
        </View>

        {/* Invoice Title */}
        <Text style={styles.invoiceTitle}>{invoiceTitle}</Text>

        {/* Invoice Details */}
        <View style={styles.invoiceDetails}>
          {/* Company Information */}
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>From</Text>
            <View style={styles.addressSection}>
              <Text style={styles.addressText}>{invoiceData.company.name}</Text>
              {invoiceData.company.address && (
                <Text style={styles.addressText}>{invoiceData.company.address}</Text>
              )}
              {invoiceData.company.phone && (
                <Text style={styles.addressText}>Phone: {invoiceData.company.phone}</Text>
              )}
              {invoiceData.company.email && (
                <Text style={styles.addressText}>Email: {invoiceData.company.email}</Text>
              )}
            </View>
          </View>

          {/* Client Information */}
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>{clientLabel}</Text>
            {clientEntity && (
              <View style={styles.addressSection}>
                <Text style={styles.addressText}>{clientEntity.name}</Text>
                {clientEntity.company && (
                  <Text style={styles.addressText}>{clientEntity.company}</Text>
                )}
                {clientEntity.address && (
                  <Text style={styles.addressText}>{clientEntity.address}</Text>
                )}
                {clientEntity.phone && (
                  <Text style={styles.addressText}>Phone: {clientEntity.phone}</Text>
                )}
                {clientEntity.email && (
                  <Text style={styles.addressText}>Email: {clientEntity.email}</Text>
                )}
              </View>
            )}
          </View>
        </View>

        {/* Invoice Information */}
        <View style={styles.invoiceDetails}>
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Invoice Details</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Invoice #:</Text>
              <Text style={styles.detailValue}>{invoiceData.number}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Date:</Text>
              <Text style={styles.detailValue}>{formatDate(invoiceData.date)}</Text>
            </View>
            {invoiceData.due_date && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Due Date:</Text>
                <Text style={styles.detailValue}>{formatDate(invoiceData.due_date)}</Text>
              </View>
            )}
            {invoiceData.payment_terms && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Terms:</Text>
                <Text style={styles.detailValue}>{invoiceData.payment_terms}</Text>
              </View>
            )}
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Status:</Text>
              <Text style={styles.detailValue}>{invoiceData.status.toUpperCase()}</Text>
            </View>
          </View>

          {/* Contract Information (if available) */}
          {invoiceData.contract && (
            <View style={styles.detailsSection}>
              <Text style={styles.sectionTitle}>Related Contract</Text>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Contract #:</Text>
                <Text style={styles.detailValue}>{invoiceData.contract.number}</Text>
              </View>
              {invoiceData.contract.title && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Title:</Text>
                  <Text style={styles.detailValue}>{invoiceData.contract.title}</Text>
                </View>
              )}
            </View>
          )}
        </View>

        {/* Amount Summary */}
        <View style={styles.totalsSection}>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Subtotal:</Text>
            <Text style={styles.totalValue}>{formatCurrency(invoiceData.amount, invoiceData.currency)}</Text>
          </View>

          {amountPaid && parseFloat(amountPaid) > 0 && (
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>{isAR ? 'Received:' : 'Paid:'}</Text>
              <Text style={styles.totalValue}>{formatCurrency(amountPaid, invoiceData.currency)}</Text>
            </View>
          )}

          <View style={styles.grandTotalRow}>
            <Text style={styles.grandTotalLabel}>
              {amountPaid && parseFloat(amountPaid) > 0 ? 'Balance Due:' : 'Total Amount:'}
            </Text>
            <Text style={styles.grandTotalValue}>
              {amountPaid && parseFloat(amountPaid) > 0
                ? formatCurrency((parseFloat(invoiceData.amount) - parseFloat(amountPaid)).toString(), invoiceData.currency)
                : formatCurrency(invoiceData.amount, invoiceData.currency)
              }
            </Text>
          </View>
        </View>

        {/* Notes Section */}
        {invoiceData.notes && (
          <View style={styles.notesSection}>
            <Text style={styles.notesTitle}>Notes</Text>
            <Text style={styles.notesText}>{invoiceData.notes}</Text>
          </View>
        )}

        {/* Professional Footer */}
        <View style={styles.footer}>
          <View style={styles.footerContent}>
            <Text style={styles.footerTitle}>Manufacturing ERP - Professional Invoice Management</Text>
            <Text style={styles.footerText}>This document was generated automatically by the Manufacturing ERP system.</Text>
            <Text style={styles.footerText}>For questions regarding this {invoiceTitle.toLowerCase()}, please contact your account representative.</Text>
            <Text style={styles.footerText}>Generated on: {new Date().toLocaleString()}</Text>
          </View>
        </View>
      </Page>
    </Document>
  )
}

// Export function to generate and download invoice PDF
export const generateInvoicePDF = async (
  invoiceData: InvoiceData,
  invoiceType: 'ar' | 'ap'
) => {
  const doc = (
    <PDFInvoiceDocument
      invoiceData={invoiceData}
      invoiceType={invoiceType}
    />
  )

  const asPdf = pdf(doc)
  const blob = await asPdf.toBlob()

  // Create download link
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${invoiceType}-invoice-${invoiceData.number}.pdf`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  return blob
}

// Export function to generate invoice PDF blob (for preview)
export const generateInvoicePDFBlob = async (
  invoiceData: InvoiceData,
  invoiceType: 'ar' | 'ap'
): Promise<Blob> => {
  const doc = (
    <PDFInvoiceDocument
      invoiceData={invoiceData}
      invoiceType={invoiceType}
    />
  )

  const asPdf = pdf(doc)
  const blob = await asPdf.toBlob()

  return blob
}
