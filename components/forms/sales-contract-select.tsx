"use client"

import * as React from "react"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { useI18n } from "@/components/i18n-provider"
import { Badge } from "@/components/ui/badge"
import { Calendar, User, FileText, DollarSign } from "lucide-react"

interface SalesContract {
  id: string
  number: string
  date: string
  status: string
  currency?: string
  total_amount?: number
  customer?: {
    id: string
    name: string
    contact_email?: string
  }
  items?: Array<{
    id: string
    product?: {
      name: string
    }
  }>
}

interface SalesContractSelectProps {
  salesContracts: SalesContract[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  required?: boolean
  onContractSelected?: (contract: SalesContract | null) => void
}

export function SalesContractSelect({
  salesContracts,
  value,
  onValueChange,
  placeholder = "Search sales contracts...",
  className,
  disabled = false,
  required = false,
  onContractSelected,
}: SalesContractSelectProps) {
  const { t } = useI18n()

  // Convert sales contracts to searchable options with professional display
  const contractOptions: SearchableSelectOption[] = React.useMemo(() => {
    return salesContracts
      .filter(contract => 
        // Only show contracts suitable for invoicing
        ['approved', 'active', 'in_production', 'shipped', 'completed'].includes(contract.status)
      )
      .map((contract) => {
        const customerDisplay = contract.customer?.name || "No customer"
        const dateDisplay = contract.date ? new Date(contract.date).toLocaleDateString() : ""
        const statusDisplay = contract.status || "draft"
        const currencyDisplay = contract.currency ? ` (${contract.currency})` : ""
        const amountDisplay = contract.total_amount 
          ? ` • ${contract.currency || 'USD'} ${contract.total_amount.toLocaleString()}`
          : ""
        const itemsCount = contract.items?.length || 0

        return {
          value: contract.id,
          label: contract.number,
          subtitle: `${customerDisplay} • ${dateDisplay}${currencyDisplay}${amountDisplay}`,
          description: `Status: ${statusDisplay.charAt(0).toUpperCase() + statusDisplay.slice(1)} • ${itemsCount} items`,
        }
      })
  }, [salesContracts])

  // Handle selection and notify parent component
  const handleValueChange = (contractId: string) => {
    onValueChange?.(contractId)
    
    // Find and pass the selected contract to parent
    const selectedContract = salesContracts.find(c => c.id === contractId)
    onContractSelected?.(selectedContract || null)
  }

  // Handle clear selection
  const handleClear = () => {
    onValueChange?.("")
    onContractSelected?.(null)
  }

  // Get selected contract for display
  const selectedContract = salesContracts.find(c => c.id === value)

  return (
    <div className="space-y-2">
      <SearchableSelect
        options={contractOptions}
        value={value}
        onValueChange={handleValueChange}
        placeholder={placeholder}
        searchPlaceholder="Search by contract number, customer, or status..."
        emptyMessage="No sales contracts found. Only approved/active contracts are shown."
        className={className}
        disabled={disabled}
        allowClear={!required}
      />
      
      {/* Display selected contract details */}
      {selectedContract && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground bg-muted/50 p-2 rounded-md">
          <FileText className="h-4 w-4" />
          <span className="font-medium">{selectedContract.number}</span>
          {selectedContract.customer && (
            <>
              <span>•</span>
              <User className="h-3 w-3" />
              <span>{selectedContract.customer.name}</span>
            </>
          )}
          {selectedContract.total_amount && (
            <>
              <span>•</span>
              <DollarSign className="h-3 w-3" />
              <span>{selectedContract.currency || 'USD'} {selectedContract.total_amount.toLocaleString()}</span>
            </>
          )}
          <Badge variant="secondary" className="text-xs">
            {selectedContract.status}
          </Badge>
        </div>
      )}
    </div>
  )
}
