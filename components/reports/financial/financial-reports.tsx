/**
 * Manufacturing ERP - Financial Reports Component
 * Enterprise-grade financial reporting and analytics dashboard
 */

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  Download,
  FileText,
  BarChart3,
  PieChart,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface FinancialMetrics {
  profitLoss: {
    revenue: { current: number; previous: number; growth: number }
    expenses: { current: number; previous: number; growth: number }
    grossProfit: { current: number; previous: number; margin: number }
    netProfit: { current: number; previous: number; margin: number }
  }
  balanceSheet: {
    assets: { current: number; fixed: number; total: number }
    liabilities: { current: number; longTerm: number; total: number }
    equity: { retained: number; total: number }
  }
  cashFlow: {
    operating: { inflow: number; outflow: number; net: number }
    investing: { inflow: number; outflow: number; net: number }
    financing: { inflow: number; outflow: number; net: number }
    netCashFlow: number
  }
  arAging: {
    current: { count: number; amount: number }
    overdue30: { count: number; amount: number }
    overdue60: { count: number; amount: number }
    overdue90: { count: number; amount: number }
    total: { count: number; amount: number }
  }
  apAging: {
    current: { count: number; amount: number }
    overdue30: { count: number; amount: number }
    overdue60: { count: number; amount: number }
    overdue90: { count: number; amount: number }
    total: { count: number; amount: number }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function formatCurrency(value: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value)
}

function formatPercentage(value: number): string {
  return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`
}

function getGrowthColor(growth: number): string {
  if (growth > 0) return 'text-green-600'
  if (growth < 0) return 'text-red-600'
  return 'text-gray-600'
}

function getGrowthIcon(growth: number) {
  if (growth > 0) return ArrowUpRight
  if (growth < 0) return ArrowDownRight
  return Activity
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function FinancialReports() {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState('month')

  // Load financial metrics
  useEffect(() => {
    loadFinancialMetrics()
  }, [dateRange])

  const loadFinancialMetrics = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/reports/financial?period=${dateRange}`)
      
      if (!response.ok) {
        throw new Error('Failed to load financial metrics')
      }

      const data = await response.json()
      setMetrics(data)
    } catch (error) {
      console.error('Error loading financial metrics:', error)
      toast({
        title: "Error",
        description: "Failed to load financial metrics",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading || !metrics) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Date Range Selector */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Calendar className="h-5 w-5 text-muted-foreground" />
          <span className="text-sm font-medium">Period:</span>
          <div className="flex gap-1">
            {['month', 'quarter', 'year'].map((period) => (
              <Button
                key={period}
                variant={dateRange === period ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDateRange(period)}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </Button>
            ))}
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
        </div>
      </div>

      {/* Profit & Loss Overview */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-green-600" />
          Profit & Loss Statement
        </h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Revenue */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                  <p className="text-2xl font-bold">{formatCurrency(metrics.profitLoss.revenue.current)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    {(() => {
                      const GrowthIcon = getGrowthIcon(metrics.profitLoss.revenue.growth)
                      return <GrowthIcon className={`h-3 w-3 ${getGrowthColor(metrics.profitLoss.revenue.growth)}`} />
                    })()}
                    <span className={`text-xs ${getGrowthColor(metrics.profitLoss.revenue.growth)}`}>
                      {formatPercentage(metrics.profitLoss.revenue.growth)}
                    </span>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Expenses */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Expenses</p>
                  <p className="text-2xl font-bold">{formatCurrency(metrics.profitLoss.expenses.current)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    {(() => {
                      const GrowthIcon = getGrowthIcon(metrics.profitLoss.expenses.growth)
                      return <GrowthIcon className={`h-3 w-3 ${getGrowthColor(metrics.profitLoss.expenses.growth)}`} />
                    })()}
                    <span className={`text-xs ${getGrowthColor(metrics.profitLoss.expenses.growth)}`}>
                      {formatPercentage(metrics.profitLoss.expenses.growth)}
                    </span>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-red-100 flex items-center justify-center">
                  <TrendingDown className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Gross Profit */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Gross Profit</p>
                  <p className="text-2xl font-bold">{formatCurrency(metrics.profitLoss.grossProfit.current)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <span className="text-xs text-muted-foreground">
                      Margin: {formatPercentage(metrics.profitLoss.grossProfit.margin)}
                    </span>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Net Profit */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Net Profit</p>
                  <p className="text-2xl font-bold">{formatCurrency(metrics.profitLoss.netProfit.current)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <span className="text-xs text-muted-foreground">
                      Margin: {formatPercentage(metrics.profitLoss.netProfit.margin)}
                    </span>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center">
                  <PieChart className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Cash Flow Analysis */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <Activity className="h-5 w-5 text-blue-600" />
          Cash Flow Statement
        </h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Operating Cash Flow</CardTitle>
              <CardDescription>Cash from operations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Inflow:</span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(metrics.cashFlow.operating.inflow)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Outflow:</span>
                  <span className="font-medium text-red-600">
                    {formatCurrency(metrics.cashFlow.operating.outflow)}
                  </span>
                </div>
                <div className="flex justify-between font-medium pt-2 border-t">
                  <span>Net:</span>
                  <span className={metrics.cashFlow.operating.net >= 0 ? 'text-green-600' : 'text-red-600'}>
                    {formatCurrency(metrics.cashFlow.operating.net)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Investing Cash Flow</CardTitle>
              <CardDescription>Cash from investments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Inflow:</span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(metrics.cashFlow.investing.inflow)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Outflow:</span>
                  <span className="font-medium text-red-600">
                    {formatCurrency(metrics.cashFlow.investing.outflow)}
                  </span>
                </div>
                <div className="flex justify-between font-medium pt-2 border-t">
                  <span>Net:</span>
                  <span className={metrics.cashFlow.investing.net >= 0 ? 'text-green-600' : 'text-red-600'}>
                    {formatCurrency(metrics.cashFlow.investing.net)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Financing Cash Flow</CardTitle>
              <CardDescription>Cash from financing</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Inflow:</span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(metrics.cashFlow.financing.inflow)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Outflow:</span>
                  <span className="font-medium text-red-600">
                    {formatCurrency(metrics.cashFlow.financing.outflow)}
                  </span>
                </div>
                <div className="flex justify-between font-medium pt-2 border-t">
                  <span>Net:</span>
                  <span className={metrics.cashFlow.financing.net >= 0 ? 'text-green-600' : 'text-red-600'}>
                    {formatCurrency(metrics.cashFlow.financing.net)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Aging Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* AR Aging */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-600" />
              Accounts Receivable Aging
            </CardTitle>
            <CardDescription>Customer payment status analysis</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Current (0-30 days)</span>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{metrics.arAging.current.count}</Badge>
                  <span className="font-medium">{formatCurrency(metrics.arAging.current.amount)}</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">31-60 days</span>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-yellow-600">{metrics.arAging.overdue30.count}</Badge>
                  <span className="font-medium">{formatCurrency(metrics.arAging.overdue30.amount)}</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">61-90 days</span>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-orange-600">{metrics.arAging.overdue60.count}</Badge>
                  <span className="font-medium">{formatCurrency(metrics.arAging.overdue60.amount)}</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">90+ days</span>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-red-600">{metrics.arAging.overdue90.count}</Badge>
                  <span className="font-medium">{formatCurrency(metrics.arAging.overdue90.amount)}</span>
                </div>
              </div>
              <div className="flex justify-between items-center pt-2 border-t font-medium">
                <span>Total Outstanding</span>
                <div className="flex items-center gap-2">
                  <Badge>{metrics.arAging.total.count}</Badge>
                  <span>{formatCurrency(metrics.arAging.total.amount)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* AP Aging */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Accounts Payable Aging
            </CardTitle>
            <CardDescription>Supplier payment obligations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Current (0-30 days)</span>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{metrics.apAging.current.count}</Badge>
                  <span className="font-medium">{formatCurrency(metrics.apAging.current.amount)}</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">31-60 days</span>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-yellow-600">{metrics.apAging.overdue30.count}</Badge>
                  <span className="font-medium">{formatCurrency(metrics.apAging.overdue30.amount)}</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">61-90 days</span>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-orange-600">{metrics.apAging.overdue60.count}</Badge>
                  <span className="font-medium">{formatCurrency(metrics.apAging.overdue60.amount)}</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">90+ days</span>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-red-600">{metrics.apAging.overdue90.count}</Badge>
                  <span className="font-medium">{formatCurrency(metrics.apAging.overdue90.amount)}</span>
                </div>
              </div>
              <div className="flex justify-between items-center pt-2 border-t font-medium">
                <span>Total Payable</span>
                <div className="flex items-center gap-2">
                  <Badge>{metrics.apAging.total.count}</Badge>
                  <span>{formatCurrency(metrics.apAging.total.amount)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
