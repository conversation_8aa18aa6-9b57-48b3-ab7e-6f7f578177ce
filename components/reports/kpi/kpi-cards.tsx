/**
 * Manufacturing ERP - KPI Card Components
 * Executive dashboard KPI cards with trend indicators and professional styling
 */

"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  ArrowUpRight,
  ArrowDownRight,
  Activity
} from "lucide-react"
import { SparklineChart } from "../charts/chart-components"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface KPICardProps {
  title: string
  value: string | number
  change?: number
  changeType?: 'percentage' | 'absolute'
  trend?: 'up' | 'down' | 'neutral'
  icon: React.ComponentType<{ className?: string }>
  color: string
  sparklineData?: Array<{ name: string; value: number }>
  subtitle?: string
  loading?: boolean
}

interface KPIMetrics {
  totalRevenue: number
  totalExpenses: number
  activeContracts: number
  completedWorkOrders: number
  pendingInspections: number
  currentInventoryValue: number
  revenueGrowth?: number
  expenseGrowth?: number
  contractGrowth?: number
  completionRate?: number
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function formatCurrency(value: number): string {
  if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(1)}M`
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(0)}K`
  }
  return `$${value.toLocaleString()}`
}

function formatNumber(value: number): string {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(0)}K`
  }
  return value.toLocaleString()
}

function getTrendIcon(trend: 'up' | 'down' | 'neutral') {
  switch (trend) {
    case 'up':
      return ArrowUpRight
    case 'down':
      return ArrowDownRight
    default:
      return Activity
  }
}

function getTrendColor(trend: 'up' | 'down' | 'neutral', isPositive: boolean = true) {
  if (trend === 'neutral') return 'text-gray-500'
  
  const isGoodTrend = (trend === 'up' && isPositive) || (trend === 'down' && !isPositive)
  return isGoodTrend ? 'text-green-600' : 'text-red-600'
}

// ============================================================================
// KPI CARD COMPONENT
// ============================================================================

export function KPICard({
  title,
  value,
  change,
  changeType = 'percentage',
  trend = 'neutral',
  icon: Icon,
  color,
  sparklineData,
  subtitle,
  loading = false
}: KPICardProps) {
  const TrendIcon = getTrendIcon(trend)
  const trendColor = getTrendColor(trend, !title.toLowerCase().includes('expense'))

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-24"></div>
              <div className="h-8 bg-gray-200 rounded w-16"></div>
              <div className="h-3 bg-gray-200 rounded w-20"></div>
            </div>
            <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <div className="flex items-baseline gap-2">
              <p className="text-2xl font-bold">
                {typeof value === 'number' ? formatNumber(value) : value}
              </p>
              {change !== undefined && (
                <div className={`flex items-center gap-1 ${trendColor}`}>
                  <TrendIcon className="h-3 w-3" />
                  <span className="text-xs font-medium">
                    {changeType === 'percentage' ? `${change >= 0 ? '+' : ''}${change.toFixed(1)}%` : 
                     changeType === 'absolute' ? `${change >= 0 ? '+' : ''}${formatNumber(change)}` : ''}
                  </span>
                </div>
              )}
            </div>
            {subtitle && (
              <p className="text-xs text-muted-foreground">{subtitle}</p>
            )}
            {sparklineData && (
              <div className="mt-2">
                <SparklineChart 
                  data={sparklineData} 
                  dataKey="value" 
                  color={color.includes('green') ? '#10b981' : 
                         color.includes('blue') ? '#3b82f6' : 
                         color.includes('purple') ? '#8b5cf6' : '#6b7280'} 
                  height={30}
                />
              </div>
            )}
          </div>
          <div className={`w-12 h-12 rounded-lg ${color} flex items-center justify-center`}>
            <Icon className="h-6 w-6" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// ============================================================================
// KPI CARDS GRID COMPONENT
// ============================================================================

interface KPICardsGridProps {
  metrics: KPIMetrics
  loading?: boolean
}

export function KPICardsGrid({ metrics, loading = false }: KPICardsGridProps) {
  // Generate sample sparkline data (in real implementation, this would come from API)
  const generateSparklineData = (baseValue: number, trend: 'up' | 'down' | 'neutral') => {
    const data = []
    let value = baseValue * 0.8
    
    for (let i = 0; i < 7; i++) {
      const variation = trend === 'up' ? Math.random() * 0.1 + 0.05 :
                       trend === 'down' ? Math.random() * -0.1 - 0.05 :
                       (Math.random() - 0.5) * 0.1
      
      value = value * (1 + variation)
      data.push({
        name: `Day ${i + 1}`,
        value: Math.round(value)
      })
    }
    
    return data
  }

  const kpiCards = [
    {
      title: "Total Revenue",
      value: formatCurrency(metrics.totalRevenue),
      change: metrics.revenueGrowth,
      trend: (metrics.revenueGrowth || 0) > 0 ? 'up' as const : 
             (metrics.revenueGrowth || 0) < 0 ? 'down' as const : 'neutral' as const,
      icon: TrendingUp,
      color: "text-green-600 bg-green-100",
      sparklineData: generateSparklineData(metrics.totalRevenue, 
        (metrics.revenueGrowth || 0) > 0 ? 'up' : 
        (metrics.revenueGrowth || 0) < 0 ? 'down' : 'neutral'),
      subtitle: "Monthly revenue"
    },
    {
      title: "Total Expenses",
      value: formatCurrency(metrics.totalExpenses),
      change: metrics.expenseGrowth,
      trend: (metrics.expenseGrowth || 0) > 0 ? 'up' as const : 
             (metrics.expenseGrowth || 0) < 0 ? 'down' as const : 'neutral' as const,
      icon: TrendingDown,
      color: "text-red-600 bg-red-100",
      sparklineData: generateSparklineData(metrics.totalExpenses,
        (metrics.expenseGrowth || 0) > 0 ? 'up' : 
        (metrics.expenseGrowth || 0) < 0 ? 'down' : 'neutral'),
      subtitle: "Monthly expenses"
    },
    {
      title: "Active Contracts",
      value: metrics.activeContracts,
      change: metrics.contractGrowth,
      trend: (metrics.contractGrowth || 0) > 0 ? 'up' as const : 
             (metrics.contractGrowth || 0) < 0 ? 'down' as const : 'neutral' as const,
      icon: Activity,
      color: "text-blue-600 bg-blue-100",
      sparklineData: generateSparklineData(metrics.activeContracts,
        (metrics.contractGrowth || 0) > 0 ? 'up' : 
        (metrics.contractGrowth || 0) < 0 ? 'down' : 'neutral'),
      subtitle: "Sales & purchase contracts"
    },
    {
      title: "Work Orders Completed",
      value: metrics.completedWorkOrders,
      change: metrics.completionRate,
      trend: (metrics.completionRate || 0) > 80 ? 'up' as const : 
             (metrics.completionRate || 0) < 60 ? 'down' as const : 'neutral' as const,
      icon: Activity,
      color: "text-purple-600 bg-purple-100",
      sparklineData: generateSparklineData(metrics.completedWorkOrders, 'up'),
      subtitle: `${(metrics.completionRate || 0).toFixed(1)}% completion rate`
    },
    {
      title: "Pending Inspections",
      value: metrics.pendingInspections,
      trend: metrics.pendingInspections > 10 ? 'up' as const : 
             metrics.pendingInspections < 5 ? 'down' as const : 'neutral' as const,
      icon: Activity,
      color: "text-orange-600 bg-orange-100",
      sparklineData: generateSparklineData(metrics.pendingInspections, 'neutral'),
      subtitle: "Quality control queue"
    },
    {
      title: "Inventory Value",
      value: formatCurrency(metrics.currentInventoryValue),
      trend: 'neutral' as const,
      icon: Activity,
      color: "text-indigo-600 bg-indigo-100",
      sparklineData: generateSparklineData(metrics.currentInventoryValue, 'neutral'),
      subtitle: "Current stock value"
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {kpiCards.map((card, index) => (
        <KPICard
          key={index}
          title={card.title}
          value={card.value}
          change={card.change}
          trend={card.trend}
          icon={card.icon}
          color={card.color}
          sparklineData={card.sparklineData}
          subtitle={card.subtitle}
          loading={loading}
        />
      ))}
    </div>
  )
}
