/**
 * Manufacturing ERP - Reports Dashboard Component
 * Enterprise-grade reporting and business intelligence dashboard
 */

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { KPICardsGrid } from "./kpi/kpi-cards"
import {
  RevenueTrendChart,
  AgingPieChart,
  ProductionBarChart,
  MultiLineChart
} from "./charts/chart-components"
import Link from "next/link"
import {
  BarChart3,
  TrendingUp,
  FileText,
  PieChart,
  Calendar,
  Download,
  DollarSign,
  Package,
  Users,
  Truck,
  ClipboardCheck,
  Factory,
  Target,
  Activity,
  ArrowRight,
  Eye,
  RefreshCw
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ReportCategory {
  id: string
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  color: string
  reports: ReportItem[]
}

interface ReportItem {
  id: string
  title: string
  description: string
  href: string
  status: 'available' | 'coming-soon'
  lastUpdated?: string
}

interface DashboardMetrics {
  summary: {
    totalReports: number
    availableCategories: number
    lastUpdated: string
  }
  quickMetrics: {
    totalRevenue: number
    totalExpenses: number
    activeContracts: number
    completedWorkOrders: number
    pendingInspections: number
    currentInventoryValue: number
  }
  recentActivity: {
    newInvoices: number
    completedOrders: number
    qualityIssues: number
    shipments: number
  }
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function ReportsDashboard() {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  // Load dashboard metrics
  useEffect(() => {
    loadDashboardMetrics()
  }, [])

  const loadDashboardMetrics = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/reports')

      if (!response.ok) {
        throw new Error('Failed to load dashboard metrics')
      }

      const data = await response.json()
      setMetrics(data)
    } catch (error) {
      console.error('Error loading dashboard metrics:', error)
      toast({
        title: "Error",
        description: "Failed to load dashboard metrics",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadDashboardMetrics()
    setRefreshing(false)
    toast({
      title: "Success",
      description: "Dashboard metrics refreshed",
    })
  }

  // Report categories configuration
  const reportCategories: ReportCategory[] = [
    {
      id: 'financial',
      title: 'Financial Reports',
      description: 'Revenue, expenses, cash flow, and financial performance',
      icon: DollarSign,
      color: 'text-green-600 bg-green-100',
      reports: [
        {
          id: 'profit-loss',
          title: 'Profit & Loss Statement',
          description: 'Revenue, expenses, and net profit analysis',
          href: '/reports/financial/profit-loss',
          status: 'available'
        },
        {
          id: 'balance-sheet',
          title: 'Balance Sheet',
          description: 'Assets, liabilities, and equity overview',
          href: '/reports/financial/balance-sheet',
          status: 'available'
        },
        {
          id: 'cash-flow',
          title: 'Cash Flow Statement',
          description: 'Operating, investing, and financing cash flows',
          href: '/reports/financial/cash-flow',
          status: 'available'
        },
        {
          id: 'ar-aging',
          title: 'Accounts Receivable Aging',
          description: 'Customer payment status and overdue analysis',
          href: '/reports/financial/ar-aging',
          status: 'available'
        },
        {
          id: 'ap-aging',
          title: 'Accounts Payable Aging',
          description: 'Supplier payment obligations and aging',
          href: '/reports/financial/ap-aging',
          status: 'available'
        }
      ]
    },
    {
      id: 'operational',
      title: 'Operational Reports',
      description: 'Production, inventory, quality, and logistics analytics',
      icon: Factory,
      color: 'text-blue-600 bg-blue-100',
      reports: [
        {
          id: 'inventory-levels',
          title: 'Inventory Levels Report',
          description: 'Stock levels, turnover, and reorder analysis',
          href: '/reports/operational/inventory',
          status: 'available'
        },
        {
          id: 'production-status',
          title: 'Production Status Report',
          description: 'Work orders, completion rates, and efficiency',
          href: '/reports/operational/production',
          status: 'available'
        },
        {
          id: 'quality-metrics',
          title: 'Quality Control Metrics',
          description: 'Inspection results, defect rates, and compliance',
          href: '/reports/operational/quality',
          status: 'available'
        },
        {
          id: 'shipping-analytics',
          title: 'Shipping & Logistics',
          description: 'Delivery performance and logistics analytics',
          href: '/reports/operational/shipping',
          status: 'available'
        }
      ]
    },
    {
      id: 'management',
      title: 'Management Reports',
      description: 'KPIs, performance metrics, and strategic insights',
      icon: Target,
      color: 'text-purple-600 bg-purple-100',
      reports: [
        {
          id: 'kpi-dashboard',
          title: 'Executive KPI Dashboard',
          description: 'Key performance indicators and business metrics',
          href: '/reports/management/kpis',
          status: 'available'
        },
        {
          id: 'contract-analysis',
          title: 'Contract Performance Analysis',
          description: 'Sales and purchase contract profitability',
          href: '/reports/management/contracts',
          status: 'available'
        },
        {
          id: 'customer-analytics',
          title: 'Customer Analytics',
          description: 'Customer behavior, satisfaction, and retention',
          href: '/reports/management/customers',
          status: 'available'
        },
        {
          id: 'supplier-performance',
          title: 'Supplier Performance',
          description: 'Supplier quality, delivery, and cost analysis',
          href: '/reports/management/suppliers',
          status: 'available'
        }
      ]
    }
  ]

  return (
    <div className="space-y-8">
      {/* Executive Dashboard Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Executive Dashboard</h2>
          <p className="text-muted-foreground">
            Real-time business intelligence and key performance indicators
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Executive KPI Cards */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Target className="h-5 w-5 text-blue-600" />
          Key Performance Indicators
        </h3>
        {metrics ? (
          <KPICardsGrid
            metrics={{
              totalRevenue: metrics.quickMetrics.totalRevenue,
              totalExpenses: metrics.quickMetrics.totalExpenses,
              activeContracts: metrics.quickMetrics.activeContracts,
              completedWorkOrders: metrics.quickMetrics.completedWorkOrders,
              pendingInspections: metrics.quickMetrics.pendingInspections,
              currentInventoryValue: metrics.quickMetrics.currentInventoryValue,
              // Calculate growth rates (simplified - in real implementation, these would come from API)
              revenueGrowth: 12.5,
              expenseGrowth: 8.3,
              contractGrowth: 15.2,
              completionRate: 87.4
            }}
            loading={loading}
          />
        ) : (
          <KPICardsGrid
            metrics={{
              totalRevenue: 0,
              totalExpenses: 0,
              activeContracts: 0,
              completedWorkOrders: 0,
              pendingInspections: 0,
              currentInventoryValue: 0
            }}
            loading={loading}
          />
        )}
      </div>

      {/* Visual Analytics Charts */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-green-600" />
          Business Analytics
        </h3>
        <div className="grid gap-6 md:grid-cols-2">
          {/* Revenue Trend Chart */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                Revenue Trend
              </CardTitle>
              <CardDescription>Monthly revenue performance</CardDescription>
            </CardHeader>
            <CardContent>
              <RevenueTrendChart
                data={metrics?.revenueHistory || [
                  { name: 'Jan', value: (metrics?.quickMetrics.totalRevenue || 0) * 0.8 },
                  { name: 'Feb', value: (metrics?.quickMetrics.totalRevenue || 0) * 0.85 },
                  { name: 'Mar', value: (metrics?.quickMetrics.totalRevenue || 0) * 0.9 },
                  { name: 'Apr', value: (metrics?.quickMetrics.totalRevenue || 0) * 0.95 },
                  { name: 'May', value: (metrics?.quickMetrics.totalRevenue || 0) * 1.0 },
                  { name: 'Jun', value: (metrics?.quickMetrics.totalRevenue || 0) }
                ]}
                dataKey="value"
                color="#10b981"
                height={250}
              />
            </CardContent>
          </Card>

          {/* AR/AP Aging Chart */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <PieChart className="h-4 w-4 text-blue-600" />
                AR Aging Analysis
              </CardTitle>
              <CardDescription>Accounts receivable aging breakdown</CardDescription>
            </CardHeader>
            <CardContent>
              <AgingPieChart
                data={metrics?.arAging || [
                  { name: 'Current (0-30)', value: (metrics?.quickMetrics.totalRevenue || 0) * 0.5 },
                  { name: '31-60 days', value: (metrics?.quickMetrics.totalRevenue || 0) * 0.25 },
                  { name: '61-90 days', value: (metrics?.quickMetrics.totalRevenue || 0) * 0.15 },
                  { name: '90+ days', value: (metrics?.quickMetrics.totalRevenue || 0) * 0.1 }
                ]}
                colors={['#10b981', '#f59e0b', '#ef4444', '#8b5cf6']}
                height={250}
              />
            </CardContent>
          </Card>

          {/* Production Efficiency Chart */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Factory className="h-4 w-4 text-purple-600" />
                Production Efficiency
              </CardTitle>
              <CardDescription>Work order completion rates by department</CardDescription>
            </CardHeader>
            <CardContent>
              <ProductionBarChart
                data={[
                  { name: 'Assembly', value: 92 },
                  { name: 'Quality', value: 88 },
                  { name: 'Packaging', value: 95 },
                  { name: 'Shipping', value: 87 }
                ]}
                dataKey="value"
                color="#8b5cf6"
                height={250}
              />
            </CardContent>
          </Card>

          {/* Revenue vs Expenses Comparison */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Activity className="h-4 w-4 text-indigo-600" />
                Revenue vs Expenses
              </CardTitle>
              <CardDescription>Monthly financial performance comparison</CardDescription>
            </CardHeader>
            <CardContent>
              <MultiLineChart
                data={metrics?.financialComparison || [
                  { name: 'Jan', revenue: (metrics?.quickMetrics.totalRevenue || 0) * 0.8, expenses: (metrics?.quickMetrics.totalExpenses || 0) * 0.8 },
                  { name: 'Feb', revenue: (metrics?.quickMetrics.totalRevenue || 0) * 0.85, expenses: (metrics?.quickMetrics.totalExpenses || 0) * 0.85 },
                  { name: 'Mar', revenue: (metrics?.quickMetrics.totalRevenue || 0) * 0.9, expenses: (metrics?.quickMetrics.totalExpenses || 0) * 0.9 },
                  { name: 'Apr', revenue: (metrics?.quickMetrics.totalRevenue || 0) * 0.95, expenses: (metrics?.quickMetrics.totalExpenses || 0) * 0.95 },
                  { name: 'May', revenue: (metrics?.quickMetrics.totalRevenue || 0) * 1.0, expenses: (metrics?.quickMetrics.totalExpenses || 0) * 1.0 },
                  { name: 'Jun', revenue: (metrics?.quickMetrics.totalRevenue || 0), expenses: (metrics?.quickMetrics.totalExpenses || 0) }
                ]}
                lines={[
                  { dataKey: 'revenue', color: '#10b981', name: 'Revenue' },
                  { dataKey: 'expenses', color: '#ef4444', name: 'Expenses' }
                ]}
                height={250}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Stats Overview */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Activity className="h-5 w-5 text-purple-600" />
          System Overview
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Available Reports</p>
                  <p className="text-2xl font-bold">{metrics?.summary.totalReports || 14}</p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Report Categories</p>
                  <p className="text-2xl font-bold">{metrics?.summary.availableCategories || 3}</p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                  <PieChart className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Recent Activity</p>
                  <p className="text-2xl font-bold">{metrics?.recentActivity.newInvoices || 0}</p>
                  <p className="text-xs text-muted-foreground">New invoices</p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Real-time Data</p>
                  <p className="text-2xl font-bold">Live</p>
                  <p className="text-xs text-muted-foreground">
                    Updated: {metrics ? new Date(metrics.summary.lastUpdated).toLocaleTimeString() : 'Loading...'}
                  </p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-orange-100 flex items-center justify-center">
                  <Activity className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Detailed Reports Section */}
      <div>
        <h3 className="text-lg font-semibold mb-6 flex items-center gap-2">
          <FileText className="h-5 w-5 text-gray-600" />
          Detailed Reports & Analytics
        </h3>
        <p className="text-muted-foreground mb-6">
          Access comprehensive reports for in-depth analysis and decision-making
        </p>
      </div>

      {/* Report Categories */}
      {reportCategories.map((category) => (
        <div key={category.id} className="space-y-4">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 rounded-lg ${category.color} flex items-center justify-center`}>
              <category.icon className="h-5 w-5" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">{category.title}</h2>
              <p className="text-muted-foreground">{category.description}</p>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {category.reports.map((report) => (
              <Card key={report.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-base">{report.title}</CardTitle>
                      <CardDescription className="text-sm">
                        {report.description}
                      </CardDescription>
                    </div>
                    <Badge
                      variant={report.status === 'available' ? 'default' : 'secondary'}
                      className="ml-2"
                    >
                      {report.status === 'available' ? 'Available' : 'Coming Soon'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center justify-between">
                    {report.lastUpdated && (
                      <p className="text-xs text-muted-foreground">
                        Updated: {report.lastUpdated}
                      </p>
                    )}
                    <div className="flex gap-2">
                      {report.status === 'available' ? (
                        <Button asChild size="sm">
                          <Link href={report.href} className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            View Report
                          </Link>
                        </Button>
                      ) : (
                        <Button size="sm" variant="outline" disabled>
                          Coming Soon
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ))}

      {/* Export Capabilities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Capabilities
          </CardTitle>
          <CardDescription>
            All reports support multiple export formats for flexible data sharing and analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <FileText className="h-8 w-8 text-red-500" />
              <div>
                <p className="font-medium">PDF Reports</p>
                <p className="text-sm text-muted-foreground">Professional layouts</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <BarChart3 className="h-8 w-8 text-green-500" />
              <div>
                <p className="font-medium">Excel Export</p>
                <p className="text-sm text-muted-foreground">Data analysis ready</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <FileText className="h-8 w-8 text-blue-500" />
              <div>
                <p className="font-medium">CSV Data</p>
                <p className="text-sm text-muted-foreground">Raw data export</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <Activity className="h-8 w-8 text-purple-500" />
              <div>
                <p className="font-medium">Print Layouts</p>
                <p className="text-sm text-muted-foreground">Optimized printing</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
