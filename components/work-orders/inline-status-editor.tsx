"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Clock, AlertTriangle, CheckCircle, XCircle, Loader2, Check, X } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { QualityGateModal } from "./quality-gate-modal"

interface InlineStatusEditorProps {
  workOrderId: string
  currentStatus: string
  onStatusChange: (newStatus: string) => void
  disabled?: boolean
  // Quality gate modal props
  workOrderNumber?: string
  productName?: string
  productSku?: string
}

const STATUS_OPTIONS = [
  { value: "pending", label: "Pending", icon: Clock, variant: "outline" as const },
  { value: "in_progress", label: "In Progress", icon: AlertTriangle, variant: "secondary" as const },
  { value: "completed", label: "Completed", icon: CheckCircle, variant: "default" as const },
  { value: "on_hold", label: "On Hold", icon: XCircle, variant: "destructive" as const },
]

export function InlineStatusEditor({
  workOrderId,
  currentStatus,
  onStatusChange,
  disabled = false,
  workOrderNumber = "",
  productName = "",
  productSku = ""
}: InlineStatusEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [tempStatus, setTempStatus] = useState(currentStatus)
  const [showQualityGateModal, setShowQualityGateModal] = useState(false)
  const [qualityInspections, setQualityInspections] = useState<any[]>([])
  const [canCompleteWorkOrder, setCanCompleteWorkOrder] = useState(false)
  const { success: toastSuccess, error: toastError } = useSafeToast()

  const currentStatusConfig = STATUS_OPTIONS.find(s => s.value === currentStatus) || STATUS_OPTIONS[0]
  const StatusIcon = currentStatusConfig.icon

  const handleStatusUpdate = async (newStatus: string) => {
    if (newStatus === currentStatus) {
      setIsEditing(false)
      return
    }

    // ✅ PROFESSIONAL ERP: Check quality gate for completion
    if (newStatus === "completed") {
      const canComplete = await checkQualityGate()
      if (!canComplete) {
        setIsEditing(false)
        setShowQualityGateModal(true)
        return
      }
    }

    setIsUpdating(true)

    try {
      // ✅ PROFESSIONAL ERP: Optimistic update with rollback
      const originalStatus = currentStatus
      onStatusChange(newStatus) // Optimistic update

      // ✅ SIMPLE STATUS UPDATE: Clean status change without complex integration
      const requestBody = { status: newStatus }

      const response = await fetch(`/api/production/work-orders/${workOrderId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        // ✅ PROFESSIONAL ERP: Handle quality gate validation errors (422)
        if (response.status === 422 && newStatus === "completed") {
          console.log("🔍 Quality gate validation failed - showing modal")
          await checkQualityGate() // Load inspection data
          setShowQualityGateModal(true)
          return // Don't throw error, just show modal
        }

        throw new Error(errorData.error || "Failed to update status")
      }

      const newStatusConfig = STATUS_OPTIONS.find(s => s.value === newStatus)
      toastSuccess(`Status updated to ${newStatusConfig?.label || newStatus}`)

    } catch (error) {
      console.error("Status update failed:", error)
      onStatusChange(currentStatus) // Rollback on error

      // ✅ PROFESSIONAL ERP: Handle quality gate errors gracefully
      if (error instanceof Error && (
        error.message.includes("Quality approval required") ||
        error.message.includes("Business Logic Error")
      )) {
        console.log("🔍 Detected quality gate error - showing modal")
        await checkQualityGate() // Load inspection data
        setShowQualityGateModal(true)
      } else {
        toastError("Failed to update status")
      }
    } finally {
      setIsUpdating(false)
      setIsEditing(false)
    }
  }

  const checkQualityGate = async (): Promise<boolean> => {
    try {
      console.log(`🔍 Checking quality gate for work order ${workOrderId}`)
      const response = await fetch(`/api/production/work-orders/${workOrderId}/quality-gate`)
      if (response.ok) {
        const data = await response.json()
        console.log(`🔍 Quality gate response:`, data)
        setQualityInspections(data.qualityInspections || [])
        setCanCompleteWorkOrder(data.canComplete || false)
        return data.canComplete || false
      } else {
        console.error(`❌ Quality gate API error: ${response.status}`)
      }
      return false
    } catch (error) {
      console.error("Quality gate check failed:", error)
      return false
    }
  }

  const handleForceComplete = async () => {
    setIsUpdating(true)
    try {
      onStatusChange("completed")

      const response = await fetch(`/api/production/work-orders/${workOrderId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "complete_production" }),
      })

      if (!response.ok) {
        throw new Error("Failed to complete work order")
      }

      toastSuccess("Work order completed successfully")
      setShowQualityGateModal(false)
    } catch (error) {
      console.error("Force complete failed:", error)
      onStatusChange(currentStatus)
      toastError("Failed to complete work order")
    } finally {
      setIsUpdating(false)
    }
  }

  const handleCancel = () => {
    setTempStatus(currentStatus)
    setIsEditing(false)
  }

  if (disabled || isUpdating) {
    return (
      <Badge variant={currentStatusConfig.variant} className="relative">
        {isUpdating && <Loader2 className="mr-1 h-3 w-3 animate-spin" />}
        <StatusIcon className="mr-1 h-3 w-3" />
        {currentStatusConfig.label}
      </Badge>
    )
  }

  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <Select value={tempStatus} onValueChange={setTempStatus}>
          <SelectTrigger className="w-32 h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {STATUS_OPTIONS.map((status) => {
              const Icon = status.icon
              return (
                <SelectItem key={status.value} value={status.value}>
                  <div className="flex items-center gap-2">
                    <Icon className="h-3 w-3" />
                    {status.label}
                  </div>
                </SelectItem>
              )
            })}
          </SelectContent>
        </Select>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => handleStatusUpdate(tempStatus)}
          className="h-8 w-8 p-0"
        >
          <Check className="h-3 w-3" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={handleCancel}
          className="h-8 w-8 p-0"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    )
  }

  return (
    <>
      <Badge
        variant={currentStatusConfig.variant}
        className="cursor-pointer hover:opacity-80 transition-opacity"
        onClick={() => setIsEditing(true)}
      >
        <StatusIcon className="mr-1 h-3 w-3" />
        {currentStatusConfig.label}
      </Badge>

      {/* Quality Gate Modal */}
      <QualityGateModal
        isOpen={showQualityGateModal}
        onClose={() => setShowQualityGateModal(false)}
        workOrderNumber={workOrderNumber}
        productName={productName}
        productSku={productSku}
        qualityInspections={qualityInspections}
        canComplete={canCompleteWorkOrder} // Use actual API response
        onForceComplete={canCompleteWorkOrder ? handleForceComplete : undefined}
      />
    </>
  )
}
