"use client"

import { useState, useEffect } from "react"
import { CheckCircle, XCircle, Clock, AlertTriangle, Edit, ArrowLeft, Calendar, User, Package, Building2, <PERSON>tings, FileText, Wrench, Play } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import Link from "next/link"

interface WorkOrderDetailProps {
  workOrder: any
  onRefresh: () => void
  loading?: boolean
}

export function WorkOrderDetail({ workOrder, onRefresh, loading = false }: WorkOrderDetailProps) {
  const { success: toastSuccess, error: toastError } = useSafeToast()
  const { t } = useI18n()

  // ✅ MATERIAL REQUIREMENTS STATE
  const [materialRequirements, setMaterialRequirements] = useState<any[]>([])
  const [materialsLoading, setMaterialsLoading] = useState(false)

  // ✅ STATUS MANAGEMENT
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4" />
      case "in_progress":
        return <AlertTriangle className="h-4 w-4" />
      case "pending":
        return <Clock className="h-4 w-4" />
      default:
        return <XCircle className="h-4 w-4" />
    }
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "completed":
        return "default"
      case "in_progress":
        return "secondary"
      case "pending":
        return "outline"
      default:
        return "destructive"
    }
  }

  const formatStatus = (status: string) => {
    return status?.replace("_", " ") || "Unknown"
  }

  // ✅ FETCH MATERIAL REQUIREMENTS
  const fetchMaterialRequirements = async () => {
    if (!workOrder?.product?.id) return

    try {
      setMaterialsLoading(true)
      console.log("Fetching BOM for product:", workOrder.product.id)
      console.log("Work order quantity:", workOrder.qty)

      const response = await fetch(`/api/products/${workOrder.product.id}/bom`)
      console.log("BOM API response status:", response.status)

      if (response.ok) {
        const data = await response.json()
        console.log("BOM API response data:", data)

        const bomItems = Array.isArray(data) ? data : (data.bomItems || [])
        console.log("BOM items:", bomItems)

        // Calculate total requirements based on work order quantity
        const requirements = bomItems.map((item: any) => {
          const quantityRequired = parseFloat(item.qty_required) || 0  // ✅ FIXED: Use qty_required and parse as float
          const workOrderQty = workOrder.qty || 0
          const totalRequired = quantityRequired * workOrderQty

          console.log(`Material ${item.rawMaterial?.name}: ${quantityRequired} per unit × ${workOrderQty} units = ${totalRequired} total`)

          return {
            ...item,
            totalRequired: totalRequired,
            quantity_required: quantityRequired  // ✅ Add normalized field for display
          }
        })

        console.log("Final requirements:", requirements)
        setMaterialRequirements(requirements)
      } else {
        console.error("BOM API failed:", response.status, response.statusText)
      }
    } catch (error) {
      console.error("Failed to fetch material requirements:", error)
      toastError("Failed to load material requirements")
    } finally {
      setMaterialsLoading(false)
    }
  }

  // ✅ LOAD MATERIAL REQUIREMENTS ON MOUNT
  useEffect(() => {
    fetchMaterialRequirements()
  }, [workOrder?.product?.id, workOrder?.qty])

  // ✅ COMPLETE PRODUCTION WITH MATERIAL CONSUMPTION
  const handleCompleteProduction = async () => {
    try {
      const response = await fetch(`/api/production/work-orders/${workOrder.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "complete_production",
          completed_qty: workOrder.qty
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to complete production")
      }

      toastSuccess("Production completed successfully! Materials consumed automatically.")
      onRefresh() // Refresh the work order data

    } catch (error) {
      console.error("Complete production failed:", error)
      toastError(error instanceof Error ? error.message : "Failed to complete production")
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="grid gap-6 md:grid-cols-2">
          <div className="h-64 bg-gray-200 rounded animate-pulse" />
          <div className="h-64 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* ✅ HEADER WITH ACTIONS */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/production">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Work Orders
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{workOrder.number}</h1>
            <p className="text-muted-foreground">Work Order Details</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {/* ✅ COMPLETE PRODUCTION BUTTON - Only show if work order is not completed */}
          {workOrder.status !== "completed" && (
            <Button
              variant="default"
              size="sm"
              onClick={handleCompleteProduction}
              className="bg-green-600 hover:bg-green-700"
            >
              <Play className="h-4 w-4 mr-2" />
              Complete Production
            </Button>
          )}
          <Button variant="outline" size="sm" asChild>
            <Link href={`/production/${workOrder.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      {/* ✅ MAIN CONTENT GRID */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* ✅ WORK ORDER OVERVIEW */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Work Order Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Status:</span>
                <Badge variant={getStatusVariant(workOrder.status)}>
                  {getStatusIcon(workOrder.status)}
                  <span className="ml-1">{formatStatus(workOrder.status)}</span>
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Quantity:</span>
                <span className="text-sm">{workOrder.qty}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Due Date:</span>
                <span className="text-sm">{workOrder.due_date || "Not set"}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Created:</span>
                <span className="text-sm">
                  {workOrder.created_at ? new Date(workOrder.created_at).toLocaleDateString() : "N/A"}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* ✅ SALES CONTRACT INFORMATION */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Sales Contract
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {workOrder.salesContract ? (
              <div className="grid gap-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Contract:</span>
                  <Link
                    href={`/sales-contracts/${workOrder.salesContract.id}`}
                    className="text-sm text-blue-600 hover:underline"
                  >
                    {workOrder.salesContract.number}
                  </Link>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Customer:</span>
                  <span className="text-sm">
                    {workOrder.salesContract.customer?.name || "N/A"}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Contract Date:</span>
                  <span className="text-sm">
                    {workOrder.salesContract.date || "N/A"}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge variant="outline">
                    {workOrder.salesContract.status || "N/A"}
                  </Badge>
                </div>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No sales contract linked</p>
            )}
          </CardContent>
        </Card>

        {/* ✅ PRODUCT INFORMATION */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Product Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {workOrder.product ? (
              <div className="grid gap-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">SKU:</span>
                  <span className="text-sm font-mono">{workOrder.product.sku}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Name:</span>
                  <span className="text-sm">{workOrder.product.name}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Unit:</span>
                  <span className="text-sm">{workOrder.product.unit}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Category:</span>
                  <span className="text-sm">{workOrder.product.category || "N/A"}</span>
                </div>
                {workOrder.product.description && (
                  <div className="pt-2 border-t">
                    <span className="text-sm font-medium">Description:</span>
                    <p className="text-sm text-muted-foreground mt-1">
                      {workOrder.product.description}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No product information</p>
            )}
          </CardContent>
        </Card>

        {/* ✅ QUALITY INSPECTIONS */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Quality Inspections
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {workOrder.qualityInspections && workOrder.qualityInspections.length > 0 ? (
              <div className="space-y-3">
                {workOrder.qualityInspections.map((inspection: any) => (
                  <div key={inspection.id} className="flex justify-between items-center p-3 border rounded">
                    <div>
                      <p className="text-sm font-medium">{inspection.inspection_type}</p>
                      <p className="text-xs text-muted-foreground">Inspector: {inspection.inspector}</p>
                    </div>
                    <Badge variant={getStatusVariant(inspection.status)}>
                      {inspection.status}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No quality inspections</p>
            )}
          </CardContent>
        </Card>

        {/* ✅ MATERIAL REQUIREMENTS */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              Material Requirements
            </CardTitle>
            <CardDescription>
              Raw materials needed for this work order
            </CardDescription>
          </CardHeader>
          <CardContent>
            {materialsLoading ? (
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
              </div>
            ) : materialRequirements.length > 0 ? (
              <div className="space-y-4">
                {materialRequirements.map((requirement: any) => (
                  <div key={requirement.id} className="flex justify-between items-center p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <div>
                          <p className="font-medium">{requirement.rawMaterial?.name || 'Unknown Material'}</p>
                          <p className="text-sm text-muted-foreground">
                            SKU: {requirement.rawMaterial?.sku || 'N/A'}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {requirement.totalRequired} {requirement.rawMaterial?.unit || 'units'}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {requirement.quantity_required} per unit × {workOrder.qty} units
                      </p>
                    </div>
                  </div>
                ))}
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Total Materials Required:</strong> {materialRequirements.length} different materials
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Wrench className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  No BOM defined for this product
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Add materials to the product's Bill of Materials to see requirements here
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
