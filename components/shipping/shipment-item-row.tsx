/**
 * Manufacturing ERP - Shipment Item Row Component
 * 
 * Professional individual item row with product selection, quantity input,
 * pricing, and validation for manual shipment creation.
 */

"use client"

import { Control } from "react-hook-form"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Badge } from "@/components/ui/badge"
import { Trash2, Package, DollarSign } from "lucide-react"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { useMemo } from "react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface Product {
  id: string
  name: string
  sku: string
  unit: string
  image: string | null
  // ✅ PRICING FIELDS: For auto-population
  price?: string | null
  base_price?: string | null
  cost_price?: string | null
  currency?: string | null
}

interface ShipmentItem {
  product_id: string
  quantity: string
  unit_price: string
}

interface FormData {
  items: ShipmentItem[]
  [key: string]: any
}

interface ShipmentItemRowProps {
  index: number
  products: Product[]
  form: {
    control: Control<FormData>
    watch: (name: string) => any
    setValue: (name: string, value: any) => void
  }
  onRemove: () => void
  canRemove: boolean
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function ShipmentItemRow({
  index,
  products,
  form,
  onRemove,
  canRemove
}: ShipmentItemRowProps) {
  const watchItem = form.watch(`items.${index}`)
  const selectedProduct = products.find(p => p.id === watchItem?.product_id)

  // ✅ SEARCHABLE OPTIONS: Convert products to searchable options
  const productOptions: SearchableSelectOption[] = useMemo(() => {
    return products.map((product) => {
      const priceDisplay = product.price ? ` • $${product.price}` : ""
      const unitDisplay = product.unit ? ` (${product.unit})` : ""

      return {
        value: product.id,
        label: product.name,
        subtitle: `SKU: ${product.sku}${unitDisplay}${priceDisplay}`,
        description: product.image ? `📷 Image available` : undefined,
      }
    })
  }, [products])

  // ✅ AUTO-POPULATE PRICE: When product is selected, populate unit price
  const handleProductChange = (productId: string) => {
    form.setValue(`items.${index}.product_id`, productId)

    // Find the selected product and auto-populate price
    const product = products.find(p => p.id === productId)
    if (product) {
      // Use pricing hierarchy: base_price > price > cost_price
      const unitPrice = product.base_price || product.price || product.cost_price || ""
      if (unitPrice) {
        form.setValue(`items.${index}.unit_price`, unitPrice)
      }
    }
  }

  // Calculate line total
  const calculateLineTotal = () => {
    if (watchItem?.quantity && watchItem?.unit_price) {
      const quantity = parseFloat(watchItem.quantity) || 0
      const price = parseFloat(watchItem.unit_price) || 0
      return quantity * price
    }
    return 0
  }

  const lineTotal = calculateLineTotal()

  return (
    <div className="grid grid-cols-12 gap-3 p-4 border rounded-lg bg-muted/20">
      {/* Product Selection - 5 columns */}
      <div className="col-span-5">
        <FormField
          control={form.control}
          name={`items.${index}.product_id`}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-xs">Product</FormLabel>
              <FormControl>
                <SearchableSelect
                  options={productOptions}
                  value={field.value}
                  onValueChange={handleProductChange}
                  placeholder="Search products..."
                  searchPlaceholder="Search by name or SKU..."
                  emptyMessage="No products found."
                  allowClear={true}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Quantity - 2 columns */}
      <div className="col-span-2">
        <FormField
          control={form.control}
          name={`items.${index}.quantity`}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-xs">Quantity</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  step="1"
                  placeholder="0"
                  {...field}
                  onChange={(e) => {
                    field.onChange(e.target.value)
                  }}
                />
              </FormControl>
              <FormMessage />
              {selectedProduct && (
                <div className="text-xs text-muted-foreground mt-1">
                  per {selectedProduct.unit}
                </div>
              )}
            </FormItem>
          )}
        />
      </div>

      {/* Unit Price - 2 columns */}
      <div className="col-span-2">
        <FormField
          control={form.control}
          name={`items.${index}.unit_price`}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-xs">Unit Price</FormLabel>
              <FormControl>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    placeholder="0.00"
                    className="pl-9"
                    {...field}
                    onChange={(e) => {
                      field.onChange(e.target.value)
                    }}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Line Total & Actions - 3 columns */}
      <div className="col-span-3 flex items-end justify-between">
        <div className="flex-1">
          <div className="text-xs text-muted-foreground mb-1">Line Total</div>
          <div className="font-medium">
            {lineTotal > 0 ? `$${lineTotal.toFixed(2)}` : '$0.00'}
          </div>
        </div>

        {/* Remove Button */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onRemove}
          disabled={!canRemove}
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      {/* Product Details (when selected) */}
      {selectedProduct && (
        <div className="col-span-12 pt-2 border-t border-muted">
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <span className="font-medium">SKU:</span>
              <span>{selectedProduct.sku}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-medium">Unit:</span>
              <span>{selectedProduct.unit}</span>
            </div>
            {watchItem?.quantity && (
              <div className="flex items-center gap-1">
                <span className="font-medium">Total:</span>
                <span>{watchItem.quantity} {selectedProduct.unit}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
