/**
 * Manufacturing ERP - Shipping Location Selector Component
 * 
 * Professional location selection component with capacity visualization,
 * optimization suggestions, and real-time validation.
 */

"use client"

import { useState, useEffect } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  MapPin, 
  Truck, 
  Ship, 
  Plane, 
  Package, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  DollarSign,
  Zap
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ShippingLocation {
  locationId: string
  locationName: string
  locationType: string
  icon: string
  capacity: number
  availableCapacity: number
  utilizationPercentage: number
  shippingMethods: string[]
  attributes: {
    dockType?: string
    maxVehicleSize?: string
    temperatureControlled: boolean
    securityLevel: string
  }
  estimatedProcessingTime: number
  costFactor: number
  status?: 'normal' | 'warning' | 'critical' | 'full'
}

interface LocationSelectorProps {
  shippingMethod: string
  selectedLocationId?: string
  onLocationSelect: (locationId: string, location: ShippingLocation) => void
  showCapacityInfo?: boolean
  showOptimization?: boolean
  requiredCapacity?: number
  className?: string
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function LocationSelector({
  shippingMethod,
  selectedLocationId,
  onLocationSelect,
  showCapacityInfo = true,
  showOptimization = false,
  requiredCapacity,
  className = ""
}: LocationSelectorProps) {
  const [locations, setLocations] = useState<ShippingLocation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [optimizedLocation, setOptimizedLocation] = useState<string | null>(null)

  // Fetch available locations
  useEffect(() => {
    fetchLocations()
  }, [shippingMethod])

  const fetchLocations = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        shipping_method: shippingMethod,
        include_utilization: 'true',
        include_capacity: 'true'
      })

      const response = await fetch(`/api/shipping/locations?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch shipping locations')
      }

      const data = await response.json()
      const locationsWithStatus = data.locations.map((loc: any) => ({
        ...loc,
        status: getLocationStatus(loc.utilizationPercentage)
      }))

      setLocations(locationsWithStatus)

      // Auto-select first available location if none selected
      if (!selectedLocationId && locationsWithStatus.length > 0) {
        const bestLocation = locationsWithStatus[0]
        onLocationSelect(bestLocation.locationId, bestLocation)
      }

    } catch (error) {
      console.error('Error fetching locations:', error)
      setError(error instanceof Error ? error.message : 'Failed to load locations')
    } finally {
      setLoading(false)
    }
  }

  const getLocationStatus = (utilization: number): 'normal' | 'warning' | 'critical' | 'full' => {
    if (utilization >= 100) return 'full'
    if (utilization >= 90) return 'critical'
    if (utilization >= 75) return 'warning'
    return 'normal'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-orange-600'
      case 'full': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'critical': return <AlertTriangle className="h-4 w-4 text-orange-600" />
      case 'full': return <AlertTriangle className="h-4 w-4 text-red-600" />
      default: return <MapPin className="h-4 w-4 text-gray-600" />
    }
  }

  const getShippingMethodIcon = (method: string) => {
    switch (method) {
      case 'sea_freight': return <Ship className="h-4 w-4" />
      case 'air_freight': return <Plane className="h-4 w-4" />
      case 'truck': return <Truck className="h-4 w-4" />
      case 'express': return <Zap className="h-4 w-4" />
      default: return <Package className="h-4 w-4" />
    }
  }

  const selectedLocation = locations.find(loc => loc.locationId === selectedLocationId)

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-10 bg-gray-200 rounded"></div>
          {showCapacityInfo && (
            <div className="mt-4 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-2 bg-gray-200 rounded"></div>
            </div>
          )}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Location Selection */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Pickup Location</label>
        <Select
          value={selectedLocationId || ""}
          onValueChange={(value) => {
            const location = locations.find(loc => loc.locationId === value)
            if (location) {
              onLocationSelect(value, location)
            }
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select pickup location" />
          </SelectTrigger>
          <SelectContent>
            {locations.map((location) => (
              <SelectItem key={location.locationId} value={location.locationId}>
                <div className="flex items-center gap-2 w-full">
                  <span className="text-lg">{location.icon}</span>
                  <div className="flex-1">
                    <div className="font-medium">{location.locationName}</div>
                    <div className="text-xs text-muted-foreground flex items-center gap-2">
                      {getStatusIcon(location.status || 'normal')}
                      <span>
                        {location.availableCapacity} / {location.capacity} available
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {location.locationType.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Selected Location Details */}
      {selectedLocation && showCapacityInfo && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <span className="text-lg">{selectedLocation.icon}</span>
              {selectedLocation.locationName}
              {getStatusIcon(selectedLocation.status || 'normal')}
            </CardTitle>
            <CardDescription>
              {selectedLocation.locationType.replace('_', ' ')} • {selectedLocation.attributes.securityLevel} security
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Capacity Information */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Capacity Utilization</span>
                <span className={getStatusColor(selectedLocation.status || 'normal')}>
                  {Math.round(selectedLocation.utilizationPercentage)}%
                </span>
              </div>
              <Progress 
                value={selectedLocation.utilizationPercentage} 
                className="h-2"
              />
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>{selectedLocation.availableCapacity} available</span>
                <span>{selectedLocation.capacity} total</span>
              </div>
            </div>

            {/* Location Attributes */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>{selectedLocation.estimatedProcessingTime}h processing</span>
              </div>
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span>{selectedLocation.costFactor}x cost factor</span>
              </div>
            </div>

            {/* Shipping Methods */}
            <div className="space-y-2">
              <span className="text-sm font-medium">Supported Methods</span>
              <div className="flex flex-wrap gap-2">
                {selectedLocation.shippingMethods.map((method) => (
                  <Badge 
                    key={method} 
                    variant={method === shippingMethod ? "default" : "outline"}
                    className="text-xs"
                  >
                    {getShippingMethodIcon(method)}
                    <span className="ml-1">{method.replace('_', ' ')}</span>
                  </Badge>
                ))}
              </div>
            </div>

            {/* Capacity Warning */}
            {requiredCapacity && selectedLocation.availableCapacity < requiredCapacity && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Insufficient capacity. Required: {requiredCapacity}, Available: {selectedLocation.availableCapacity}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Optimization Suggestion */}
      {showOptimization && optimizedLocation && optimizedLocation !== selectedLocationId && (
        <Alert>
          <Zap className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span>A more optimal location is available</span>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  const location = locations.find(loc => loc.locationId === optimizedLocation)
                  if (location) {
                    onLocationSelect(optimizedLocation, location)
                  }
                }}
              >
                Use Optimal
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
