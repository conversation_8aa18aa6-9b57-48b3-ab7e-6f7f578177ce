/**
 * Manufacturing ERP - Shipment Items Manager Component
 * 
 * Professional items management for manual shipments with product selection,
 * quantity validation, and dynamic item addition/removal.
 */

"use client"

import { useFieldArray, Control } from "react-hook-form"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Plus, Package, Trash2 } from "lucide-react"
import { ShipmentItemRow } from "./shipment-item-row"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface Product {
  id: string
  name: string
  sku: string
  unit: string
  image: string | null
  // ✅ PRICING FIELDS: For auto-population
  price?: string | null
  base_price?: string | null
  cost_price?: string | null
  currency?: string | null
}

interface ShipmentItem {
  product_id: string
  quantity: string
  unit_price: string
}

interface ShipmentItemsManagerProps {
  products: Product[]
  form: {
    control: Control<any>
    watch: (name: string) => any
    setValue: (name: string, value: any) => void
    getValues: (name?: string) => any
  }
  watchSource: "contract" | "manual"
  className?: string
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function ShipmentItemsManager({
  products,
  form,
  watchSource,
  className = ""
}: ShipmentItemsManagerProps) {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items"
  })

  const watchItems = form.watch("items")

  // Add new item
  const addItem = () => {
    append({
      product_id: "",
      quantity: "",
      unit_price: ""
    })
  }

  // Remove item
  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  // Calculate totals
  const calculateTotals = () => {
    let totalItems = 0
    let totalValue = 0

    watchItems?.forEach((item: ShipmentItem) => {
      if (item.quantity && item.unit_price) {
        const quantity = parseFloat(item.quantity) || 0
        const price = parseFloat(item.unit_price) || 0
        totalItems += quantity
        totalValue += quantity * price
      }
    })

    return { totalItems, totalValue }
  }

  const { totalItems, totalValue } = calculateTotals()

  // Only show for manual shipments
  if (watchSource !== "manual") {
    return null
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Shipment Items
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {fields.length} item{fields.length !== 1 ? 's' : ''}
            </Badge>
            {totalItems > 0 && (
              <Badge variant="outline">
                {totalItems} units
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Items List */}
        <div className="space-y-3">
          {fields.map((field, index) => (
            <ShipmentItemRow
              key={field.id}
              index={index}
              products={products}
              form={form}
              onRemove={() => removeItem(index)}
              canRemove={fields.length > 1}
            />
          ))}
        </div>

        {/* Add Item Button */}
        <div className="flex items-center justify-between pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={addItem}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Item
          </Button>

          {/* Totals Summary */}
          {totalValue > 0 && (
            <div className="text-right space-y-1">
              <div className="text-sm text-muted-foreground">
                Total Quantity: <span className="font-medium">{totalItems} units</span>
              </div>
              <div className="text-sm text-muted-foreground">
                Total Value: <span className="font-medium">${totalValue.toFixed(2)}</span>
              </div>
            </div>
          )}
        </div>

        {/* Empty State */}
        {fields.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No items added yet</p>
            <p className="text-xs">Click "Add Item" to start building your shipment</p>
          </div>
        )}

        {/* Validation Messages */}
        {fields.length === 0 && (
          <div className="text-sm text-red-600">
            At least one item is required for the shipment
          </div>
        )}
      </CardContent>
    </Card>
  )
}
