/**
 * Manufacturing ERP - Shipping Analytics Dashboard Component
 * 
 * Professional analytics dashboard with location performance, inventory impact,
 * and capacity utilization insights with interactive charts and KPIs.
 */

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from "recharts"
import { 
  TrendingUp, 
  TrendingDown, 
  MapPin, 
  Package, 
  Clock, 
  DollarSign,
  Target,
  AlertTriangle,
  CheckCircle,
  BarChart3
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ShippingKPIs {
  total_shipments: number
  on_time_delivery_rate: number
  average_processing_time: number
  cost_per_shipment: number
  location_efficiency: number
  inventory_accuracy: number
}

interface LocationMetrics {
  location_id: string
  location_name: string
  location_type: string
  total_shipments: number
  efficiency_score: number
  capacity_utilization: number
  on_time_delivery_rate: number
}

interface InventoryImpact {
  total_outbound_value: number
  total_units_shipped: number
  location_distribution: Array<{
    location_id: string
    location_name: string
    value_shipped: number
    percentage_of_total: number
  }>
  product_performance: Array<{
    product_id: string
    product_name: string
    units_shipped: number
    value_shipped: number
  }>
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function ShippingAnalyticsDashboard() {
  const [kpis, setKpis] = useState<ShippingKPIs | null>(null)
  const [locationMetrics, setLocationMetrics] = useState<LocationMetrics[]>([])
  const [inventoryImpact, setInventoryImpact] = useState<InventoryImpact | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAnalyticsData()
  }, [])

  const loadAnalyticsData = async () => {
    try {
      setLoading(true)
      
      // In a real implementation, these would be API calls
      // For now, using mock data
      setKpis({
        total_shipments: 1247,
        on_time_delivery_rate: 94.2,
        average_processing_time: 4.8,
        cost_per_shipment: 125.50,
        location_efficiency: 87.2,
        inventory_accuracy: 98.5
      })

      setLocationMetrics([
        {
          location_id: 'shipping_staging_main',
          location_name: '📦 Main Shipping Staging Area',
          location_type: 'shipping_staging',
          total_shipments: 856,
          efficiency_score: 92,
          capacity_utilization: 78,
          on_time_delivery_rate: 96
        },
        {
          location_id: 'shipping_dock_a',
          location_name: '🚛 Shipping Dock A',
          location_type: 'shipping_dock',
          total_shipments: 234,
          efficiency_score: 85,
          capacity_utilization: 65,
          on_time_delivery_rate: 91
        },
        {
          location_id: 'shipping_dock_b',
          location_name: '🚢 Shipping Dock B',
          location_type: 'shipping_dock',
          total_shipments: 157,
          efficiency_score: 88,
          capacity_utilization: 82,
          on_time_delivery_rate: 95
        }
      ])

      setInventoryImpact({
        total_outbound_value: 2456789,
        total_units_shipped: 15678,
        location_distribution: [
          { location_id: 'fg_main_warehouse', location_name: 'Main Warehouse', value_shipped: 1234567, percentage_of_total: 50.2 },
          { location_id: 'fg_secondary_warehouse', location_name: 'Secondary Warehouse', value_shipped: 876543, percentage_of_total: 35.7 },
          { location_id: 'export_staging', location_name: 'Export Staging', value_shipped: 345679, percentage_of_total: 14.1 }
        ],
        product_performance: [
          { product_id: '1', product_name: 'Premium Widget A', units_shipped: 2345, value_shipped: 456789 },
          { product_id: '2', product_name: 'Standard Component B', units_shipped: 3456, value_shipped: 345678 },
          { product_id: '3', product_name: 'Deluxe Assembly C', units_shipped: 1234, value_shipped: 234567 }
        ]
      })

    } catch (error) {
      console.error('Error loading analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getKPITrend = (value: number, benchmark: number) => {
    if (value > benchmark) return { icon: TrendingUp, color: 'text-green-600', status: 'positive' }
    if (value < benchmark * 0.9) return { icon: TrendingDown, color: 'text-red-600', status: 'negative' }
    return { icon: Target, color: 'text-blue-600', status: 'neutral' }
  }

  const getEfficiencyColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-blue-600'
    if (score >= 70) return 'text-orange-600'
    return 'text-red-600'
  }

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* KPI Overview */}
      {kpis && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Shipments</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{kpis.total_shipments.toLocaleString()}</div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 text-green-600" />
                <span>+12% from last month</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">On-Time Delivery</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{kpis.on_time_delivery_rate}%</div>
              <Progress value={kpis.on_time_delivery_rate} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Processing Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{kpis.average_processing_time}h</div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <TrendingDown className="h-3 w-3 text-green-600" />
                <span>-0.3h improvement</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cost per Shipment</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${kpis.cost_per_shipment}</div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Target className="h-3 w-3 text-blue-600" />
                <span>Within target range</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Location Efficiency</CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{kpis.location_efficiency}%</div>
              <Progress value={kpis.location_efficiency} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inventory Accuracy</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{kpis.inventory_accuracy}%</div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <CheckCircle className="h-3 w-3 text-green-600" />
                <span>Excellent accuracy</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Analytics Tabs */}
      <Tabs defaultValue="locations" className="space-y-4">
        <TabsList>
          <TabsTrigger value="locations">Location Performance</TabsTrigger>
          <TabsTrigger value="inventory">Inventory Impact</TabsTrigger>
          <TabsTrigger value="capacity">Capacity Analysis</TabsTrigger>
        </TabsList>

        {/* Location Performance Tab */}
        <TabsContent value="locations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Location Performance Metrics</CardTitle>
              <CardDescription>
                Shipping performance by location with efficiency scores and utilization rates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {locationMetrics.map((location) => (
                  <div key={location.location_id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium">{location.location_name}</span>
                        <Badge variant="outline">{location.location_type.replace('_', ' ')}</Badge>
                      </div>
                      <div className="grid grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Shipments:</span>
                          <span className="ml-2 font-medium">{location.total_shipments}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Efficiency:</span>
                          <span className={`ml-2 font-medium ${getEfficiencyColor(location.efficiency_score)}`}>
                            {location.efficiency_score}%
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Utilization:</span>
                          <span className="ml-2 font-medium">{location.capacity_utilization}%</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">On-Time:</span>
                          <span className="ml-2 font-medium">{location.on_time_delivery_rate}%</span>
                        </div>
                      </div>
                    </div>
                    <div className="w-24">
                      <Progress value={location.efficiency_score} className="h-2" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Inventory Impact Tab */}
        <TabsContent value="inventory" className="space-y-4">
          {inventoryImpact && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Location Distribution</CardTitle>
                  <CardDescription>Outbound value by source location</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={inventoryImpact.location_distribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ location_name, percentage_of_total }) => 
                          `${location_name}: ${percentage_of_total.toFixed(1)}%`
                        }
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value_shipped"
                      >
                        {inventoryImpact.location_distribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value: number) => [`$${value.toLocaleString()}`, 'Value Shipped']} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Products</CardTitle>
                  <CardDescription>Best performing products by shipping volume</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={inventoryImpact.product_performance}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="product_name" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <Tooltip formatter={(value: number) => [value.toLocaleString(), 'Units Shipped']} />
                      <Bar dataKey="units_shipped" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* Capacity Analysis Tab */}
        <TabsContent value="capacity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Capacity Utilization Analysis</CardTitle>
              <CardDescription>
                Real-time capacity monitoring and optimization opportunities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Capacity analysis coming soon</p>
                <p className="text-sm">Advanced capacity metrics and optimization recommendations</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
