/**
 * Manufacturing ERP - Shipping Capacity Validation Component
 * 
 * Professional capacity validation display with real-time validation,
 * alternative suggestions, and visual status indicators.
 */

"use client"

import { useState, useEffect } from "react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  MapPin, 
  TrendingUp,
  Clock,
  ArrowRight
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface CapacityValidation {
  location_id: string
  is_valid: boolean
  can_accommodate: boolean
  available_capacity: number
  utilization_after: number
  status: 'normal' | 'warning' | 'critical' | 'full'
  message: string
}

interface AlternativeLocation {
  location_id: string
  location_name: string
  available_capacity: number
  utilization_percentage: number
  estimated_processing_time: number
  cost_factor: number
  attributes: {
    dock_type?: string
    security_level: string
    temperature_controlled: boolean
  }
}

interface CapacityValidationProps {
  locationId: string
  locationName: string
  requiredCapacity: number
  shippingMethod?: string
  onAlternativeSelect?: (locationId: string) => void
  showAlternatives?: boolean
  className?: string
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function ShippingCapacityValidation({
  locationId,
  locationName,
  requiredCapacity,
  shippingMethod = 'sea_freight',
  onAlternativeSelect,
  showAlternatives = true,
  className = ""
}: CapacityValidationProps) {
  const [validation, setValidation] = useState<CapacityValidation | null>(null)
  const [alternatives, setAlternatives] = useState<AlternativeLocation[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Validate capacity when inputs change
  useEffect(() => {
    if (locationId && requiredCapacity > 0) {
      validateCapacity()
    }
  }, [locationId, requiredCapacity, shippingMethod])

  const validateCapacity = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/shipping/locations/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          location_id: locationId,
          required_capacity: requiredCapacity,
          shipping_method: shippingMethod,
          include_alternatives: showAlternatives,
          max_alternatives: 3
        })
      })

      if (!response.ok) {
        throw new Error('Failed to validate capacity')
      }

      const data = await response.json()
      setValidation(data.validation)
      setAlternatives(data.alternatives || [])

    } catch (error) {
      console.error('Error validating capacity:', error)
      setError(error instanceof Error ? error.message : 'Validation failed')
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string, canAccommodate: boolean) => {
    if (!canAccommodate) {
      return <XCircle className="h-5 w-5 text-red-500" />
    }
    
    switch (status) {
      case 'normal':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />
      case 'full':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <MapPin className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string, canAccommodate: boolean) => {
    if (!canAccommodate) return 'destructive'
    
    switch (status) {
      case 'normal': return 'default'
      case 'warning': return 'secondary'
      case 'critical': return 'secondary'
      case 'full': return 'destructive'
      default: return 'outline'
    }
  }

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return 'bg-red-500'
    if (utilization >= 75) return 'bg-orange-500'
    if (utilization >= 50) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  if (loading) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-2 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!validation) {
    return null
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Validation Status */}
      <Alert variant={getStatusColor(validation.status, validation.can_accommodate)}>
        <div className="flex items-start gap-3">
          {getStatusIcon(validation.status, validation.can_accommodate)}
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <span className="font-medium">
                {locationName} Capacity Validation
              </span>
              <Badge variant={getStatusColor(validation.status, validation.can_accommodate)}>
                {validation.status.toUpperCase()}
              </Badge>
            </div>
            <AlertDescription>{validation.message}</AlertDescription>
            
            {/* Capacity Details */}
            <div className="grid grid-cols-2 gap-4 mt-3 text-sm">
              <div>
                <span className="text-muted-foreground">Required:</span>
                <span className="ml-2 font-medium">{requiredCapacity} units</span>
              </div>
              <div>
                <span className="text-muted-foreground">Available:</span>
                <span className="ml-2 font-medium">{validation.available_capacity} units</span>
              </div>
            </div>

            {/* Utilization After */}
            {validation.can_accommodate && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Utilization after shipment:</span>
                  <span className="font-medium">{validation.utilization_after}%</span>
                </div>
                <Progress 
                  value={validation.utilization_after} 
                  className="h-2"
                />
              </div>
            )}
          </div>
        </div>
      </Alert>

      {/* Alternative Locations */}
      {!validation.can_accommodate && alternatives.length > 0 && showAlternatives && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Alternative Locations
            </CardTitle>
            <CardDescription>
              These locations can accommodate your shipment requirements
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {alternatives.map((alt, index) => (
              <div 
                key={alt.location_id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex-1 space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{alt.location_name}</span>
                    <Badge variant="outline" className="text-xs">
                      {alt.attributes.security_level} security
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      <span>{alt.available_capacity} available</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{alt.estimated_processing_time}h processing</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      <span>{alt.utilization_percentage}% utilized</span>
                    </div>
                  </div>

                  {/* Utilization Bar */}
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div 
                      className={`h-1.5 rounded-full ${getUtilizationColor(alt.utilization_percentage)}`}
                      style={{ width: `${Math.min(alt.utilization_percentage, 100)}%` }}
                    />
                  </div>
                </div>

                {onAlternativeSelect && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onAlternativeSelect(alt.location_id)}
                    className="ml-4"
                  >
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* No Alternatives Available */}
      {!validation.can_accommodate && alternatives.length === 0 && showAlternatives && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            No alternative locations are currently available that can accommodate {requiredCapacity} units.
            Consider reducing shipment size or scheduling for a later time.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
