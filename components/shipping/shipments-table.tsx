/**
 * Manufacturing ERP - Shipments Table Component
 * Professional shipments data table with actions
 */

"use client"

import { useState } from "react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { FinancialImpactCard } from "@/components/operational/financial-impact-card"
import { CompactFinancialImpact } from "@/components/operational/compact-financial-impact"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu"
import {
  Eye,
  Edit,
  MoreHorizontal,
  Truck,
  Ship,
  Plane,
  Package,
  MapPin,
  Calendar,
  User,
  FileText
} from "lucide-react"
import { formatDate } from "@/lib/utils"

interface ShipmentsTableProps {
  shipments: any[]
}

export function ShipmentsTable({ shipments }: ShipmentsTableProps) {
  const [selectedShipments, setSelectedShipments] = useState<string[]>([])

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      preparing: { variant: "secondary" as const, color: "text-yellow-600", label: "Preparing" },
      ready: { variant: "secondary" as const, color: "text-blue-600", label: "Ready" },
      shipped: { variant: "default" as const, color: "text-purple-600", label: "Shipped" },
      in_transit: { variant: "default" as const, color: "text-indigo-600", label: "In Transit" },
      out_for_delivery: { variant: "default" as const, color: "text-orange-600", label: "Out for Delivery" },
      delivered: { variant: "default" as const, color: "text-green-600", label: "Delivered" },
      cancelled: { variant: "destructive" as const, color: "text-red-600", label: "Cancelled" },
      exception: { variant: "destructive" as const, color: "text-red-600", label: "Exception" }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.preparing

    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    )
  }

  const getShippingMethodIcon = (method: string) => {
    switch (method) {
      case 'air_freight':
        return <Plane className="h-4 w-4 text-blue-600" />
      case 'sea_freight':
        return <Ship className="h-4 w-4 text-blue-600" />
      case 'express':
        return <Truck className="h-4 w-4 text-green-600" />
      case 'truck':
        return <Truck className="h-4 w-4 text-gray-600" />
      default:
        return <Package className="h-4 w-4 text-gray-600" />
    }
  }

  const getShippingMethodLabel = (method: string) => {
    const labels = {
      air_freight: "Air Freight",
      sea_freight: "Sea Freight",
      express: "Express",
      truck: "Truck"
    }
    return labels[method as keyof typeof labels] || method
  }

  if (shipments.length === 0) {
    return (
      <div className="text-center py-12">
        <Ship className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Shipments Found</h3>
        <p className="text-muted-foreground mb-4">
          Get started by creating your first shipment
        </p>
        <Button asChild>
          <Link href="/shipping/create">
            <Package className="mr-2 h-4 w-4" />
            Create Shipment
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Shipment</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Method</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Items & Qty</TableHead>
            <TableHead>Financial Impact</TableHead>
            <TableHead>Ship Date</TableHead>
            <TableHead>Tracking</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {shipments.map((shipment) => (
            <TableRow key={shipment.id}>
              {/* Shipment Number & Contract */}
              <TableCell>
                <div className="space-y-1">
                  <Link href={`/shipping/${shipment.id}`} className="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                    {shipment.shipment_number}
                  </Link>
                  {shipment.salesContract && (
                    <div className="text-sm text-muted-foreground flex items-center gap-1">
                      <FileText className="h-3 w-3" />
                      {shipment.salesContract.number}
                    </div>
                  )}
                </div>
              </TableCell>

              {/* Customer */}
              <TableCell>
                <div className="space-y-1">
                  <div className="font-medium">{shipment.customer.name}</div>
                  {shipment.customer.contact_name && (
                    <div className="text-sm text-muted-foreground flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {shipment.customer.contact_name}
                    </div>
                  )}
                </div>
              </TableCell>

              {/* Shipping Method */}
              <TableCell>
                <div className="flex items-center gap-2">
                  {getShippingMethodIcon(shipment.shipping_method)}
                  <span className="text-sm">
                    {getShippingMethodLabel(shipment.shipping_method)}
                  </span>
                </div>
                {shipment.carrier && (
                  <div className="text-xs text-muted-foreground mt-1">
                    {shipment.carrier}
                  </div>
                )}
              </TableCell>

              {/* Status */}
              <TableCell>
                {getStatusBadge(shipment.status)}
              </TableCell>

              {/* Items Count & Total Quantity */}
              <TableCell>
                <div className="text-sm">
                  {shipment.items?.length || 0} items
                </div>
                <div className="text-xs text-muted-foreground">
                  {(() => {
                    const totalQty = shipment.items?.reduce((sum: number, item: any) => {
                      return sum + (parseFloat(item.quantity) || 0)
                    }, 0) || 0
                    return `${totalQty.toLocaleString()} units`
                  })()}
                </div>
                {shipment.total_value && (
                  <div className="text-xs text-muted-foreground">
                    ${parseFloat(shipment.total_value).toLocaleString()}
                  </div>
                )}
              </TableCell>

              {/* ✅ ACTIVATED: Financial Impact - Compact View */}
              <TableCell>
                <CompactFinancialImpact shipmentId={shipment.id} />
              </TableCell>

              {/* Ship Date */}
              <TableCell>
                <div className="space-y-1">
                  {shipment.ship_date ? (
                    <div className="text-sm flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(shipment.ship_date)}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">
                      Not scheduled
                    </div>
                  )}
                  {shipment.estimated_delivery && (
                    <div className="text-xs text-muted-foreground">
                      ETA: {formatDate(shipment.estimated_delivery)}
                    </div>
                  )}
                </div>
              </TableCell>

              {/* Tracking */}
              <TableCell>
                {shipment.tracking_number ? (
                  <div className="text-sm font-mono">
                    {shipment.tracking_number}
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">
                    No tracking
                  </div>
                )}
              </TableCell>

              {/* Actions */}
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/shipping/${shipment.id}`}>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/shipping/${shipment.id}/edit`}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Shipment
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href={`/shipping/${shipment.id}/track`}>
                        <MapPin className="mr-2 h-4 w-4" />
                        Track Shipment
                      </Link>
                    </DropdownMenuItem>
                    {shipment.status !== 'delivered' && shipment.status !== 'cancelled' && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          Cancel Shipment
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
