/**
 * Manufacturing ERP - Location Optimization Component
 * 
 * Professional location optimization suggestions with AI-powered
 * recommendations based on inventory distribution and efficiency metrics.
 */

"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { 
  Zap, 
  TrendingUp, 
  Clock, 
  DollarSign, 
  MapPin,
  CheckCircle,
  ArrowRight,
  Lightbulb,
  Target
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ProductAllocation {
  product_id: string
  quantity: number
  current_location_id: string
  weight?: number
  volume?: number
}

interface OptimizationResult {
  recommended_location: {
    location_id: string
    location_name: string
    optimization_score: number
    reasons: string[]
  }
  estimated_savings: {
    time: number
    cost: number
    efficiency: number
  }
  alternative_locations: Array<{
    location_id: string
    location_name: string
    available_capacity: number
    estimated_processing_time: number
    cost_factor: number
  }>
  analysis: {
    total_products: number
    total_quantity: number
    shipping_method: string
    optimization_criteria: any
  }
}

interface LocationOptimizationProps {
  productAllocations: ProductAllocation[]
  shippingMethod: string
  currentLocationId?: string
  onOptimizedLocationSelect?: (locationId: string, locationName: string) => void
  showAnalysis?: boolean
  className?: string
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function LocationOptimization({
  productAllocations,
  shippingMethod,
  currentLocationId,
  onOptimizedLocationSelect,
  showAnalysis = true,
  className = ""
}: LocationOptimizationProps) {
  const [optimization, setOptimization] = useState<OptimizationResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Run optimization when inputs change
  useEffect(() => {
    if (productAllocations.length > 0) {
      optimizeLocation()
    }
  }, [productAllocations, shippingMethod])

  const optimizeLocation = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/shipping/locations/optimize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          shipping_method: shippingMethod,
          product_allocations: productAllocations,
          preferences: {
            prioritize_capacity: true,
            prioritize_speed: false,
            prioritize_cost: false
          }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to optimize location')
      }

      const data = await response.json()
      setOptimization(data.optimization)

    } catch (error) {
      console.error('Error optimizing location:', error)
      setError(error instanceof Error ? error.message : 'Optimization failed')
    } finally {
      setLoading(false)
    }
  }

  const getSavingsColor = (value: number) => {
    if (value >= 20) return 'text-green-600'
    if (value >= 10) return 'text-blue-600'
    if (value > 0) return 'text-orange-600'
    return 'text-gray-600'
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-500'
    if (score >= 60) return 'bg-blue-500'
    if (score >= 40) return 'bg-orange-500'
    return 'bg-gray-500'
  }

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <Lightbulb className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!optimization) {
    return null
  }

  const isCurrentOptimal = currentLocationId === optimization.recommended_location.location_id
  const hasSignificantSavings = optimization.estimated_savings.efficiency > 10 || 
                                optimization.estimated_savings.time > 2 || 
                                optimization.estimated_savings.cost > 5

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Optimization Recommendation */}
      <Card className={isCurrentOptimal ? "border-green-200 bg-green-50" : "border-blue-200 bg-blue-50"}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            {isCurrentOptimal ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <Zap className="h-5 w-5 text-blue-600" />
            )}
            {isCurrentOptimal ? "Optimal Location Selected" : "Optimization Recommendation"}
          </CardTitle>
          <CardDescription>
            {isCurrentOptimal 
              ? "Your current selection is already optimized for efficiency"
              : "AI-powered location recommendation based on your inventory distribution"
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Recommended Location */}
          <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium">{optimization.recommended_location.location_name}</span>
                <Badge variant="secondary">
                  Score: {optimization.recommended_location.optimization_score}
                </Badge>
              </div>
              
              {/* Optimization Score Bar */}
              <div className="space-y-1">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">Optimization Score</span>
                  <span className="font-medium">{optimization.recommended_location.optimization_score}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${getScoreColor(optimization.recommended_location.optimization_score)}`}
                    style={{ width: `${optimization.recommended_location.optimization_score}%` }}
                  />
                </div>
              </div>

              {/* Optimization Reasons */}
              <div className="mt-3 space-y-1">
                {optimization.recommended_location.reasons.map((reason, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span>{reason}</span>
                  </div>
                ))}
              </div>
            </div>

            {!isCurrentOptimal && onOptimizedLocationSelect && (
              <Button
                onClick={() => onOptimizedLocationSelect(
                  optimization.recommended_location.location_id,
                  optimization.recommended_location.location_name
                )}
                className="ml-4"
              >
                <Target className="h-4 w-4 mr-2" />
                Use This Location
              </Button>
            )}
          </div>

          {/* Estimated Savings */}
          {hasSignificantSavings && (
            <div className="grid grid-cols-3 gap-4 p-3 bg-white rounded-lg border">
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">Time Savings</span>
                </div>
                <div className={`text-lg font-semibold ${getSavingsColor(optimization.estimated_savings.time)}`}>
                  {optimization.estimated_savings.time > 0 ? `${optimization.estimated_savings.time}h` : 'None'}
                </div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">Cost Savings</span>
                </div>
                <div className={`text-lg font-semibold ${getSavingsColor(optimization.estimated_savings.cost)}`}>
                  {optimization.estimated_savings.cost > 0 ? `${optimization.estimated_savings.cost}%` : 'None'}
                </div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">Efficiency Gain</span>
                </div>
                <div className={`text-lg font-semibold ${getSavingsColor(optimization.estimated_savings.efficiency)}`}>
                  {optimization.estimated_savings.efficiency > 0 ? `${optimization.estimated_savings.efficiency}%` : 'None'}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Alternative Locations */}
      {optimization.alternative_locations.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Alternative Options
            </CardTitle>
            <CardDescription>
              Other locations that could work for your shipment
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            {optimization.alternative_locations.slice(0, 3).map((alt, index) => (
              <div 
                key={alt.location_id}
                className="flex items-center justify-between p-2 border rounded hover:bg-muted/50 transition-colors"
              >
                <div className="flex-1">
                  <div className="font-medium text-sm">{alt.location_name}</div>
                  <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground mt-1">
                    <span>{alt.available_capacity} capacity</span>
                    <span>{alt.estimated_processing_time}h processing</span>
                    <span>{alt.cost_factor}x cost factor</span>
                  </div>
                </div>
                
                {onOptimizedLocationSelect && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onOptimizedLocationSelect(alt.location_id, alt.location_name)}
                  >
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Analysis Summary */}
      {showAnalysis && (
        <div className="text-xs text-muted-foreground space-y-1">
          <div>Analysis based on {optimization.analysis.total_products} products, {optimization.analysis.total_quantity} total units</div>
          <div>Shipping method: {optimization.analysis.shipping_method.replace('_', ' ')}</div>
          <div>Optimization prioritizes: capacity utilization and processing efficiency</div>
        </div>
      )}
    </div>
  )
}
