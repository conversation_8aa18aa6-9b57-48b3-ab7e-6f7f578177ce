"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  RefreshCw, 
  TrendingUp,
  Package,
  Database,
  Activity,
  Target,
  Wrench
} from "lucide-react"

interface IntegrationStatus {
  summary: {
    totalShipments: number
    shipmentsWithStockLots: number
    shipmentsWithTransactions: number
    integrationRate: number
    transactionCoverage: number
  }
  breakdown: {
    byStatus: {
      [status: string]: {
        total: number
        withStockLots: number
        withTransactions: number
        integrationRate: number
      }
    }
    recent: Array<{
      shipmentNumber: string
      status: string
      itemCount: number
      integratedItems: number
      hasTransactions: boolean
      createdAt: string
    }>
  }
  healthScore: number
  recommendations: string[]
}

export function IntegrationStatusDashboard() {
  const [status, setStatus] = useState<IntegrationStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadIntegrationStatus()
  }, [])

  const loadIntegrationStatus = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/shipping/integration-status')
      if (response.ok) {
        const data = await response.json()
        setStatus(data)
      } else {
        throw new Error('Failed to load integration status')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 75) return "text-yellow-600"
    return "text-red-600"
  }

  const getHealthScoreBadge = (score: number) => {
    if (score >= 90) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>
    if (score >= 75) return <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>
    return <Badge className="bg-red-100 text-red-800">Needs Attention</Badge>
  }

  const getIntegrationRateColor = (rate: number) => {
    if (rate >= 90) return "bg-green-500"
    if (rate >= 75) return "bg-yellow-500"
    return "bg-red-500"
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading integration status...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load integration status: {error}
            </AlertDescription>
          </Alert>
          <Button onClick={loadIntegrationStatus} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!status) return null

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Shipping-Inventory Integration Status</h2>
          <p className="text-muted-foreground">
            Monitor and maintain integration health between shipping and inventory modules
          </p>
        </div>
        <Button onClick={loadIntegrationStatus} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Health Score Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Integration Health Score
            </CardTitle>
            {getHealthScoreBadge(status.healthScore)}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Overall Health</span>
                <span className={`text-2xl font-bold ${getHealthScoreColor(status.healthScore)}`}>
                  {status.healthScore}/100
                </span>
              </div>
              <Progress 
                value={status.healthScore} 
                className="h-3"
              />
            </div>
            <div className="text-center">
              {status.healthScore >= 90 ? (
                <CheckCircle className="h-12 w-12 text-green-600" />
              ) : status.healthScore >= 75 ? (
                <AlertTriangle className="h-12 w-12 text-yellow-600" />
              ) : (
                <XCircle className="h-12 w-12 text-red-600" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Package className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Total Shipments</p>
                <p className="text-2xl font-bold">{status.summary.totalShipments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Database className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">Integration Rate</p>
                <p className="text-2xl font-bold">{status.summary.integrationRate}%</p>
                <Progress 
                  value={status.summary.integrationRate} 
                  className={`h-2 mt-1 ${getIntegrationRateColor(status.summary.integrationRate)}`}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Transaction Coverage</p>
                <p className="text-2xl font-bold">{status.summary.transactionCoverage}%</p>
                <Progress 
                  value={status.summary.transactionCoverage} 
                  className={`h-2 mt-1 ${getIntegrationRateColor(status.summary.transactionCoverage)}`}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Integrated Shipments</p>
                <p className="text-2xl font-bold">{status.summary.shipmentsWithStockLots}</p>
                <p className="text-xs text-muted-foreground">
                  of {status.summary.totalShipments} total
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      {status.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {status.recommendations.map((recommendation, index) => (
                <Alert key={index} className={
                  recommendation.includes('Excellent') ? 'border-green-200 bg-green-50' :
                  recommendation.includes('Good') ? 'border-yellow-200 bg-yellow-50' :
                  'border-blue-200 bg-blue-50'
                }>
                  <AlertDescription>{recommendation}</AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Status Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Integration by Shipment Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(status.breakdown.byStatus).map(([statusName, data]) => (
              <div key={statusName} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Badge variant="outline" className="capitalize">
                    {statusName.replace('_', ' ')}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {data.total} shipments
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {data.integrationRate}% integrated
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {data.withStockLots}/{data.total} with stock lots
                    </div>
                  </div>
                  <Progress 
                    value={data.integrationRate} 
                    className="w-20 h-2"
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Shipments */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Shipments Integration Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {status.breakdown.recent.map((shipment, index) => (
              <div key={index} className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center gap-3">
                  <span className="font-medium">{shipment.shipmentNumber}</span>
                  <Badge variant="outline" className="capitalize">
                    {shipment.status.replace('_', ' ')}
                  </Badge>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <span>{shipment.integratedItems}/{shipment.itemCount} items integrated</span>
                  <span>{shipment.hasTransactions ? '✅' : '❌'} transactions</span>
                  {shipment.integratedItems === shipment.itemCount && shipment.hasTransactions ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
