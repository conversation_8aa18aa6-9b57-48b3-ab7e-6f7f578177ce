/**
 * Manufacturing ERP - Create Shipment Form Component
 * Professional shipment creation with validation
 */

"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { Textarea } from "@/components/ui/textarea"


import { useSafeToast } from "@/hooks/use-safe-toast"
import { ShipmentItemsManager } from "./shipment-items-manager"
import {
  Ship,
  Plane,
  Truck,
  Package,
  User,
  FileText
} from "lucide-react"

// Form validation schema
const createShipmentSchema = z.object({
  source: z.enum(["contract", "manual"]),
  sales_contract_id: z.string().default(""),
  customer_id: z.string().min(1, "Customer is required"),
  shipping_method: z.enum(["sea_freight", "air_freight", "express", "truck"]),
  carrier: z.string().default(""),
  service_type: z.enum(["standard", "express", "economy"]).default("standard"),
  ship_date: z.string().default(""),
  estimated_delivery: z.string().default(""),
  shipping_cost: z.string().default(""),
  insurance_cost: z.string().default(""),
  notes: z.string().default(""),
  special_instructions: z.string().default(""),
  // ✅ PHASE 2 ENHANCEMENT: Location integration fields
  pickup_location_id: z.string().default(""),
  staging_location_id: z.string().default(""),
  pickup_instructions: z.string().default(""),
  items: z.array(z.object({
    product_id: z.string().min(1, "Product is required"),
    quantity: z.string().min(1, "Quantity is required"),
    unit_price: z.string().default(""),
  })).min(1, "At least one item is required")
})

type FormData = z.infer<typeof createShipmentSchema>

interface CreateShipmentFormProps {
  customers: Array<{
    id: string
    name: string
    contact_name: string | null
    contact_email: string | null
    address: string | null
  }>
  contracts: Array<{
    id: string
    number: string
    customer: {
      id: string
      name: string
    }
    items: Array<{
      product: {
        id: string
        name: string
        sku: string
        unit: string
      }
      qty: string
      price: string
    }>
  }>
  products: Array<{
    id: string
    name: string
    sku: string
    unit: string
    image: string | null
  }>
  defaultMethod?: string
  selectedContract?: any
}

export function CreateShipmentForm({
  customers,
  contracts,
  products,
  defaultMethod,
  selectedContract
}: CreateShipmentFormProps) {
  const router = useRouter()
  const { toast } = useSafeToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // ✅ SEARCHABLE OPTIONS: Convert contracts to searchable options
  const contractOptions: SearchableSelectOption[] = contracts.map((contract) => ({
    value: contract.id,
    label: contract.number,
    subtitle: contract.customer.name,
    description: `${contract.items.length} items • Contract`,
  }))

  const form = useForm<FormData>({
    resolver: zodResolver(createShipmentSchema),
    defaultValues: {
      source: selectedContract ? "contract" : "manual",
      sales_contract_id: selectedContract?.id || "",
      customer_id: selectedContract?.customer.id || "",
      shipping_method: (defaultMethod as any) || "sea_freight",
      carrier: "",
      service_type: "standard",
      ship_date: "",
      estimated_delivery: "",
      shipping_cost: "",
      insurance_cost: "",
      notes: "",
      special_instructions: "",
      // ✅ PHASE 2 ENHANCEMENT: Location integration default values
      pickup_location_id: "",
      staging_location_id: "",
      pickup_instructions: "",
      items: selectedContract ?
        selectedContract.items.map((item: any) => ({
          product_id: item.product.id,
          quantity: item.qty,
          unit_price: item.price || ""
        })) :
        [{ product_id: "", quantity: "", unit_price: "" }]
    }
  })

  const watchSource = form.watch("source")
  const watchContractId = form.watch("sales_contract_id")

  // Handle contract selection
  const handleContractChange = (contractId: string) => {
    const contract = contracts.find(c => c.id === contractId)
    if (contract) {
      form.setValue("customer_id", contract.customer.id)
      form.setValue("items", contract.items.map(item => ({
        product_id: item.product.id,
        quantity: item.qty,
        unit_price: item.price || ""
      })))
    }
  }

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)

    try {
      // ✅ FIX: Filter out sales_contract_id for manual shipments
      const requestData = {
        ...data,
        from_contract: data.source === "contract",
        // Remove sales_contract_id for manual shipments to avoid foreign key constraint
        sales_contract_id: data.source === "contract" ? data.sales_contract_id : undefined
      }
      console.log("Sending shipment data:", requestData)

      const response = await fetch("/api/shipping/shipments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      })

      if (!response.ok) {
        const error = await response.json()
        console.error("API Error Response:", error)
        throw new Error(error.message || "Failed to create shipment")
      }

      const shipment = await response.json()

      toast({
        title: "Shipment Created",
        description: `Shipment ${shipment.shipment_number} has been created successfully.`,
      })

      router.push(`/shipping/${shipment.id}`)
    } catch (error) {
      console.error("Error creating shipment:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create shipment",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }



  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Source Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Shipment Source
            </CardTitle>
            <CardDescription>
              Choose whether to create from an existing contract or manually
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="source"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="grid grid-cols-2 gap-4">
                      <div
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${field.value === "contract" ? "border-blue-500 bg-blue-50" : "border-gray-200"
                          }`}
                        onClick={() => field.onChange("contract")}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <FileText className="h-5 w-5 text-blue-600" />
                          <span className="font-medium">From Contract</span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Create shipment from approved sales contract
                        </p>
                      </div>

                      <div
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${field.value === "manual" ? "border-blue-500 bg-blue-50" : "border-gray-200"
                          }`}
                        onClick={() => field.onChange("manual")}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <Package className="h-5 w-5 text-green-600" />
                          <span className="font-medium">Manual Entry</span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Create shipment manually with custom items
                        </p>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Contract Selection */}
            {watchSource === "contract" && (
              <FormField
                control={form.control}
                name="sales_contract_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sales Contract</FormLabel>
                    <FormControl>
                      <SearchableSelect
                        options={contractOptions}
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value)
                          handleContractChange(value)
                        }}
                        placeholder="Search contracts..."
                        searchPlaceholder="Search by contract number or customer..."
                        emptyMessage="No contracts found."
                        allowClear={true}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </CardContent>
        </Card>

        {/* Customer Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="customer_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer</FormLabel>
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={watchSource === "contract" && !!watchContractId}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a customer" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          <div>
                            <div className="font-medium">{customer.name}</div>
                            {customer.contact_name && (
                              <div className="text-sm text-muted-foreground">
                                {customer.contact_name}
                              </div>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Shipping Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ship className="h-5 w-5" />
              Shipping Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="shipping_method"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Shipping Method</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="sea_freight">
                          <div className="flex items-center gap-2">
                            <Ship className="h-4 w-4" />
                            Sea Freight
                          </div>
                        </SelectItem>
                        <SelectItem value="air_freight">
                          <div className="flex items-center gap-2">
                            <Plane className="h-4 w-4" />
                            Air Freight
                          </div>
                        </SelectItem>
                        <SelectItem value="express">
                          <div className="flex items-center gap-2">
                            <Truck className="h-4 w-4 text-green-600" />
                            Express
                          </div>
                        </SelectItem>
                        <SelectItem value="truck">
                          <div className="flex items-center gap-2">
                            <Truck className="h-4 w-4" />
                            Truck
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="carrier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Carrier (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., DHL, FedEx, Maersk"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="ship_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ship Date (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estimated_delivery"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Delivery (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="shipping_cost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Shipping Cost (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="insurance_cost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Insurance Cost (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* ✅ PHASE 2 ENHANCEMENT: Shipment Items for Manual Shipments */}
        <ShipmentItemsManager
          products={products}
          form={form as any}
          watchSource={watchSource}
        />

        {/* Notes */}
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional notes about this shipment..."
                      className="min-h-[80px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="special_instructions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Special Instructions (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Special handling instructions, delivery requirements, etc."
                      className="min-h-[80px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex items-center justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Shipment"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
