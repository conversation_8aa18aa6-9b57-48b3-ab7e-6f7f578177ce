/**
 * Manufacturing ERP - Shipping Statistics Component
 * Best-in-class UI/UX with progressive disclosure and actionable insights
 */

"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Package,
  Ship,
  Truck,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  TrendingUp,
  ArrowRight,
  ChevronDown,
  Activity,
  Target,
  Plane,
  Filter,
  X
} from "lucide-react"

interface ShippingStatsProps {
  stats: {
    total: number
    preparing: number
    ready: number
    shipped: number
    in_transit: number
    delivered: number
    cancelled: number
    exception: number
  }
}

export function ShippingStats({ stats }: ShippingStatsProps) {
  const [showDetailedBreakdown, setShowDetailedBreakdown] = useState(false)
  const [currentTime, setCurrentTime] = useState<string>("")
  const [isClient, setIsClient] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const currentFilter = searchParams.get('status')

  const activeShipments = stats.preparing + stats.ready + stats.shipped + stats.in_transit
  const completionRate = stats.total > 0 ? Math.round((stats.delivered / stats.total) * 100) : 0

  // Fix hydration mismatch by only showing time on client
  useEffect(() => {
    setIsClient(true)
    setCurrentTime(new Date().toLocaleTimeString())
  }, [])

  // Filter handler for clickable cards
  const handleFilter = (status: string | null) => {
    const params = new URLSearchParams(searchParams.toString())

    if (status === null || status === currentFilter) {
      // Clear filter if clicking the same filter or "All"
      params.delete('status')
    } else {
      // Set new filter
      params.set('status', status)
    }

    // Navigate with new filter
    const newUrl = params.toString() ? `/shipping?${params.toString()}` : '/shipping'
    router.push(newUrl)
  }

  // Get card styling based on filter state
  const getCardStyle = (status: string | null) => {
    if (!currentFilter) return "cursor-pointer hover:shadow-md transition-all duration-200"

    if (status === currentFilter) {
      return "cursor-pointer ring-2 ring-blue-500 bg-blue-50 hover:shadow-md transition-all duration-200"
    }

    return "cursor-pointer opacity-60 hover:opacity-80 hover:shadow-md transition-all duration-200"
  }

  return (
    <div className="space-y-6">
      {/* Critical Alerts Section */}
      {(stats.exception > 0 || stats.cancelled > 0) && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <CardTitle className="text-red-800">Attention Required</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-6">
              {stats.exception > 0 && (
                <div className="flex items-center gap-2">
                  <Badge variant="destructive">{stats.exception}</Badge>
                  <span className="text-sm text-red-700">Exceptions need review</span>
                </div>
              )}
              {stats.cancelled > 0 && (
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="border-red-300 text-red-700">{stats.cancelled}</Badge>
                  <span className="text-sm text-red-700">Cancelled shipments</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Overview Dashboard */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">Shipping Overview</CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Real-time shipment status and performance metrics
              </p>
            </div>
            <div className="flex items-center gap-2">
              {isClient && (
                <Badge variant="outline" className="text-xs">
                  Last updated: {currentTime}
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Key Metrics Row - Clickable Filter Cards */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {/* Total Shipments - Clear All Filters */}
            <div
              className={`text-center p-4 rounded-lg bg-slate-50 ${getCardStyle(null)}`}
              onClick={() => handleFilter(null)}
              title="Click to show all shipments"
            >
              <div className="flex items-center justify-center gap-2 mb-2">
                <Package className="h-4 w-4 text-slate-600" />
                <span className="text-sm font-medium text-slate-600">Total</span>
                {!currentFilter && <Filter className="h-3 w-3 text-slate-400" />}
              </div>
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="text-xs text-slate-500 mt-1">All shipments</div>
            </div>

            {/* Preparing Shipments */}
            <div
              className={`text-center p-4 rounded-lg bg-yellow-50 ${getCardStyle('preparing')}`}
              onClick={() => handleFilter('preparing')}
              title="Click to filter preparing shipments"
            >
              <div className="flex items-center justify-center gap-2 mb-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-600">Preparing</span>
                {currentFilter === 'preparing' && <Filter className="h-3 w-3 text-yellow-600" />}
              </div>
              <div className="text-2xl font-bold text-yellow-600">{stats.preparing}</div>
              <div className="text-xs text-yellow-500 mt-1">Getting ready</div>
            </div>

            {/* Shipped Shipments */}
            <div
              className={`text-center p-4 rounded-lg bg-purple-50 ${getCardStyle('shipped')}`}
              onClick={() => handleFilter('shipped')}
              title="Click to filter shipped shipments"
            >
              <div className="flex items-center justify-center gap-2 mb-2">
                <Ship className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-purple-600">Shipped</span>
                {currentFilter === 'shipped' && <Filter className="h-3 w-3 text-purple-600" />}
              </div>
              <div className="text-2xl font-bold text-purple-600">{stats.shipped}</div>
              <div className="text-xs text-purple-500 mt-1">In transit</div>
            </div>

            {/* Delivered Shipments */}
            <div
              className={`text-center p-4 rounded-lg bg-green-50 ${getCardStyle('delivered')}`}
              onClick={() => handleFilter('delivered')}
              title="Click to filter delivered shipments"
            >
              <div className="flex items-center justify-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-600">Delivered</span>
                {currentFilter === 'delivered' && <Filter className="h-3 w-3 text-green-600" />}
              </div>
              <div className="text-2xl font-bold text-green-600">{stats.delivered}</div>
              <div className="text-xs text-green-500 mt-1">Completed</div>
            </div>

            {/* Success Rate - Performance Metric */}
            <div className="text-center p-4 rounded-lg bg-emerald-50 relative">
              <div className="flex items-center justify-center gap-2 mb-2">
                <TrendingUp className="h-4 w-4 text-emerald-600" />
                <span className="text-sm font-medium text-emerald-600">Success Rate</span>
              </div>
              <div className="text-2xl font-bold text-emerald-600">{completionRate}%</div>
              <div className="text-xs text-emerald-500 mt-1">Performance</div>
            </div>
          </div>

          {/* Active Filter Indicator */}
          {currentFilter && (
            <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-700">
                  Filtered by: <span className="capitalize">{currentFilter}</span>
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilter(null)}
                className="text-blue-600 hover:text-blue-700 hover:bg-blue-100"
              >
                <X className="h-4 w-4 mr-1" />
                Clear Filter
              </Button>
            </div>
          )}

          {/* Status Pipeline Visualization */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-slate-700">Shipment Pipeline</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDetailedBreakdown(!showDetailedBreakdown)}
                className="text-xs"
              >
                {showDetailedBreakdown ? 'Hide Details' : 'Show Details'}
                <ChevronDown className={`ml-1 h-3 w-3 transition-transform ${showDetailedBreakdown ? 'rotate-180' : ''}`} />
              </Button>
            </div>

            {/* Pipeline Flow - Clickable Status Stages */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-50 to-green-50 rounded-lg overflow-x-auto">
              <div className="flex items-center gap-4 min-w-max">
                {/* Preparing Stage */}
                <div
                  className={`flex items-center gap-2 p-2 rounded-lg transition-all duration-200 ${currentFilter === 'preparing'
                    ? 'bg-yellow-200 ring-2 ring-yellow-400'
                    : 'hover:bg-yellow-100 cursor-pointer'
                    }`}
                  onClick={() => handleFilter('preparing')}
                  title="Click to filter preparing shipments"
                >
                  <div className="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center">
                    <Clock className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-yellow-600">{stats.preparing}</div>
                    <div className="text-xs text-muted-foreground">Preparing</div>
                  </div>
                </div>

                <div className="flex items-center text-slate-400">
                  <ArrowRight className="h-4 w-4" />
                </div>

                {/* Ready Stage */}
                <div
                  className={`flex items-center gap-2 p-2 rounded-lg transition-all duration-200 ${currentFilter === 'ready'
                    ? 'bg-blue-200 ring-2 ring-blue-400'
                    : 'hover:bg-blue-100 cursor-pointer'
                    }`}
                  onClick={() => handleFilter('ready')}
                  title="Click to filter ready shipments"
                >
                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <Package className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-blue-600">{stats.ready}</div>
                    <div className="text-xs text-muted-foreground">Ready</div>
                  </div>
                </div>

                <div className="flex items-center text-slate-400">
                  <ArrowRight className="h-4 w-4" />
                </div>

                {/* Shipped Stage */}
                <div
                  className={`flex items-center gap-2 p-2 rounded-lg transition-all duration-200 ${currentFilter === 'shipped'
                    ? 'bg-purple-200 ring-2 ring-purple-400'
                    : 'hover:bg-purple-100 cursor-pointer'
                    }`}
                  onClick={() => handleFilter('shipped')}
                  title="Click to filter shipped shipments"
                >
                  <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                    <Ship className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-purple-600">{stats.shipped}</div>
                    <div className="text-xs text-muted-foreground">Shipped</div>
                  </div>
                </div>

                <div className="flex items-center text-slate-400">
                  <ArrowRight className="h-4 w-4" />
                </div>

                {/* In Transit Stage */}
                <div
                  className={`flex items-center gap-2 p-2 rounded-lg transition-all duration-200 ${currentFilter === 'in_transit'
                    ? 'bg-indigo-200 ring-2 ring-indigo-400'
                    : 'hover:bg-indigo-100 cursor-pointer'
                    }`}
                  onClick={() => handleFilter('in_transit')}
                  title="Click to filter in-transit shipments"
                >
                  <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                    <Truck className="h-4 w-4 text-indigo-600" />
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-indigo-600">{stats.in_transit}</div>
                    <div className="text-xs text-muted-foreground">Transit</div>
                  </div>
                </div>

                <div className="flex items-center text-slate-400">
                  <ArrowRight className="h-4 w-4" />
                </div>

                {/* Delivered Stage */}
                <div
                  className={`flex items-center gap-2 p-2 rounded-lg transition-all duration-200 ${currentFilter === 'delivered'
                    ? 'bg-green-200 ring-2 ring-green-400'
                    : 'hover:bg-green-100 cursor-pointer'
                    }`}
                  onClick={() => handleFilter('delivered')}
                  title="Click to filter delivered shipments"
                >
                  <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-600">{stats.delivered}</div>
                    <div className="text-xs text-muted-foreground">Delivered</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Progress Indicators */}
            <div className="mt-6 space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span>Active Shipments Progress</span>
                <span className="font-medium">{activeShipments} / {stats.total}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <div
                  className="h-full bg-blue-600 rounded-full transition-all duration-300 ease-out"
                  style={{
                    width: stats.total > 0 ? `${Math.min((activeShipments / stats.total) * 100, 100)}%` : '0%'
                  }}
                />
              </div>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Overall Progress</span>
                <span className="font-medium">
                  {stats.delivered} of {stats.total} delivered ({completionRate}%)
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${Math.min(completionRate, 100)}%` }}
                />
              </div>
            </div>
          </div>

          {/* Detailed Breakdown (Collapsible) */}
          {showDetailedBreakdown && (
            <div className="pt-4 border-t space-y-4">
              <h3 className="text-sm font-medium text-slate-700">Detailed Status Breakdown</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {/* Exception Shipments - Clickable */}
                {stats.exception > 0 && (
                  <div
                    className={`p-3 rounded-lg border border-red-200 bg-red-50 transition-all duration-200 ${currentFilter === 'exception'
                      ? 'ring-2 ring-red-400 cursor-pointer'
                      : 'hover:shadow-md cursor-pointer'
                      }`}
                    onClick={() => handleFilter('exception')}
                    title="Click to filter exception shipments"
                  >
                    <div className="flex items-center gap-2 mb-1">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <span className="text-sm font-medium text-red-700">Exceptions</span>
                      {currentFilter === 'exception' && <Filter className="h-3 w-3 text-red-600" />}
                    </div>
                    <div className="text-xl font-bold text-red-600">{stats.exception}</div>
                    <div className="text-xs text-red-600">Need immediate attention</div>
                  </div>
                )}

                {/* Cancelled Shipments - Clickable */}
                {stats.cancelled > 0 && (
                  <div
                    className={`p-3 rounded-lg border border-gray-200 bg-gray-50 transition-all duration-200 ${currentFilter === 'cancelled'
                      ? 'ring-2 ring-gray-400 cursor-pointer'
                      : 'hover:shadow-md cursor-pointer'
                      }`}
                    onClick={() => handleFilter('cancelled')}
                    title="Click to filter cancelled shipments"
                  >
                    <div className="flex items-center gap-2 mb-1">
                      <XCircle className="h-4 w-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-700">Cancelled</span>
                      {currentFilter === 'cancelled' && <Filter className="h-3 w-3 text-gray-600" />}
                    </div>
                    <div className="text-xl font-bold text-gray-600">{stats.cancelled}</div>
                    <div className="text-xs text-gray-600">Cancelled shipments</div>
                  </div>
                )}

                {/* Active Rate - Performance Metric */}
                <div className="p-3 rounded-lg border border-blue-200 bg-blue-50">
                  <div className="flex items-center gap-2 mb-1">
                    <Activity className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-700">Active Rate</span>
                  </div>
                  <div className="text-xl font-bold text-blue-600">
                    {stats.total > 0 ? Math.round((activeShipments / stats.total) * 100) : 0}%
                  </div>
                  <div className="text-xs text-blue-600">Currently in progress</div>
                </div>

                {/* Success Rate - Performance Metric */}
                <div className="p-3 rounded-lg border border-green-200 bg-green-50">
                  <div className="flex items-center gap-2 mb-1">
                    <Target className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-green-700">On-Time Rate</span>
                  </div>
                  <div className="text-xl font-bold text-green-600">{completionRate}%</div>
                  <div className="text-xs text-green-600">Delivery performance</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
