/**
 * Manufacturing ERP - Shipment Detail View Component
 * Professional shipment information display
 */

"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Separator } from "@/components/ui/separator"
import { 
  User, 
  FileText, 
  Package, 
  Calendar, 
  DollarSign, 
  MapPin, 
  Truck,
  Phone,
  Mail,
  Building,
  Hash
} from "lucide-react"
import { formatDate } from "@/lib/utils"

interface ShipmentDetailViewProps {
  shipment: any
}

export function ShipmentDetailView({ shipment }: ShipmentDetailViewProps) {
  const totalValue = shipment.items?.reduce((sum: number, item: any) => {
    const price = parseFloat(item.unit_price || "0")
    const qty = parseFloat(item.quantity || "0")
    return sum + (price * qty)
  }, 0) || 0

  const totalItems = shipment.items?.reduce((sum: number, item: any) => {
    return sum + parseFloat(item.quantity || "0")
  }, 0) || 0

  return (
    <div className="grid gap-6 md:grid-cols-3">
      {/* Main Information */}
      <div className="md:col-span-2 space-y-6">
        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Company</span>
                </div>
                <p className="text-lg font-semibold">{shipment.customer.name}</p>
              </div>

              {shipment.customer.contact_name && (
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Contact Person</span>
                  </div>
                  <p>{shipment.customer.contact_name}</p>
                </div>
              )}

              {shipment.customer.contact_email && (
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Email</span>
                  </div>
                  <p>{shipment.customer.contact_email}</p>
                </div>
              )}

              {shipment.customer.contact_phone && (
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Phone</span>
                  </div>
                  <p>{shipment.customer.contact_phone}</p>
                </div>
              )}
            </div>

            {shipment.customer.address && (
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Address</span>
                </div>
                <p className="text-sm text-muted-foreground">{shipment.customer.address}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Shipment Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Shipment Items
            </CardTitle>
            <CardDescription>
              {shipment.items?.length || 0} items • Total quantity: {totalItems}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {shipment.items && shipment.items.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead className="text-right">Quantity</TableHead>
                    <TableHead className="text-right">Unit Price</TableHead>
                    <TableHead className="text-right">Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {shipment.items.map((item: any, index: number) => {
                    const unitPrice = parseFloat(item.unit_price || "0")
                    const quantity = parseFloat(item.quantity || "0")
                    const total = unitPrice * quantity

                    return (
                      <TableRow key={index}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.product.name}</div>
                            <div className="text-sm text-muted-foreground">
                              Unit: {item.product.unit}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{item.product.sku}</Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          {quantity.toLocaleString()}
                        </TableCell>
                        <TableCell className="text-right">
                          ${unitPrice.toFixed(2)}
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          ${total.toFixed(2)}
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No items in this shipment
              </div>
            )}

            {totalValue > 0 && (
              <>
                <Separator className="my-4" />
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total Shipment Value:</span>
                  <span className="text-lg font-bold">${totalValue.toFixed(2)}</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Notes and Instructions */}
        {(shipment.notes || shipment.special_instructions) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Additional Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {shipment.notes && (
                <div>
                  <h4 className="font-medium mb-2">Notes</h4>
                  <p className="text-sm text-muted-foreground">{shipment.notes}</p>
                </div>
              )}

              {shipment.special_instructions && (
                <div>
                  <h4 className="font-medium mb-2">Special Instructions</h4>
                  <p className="text-sm text-muted-foreground">{shipment.special_instructions}</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Sidebar Information */}
      <div className="space-y-6">
        {/* Shipping Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Shipping Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <span className="text-sm font-medium">Shipment Number</span>
              <p className="font-mono text-sm">{shipment.shipment_number}</p>
            </div>

            {shipment.tracking_number && (
              <div>
                <span className="text-sm font-medium">Tracking Number</span>
                <p className="font-mono text-sm">{shipment.tracking_number}</p>
              </div>
            )}

            {shipment.carrier && (
              <div>
                <span className="text-sm font-medium">Carrier</span>
                <p>{shipment.carrier}</p>
              </div>
            )}

            <Separator />

            {shipment.ship_date && (
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Ship Date</span>
                </div>
                <p>{formatDate(shipment.ship_date)}</p>
              </div>
            )}

            {shipment.estimated_delivery && (
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Estimated Delivery</span>
                </div>
                <p>{formatDate(shipment.estimated_delivery)}</p>
              </div>
            )}

            <Separator />

            <div>
              <span className="text-sm font-medium">Created</span>
              <p className="text-sm text-muted-foreground">
                {formatDate(shipment.created_at)}
              </p>
            </div>

            {shipment.updated_at !== shipment.created_at && (
              <div>
                <span className="text-sm font-medium">Last Updated</span>
                <p className="text-sm text-muted-foreground">
                  {formatDate(shipment.updated_at)}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Contract Information */}
        {shipment.salesContract && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Related Contract
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <span className="text-sm font-medium">Contract Number</span>
                  <p className="font-mono text-sm">{shipment.salesContract.number}</p>
                </div>
                <div>
                  <span className="text-sm font-medium">Contract Status</span>
                  <div className="mt-1">
                    <Badge variant="outline">{shipment.salesContract.status}</Badge>
                  </div>
                </div>
              </div>
              <Button variant="outline" size="sm" className="w-full mt-4" asChild>
                <a href={`/sales-contracts/${shipment.salesContract.id}`}>
                  View Contract
                </a>
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Cost Information */}
        {(shipment.shipping_cost || shipment.insurance_cost) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Cost Breakdown
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {shipment.shipping_cost && (
                <div className="flex justify-between">
                  <span className="text-sm">Shipping Cost</span>
                  <span className="font-medium">${parseFloat(shipment.shipping_cost).toFixed(2)}</span>
                </div>
              )}

              {shipment.insurance_cost && (
                <div className="flex justify-between">
                  <span className="text-sm">Insurance Cost</span>
                  <span className="font-medium">${parseFloat(shipment.insurance_cost).toFixed(2)}</span>
                </div>
              )}

              {(shipment.shipping_cost || shipment.insurance_cost) && (
                <>
                  <Separator />
                  <div className="flex justify-between font-medium">
                    <span>Total Shipping Cost</span>
                    <span>
                      ${(
                        parseFloat(shipment.shipping_cost || "0") + 
                        parseFloat(shipment.insurance_cost || "0")
                      ).toFixed(2)}
                    </span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
