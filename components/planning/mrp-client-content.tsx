"use client"

import { useI18n } from "@/components/i18n-provider"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { RefreshPrioritiesButton } from "@/components/planning/refresh-priorities-button"
import { PriorityBadge } from "@/components/planning/priority-badge"
import { ProfitabilityOverviewCard } from "@/components/planning/profit-margin-display"
import {
  Plus,
  TrendingUp,
  Package,
  AlertTriangle,
  Calendar,
  BarChart3,
  Truck,
  Clock,
  DollarSign,
  Target,
  Activity,
  ShoppingCart,
  Info,
  CheckCircle,
  XCircle,
  Zap
} from "lucide-react"
import Link from "next/link"

interface MRPClientContentProps {
  kpis: any
  forecasts: any[]
  plans: any[]
  profitabilityData: any
}

export function MRPClientContent({ kpis, forecasts, plans, profitabilityData }: MRPClientContentProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL: Page header with actions */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("mrp.title")}</h1>
          <p className="text-muted-foreground">
            {t("mrp.subtitle")}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <RefreshPrioritiesButton />
          <Button asChild>
            <Link href="/planning/forecasting/create">
              <Plus className="mr-2 h-4 w-4" />
              {t("mrp.new_forecast")}
            </Link>
          </Button>
        </div>
      </div>

      {/* ✅ PROFESSIONAL: Enhanced KPI Cards with Real Data */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrp.active_forecasts")}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.activeForecasts}</div>
            <p className="text-xs text-muted-foreground">
              {kpis.approvedForecasts} {t("mrp.approved")}
            </p>
            {kpis.activeForecasts > 0 && (
              <Progress
                value={(kpis.approvedForecasts / kpis.activeForecasts) * 100}
                className="mt-2 h-1"
              />
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrp.procurement_status")}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.totalProcurementPlans}</div>
            <p className="text-xs text-muted-foreground">
              {kpis.pendingApproval} {t("mrp.pending_approval")} • {kpis.approvedPlans} {t("mrp.approved")} • {kpis.orderedPlans} {t("mrp.ordered")}
            </p>
            {kpis.pendingApproval > 0 ? (
              <Badge variant="secondary" className="mt-1 text-xs bg-orange-100 text-orange-800 hover:bg-orange-200">
                {kpis.pendingApproval} {t("mrp.need_approval")}
              </Badge>
            ) : kpis.readyToOrder > 0 ? (
              <Badge variant="default" className="mt-1 text-xs bg-blue-600 text-white hover:bg-blue-700">
                {kpis.readyToOrder} {t("mrp.ready_to_order")}
              </Badge>
            ) : (
              <Badge variant="outline" className="mt-1 text-xs bg-green-50 text-green-700 border-green-200">
                {t("mrp.all_up_to_date")}
              </Badge>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrp.procurement_plans")}</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.totalProcurementPlans}</div>
            <p className="text-xs text-muted-foreground">
              ${kpis.totalEstimatedCost.toLocaleString()} {t("mrp.estimated")}
            </p>
            {kpis.pendingCost > 0 ? (
              <Badge variant="secondary" className="mt-1 text-xs bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
                ${kpis.pendingCost.toLocaleString()} {t("mrp.pending")}
              </Badge>
            ) : (
              <Badge variant="outline" className="mt-1 text-xs bg-green-50 text-green-700 border-green-200">
                {t("mrp.all_approved")}
              </Badge>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrp.action_required")}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.criticalActions}</div>
            <p className="text-xs text-muted-foreground">
              {t("mrp.urgent_high_priority")}
            </p>
            {kpis.criticalActions > 0 ? (
              <Badge variant="destructive" className="mt-1 text-xs bg-red-600 text-white hover:bg-red-700">
                {t("mrp.immediate_action")}
              </Badge>
            ) : kpis.overduePlans > 0 ? (
              <Badge variant="destructive" className="mt-1 text-xs bg-orange-600 text-white hover:bg-orange-700">
                {kpis.overduePlans} {t("mrp.overdue")}
              </Badge>
            ) : (
              <Badge variant="outline" className="mt-1 text-xs bg-green-50 text-green-700 border-green-200">
                {t("mrp.no_action_needed")}
              </Badge>
            )}
          </CardContent>
        </Card>
      </div>

      {/* ✅ PROFESSIONAL: Secondary KPI Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrp.supplier_network")}</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.totalSuppliers}</div>
            <p className="text-xs text-muted-foreground">
              {kpis.excellentSuppliers} {t("mrp.excellent_rated")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrp.avg_lead_time")}</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(kpis.averageLeadTime)}</div>
            <p className="text-xs text-muted-foreground">
              {t("mrp.days_average_delivery")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrp.ready_to_order")}</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.readyToOrder}</div>
            <p className="text-xs text-muted-foreground">
              {t("mrp.approved_plans_ready")}
            </p>
            {kpis.readyToOrder > 0 ? (
              <Badge variant="default" className="mt-1 text-xs bg-blue-600 text-white hover:bg-blue-700">
                {t("mrp.action_available")}
              </Badge>
            ) : (
              <Badge variant="outline" className="mt-1 text-xs">
                {t("mrp.none_ready")}
              </Badge>
            )}
          </CardContent>
        </Card>

        {/* ✅ NEW: Forecast Profitability Overview */}
        <ProfitabilityOverviewCard
          totalForecasts={profitabilityData.totalForecasts}
          averageMargin={profitabilityData.averageMargin}
          totalProfit={profitabilityData.totalProfit}
          totalRevenue={profitabilityData.totalRevenue}
          profitabilityDistribution={profitabilityData.profitabilityDistribution}
          currency="USD"
        />
      </div>

      {/* ✅ PROFESSIONAL: Streamlined MRP Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">{t("mrp.overview")}</TabsTrigger>
          <TabsTrigger value="forecasting">{t("mrp.forecasting")}</TabsTrigger>
          <TabsTrigger value="procurement">{t("mrp.procurement")}</TabsTrigger>
          <TabsTrigger value="analytics">{t("mrp.analytics")}</TabsTrigger>
        </TabsList>

        {/* Rest of the content will be added in the next part */}
        <div className="text-center py-8">
          <p className="text-muted-foreground">Tab content will be localized in the next step...</p>
        </div>
      </Tabs>
    </div>
  )
}
