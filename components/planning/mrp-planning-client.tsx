"use client"

import { useI18n } from "@/hooks/use-i18n"

interface MRPPlanningClientProps {
  children: React.ReactNode
}

export function MRPPlanningClient({ children }: MRPPlanningClientProps) {
  const { t } = useI18n()
  
  return (
    <div data-mrp-translations={JSON.stringify({
      title: t("mrp.title"),
      subtitle: t("mrp.subtitle"),
      refreshPriorities: t("mrp.refresh_priorities"),
      newForecast: t("mrp.new_forecast"),
      activeForecasts: t("mrp.active_forecasts"),
      approved: t("mrp.approved"),
      procurementStatus: t("mrp.procurement_status"),
      pendingApproval: t("mrp.pending_approval"),
      ordered: t("mrp.ordered"),
      needApproval: t("mrp.need_approval"),
      readyToOrder: t("mrp.ready_to_order"),
      allUpToDate: t("mrp.all_up_to_date"),
      procurementPlans: t("mrp.procurement_plans"),
      estimated: t("mrp.estimated"),
      pending: t("mrp.pending"),
      allApproved: t("mrp.all_approved"),
      actionRequired: t("mrp.action_required"),
      urgentHighPriority: t("mrp.urgent_high_priority"),
      immediateAction: t("mrp.immediate_action"),
      overdue: t("mrp.overdue"),
      noActionNeeded: t("mrp.no_action_needed"),
      supplierNetwork: t("mrp.supplier_network"),
      excellentRated: t("mrp.excellent_rated"),
      avgLeadTime: t("mrp.avg_lead_time"),
      daysAverageDelivery: t("mrp.days_average_delivery"),
      approvedPlansReady: t("mrp.approved_plans_ready"),
      actionAvailable: t("mrp.action_available"),
      noneReady: t("mrp.none_ready"),
      overview: t("mrp.overview"),
      forecasting: t("mrp.forecasting"),
      procurement: t("mrp.procurement"),
      analytics: t("mrp.analytics"),
      demandForecastsProduction: t("mrp.demand_forecasts_production"),
      activeForecastsDriving: t("mrp.active_forecasts_driving"),
      materialProcurementStatus: t("mrp.material_procurement_status"),
      materialsNeeded: t("mrp.materials_needed"),
      requireImmediateAction: t("mrp.require_immediate_action"),
      units: t("mrp.units"),
      target: t("mrp.target"),
      estimatedCost: t("mrp.estimated_cost"),
      moreProcurementPlans: t("mrp.more_procurement_plans"),
      viewAllPlans: t("mrp.view_all_plans"),
      noActivePlans: t("mrp.no_active_plans"),
      createDemandForecasts: t("mrp.create_demand_forecasts"),
      supplierNetworkSummary: t("mrp.supplier_network_summary"),
      keySuppliersSupporting: t("mrp.key_suppliers_supporting"),
      activeSuppliers: t("mrp.active_suppliers"),
      totalProcurementValue: t("mrp.total_procurement_value"),
      activeSuppliersColon: t("mrp.active_suppliers_colon"),
      demandForecastingManagement: t("mrp.demand_forecasting_management"),
      createManageForecasts: t("mrp.create_manage_forecasts"),
      pipelineBasedForecasting: t("mrp.pipeline_based_forecasting"),
      generateForecastsPipeline: t("mrp.generate_forecasts_pipeline"),
      product: t("mrp.product"),
      period: t("mrp.period"),
      demand: t("mrp.demand"),
      confidence: t("mrp.confidence"),
      status: t("mrp.status"),
      high: t("mrp.high"),
      medium: t("mrp.medium"),
      noForecastsCreated: t("mrp.no_forecasts_created"),
      startCreatingForecast: t("mrp.start_creating_forecast"),
      createFirstForecast: t("mrp.create_first_forecast"),
      procurementPlanning: t("mrp.procurement_planning"),
      automatedProcurementRecommendations: t("mrp.automated_procurement_recommendations"),
      purchaseRecommendations: t("mrp.purchase_recommendations"),
      material: t("mrp.material"),
      quantity: t("mrp.quantity"),
      supplier: t("mrp.supplier"),
      targetDate: t("mrp.target_date"),
      cost: t("mrp.cost"),
      urgency: t("mrp.urgency"),
      noProcurementPlans: t("mrp.no_procurement_plans"),
      procurementGeneratedAutomatically: t("mrp.procurement_generated_automatically"),
      requiresApprovedForecasts: t("mrp.requires_approved_forecasts"),
      systemAnalytics: t("mrp.system_analytics"),
      realTimeAnalytics: t("mrp.real_time_analytics"),
      performanceOverview: t("mrp.performance_overview"),
      totalValue: t("mrp.total_value"),
      daysAcrossSuppliers: t("mrp.days_across_suppliers"),
      workflowStatus: t("mrp.workflow_status"),
      demandForecasting: t("mrp.demand_forecasting"),
      activeForecastsCount: t("mrp.active_forecasts_count"),
      active: t("mrp.active"),
      inactive: t("mrp.inactive"),
      activePlans: t("mrp.active_plans"),
      supplierIntegration: t("mrp.supplier_integration"),
      suppliersConfigured: t("mrp.suppliers_configured"),
      connected: t("mrp.connected"),
      setupRequired: t("mrp.setup_required"),
      priorityManagement: t("mrp.priority_management"),
      criticalItems: t("mrp.critical_items"),
      attentionRequired: t("mrp.attention_required"),
      allClear: t("mrp.all_clear"),
      advancedAnalytics: t("mrp.advanced_analytics"),
      advancedAnalyticsComing: t("mrp.advanced_analytics_coming"),
      enhancedChartsDescription: t("mrp.enhanced_charts_description"),
      demandTrends: t("mrp.demand_trends"),
      costAnalysis: t("mrp.cost_analysis"),
      supplierPerformanceCharts: t("mrp.supplier_performance_charts"),
      leadTimeOptimization: t("mrp.lead_time_optimization"),
      priorityLevels: t("mrp.priority_levels"),
      immediate: t("mrp.immediate"),
      soon: t("mrp.soon"),
      standard: t("mrp.standard"),
      future: t("mrp.future"),
      noForecastsYet: t("mrp.no_forecasts_yet"),
      createFirstForecastDescription: t("mrp.create_first_forecast_description"),
      createForecast: t("mrp.create_forecast"),
      generatingProcurementPlans: t("mrp.generating_procurement_plans"),
      materials: t("mrp.materials"),
      viewAllForecasts: t("mrp.view_all_forecasts"),
      excellent: t("mrp.excellent"),
      good: t("mrp.good"),
      fair: t("mrp.fair"),
      poor: t("mrp.poor"),
      margin: t("mrp.margin")
    })}>
      {children}
    </div>
  )
}
