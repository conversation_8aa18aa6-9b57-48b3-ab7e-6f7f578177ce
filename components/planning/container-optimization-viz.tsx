"use client"

/**
 * Manufacturing ERP - Container Optimization Visualization Component
 * 
 * Professional visualization component for container load optimization results.
 * Displays container utilization, cost analysis, and loading recommendations.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP UI Components
 */

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { 
  Truck, 
  Package, 
  DollarSign, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  BarChart3,
  Leaf,
  Clock,
  Target,
  Info
} from "lucide-react"

// ✅ PROFESSIONAL: Type definitions
interface ContainerLoadPlan {
  containerId: string
  containerType: "20ft" | "40ft" | "40ft-hc" | "45ft"
  items: LoadedItem[]
  totalWeight: number
  totalVolume: number
  totalValue: number
  weightUtilization: number
  volumeUtilization: number
  overallUtilization: number
  estimatedCost: number
  estimatedLoadingTime: number
}

interface LoadedItem {
  cargoItemId: string
  name: string
  sku: string
  loadedQuantity: number
  totalWeight: number
  totalVolume: number
  totalValue: number
}

interface OptimizationResult {
  containers: ContainerLoadPlan[]
  unloadedItems: any[]
  summary: {
    totalContainers: number
    totalWeight: number
    totalVolume: number
    totalValue: number
    totalCost: number
    averageUtilization: number
    estimatedShippingTime: number
    co2Emissions: number
  }
  efficiency: {
    weightEfficiency: number
    volumeEfficiency: number
    costEfficiency: number
    overallScore: number
  }
  recommendations: string[]
  warnings: string[]
}

interface ContainerOptimizationVizProps {
  optimizationResult: OptimizationResult
  isLoading?: boolean
  onReoptimize?: () => void
  onExport?: () => void
}

export function ContainerOptimizationViz({
  optimizationResult,
  isLoading = false,
  onReoptimize,
  onExport
}: ContainerOptimizationVizProps) {
  const [selectedContainer, setSelectedContainer] = useState<string | null>(null)

  const { containers, summary, efficiency, recommendations, warnings } = optimizationResult

  // Container type specifications for visualization
  const containerSpecs = {
    "20ft": { name: "20ft Standard", color: "bg-blue-500", maxWeight: 28080, maxVolume: 33.2 },
    "40ft": { name: "40ft Standard", color: "bg-green-500", maxWeight: 26500, maxVolume: 67.7 },
    "40ft-hc": { name: "40ft High Cube", color: "bg-purple-500", maxWeight: 26500, maxVolume: 76.4 },
    "45ft": { name: "45ft High Cube", color: "bg-orange-500", maxWeight: 26500, maxVolume: 86.0 }
  }

  // Get efficiency color
  const getEfficiencyColor = (value: number) => {
    if (value >= 0.9) return "text-green-600"
    if (value >= 0.7) return "text-blue-600"
    if (value >= 0.5) return "text-orange-600"
    return "text-red-600"
  }

  // Get utilization color
  const getUtilizationColor = (value: number) => {
    if (value >= 0.85) return "bg-green-500"
    if (value >= 0.7) return "bg-blue-500"
    if (value >= 0.5) return "bg-orange-500"
    return "bg-red-500"
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Truck className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">Container Load Optimization</h2>
            <p className="text-muted-foreground">
              Optimized container loading for maximum efficiency and cost savings
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onReoptimize} disabled={isLoading}>
            <TrendingUp className="h-4 w-4 mr-2" />
            Re-optimize
          </Button>
          <Button onClick={onExport}>
            <Package className="h-4 w-4 mr-2" />
            Export Plan
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Containers</p>
                <p className="text-2xl font-bold">{summary.totalContainers}</p>
              </div>
              <Truck className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Cost</p>
                <p className="text-2xl font-bold">${summary.totalCost.toLocaleString()}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Utilization</p>
                <p className={`text-2xl font-bold ${getEfficiencyColor(summary.averageUtilization)}`}>
                  {(summary.averageUtilization * 100).toFixed(1)}%
                </p>
              </div>
              <Target className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">CO₂ Emissions</p>
                <p className="text-2xl font-bold">{summary.co2Emissions.toFixed(1)} kg</p>
              </div>
              <Leaf className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Efficiency Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Optimization Efficiency
          </CardTitle>
          <CardDescription>
            Overall performance metrics for the container optimization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className={`text-3xl font-bold mb-2 ${getEfficiencyColor(efficiency.weightEfficiency)}`}>
                {(efficiency.weightEfficiency * 100).toFixed(1)}%
              </div>
              <p className="text-sm text-muted-foreground">Weight Efficiency</p>
              <Progress value={efficiency.weightEfficiency * 100} className="mt-2" />
            </div>
            <div className="text-center">
              <div className={`text-3xl font-bold mb-2 ${getEfficiencyColor(efficiency.volumeEfficiency)}`}>
                {(efficiency.volumeEfficiency * 100).toFixed(1)}%
              </div>
              <p className="text-sm text-muted-foreground">Volume Efficiency</p>
              <Progress value={efficiency.volumeEfficiency * 100} className="mt-2" />
            </div>
            <div className="text-center">
              <div className={`text-3xl font-bold mb-2 ${getEfficiencyColor(efficiency.costEfficiency)}`}>
                {(efficiency.costEfficiency * 100).toFixed(1)}%
              </div>
              <p className="text-sm text-muted-foreground">Cost Efficiency</p>
              <Progress value={efficiency.costEfficiency * 100} className="mt-2" />
            </div>
            <div className="text-center">
              <div className={`text-3xl font-bold mb-2 ${getEfficiencyColor(efficiency.overallScore)}`}>
                {(efficiency.overallScore * 100).toFixed(1)}%
              </div>
              <p className="text-sm text-muted-foreground">Overall Score</p>
              <Progress value={efficiency.overallScore * 100} className="mt-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Container Details */}
      <Tabs defaultValue="containers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="containers">Container Details</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="timeline">Loading Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="containers" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {containers.map((container) => {
              const spec = containerSpecs[container.containerType]
              return (
                <Card 
                  key={container.containerId}
                  className={`cursor-pointer transition-all ${
                    selectedContainer === container.containerId ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedContainer(container.containerId)}
                >
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-4 h-4 rounded ${spec.color}`} />
                        <span>{container.containerId}</span>
                      </div>
                      <Badge variant="outline">{spec.name}</Badge>
                    </CardTitle>
                    <CardDescription>
                      {container.items.length} items • {container.estimatedLoadingTime.toFixed(1)} hours loading time
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Utilization Bars */}
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Weight Utilization</span>
                          <span>{(container.weightUtilization * 100).toFixed(1)}%</span>
                        </div>
                        <Progress 
                          value={container.weightUtilization * 100} 
                          className="h-2"
                        />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Volume Utilization</span>
                          <span>{(container.volumeUtilization * 100).toFixed(1)}%</span>
                        </div>
                        <Progress 
                          value={container.volumeUtilization * 100} 
                          className="h-2"
                        />
                      </div>
                    </div>

                    <Separator />

                    {/* Container Stats */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Total Weight</p>
                        <p className="font-medium">{container.totalWeight.toLocaleString()} kg</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Total Volume</p>
                        <p className="font-medium">{container.totalVolume.toFixed(1)} m³</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Total Value</p>
                        <p className="font-medium">${container.totalValue.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Est. Cost</p>
                        <p className="font-medium">${container.estimatedCost.toLocaleString()}</p>
                      </div>
                    </div>

                    {/* Items Preview */}
                    <div>
                      <p className="text-sm font-medium mb-2">Loaded Items ({container.items.length})</p>
                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {container.items.slice(0, 3).map((item) => (
                          <div key={item.cargoItemId} className="flex justify-between text-xs">
                            <span className="truncate">{item.name}</span>
                            <span className="text-muted-foreground">{item.loadedQuantity} units</span>
                          </div>
                        ))}
                        {container.items.length > 3 && (
                          <p className="text-xs text-muted-foreground">
                            +{container.items.length - 3} more items
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Recommendations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  Optimization Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                {recommendations.length > 0 ? (
                  <div className="space-y-3">
                    {recommendations.map((recommendation, index) => (
                      <Alert key={index}>
                        <Info className="h-4 w-4" />
                        <AlertDescription>{recommendation}</AlertDescription>
                      </Alert>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">No specific recommendations at this time.</p>
                )}
              </CardContent>
            </Card>

            {/* Warnings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Warnings & Considerations
                </CardTitle>
              </CardHeader>
              <CardContent>
                {warnings.length > 0 ? (
                  <div className="space-y-3">
                    {warnings.map((warning, index) => (
                      <Alert key={index} variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>{warning}</AlertDescription>
                      </Alert>
                    ))}
                  </div>
                ) : (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      No warnings detected. Optimization looks good!
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Loading Timeline
              </CardTitle>
              <CardDescription>
                Estimated loading sequence and timeline for all containers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <span className="font-medium">Total Estimated Loading Time</span>
                  <span className="text-lg font-bold">
                    {containers.reduce((total, c) => total + c.estimatedLoadingTime, 0).toFixed(1)} hours
                  </span>
                </div>
                
                <div className="space-y-3">
                  {containers.map((container, index) => (
                    <div key={container.containerId} className="flex items-center gap-4 p-3 border rounded-lg">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full text-sm font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{container.containerId}</p>
                        <p className="text-sm text-muted-foreground">
                          {container.items.length} items • {containerSpecs[container.containerType].name}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{container.estimatedLoadingTime.toFixed(1)} hours</p>
                        <p className="text-sm text-muted-foreground">
                          {(container.overallUtilization * 100).toFixed(1)}% utilized
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
