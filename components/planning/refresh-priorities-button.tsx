"use client"

import { But<PERSON> } from "@/components/ui/button"
import { RefreshCw } from "lucide-react"
import { useState } from "react"
import { useSafeToast } from "@/hooks/use-safe-toast"

export function RefreshPrioritiesButton() {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useSafeToast()

  const handleRefresh = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/planning/refresh-priorities', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })
      
      if (response.ok) {
        const result = await response.json()
        toast({
          title: "Success",
          description: result.message,
        })
        // Refresh the page to show updated priorities
        window.location.reload()
      } else {
        const error = await response.json()
        toast({
          title: "Error",
          description: error.message || "Failed to refresh priorities",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh priorities",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button 
      variant="outline" 
      size="sm"
      onClick={handleRefresh}
      disabled={isLoading}
      className="flex items-center gap-2"
    >
      <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
      {isLoading ? 'Refreshing...' : 'Refresh Priorities'}
    </Button>
  )
}
