/**
 * Manufacturing ERP - Profit Margin Display Component
 * 
 * Professional component for displaying profit margins and profitability metrics
 * in forecasting and MRP interfaces. Maintains consistency with BOM profit margin display.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Profit Margin Integration
 */

import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target,
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import { ForecastProfitabilityService } from "@/lib/services/forecast-profitability"

// ✅ PROFESSIONAL: Type definitions
interface ProfitMarginDisplayProps {
  marginPercentage: number
  profitabilityStatus: 'excellent' | 'good' | 'fair' | 'poor'
  profit?: number
  revenue?: number
  materialCost?: number
  currency?: string
  size?: 'sm' | 'md' | 'lg'
  showDetails?: boolean
  className?: string
}

interface ProfitMarginCardProps {
  title: string
  marginPercentage: number
  profitabilityStatus: 'excellent' | 'good' | 'fair' | 'poor'
  profit: number
  revenue: number
  materialCost: number
  currency?: string
  icon?: React.ReactNode
  className?: string
}

interface ProfitabilityOverviewCardProps {
  totalForecasts: number
  averageMargin: number
  totalProfit: number
  totalRevenue: number
  profitabilityDistribution: {
    excellent: number
    good: number
    fair: number
    poor: number
  }
  currency?: string
}

/**
 * ✅ PROFESSIONAL: Inline profit margin display (for tables/lists)
 */
export function ProfitMarginDisplay({
  marginPercentage,
  profitabilityStatus,
  profit,
  revenue,
  materialCost,
  currency = 'USD',
  size = 'md',
  showDetails = false,
  className
}: ProfitMarginDisplayProps) {
  const badgeConfig = ForecastProfitabilityService.getProfitabilityBadgeConfig(profitabilityStatus)
  
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* Margin Percentage */}
      <span className={cn(
        "font-medium",
        profitabilityStatus === 'excellent' ? 'text-green-700' :
        profitabilityStatus === 'good' ? 'text-blue-600' :
        profitabilityStatus === 'fair' ? 'text-yellow-600' : 'text-red-600',
        sizeClasses[size]
      )}>
        {marginPercentage.toFixed(1)}%
      </span>

      {/* Status Badge */}
      <Badge
        variant={badgeConfig.variant}
        className={cn(badgeConfig.className, sizeClasses[size])}
        title={badgeConfig.description}
      >
        {badgeConfig.label}
      </Badge>

      {/* Trend Icon */}
      {marginPercentage >= 20 ? (
        <TrendingUp className={cn("text-green-600", size === 'sm' ? 'h-3 w-3' : 'h-4 w-4')} />
      ) : marginPercentage >= 10 ? (
        <Target className={cn("text-yellow-600", size === 'sm' ? 'h-3 w-3' : 'h-4 w-4')} />
      ) : (
        <TrendingDown className={cn("text-red-600", size === 'sm' ? 'h-3 w-3' : 'h-4 w-4')} />
      )}

      {/* Detailed Financial Info */}
      {showDetails && profit !== undefined && revenue !== undefined && (
        <div className={cn("text-muted-foreground", sizeClasses[size])}>
          <span>Profit: {formatCurrency(profit)}</span>
          {materialCost !== undefined && (
            <span className="ml-2">Cost: {formatCurrency(materialCost)}</span>
          )}
        </div>
      )}
    </div>
  )
}

/**
 * ✅ PROFESSIONAL: Profit margin card (for dashboards)
 */
export function ProfitMarginCard({
  title,
  marginPercentage,
  profitabilityStatus,
  profit,
  revenue,
  materialCost,
  currency = 'USD',
  icon,
  className
}: ProfitMarginCardProps) {
  const badgeConfig = ForecastProfitabilityService.getProfitabilityBadgeConfig(profitabilityStatus)
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon || <DollarSign className="h-4 w-4 text-muted-foreground" />}
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {/* Main Margin Display */}
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold">
              {marginPercentage.toFixed(1)}%
            </div>
            <Badge
              variant={badgeConfig.variant}
              className={badgeConfig.className}
              title={badgeConfig.description}
            >
              {badgeConfig.label}
            </Badge>
          </div>

          {/* Financial Breakdown */}
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Revenue:</span>
              <span className="font-medium text-green-600">{formatCurrency(revenue)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Material Cost:</span>
              <span className="font-medium text-orange-600">{formatCurrency(materialCost)}</span>
            </div>
            <div className="flex justify-between border-t pt-1">
              <span className="text-muted-foreground">Profit:</span>
              <span className={`font-medium ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(profit)}
              </span>
            </div>
          </div>

          {/* Margin Progress Bar */}
          <div className="space-y-1">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Margin</span>
              <span>{marginPercentage.toFixed(1)}%</span>
            </div>
            <Progress 
              value={Math.min(marginPercentage, 50)} 
              max={50}
              className="h-2"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * ✅ PROFESSIONAL: Profitability overview card (for MRP dashboard)
 */
export function ProfitabilityOverviewCard({
  totalForecasts,
  averageMargin,
  totalProfit,
  totalRevenue,
  profitabilityDistribution,
  currency = 'USD'
}: ProfitabilityOverviewCardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getOverallStatus = () => {
    if (averageMargin >= 25) return { status: 'excellent', icon: CheckCircle, color: 'text-green-600' }
    if (averageMargin >= 15) return { status: 'good', icon: Target, color: 'text-blue-600' }
    if (averageMargin >= 8) return { status: 'fair', icon: AlertTriangle, color: 'text-yellow-600' }
    return { status: 'poor', icon: TrendingDown, color: 'text-red-600' }
  }

  const overallStatus = getOverallStatus()
  const StatusIcon = overallStatus.icon

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Forecast Profitability</CardTitle>
        <StatusIcon className={cn("h-4 w-4", overallStatus.color)} />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Main Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-2xl font-bold">{averageMargin.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Average Margin</p>
            </div>
            <div>
              <div className="text-2xl font-bold">{totalForecasts}</div>
              <p className="text-xs text-muted-foreground">Active Forecasts</p>
            </div>
          </div>

          {/* Financial Summary */}
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Revenue:</span>
              <span className="font-medium text-green-600">{formatCurrency(totalRevenue)}</span>
            </div>
            <div className="flex justify-between border-t pt-1">
              <span className="text-muted-foreground">Total Profit:</span>
              <span className={`font-medium ${totalProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(totalProfit)}
              </span>
            </div>
          </div>

          {/* Profitability Distribution */}
          <div className="space-y-2">
            <p className="text-xs font-medium text-muted-foreground">Profitability Distribution</p>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-green-600"></div>
                <span>Excellent: {profitabilityDistribution.excellent}</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-blue-600"></div>
                <span>Good: {profitabilityDistribution.good}</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-yellow-600"></div>
                <span>Fair: {profitabilityDistribution.fair}</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-red-600"></div>
                <span>Poor: {profitabilityDistribution.poor}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
