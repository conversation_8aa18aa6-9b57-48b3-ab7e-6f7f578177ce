"use client"

import { useState } from "react"
import { useR<PERSON>er } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Save } from "lucide-react"
import Link from "next/link"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface ProcurementPlan {
  id: string
  materialName: string
  materialSku?: string
  plannedQty: number
  targetDate: string
  priority: "low" | "normal" | "high" | "urgent"
  status: "draft" | "pending" | "approved" | "ordered" | "received"
  supplierName?: string
  supplierId?: string
  estimatedCost: number
  estimatedLeadTime: number
  notes?: string
  createdBy?: string
}

interface ProcurementPlanEditClientProps {
  plan: ProcurementPlan
}

export function ProcurementPlanEditClient({ plan }: ProcurementPlanEditClientProps) {
  const router = useRouter()
  const { success: toastSuccess, error: toastError } = useSafeToast()
  const [isLoading, setIsLoading] = useState(false)

  // ✅ BUSINESS RULES: Field editability based on status
  const getFieldEditability = () => {
    switch (plan.status) {
      case "draft":
        return {
          plannedQty: true,
          targetDate: true,
          priority: true,
          status: true,
          supplier: true,
          notes: true,
        }
      case "pending":
        return {
          plannedQty: true,
          targetDate: true,
          priority: true,
          status: true,
          supplier: false, // Supplier locked once pending
          notes: true,
        }
      case "approved":
        return {
          plannedQty: false, // Quantity locked once approved
          targetDate: true,  // Date can still be adjusted
          priority: false,   // Priority locked once approved
          status: true,      // Can move to ordered
          supplier: false,   // Supplier locked
          notes: true,       // Notes always editable
        }
      case "ordered":
        return {
          plannedQty: false,
          targetDate: false, // Date locked once ordered
          priority: false,
          status: true,      // Can move to received
          supplier: false,
          notes: true,       // Notes always editable
        }
      case "received":
        return {
          plannedQty: false,
          targetDate: false,
          priority: false,
          status: false,     // Final status
          supplier: false,
          notes: true,       // Notes always editable
        }
      default:
        return {
          plannedQty: true,
          targetDate: true,
          priority: true,
          status: true,
          supplier: true,
          notes: true,
        }
    }
  }

  const fieldEditability = getFieldEditability()

  const handleSubmit = async (formData: FormData) => {
    try {
      setIsLoading(true)

      const plannedQty = parseFloat(formData.get("plannedQty") as string)
      const targetDate = formData.get("targetDate") as string
      const priority = formData.get("priority") as string
      const status = formData.get("status") as string
      const supplierId = formData.get("supplierId") as string
      const notes = formData.get("notes") as string

      console.log(`🔄 Client: Updating procurement plan ${plan.id}`)

      // ✅ BUSINESS RULES: Only send editable fields based on status
      const updateData: any = {}

      if (fieldEditability.plannedQty) updateData.plannedQty = plannedQty
      if (fieldEditability.targetDate) updateData.targetDate = targetDate
      if (fieldEditability.priority) updateData.priority = priority as "low" | "normal" | "high" | "urgent"
      if (fieldEditability.status) updateData.status = status as "draft" | "pending" | "approved" | "ordered" | "received"
      if (fieldEditability.supplier && supplierId) updateData.supplierId = supplierId
      if (fieldEditability.notes) updateData.notes = notes

      const response = await fetch(`/api/planning/procurement/${plan.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `Failed to update plan: ${response.status}`)
      }

      const result = await response.json()
      console.log(`✅ Plan updated successfully:`, result)

      // ✅ PROFESSIONAL ERP: Show success toast notification
      toastSuccess(
        "Procurement plan updated successfully",
        `Plan for ${plan.materialName} has been updated`
      )

      // Navigate back to detail page
      router.push(`/planning/procurement/${plan.id}`)
    } catch (error) {
      console.error("❌ Error updating plan:", error)

      // ✅ PROFESSIONAL ERP: Show error toast notification
      toastError(
        "Failed to update procurement plan",
        error instanceof Error ? error.message : "An unexpected error occurred"
      )
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/planning/procurement/${plan.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Plan Details
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Procurement Plan</h1>
            <p className="text-muted-foreground">
              {plan.materialName} - {plan.materialSku}
            </p>
          </div>
        </div>
      </div>

      {/* Edit Form */}
      <Card>
        <CardHeader>
          <CardTitle>Plan Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form action={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Material Information (Read-only) */}
              <div className="space-y-2">
                <Label>Material Name</Label>
                <Input
                  value={plan.materialName}
                  disabled
                  className="bg-muted"
                />
              </div>

              <div className="space-y-2">
                <Label>Material SKU</Label>
                <Input
                  value={plan.materialSku || "N/A"}
                  disabled
                  className="bg-muted"
                />
              </div>

              {/* Editable Fields */}
              <div className="space-y-2">
                <Label htmlFor="plannedQty">
                  Planned Quantity *
                  {!fieldEditability.plannedQty && (
                    <span className="text-xs text-muted-foreground ml-2">(Locked)</span>
                  )}
                </Label>
                <Input
                  id="plannedQty"
                  name="plannedQty"
                  type="number"
                  step="0.01"
                  min="0"
                  defaultValue={plan.plannedQty}
                  required
                  disabled={isLoading || !fieldEditability.plannedQty}
                  className={!fieldEditability.plannedQty ? "bg-muted" : ""}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="targetDate">
                  Target Date *
                  {!fieldEditability.targetDate && (
                    <span className="text-xs text-muted-foreground ml-2">(Locked)</span>
                  )}
                </Label>
                <Input
                  id="targetDate"
                  name="targetDate"
                  type="date"
                  defaultValue={plan.targetDate}
                  required
                  disabled={isLoading || !fieldEditability.targetDate}
                  className={!fieldEditability.targetDate ? "bg-muted" : ""}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">
                  Priority *
                  {!fieldEditability.priority && (
                    <span className="text-xs text-muted-foreground ml-2">(Locked)</span>
                  )}
                </Label>
                <Select
                  name="priority"
                  defaultValue={plan.priority}
                  disabled={isLoading || !fieldEditability.priority}
                >
                  <SelectTrigger className={!fieldEditability.priority ? "bg-muted" : ""}>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">
                  Status
                  {!fieldEditability.status && (
                    <span className="text-xs text-muted-foreground ml-2">(Final)</span>
                  )}
                </Label>
                <Select
                  name="status"
                  defaultValue={plan.status}
                  disabled={isLoading || !fieldEditability.status}
                >
                  <SelectTrigger className={!fieldEditability.status ? "bg-muted" : ""}>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="ordered">Ordered</SelectItem>
                    <SelectItem value="received">Received</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Supplier Information */}
            <div className="space-y-2">
              <Label htmlFor="supplierId">
                Supplier
                {!fieldEditability.supplier && (
                  <span className="text-xs text-muted-foreground ml-2">(Locked)</span>
                )}
              </Label>
              {fieldEditability.supplier ? (
                <Select
                  name="supplierId"
                  defaultValue={plan.supplierId || ""}
                  disabled={isLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select supplier (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No supplier assigned</SelectItem>
                    {/* TODO: Load suppliers dynamically */}
                    <SelectItem value="supplier-1">Premium Fabric Mills Co</SelectItem>
                    <SelectItem value="supplier-2">Global Textile Suppliers</SelectItem>
                    <SelectItem value="supplier-3">Asia Pacific Materials</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <Input
                  value={plan.supplierName || "No supplier assigned"}
                  disabled
                  className="bg-muted"
                />
              )}
            </div>

            {/* Cost Information (Read-only) */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>Estimated Cost</Label>
                <Input
                  value={`$${plan.estimatedCost.toLocaleString()}`}
                  disabled
                  className="bg-muted"
                />
              </div>

              <div className="space-y-2">
                <Label>Estimated Lead Time</Label>
                <Input
                  value={`${plan.estimatedLeadTime} days`}
                  disabled
                  className="bg-muted"
                />
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">
                Notes
                <span className="text-xs text-muted-foreground ml-2">(Always editable)</span>
              </Label>
              <Textarea
                id="notes"
                name="notes"
                placeholder="Add any additional notes, requirements, or status updates..."
                defaultValue={plan.notes || ""}
                rows={4}
                disabled={isLoading}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end gap-4">
              <Button type="button" variant="outline" asChild disabled={isLoading}>
                <Link href={`/planning/procurement/${plan.id}`}>
                  Cancel
                </Link>
              </Button>
              <Button type="submit" disabled={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Help Text */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-sm text-muted-foreground space-y-3">
            <p><strong>Field Editability Rules:</strong> Fields are locked based on procurement plan status to maintain data integrity:</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="font-medium text-foreground mb-2">Editable by Status:</p>
                <ul className="list-disc list-inside space-y-1 ml-2">
                  <li><strong>Draft:</strong> All fields editable</li>
                  <li><strong>Pending:</strong> Quantity, date, priority, status, notes</li>
                  <li><strong>Approved:</strong> Target date, status, notes only</li>
                  <li><strong>Ordered:</strong> Status, notes only</li>
                  <li><strong>Received:</strong> Notes only (final status)</li>
                </ul>
              </div>
              <div>
                <p className="font-medium text-foreground mb-2">Always Locked:</p>
                <ul className="list-disc list-inside space-y-1 ml-2">
                  <li>Material name and SKU</li>
                  <li>Estimated cost and lead time</li>
                  <li>Container optimization data</li>
                  <li>Created by information</li>
                </ul>
              </div>
            </div>
            <p className="text-xs italic">
              💡 <strong>Tip:</strong> Notes field is always editable for tracking status updates and communication.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
