"use client"

/**
 * Manufacturing ERP - Professional Procurement Planning Table Component
 * 
 * Professional table component for displaying and managing procurement plans.
 * Includes filtering, sorting, bulk actions, and status management.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP UI Components
 */

import { useState, useMemo } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useSafeToast } from "@/hooks/use-safe-toast"
import {
  Package,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Download,
  RefreshCw,
  Plus
} from "lucide-react"

// ✅ PROFESSIONAL: Type definitions
interface ProcurementPlan {
  id: string
  rawMaterialId: string
  materialName: string
  materialSku?: string
  demandForecastId?: string
  plannedQty: string
  targetDate: string
  supplierId?: string
  supplierName?: string
  estimatedCost: string
  estimatedLeadTime: string
  priority: "low" | "normal" | "high" | "urgent"
  status: "draft" | "pending" | "approved" | "ordered" | "received"
  notes?: string
  createdAt: string
  updatedAt: string
}

interface ProcurementPlanningTableProps {
  data: ProcurementPlan[]
  isLoading?: boolean
  onRefresh?: () => void
  onStatusChange?: (id: string, status: string) => Promise<void>
  onBulkAction?: (ids: string[], action: string) => Promise<void>
  onView?: (id: string) => void
  onEdit?: (id: string) => void
  onDelete?: (id: string) => Promise<void>
}

export function ProcurementPlanningTable({
  data,
  isLoading = false,
  onRefresh,
  onStatusChange,
  onBulkAction,
  onView,
  onEdit,
  onDelete
}: ProcurementPlanningTableProps) {
  const router = useRouter()
  const { toast } = useSafeToast()

  // State management
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [priorityFilter, setPriorityFilter] = useState<string>("all")
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [sortField, setSortField] = useState<string>("targetDate")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {
    let filtered = data.filter(item => {
      const matchesSearch = searchTerm === "" ||
        item.materialName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.supplierName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.materialSku?.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesStatus = statusFilter === "all" || item.status === statusFilter
      const matchesPriority = priorityFilter === "all" || item.priority === priorityFilter

      return matchesSearch && matchesStatus && matchesPriority
    })

    // Sort data
    filtered.sort((a, b) => {
      let aValue: any = a[sortField as keyof ProcurementPlan]
      let bValue: any = b[sortField as keyof ProcurementPlan]

      // Handle date sorting
      if (sortField === "targetDate" || sortField === "createdAt") {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // Handle numeric sorting
      if (sortField === "plannedQty" || sortField === "estimatedCost") {
        aValue = parseFloat(aValue) || 0
        bValue = parseFloat(bValue) || 0
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
      return 0
    })

    return filtered
  }, [data, searchTerm, statusFilter, priorityFilter, sortField, sortDirection])

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // Handle selection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(filteredAndSortedData.map(item => item.id))
    } else {
      setSelectedItems([])
    }
  }

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id])
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id))
    }
  }

  // Handle actions
  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      console.log(`🔄 Table Action: Status change requested for plan ${id} to ${newStatus}`)
      console.log(`📋 Plan details:`, {
        planId: id,
        newStatus,
        timestamp: new Date().toISOString()
      })

      if (onStatusChange) {
        await onStatusChange(id, newStatus)
        console.log(`✅ Status change completed for plan ${id}`)
        toast({
          title: "Success",
          description: `Procurement plan status updated to ${newStatus}`,
        })
      } else {
        console.warn("⚠️ No onStatusChange handler provided")
      }
    } catch (error) {
      console.error("❌ Error in table status change:", error)
      toast({
        title: "Error",
        description: "Failed to update status",
        variant: "destructive",
      })
    }
  }

  const handleBulkAction = async (action: string) => {
    if (selectedItems.length === 0) {
      toast({
        title: "No Selection",
        description: "Please select items to perform bulk actions",
        variant: "destructive",
      })
      return
    }

    try {
      if (onBulkAction) {
        await onBulkAction(selectedItems, action)
        setSelectedItems([])
        toast({
          title: "Success",
          description: `Bulk ${action} completed successfully`,
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to perform bulk ${action}`,
        variant: "destructive",
      })
    }
  }

  // Status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "approved": return "default"
      case "pending": return "secondary"
      case "ordered": return "outline"
      case "received": return "default"
      case "draft": return "outline"
      default: return "outline"
    }
  }

  // Priority badge variant
  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case "urgent": return "destructive"
      case "high": return "secondary"
      case "normal": return "outline"
      case "low": return "outline"
      default: return "outline"
    }
  }

  // Priority icon
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "urgent": return <AlertTriangle className="h-3 w-3" />
      case "high": return <Clock className="h-3 w-3" />
      default: return null
    }
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Procurement Planning</h2>
          <p className="text-muted-foreground">
            Manage material procurement plans and supplier relationships
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={onRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button size="sm" onClick={() => router.push("/planning/procurement/create")}>
            <Plus className="h-4 w-4 mr-2" />
            New Plan
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search materials, suppliers, or SKUs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="ordered">Ordered</SelectItem>
                <SelectItem value="received">Received</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{selectedItems.length} items selected</span>
            <div className="flex items-center gap-2">
              <Button size="sm" variant="outline" onClick={() => handleBulkAction("approve")}>
                Approve Selected
              </Button>
              <Button size="sm" variant="outline" onClick={() => handleBulkAction("export")}>
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
              <Button size="sm" variant="outline" onClick={() => setSelectedItems([])}>
                Clear Selection
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedItems.length === filteredAndSortedData.length && filteredAndSortedData.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("materialName")}
                  >
                    Material
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("plannedQty")}
                  >
                    Quantity
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("targetDate")}
                  >
                    Target Date
                  </TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("estimatedCost")}
                  >
                    Est. Cost
                  </TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-12">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Loading procurement plans...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredAndSortedData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <Package className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">No procurement plans found</p>
                        <Button size="sm" onClick={() => router.push("/planning/procurement/create")}>
                          <Plus className="h-4 w-4 mr-2" />
                          Create First Plan
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredAndSortedData.map((plan) => (
                    <TableRow key={plan.id} className="hover:bg-muted/50">
                      <TableCell>
                        <Checkbox
                          checked={selectedItems.includes(plan.id)}
                          onCheckedChange={(checked) => handleSelectItem(plan.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{plan.materialName}</p>
                          {plan.materialSku && (
                            <p className="text-sm text-muted-foreground">{plan.materialSku}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {parseInt(plan.plannedQty).toLocaleString()} units
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(plan.targetDate).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        {plan.supplierName ? (
                          <div>
                            <p className="font-medium">{plan.supplierName}</p>
                            <p className="text-sm text-muted-foreground">
                              {plan.estimatedLeadTime} days lead time
                            </p>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Not assigned</span>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">
                        ${parseFloat(plan.estimatedCost).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getPriorityBadgeVariant(plan.priority)} className="flex items-center gap-1 w-fit">
                          {getPriorityIcon(plan.priority)}
                          {plan.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(plan.status)}>
                          {plan.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => onView?.(plan.id)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onEdit?.(plan.id)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Plan
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => {
                                console.log(`🎯 Approve clicked for plan:`, {
                                  id: plan.id,
                                  materialName: plan.materialName,
                                  currentStatus: plan.status
                                })
                                handleStatusChange(plan.id, "approved")
                              }}
                              disabled={plan.status === "approved" || plan.status === "ordered" || plan.status === "received"}
                            >
                              <CheckCircle className="h-4 w-4 mr-2" />
                              Approve
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => {
                                console.log(`🎯 Mark as Ordered clicked for plan:`, {
                                  id: plan.id,
                                  materialName: plan.materialName,
                                  currentStatus: plan.status
                                })
                                handleStatusChange(plan.id, "ordered")
                              }}
                              disabled={plan.status === "ordered" || plan.status === "received"}
                            >
                              <Package className="h-4 w-4 mr-2" />
                              Mark as Ordered
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      {filteredAndSortedData.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>
                Showing {filteredAndSortedData.length} of {data.length} procurement plans
              </span>
              <span>
                Total estimated cost: ${filteredAndSortedData.reduce((sum, plan) => sum + parseFloat(plan.estimatedCost), 0).toLocaleString()}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
