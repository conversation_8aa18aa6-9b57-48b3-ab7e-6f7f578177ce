"use client"

/**
 * Manufacturing ERP - Procurement Status Handler Component
 * 
 * Client component to handle URL-based status messages and convert them to toast notifications.
 * Provides better UX by showing toast notifications instead of persistent alert banners.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Next.js 15 Compatibility
 */

import { useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useSafeToast } from "@/hooks/use-safe-toast"

export function ProcurementStatusHandler() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useSafeToast()

  useEffect(() => {
    const success = searchParams.get('success')
    const error = searchParams.get('error')

    if (success === 'status-updated') {
      toast({
        title: "Success",
        description: "Procurement plan status updated successfully!",
      })
      
      // Clean up URL parameters
      const newUrl = window.location.pathname
      router.replace(newUrl, { scroll: false })
    }

    if (error === 'status-update-failed') {
      toast({
        title: "Error",
        description: "Failed to update procurement plan status. Please try again.",
        variant: "destructive",
      })
      
      // Clean up URL parameters
      const newUrl = window.location.pathname
      router.replace(newUrl, { scroll: false })
    }

    if (error === 'plan-not-found') {
      toast({
        title: "Error",
        description: "Procurement plan not found.",
        variant: "destructive",
      })
      
      // Clean up URL parameters
      const newUrl = window.location.pathname
      router.replace(newUrl, { scroll: false })
    }
  }, [searchParams, toast, router])

  return null // This component doesn't render anything
}
