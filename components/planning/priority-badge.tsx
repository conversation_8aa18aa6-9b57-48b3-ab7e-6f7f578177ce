/**
 * Manufacturing ERP - Unified Priority Badge Component
 * 
 * Provides consistent priority display with proper contrast and business terminology
 * Maps database priority values to user-friendly business labels
 */

import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

export type PriorityLevel = "low" | "normal" | "high" | "urgent"

interface PriorityBadgeProps {
  priority: PriorityLevel
  size?: "sm" | "md" | "lg"
  className?: string
}

/**
 * Business Priority Definitions:
 * 
 * CRITICAL (urgent): Immediate action required to prevent production delays
 * - Timeline: ≤80% of supplier lead time
 * - Impact: Production stoppage risk, customer delivery delays
 * 
 * HIGH (high): Important procurement that needs attention soon  
 * - Timeline: ≤100% of supplier lead time
 * - Impact: Potential delays if not addressed within days
 * 
 * NORMAL (normal): Standard procurement planning timeline
 * - Timeline: ≤150% of supplier lead time
 * - Impact: Normal business operations, adequate planning time
 * 
 * LOW (low): Future planning, no immediate action needed
 * - Timeline: >150% of supplier lead time
 * - Impact: Long-term planning, ample time for procurement
 */

export function PriorityBadge({ priority, size = "sm", className }: PriorityBadgeProps) {
  const getPriorityConfig = (priority: PriorityLevel) => {
    switch (priority) {
      case "urgent":
        return {
          label: "CRITICAL",
          variant: "destructive" as const,
          className: "bg-red-600 text-white hover:bg-red-700 font-semibold",
          description: "Immediate action required"
        }
      case "high":
        return {
          label: "HIGH",
          variant: "secondary" as const,
          className: "bg-orange-600 text-white hover:bg-orange-700 font-medium",
          description: "Needs attention soon"
        }
      case "normal":
        return {
          label: "NORMAL",
          variant: "outline" as const,
          className: "bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200",
          description: "Standard timeline"
        }
      case "low":
        return {
          label: "LOW",
          variant: "outline" as const,
          className: "bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200",
          description: "Future planning"
        }
      default:
        return {
          label: "NORMAL",
          variant: "outline" as const,
          className: "bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200",
          description: "Standard timeline"
        }
    }
  }

  const config = getPriorityConfig(priority)
  
  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    md: "text-sm px-3 py-1",
    lg: "text-base px-4 py-2"
  }

  return (
    <Badge 
      variant={config.variant}
      className={cn(
        config.className,
        sizeClasses[size],
        "font-medium tracking-wide",
        className
      )}
      title={`${config.label} Priority: ${config.description}`}
    >
      {config.label}
    </Badge>
  )
}

/**
 * Helper function to get priority display label
 */
export function getPriorityLabel(priority: PriorityLevel): string {
  switch (priority) {
    case "urgent": return "CRITICAL"
    case "high": return "HIGH" 
    case "normal": return "NORMAL"
    case "low": return "LOW"
    default: return "NORMAL"
  }
}

/**
 * Helper function to get priority business description
 */
export function getPriorityDescription(priority: PriorityLevel): string {
  switch (priority) {
    case "urgent": return "Immediate action required to prevent production delays"
    case "high": return "Important procurement that needs attention soon"
    case "normal": return "Standard procurement planning timeline"
    case "low": return "Future planning, no immediate action needed"
    default: return "Standard procurement planning timeline"
  }
}
