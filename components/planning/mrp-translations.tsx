"use client"

import { useI18n } from "@/components/i18n-provider"

export function useMRPTranslations() {
  const { t } = useI18n()
  
  return {
    title: t("mrp.title"),
    subtitle: t("mrp.subtitle"),
    refreshPriorities: t("mrp.refresh_priorities"),
    newForecast: t("mrp.new_forecast"),
    activeForecasts: t("mrp.active_forecasts"),
    approved: t("mrp.approved"),
    procurementStatus: t("mrp.procurement_status"),
    pendingApproval: t("mrp.pending_approval"),
    ordered: t("mrp.ordered"),
    needApproval: t("mrp.need_approval"),
    readyToOrder: t("mrp.ready_to_order"),
    allUpToDate: t("mrp.all_up_to_date"),
    procurementPlans: t("mrp.procurement_plans"),
    estimated: t("mrp.estimated"),
    pending: t("mrp.pending"),
    allApproved: t("mrp.all_approved"),
    actionRequired: t("mrp.action_required"),
    urgentHighPriority: t("mrp.urgent_high_priority"),
    immediateAction: t("mrp.immediate_action"),
    overdue: t("mrp.overdue"),
    noActionNeeded: t("mrp.no_action_needed"),
    supplierNetwork: t("mrp.supplier_network"),
    excellentRated: t("mrp.excellent_rated"),
    avgLeadTime: t("mrp.avg_lead_time"),
    daysAverageDelivery: t("mrp.days_average_delivery"),
    approvedPlansReady: t("mrp.approved_plans_ready"),
    actionAvailable: t("mrp.action_available"),
    noneReady: t("mrp.none_ready"),
    overview: t("mrp.overview"),
    forecasting: t("mrp.forecasting"),
    procurement: t("mrp.procurement"),
    analytics: t("mrp.analytics"),
  }
}

interface MRPHeaderProps {
  translations: ReturnType<typeof useMRPTranslations>
}

export function MRPHeader({ translations }: MRPHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{translations.title}</h1>
        <p className="text-muted-foreground">
          {translations.subtitle}
        </p>
      </div>
    </div>
  )
}
