"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight } from "lucide-react"
import { useI18n } from "./i18n-provider"

const pathKeyMap: Record<string, string> = {
  "/": "nav.item.dashboard",
  "/customers": "nav.item.customers",
  "/suppliers": "nav.item.suppliers",
  "/products": "nav.item.products",
  "/samples": "nav.item.samples",
  "/sales-contracts": "nav.item.sales-contracts",
  "/purchase-contracts": "nav.item.purchase-contracts",
  "/contract-templates": "nav.item.contract-templates",
  "/production": "nav.item.work-orders",
  "/bom": "nav.item.bom-management",
  "/planning": "nav.item.mrp-planning", // ✅ ADDED: MRP Planning breadcrumb support
  "/quality": "nav.item.quality-control",
  "/inventory": "nav.item.inventory",
  "/raw-materials": "nav.item.raw-materials",
  "/locations": "nav.item.locations",
  "/shipping": "nav.item.shipping",
  "/export": "nav.item.export-declarations",
  "/finance": "nav.item.accounting",
  "/finance/dashboard": "nav.item.financial-dashboard",
  "/finance/ar": "nav.item.accounts-receivable",
  "/finance/ap": "nav.item.accounts-payable",
  "/reports": "nav.item.reports",
}

export function Breadcrumbs() {
  const pathname = usePathname()
  const { t } = useI18n()
  const crumbs = [{ href: "/", label: t("nav.item.dashboard") }]

  if (pathname !== "/") {
    // Special handling for finance sub-pages to show proper hierarchy
    if (pathname.startsWith("/finance/")) {
      // Add Accounting as intermediate breadcrumb
      crumbs.push({ href: "/finance", label: t("nav.item.accounting") })

      // Add the specific finance page
      const key = pathKeyMap[pathname] || pathname
      crumbs.push({ href: pathname, label: t(key) })
    }
    // Special handling for inventory-related pages to show proper hierarchy
    else if (pathname === "/raw-materials" || pathname.startsWith("/raw-materials/")) {
      // Add Inventory as intermediate breadcrumb for raw materials
      crumbs.push({ href: "/inventory", label: t("nav.item.inventory") })

      if (pathname === "/raw-materials") {
        crumbs.push({ href: pathname, label: t("inventory.rawMaterials") })
      } else {
        // Handle raw materials sub-pages
        crumbs.push({ href: "/raw-materials", label: t("inventory.rawMaterials") })
        const segments = pathname.split("/").filter(Boolean)
        if (segments.length > 2) {
          // Add specific raw material page (e.g., /raw-materials/123)
          crumbs.push({ href: pathname, label: segments[segments.length - 1] })
        }
      }
    }
    else if (pathname === "/locations" || pathname.startsWith("/locations/")) {
      // Add Inventory as intermediate breadcrumb for locations
      crumbs.push({ href: "/inventory", label: t("nav.item.inventory") })

      if (pathname === "/locations") {
        crumbs.push({ href: pathname, label: t("nav.item.locations") })
      } else {
        // Handle location sub-pages
        crumbs.push({ href: "/locations", label: t("nav.item.locations") })
        const segments = pathname.split("/").filter(Boolean)
        if (segments.length > 1) {
          crumbs.push({ href: pathname, label: segments[segments.length - 1] })
        }
      }
    }
    else if (pathname === "/inventory") {
      // ✅ FIXED: Handle inventory without hydration issues
      // Default to showing "Finished Goods" for inventory page
      crumbs.push({ href: pathname, label: t("inventory.finishedGoods") })
    }
    else {
      // Default behavior for other pages
      const key = pathKeyMap[pathname] || pathname
      crumbs.push({ href: pathname, label: t(key) })
    }
  }

  return (
    <nav aria-label="Breadcrumb" className="mb-4 text-sm text-muted-foreground">
      <ol className="flex items-center gap-1">
        {crumbs.map((c, idx) => (
          <li key={c.href} className="flex items-center">
            {idx > 0 && <ChevronRight className="mx-1 h-4 w-4" aria-hidden="true" />}
            {idx < crumbs.length - 1 ? (
              <Link className="hover:underline hover:text-foreground transition-colors" href={c.href}>
                {c.label}
              </Link>
            ) : (
              <span aria-current="page" className="text-foreground font-medium">
                {c.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}
