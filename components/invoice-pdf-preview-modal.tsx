"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Download, FileText, X, AlertCircle, Maximize2, Minimize2 } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface InvoicePDFPreviewModalProps {
  isOpen: boolean
  onClose: () => void
  invoiceId: string
  invoiceNumber: string
  invoiceType: 'ar' | 'ap'
}

export function InvoicePDFPreviewModal({
  isOpen,
  onClose,
  invoiceId,
  invoiceNumber,
  invoiceType
}: InvoicePDFPreviewModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [pdfUrl, setPdfUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const { toast } = useSafeToast()

  // Load PDF data when modal opens
  useEffect(() => {
    if (isOpen && invoiceId) {
      loadPDFPreview()
    }
    return () => {
      // Cleanup PDF URL when modal closes
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl)
        setPdfUrl(null)
      }
    }
  }, [isOpen, invoiceId])

  const loadPDFPreview = async () => {
    setIsLoading(true)
    setError(null)

    try {
      console.log(`Loading PDF preview for ${invoiceType} invoice:`, invoiceId)

      // Fetch invoice data for PDF generation
      const response = await fetch(`/api/finance/${invoiceType}/${invoiceId}/pdf`)

      console.log(`PDF API response status:`, response.status)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('PDF API error:', errorData)
        throw new Error(errorData.message || `Failed to fetch invoice data (${response.status})`)
      }

      const responseData = await response.json()
      console.log('PDF API response data:', responseData)

      const { invoiceData } = responseData

      if (!invoiceData) {
        throw new Error('No invoice data received from API')
      }

      // Import PDF generation function dynamically
      const { generateInvoicePDFBlob } = await import('@/components/pdf-invoice-document')

      // Generate PDF blob for preview
      const pdfBlob = await generateInvoicePDFBlob(invoiceData, invoiceType)

      // Create object URL for preview
      const url = URL.createObjectURL(pdfBlob)
      setPdfUrl(url)

    } catch (error) {
      console.error('PDF preview error:', error)
      setError(error instanceof Error ? error.message : 'Failed to load PDF preview')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDownload = async () => {
    try {
      // Fetch invoice data for PDF generation
      const response = await fetch(`/api/finance/${invoiceType}/${invoiceId}/pdf`)
      if (!response.ok) {
        throw new Error('Failed to fetch invoice data')
      }

      const { invoiceData } = await response.json()

      // Import PDF generation function dynamically
      const { generateInvoicePDF } = await import('@/components/pdf-invoice-document')

      // Generate and download PDF
      await generateInvoicePDF(invoiceData, invoiceType)

      toast({
        title: "PDF Downloaded",
        description: `${invoiceType.toUpperCase()} Invoice ${invoiceNumber} has been downloaded`,
        variant: "default"
      })
    } catch (error) {
      console.error('PDF download error:', error)
      toast({
        title: "Download Failed",
        description: "Failed to download PDF. Please try again.",
        variant: "destructive"
      })
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const invoiceTypeLabel = invoiceType === 'ar' ? 'AR' : 'AP'
  const invoiceTypeDescription = invoiceType === 'ar' ? 'Accounts Receivable' : 'Accounts Payable'

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={`${isFullscreen
          ? "w-[98vw] max-w-none h-[98vh]"
          : "max-w-4xl max-h-[90vh]"
          } overflow-hidden flex flex-col p-0`}
      >
        <DialogHeader className="flex-shrink-0 border-b p-6 bg-white">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center gap-2 text-xl">
                <FileText className="h-5 w-5" />
                {invoiceTypeLabel} Invoice Preview - {invoiceNumber}
              </DialogTitle>
              <p className="text-sm text-muted-foreground mt-1">
                {invoiceTypeDescription} invoice document preview
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Download PDF
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleFullscreen}
                className="flex items-center gap-2"
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
                {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden bg-gray-100 p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading PDF preview...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <Alert className="max-w-md">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">Failed to load PDF preview</p>
                    <p className="text-sm">{error}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={loadPDFPreview}
                      className="mt-2"
                    >
                      Try Again
                    </Button>
                  </div>
                </AlertDescription>
              </Alert>
            </div>
          ) : pdfUrl ? (
            <div className="w-full h-full border rounded-lg overflow-hidden bg-white">
              <iframe
                src={pdfUrl}
                className="w-full h-full border-none"
                title={`Preview of ${invoiceTypeLabel} Invoice ${invoiceNumber}`}
              />
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No PDF preview available</p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
