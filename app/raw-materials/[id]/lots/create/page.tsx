/**
 * Manufacturing ERP - Create Raw Material Lot Page
 * Professional form for adding inventory lots to raw materials
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Package } from "lucide-react"
import Link from "next/link"
import { db } from "@/lib/db"
import { rawMaterials, suppliers } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { CreateRawMaterialLotForm } from "@/components/raw-materials/create-raw-material-lot-form"

export default async function CreateRawMaterialLotPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // ✅ REAL DATA: Fetch raw material and suppliers
  const [material, suppliersList] = await Promise.all([
    db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ),
    }),
    db.query.suppliers.findMany({
      where: eq(suppliers.company_id, context.companyId),
      orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
      limit: 100,
    }),
  ])

  if (!material) {
    notFound()
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Professional Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/raw-materials/${material.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Material
            </Link>
          </Button>
          <div className="flex items-center gap-2">
            <Package className="h-6 w-6 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Add Inventory Lot</h1>
              <p className="text-muted-foreground">
                Add new inventory lot for {material.name} ({material.sku})
              </p>
            </div>
          </div>
        </div>

        {/* Create Form */}
        <div className="max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle>Lot Information</CardTitle>
            </CardHeader>
            <CardContent>
              <CreateRawMaterialLotForm material={material} suppliers={suppliersList} />
            </CardContent>
          </Card>
        </div>

        {/* Professional Note */}
        <Card>
          <CardContent className="pt-6">
            <p className="text-sm text-muted-foreground">
              <strong>Note:</strong> Inventory lots track specific batches of materials 
              with their own costs, quality status, and supplier information. This enables 
              FIFO consumption tracking and accurate cost allocation.
            </p>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
