/**
 * Manufacturing ERP - Edit Raw Material Page
 * Professional form for editing existing raw materials
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Package2 } from "lucide-react"
import Link from "next/link"
import { db } from "@/lib/db"
import { rawMaterials, suppliers } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { EditRawMaterialForm } from "@/components/raw-materials/edit-raw-material-form"

export default async function EditRawMaterialPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // ✅ REAL DATA: Fetch raw material and suppliers
  const [material, suppliersList] = await Promise.all([
    db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ),
      with: {
        primarySupplier: true,
      },
    }),
    db.query.suppliers.findMany({
      where: eq(suppliers.company_id, context.companyId),
      orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
      limit: 100,
    }),
  ])

  if (!material) {
    notFound()
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Professional Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/raw-materials/${material.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Material
            </Link>
          </Button>
          <div className="flex items-center gap-2">
            <Package2 className="h-6 w-6 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Edit Raw Material</h1>
              <p className="text-muted-foreground">
                Update {material.name} ({material.sku})
              </p>
            </div>
          </div>
        </div>

        {/* Edit Form */}
        <div className="max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle>Raw Material Information</CardTitle>
            </CardHeader>
            <CardContent>
              <EditRawMaterialForm material={material} suppliers={suppliersList} />
            </CardContent>
          </Card>
        </div>

        {/* Professional Note */}
        <Card>
          <CardContent className="pt-6">
            <p className="text-sm text-muted-foreground">
              <strong>Note:</strong> Changes to material specifications may affect existing 
              BOM calculations and work order costs. Review related products after making changes.
            </p>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
