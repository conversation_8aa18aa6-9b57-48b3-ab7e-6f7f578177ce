/**
 * Manufacturing ERP - Raw Material Detail Page
 * Professional detail view with lots, consumption history, and BOM usage
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Edit, Plus, Package, TrendingUp, AlertTriangle, DollarSign } from "lucide-react"
import Link from "next/link"
import { db } from "@/lib/db"
import { rawMaterials, rawMaterialLots } from "@/lib/schema-postgres"
import { eq, and, desc } from "drizzle-orm"
import { ConsumptionHistoryTab } from "@/components/raw-materials/consumption-history-tab"
import { LotActions } from "@/components/raw-materials/lot-actions"

export default async function RawMaterialDetailPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // ✅ REAL DATA: Fetch raw material with relationships
  const material = await db.query.rawMaterials.findFirst({
    where: and(
      eq(rawMaterials.id, id),
      eq(rawMaterials.company_id, context.companyId)
    ),
    with: {
      primarySupplier: true,
      lots: {
        orderBy: [desc(rawMaterialLots.created_at)],
        with: {
          supplier: true,
          purchaseContract: true,
          inspection: true,
        },
      },
      bomItems: {
        with: {
          product: true,
        },
      },
    },
  })

  if (!material) {
    notFound()
  }

  // Calculate summary statistics
  const totalLots = material.lots.length
  const availableLots = material.lots.filter(lot => lot.status === "available")
  const totalQty = material.lots.reduce((sum, lot) => sum + parseFloat(lot.qty || "0"), 0)
  const availableQty = availableLots.reduce((sum, lot) => sum + parseFloat(lot.qty || "0"), 0)
  const totalValue = material.lots.reduce((sum, lot) => {
    return sum + (parseFloat(lot.qty || "0") * parseFloat(lot.unit_cost || "0"))
  }, 0)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case "inactive":
        return <Badge variant="secondary">Inactive</Badge>
      case "discontinued":
        return <Badge variant="destructive">Discontinued</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getCategoryBadge = (category: string) => {
    const categoryColors = {
      yarn: "bg-blue-100 text-blue-800",
      fabric: "bg-purple-100 text-purple-800",
      dyes: "bg-red-100 text-red-800",
      chemicals: "bg-yellow-100 text-yellow-800",
      accessories: "bg-green-100 text-green-800",
      other: "bg-gray-100 text-gray-800",
    }

    return (
      <Badge className={categoryColors[category as keyof typeof categoryColors] || categoryColors.other}>
        {category.charAt(0).toUpperCase() + category.slice(1)}
      </Badge>
    )
  }

  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(num || 0)
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Professional Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/raw-materials">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Raw Materials
              </Link>
            </Button>
            <div>
              <div className="flex items-center gap-3">
                <h1 className="text-3xl font-bold tracking-tight">{material.name}</h1>
                {getStatusBadge(material.status)}
                {getCategoryBadge(material.category)}
              </div>
              <p className="text-muted-foreground">
                SKU: {material.sku} • Unit: {material.unit}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href={`/raw-materials/${material.id}/lots/create`}>
                <Plus className="mr-2 h-4 w-4" />
                Add Lot
              </Link>
            </Button>
            <Button asChild>
              <Link href={`/raw-materials/${material.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Lots</p>
                  <p className="text-2xl font-bold">{totalLots}</p>
                </div>
                <Package className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Available Qty</p>
                  <p className="text-2xl font-bold">{availableQty.toFixed(2)}</p>
                  <p className="text-xs text-muted-foreground">{material.unit}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                  <p className="text-2xl font-bold">{formatCurrency(totalValue)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Standard Cost</p>
                  <p className="text-2xl font-bold">
                    {material.standard_cost ? formatCurrency(material.standard_cost) : "N/A"}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Material Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Material Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Primary Supplier</p>
                  <p className="font-medium">
                    {material.primarySupplier?.name || "No supplier assigned"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Composition</p>
                  <p className="font-medium">{material.composition || "Not specified"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Quality Grade</p>
                  <p className="font-medium">{material.quality_grade || "Not specified"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Lead Time</p>
                  <p className="font-medium">{material.lead_time_days} days</p>
                </div>
              </div>
              {material.specifications && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Specifications</p>
                  <p className="text-sm">{material.specifications}</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Inventory Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Reorder Point</p>
                <p className="font-medium">{material.reorder_point} {material.unit}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Max Stock Level</p>
                <p className="font-medium">{material.max_stock_level} {material.unit}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Inspection Required</p>
                <Badge variant={material.inspection_required === "true" ? "default" : "secondary"}>
                  {material.inspection_required === "true" ? "Yes" : "No"}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs for Lots, BOM Usage, Consumption History */}
        <Tabs defaultValue="lots" className="space-y-4">
          <TabsList>
            <TabsTrigger value="lots">Inventory Lots ({totalLots})</TabsTrigger>
            <TabsTrigger value="bom">BOM Usage ({material.bomItems.length})</TabsTrigger>
            <TabsTrigger value="consumption">Consumption History</TabsTrigger>
          </TabsList>

          <TabsContent value="lots">
            <Card>
              <CardHeader>
                <CardTitle>Inventory Lots</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Lot Number</TableHead>
                        <TableHead>Supplier</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Unit Cost</TableHead>
                        <TableHead>Received Date</TableHead>
                        <TableHead>Quality Status</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {material.lots.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                            No inventory lots found. Add your first lot to get started.
                          </TableCell>
                        </TableRow>
                      ) : (
                        material.lots.map((lot) => (
                          <TableRow key={lot.id}>
                            <TableCell className="font-mono text-sm">
                              {lot.lot_number || `LOT-${lot.id.slice(-8)}`}
                            </TableCell>
                            <TableCell>{lot.supplier?.name || "Unknown"}</TableCell>
                            <TableCell>
                              {parseFloat(lot.qty || "0").toFixed(2)} {material.unit}
                            </TableCell>
                            <TableCell>{formatCurrency(lot.unit_cost || "0")}</TableCell>
                            <TableCell>{formatDate(lot.received_date)}</TableCell>
                            <TableCell>
                              <Badge variant={lot.quality_status === "approved" ? "default" : "secondary"}>
                                {lot.quality_status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant={lot.status === "available" ? "default" : "secondary"}>
                                {lot.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <LotActions
                                lotId={lot.id}
                                lotNumber={lot.lot_number || `LOT-${lot.id.slice(-8)}`}
                                materialId={material.id}
                                materialName={material.name}
                                quantity={lot.qty || "0"}
                                unit={material.unit}
                              />
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="bom">
            <Card>
              <CardHeader>
                <CardTitle>Bill of Materials Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>Quantity Required</TableHead>
                        <TableHead>Unit</TableHead>
                        <TableHead>Waste Factor</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {material.bomItems.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                            This material is not used in any product BOMs yet.
                          </TableCell>
                        </TableRow>
                      ) : (
                        material.bomItems.map((bomItem) => (
                          <TableRow key={bomItem.id}>
                            <TableCell>
                              <Link
                                href={`/products/${bomItem.product_id}`}
                                className="font-medium text-blue-600 hover:underline"
                              >
                                {bomItem.product?.name || "Unknown Product"}
                              </Link>
                            </TableCell>
                            <TableCell>{bomItem.qty_required}</TableCell>
                            <TableCell>{bomItem.unit}</TableCell>
                            <TableCell>{(parseFloat(bomItem.waste_factor || "0") * 100).toFixed(1)}%</TableCell>
                            <TableCell>
                              <Badge variant={bomItem.status === "active" ? "default" : "secondary"}>
                                {bomItem.status}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="consumption">
            <ConsumptionHistoryTab materialId={material.id} />
          </TabsContent>
        </Tabs>
      </div>
    </AppShell>
  )
}
