/**
 * Manufacturing ERP - Create Raw Material Page
 * Professional form for creating new raw materials with supplier integration
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Save, Package2 } from "lucide-react"
import Link from "next/link"
import { db } from "@/lib/db"
import { suppliers } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { CreateRawMaterialForm } from "@/components/raw-materials/create-raw-material-form"

export default async function CreateRawMaterialPage() {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // ✅ REAL DATA: Fetch suppliers for dropdown
  const suppliersList = await db.query.suppliers.findMany({
    where: eq(suppliers.company_id, context.companyId),
    orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
    limit: 100,
  })

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Professional Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/raw-materials">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Raw Materials
            </Link>
          </Button>
          <div className="flex items-center gap-2">
            <Package2 className="h-6 w-6 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Create Raw Material</h1>
              <p className="text-muted-foreground">
                Add a new raw material to your inventory system
              </p>
            </div>
          </div>
        </div>

        {/* Create Form */}
        <div className="max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle>Raw Material Information</CardTitle>
            </CardHeader>
            <CardContent>
              <CreateRawMaterialForm suppliers={suppliersList} />
            </CardContent>
          </Card>
        </div>

        {/* Professional Note */}
        <Card>
          <CardContent className="pt-6">
            <p className="text-sm text-muted-foreground">
              <strong>Note:</strong> After creating the raw material, you can add inventory lots 
              through the material detail page. Make sure to set up proper BOM relationships 
              for production planning.
            </p>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
