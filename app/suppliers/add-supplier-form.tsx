"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { useRouter } from "next/navigation"
import { useTransition } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"

const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  contact_name: z.string().optional(),
  contact_phone: z.string().optional(),
  contact_email: z.string().email({ message: "Invalid email address." }).optional().or(z.literal("")),
  address: z.string().optional(),
  bank: z.string().optional(),
  tax_id: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

export function AddSupplierForm({ setOpen }: { setOpen?: () => void }) {
  const router = useRouter()
  const { t } = useI18n()
  const [isPending, startTransition] = useTransition()
  const toast = useSafeToast()

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      contact_name: "",
      contact_phone: "",
      contact_email: "",
      address: "",
      bank: "",
      tax_id: "",
    },
  })

  async function onSubmit(values: FormValues) {
    const response = await fetch("/api/suppliers", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(values),
    })

    if (response.ok) {
      // Use startTransition to handle state updates safely
      startTransition(() => {
        if (setOpen) {
          setOpen() // Close dialog if in modal context
        } else {
          router.push('/suppliers') // Navigate back to suppliers list if in page context
        }
        router.refresh()
      })

      // Show toast using safe toast hook
      toast.success(t("suppliers.success.created"))
    } else {
      const errorData = await response.json().catch(() => ({}))
      toast.error(
        t("suppliers.error.create"),
        errorData.error || "An unexpected error occurred."
      )
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("suppliers.form.name")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Global Tech Supplies" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="contact_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("suppliers.form.contact_name")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Jane Smith" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="contact_email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("suppliers.form.contact_email")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. <EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="contact_phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("suppliers.form.contact_phone")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. ******-987-6543" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("suppliers.form.address")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. 456 Industrial Ave, Tech City" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="bank"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("suppliers.form.bank")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Bank of Innovation, *********" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="tax_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("suppliers.form.tax_id")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. VAT ID, EIN" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="ghost"
            onClick={() => {
              if (setOpen) {
                setOpen() // Close dialog if in modal context
              } else {
                router.push('/suppliers') // Navigate back if in page context
              }
            }}
          >
            {t("common.cancel")}
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting || isPending}>
            {(form.formState.isSubmitting || isPending) ? t("common.loading") : t("common.create") + " " + t("field.supplier")}
          </Button>
        </div>
      </form>
    </Form>
  )
}
