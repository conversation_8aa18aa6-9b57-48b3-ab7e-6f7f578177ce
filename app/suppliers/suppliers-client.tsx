"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Eye, Pencil, Trash2, Plus, Search } from "lucide-react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { AddSupplierForm } from "./add-supplier-form"
import { EditSupplierForm } from "./edit-supplier-form"
import { useRouter } from "next/navigation"
import { useTabNavigation } from "@/components/tab-navigation"
import { useI18n } from "@/components/i18n-provider"

// This type should match the data returned from the API /api/suppliers
export type SupplierFromApi = {
  id: string
  name: string
  contact_name: string | null
  contact_phone: string | null
  contact_email: string | null
  address: string | null
  tax_id: string | null
  bank: string | null
  status: string
  created_at: string
}

interface SuppliersClientPageProps {
  initialSuppliers: SupplierFromApi[]
}

export function SuppliersClientPage({ initialSuppliers }: SuppliersClientPageProps) {
  const router = useRouter()
  const { t } = useI18n()
  const { openSupplierAddTab, openSupplierViewTab } = useTabNavigation()
  const [searchTerm, setSearchTerm] = useState("")
  const [supplierToDelete, setSupplierToDelete] = useState<SupplierFromApi | null>(null)
  const [supplierToEdit, setSupplierToEdit] = useState<SupplierFromApi | null>(null)

  const handleDeleteSupplier = async () => {
    if (!supplierToDelete) return

    try {
      const response = await fetch(`/api/suppliers/${supplierToDelete.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        // Refresh the page to get updated data
        router.refresh()
        setSupplierToDelete(null)
      } else {
        console.error('Failed to delete supplier')
      }
    } catch (error) {
      console.error('Error deleting supplier:', error)
    }
  }

  const handleEditSupplier = (supplier: SupplierFromApi) => {
    setSupplierToEdit(supplier)
  }

  // Filter suppliers based on search term
  const filteredSuppliers = initialSuppliers.filter(supplier => {
    const search = searchTerm.toLowerCase()
    const companyName = supplier.name.toLowerCase()
    const contactName = supplier.contact_name?.toLowerCase() || ""
    const contactEmail = supplier.contact_email?.toLowerCase() || ""

    return companyName.includes(search) ||
      contactName.includes(search) ||
      contactEmail.includes(search)
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("suppliers.title")}</h1>
          <p className="text-muted-foreground">
            {t("suppliers.subtitle")}
          </p>
        </div>
        <Button onClick={openSupplierAddTab}>
          <Plus className="mr-2 h-4 w-4" />
          {t("suppliers.add")}
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search suppliers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("suppliers.table.company_name")}</TableHead>
              <TableHead>{t("suppliers.table.contact_person")}</TableHead>
              <TableHead>{t("suppliers.table.phone")}</TableHead>
              <TableHead>{t("suppliers.table.email")}</TableHead>
              <TableHead>{t("suppliers.table.address")}</TableHead>
              <TableHead>{t("suppliers.table.bank")}</TableHead>
              <TableHead>{t("suppliers.table.status")}</TableHead>
              <TableHead className="text-right">{t("suppliers.table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSuppliers.map((supplier) => (
              <TableRow key={supplier.id} className="hover:bg-muted/50">
                <TableCell className="font-medium">{supplier.name}</TableCell>
                <TableCell>{supplier.contact_name || "-"}</TableCell>
                <TableCell>{supplier.contact_phone || "-"}</TableCell>
                <TableCell>{supplier.contact_email || "-"}</TableCell>
                <TableCell className="max-w-[200px] truncate" title={supplier.address || ""}>
                  {supplier.address || "-"}
                </TableCell>
                <TableCell>{supplier.bank || "-"}</TableCell>
                <TableCell>
                  <Badge variant={supplier.status === "active" ? "default" : "secondary"}>
                    {t(`status.${supplier.status}` as keyof typeof t) !== `status.${supplier.status}` ? t(`status.${supplier.status}` as keyof typeof t) : supplier.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex gap-2 justify-end">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openSupplierViewTab(supplier.id, supplier.name)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      {t("common.view")}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditSupplier(supplier)}
                    >
                      <Pencil className="h-4 w-4 mr-1" />
                      {t("common.edit")}
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => setSupplierToDelete(supplier)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      {t("common.delete")}
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Edit Supplier Dialog */}
      <Dialog open={!!supplierToEdit} onOpenChange={() => setSupplierToEdit(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{t("suppliers.edit.title")}</DialogTitle>
            <DialogDescription>
              {t("suppliers.edit.description")}
            </DialogDescription>
          </DialogHeader>
          {supplierToEdit && (
            <EditSupplierForm
              supplier={supplierToEdit}
              setOpen={() => {
                setSupplierToEdit(null)
                router.refresh()
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!supplierToDelete} onOpenChange={() => setSupplierToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("common.confirm_delete")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("suppliers.delete.confirmation", { name: supplierToDelete?.name })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteSupplier}>
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
