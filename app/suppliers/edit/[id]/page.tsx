import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { suppliers } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { EditSupplierForm } from "../../edit-supplier-form"

export default async function EditSupplierPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // Fetch supplier with proper tenant isolation
  const supplier = await db.query.suppliers.findFirst({
    where: and(
      eq(suppliers.id, id),
      eq(suppliers.company_id, context.companyId)
    ),
  })

  if (!supplier) {
    notFound()
  }

  return (
    <AppShell>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Supplier</h1>
          <p className="text-muted-foreground">
            Update supplier information and contact details.
          </p>
        </div>

        <div className="max-w-2xl">
          <EditSupplierForm
            supplier={supplier}
            setOpen={() => {}}
          />
        </div>
      </div>
    </AppShell>
  )
}
