"use client";

import { useState, useMemo } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Building, Phone, Mail, MapPin, CreditCard, FileText, Eye, Calendar, DollarSign, Package, Search } from "lucide-react";
import Link from "next/link";
import { useI18n } from "@/components/i18n-provider";

type Supplier = {
  id: string;
  name: string;
  contact_name: string | null;
  contact_phone: string | null;
  contact_email: string | null;
  address: string | null;
  tax_id: string | null;
  bank: string | null;
  status: string | null;
  created_at: Date | null;
};

type PurchaseContract = {
  id: string;
  number: string;
  date: string;
  status: string;
  currency: string | null;
  created_at: Date | null;
  items: Array<{
    id: string;
    qty: string;
    price: string;
    product: {
      id: string;
      name: string;
      sku: string;
    } | null;
  }>;
};

type Sample = {
  id: string;
  code: string;
  name: string;
  sample_direction: string;
  status: string;
  approval_status: string | null;
  priority: string | null;
  date: string;
  created_at: Date | null;
  customer: {
    id: string;
    name: string;
  } | null;
  product: {
    id: string;
    name: string;
    sku: string;
  } | null;
};

interface SupplierViewClientProps {
  supplier: Supplier;
  purchaseContracts: PurchaseContract[];
  relatedSamples: Sample[];
}

export function SupplierViewClient({ supplier, purchaseContracts, relatedSamples }: SupplierViewClientProps) {
  const { t } = useI18n();
  const [sampleSearchTerm, setSampleSearchTerm] = useState("");

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case "active":
        return "default";
      case "inactive":
        return "secondary";
      case "pending":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getContractStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "default";
      case "completed":
        return "secondary";
      case "draft":
        return "outline";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const calculateContractTotal = (contract: PurchaseContract) => {
    // Safe check for items property since we simplified the query
    if (!contract.items || !Array.isArray(contract.items)) {
      return 0;
    }
    return contract.items.reduce((total, item) => {
      return total + (parseFloat(item.qty) * parseFloat(item.price));
    }, 0);
  };

  const getSampleStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return "default";
      case "pending":
        return "secondary";
      case "rejected":
        return "destructive";
      case "in_progress":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getSampleDirectionIcon = (direction: string) => {
    switch (direction) {
      case "inbound":
        return "📥";
      case "outbound":
        return "📤";
      case "internal":
        return "🏭";
      default:
        return "📦";
    }
  };

  // Filter samples based on search term
  const filteredSamples = useMemo(() => {
    if (!sampleSearchTerm.trim()) {
      return relatedSamples;
    }

    const searchLower = sampleSearchTerm.toLowerCase();
    return relatedSamples.filter((sample) =>
      sample.name.toLowerCase().includes(searchLower) ||
      sample.code.toLowerCase().includes(searchLower) ||
      sample.sample_direction.toLowerCase().includes(searchLower) ||
      sample.status.toLowerCase().includes(searchLower) ||
      (sample.approval_status && sample.approval_status.toLowerCase().includes(searchLower)) ||
      (sample.product && sample.product.name && sample.product.name.toLowerCase().includes(searchLower)) ||
      (sample.customer && sample.customer.name && sample.customer.name.toLowerCase().includes(searchLower))
    );
  }, [relatedSamples, sampleSearchTerm]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{supplier.name}</h1>
        <p className="text-muted-foreground">
          {t("suppliers.view.subtitle")}
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Supplier Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              {t("suppliers.view.supplier_info")}
            </CardTitle>
            <CardDescription>
              {t("suppliers.view.supplier_info_desc")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">{t("field.status")}</span>
              <Badge variant={getStatusColor(supplier.status)}>
                {t(`status.${supplier.status}` as keyof typeof t) !== `status.${supplier.status}` ? t(`status.${supplier.status}` as keyof typeof t) : supplier.status}
              </Badge>
            </div>

            {supplier.contact_name && (
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">{t("field.contact_person")}:</span>
                <span className="text-sm">{supplier.contact_name}</span>
              </div>
            )}

            {supplier.contact_phone && (
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">{t("field.phone")}:</span>
                <span className="text-sm">{supplier.contact_phone}</span>
              </div>
            )}

            {supplier.contact_email && (
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">{t("field.email")}:</span>
                <span className="text-sm">{supplier.contact_email}</span>
              </div>
            )}

            {supplier.address && (
              <div className="flex items-start gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                <div>
                  <span className="text-sm font-medium">{t("field.address")}:</span>
                  <p className="text-sm text-muted-foreground">{supplier.address}</p>
                </div>
              </div>
            )}

            {supplier.tax_id && (
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">{t("field.tax_id")}:</span>
                <span className="text-sm">{supplier.tax_id}</span>
              </div>
            )}

            {supplier.bank && (
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">{t("field.bank")}:</span>
                <span className="text-sm">{supplier.bank}</span>
              </div>
            )}

            {supplier.created_at && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">{t("field.created_at")}:</span>
                <span className="text-sm">{new Date(supplier.created_at).toLocaleDateString()}</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Purchase Contracts Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t("suppliers.view.purchase_contracts")}
            </CardTitle>
            <CardDescription>
              {t("suppliers.view.purchase_contracts_desc")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {purchaseContracts.length > 0 ? (
              <div className="space-y-4">
                {purchaseContracts.slice(0, 3).map((contract) => (
                  <div key={contract.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{contract.number}</span>
                        <Badge variant={getContractStatusColor(contract.status)}>
                          {t(`status.${contract.status}` as keyof typeof t) !== `status.${contract.status}` ? t(`status.${contract.status}` as keyof typeof t) : contract.status}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>{new Date(contract.date).toLocaleDateString()}</span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />
                          {calculateContractTotal(contract).toFixed(2)} {contract.currency || 'USD'}
                        </span>
                      </div>
                    </div>
                    <Link href={`/purchase-contracts/view/${contract.id}`}>
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-1" />
                        {t("common.view")}
                      </Button>
                    </Link>
                  </div>
                ))}

                {purchaseContracts.length > 3 && (
                  <div className="text-center pt-2">
                    <Link href="/purchase-contracts">
                      <Button variant="outline" size="sm">
                        {t("suppliers.view.view_all_contracts")} ({purchaseContracts.length})
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-6">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">{t("suppliers.view.no_contracts")}</h3>
                <p className="text-muted-foreground mb-4">
                  {t("suppliers.view.no_contracts_desc")}
                </p>
                <Link href="/purchase-contracts/add">
                  <Button>
                    <FileText className="h-4 w-4 mr-2" />
                    {t("suppliers.view.create_contract")}
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Related Samples with Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Related Samples
            </CardTitle>
            <CardDescription>
              Samples related to this supplier
            </CardDescription>
          </CardHeader>
          <CardContent>
            {relatedSamples.length > 0 ? (
              <div className="space-y-4">
                {/* Search Input */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search samples by name, code, direction, status..."
                    value={sampleSearchTerm}
                    onChange={(e) => setSampleSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Filtered Results */}
                {filteredSamples.length > 0 ? (
                  <div className="space-y-4">
                    {filteredSamples.map((sample) => (
                      <div key={sample.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="text-sm">{getSampleDirectionIcon(sample.sample_direction)}</span>
                            <span className="font-medium">{sample.name}</span>
                            <Badge variant="outline" className="text-xs">
                              {sample.code}
                            </Badge>
                            {sample.approval_status && (
                              <Badge variant={getSampleStatusColor(sample.approval_status)}>
                                {sample.approval_status}
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>{new Date(sample.date).toLocaleDateString()}</span>
                            <span className="capitalize">{sample.sample_direction}</span>
                            {sample.product && sample.product.name && (
                              <span className="flex items-center gap-1">
                                <Package className="h-3 w-3" />
                                {sample.product.name}
                              </span>
                            )}
                            {sample.priority && (
                              <Badge variant="outline" className="text-xs">
                                {sample.priority}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <Link href={`/samples/${sample.id}`}>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                        </Link>
                      </div>
                    ))}


                  </div>
                ) : (
                  <div className="text-center py-6">
                    <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No samples found</h3>
                    <p className="text-muted-foreground mb-4">
                      No samples match your search criteria. Try adjusting your search terms.
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-6">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Related Samples</h3>
                <p className="text-muted-foreground mb-4">
                  No samples have been associated with this supplier yet.
                </p>
                <Link href="/samples/create">
                  <Button>
                    <Package className="h-4 w-4 mr-2" />
                    Create Sample
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div >
  );
}
