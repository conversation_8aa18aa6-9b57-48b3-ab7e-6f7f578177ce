import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { suppliers, purchaseContracts, samples } from "@/lib/schema-postgres";
import { eq, and } from "drizzle-orm";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect, notFound } from "next/navigation";
import { SupplierViewClient } from "./supplier-view-client";

export default async function SupplierViewPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  const { id } = await params

  // 🛡️ SECURE: Only fetch supplier for the current company
  const supplier = await db.query.suppliers.findFirst({
    where: and(
      eq(suppliers.id, id),
      eq(suppliers.company_id, context.companyId)
    ),
  });

  if (!supplier) {
    notFound();
  }

  // 🛡️ SECURE: Only fetch purchase contracts for this supplier and company
  const relatedPurchaseContracts = await db.query.purchaseContracts.findMany({
    where: and(
      eq(purchaseContracts.supplier_id, id),
      eq(purchaseContracts.company_id, context.companyId)
    ),
    with: {
      items: true,
    },
    orderBy: (purchaseContracts, { desc }) => [desc(purchaseContracts.created_at)],
  });

  // 🛡️ SECURE: Only fetch related samples for this supplier and company
  const relatedSamples = await db.query.samples.findMany({
    where: and(
      eq(samples.supplier_id, id),
      eq(samples.company_id, context.companyId)
    ),
    with: {
      customer: true,
      product: true,
    },
    orderBy: (samples, { desc }) => [desc(samples.created_at)],
  });

  return (
    <AppShell>
      <SupplierViewClient
        supplier={supplier}
        purchaseContracts={relatedPurchaseContracts}
        relatedSamples={relatedSamples}
      />
    </AppShell>
  );
}
