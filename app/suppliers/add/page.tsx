import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { AddSupplierForm } from "../add-supplier-form"

export default async function AddSupplierPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  return (
    <AppShell>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Add New Supplier</h1>
          <p className="text-muted-foreground">
            Create a new supplier record with their contact information and details.
          </p>
        </div>

        <div className="max-w-2xl">
          <AddSupplierForm />
        </div>
      </div>
    </AppShell>
  )
}
