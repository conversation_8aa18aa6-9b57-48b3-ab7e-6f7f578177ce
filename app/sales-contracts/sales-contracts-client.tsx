"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Eye, FilePen, Plus, Trash2, Search } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useI18n } from "@/components/i18n-provider";

// Type definitions matching the schema
type SalesContract = {
  id: string;
  number: string;
  customer_id: string;
  template_id?: string;
  date: string;
  currency?: string;
  status: string;
  created_at: Date | null;
  customer: {
    id: string;
    name: string;
    contact_name: string | null;
    contact_phone: string | null;
    contact_email: string | null;
    address: string | null;
    tax_id: string | null;
    bank: string | null;
    incoterm: string | null;
    payment_term: string | null;
    status: string | null;
    created_at: Date | null;
  };
  items: {
    id: string;
    contract_id: string;
    product_id: string;
    qty: string;
    price: string;
    product: {
      id: string;
      sku: string;
      name: string;
      unit: string;
      hs_code: string | null;
      origin: string | null;
      package: string | null;
      image: string | null;
      created_at: Date | null;
    };
  }[];
};

// Type definitions are inferred from the SalesContract type above

// Forms are handled in dedicated pages:
// - Add: /sales-contracts/add
// - Edit: /sales-contracts/edit/[id]

export function SalesContractsClientPage({
  initialContracts,
}: {
  initialContracts: SalesContract[];
}) {
  const router = useRouter();
  const { t } = useI18n();

  const [contractToDelete, setContractToDelete] = useState<SalesContract | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const handleViewDocument = (contract: SalesContract) => {
    // Navigate to the dedicated contract view page
    router.push(`/sales-contracts/view/${contract.id}`);
  };

  const handleDelete = async () => {
    if (!contractToDelete) return;
    const response = await fetch(`/api/contracts/sales/${contractToDelete.id}`, { method: "DELETE" });
    if (response.ok) {
      toast.success(t("sales_contracts.success.deleted"));
      setContractToDelete(null);
      router.refresh();
    } else {
      toast.error(t("sales_contracts.error.delete"));
    }
  };

  // Filter contracts based on search term
  const filteredContracts = initialContracts.filter(contract => {
    const search = searchTerm.toLowerCase();
    const contractNumber = contract.number.toLowerCase();
    const customerName = contract.customer.name.toLowerCase();
    const status = contract.status.toLowerCase();
    const currency = contract.currency?.toLowerCase() || "";

    return contractNumber.includes(search) ||
      customerName.includes(search) ||
      status.includes(search) ||
      currency.includes(search);
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("sales_contracts.title")}</h1>
          <p className="text-muted-foreground">{t("sales_contracts.subtitle")}</p>
        </div>
        <Link href="/sales-contracts/add">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            {t("sales_contracts.add")}
          </Button>
        </Link>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t("sales_contracts.search_placeholder")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("sales_contracts.table.contract_number")}</TableHead>
              <TableHead>{t("sales_contracts.table.customer")}</TableHead>
              <TableHead>{t("sales_contracts.table.date")}</TableHead>
              <TableHead>{t("sales_contracts.table.currency")}</TableHead>
              <TableHead>{t("sales_contracts.table.items")}</TableHead>
              <TableHead>{t("sales_contracts.table.status")}</TableHead>
              <TableHead className="text-right">{t("sales_contracts.table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredContracts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchTerm ? (
                      <>No contracts found matching "{searchTerm}"</>
                    ) : (
                      <>{t("sales_contracts.empty")}</>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredContracts.map((contract) => (
                <TableRow key={contract.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">{contract.number}</TableCell>
                  <TableCell>{contract.customer.name}</TableCell>
                  <TableCell>{new Date(contract.date).toLocaleDateString()}</TableCell>
                  <TableCell>{contract.currency || "-"}</TableCell>
                  <TableCell>
                    <div className="max-w-[200px]">
                      <span className="text-sm text-muted-foreground">
                        {contract.items.length} {contract.items.length === 1 ? "item" : "items"}
                      </span>
                      <div className="text-xs text-muted-foreground truncate" title={contract.items.map(item => `${item.product.name} (${item.qty} ${item.product.unit})`).join(", ")}>
                        {contract.items.slice(0, 2).map(item => item.product.name).join(", ")}
                        {contract.items.length > 2 && ` +${contract.items.length - 2} more`}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={
                      contract.status === "approved" ? "default" :
                        contract.status === "draft" ? "secondary" :
                          contract.status === "pending" ? "destructive" :
                            "outline"
                    } className={
                      contract.status === "approved" ? "bg-green-600 hover:bg-green-700 text-white" :
                        contract.status === "draft" ? "bg-gray-600 hover:bg-gray-700 text-white" :
                          contract.status === "pending" ? "bg-yellow-600 hover:bg-yellow-700 text-white" :
                            "bg-slate-600 hover:bg-slate-700 text-white"
                    }>
                      {contract.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex gap-2 justify-end">
                      {contract.template_id && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleViewDocument(contract)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          {t("common.view")}
                        </Button>
                      )}
                      <Link href={`/sales-contracts/edit/${contract.id}`}>
                        <Button
                          size="sm"
                          variant="outline"
                        >
                          <FilePen className="h-4 w-4 mr-1" />
                          {t("common.edit")}
                        </Button>
                      </Link>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => setContractToDelete(contract)}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        {t("common.delete")}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>



      {/* Delete Alert Dialog */}
      <AlertDialog open={!!contractToDelete} onOpenChange={(open) => !open && setContractToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("sales_contracts.delete.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("sales_contracts.delete.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>{t("common.delete")}</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
