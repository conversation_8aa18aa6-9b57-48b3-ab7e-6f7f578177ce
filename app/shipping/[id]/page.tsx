/**
 * Manufacturing ERP - Shipment Detail Page
 * Professional shipment view with tracking and status management
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { shipments } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { ShipmentDetailView } from "@/components/shipping/shipment-detail-view"
import { FinancialImpactCard } from "@/components/operational/financial-impact-card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Edit, Truck, Ship, Plane, Package } from "lucide-react"
import Link from "next/link"

interface ShipmentDetailPageProps {
  params: Promise<{ id: string }>
}

async function getShipment(shipmentId: string, companyId: string) {
  const shipment = await db.query.shipments.findFirst({
    where: and(
      eq(shipments.id, shipmentId),
      eq(shipments.company_id, companyId)
    ),
    with: {
      customer: {
        columns: {
          id: true,
          name: true,
          contact_name: true,
          contact_email: true,
          contact_phone: true,
          address: true
        }
      },
      salesContract: {
        columns: {
          id: true,
          number: true,
          status: true
        }
      },
      items: {
        with: {
          product: {
            columns: {
              id: true,
              name: true,
              sku: true,
              unit: true,
              image: true
            }
          }
        }
      }
    }
  })

  return shipment
}

export default async function ShipmentDetailPage({ params }: ShipmentDetailPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params
  const shipment = await getShipment(id, context.companyId)

  if (!shipment) {
    notFound()
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      preparing: { variant: "secondary" as const, color: "text-yellow-600", label: "Preparing" },
      ready: { variant: "secondary" as const, color: "text-blue-600", label: "Ready" },
      shipped: { variant: "default" as const, color: "text-purple-600", label: "Shipped" },
      in_transit: { variant: "default" as const, color: "text-indigo-600", label: "In Transit" },
      out_for_delivery: { variant: "default" as const, color: "text-orange-600", label: "Out for Delivery" },
      delivered: { variant: "default" as const, color: "text-green-600", label: "Delivered" },
      cancelled: { variant: "destructive" as const, color: "text-red-600", label: "Cancelled" },
      exception: { variant: "destructive" as const, color: "text-red-600", label: "Exception" }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.preparing

    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    )
  }

  const getShippingMethodIcon = (method: string) => {
    switch (method) {
      case 'air_freight':
        return <Plane className="h-5 w-5 text-blue-600" />
      case 'sea_freight':
        return <Ship className="h-5 w-5 text-blue-600" />
      case 'express':
        return <Truck className="h-5 w-5 text-green-600" />
      case 'truck':
        return <Truck className="h-5 w-5 text-gray-600" />
      default:
        return <Package className="h-5 w-5 text-gray-600" />
    }
  }

  const getShippingMethodLabel = (method: string) => {
    const labels = {
      air_freight: "Air Freight",
      sea_freight: "Sea Freight",
      express: "Express",
      truck: "Truck"
    }
    return labels[method as keyof typeof labels] || method
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/shipping">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Shipping
              </Link>
            </Button>

            <div>
              <div className="flex items-center gap-3">
                {getShippingMethodIcon(shipment.shipping_method)}
                <h1 className="text-2xl font-bold tracking-tight">
                  {shipment.shipment_number}
                </h1>
                {getStatusBadge(shipment.status)}
              </div>
              <p className="text-muted-foreground">
                {getShippingMethodLabel(shipment.shipping_method)} shipment to {shipment.customer.name}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href={`/shipping/${shipment.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Shipment
              </Link>
            </Button>
          </div>
        </div>

        {/* ✅ NEW: Financial Impact Panel - Zero Breaking Changes */}
        <FinancialImpactCard
          title="Shipment Financial Impact"
          type="shipment"
          entityId={shipment.id}
          className="mb-6"
          collapsible={true}
        />

        {/* Shipment Details */}
        <ShipmentDetailView shipment={shipment} />
      </div>
    </AppShell>
  )
}
