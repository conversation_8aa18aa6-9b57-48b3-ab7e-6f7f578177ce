/**
 * Manufacturing ERP - Shipping Management Page
 * Professional shipping module with complete workflow integration
 */

import { Suspense } from "react"
import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { db } from "@/lib/db"
import { shipments } from "@/lib/schema-postgres"
import { eq, desc, and, or, ilike } from "drizzle-orm"
import { ShipmentsTable } from "@/components/shipping/shipments-table"
import { ShippingStats } from "@/components/shipping/shipping-stats"
import { ShippingFilters } from "@/components/shipping/shipping-filters"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Search, Filter, Ship, Package, Truck, Plane } from "lucide-react"
import Link from "next/link"

interface ShippingPageProps {
  searchParams: Promise<{
    search?: string
    status?: string
    customer_id?: string
    page?: string
  }>
}

async function getShipments(companyId: string, searchParams: any) {
  const search = searchParams.search || ""
  const status = searchParams.status || ""
  const customer_id = searchParams.customer_id || ""
  const page = parseInt(searchParams.page || "1")
  const limit = 50
  const offset = (page - 1) * limit

  // Build where conditions
  let whereConditions = [eq(shipments.company_id, companyId)]

  if (search) {
    whereConditions.push(
      or(
        ilike(shipments.shipment_number, `%${search}%`),
        ilike(shipments.tracking_number, `%${search}%`),
        ilike(shipments.notes, `%${search}%`)
      )!
    )
  }

  if (status) {
    whereConditions.push(eq(shipments.status, status))
  }

  if (customer_id) {
    whereConditions.push(eq(shipments.customer_id, customer_id))
  }

  // Get shipments with relationships
  const shipmentsList = await db.query.shipments.findMany({
    where: and(...whereConditions),
    with: {
      customer: {
        columns: {
          id: true,
          name: true,
          contact_name: true,
          contact_email: true,
          contact_phone: true
        }
      },
      salesContract: {
        columns: {
          id: true,
          number: true,
          status: true
        }
      },
      items: {
        with: {
          product: {
            columns: {
              id: true,
              name: true,
              sku: true,
              unit: true
            }
          }
        }
      }
    },
    orderBy: [desc(shipments.created_at)],
    limit,
    offset
  })

  return shipmentsList
}

async function getShippingStats(companyId: string) {
  // Get shipment counts by status
  const allShipments = await db.query.shipments.findMany({
    where: eq(shipments.company_id, companyId),
    columns: {
      status: true
    }
  })

  const stats = {
    total: allShipments.length,
    preparing: allShipments.filter(s => s.status === 'preparing').length,
    ready: allShipments.filter(s => s.status === 'ready').length,
    shipped: allShipments.filter(s => s.status === 'shipped').length,
    in_transit: allShipments.filter(s => s.status === 'in_transit').length,
    delivered: allShipments.filter(s => s.status === 'delivered').length,
    cancelled: allShipments.filter(s => s.status === 'cancelled').length,
    exception: allShipments.filter(s => s.status === 'exception').length
  }

  return stats
}

export default async function ShippingPage({ searchParams }: ShippingPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const resolvedSearchParams = await searchParams
  const [shipmentsList, stats] = await Promise.all([
    getShipments(context.companyId, resolvedSearchParams),
    getShippingStats(context.companyId)
  ])

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Ship className="h-8 w-8 text-blue-600" />
              Shipping Management
            </h1>
            <p className="text-muted-foreground">
              Manage shipments, track deliveries, and coordinate logistics
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button asChild>
              <Link href="/shipping/create">
                <Plus className="mr-2 h-4 w-4" />
                New Shipment
              </Link>
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <ShippingFilters />

        {/* Stats Cards */}
        <Suspense fallback={<div>Loading stats...</div>}>
          <ShippingStats stats={stats} />
        </Suspense>

        {/* Shipments Table */}
        <div className="rounded-md border">
          <Suspense fallback={<div>Loading shipments...</div>}>
            <ShipmentsTable shipments={shipmentsList} />
          </Suspense>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Plane className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold">Air Freight</h3>
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              Fast delivery for urgent shipments
            </p>
            <Button variant="outline" size="sm" asChild>
              <Link href="/shipping/create?method=air_freight">
                Create Air Shipment
              </Link>
            </Button>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Ship className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold">Sea Freight</h3>
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              Cost-effective for large shipments
            </p>
            <Button variant="outline" size="sm" asChild>
              <Link href="/shipping/create?method=sea_freight">
                Create Sea Shipment
              </Link>
            </Button>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Truck className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibant">Express Delivery</h3>
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              Same-day and next-day delivery
            </p>
            <Button variant="outline" size="sm" asChild>
              <Link href="/shipping/create?method=express">
                Create Express Shipment
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </AppShell>
  )
}
