"use client"

import { useEffect, useState } from "react"
import { AppShell } from "@/components/app-shell"

export default function TestRawMaterialsAPI() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('🔍 Test Page: Fetching raw materials...')
        const response = await fetch('/api/raw-materials')
        
        console.log('📡 Test Page: Response status:', response.status)
        console.log('📡 Test Page: Response ok:', response.ok)
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('📊 Test Page: API Response:', result)
        
        setData(result)
      } catch (err) {
        console.error('❌ Test Page: Error:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <AppShell>
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-4">Testing Raw Materials API</h1>
          <p>Loading...</p>
        </div>
      </AppShell>
    )
  }

  if (error) {
    return (
      <AppShell>
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-4">Testing Raw Materials API</h1>
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong>Error:</strong> {error}
          </div>
        </div>
      </AppShell>
    )
  }

  // Calculate totals like the KPI component
  const materials = data?.materials || []
  let totalUnits = 0
  let totalValue = 0
  let availableLots = 0
  const allLocations = new Set<string>()

  materials.forEach((material: any) => {
    const lots = material.lots || []
    lots.forEach((lot: any) => {
      if (lot.status === 'available') {
        totalUnits += parseFloat(lot.qty || '0')
        totalValue += parseFloat(lot.total_cost || '0')
        availableLots++
        if (lot.location) allLocations.add(lot.location)
      }
    })
  })

  return (
    <AppShell>
      <div className="p-6 space-y-6">
        <h1 className="text-2xl font-bold">Testing Raw Materials API</h1>
        
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <strong>Success!</strong> API call completed successfully.
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border">
            <h3 className="font-semibold">Total Materials</h3>
            <p className="text-2xl font-bold">{materials.length}</p>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <h3 className="font-semibold">Total Units</h3>
            <p className="text-2xl font-bold">{totalUnits.toFixed(2)}</p>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <h3 className="font-semibold">Total Value</h3>
            <p className="text-2xl font-bold">${totalValue.toFixed(2)}</p>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <h3 className="font-semibold">Available Lots</h3>
            <p className="text-2xl font-bold">{availableLots}</p>
          </div>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Raw API Response:</h3>
          <pre className="text-sm overflow-auto max-h-96">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>

        <div className="bg-blue-100 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Calculated Values:</h3>
          <ul className="space-y-1">
            <li><strong>Materials:</strong> {materials.length}</li>
            <li><strong>Total Units:</strong> {totalUnits.toFixed(2)}</li>
            <li><strong>Total Value:</strong> ${totalValue.toFixed(2)}</li>
            <li><strong>Available Lots:</strong> {availableLots}</li>
            <li><strong>Locations:</strong> {Array.from(allLocations).join(', ')}</li>
          </ul>
        </div>
      </div>
    </AppShell>
  )
}
