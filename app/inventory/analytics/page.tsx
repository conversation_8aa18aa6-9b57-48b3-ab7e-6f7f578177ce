import { AppShell } from "@/components/app-shell"
import { CrossCategoryAnalytics } from "@/components/inventory/cross-category-analytics"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"

export default async function InventoryAnalyticsPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  return (
    <AppShell>
      <div className="space-y-6">
        <CrossCategoryAnalytics />
      </div>
    </AppShell>
  )
}
