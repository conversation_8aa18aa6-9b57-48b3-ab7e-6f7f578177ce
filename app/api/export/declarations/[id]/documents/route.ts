import { json<PERSON>rror, json<PERSON>k } from "@/lib/api-helpers"
import { db, uid } from "@/lib/db"
import { documents, declarations } from "@/lib/schema-postgres"
import { and, eq } from "drizzle-orm"
import { z } from "zod"
import { withTenantAuth } from "@/lib/tenant-utils"
import { writeFile, mkdir } from "fs/promises"
import { join } from "path"
import { createClient } from "@supabase/supabase-js"

// ✅ DUAL STORAGE: Environment-aware storage configuration
const isProduction = process.env.NODE_ENV === 'production'

function getSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseKey) {
    console.warn('⚠️ Supabase credentials not found, using local storage')
    return null
  }

  return createClient(supabaseUrl, supabaseKey)
}

// ✅ PROFESSIONAL ERP: File upload endpoint following working patterns
export const POST = withTenantAuth(async function POST(
  req: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: declarationId } = await params

    // ✅ SECURITY: Check if declaration exists and belongs to company
    const existing = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, declarationId),
        eq(declarations.company_id, context.companyId)
      )
    })

    if (!existing) {
      return jsonError("Declaration not found", 404)
    }

    // ✅ HANDLE BOTH FORMDATA AND DIRECT FILE UPLOAD
    let file: File
    let filename: string

    const contentType = req.headers.get('content-type')

    if (contentType?.includes('multipart/form-data')) {
      // FormData approach (future enhancement)
      const formData = await req.formData()
      const uploadedFile = formData.get('file') as File
      if (!uploadedFile) {
        return jsonError("No file provided", 400)
      }
      file = uploadedFile
      filename = file.name
    } else {
      // Direct file upload (current approach)
      if (!req.body) {
        return jsonError("No file in request", 400)
      }
      filename = req.headers.get("x-vercel-filename") || "document.bin"

      // Convert request body to File-like object
      const buffer = await req.arrayBuffer()
      file = new File([buffer], filename, {
        type: req.headers.get('content-type') || 'application/octet-stream'
      })
    }

    // ✅ SECURITY: File size validation (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      return jsonError(`File ${filename} exceeds 10MB limit`, 400)
    }

    // ✅ SECURITY: Sanitize filename
    const sanitizedName = filename.replace(/[^a-zA-Z0-9.-]/g, '_')
    const timestamp = Date.now()
    const finalFilename = `${timestamp}_${sanitizedName}`

    let fileUrl: string

    const supabase = getSupabaseClient()
    if (isProduction && supabase) {
      // ✅ PRODUCTION: Use Supabase Storage
      const bucketPath = `export/${context.companyId}/${declarationId}/documents/${finalFilename}`

      const bytes = await file.arrayBuffer()
      const { error } = await supabase.storage
        .from('attachments')
        .upload(bucketPath, bytes, {
          contentType: file.type,
          upsert: false
        })

      if (error) {
        console.error(`Failed to upload ${finalFilename} to Supabase:`, error)
        return jsonError(`Failed to upload ${finalFilename}`, 500)
      }

      // Get public URL
      const { data } = supabase.storage
        .from('attachments')
        .getPublicUrl(bucketPath)

      fileUrl = data.publicUrl
    } else {
      // ✅ DEVELOPMENT: Use local file system
      const uploadsDir = join(process.cwd(), 'uploads', 'export', context.companyId, declarationId, 'documents')
      await mkdir(uploadsDir, { recursive: true })

      const filePath = join(uploadsDir, finalFilename)
      const bytes = await file.arrayBuffer()
      await writeFile(filePath, Buffer.from(bytes))

      fileUrl = `/api/export/declarations/${declarationId}/files/${finalFilename}`
    }

    // ✅ DATABASE: Store document record
    const docId = uid("doc")
    await db.insert(documents).values({
      id: docId,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      filename: finalFilename,
      url: fileUrl,
      filetype: file.type,
      declaration_id: declarationId,
    })

    // ✅ SECURE: Only return document if it belongs to current company
    const newDoc = await db.query.documents.findFirst({
      where: and(
        eq(documents.id, docId),
        eq(documents.company_id, context.companyId)
      ),
    })

    // ✅ AUDIT TRAIL: Log file upload
    console.log(`📎 Export Declaration Document Uploaded:`, {
      declarationId,
      companyId: context.companyId,
      userId: context.userId,
      filename: finalFilename,
      fileSize: file.size,
      timestamp: new Date().toISOString(),
    })

    return jsonOk(newDoc, { status: 201 })
  } catch (error) {
    console.error("Document upload error:", error)
    return jsonError(error instanceof Error ? error.message : "Upload failed", 500)
  }
})

const deleteSchema = z.object({
  documentId: z.string(),
  url: z.string(),
})

// ✅ PROFESSIONAL ERP: File removal endpoint following working patterns
export const DELETE = withTenantAuth(async function DELETE(
  req: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: declarationId } = await params
    const body = await req.json()
    const data = deleteSchema.parse(body)

    // ✅ SECURITY: Check if declaration exists and belongs to company
    const declaration = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, declarationId),
        eq(declarations.company_id, context.companyId)
      )
    })

    if (!declaration) {
      return jsonError("Declaration not found", 404)
    }

    // ✅ SECURITY: Only delete document if it belongs to current company
    const document = await db.query.documents.findFirst({
      where: and(
        eq(documents.id, data.documentId),
        eq(documents.company_id, context.companyId),
        eq(documents.declaration_id, declarationId)
      ),
    })

    if (!document) {
      return jsonError("Document not found", 404)
    }

    // ✅ DUAL STORAGE: Delete from appropriate storage
    const supabase = getSupabaseClient()
    if (isProduction && supabase) {
      // ✅ PRODUCTION: Delete from Supabase Storage
      const bucketPath = `export/${context.companyId}/${declarationId}/documents/${document.filename}`
      const { error } = await supabase.storage
        .from('attachments')
        .remove([bucketPath])

      if (error) {
        console.error(`Failed to delete ${document.filename} from Supabase:`, error)
        // Continue with database deletion even if storage deletion fails
      }
    } else {
      // ✅ DEVELOPMENT: Delete from local file system
      try {
        const { unlink } = await import('fs/promises')
        const filePath = join(process.cwd(), 'uploads', 'export', context.companyId, declarationId, 'documents', document.filename)
        await unlink(filePath)
      } catch (error) {
        console.error(`Failed to delete local file ${document.filename}:`, error)
        // Continue with database deletion even if file deletion fails
      }
    }

    // ✅ DATABASE: Delete document record
    await db.delete(documents).where(
      and(
        eq(documents.id, data.documentId),
        eq(documents.company_id, context.companyId),
        eq(documents.declaration_id, declarationId)
      )
    )

    // ✅ AUDIT TRAIL: Log file deletion
    console.log(`🗑️ Export Declaration Document Deleted:`, {
      declarationId,
      companyId: context.companyId,
      userId: context.userId,
      filename: document.filename,
      timestamp: new Date().toISOString(),
    })

    return new Response(null, { status: 204 })
  } catch (error) {
    console.error("Document deletion error:", error)
    return jsonError(error instanceof Error ? error.message : "Deletion failed", 500)
  }
})
