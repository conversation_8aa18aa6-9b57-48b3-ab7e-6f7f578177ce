import { db } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { declarations, declarationItems, documents } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"
import { withTenantAuth } from "@/lib/tenant-utils"

// ✅ PROFESSIONAL ERP: Secure GET endpoint for individual declaration
export const GET = withTenantAuth(async function GET(
  _: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // ✅ SECURITY: Fetch declaration with company isolation
    const declaration = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, id),
        eq(declarations.company_id, context.companyId)
      ),
      with: {
        items: {
          with: {
            product: true,
          },
        },
      },
    })

    if (!declaration) {
      return jsonError("Declaration not found", 404)
    }

    return jsonOk(declaration)
  } catch (e) {
    return jsonError(e)
  }
})

const patchSchema = z.object({
  status: z.string().min(1).optional(),
  number: z.string().min(1).optional(),
})

// ✅ PROFESSIONAL ERP: Secure PATCH endpoint with tenant isolation
export const PATCH = withTenantAuth(async function PATCH(
  req: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await req.json()
    const data = patchSchema.parse(body)

    // ✅ SECURITY: Verify declaration belongs to current company
    const existing = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, id),
        eq(declarations.company_id, context.companyId)
      )
    })

    if (!existing) {
      return jsonError("Declaration not found", 404)
    }

    // ✅ PROFESSIONAL: Update only provided fields
    const updateData: any = {}
    if (data.status) updateData.status = data.status
    if (data.number) updateData.number = data.number

    const updated = await db
      .update(declarations)
      .set(updateData)
      .where(and(
        eq(declarations.id, id),
        eq(declarations.company_id, context.companyId)
      ))
      .returning()

    return jsonOk(updated[0])
  } catch (e) {
    return jsonError(e)
  }
})

export const DELETE = withTenantAuth(async function DELETE(
  _: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 🛡️ SECURITY: Verify declaration belongs to current company
    const declaration = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, id),
        eq(declarations.company_id, context.companyId)
      )
    })

    if (!declaration) {
      return jsonError("Declaration not found", 404)
    }

    // ✅ PROPER CASCADE DELETE: Delete in correct order to avoid foreign key violations
    // 1. Delete documents first
    await db.delete(documents).where(eq(documents.declaration_id, id))
    // 2. Delete declaration items
    await db.delete(declarationItems).where(eq(declarationItems.declaration_id, id))
    // 3. Finally delete the declaration
    await db.delete(declarations).where(
      and(
        eq(declarations.id, id),
        eq(declarations.company_id, context.companyId)
      )
    )

    return new Response(null, { status: 204 })
  } catch (e) {
    console.error("Delete declaration error:", e)
    return jsonError(e instanceof Error ? e.message : "Delete failed", 500)
  }
})
