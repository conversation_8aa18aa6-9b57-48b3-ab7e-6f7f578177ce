import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse, jsonError } from "@/lib/api-helpers"
import { workOrders, workOperations, qualityInspections, stockLots, stockTxns, materialConsumption } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"
import { qualityWorkflowService } from "@/lib/services/quality-workflow"

import { MaterialConsumptionService } from "@/lib/services/material-consumption-service"
import { withTenantAuth } from "@/lib/tenant-utils"
import {
  createReferenceNotFoundError,
  createInvalidWorkflowTransitionError
} from "@/lib/errors"
import { logWorkflowStep, logWorkflowError, withWorkflowTiming } from "@/lib/logging/workflow-logger"

const patchSchema = z.object({
  workOperationId: z.string().optional(),
  status: z.string().optional(),
  action: z.enum(["update_operation", "start_production", "complete_production"]).optional(),
  // ✅ PROFESSIONAL ERP: Inline editing fields
  qty: z.string().optional(),
  due_date: z.string().nullable().optional(),
  priority: z.enum(["low", "normal", "high", "urgent"]).optional(),
  notes: z.string().nullable().optional(),
})

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId) // 🛡️ CRITICAL: Multi-tenant isolation
      ),
      with: {
        salesContract: {
          with: {
            customer: true,
          },
        },
        product: true,
        operations: true,
        // TODO: Add quality inspections when schema relationships are fixed
        // qualityInspections: true,
      },
    })

    if (!workOrder) {
      throw createReferenceNotFoundError("Work Order", id)
    }

    return createSuccessResponse(workOrder)
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant PATCH endpoint with proper isolation
export const PATCH = withTenantAuth(async function PATCH(req: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await req.json()
    const data = patchSchema.parse(body)

    // 🛡️ CRITICAL: Verify work order belongs to current company
    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId)
      ),
    })

    if (!workOrder) {
      throw createReferenceNotFoundError("Work Order", id)
    }

    // Handle different types of updates
    if (data.action === "update_operation" && data.workOperationId) {
      // Update work operation status
      const updated = await db
        .update(workOperations)
        .set({ status: data.status })
        .where(eq(workOperations.id, data.workOperationId))
        .returning()

      return createSuccessResponse(updated[0], "Operation status updated")

    } else if (data.action === "start_production") {
      // Check quality gate before starting production
      const canStart = await qualityWorkflowService.validateQualityGate(id, "start_production")

      if (!canStart) {
        throw createInvalidWorkflowTransitionError(
          "Cannot start production: Quality approval required"
        )
      }

      const updated = await db
        .update(workOrders)
        .set({ status: "in_progress" })
        .where(eq(workOrders.id, id))
        .returning()

      return createSuccessResponse(updated[0], "Production started")

    } else if (data.action === "complete_production") {
      // Check quality gate before completing production
      const canComplete = await qualityWorkflowService.validateQualityGate(id, "complete_production")

      if (!canComplete) {
        throw createInvalidWorkflowTransitionError(
          "Cannot complete production: Quality approval required"
        )
      }

      // ✅ PROFESSIONAL ERP: Get work order details for material consumption
      const workOrderDetails = await db.query.workOrders.findFirst({
        where: and(
          eq(workOrders.id, id),
          eq(workOrders.company_id, context.companyId)
        ),
        with: {
          product: true
        }
      })

      if (!workOrderDetails) {
        throw createReferenceNotFoundError("Work Order", id)
      }

      const completedQty = parseFloat(workOrderDetails.qty)

      // ✅ MATERIAL CONSUMPTION: Process material consumption before completing work order
      console.log(`🏭 API: Starting material consumption for work order ${id}`)
      let materialConsumptionResult = null

      try {
        const materialConsumptionService = new MaterialConsumptionService({
          companyId: context.companyId,
          userId: context.userId,
          workOrderId: id,
          completedQty: completedQty
        })

        materialConsumptionResult = await materialConsumptionService.processWorkOrderMaterialConsumption()

        if (!materialConsumptionResult.success) {
          // Material shortages prevent completion
          const shortageMessages = materialConsumptionResult.shortages.map(s =>
            `${s.materialName}: Need ${s.shortageQty} ${s.requiredQty > 0 ? 'more' : ''}`
          ).join(', ')

          throw createInvalidWorkflowTransitionError(
            `Cannot complete production: Material shortages - ${shortageMessages}`
          )
        }

        console.log(`✅ API: Material consumption completed. Cost: $${materialConsumptionResult.totalMaterialCost}`)
      } catch (materialError) {
        console.error(`❌ API: Material consumption failed for work order ${id}:`, materialError)

        // If it's a workflow error (shortages), re-throw it
        if (materialError instanceof Error && materialError.message.includes("Cannot complete production")) {
          throw materialError
        }

        // For other errors, log but continue (manual intervention may be needed)
        console.error(`⚠️ Material consumption failed but continuing with work order completion`)
      }

      // Update work order status to completed
      const updated = await db
        .update(workOrders)
        .set({ status: "completed" })
        .where(eq(workOrders.id, id))
        .returning()

      // ✅ SIMPLE INVENTORY INTEGRATION: Create stock lot when work order completed
      console.log(`📦 Creating inventory stock lot for completed work order ${id}`)
      try {
        const { uid } = require("uid")

        // Create stock lot for finished goods
        const stockLotId = uid()
        const lotNumber = `${workOrderDetails.product?.sku || 'PROD'}-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${stockLotId.slice(-4).toUpperCase()}`

        await db.insert(stockLots).values({
          id: stockLotId,
          company_id: context.companyId,
          product_id: workOrderDetails.product_id,
          lot_number: lotNumber,
          qty: workOrderDetails.qty,
          location: "finished_goods",
          quality_status: "approved", // Auto-approve for completed work orders
          work_order_id: id, // Link to work order
        })

        console.log(`✅ Stock lot created: ${lotNumber} (${workOrderDetails.qty} units)`)
      } catch (inventoryError) {
        console.error(`⚠️ Inventory creation failed for work order ${id}:`, inventoryError)
        // Don't fail the work order completion, just log the error
      }



      // Prepare success response with material consumption details
      const responseMessage = materialConsumptionResult?.success
        ? `Production completed. Materials consumed: $${materialConsumptionResult.totalMaterialCost}`
        : "Production completed"

      return createSuccessResponse({
        ...updated[0],
        materialConsumption: materialConsumptionResult
      }, responseMessage)

    } else {
      // ✅ PROFESSIONAL ERP: Direct field updates for inline editing
      if (!data.action) {
        // Prepare update object with only provided fields
        const updateData: any = {}
        if (data.status !== undefined) updateData.status = data.status
        if (data.qty !== undefined) updateData.qty = data.qty
        if (data.due_date !== undefined) updateData.due_date = data.due_date
        if (data.priority !== undefined) updateData.priority = data.priority
        if (data.notes !== undefined) updateData.notes = data.notes

        // Only update if there are fields to update
        if (Object.keys(updateData).length === 0) {
          throw createReferenceNotFoundError("Update Data", "No fields provided")
        }

        // ✅ PROFESSIONAL ERP: Simple work order update (no contract sync)
        const updated = await db
          .update(workOrders)
          .set(updateData)
          .where(and(
            eq(workOrders.id, id),
            eq(workOrders.company_id, context.companyId)
          ))
          .returning()

        if (updated.length === 0) {
          throw createReferenceNotFoundError("Work Order", id)
        }

        return createSuccessResponse(updated[0], "Work order updated successfully")
      }

      // Legacy support: update work operation if workOperationId provided
      if (data.workOperationId) {
        const updated = await db
          .update(workOperations)
          .set({ status: data.status })
          .where(eq(workOperations.id, data.workOperationId))
          .returning()

        return createSuccessResponse(updated[0], "Operation status updated")
      } else {
        throw createReferenceNotFoundError("Work Operation or Action", "N/A")
      }
    }
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant DELETE endpoint with proper isolation
export const DELETE = withTenantAuth(async function DELETE(request: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    // 🛡️ CRITICAL: Verify work order belongs to current company before deletion
    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId)
      ),
    })

    if (!workOrder) {
      return jsonError("Work order not found", 404)
    }

    // 🗑️ CASCADE DELETE: Delete related records in correct order (respecting foreign key constraints)

    // 1. Delete stock transactions first (they may reference stock lots)
    try {
      const stockLotsToDelete = await db.query.stockLots.findMany({
        where: and(
          eq(stockLots.work_order_id, id),
          eq(stockLots.company_id, context.companyId)
        )
      })

      for (const stockLot of stockLotsToDelete) {
        // Delete stock transactions for this lot
        await db.delete(stockTxns).where(
          and(
            eq(stockTxns.stock_lot_id, stockLot.id),
            eq(stockTxns.company_id, context.companyId)
          )
        )
      }
    } catch (error) {
      console.log("Stock transactions delete attempt completed:", error)
    }

    // 2. Delete stock lots (they reference work_order_id)
    try {
      await db.delete(stockLots).where(
        and(
          eq(stockLots.work_order_id, id),
          eq(stockLots.company_id, context.companyId)
        )
      )
    } catch (error) {
      console.log("Stock lots delete attempt completed:", error)
    }

    // 3. Delete material consumption records (they reference work_order_id)
    try {
      await db.delete(materialConsumption).where(
        and(
          eq(materialConsumption.work_order_id, id),
          eq(materialConsumption.company_id, context.companyId)
        )
      )
    } catch (error) {
      console.log("Material consumption delete attempt completed:", error)
    }

    // 4. Delete quality inspections (they reference work_order_id)
    try {
      await db.delete(qualityInspections).where(
        and(
          eq(qualityInspections.work_order_id, id),
          eq(qualityInspections.company_id, context.companyId)
        )
      )
    } catch (error) {
      console.log("Quality inspections delete attempt completed:", error)
    }

    // 5. Delete work operations (they reference work_order_id)
    await db.delete(workOperations).where(
      and(
        eq(workOperations.work_order_id, id),
        eq(workOperations.company_id, context.companyId)
      )
    )

    // 6. Finally, delete the work order itself
    await db.delete(workOrders).where(
      and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId)
      )
    )

    return new Response(null, { status: 204 })
  } catch (error) {
    console.error("Error deleting work order:", error)
    return jsonError("Failed to delete work order", 500)
  }
})
