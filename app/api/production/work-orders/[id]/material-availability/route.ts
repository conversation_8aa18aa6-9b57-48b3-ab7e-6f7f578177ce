/**
 * Manufacturing ERP - Work Order Material Availability Check
 * Pre-production validation to ensure materials are available
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { workOrders } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { MaterialConsumptionService } from "@/lib/services/material-consumption-service"

// ✅ GET - Check material availability for work order
export const GET = withTenantAuth(async function GET(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: workOrderId } = await params

    // Get work order details
    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, workOrderId),
        eq(workOrders.company_id, context.companyId)
      ),
      with: {
        product: true,
        salesContract: {
          with: {
            customer: true,
          },
        },
      },
    })

    if (!workOrder) {
      return jsonError("Work order not found", 404)
    }

    const plannedQty = parseFloat(workOrder.qty)

    // Check material availability
    const availabilityCheck = await MaterialConsumptionService.checkMaterialAvailability(
      context.companyId,
      workOrder.product_id,
      plannedQty
    )

    // ✅ ENHANCED: Calculate shortage value using enhanced pricing system
    const totalShortageValue = await Promise.all(
      availabilityCheck.shortages.map(async (shortage) => {
        // Get product pricing information
        const product = await db.query.products.findFirst({
          where: and(
            eq(products.id, shortage.materialId),
            eq(products.company_id, context.companyId)
          ),
          columns: {
            cost_price: true,
            base_price: true,
            price: true,
          }
        })

        if (!product) return shortage.shortageQty * 10 // Fallback

        // ✅ PRICING HIERARCHY: cost_price → base_price → legacy price → fallback
        let unitCost = 10 // Fallback cost
        if (product.cost_price && parseFloat(product.cost_price) > 0) {
          unitCost = parseFloat(product.cost_price)
        } else if (product.base_price && parseFloat(product.base_price) > 0) {
          unitCost = parseFloat(product.base_price)
        } else if (product.price && parseFloat(product.price) > 0) {
          unitCost = parseFloat(product.price)
        }

        return shortage.shortageQty * unitCost
      })
    ).then(values => values.reduce((sum, value) => sum + value, 0))

    const criticalShortages = availabilityCheck.shortages.filter(s => s.shortageQty > 0)
    const canStartProduction = availabilityCheck.available && criticalShortages.length === 0

    return jsonOk({
      workOrder: {
        id: workOrder.id,
        number: workOrder.number,
        productName: workOrder.product?.name,
        plannedQty,
        status: workOrder.status,
      },
      materialAvailability: {
        available: availabilityCheck.available,
        canStartProduction,
        shortages: availabilityCheck.shortages,
        summary: {
          totalShortages: availabilityCheck.shortages.length,
          criticalShortages: criticalShortages.length,
          totalShortageValue,
        },
      },
      recommendations: generateRecommendations(availabilityCheck.shortages, workOrder.status),
    })
  } catch (error) {
    console.error("Material Availability Check Error:", error)
    return jsonError("Failed to check material availability", 500)
  }
})

/**
 * Generate actionable recommendations based on material availability
 */
function generateRecommendations(
  shortages: Array<{
    materialId: string
    materialName: string
    requiredQty: number
    availableQty: number
    shortageQty: number
  }>,
  workOrderStatus: string
) {
  const recommendations = []

  if (shortages.length === 0) {
    recommendations.push({
      type: "success",
      message: "All materials are available. Production can proceed.",
      action: workOrderStatus === "pending" ? "start_production" : null,
    })
  } else {
    // Critical shortages
    const criticalShortages = shortages.filter(s => s.shortageQty > 0)

    if (criticalShortages.length > 0) {
      recommendations.push({
        type: "error",
        message: `${criticalShortages.length} critical material shortage(s) prevent production.`,
        action: "resolve_shortages",
        details: criticalShortages.map(s => ({
          material: s.materialName,
          shortage: s.shortageQty,
          suggestion: "Create purchase order or check incoming deliveries",
        })),
      })
    }

    // Low stock warnings
    const lowStockItems = shortages.filter(s => s.availableQty > 0 && s.shortageQty <= 0)

    if (lowStockItems.length > 0) {
      recommendations.push({
        type: "warning",
        message: `${lowStockItems.length} material(s) have low stock levels.`,
        action: "monitor_stock",
        details: lowStockItems.map(s => ({
          material: s.materialName,
          available: s.availableQty,
          suggestion: "Consider reordering to maintain safety stock",
        })),
      })
    }
  }

  return recommendations
}
