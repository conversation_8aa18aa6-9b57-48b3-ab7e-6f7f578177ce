/**
 * Inventory Financial Impact API
 * 
 * Provides real-time financial impact data for inventory changes.
 * Integrates with existing inventory module without breaking changes.
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { OperationalFinancialIntegration } from "@/lib/services/operational-financial-integration"

export const GET = withTenantAuth(async function GET(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const url = new URL(request.url)
    const quantityChange = parseFloat(url.searchParams.get('quantityChange') || '0')
    
    if (!id) {
      return jsonError("Product ID is required", 400)
    }

    const financialService = new OperationalFinancialIntegration(context)
    
    // Get inventory financial impact
    const impact = await financialService.getInventoryFinancialImpact(id, quantityChange)
    
    const response = {
      currentValue: impact.currentValue,
      valueChange: impact.valueChange,
      averageCostPerUnit: impact.averageCostPerUnit,
      totalUnits: impact.totalUnits,
      currency: impact.currency,
      // Convert to format expected by FinancialImpactCard
      revenue: impact.currentValue, // Current inventory value as "revenue"
      cogs: impact.currentValue - impact.valueChange, // Previous value as "cogs"
      grossProfit: impact.valueChange, // Value change as "profit"
      profitMargin: impact.currentValue > 0 ? (impact.valueChange / impact.currentValue) * 100 : 0,
      timestamp: new Date().toISOString()
    }

    return jsonOk(response)
    
  } catch (error) {
    console.error("Inventory financial impact API error:", error)
    
    if (error instanceof Error && error.message.includes("not found")) {
      return jsonError("Product not found", 404)
    }
    
    return jsonError("Failed to calculate inventory financial impact", 500)
  }
})
