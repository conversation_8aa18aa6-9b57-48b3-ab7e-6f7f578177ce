/**
 * Shipment Financial Impact API
 * 
 * Provides real-time financial impact data for shipments.
 * Integrates with existing shipping module without breaking changes.
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { OperationalFinancialIntegration } from "@/lib/services/operational-financial-integration"

export const GET = withTenantAuth(async function GET(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    if (!id) {
      return jsonError("Shipment ID is required", 400)
    }

    const financialService = new OperationalFinancialIntegration(context)
    
    // Get shipment financial impact
    const impact = await financialService.getShipmentFinancialImpact(id)
    
    // Get container utilization if available
    let containerImpact = null
    try {
      containerImpact = await financialService.getContainerFinancialImpact(id)
    } catch (error) {
      // Container impact is optional - don't fail if unavailable
      console.warn("Container impact calculation failed:", error)
    }

    const response = {
      ...impact,
      utilizationRate: containerImpact?.utilizationRate,
      timestamp: new Date().toISOString()
    }

    return jsonOk(response)
    
  } catch (error) {
    console.error("Shipment financial impact API error:", error)
    
    if (error instanceof Error && error.message.includes("not found")) {
      return jsonError("Shipment not found", 404)
    }
    
    return jsonError("Failed to calculate financial impact", 500)
  }
})
