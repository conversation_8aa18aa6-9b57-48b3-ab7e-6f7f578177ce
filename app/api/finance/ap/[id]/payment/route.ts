import { db } from "@/lib/db"
import { apInvoices } from "@/lib/schema-postgres"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { withTenantAuth } from "@/lib/tenant-utils"
import { createFinancialService } from "@/lib/services/financial-integration"
import { eq, and } from "drizzle-orm"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const paymentSchema = z.object({
  amount: z.number().positive("Payment amount must be positive"),
  payment_date: z.string().optional(),
  notes: z.string().optional(),
})

// ============================================================================
// API ENDPOINTS
// ============================================================================

/**
 * Record payment for AP invoice
 * POST /api/finance/ap/[id]/payment
 */
export const POST = withTenantAuth(async function POST(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: invoiceId } = await params
    const body = await request.json()
    
    // Validate request body
    const data = paymentSchema.parse(body)

    // Initialize financial service
    const financialService = createFinancialService(context)

    // Record payment
    const result = await financialService.recordAPPayment({
      invoiceId,
      amount: data.amount,
      paymentDate: data.payment_date,
      notes: data.notes,
    })

    return jsonOk({
      ...result,
      message: `Payment of ${data.amount} recorded successfully`,
    })

  } catch (error) {
    console.error("Error recording AP payment:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid payment data", 400, error.errors)
    }
    
    return jsonError(
      error instanceof Error ? error.message : "Failed to record payment",
      500
    )
  }
})

/**
 * Get payment history for AP invoice
 * GET /api/finance/ap/[id]/payment
 */
export const GET = withTenantAuth(async function GET(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: invoiceId } = await params

    // Verify invoice exists and belongs to company
    const invoice = await db.query.apInvoices.findFirst({
      where: and(
        eq(apInvoices.id, invoiceId),
        eq(apInvoices.company_id, context.companyId)
      ),
    })

    if (!invoice) {
      return jsonError("Invoice not found", 404)
    }

    // Initialize financial service
    const financialService = createFinancialService(context)

    // Get payment history
    const paymentHistory = await financialService.getAPPaymentHistory(invoiceId)

    return jsonOk({
      invoice: {
        id: invoice.id,
        number: invoice.number,
        amount: invoice.amount,
        paid: invoice.paid,
        currency: invoice.currency,
        status: invoice.status,
      },
      paymentHistory,
      summary: {
        totalAmount: parseFloat(invoice.amount),
        totalPaid: parseFloat(invoice.paid || '0'),
        remainingBalance: parseFloat(invoice.amount) - parseFloat(invoice.paid || '0'),
      },
    })

  } catch (error) {
    console.error("Error fetching AP payment history:", error)
    return jsonError("Failed to fetch payment history", 500)
  }
})
