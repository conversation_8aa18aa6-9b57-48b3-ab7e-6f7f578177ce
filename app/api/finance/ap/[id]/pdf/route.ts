import { db } from "@/lib/db"
import { apInvoices } from "@/lib/schema-postgres"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { withTenantAuth } from "@/lib/tenant-utils"
import { eq, and } from "drizzle-orm"

/**
 * Generate PDF for AP Invoice
 * GET /api/finance/ap/[id]/pdf
 */
export const GET = withTenantAuth(async function GET(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Get invoice with all related data
    const invoice = await db.query.apInvoices.findFirst({
      where: and(
        eq(apInvoices.id, id),
        eq(apInvoices.company_id, context.companyId)
      ),
      with: {
        company: true,
        supplier: true,
        purchaseContract: true,
      },
    })

    if (!invoice) {
      return jsonError("Invoice not found", 404)
    }

    // Prepare invoice data for PDF generation
    const invoiceData = {
      // Invoice details
      number: invoice.number,
      date: invoice.date,
      due_date: invoice.due_date,
      amount: invoice.amount,
      paid: invoice.paid,
      currency: invoice.currency,
      status: invoice.status,
      payment_terms: invoice.payment_terms,
      notes: invoice.notes,
      
      // Company details
      company: {
        name: invoice.company?.name || "Manufacturing ERP Company",
        address: invoice.company?.address,
        phone: invoice.company?.phone,
        email: invoice.company?.email,
      },
      
      // Supplier details
      supplier: invoice.supplier ? {
        name: invoice.supplier.name,
        company: invoice.supplier.company,
        address: invoice.supplier.address,
        phone: invoice.supplier.phone,
        email: invoice.supplier.email,
      } : undefined,
      
      // Contract details (if available)
      contract: invoice.purchaseContract ? {
        number: invoice.purchaseContract.number,
        title: invoice.purchaseContract.title,
      } : undefined,
    }

    return jsonOk({
      invoiceData,
      invoiceType: 'ap',
      message: `PDF data prepared for AP Invoice ${invoice.number}`,
    })

  } catch (error) {
    console.error("Error preparing AP invoice PDF data:", error)
    return jsonError("Failed to prepare PDF data", 500)
  }
})
