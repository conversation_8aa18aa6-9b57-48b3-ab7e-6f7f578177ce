import { db } from "@/lib/db"
import { apInvoices } from "@/lib/schema-postgres"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { withTenantAuth } from "@/lib/tenant-utils"
import { desc, eq, like } from "drizzle-orm"

/**
 * Generate next AP invoice number
 * GET /api/finance/ap/generate-number
 */
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const currentYear = new Date().getFullYear()
    const prefix = `BILL-${currentYear}-`
    
    // Find the highest invoice number for current year
    const lastInvoice = await db.query.apInvoices.findFirst({
      where: eq(apInvoices.company_id, context.companyId),
      orderBy: [desc(apInvoices.created_at)],
      columns: {
        number: true,
      },
    })

    let nextNumber = 1
    
    if (lastInvoice?.number && lastInvoice.number.startsWith(prefix)) {
      // Extract the sequence number from the last invoice
      const lastSequence = lastInvoice.number.replace(prefix, '')
      const lastNum = parseInt(lastSequence)
      if (!isNaN(lastNum)) {
        nextNumber = lastNum + 1
      }
    }

    // Format with leading zeros (3 digits)
    const formattedNumber = nextNumber.toString().padStart(3, '0')
    const invoiceNumber = `${prefix}${formattedNumber}`

    return jsonOk({
      number: invoiceNumber,
      sequence: nextNumber,
      year: currentYear,
    })
  } catch (error) {
    console.error("Failed to generate AP invoice number:", error)
    return jsonError("Failed to generate invoice number", 500)
  }
})
