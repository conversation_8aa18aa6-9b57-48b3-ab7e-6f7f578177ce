import { json<PERSON>rror, jsonOk } from "@/lib/api-helpers"
import { withTenantAuth } from "@/lib/tenant-utils"
import { ShippingFinancialIntegration } from "@/lib/services/shipping-financial-integration"
import { z } from "zod"

/**
 * Manufacturing ERP - Auto Invoice Generation API
 * Professional endpoint for automatic AR invoice generation from shipments
 * Zero-breaking-changes: Additive API that extends existing financial functionality
 */

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const autoInvoiceSchema = z.object({
  shipmentId: z.string().min(1, "Shipment ID is required"),
  invoiceNumber: z.string().optional(),
  dueDate: z.string().optional(),
  paymentTerms: z.string().optional(),
  notes: z.string().optional(),
  autoGenerate: z.boolean().default(true),
})

const batchAutoInvoiceSchema = z.object({
  shipmentIds: z.array(z.string()).min(1, "At least one shipment ID is required"),
  paymentTerms: z.string().optional(),
  notes: z.string().optional(),
})

// ============================================================================
// API ENDPOINTS
// ============================================================================

/**
 * POST /api/finance/auto-invoice
 * Generate AR invoice from shipped order
 */
export const POST = withTenantAuth(async function POST(req: Request, context) {
  try {
    const body = await req.json()
    const data = autoInvoiceSchema.parse(body)

    console.log(`🔄 Auto-generating AR invoice for shipment: ${data.shipmentId}`)

    // Initialize the shipping financial integration service
    const financialIntegration = new ShippingFinancialIntegration(context)

    // Generate AR invoice from shipment
    const invoice = await financialIntegration.generateARInvoiceFromShipment({
      shipmentId: data.shipmentId,
      invoiceNumber: data.invoiceNumber,
      dueDate: data.dueDate,
      paymentTerms: data.paymentTerms,
      notes: data.notes,
      autoGenerate: data.autoGenerate,
    })

    console.log(`✅ Auto-generated AR invoice: ${invoice.number} for $${invoice.amount}`)

    return jsonOk({
      success: true,
      invoice: {
        id: invoice.id,
        number: invoice.number,
        amount: invoice.amount,
        customer_id: invoice.customer_id,
        customer_name: invoice.customer?.name,
        status: invoice.status,
        due_date: invoice.due_date,
        created_at: invoice.created_at,
      },
      message: `AR invoice ${invoice.number} generated successfully from shipment`,
    }, { status: 201 })

  } catch (error) {
    console.error("❌ Auto-invoice generation failed:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        errors: error.errors,
      })
    }

    if (error.code === "REFERENCE_NOT_FOUND") {
      return jsonError("Shipment not found", 404, {
        error: error.message,
      })
    }

    if (error.code === "INVALID_STATE_TRANSITION") {
      return jsonError("Invalid shipment status", 400, {
        error: error.message,
      })
    }

    if (error.code === "INVALID_BUSINESS_LOGIC") {
      return jsonError("Business logic error", 400, {
        error: error.message,
      })
    }

    return jsonError("Failed to generate invoice", 500, {
      error: error.message,
    })
  }
})

/**
 * POST /api/finance/auto-invoice/batch
 * Generate AR invoices for multiple shipments
 */
export const PUT = withTenantAuth(async function PUT(req: Request, context) {
  try {
    const body = await req.json()
    const data = batchAutoInvoiceSchema.parse(body)

    console.log(`🔄 Batch auto-generating AR invoices for ${data.shipmentIds.length} shipments`)

    // Initialize the shipping financial integration service
    const financialIntegration = new ShippingFinancialIntegration(context)

    const results = []
    const errors = []

    // Process each shipment
    for (const shipmentId of data.shipmentIds) {
      try {
        const invoice = await financialIntegration.generateARInvoiceFromShipment({
          shipmentId,
          paymentTerms: data.paymentTerms,
          notes: data.notes,
          autoGenerate: true,
        })

        results.push({
          shipmentId,
          success: true,
          invoice: {
            id: invoice.id,
            number: invoice.number,
            amount: invoice.amount,
            customer_name: invoice.customer?.name,
          },
        })

        console.log(`✅ Generated invoice ${invoice.number} for shipment ${shipmentId}`)

      } catch (error) {
        console.error(`❌ Failed to generate invoice for shipment ${shipmentId}:`, error)
        
        errors.push({
          shipmentId,
          success: false,
          error: error.message,
        })
      }
    }

    const successCount = results.length
    const errorCount = errors.length

    console.log(`📊 Batch processing complete: ${successCount} success, ${errorCount} errors`)

    return jsonOk({
      success: true,
      summary: {
        total: data.shipmentIds.length,
        successful: successCount,
        failed: errorCount,
      },
      results,
      errors: errorCount > 0 ? errors : undefined,
      message: `Processed ${data.shipmentIds.length} shipments: ${successCount} invoices generated, ${errorCount} errors`,
    })

  } catch (error) {
    console.error("❌ Batch auto-invoice generation failed:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        errors: error.errors,
      })
    }

    return jsonError("Failed to process batch invoice generation", 500, {
      error: error.message,
    })
  }
})

/**
 * GET /api/finance/auto-invoice/status/{shipmentId}
 * Check if AR invoice exists for a shipment
 */
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const url = new URL(request.url)
    const shipmentId = url.searchParams.get('shipmentId')

    if (!shipmentId) {
      return jsonError("Shipment ID is required", 400)
    }

    console.log(`🔍 Checking invoice status for shipment: ${shipmentId}`)

    // Initialize the shipping financial integration service
    const financialIntegration = new ShippingFinancialIntegration(context)

    // Get financial summary which includes invoice status
    const summary = await financialIntegration.getShipmentFinancialSummary(shipmentId)

    return jsonOk({
      shipmentId,
      shipmentNumber: summary.shipmentNumber,
      invoiceGenerated: summary.invoiceGenerated,
      invoiceId: summary.invoiceId,
      financialSummary: {
        totalRevenue: summary.totalRevenue,
        totalCOGS: summary.totalCOGS,
        grossProfit: summary.grossProfit,
        profitMargin: summary.profitMargin,
      },
    })

  } catch (error) {
    console.error("❌ Invoice status check failed:", error)

    if (error.code === "REFERENCE_NOT_FOUND") {
      return jsonError("Shipment not found", 404, {
        error: error.message,
      })
    }

    return jsonError("Failed to check invoice status", 500, {
      error: error.message,
    })
  }
})
