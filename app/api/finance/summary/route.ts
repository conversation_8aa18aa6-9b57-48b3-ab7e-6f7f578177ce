/**
 * Manufacturing ERP - Financial Summary API
 * Professional financial dashboard metrics and analytics
 */

import { jsonError, jsonOk } from "@/lib/api-helpers"
import { withTenantAuth } from "@/lib/tenant-utils"
import { createFinancialService } from "@/lib/services/financial-integration"

// ============================================================================
// API ENDPOINTS
// ============================================================================

/**
 * Get comprehensive manufacturing financial KPIs
 * GET /api/finance/summary
 * GET /api/finance/summary?type=kpis (enhanced KPIs)
 * GET /api/finance/summary?type=legacy (legacy summary)
 */
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const url = new URL(request.url)
    const type = url.searchParams.get("type") || "kpis"

    const financialService = createFinancialService(context)

    if (type === "kpis") {
      // Get comprehensive manufacturing KPIs
      const kpis = await financialService.getManufacturingFinancialKPIs()

      return jsonOk({
        kpis,
        type: "manufacturing_financial_kpis",
        timestamp: new Date().toISOString(),
        message: "Manufacturing financial KPIs retrieved successfully",
      })
    }

    if (type === "legacy") {
      // Legacy financial summary for backward compatibility
      const summary = await financialService.getFinancialSummary()

      const metrics = {
        ...summary,
        arTurnover: summary.totalAR > 0 ? (summary.totalAR - summary.outstandingAR) / summary.totalAR : 0,
        apTurnover: summary.totalAP > 0 ? (summary.totalAP - summary.outstandingAP) / summary.totalAP : 0,
        netCashFlow: (summary.totalAR - summary.outstandingAR) - (summary.totalAP - summary.outstandingAP),
        overdueRatio: {
          ar: summary.outstandingAR > 0 ? summary.overdueAR / summary.outstandingAR : 0,
          ap: summary.outstandingAP > 0 ? summary.overdueAP / summary.outstandingAP : 0,
        },
        financialHealth: {
          arHealth: summary.overdueAR / Math.max(summary.totalAR, 1) < 0.1 ? "good" :
            summary.overdueAR / Math.max(summary.totalAR, 1) < 0.25 ? "warning" : "critical",
          apHealth: summary.overdueAP / Math.max(summary.totalAP, 1) < 0.1 ? "good" :
            summary.overdueAP / Math.max(summary.totalAP, 1) < 0.25 ? "warning" : "critical",
        },
      }

      return jsonOk({
        summary: metrics,
        type: "legacy_summary",
        timestamp: new Date().toISOString(),
        message: "Legacy financial summary retrieved successfully",
      })
    }

    return jsonError("Invalid summary type. Use 'kpis' or 'legacy'", 400)

  } catch (error) {
    console.error("Financial summary error:", error)
    return jsonError(
      error instanceof Error ? error.message : "Failed to get financial summary",
      500
    )
  }
})

/**
 * Get aging report
 * GET /api/finance/summary?type=aging
 */
export const getAgingReport = withTenantAuth(async function GET(request, context) {
  try {
    const url = new URL(request.url)
    const type = url.searchParams.get("type")

    if (type !== "aging") {
      return jsonError("Invalid report type", 400)
    }

    const financialService = createFinancialService(context)
    const agingReport = await financialService.getAgingReport()

    return jsonOk({
      agingReport,
      timestamp: new Date().toISOString(),
      message: "Aging report retrieved successfully",
    })

  } catch (error) {
    console.error("Aging report error:", error)
    return jsonError(
      error instanceof Error ? error.message : "Failed to get aging report",
      500
    )
  }
})
