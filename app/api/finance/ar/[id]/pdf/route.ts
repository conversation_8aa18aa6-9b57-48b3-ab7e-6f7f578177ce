import { db } from "@/lib/db"
import { arInvoices } from "@/lib/schema-postgres"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { withTenantAuth } from "@/lib/tenant-utils"
import { eq, and } from "drizzle-orm"

/**
 * Generate PDF for AR Invoice
 * GET /api/finance/ar/[id]/pdf
 */
export const GET = withTenantAuth(async function GET(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Get invoice with all related data
    const invoice = await db.query.arInvoices.findFirst({
      where: and(
        eq(arInvoices.id, id),
        eq(arInvoices.company_id, context.companyId)
      ),
      with: {
        company: true,
        customer: true,
        salesContract: true,
      },
    })

    if (!invoice) {
      return jsonError("Invoice not found", 404)
    }

    // Prepare invoice data for PDF generation
    const invoiceData = {
      // Invoice details
      number: invoice.number,
      date: invoice.date,
      due_date: invoice.due_date,
      amount: invoice.amount,
      received: invoice.received,
      currency: invoice.currency,
      status: invoice.status,
      payment_terms: invoice.payment_terms,
      notes: invoice.notes,
      
      // Company details
      company: {
        name: invoice.company?.name || "Manufacturing ERP Company",
        address: invoice.company?.address,
        phone: invoice.company?.phone,
        email: invoice.company?.email,
      },
      
      // Customer details
      customer: invoice.customer ? {
        name: invoice.customer.name,
        company: invoice.customer.company,
        address: invoice.customer.address,
        phone: invoice.customer.phone,
        email: invoice.customer.email,
      } : undefined,
      
      // Contract details (if available)
      contract: invoice.salesContract ? {
        number: invoice.salesContract.number,
        title: invoice.salesContract.title,
      } : undefined,
    }

    return jsonOk({
      invoiceData,
      invoiceType: 'ar',
      message: `PDF data prepared for AR Invoice ${invoice.number}`,
    })

  } catch (error) {
    console.error("Error preparing AR invoice PDF data:", error)
    return jsonError("Failed to prepare PDF data", 500)
  }
})
