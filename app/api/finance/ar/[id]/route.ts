import { db } from "@/lib/db"
import { json<PERSON>rror, json<PERSON>k } from "@/lib/api-helpers"
import { arInvoices } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"
import { z } from "zod"

// Validation schema for AR invoice updates
const updateSchema = z.object({
  number: z.string().min(1, "Invoice number is required").optional(),
  customer_id: z.string().min(1, "Customer is required").optional(),
  sales_contract_id: z.string().nullable().optional(),
  amount: z.string().min(1, "Amount is required").optional(),
  received: z.string().optional(),
  currency: z.string().min(1, "Currency is required").optional(),
  date: z.string().min(1, "Invoice date is required").optional(),
  due_date: z.string().nullable().optional(),
  payment_terms: z.string().min(1, "Payment terms are required").optional(),
  status: z.enum(["draft", "sent", "paid", "overdue", "cancelled", "deposit_received", "partial_paid"]).optional(),
  notes: z.string().nullable().optional(),
})

// 🛡️ SECURE: Multi-tenant PATCH endpoint for updating AR invoices
export const PATCH = withTenantAuth(async function PATCH(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // Validate input data
    const validatedData = updateSchema.parse(body)

    // Check if invoice exists and belongs to the company
    const existingInvoice = await db.query.arInvoices.findFirst({
      where: and(
        eq(arInvoices.id, id),
        eq(arInvoices.company_id, context.companyId)
      )
    })

    if (!existingInvoice) {
      return jsonError("AR invoice not found", 404)
    }

    // Update the invoice - be explicit about fields to avoid serialization issues
    const updateData: any = {}

    // Only include fields that are actually being updated
    if (validatedData.number !== undefined) updateData.number = validatedData.number
    if (validatedData.customer_id !== undefined) updateData.customer_id = validatedData.customer_id
    if (validatedData.sales_contract_id !== undefined) updateData.sales_contract_id = validatedData.sales_contract_id
    if (validatedData.amount !== undefined) updateData.amount = validatedData.amount
    if (validatedData.received !== undefined) updateData.received = validatedData.received
    if (validatedData.currency !== undefined) updateData.currency = validatedData.currency
    if (validatedData.date !== undefined) updateData.date = validatedData.date
    if (validatedData.due_date !== undefined) updateData.due_date = validatedData.due_date
    if (validatedData.payment_terms !== undefined) updateData.payment_terms = validatedData.payment_terms
    if (validatedData.status !== undefined) updateData.status = validatedData.status
    if (validatedData.notes !== undefined) updateData.notes = validatedData.notes

    const [updatedInvoice] = await db
      .update(arInvoices)
      .set(updateData)
      .where(and(
        eq(arInvoices.id, id),
        eq(arInvoices.company_id, context.companyId)
      ))
      .returning()

    return jsonOk(updatedInvoice)
  } catch (error) {
    console.error("Error updating AR invoice:", error)
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }
    return jsonError("Failed to update AR invoice", 500)
  }
})

/**
 * Delete AR Invoice
 * DELETE /api/finance/ar/[id]
 */
export const DELETE = withTenantAuth(async function DELETE(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // First check if the invoice exists and belongs to the company
    const existingInvoice = await db.query.arInvoices.findFirst({
      where: and(
        eq(arInvoices.id, id),
        eq(arInvoices.company_id, context.companyId)
      ),
    })

    if (!existingInvoice) {
      return jsonError("AR invoice not found", 404)
    }

    // Delete the invoice
    await db.delete(arInvoices).where(
      and(
        eq(arInvoices.id, id),
        eq(arInvoices.company_id, context.companyId)
      )
    )

    return jsonOk({
      message: "AR invoice deleted successfully",
      deletedId: id
    })

  } catch (error) {
    console.error("Error deleting AR invoice:", error)
    return jsonError("Failed to delete AR invoice", 500)
  }
})
