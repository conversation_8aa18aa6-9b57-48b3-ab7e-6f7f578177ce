/**
 * Manufacturing ERP - AR Invoice Payment Recording
 * Professional payment tracking with audit trails
 */

import { jsonError, jsonOk } from "@/lib/api-helpers"
import { withTenantAuth } from "@/lib/tenant-utils"
import { createFinancialService } from "@/lib/services/financial-integration"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const paymentSchema = z.object({
  amount: z.number().positive("Payment amount must be positive"),
  payment_date: z.string().optional(),
  notes: z.string().optional(),
})

// ============================================================================
// API ENDPOINTS
// ============================================================================

/**
 * Record payment for AR invoice
 * POST /api/finance/ar/[id]/payment
 */
export const POST = withTenantAuth(async function POST(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: invoiceId } = await params
    const body = await request.json()
    
    // Validate request body
    const data = paymentSchema.parse(body)

    // Initialize financial service
    const financialService = createFinancialService(context)

    // Record payment
    const result = await financialService.recordARPayment({
      invoiceId,
      amount: data.amount,
      paymentDate: data.payment_date,
      notes: data.notes,
    })

    return jsonOk({
      ...result,
      message: `Payment of ${data.amount} recorded successfully`,
    })

  } catch (error) {
    console.error("Record payment error:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid payment data", 400, error.errors)
    }
    
    return jsonError(
      error instanceof Error ? error.message : "Failed to record payment",
      500
    )
  }
})

/**
 * Get payment history for AR invoice
 * GET /api/finance/ar/[id]/payment
 */
export const GET = withTenantAuth(async function GET(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: invoiceId } = await params

    const financialService = createFinancialService(context)
    
    // Get invoice with payment details
    const invoice = await financialService.getInvoiceDetails(invoiceId)

    if (!invoice) {
      return jsonError("Invoice not found", 404)
    }

    const totalAmount = parseFloat(invoice.amount)
    const receivedAmount = parseFloat(invoice.received || "0")
    const outstandingAmount = totalAmount - receivedAmount

    return jsonOk({
      invoice: {
        id: invoice.id,
        number: invoice.number,
        totalAmount,
        receivedAmount,
        outstandingAmount,
        status: invoice.status,
        dueDate: invoice.due_date,
      },
      paymentSummary: {
        totalAmount,
        receivedAmount,
        outstandingAmount,
        percentagePaid: totalAmount > 0 ? (receivedAmount / totalAmount) * 100 : 0,
        isOverdue: invoice.due_date && invoice.due_date < new Date().toISOString().split('T')[0] && outstandingAmount > 0,
      },
    })

  } catch (error) {
    console.error("Get payment details error:", error)
    return jsonError(
      error instanceof Error ? error.message : "Failed to get payment details",
      500
    )
  }
})
