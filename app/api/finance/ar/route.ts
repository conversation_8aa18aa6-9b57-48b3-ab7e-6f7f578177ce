import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { arInvoices } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"
// TODO: Re-enable after fixing financial service
// import { createFinancialService } from "@/lib/services/financial-integration"
import { z } from "zod"

// ✅ ENHANCED: Multi-tenant GET endpoint with contract relationships
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.arInvoices.findMany({
      where: eq(arInvoices.company_id, context.companyId),
      orderBy: [desc(arInvoices.created_at)],
      with: {
        customer: true,
        salesContract: true,
      },
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
})

// ✅ ENHANCED: Support both manual and contract-based invoice creation
const createInvoiceSchema = z.object({
  // Manual invoice creation
  number: z.string().optional(),
  customer_id: z.string().optional(),
  amount: z.number().optional(),
  currency: z.string().default("USD"),
  date: z.string().optional(),
  due_date: z.string().optional(),
  payment_terms: z.string().optional(),
  notes: z.string().optional(),

  // Contract-based invoice creation
  sales_contract_id: z.string().optional(),
  partial_amount: z.number().optional(),
})

export const POST = withTenantAuth(async function POST(req: Request, context) {
  try {
    const body = await req.json()
    const data = createInvoiceSchema.parse(body)

    // TODO: Re-enable contract-based invoice generation after fixing financial service
    // For now, use manual creation for all invoices
    // if (data.sales_contract_id) {
    //   const financialService = createFinancialService(context)
    //   const invoice = await financialService.generateARInvoiceFromContract({
    //     contractId: data.sales_contract_id,
    //     invoiceNumber: data.number,
    //     dueDate: data.due_date,
    //     paymentTerms: data.payment_terms,
    //     notes: data.notes,
    //     partialAmount: data.partial_amount,
    //   })
    //   return jsonOk(invoice, { status: 201 })
    // }

    // Manual invoice creation
    const id = uid("ari")
    const newInvoice = {
      id,
      company_id: context.companyId,
      number: data.number || `INV-${Date.now()}`,
      customer_id: data.customer_id!,
      sales_contract_id: data.sales_contract_id || null,
      date: data.date || new Date().toISOString().split('T')[0],
      due_date: data.due_date || null,
      amount: data.amount!.toString(),
      currency: data.currency || "USD",
      status: "draft",
      payment_terms: data.payment_terms || "TT",
      notes: data.notes || null,
    }

    await db.insert(arInvoices).values(newInvoice)

    const row = await db.query.arInvoices.findFirst({
      where: and(
        eq(arInvoices.id, id),
        eq(arInvoices.company_id, context.companyId)
      ),
      with: {
        customer: true,
        salesContract: true,
      },
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
})
