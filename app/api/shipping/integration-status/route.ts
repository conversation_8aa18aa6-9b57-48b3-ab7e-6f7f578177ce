import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { shipments, shipmentItems, stockTxns } from "@/lib/schema-postgres"
import { eq, and, desc } from "drizzle-orm"

interface IntegrationStatus {
  summary: {
    totalShipments: number
    shipmentsWithStockLots: number
    shipmentsWithTransactions: number
    integrationRate: number
    transactionCoverage: number
  }
  breakdown: {
    byStatus: {
      [status: string]: {
        total: number
        withStockLots: number
        withTransactions: number
        integrationRate: number
      }
    }
    recent: Array<{
      shipmentNumber: string
      status: string
      itemCount: number
      integratedItems: number
      hasTransactions: boolean
      createdAt: string
    }>
  }
  healthScore: number
  recommendations: string[]
}

export const GET = withTenantAuth(async function GET(request, context) {
  try {
    console.log('🔄 Fetching shipping-inventory integration status...')

    // Get all shipments with their items and relationships
    const allShipments = await db.query.shipments.findMany({
      where: eq(shipments.company_id, context.companyId),
      with: {
        items: {
          with: {
            product: true,
            stockLot: true
          }
        }
      },
      orderBy: [desc(shipments.created_at)]
    })

    // Analyze integration status
    let shipmentsWithStockLots = 0
    let shipmentsWithTransactions = 0
    const statusBreakdown: { [status: string]: { total: number, withStockLots: number, withTransactions: number } } = {}
    const recentShipments = []

    for (const shipment of allShipments) {
      // Check stock lot integration
      const hasStockLots = shipment.items.some(item => item.stock_lot_id)
      const integratedItems = shipment.items.filter(item => item.stock_lot_id).length
      
      if (hasStockLots) shipmentsWithStockLots++

      // Check transaction integration
      const transactions = await db.query.stockTxns.findMany({
        where: and(
          eq(stockTxns.company_id, context.companyId),
          eq(stockTxns.reference_id, shipment.id),
          eq(stockTxns.reason_code, "shipment")
        )
      })
      
      const hasTransactions = transactions.length > 0
      if (hasTransactions) shipmentsWithTransactions++

      // Status breakdown
      const status = shipment.status || 'unknown'
      if (!statusBreakdown[status]) {
        statusBreakdown[status] = { total: 0, withStockLots: 0, withTransactions: 0 }
      }
      statusBreakdown[status].total++
      if (hasStockLots) statusBreakdown[status].withStockLots++
      if (hasTransactions) statusBreakdown[status].withTransactions++

      // Recent shipments (last 10)
      if (recentShipments.length < 10) {
        recentShipments.push({
          shipmentNumber: shipment.shipment_number || 'N/A',
          status: shipment.status || 'unknown',
          itemCount: shipment.items.length,
          integratedItems,
          hasTransactions,
          createdAt: shipment.created_at?.toISOString() || new Date().toISOString()
        })
      }
    }

    // Calculate rates
    const integrationRate = allShipments.length > 0 
      ? Math.round((shipmentsWithStockLots / allShipments.length) * 100)
      : 100

    const transactionCoverage = allShipments.length > 0 
      ? Math.round((shipmentsWithTransactions / allShipments.length) * 100)
      : 100

    // Calculate health score
    let healthScore = 0
    if (integrationRate >= 95) healthScore += 40
    else if (integrationRate >= 85) healthScore += 30
    else if (integrationRate >= 70) healthScore += 20
    else healthScore += 10

    if (transactionCoverage >= 95) healthScore += 40
    else if (transactionCoverage >= 85) healthScore += 30
    else if (transactionCoverage >= 70) healthScore += 20
    else healthScore += 10

    // Data consistency bonus
    const consistencyBonus = Math.min(20, Math.round((integrationRate + transactionCoverage) / 10))
    healthScore += consistencyBonus

    // Generate recommendations
    const recommendations: string[] = []
    
    if (integrationRate < 90) {
      recommendations.push(`Integration rate is ${integrationRate}%. Run legacy shipment repair to improve.`)
    }
    
    if (transactionCoverage < 90) {
      recommendations.push(`Transaction coverage is ${transactionCoverage}%. Some shipped orders lack stock transactions.`)
    }
    
    const shipmentsWithoutIntegration = allShipments.length - shipmentsWithStockLots
    if (shipmentsWithoutIntegration > 0) {
      recommendations.push(`${shipmentsWithoutIntegration} shipments lack inventory integration. Consider running repair tool.`)
    }
    
    if (healthScore >= 90) {
      recommendations.push("Excellent integration health! System is operating optimally.")
    } else if (healthScore >= 75) {
      recommendations.push("Good integration health with minor improvements needed.")
    } else {
      recommendations.push("Integration health needs attention. Run diagnostic and repair tools.")
    }

    // Build status breakdown with rates
    const byStatus: { [status: string]: any } = {}
    Object.entries(statusBreakdown).forEach(([status, data]) => {
      byStatus[status] = {
        ...data,
        integrationRate: data.total > 0 ? Math.round((data.withStockLots / data.total) * 100) : 100
      }
    })

    const integrationStatus: IntegrationStatus = {
      summary: {
        totalShipments: allShipments.length,
        shipmentsWithStockLots,
        shipmentsWithTransactions,
        integrationRate,
        transactionCoverage
      },
      breakdown: {
        byStatus,
        recent: recentShipments
      },
      healthScore,
      recommendations
    }

    console.log('✅ Integration status compiled successfully')
    console.log(`   Integration Rate: ${integrationRate}%`)
    console.log(`   Transaction Coverage: ${transactionCoverage}%`)
    console.log(`   Health Score: ${healthScore}/100`)

    return jsonOk(integrationStatus)

  } catch (error) {
    console.error('❌ Error fetching integration status:', error)
    return jsonError('Failed to fetch integration status', 500, {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})
