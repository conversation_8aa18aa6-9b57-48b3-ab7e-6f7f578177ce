/**
 * Manufacturing ERP - Individual Shipment API
 * CRUD operations for specific shipments
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { shipments } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { ShippingService } from "@/lib/services/shipping-service"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const updateShipmentSchema = z.object({
  customer_id: z.string().optional(),
  shipping_method: z.enum(["sea_freight", "air_freight", "express", "truck"]).optional(),
  carrier: z.string().optional(),
  service_type: z.enum(["standard", "express", "economy"]).optional(),
  tracking_number: z.string().optional(),
  pickup_address: z.any().optional(),
  delivery_address: z.any().optional(),
  ship_date: z.string().optional(),
  estimated_delivery: z.string().optional(),
  shipping_cost: z.string().optional(),
  insurance_cost: z.string().optional(),
  notes: z.string().optional(),
  special_instructions: z.string().optional(),
  customs_declaration: z.string().optional(),
  status: z.enum([
    "preparing",
    "ready",
    "shipped",
    "in_transit",
    "out_for_delivery",
    "delivered",
    "cancelled",
    "exception"
  ]).optional(),
})

const statusUpdateSchema = z.object({
  status: z.enum([
    "preparing",
    "ready",
    "shipped",
    "in_transit",
    "out_for_delivery",
    "delivered",
    "cancelled",
    "exception"
  ]),
  location: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  estimated_delivery: z.string().optional(),
  notes: z.string().optional(),
})

// ============================================================================
// GET - Get Single Shipment
// ============================================================================

export const GET = withTenantAuth(async function GET(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const shipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, id),
        eq(shipments.company_id, context.companyId)
      ),
      with: {
        customer: {
          columns: {
            id: true,
            name: true,
            contact_name: true,
            contact_email: true,
            contact_phone: true,
            address: true
          }
        },
        salesContract: {
          columns: {
            id: true,
            number: true,
            status: true,
            date: true
          }
        },
        items: {
          with: {
            product: {
              columns: {
                id: true,
                name: true,
                sku: true,
                unit: true,
                image: true
              }
            },
            stockLot: {
              columns: {
                id: true,
                lot_number: true,
                location: true,
                quality_status: true
              }
            }
          }
        },
        documents: {
          orderBy: (documents, { desc }) => [desc(documents.created_at)]
        },
        tracking: {
          orderBy: (tracking, { desc }) => [desc(tracking.timestamp)]
        }
      }
    })

    if (!shipment) {
      return jsonError("Shipment not found", 404)
    }

    return jsonOk(shipment)

  } catch (error) {
    console.error("Error fetching shipment:", error)
    return jsonError("Failed to fetch shipment", 500)
  }
})

// ============================================================================
// PATCH - Update Shipment
// ============================================================================

export const PATCH = withTenantAuth(async function PATCH(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // Check if this is a status-only update (with description - from status tracking)
    if (body.status && body.description && !body.customer_id && !body.shipping_method && !body.carrier) {
      const validated = statusUpdateSchema.parse(body)

      const shippingService = new ShippingService()
      await shippingService.updateShipmentStatus(
        id,
        validated,
        context.companyId,
        context.userId
      )

      // Get updated shipment
      const updatedShipment = await db.query.shipments.findFirst({
        where: and(
          eq(shipments.id, id),
          eq(shipments.company_id, context.companyId)
        ),
        with: {
          customer: true,
          salesContract: true,
          items: {
            with: {
              product: true
            }
          },
          tracking: {
            orderBy: (tracking, { desc }) => [desc(tracking.timestamp)]
          }
        }
      })

      return jsonOk(updatedShipment)
    }

    // Regular shipment update
    const validated = updateShipmentSchema.parse(body)

    // Verify shipment exists and belongs to company
    const existingShipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, id),
        eq(shipments.company_id, context.companyId)
      )
    })

    if (!existingShipment) {
      return jsonError("Shipment not found", 404)
    }

    // Prepare update data with proper type conversions
    const updateData: any = {
      updated_at: new Date(),
      updated_by: context.userId
    }

    // Handle each field with proper type conversion
    Object.entries(validated).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        if (key === "ship_date" || key === "estimated_delivery") {
          // Convert date string to ISO string for database
          updateData[key] = new Date(value as string).toISOString()
        } else if (key === "shipping_cost" || key === "insurance_cost") {
          updateData[key] = parseFloat(value as string).toString()
        } else {
          updateData[key] = value
        }
      }
    })

    // Update shipment
    await db.update(shipments)
      .set(updateData)
      .where(and(
        eq(shipments.id, id),
        eq(shipments.company_id, context.companyId)
      ))

    // ✅ CRITICAL FIX: Handle status-specific logic for regular updates
    if (validated.status && validated.status !== existingShipment.status) {
      console.log(`Status changed from ${existingShipment.status} to ${validated.status} for shipment ${id}`)

      if (validated.status === "shipped") {
        console.log(`Triggering inventory integration for shipped shipment ${id}`)
        const shippingService = new ShippingService()
        await shippingService.handleShipmentShipped(id, context.companyId, context.userId)
      }
    }

    // Get updated shipment with relationships
    const updatedShipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, id),
        eq(shipments.company_id, context.companyId)
      ),
      with: {
        customer: true,
        salesContract: true,
        items: {
          with: {
            product: true
          }
        }
      }
    })

    return jsonOk(updatedShipment)

  } catch (error) {
    console.error("Error updating shipment:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    if (error instanceof Error) {
      return jsonError(error.message, 400)
    }

    return jsonError("Failed to update shipment", 500)
  }
})

// ============================================================================
// DELETE - Cancel Shipment
// ============================================================================

export const DELETE = withTenantAuth(async function DELETE(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Verify shipment exists and belongs to company
    const existingShipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, id),
        eq(shipments.company_id, context.companyId)
      )
    })

    if (!existingShipment) {
      return jsonError("Shipment not found", 404)
    }

    // Check if shipment can be cancelled
    if (existingShipment.status === "delivered") {
      return jsonError("Cannot cancel delivered shipment", 400)
    }

    if (existingShipment.status === "shipped" || existingShipment.status === "in_transit") {
      return jsonError("Cannot cancel shipment that is already in transit", 400)
    }

    // Update status to cancelled instead of deleting
    const shippingService = new ShippingService()
    await shippingService.updateShipmentStatus(
      id,
      {
        status: "cancelled",
        description: "Shipment cancelled by user",
        notes: "Shipment was cancelled and inventory has been released"
      },
      context.companyId,
      context.userId
    )

    // TODO: Release reserved inventory back to available status

    return jsonOk({ message: "Shipment cancelled successfully" })

  } catch (error) {
    console.error("Error cancelling shipment:", error)

    if (error instanceof Error) {
      return jsonError(error.message, 400)
    }

    return jsonError("Failed to cancel shipment", 500)
  }
})
