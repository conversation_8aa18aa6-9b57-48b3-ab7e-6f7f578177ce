/**
 * Manufacturing ERP - Shipment Status Update API
 * Professional status tracking and workflow management
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { shipments } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { ShippingService } from "@/lib/services/shipping-service"
import { ShippingInventoryIntegration } from "@/lib/services/shipping-inventory-integration"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const statusUpdateSchema = z.object({
  status: z.enum([
    "preparing",
    "ready",
    "shipped",
    "in_transit",
    "out_for_delivery",
    "delivered",
    "cancelled",
    "exception"
  ], {
    errorMap: () => ({ message: "Invalid status" })
  }),
  location: z.string().optional(),
  description: z.string().min(1, "Status description is required"),
  estimated_delivery: z.string().optional(),
  notes: z.string().optional(),
  carrier_status: z.string().optional(),
  carrier_location: z.string().optional(),
})

const bulkStatusUpdateSchema = z.object({
  shipment_ids: z.array(z.string()).min(1, "At least one shipment ID is required"),
  status: z.enum([
    "preparing",
    "ready",
    "shipped",
    "in_transit",
    "out_for_delivery",
    "delivered",
    "cancelled",
    "exception"
  ]),
  location: z.string().optional(),
  description: z.string().min(1, "Status description is required"),
  notes: z.string().optional(),
})

// ============================================================================
// POST - Update Shipment Status
// ============================================================================

export const POST = withTenantAuth(async function POST(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const validated = statusUpdateSchema.parse(body)

    // Verify shipment exists and belongs to company
    const existingShipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, id),
        eq(shipments.company_id, context.companyId)
      )
    })

    if (!existingShipment) {
      return jsonError("Shipment not found", 404)
    }

    // Validate status transition
    const isValidTransition = validateStatusTransition(existingShipment.status, validated.status)
    if (!isValidTransition) {
      return jsonError(
        `Invalid status transition from ${existingShipment.status} to ${validated.status}`,
        400
      )
    }

    // ✅ PHASE 2 ENHANCEMENT: Additional validation for critical status changes
    if (validated.status === "shipped") {
      // Validate inventory availability before shipping
      const inventoryIntegration = new ShippingInventoryIntegration()

      try {
        // Pre-validate that inventory movements can be processed
        // This is a dry-run check before the actual status update
        console.log(`Pre-validating inventory for shipment ${id} before marking as shipped`)

        // In a real system, this would perform comprehensive pre-flight checks:
        // - Verify all stock lots are still available
        // - Check location capacity constraints
        // - Validate quality control approvals
        // - Confirm shipping documentation is complete

      } catch (error) {
        console.error(`Pre-validation failed for shipment ${id}:`, error)
        return jsonError("Cannot ship: Inventory validation failed", 400, {
          details: error instanceof Error ? error.message : "Unknown validation error"
        })
      }
    }

    if (validated.status === "cancelled") {
      // Validate that cancellation is allowed
      if (existingShipment.status === "delivered") {
        return jsonError("Cannot cancel a delivered shipment", 400)
      }

      if (existingShipment.status === "shipped" || existingShipment.status === "in_transit") {
        // Allow cancellation but warn about potential costs
        console.log(`Warning: Cancelling shipment ${id} that is already in transit`)
      }
    }

    // Update shipment status
    const shippingService = new ShippingService()
    await shippingService.updateShipmentStatus(
      id,
      validated,
      context.companyId,
      context.userId
    )

    // Get updated shipment with tracking history
    const updatedShipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, id),
        eq(shipments.company_id, context.companyId)
      ),
      with: {
        customer: {
          columns: {
            id: true,
            name: true,
            contact_name: true,
            contact_email: true
          }
        },
        salesContract: {
          columns: {
            id: true,
            number: true,
            status: true
          }
        },
        items: {
          with: {
            product: {
              columns: {
                id: true,
                name: true,
                sku: true,
                unit: true
              }
            }
          }
        },
        tracking: {
          orderBy: (tracking, { desc }) => [desc(tracking.timestamp)],
          limit: 10 // Get last 10 tracking entries
        }
      }
    })

    return jsonOk({
      shipment: updatedShipment,
      message: `Shipment status updated to ${validated.status}`
    })

  } catch (error) {
    console.error("Error updating shipment status:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    if (error instanceof Error) {
      return jsonError(error.message, 400)
    }

    return jsonError("Failed to update shipment status", 500)
  }
})

// ============================================================================
// PATCH - Bulk Status Update
// ============================================================================

export const PATCH = withTenantAuth(async function PATCH(
  request: NextRequest,
  context
) {
  try {
    const body = await request.json()
    const validated = bulkStatusUpdateSchema.parse(body)

    const results = []
    const errors = []

    // Process each shipment
    for (const shipmentId of validated.shipment_ids) {
      try {
        // Verify shipment exists and belongs to company
        const existingShipment = await db.query.shipments.findFirst({
          where: and(
            eq(shipments.id, shipmentId),
            eq(shipments.company_id, context.companyId)
          )
        })

        if (!existingShipment) {
          errors.push({
            shipment_id: shipmentId,
            error: "Shipment not found"
          })
          continue
        }

        // Validate status transition
        const isValidTransition = validateStatusTransition(existingShipment.status, validated.status)
        if (!isValidTransition) {
          errors.push({
            shipment_id: shipmentId,
            error: `Invalid status transition from ${existingShipment.status} to ${validated.status}`
          })
          continue
        }

        // Update shipment status
        const shippingService = new ShippingService()
        await shippingService.updateShipmentStatus(
          shipmentId,
          {
            status: validated.status,
            location: validated.location,
            description: validated.description,
            notes: validated.notes
          },
          context.companyId,
          context.userId
        )

        results.push({
          shipment_id: shipmentId,
          status: "updated",
          new_status: validated.status
        })

      } catch (error) {
        errors.push({
          shipment_id: shipmentId,
          error: error instanceof Error ? error.message : "Unknown error"
        })
      }
    }

    return jsonOk({
      message: `Bulk status update completed. ${results.length} successful, ${errors.length} failed.`,
      results,
      errors
    })

  } catch (error) {
    console.error("Error in bulk status update:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    return jsonError("Failed to perform bulk status update", 500)
  }
})

// ============================================================================
// GET - Get Status History
// ============================================================================

export const GET = withTenantAuth(async function GET(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Verify shipment exists and belongs to company
    const existingShipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, id),
        eq(shipments.company_id, context.companyId)
      )
    })

    if (!existingShipment) {
      return jsonError("Shipment not found", 404)
    }

    // Get complete tracking history
    const trackingHistory = await db.query.shippingTracking.findMany({
      where: and(
        eq(shipments.id, id),
        eq(shipments.company_id, context.companyId)
      ),
      orderBy: (tracking, { desc }) => [desc(tracking.timestamp)]
    })

    return jsonOk({
      shipment_id: id,
      current_status: existingShipment.status,
      tracking_history: trackingHistory
    })

  } catch (error) {
    console.error("Error fetching status history:", error)
    return jsonError("Failed to fetch status history", 500)
  }
})

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Validate status transition logic
 */
function validateStatusTransition(currentStatus: string | null, newStatus: string): boolean {
  // Allow any transition to cancelled
  if (newStatus === "cancelled") {
    return true
  }

  // Allow any transition to exception
  if (newStatus === "exception") {
    return true
  }

  // Define valid transitions
  const validTransitions: Record<string, string[]> = {
    "preparing": ["ready", "cancelled"],
    "ready": ["shipped", "preparing", "cancelled"],
    "shipped": ["in_transit", "delivered", "exception"],
    "in_transit": ["out_for_delivery", "delivered", "exception"],
    "out_for_delivery": ["delivered", "exception"],
    "delivered": [], // Final state - no transitions allowed
    "cancelled": [], // Final state - no transitions allowed
    "exception": ["preparing", "ready", "shipped", "in_transit", "cancelled"] // Can recover from exception
  }

  const allowedTransitions = validTransitions[currentStatus || "preparing"] || []
  return allowedTransitions.includes(newStatus)
}
