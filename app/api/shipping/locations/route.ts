/**
 * Manufacturing ERP - Shipping Locations API
 * 
 * Professional API endpoints for shipping location management with
 * capacity validation and optimization features.
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { ShippingLocationService } from "@/lib/services/shipping-location-service"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const getLocationsSchema = z.object({
  shipping_method: z.enum(['sea_freight', 'air_freight', 'express', 'truck']).optional(),
  include_utilization: z.boolean().default(true),
  include_capacity: z.boolean().default(true)
})

// ============================================================================
// API ENDPOINTS
// ============================================================================

/**
 * GET /api/shipping/locations
 * Get available shipping locations with capacity and utilization info
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse and validate query parameters
    const queryParams = {
      shipping_method: searchParams.get("shipping_method") || undefined,
      include_utilization: searchParams.get("include_utilization") !== "false",
      include_capacity: searchParams.get("include_capacity") !== "false"
    }

    const validated = getLocationsSchema.parse(queryParams)
    const shippingLocationService = new ShippingLocationService()

    // Get available shipping locations
    const locations = validated.shipping_method
      ? await shippingLocationService.getAvailableShippingLocations(
          validated.shipping_method,
          context.companyId
        )
      : await shippingLocationService.getAvailableShippingLocations(
          'sea_freight', // default method to get all locations
          context.companyId
        )

    // Filter and format response based on requested includes
    const formattedLocations = locations.map(location => ({
      locationId: location.locationId,
      locationName: location.locationName,
      locationType: location.locationType,
      icon: location.icon,
      shippingMethods: location.shippingMethods,
      attributes: location.attributes,
      
      // Conditional includes
      ...(validated.include_capacity && {
        capacity: location.capacity,
        availableCapacity: location.availableCapacity,
        utilizationPercentage: location.utilizationPercentage
      }),
      
      ...(validated.include_utilization && {
        estimatedProcessingTime: location.estimatedProcessingTime,
        costFactor: location.costFactor,
        status: location.utilizationPercentage >= 90 ? 'critical' :
                location.utilizationPercentage >= 75 ? 'warning' : 'normal'
      })
    }))

    return jsonOk({
      locations: formattedLocations,
      total: formattedLocations.length,
      filters: {
        shipping_method: validated.shipping_method,
        include_utilization: validated.include_utilization,
        include_capacity: validated.include_capacity
      },
      metadata: {
        timestamp: new Date().toISOString(),
        company_id: context.companyId
      }
    })

  } catch (error) {
    console.error("Shipping Locations API Error:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid query parameters", 400, {
        validation_errors: error.errors
      })
    }
    
    return jsonError("Failed to fetch shipping locations", 500)
  }
})
