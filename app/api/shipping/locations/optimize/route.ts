/**
 * Manufacturing ERP - Shipping Location Optimization API
 * 
 * Professional API endpoint for optimizing pickup locations based on
 * inventory distribution and shipping requirements.
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { ShippingLocationService } from "@/lib/services/shipping-location-service"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const optimizeLocationSchema = z.object({
  shipping_method: z.enum(['sea_freight', 'air_freight', 'express', 'truck']),
  product_allocations: z.array(z.object({
    product_id: z.string().min(1, "Product ID is required"),
    quantity: z.number().min(1, "Quantity must be positive"),
    current_location_id: z.string().min(1, "Current location ID is required"),
    weight: z.number().optional(),
    volume: z.number().optional()
  })).min(1, "At least one product allocation is required"),
  preferences: z.object({
    prioritize_cost: z.boolean().default(false),
    prioritize_speed: z.boolean().default(false),
    prioritize_capacity: z.boolean().default(true),
    max_processing_time: z.number().optional(), // in hours
    required_security_level: z.enum(['low', 'medium', 'high', 'restricted']).optional()
  }).optional()
})

// ============================================================================
// API ENDPOINTS
// ============================================================================

/**
 * POST /api/shipping/locations/optimize
 * Optimize pickup location based on inventory distribution and requirements
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()
    const validated = optimizeLocationSchema.parse(body)
    
    const shippingLocationService = new ShippingLocationService()

    // Convert to service format
    const productAllocations = validated.product_allocations.map(allocation => ({
      productId: allocation.product_id,
      quantity: allocation.quantity,
      currentLocationId: allocation.current_location_id,
      weight: allocation.weight,
      volume: allocation.volume
    }))

    // Get optimization recommendation
    const optimization = await shippingLocationService.optimizePickupLocation(
      productAllocations,
      validated.shipping_method,
      context.companyId
    )

    // Apply user preferences to adjust recommendation
    let finalRecommendation = optimization
    if (validated.preferences) {
      finalRecommendation = await applyOptimizationPreferences(
        optimization,
        validated.preferences,
        shippingLocationService,
        validated.shipping_method,
        context.companyId
      )
    }

    return jsonOk({
      optimization: {
        recommended_location: {
          location_id: finalRecommendation.recommendedLocationId,
          location_name: finalRecommendation.locationName,
          optimization_score: Math.round(finalRecommendation.optimizationScore * 10) / 10,
          reasons: finalRecommendation.reasons
        },
        estimated_savings: finalRecommendation.estimatedSavings,
        alternative_locations: finalRecommendation.alternativeLocations.map(alt => ({
          location_id: alt.locationId,
          location_name: alt.locationName,
          available_capacity: alt.availableCapacity,
          estimated_processing_time: alt.estimatedProcessingTime,
          cost_factor: alt.costFactor
        })),
        analysis: {
          total_products: validated.product_allocations.length,
          total_quantity: validated.product_allocations.reduce((sum, p) => sum + p.quantity, 0),
          shipping_method: validated.shipping_method,
          optimization_criteria: validated.preferences || {
            prioritize_capacity: true
          }
        }
      },
      metadata: {
        timestamp: new Date().toISOString(),
        company_id: context.companyId,
        optimization_version: "1.0"
      }
    })

  } catch (error) {
    console.error("Location Optimization API Error:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid request data", 400, {
        validation_errors: error.errors
      })
    }
    
    if (error instanceof Error) {
      return jsonError(error.message, 400)
    }
    
    return jsonError("Failed to optimize location", 500)
  }
})

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

async function applyOptimizationPreferences(
  baseOptimization: any,
  preferences: any,
  service: ShippingLocationService,
  shippingMethod: string,
  companyId: string
) {
  // If user has specific preferences, we might need to re-rank alternatives
  let alternatives = [
    {
      locationId: baseOptimization.recommendedLocationId,
      locationName: baseOptimization.locationName,
      score: baseOptimization.optimizationScore
    },
    ...baseOptimization.alternativeLocations.map((alt: any) => ({
      locationId: alt.locationId,
      locationName: alt.locationName,
      score: 0 // Will be recalculated
    }))
  ]

  // Get detailed info for all locations to re-score
  const allLocations = await service.getAvailableShippingLocations(shippingMethod, companyId)
  
  // Re-score based on preferences
  for (let alt of alternatives) {
    const locationDetail = allLocations.find(loc => loc.locationId === alt.locationId)
    if (!locationDetail) continue

    let score = 0
    let factors = 0

    if (preferences.prioritize_cost) {
      score += (2 - locationDetail.costFactor) * 50 // Lower cost factor = higher score
      factors++
    }

    if (preferences.prioritize_speed) {
      score += Math.max(0, 100 - locationDetail.estimatedProcessingTime * 10)
      factors++
    }

    if (preferences.prioritize_capacity) {
      score += (locationDetail.availableCapacity / locationDetail.capacity) * 100
      factors++
    }

    // Apply constraints
    if (preferences.max_processing_time && 
        locationDetail.estimatedProcessingTime > preferences.max_processing_time) {
      score *= 0.5 // Penalty for exceeding max processing time
    }

    if (preferences.required_security_level) {
      const securityLevels = { low: 1, medium: 2, high: 3, restricted: 4 }
      const requiredLevel = securityLevels[preferences.required_security_level]
      const locationLevel = securityLevels[locationDetail.attributes.securityLevel as keyof typeof securityLevels] || 1
      
      if (locationLevel < requiredLevel) {
        score = 0 // Disqualify if security level is insufficient
      }
    }

    alt.score = factors > 0 ? score / factors : score
  }

  // Sort by new scores
  alternatives.sort((a, b) => b.score - a.score)
  const newRecommendation = alternatives[0]

  return {
    ...baseOptimization,
    recommendedLocationId: newRecommendation.locationId,
    locationName: newRecommendation.locationName,
    optimizationScore: newRecommendation.score,
    alternativeLocations: baseOptimization.alternativeLocations.filter(
      (alt: any) => alt.locationId !== newRecommendation.locationId
    )
  }
}
