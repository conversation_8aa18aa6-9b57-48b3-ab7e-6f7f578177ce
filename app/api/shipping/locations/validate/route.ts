/**
 * Manufacturing ERP - Shipping Capacity Validation API
 * 
 * Professional API endpoint for validating shipping capacity at staging areas
 * and providing alternative location suggestions.
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { ShippingLocationService } from "@/lib/services/shipping-location-service"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const validateCapacitySchema = z.object({
  location_id: z.string().min(1, "Location ID is required"),
  required_capacity: z.number().min(1, "Required capacity must be positive"),
  shipment_volume: z.number().optional(),
  shipping_method: z.enum(['sea_freight', 'air_freight', 'express', 'truck']).optional(),
  include_alternatives: z.boolean().default(true),
  max_alternatives: z.number().min(1).max(10).default(3)
})

const batchValidateSchema = z.object({
  validations: z.array(z.object({
    location_id: z.string().min(1, "Location ID is required"),
    required_capacity: z.number().min(1, "Required capacity must be positive"),
    shipment_id: z.string().optional() // For tracking purposes
  })).min(1, "At least one validation is required").max(20, "Maximum 20 validations per batch")
})

// ============================================================================
// API ENDPOINTS
// ============================================================================

/**
 * POST /api/shipping/locations/validate
 * Validate shipping capacity for a specific location
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()
    const validated = validateCapacitySchema.parse(body)
    
    const shippingLocationService = new ShippingLocationService()

    // Validate capacity at the specified location
    const validation = await shippingLocationService.validateShippingCapacity(
      validated.location_id,
      validated.required_capacity,
      context.companyId
    )

    // Get alternative locations if requested and current location can't accommodate
    let alternatives: any[] = []
    if (validated.include_alternatives && !validation.canAccommodate) {
      const shippingMethod = validated.shipping_method || 'sea_freight'
      const availableLocations = await shippingLocationService.getAvailableShippingLocations(
        shippingMethod,
        context.companyId
      )

      alternatives = availableLocations
        .filter(loc => 
          loc.locationId !== validated.location_id && 
          loc.availableCapacity >= validated.required_capacity
        )
        .slice(0, validated.max_alternatives)
        .map(loc => ({
          location_id: loc.locationId,
          location_name: loc.locationName,
          available_capacity: loc.availableCapacity,
          utilization_percentage: loc.utilizationPercentage,
          estimated_processing_time: loc.estimatedProcessingTime,
          cost_factor: loc.costFactor,
          attributes: {
            dock_type: loc.attributes.dockType,
            security_level: loc.attributes.securityLevel,
            temperature_controlled: loc.attributes.temperatureControlled
          }
        }))
    }

    return jsonOk({
      validation: {
        location_id: validated.location_id,
        is_valid: validation.isValid,
        can_accommodate: validation.canAccommodate,
        available_capacity: validation.availableCapacity,
        utilization_after: Math.round(validation.utilizationAfter * 10) / 10,
        status: validation.status,
        message: validation.message
      },
      alternatives: alternatives,
      recommendations: {
        should_use_alternative: !validation.canAccommodate && alternatives.length > 0,
        best_alternative: alternatives.length > 0 ? alternatives[0] : null,
        capacity_warning: validation.status === 'warning' || validation.status === 'critical'
      },
      metadata: {
        timestamp: new Date().toISOString(),
        company_id: context.companyId,
        required_capacity: validated.required_capacity
      }
    })

  } catch (error) {
    console.error("Capacity Validation API Error:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid request data", 400, {
        validation_errors: error.errors
      })
    }
    
    if (error instanceof Error) {
      return jsonError(error.message, 400)
    }
    
    return jsonError("Failed to validate capacity", 500)
  }
})

/**
 * POST /api/shipping/locations/validate/batch
 * Batch validate capacity for multiple locations
 */
export const PUT = withTenantAuth(async function PUT(request: NextRequest, context) {
  try {
    const body = await request.json()
    const validated = batchValidateSchema.parse(body)
    
    const shippingLocationService = new ShippingLocationService()
    const results = []

    // Process each validation
    for (const validation of validated.validations) {
      try {
        const result = await shippingLocationService.validateShippingCapacity(
          validation.location_id,
          validation.required_capacity,
          context.companyId
        )

        results.push({
          location_id: validation.location_id,
          shipment_id: validation.shipment_id,
          validation: {
            is_valid: result.isValid,
            can_accommodate: result.canAccommodate,
            available_capacity: result.availableCapacity,
            utilization_after: Math.round(result.utilizationAfter * 10) / 10,
            status: result.status,
            message: result.message
          },
          success: true
        })

      } catch (error) {
        results.push({
          location_id: validation.location_id,
          shipment_id: validation.shipment_id,
          validation: null,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Calculate summary statistics
    const successful = results.filter(r => r.success)
    const canAccommodateCount = successful.filter(r => r.validation?.can_accommodate).length
    const warningCount = successful.filter(r => r.validation?.status === 'warning' || r.validation?.status === 'critical').length

    return jsonOk({
      results: results,
      summary: {
        total_validations: validated.validations.length,
        successful_validations: successful.length,
        failed_validations: results.length - successful.length,
        can_accommodate: canAccommodateCount,
        capacity_warnings: warningCount,
        success_rate: Math.round((successful.length / validated.validations.length) * 100)
      },
      metadata: {
        timestamp: new Date().toISOString(),
        company_id: context.companyId,
        batch_size: validated.validations.length
      }
    })

  } catch (error) {
    console.error("Batch Capacity Validation API Error:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid request data", 400, {
        validation_errors: error.errors
      })
    }
    
    return jsonError("Failed to process batch validation", 500)
  }
})
