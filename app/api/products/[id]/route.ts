import { db } from "@/lib/db"
import { json<PERSON>rror, json<PERSON>k } from "@/lib/api-helpers"
import { products } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"
import { withTenantAuth, createForbiddenResponse } from "@/lib/tenant-utils"

const patchSchema = z.object({
  name: z.string().min(2).optional(),
  sku: z.string().min(2).optional(),
  unit: z.string().min(1).optional(),
  hs_code: z.string().optional(),
  origin: z.string().optional(),
  package: z.string().optional(),
  image: z.string().optional(),

  // ✅ PRICING FIELDS: Enhanced product pricing system
  base_price: z.string().optional(),
  cost_price: z.string().optional(),
  margin_percentage: z.string().optional(),
  currency: z.string().max(10).optional(),

  // Quality Requirements
  inspection_required: z.string().optional(),
  quality_tolerance: z.string().max(255).optional(),
  quality_notes: z.string().max(1000).optional(),
})

// 🛡️ SECURE: Multi-tenant PATCH endpoint with proper isolation
export const PATCH = withTenantAuth(async function PATCH(req: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await req.json()
    const data = patchSchema.parse(body)

    // 🛡️ CRITICAL: Verify product belongs to current company before updating
    const existingProduct = await db.query.products.findFirst({
      where: and(
        eq(products.id, id),
        eq(products.company_id, context.companyId)
      ),
    })

    if (!existingProduct) {
      return createForbiddenResponse()
    }

    // ✅ PRICING ENHANCEMENT: Update price_updated_at when pricing fields change
    const updateData = {
      ...data,
      ...(data.base_price || data.cost_price || data.margin_percentage ?
        { price_updated_at: new Date() } : {})
    }

    const updated = await db.update(products)
      .set(updateData)
      .where(and(
        eq(products.id, id),
        eq(products.company_id, context.companyId) // 🛡️ Double-check tenant isolation
      ))
      .returning()

    return jsonOk(updated[0])
  } catch (e) {
    return jsonError(e)
  }
})

// 🛡️ SECURE: Multi-tenant DELETE endpoint with proper isolation
export const DELETE = withTenantAuth(async function DELETE(_: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    // 🛡️ CRITICAL: Verify product belongs to current company before deleting
    const existingProduct = await db.query.products.findFirst({
      where: and(
        eq(products.id, id),
        eq(products.company_id, context.companyId)
      ),
    })

    if (!existingProduct) {
      return createForbiddenResponse()
    }

    await db.delete(products).where(and(
      eq(products.id, id),
      eq(products.company_id, context.companyId) // 🛡️ Double-check tenant isolation
    ))

    return new Response(null, { status: 204 })
  } catch (e: any) {
    // Handle foreign key constraint errors specifically
    if (e.message && e.message.includes("FOREIGN KEY constraint failed")) {
      return new Response(
        JSON.stringify({
          error: "Cannot delete product: it is referenced by existing contracts, work orders, or inventory records. Please remove all references first.",
          code: "FOREIGN_KEY_CONSTRAINT"
        }),
        {
          status: 409, // Conflict
          headers: { "Content-Type": "application/json" }
        }
      )
    }
    return jsonError(e)
  }
})
