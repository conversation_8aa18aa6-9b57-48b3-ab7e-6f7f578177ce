/**
 * Manufacturing ERP - Individual BOM Item API
 * CRUD operations for individual BOM items
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { billOfMaterials, products, rawMaterials } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"

// ✅ VALIDATION SCHEMA
const updateBomItemSchema = z.object({
  qty_required: z.string().min(1, "Quantity required is required").optional(),
  unit: z.string().min(1, "Unit is required").optional(),
  waste_factor: z.string().optional(),
  status: z.enum(["active", "inactive"]).optional(),
})

// ✅ GET - Get individual BOM item
export const GET = withTenantAuth(async function GET(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string; bomId: string }> }
) {
  try {
    const { id: productId, bomId } = await params

    const bomItem = await db.query.billOfMaterials.findFirst({
      where: and(
        eq(billOfMaterials.id, bomId),
        eq(billOfMaterials.product_id, productId),
        eq(billOfMaterials.company_id, context.companyId)
      ),
      with: {
        product: true,
        rawMaterial: {
          with: {
            primarySupplier: true,
          },
        },
      },
    })

    if (!bomItem) {
      return jsonError("BOM item not found", 404)
    }

    return jsonOk(bomItem)
  } catch (error) {
    console.error("BOM Item GET Error:", error)
    return jsonError("Failed to fetch BOM item", 500)
  }
})

// ✅ PATCH - Update BOM item
export const PATCH = withTenantAuth(async function PATCH(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string; bomId: string }> }
) {
  try {
    const { id: productId, bomId } = await params
    const body = await request.json()
    const validatedData = updateBomItemSchema.parse(body)

    // Check if BOM item exists
    const existingBomItem = await db.query.billOfMaterials.findFirst({
      where: and(
        eq(billOfMaterials.id, bomId),
        eq(billOfMaterials.product_id, productId),
        eq(billOfMaterials.company_id, context.companyId)
      ),
    })

    if (!existingBomItem) {
      return jsonError("BOM item not found", 404)
    }

    // Update BOM item
    await db
      .update(billOfMaterials)
      .set({
        ...validatedData,
        updated_at: new Date(),
      })
      .where(eq(billOfMaterials.id, bomId))

    // Fetch updated BOM item with relationships
    const updatedBomItem = await db.query.billOfMaterials.findFirst({
      where: eq(billOfMaterials.id, bomId),
      with: {
        product: true,
        rawMaterial: {
          with: {
            primarySupplier: true,
          },
        },
      },
    })

    return jsonOk(updatedBomItem)
  } catch (error) {
    console.error("BOM Item PATCH Error:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }
    
    return jsonError("Failed to update BOM item", 500)
  }
})

// ✅ DELETE - Delete BOM item
export const DELETE = withTenantAuth(async function DELETE(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string; bomId: string }> }
) {
  try {
    const { id: productId, bomId } = await params

    // Check if BOM item exists
    const existingBomItem = await db.query.billOfMaterials.findFirst({
      where: and(
        eq(billOfMaterials.id, bomId),
        eq(billOfMaterials.product_id, productId),
        eq(billOfMaterials.company_id, context.companyId)
      ),
    })

    if (!existingBomItem) {
      return jsonError("BOM item not found", 404)
    }

    // Delete BOM item
    await db.delete(billOfMaterials).where(eq(billOfMaterials.id, bomId))

    return jsonOk({ message: "BOM item deleted successfully" })
  } catch (error) {
    console.error("BOM Item DELETE Error:", error)
    return jsonError("Failed to delete BOM item", 500)
  }
})
