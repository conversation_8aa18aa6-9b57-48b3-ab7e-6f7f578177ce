/**
 * Manufacturing ERP - Bill of Materials (BOM) API
 * Manage product-material relationships for production planning
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db, uid } from "@/lib/db"
import { billOfMaterials, products, rawMaterials } from "@/lib/schema-postgres"
import { eq, and, desc } from "drizzle-orm"
import { z } from "zod"

// ✅ VALIDATION SCHEMA
const bomItemSchema = z.object({
  raw_material_id: z.string().min(1, "Raw material is required"),
  qty_required: z.string().min(1, "Quantity required is required"),
  unit: z.string().min(1, "Unit is required"),
  waste_factor: z.string().default("0.05"), // Default 5% waste
})

const bomBatchSchema = z.object({
  items: z.array(bomItemSchema).min(1, "At least one BOM item is required"),
})

// ✅ GET - Get BOM for a product
export const GET = withTenantAuth(async function GET(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await params

    // Verify product exists and belongs to company
    const product = await db.query.products.findFirst({
      where: and(
        eq(products.id, productId),
        eq(products.company_id, context.companyId)
      ),
    })

    if (!product) {
      return jsonError("Product not found", 404)
    }

    // Get BOM items with material details
    const bomItems = await db.query.billOfMaterials.findMany({
      where: and(
        eq(billOfMaterials.product_id, productId),
        eq(billOfMaterials.company_id, context.companyId)
      ),
      orderBy: [desc(billOfMaterials.created_at)],
      with: {
        rawMaterial: {
          with: {
            primarySupplier: true,
          },
        },
      },
    })

    // Calculate BOM summary
    const totalItems = bomItems.length
    const activeItems = bomItems.filter(item => item.status === "active").length
    const estimatedCost = bomItems.reduce((sum, item) => {
      if (item.status === "active" && item.rawMaterial?.standard_cost) {
        const qty = parseFloat(item.qty_required)
        const wasteFactor = parseFloat(item.waste_factor || "0.05")
        const totalQty = qty * (1 + wasteFactor)
        const cost = parseFloat(item.rawMaterial.standard_cost)
        return sum + (totalQty * cost)
      }
      return sum
    }, 0)

    return jsonOk({
      product: {
        id: product.id,
        name: product.name,
        sku: product.sku,
      },
      bomItems,
      summary: {
        totalItems,
        activeItems,
        estimatedCost,
      },
    })
  } catch (error) {
    console.error("BOM GET Error:", error)
    return jsonError("Failed to fetch BOM", 500)
  }
})

// ✅ POST - Add BOM item to product
export const POST = withTenantAuth(async function POST(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await params
    const body = await request.json()
    const validatedData = bomItemSchema.parse(body)

    // Verify product exists and belongs to company
    const product = await db.query.products.findFirst({
      where: and(
        eq(products.id, productId),
        eq(products.company_id, context.companyId)
      ),
    })

    if (!product) {
      return jsonError("Product not found", 404)
    }

    // Verify raw material exists and belongs to company
    const rawMaterial = await db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, validatedData.raw_material_id),
        eq(rawMaterials.company_id, context.companyId)
      ),
    })

    if (!rawMaterial) {
      return jsonError("Raw material not found", 400)
    }

    // Check if BOM item already exists
    const existingBomItem = await db.query.billOfMaterials.findFirst({
      where: and(
        eq(billOfMaterials.product_id, productId),
        eq(billOfMaterials.raw_material_id, validatedData.raw_material_id),
        eq(billOfMaterials.company_id, context.companyId)
      ),
    })

    if (existingBomItem) {
      return jsonError("BOM item for this material already exists", 400)
    }

    // Create BOM item
    const newBomItem = {
      id: uid("bom"),
      company_id: context.companyId,
      product_id: productId,
      ...validatedData,
    }

    await db.insert(billOfMaterials).values(newBomItem)

    // Fetch created BOM item with relationships
    const createdBomItem = await db.query.billOfMaterials.findFirst({
      where: eq(billOfMaterials.id, newBomItem.id),
      with: {
        rawMaterial: {
          with: {
            primarySupplier: true,
          },
        },
      },
    })

    return jsonOk(createdBomItem, { status: 201 })
  } catch (error) {
    console.error("BOM POST Error:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    return jsonError("Failed to create BOM item", 500)
  }
})

// ✅ PUT - Batch update BOM (replace all items)
export const PUT = withTenantAuth(async function PUT(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await params
    const body = await request.json()
    const validatedData = bomBatchSchema.parse(body)

    // Verify product exists and belongs to company
    const product = await db.query.products.findFirst({
      where: and(
        eq(products.id, productId),
        eq(products.company_id, context.companyId)
      ),
    })

    if (!product) {
      return jsonError("Product not found", 404)
    }

    // Validate all raw materials exist
    for (const item of validatedData.items) {
      const rawMaterial = await db.query.rawMaterials.findFirst({
        where: and(
          eq(rawMaterials.id, item.raw_material_id),
          eq(rawMaterials.company_id, context.companyId)
        ),
      })

      if (!rawMaterial) {
        return jsonError(`Raw material ${item.raw_material_id} not found`, 400)
      }
    }

    // Execute batch update in transaction
    await db.transaction(async (tx) => {
      // Delete existing BOM items
      await tx.delete(billOfMaterials)
        .where(and(
          eq(billOfMaterials.product_id, productId),
          eq(billOfMaterials.company_id, context.companyId)
        ))

      // Insert new BOM items
      const newBomItems = validatedData.items.map(item => ({
        id: uid("bom"),
        company_id: context.companyId,
        product_id: productId,
        ...item,
      }))

      if (newBomItems.length > 0) {
        await tx.insert(billOfMaterials).values(newBomItems)
      }
    })

    // Fetch updated BOM
    const updatedBomItems = await db.query.billOfMaterials.findMany({
      where: and(
        eq(billOfMaterials.product_id, productId),
        eq(billOfMaterials.company_id, context.companyId)
      ),
      with: {
        rawMaterial: {
          with: {
            primarySupplier: true,
          },
        },
      },
    })

    return jsonOk({
      message: "BOM updated successfully",
      bomItems: updatedBomItems,
    })
  } catch (error) {
    console.error("BOM PUT Error:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    return jsonError("Failed to update BOM", 500)
  }
})

// ✅ DELETE - Clear all BOM items for product
export const DELETE = withTenantAuth(async function DELETE(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await params

    // Verify product exists and belongs to company
    const product = await db.query.products.findFirst({
      where: and(
        eq(products.id, productId),
        eq(products.company_id, context.companyId)
      ),
    })

    if (!product) {
      return jsonError("Product not found", 404)
    }

    // Delete all BOM items for this product
    await db.delete(billOfMaterials)
      .where(and(
        eq(billOfMaterials.product_id, productId),
        eq(billOfMaterials.company_id, context.companyId)
      ))

    return jsonOk({ message: "BOM cleared successfully" })
  } catch (error) {
    console.error("BOM DELETE Error:", error)
    return jsonError("Failed to clear BOM", 500)
  }
})
