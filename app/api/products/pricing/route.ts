/**
 * Manufacturing ERP - Product Pricing API
 * 
 * Dedicated API endpoints for managing product pricing with professional
 * validation, audit trails, and multi-tenant security.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Product Pricing Enhancement
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db, uid } from "@/lib/db"
import { products, productPricingHistory } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"

// ✅ VALIDATION SCHEMAS
const bulkPricingUpdateSchema = z.object({
  updates: z.array(z.object({
    product_id: z.string().min(1, "Product ID is required"),
    base_price: z.string().optional(),
    cost_price: z.string().optional(),
    margin_percentage: z.string().optional(),
    currency: z.string().max(10).optional(),
    change_reason: z.string().optional().default("manual"),
    change_notes: z.string().optional(),
  })).min(1, "At least one product update is required")
})

const singlePricingUpdateSchema = z.object({
  product_id: z.string().min(1, "Product ID is required"),
  base_price: z.string().optional(),
  cost_price: z.string().optional(),
  margin_percentage: z.string().optional(),
  currency: z.string().max(10).optional(),
  change_reason: z.string().optional().default("manual"),
  change_notes: z.string().optional(),
})

/**
 * ✅ GET /api/products/pricing - Get pricing information for all products
 * 
 * Returns comprehensive pricing data including base prices, cost prices,
 * margins, and pricing history for analytics and reporting.
 * 
 * @param request - HTTP request
 * @returns Product pricing information
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    console.log(`💰 Fetching product pricing for company: ${context.companyId}`)

    // ✅ FETCH ALL PRODUCTS WITH PRICING DATA
    const productsWithPricing = await db.query.products.findMany({
      where: eq(products.company_id, context.companyId),
      columns: {
        id: true,
        sku: true,
        name: true,
        unit: true,
        category: true,
        price: true, // Legacy price field
        base_price: true,
        cost_price: true,
        margin_percentage: true,
        currency: true,
        price_updated_at: true,
        created_at: true,
      },
      orderBy: [products.name],
    })

    // ✅ CALCULATE PRICING ANALYTICS
    const pricingAnalytics = {
      totalProducts: productsWithPricing.length,
      productsWithPricing: productsWithPricing.filter(p => p.base_price || p.cost_price).length,
      productsWithoutPricing: productsWithPricing.filter(p => !p.base_price && !p.cost_price).length,
      averageMargin: 0,
      currencies: [...new Set(productsWithPricing.map(p => p.currency).filter(Boolean))],
    }

    // Calculate average margin
    const productsWithMargin = productsWithPricing.filter(p => p.margin_percentage)
    if (productsWithMargin.length > 0) {
      const totalMargin = productsWithMargin.reduce((sum, p) => 
        sum + parseFloat(p.margin_percentage || '0'), 0)
      pricingAnalytics.averageMargin = Math.round((totalMargin / productsWithMargin.length) * 100) / 100
    }

    return jsonOk({
      products: productsWithPricing,
      analytics: pricingAnalytics,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Product pricing API error:", error)
    return jsonError("Failed to fetch product pricing", 500)
  }
})

/**
 * ✅ POST /api/products/pricing - Update product pricing (single or bulk)
 * 
 * Supports both single product pricing updates and bulk updates with
 * comprehensive validation, audit trails, and pricing history tracking.
 * 
 * @param request - HTTP request with pricing update data
 * @returns Updated pricing information
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()
    
    // ✅ DETERMINE UPDATE TYPE (single vs bulk)
    const isBulkUpdate = Array.isArray(body.updates)
    const validationSchema = isBulkUpdate ? bulkPricingUpdateSchema : singlePricingUpdateSchema
    
    const validatedData = validationSchema.parse(isBulkUpdate ? body : { updates: [body] })
    const updates = isBulkUpdate ? validatedData.updates : [body]

    console.log(`💰 Processing ${updates.length} pricing updates for company: ${context.companyId}`)

    const results = []
    const auditEntries = []

    // ✅ PROCESS EACH PRICING UPDATE
    for (const update of updates) {
      // Verify product exists and belongs to company
      const existingProduct = await db.query.products.findFirst({
        where: and(
          eq(products.id, update.product_id),
          eq(products.company_id, context.companyId)
        ),
      })

      if (!existingProduct) {
        return jsonError(`Product not found: ${update.product_id}`, 404)
      }

      // ✅ PREPARE UPDATE DATA
      const updateData: any = {
        price_updated_at: new Date(),
      }

      if (update.base_price !== undefined) updateData.base_price = update.base_price
      if (update.cost_price !== undefined) updateData.cost_price = update.cost_price
      if (update.margin_percentage !== undefined) updateData.margin_percentage = update.margin_percentage
      if (update.currency !== undefined) updateData.currency = update.currency

      // ✅ UPDATE PRODUCT PRICING
      const updatedProduct = await db.update(products)
        .set(updateData)
        .where(and(
          eq(products.id, update.product_id),
          eq(products.company_id, context.companyId)
        ))
        .returning()

      results.push(updatedProduct[0])

      // ✅ CREATE AUDIT TRAIL ENTRY
      auditEntries.push({
        id: uid("ph"),
        company_id: context.companyId,
        product_id: update.product_id,
        old_price: existingProduct.price,
        new_price: updateData.base_price || existingProduct.price,
        old_base_price: existingProduct.base_price,
        new_base_price: updateData.base_price || existingProduct.base_price,
        old_cost_price: existingProduct.cost_price,
        new_cost_price: updateData.cost_price || existingProduct.cost_price,
        old_margin_percentage: existingProduct.margin_percentage,
        new_margin_percentage: updateData.margin_percentage || existingProduct.margin_percentage,
        change_reason: update.change_reason || "manual",
        changed_by: context.userId,
        change_notes: update.change_notes,
      })
    }

    // ✅ BULK INSERT AUDIT ENTRIES
    if (auditEntries.length > 0) {
      await db.insert(productPricingHistory).values(auditEntries)
    }

    return jsonOk({
      success: true,
      updated: results.length,
      products: results,
      message: `Successfully updated pricing for ${results.length} product(s)`,
    }, { status: 200 })

  } catch (error) {
    console.error("Product pricing update error:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }
    
    return jsonError("Failed to update product pricing", 500)
  }
})
