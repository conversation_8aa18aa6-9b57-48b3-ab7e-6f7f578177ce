import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { products } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { productSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    console.log("🔍 PRODUCTS API: Starting query for company:", context.companyId)

    const rows = await db.query.products.findMany({
      where: eq(products.company_id, context.companyId),
      orderBy: [desc(products.created_at)],
    })

    console.log("✅ PRODUCTS API: Query successful, found", rows.length, "products")
    return jsonOk(rows)
  } catch (e) {
    console.error("❌ PRODUCTS API: Query failed:", e)
    return jsonError(e)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  try {
    console.log("🔍 PRODUCTS API: Request received - margin validation removed")

    // Validate request body
    const validation = await validateRequestBody(req, productSchema)
    console.log("✅ PRODUCTS API: Validation result:", validation.success ? "SUCCESS" : "FAILED")

    if (!validation.success) {
      console.error("❌ PRODUCTS API: Validation errors:", validation.error.issues)
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    console.log("✅ PRODUCTS API: Validated data received")
    const id = uid("prod")

    // Map validated data to database schema with company_id for tenant isolation
    const newProduct = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      sku: body.sku,
      name: body.name,
      unit: body.unit,
      hs_code: body.hs_code,
      origin: body.origin,
      package: body.package,
      image: body.image,

      // ✅ PRICING FIELDS: Enhanced product pricing system
      base_price: body.base_price,
      cost_price: body.cost_price,
      margin_percentage: body.margin_percentage,
      currency: body.currency || "USD",
      price_updated_at: body.base_price || body.cost_price ? new Date() : null,

      // Quality Requirements
      inspection_required: body.inspection_required || "false",
      quality_tolerance: body.quality_tolerance,
      quality_notes: body.quality_notes,
    }

    await db.insert(products).values(newProduct)

    // 🛡️ SECURE: Only return product if it belongs to current company
    const row = await db.query.products.findFirst({
      where: and(
        eq(products.id, id),
        eq(products.company_id, context.companyId)
      ),
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    console.error("💥 PRODUCTS API: Unexpected error:", e)
    return jsonError("Failed to create product", 500, { error: e instanceof Error ? e.message : String(e) })
  }
})
