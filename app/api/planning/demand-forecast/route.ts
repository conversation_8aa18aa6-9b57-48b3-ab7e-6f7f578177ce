/**
 * Manufacturing ERP - Demand Forecast API Endpoints
 * 
 * Professional API endpoints for demand forecasting with enterprise-grade validation
 * Implements multi-tenant security and comprehensive error handling
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import {
  DemandForecastingService,
  createDemandForecastSchema,
  type DemandForecastContext
} from "@/lib/services/demand-forecasting"
import { z } from "zod"

// ✅ PROFESSIONAL: Query parameter validation schema
const listDemandForecastsSchema = z.object({
  productId: z.string().optional(),
  forecastPeriod: z.string().optional(),
  approvalStatus: z.enum(["draft", "pending", "approved", "rejected"]).optional(),
  forecastMethod: z.enum(["pipeline", "historical", "manual", "hybrid"]).optional(),
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
})

/**
 * ✅ GET /api/planning/demand-forecast
 * List demand forecasts with filtering and pagination
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())

    // Validate query parameters
    const validatedParams = listDemandForecastsSchema.parse(queryParams)

    // Initialize service
    const forecastingService = new DemandForecastingService()

    // Build filters
    const filters: any = {}
    if (validatedParams.productId) filters.productId = validatedParams.productId
    if (validatedParams.forecastPeriod) filters.forecastPeriod = validatedParams.forecastPeriod
    if (validatedParams.approvalStatus) filters.approvalStatus = validatedParams.approvalStatus
    if (validatedParams.forecastMethod) filters.forecastMethod = validatedParams.forecastMethod

    // Get demand forecasts
    const forecasts = await forecastingService.listDemandForecasts(
      context.companyId,
      filters
    )

    // Apply pagination (simple implementation)
    const page = validatedParams.page || 1
    const limit = validatedParams.limit || 50
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedForecasts = forecasts.slice(startIndex, endIndex)

    return jsonOk({
      forecasts: paginatedForecasts,
      pagination: {
        page,
        limit,
        total: forecasts.length,
        totalPages: Math.ceil(forecasts.length / limit),
      },
      filters: validatedParams,
    })
  } catch (error) {
    console.error("Error listing demand forecasts:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Invalid query parameters", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to retrieve demand forecasts",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/demand-forecast
 * Create new demand forecast
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()

    // Validate request body
    const validatedData = createDemandForecastSchema.parse(body)

    // Initialize service
    const forecastingService = new DemandForecastingService()

    // Create forecast context
    const forecastContext: DemandForecastContext = {
      companyId: context.companyId,
      userId: context.userId,
      productId: validatedData.productId,
      forecastPeriod: validatedData.forecastPeriod,
      forecastMethod: validatedData.forecastMethod,
    }

    // Generate forecast based on method
    let forecast
    if (validatedData.forecastMethod === "pipeline") {
      // For pipeline method, check if user provided specific demand
      if (validatedData.forecastedDemand && validatedData.forecastedDemand > 0) {
        // User provided manual demand, create manual forecast
        forecast = await forecastingService.createManualForecast(
          forecastContext,
          validatedData.forecastedDemand,
          validatedData.confidenceLevel,
          validatedData.notes,
          validatedData.preferredSupplierId
        )
      } else {
        // Pure pipeline analysis
        forecast = await forecastingService.generatePipelineForecast(forecastContext, validatedData.preferredSupplierId)
      }
    } else if (validatedData.forecastMethod === "manual") {
      // Manual method always uses user input
      forecast = await forecastingService.createManualForecast(
        forecastContext,
        validatedData.forecastedDemand,
        validatedData.confidenceLevel,
        validatedData.notes,
        validatedData.preferredSupplierId
      )
    } else {
      // For historical/hybrid methods, we'll implement later
      return jsonError("Forecast method not yet implemented", 400, {
        supportedMethods: ["pipeline", "manual"],
        requestedMethod: validatedData.forecastMethod,
      })
    }

    return jsonOk(forecast, { status: 201 })
  } catch (error) {
    console.error("Error creating demand forecast:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to create demand forecast",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/demand-forecast/generate-pipeline
 * Generate demand forecast from sales pipeline analysis
 */
export const generatePipelineForecast = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()

    // Validate request body
    const schema = z.object({
      productId: z.string().min(1, "Product ID is required"),
      forecastPeriod: z.string().min(1, "Forecast period is required"),
      notes: z.string().optional(),
    })

    const validatedData = schema.parse(body)

    // Initialize service
    const forecastingService = new DemandForecastingService()

    // Create forecast context
    const forecastContext: DemandForecastContext = {
      companyId: context.companyId,
      userId: context.userId,
      productId: validatedData.productId,
      forecastPeriod: validatedData.forecastPeriod,
      forecastMethod: "pipeline",
    }

    // Generate pipeline-based forecast
    const forecast = await forecastingService.generatePipelineForecast(forecastContext)

    // If notes provided, update the forecast
    if (validatedData.notes) {
      // TODO: Implement forecast update method
      forecast.notes = validatedData.notes
    }

    return jsonOk({
      forecast,
      message: "Pipeline forecast generated successfully",
      analysisDetails: {
        method: "pipeline",
        dataSource: "sales_contracts",
        confidenceLevel: forecast.confidenceLevel,
        forecastPeriod: forecast.forecastPeriod,
      },
    }, { status: 201 })
  } catch (error) {
    console.error("Error generating pipeline forecast:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to generate pipeline forecast",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/demand-forecast/bulk-generate
 * Generate forecasts for multiple products
 */
export const bulkGenerateForecasts = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()

    // Validate request body
    const schema = z.object({
      productIds: z.array(z.string()).min(1, "At least one product ID is required"),
      forecastPeriod: z.string().min(1, "Forecast period is required"),
      forecastMethod: z.enum(["pipeline", "historical", "manual", "hybrid"]).default("pipeline"),
    })

    const validatedData = schema.parse(body)

    // Initialize service
    const forecastingService = new DemandForecastingService()

    // Generate forecasts for each product
    const results = []
    const errors = []

    for (const productId of validatedData.productIds) {
      try {
        const forecastContext: DemandForecastContext = {
          companyId: context.companyId,
          userId: context.userId,
          productId,
          forecastPeriod: validatedData.forecastPeriod,
          forecastMethod: validatedData.forecastMethod,
        }

        if (validatedData.forecastMethod === "pipeline") {
          const forecast = await forecastingService.generatePipelineForecast(forecastContext)
          results.push({
            productId,
            forecast,
            status: "success",
          })
        } else {
          errors.push({
            productId,
            error: "Forecast method not yet implemented",
            status: "error",
          })
        }
      } catch (error) {
        errors.push({
          productId,
          error: error instanceof Error ? error.message : "Unknown error",
          status: "error",
        })
      }
    }

    return jsonOk({
      results,
      errors,
      summary: {
        total: validatedData.productIds.length,
        successful: results.length,
        failed: errors.length,
      },
    })
  } catch (error) {
    console.error("Error bulk generating forecasts:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to bulk generate forecasts",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})
