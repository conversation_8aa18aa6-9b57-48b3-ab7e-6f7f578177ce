/**
 * Manufacturing ERP - Material Requirements API Endpoint
 * 
 * Professional API endpoint for material requirements calculation from demand forecasts
 * Implements BOM explosion with waste factors and container optimization
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { DemandForecastingService } from "@/lib/services/demand-forecasting"
import { z } from "zod"

// ✅ PROFESSIONAL: Query parameter validation schema
const materialRequirementsQuerySchema = z.object({
  includeAvailability: z.string().optional().transform(val => val === "true"),
  includeProcurementPlan: z.string().optional().transform(val => val === "true"),
  containerOptimization: z.string().optional().transform(val => val === "true"),
  targetDate: z.string().optional(),
})

/**
 * ✅ GET /api/planning/demand-forecast/[id]/material-requirements
 * Get material requirements for a specific demand forecast
 */
export const GET = withTenantAuth(async function GET(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    // Validate query parameters
    const validatedParams = materialRequirementsQuerySchema.parse(queryParams)
    
    // Initialize service
    const forecastingService = new DemandForecastingService()
    
    // Get forecast details first
    const forecast = await forecastingService.getDemandForecastById(
      context.companyId,
      id
    )
    
    // Calculate material requirements from BOM explosion
    const materialRequirements = await forecastingService.explodeForecastToBOM(
      context.companyId,
      id
    )
    
    // Prepare response data
    const response: any = {
      forecast: {
        id: forecast.id,
        productName: forecast.productName,
        productSku: forecast.productSku,
        forecastedDemand: forecast.forecastedDemand,
        forecastPeriod: forecast.forecastPeriod,
        confidenceLevel: forecast.confidenceLevel,
      },
      materialRequirements,
      summary: {
        totalMaterials: materialRequirements.length,
        totalRequiredValue: 0, // TODO: Calculate based on material costs
        highPriorityItems: materialRequirements.filter(req => req.priority === "high" || req.priority === "urgent").length,
        targetDate: validatedParams.targetDate || materialRequirements[0]?.targetDate,
      },
    }
    
    // Add availability information if requested
    if (validatedParams.includeAvailability) {
      // TODO: Implement availability checking
      response.availability = {
        message: "Availability checking not yet implemented",
        note: "This feature will be implemented in the next iteration",
      }
    }
    
    // Add procurement plan if requested
    if (validatedParams.includeProcurementPlan) {
      // TODO: Implement procurement plan generation
      response.procurementPlan = {
        message: "Procurement plan generation not yet implemented",
        note: "This feature will be implemented in the next iteration",
      }
    }
    
    // Add container optimization if requested
    if (validatedParams.containerOptimization) {
      // TODO: Implement container optimization
      response.containerOptimization = {
        message: "Container optimization not yet implemented",
        note: "This feature will be implemented in the next iteration",
      }
    }
    
    return jsonOk(response)
  } catch (error) {
    console.error("Error calculating material requirements:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid query parameters", 400, {
        validationErrors: error.errors,
      })
    }
    
    if (error instanceof Error && error.message.includes("not found")) {
      return jsonError("Demand forecast not found", 404)
    }
    
    if (error instanceof Error && error.message.includes("No BOM found")) {
      return jsonError("No Bill of Materials found for this product", 400, {
        message: "Cannot calculate material requirements without a BOM",
        suggestion: "Please create a Bill of Materials for this product first",
      })
    }
    
    return jsonError(
      "Failed to calculate material requirements",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/demand-forecast/[id]/material-requirements/generate-procurement
 * Generate procurement plan from material requirements
 */
export const generateProcurementPlan = withTenantAuth(async function POST(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    
    // Validate request body
    const schema = z.object({
      targetDate: z.string().optional(),
      priority: z.enum(["low", "normal", "high", "urgent"]).default("normal"),
      containerOptimization: z.boolean().default(true),
      notes: z.string().optional(),
    })
    
    const validatedData = schema.parse(body)
    
    // Initialize service
    const forecastingService = new DemandForecastingService()
    
    // Get forecast and material requirements
    const forecast = await forecastingService.getDemandForecastById(
      context.companyId,
      id
    )
    
    const materialRequirements = await forecastingService.explodeForecastToBOM(
      context.companyId,
      id
    )
    
    // TODO: Implement procurement plan generation service
    // For now, return a structured response showing what would be generated
    const procurementPlan = {
      forecastId: id,
      generatedAt: new Date().toISOString(),
      targetDate: validatedData.targetDate || materialRequirements[0]?.targetDate,
      priority: validatedData.priority,
      containerOptimization: validatedData.containerOptimization,
      materials: materialRequirements.map(req => ({
        rawMaterialId: req.rawMaterialId,
        materialName: req.materialName,
        materialSku: req.materialSku,
        requiredQuantity: req.totalRequiredQty,
        unit: req.unit,
        priority: req.priority,
        targetDate: req.targetDate,
        // TODO: Add supplier information, lead times, costs
        status: "planned",
      })),
      summary: {
        totalMaterials: materialRequirements.length,
        estimatedCost: 0, // TODO: Calculate based on supplier prices
        estimatedLeadTime: "14-30 days", // TODO: Calculate based on supplier lead times
      },
      notes: validatedData.notes,
    }
    
    return jsonOk({
      procurementPlan,
      message: "Procurement plan structure generated (implementation pending)",
      nextSteps: [
        "Implement ProcurementPlanningService",
        "Add supplier lead time integration",
        "Add cost calculation logic",
        "Add container optimization algorithms",
      ],
    }, { status: 201 })
  } catch (error) {
    console.error("Error generating procurement plan:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }
    
    if (error instanceof Error && error.message.includes("not found")) {
      return jsonError("Demand forecast not found", 404)
    }
    
    return jsonError(
      "Failed to generate procurement plan",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ GET /api/planning/demand-forecast/[id]/material-requirements/availability
 * Check material availability for forecast requirements
 */
export const checkMaterialAvailability = withTenantAuth(async function GET(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    // Initialize service
    const forecastingService = new DemandForecastingService()
    
    // Get material requirements
    const materialRequirements = await forecastingService.explodeForecastToBOM(
      context.companyId,
      id
    )
    
    // TODO: Implement availability checking against current inventory
    // For now, return a structured response showing what would be checked
    const availabilityCheck = {
      forecastId: id,
      checkedAt: new Date().toISOString(),
      materials: materialRequirements.map(req => ({
        rawMaterialId: req.rawMaterialId,
        materialName: req.materialName,
        materialSku: req.materialSku,
        requiredQuantity: req.totalRequiredQty,
        unit: req.unit,
        // TODO: Add actual availability data
        availableQuantity: 0,
        shortfall: req.totalRequiredQty,
        status: "insufficient",
        locations: [],
        nextDelivery: null,
      })),
      summary: {
        totalMaterials: materialRequirements.length,
        availableMaterials: 0,
        insufficientMaterials: materialRequirements.length,
        criticalShortfalls: materialRequirements.filter(req => req.priority === "high" || req.priority === "urgent").length,
      },
    }
    
    return jsonOk({
      availabilityCheck,
      message: "Availability checking structure generated (implementation pending)",
      nextSteps: [
        "Integrate with inventory management system",
        "Add real-time stock level checking",
        "Add location-based availability",
        "Add supplier delivery schedule integration",
      ],
    })
  } catch (error) {
    console.error("Error checking material availability:", error)
    
    if (error instanceof Error && error.message.includes("not found")) {
      return jsonError("Demand forecast not found", 404)
    }
    
    return jsonError(
      "Failed to check material availability",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})
