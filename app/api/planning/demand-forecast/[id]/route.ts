/**
 * Manufacturing ERP - Individual Demand Forecast API Endpoints
 * 
 * Professional API endpoints for individual demand forecast operations
 * Implements multi-tenant security and comprehensive error handling
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import {
  DemandForecastingService,
  updateDemandForecastSchema
} from "@/lib/services/demand-forecasting"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { z } from "zod"

/**
 * ✅ GET /api/planning/demand-forecast/[id]
 * Get specific demand forecast with details
 */
export const GET = withTenantAuth(async function GET(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Initialize service
    const forecastingService = new DemandForecastingService()

    // Get forecast details
    const forecast = await forecastingService.getDemandForecastById(
      context.companyId,
      id
    )

    return jsonOk({
      forecast,
      metadata: {
        canEdit: forecast.approvalStatus === "draft",
        canApprove: forecast.approvalStatus === "pending",
        canDelete: forecast.approvalStatus === "draft",
      },
    })
  } catch (error) {
    console.error("Error retrieving demand forecast:", error)

    if (error instanceof Error && error.message.includes("not found")) {
      return jsonError("Demand forecast not found", 404)
    }

    return jsonError(
      "Failed to retrieve demand forecast",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ PATCH /api/planning/demand-forecast/[id]
 * Update demand forecast
 */
export const PATCH = withTenantAuth(async function PATCH(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // Validate request body
    const validatedData = updateDemandForecastSchema.parse(body)

    // Initialize service
    const forecastingService = new DemandForecastingService()

    // Check if forecast exists and can be updated
    const existingForecast = await forecastingService.getDemandForecastById(
      context.companyId,
      id
    )

    // Business rule: Only draft forecasts can be updated
    if (existingForecast.approvalStatus !== "draft") {
      return jsonError(
        "Cannot update forecast that is not in draft status",
        400,
        {
          currentStatus: existingForecast.approvalStatus,
          allowedStatus: "draft"
        }
      )
    }

    // Update the forecast
    const updatedForecast = await forecastingService.updateDemandForecast(
      context.companyId,
      id,
      validatedData
    )

    // If status changed to approved, trigger procurement plan generation
    if (validatedData.approvalStatus === "approved" && existingForecast.approvalStatus !== "approved") {
      try {
        const procurementService = new ProcurementPlanningService()
        await procurementService.generateProcurementPlansFromForecast(
          context.companyId,
          id,
          context.userId,
          {
            containerOptimization: true,
            maxLeadTime: 60,
          }
        )
      } catch (procurementError) {
        console.error("Error generating procurement plans:", procurementError)
        // Don't fail the update if procurement generation fails
      }
    }

    return jsonOk({
      forecast: updatedForecast,
      message: "Demand forecast updated successfully",
    })
  } catch (error) {
    console.error("Error updating demand forecast:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    if (error instanceof Error && error.message.includes("not found")) {
      return jsonError("Demand forecast not found", 404)
    }

    return jsonError(
      "Failed to update demand forecast",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ DELETE /api/planning/demand-forecast/[id]
 * Delete demand forecast (only if in draft status)
 */
export const DELETE = withTenantAuth(async function DELETE(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Initialize service
    const forecastingService = new DemandForecastingService()

    // Check if forecast exists and can be deleted
    const existingForecast = await forecastingService.getDemandForecastById(
      context.companyId,
      id
    )

    // Business rule: Only draft forecasts can be deleted
    if (existingForecast.approvalStatus !== "draft") {
      return jsonError(
        "Cannot delete forecast that is not in draft status",
        400,
        {
          currentStatus: existingForecast.approvalStatus,
          allowedStatus: "draft"
        }
      )
    }

    // Delete the forecast
    await forecastingService.deleteDemandForecast(context.companyId, id)

    return jsonOk({
      message: "Demand forecast deleted successfully",
      deletedId: id
    })
  } catch (error) {
    console.error("Error deleting demand forecast:", error)

    if (error instanceof Error && error.message.includes("not found")) {
      return jsonError("Demand forecast not found", 404)
    }

    return jsonError(
      "Failed to delete demand forecast",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/demand-forecast/[id]/approve
 * Approve demand forecast
 */
export const approveForecast = withTenantAuth(async function POST(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // Validate request body
    const schema = z.object({
      notes: z.string().optional(),
    })

    const validatedData = schema.parse(body)

    // Initialize service
    const forecastingService = new DemandForecastingService()

    // Check if forecast exists and can be approved
    const existingForecast = await forecastingService.getDemandForecastById(
      context.companyId,
      id
    )

    // Business rule: Only pending forecasts can be approved
    if (existingForecast.approvalStatus !== "pending") {
      return jsonError(
        "Cannot approve forecast that is not in pending status",
        400,
        {
          currentStatus: existingForecast.approvalStatus,
          allowedStatus: "pending"
        }
      )
    }

    // TODO: Implement forecast approval method in service
    // For now, we'll return a placeholder response
    return jsonError(
      "Forecast approval functionality not yet implemented",
      501,
      {
        message: "This feature will be implemented in the next iteration",
        forecastId: id,
        approvalNotes: validatedData.notes
      }
    )
  } catch (error) {
    console.error("Error approving demand forecast:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    if (error instanceof Error && error.message.includes("not found")) {
      return jsonError("Demand forecast not found", 404)
    }

    return jsonError(
      "Failed to approve demand forecast",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/demand-forecast/[id]/reject
 * Reject demand forecast
 */
export const rejectForecast = withTenantAuth(async function POST(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // Validate request body
    const schema = z.object({
      rejectionReason: z.string().min(1, "Rejection reason is required"),
      notes: z.string().optional(),
    })

    const validatedData = schema.parse(body)

    // Initialize service
    const forecastingService = new DemandForecastingService()

    // Check if forecast exists and can be rejected
    const existingForecast = await forecastingService.getDemandForecastById(
      context.companyId,
      id
    )

    // Business rule: Only pending forecasts can be rejected
    if (existingForecast.approvalStatus !== "pending") {
      return jsonError(
        "Cannot reject forecast that is not in pending status",
        400,
        {
          currentStatus: existingForecast.approvalStatus,
          allowedStatus: "pending"
        }
      )
    }

    // TODO: Implement forecast rejection method in service
    // For now, we'll return a placeholder response
    return jsonError(
      "Forecast rejection functionality not yet implemented",
      501,
      {
        message: "This feature will be implemented in the next iteration",
        forecastId: id,
        rejectionReason: validatedData.rejectionReason,
        rejectionNotes: validatedData.notes
      }
    )
  } catch (error) {
    console.error("Error rejecting demand forecast:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    if (error instanceof Error && error.message.includes("not found")) {
      return jsonError("Demand forecast not found", 404)
    }

    return jsonError(
      "Failed to reject demand forecast",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})
