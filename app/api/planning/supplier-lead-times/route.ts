/**
 * Manufacturing ERP - Supplier Lead Time API Endpoints
 * 
 * Professional API endpoints for supplier lead time management
 * Implements multi-tenant security and comprehensive error handling
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { 
  SupplierLeadTimeService,
  createSupplierLeadTimeSchema 
} from "@/lib/services/supplier-lead-time"
import { z } from "zod"

// ✅ PROFESSIONAL: Query parameter validation schema
const listSupplierLeadTimesSchema = z.object({
  supplierId: z.string().optional(),
  rawMaterialId: z.string().optional(),
  reliability: z.enum(["excellent", "good", "fair", "poor"]).optional(),
  minLeadTime: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  maxLeadTime: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
})

/**
 * ✅ GET /api/planning/supplier-lead-times
 * List supplier lead times with filtering and pagination
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    // Validate query parameters
    const validatedParams = listSupplierLeadTimesSchema.parse(queryParams)
    
    // Initialize service
    const leadTimeService = new SupplierLeadTimeService()
    
    // Build filters
    const filters: any = {}
    if (validatedParams.supplierId) filters.supplierId = validatedParams.supplierId
    if (validatedParams.rawMaterialId) filters.rawMaterialId = validatedParams.rawMaterialId
    if (validatedParams.reliability) filters.reliability = validatedParams.reliability
    if (validatedParams.minLeadTime) filters.minLeadTime = validatedParams.minLeadTime
    if (validatedParams.maxLeadTime) filters.maxLeadTime = validatedParams.maxLeadTime
    
    // Get supplier lead times
    const leadTimes = await leadTimeService.listSupplierLeadTimes(
      context.companyId,
      filters
    )
    
    // Apply pagination (simple implementation)
    const page = validatedParams.page || 1
    const limit = validatedParams.limit || 50
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedLeadTimes = leadTimes.slice(startIndex, endIndex)
    
    return jsonOk({
      leadTimes: paginatedLeadTimes,
      pagination: {
        page,
        limit,
        total: leadTimes.length,
        totalPages: Math.ceil(leadTimes.length / limit),
      },
      filters: validatedParams,
    })
  } catch (error) {
    console.error("Error listing supplier lead times:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid query parameters", 400, {
        validationErrors: error.errors,
      })
    }
    
    return jsonError(
      "Failed to retrieve supplier lead times",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/supplier-lead-times
 * Create new supplier lead time record
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()
    
    // Validate request body
    const validatedData = createSupplierLeadTimeSchema.parse(body)
    
    // Initialize service
    const leadTimeService = new SupplierLeadTimeService()
    
    // Create supplier lead time record
    const leadTime = await leadTimeService.createSupplierLeadTime(
      context.companyId,
      validatedData,
      context.userId
    )
    
    return jsonOk(leadTime, { status: 201 })
  } catch (error) {
    console.error("Error creating supplier lead time:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }
    
    return jsonError(
      "Failed to create supplier lead time",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ GET /api/planning/supplier-lead-times/performance-report
 * Generate supplier performance report
 */
export const getPerformanceReport = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    const supplierId = searchParams.get("supplierId") || undefined
    
    // Initialize service
    const leadTimeService = new SupplierLeadTimeService()
    
    // Generate performance report
    const performanceReport = await leadTimeService.generateSupplierPerformanceReport(
      context.companyId,
      supplierId
    )
    
    // Categorize suppliers by rating
    const categorized = {
      excellent: performanceReport.filter(r => r.overallRating === "excellent"),
      good: performanceReport.filter(r => r.overallRating === "good"),
      fair: performanceReport.filter(r => r.overallRating === "fair"),
      poor: performanceReport.filter(r => r.overallRating === "poor"),
    }
    
    return jsonOk({
      performanceReport,
      categorized,
      summary: {
        totalSuppliers: performanceReport.length,
        excellent: categorized.excellent.length,
        good: categorized.good.length,
        fair: categorized.fair.length,
        poor: categorized.poor.length,
        averageOnTimeRate: performanceReport.reduce((sum, r) => sum + r.onTimeDeliveryRate, 0) / performanceReport.length,
        averageLeadTime: performanceReport.reduce((sum, r) => sum + r.averageLeadTime, 0) / performanceReport.length,
      },
    })
  } catch (error) {
    console.error("Error generating supplier performance report:", error)
    
    return jsonError(
      "Failed to generate supplier performance report",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ GET /api/planning/supplier-lead-times/best-suppliers
 * Get best suppliers for a specific material
 */
export const getBestSuppliers = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Validate query parameters
    const schema = z.object({
      rawMaterialId: z.string().min(1, "Raw material ID is required"),
      requiredQty: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
    })
    
    const validatedParams = schema.parse(Object.fromEntries(searchParams.entries()))
    
    // Initialize service
    const leadTimeService = new SupplierLeadTimeService()
    
    // Get best suppliers for material
    const bestSuppliers = await leadTimeService.getBestSuppliersForMaterial(
      context.companyId,
      validatedParams.rawMaterialId,
      validatedParams.requiredQty
    )
    
    return jsonOk({
      bestSuppliers,
      criteria: {
        rawMaterialId: validatedParams.rawMaterialId,
        requiredQty: validatedParams.requiredQty,
        sortedBy: ["reliability", "performance_score", "lead_time"],
      },
      summary: {
        totalSuppliers: bestSuppliers.length,
        averageLeadTime: bestSuppliers.reduce((sum, s) => sum + s.leadTimeDays, 0) / bestSuppliers.length,
        bestReliability: bestSuppliers[0]?.reliability || "none",
      },
    })
  } catch (error) {
    console.error("Error getting best suppliers:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid query parameters", 400, {
        validationErrors: error.errors,
      })
    }
    
    return jsonError(
      "Failed to get best suppliers",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/supplier-lead-times/bulk-import
 * Bulk import supplier lead time data
 */
export const bulkImportLeadTimes = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()
    
    // Validate request body
    const schema = z.object({
      leadTimes: z.array(createSupplierLeadTimeSchema).min(1, "At least one lead time record is required"),
      overwriteExisting: z.boolean().default(false),
    })
    
    const validatedData = schema.parse(body)
    
    // Initialize service
    const leadTimeService = new SupplierLeadTimeService()
    
    // Process bulk import
    const results = []
    const errors = []
    
    for (const leadTimeData of validatedData.leadTimes) {
      try {
        const leadTime = await leadTimeService.createSupplierLeadTime(
          context.companyId,
          leadTimeData,
          context.userId
        )
        results.push({
          supplierId: leadTimeData.supplierId,
          rawMaterialId: leadTimeData.rawMaterialId,
          leadTime,
          status: "success",
        })
      } catch (error) {
        errors.push({
          supplierId: leadTimeData.supplierId,
          rawMaterialId: leadTimeData.rawMaterialId,
          error: error instanceof Error ? error.message : "Unknown error",
          status: "error",
        })
      }
    }
    
    return jsonOk({
      results,
      errors,
      summary: {
        total: validatedData.leadTimes.length,
        successful: results.length,
        failed: errors.length,
      },
    })
  } catch (error) {
    console.error("Error bulk importing lead times:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }
    
    return jsonError(
      "Failed to bulk import lead times",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})
