/**
 * Manufacturing ERP - Refresh Procurement Plan Priorities
 * 
 * Temporary API endpoint to update existing procurement plan priorities
 * using the new unified priority calculation system
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { procurementPlans } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"

export const POST = withTenantAuth(async function POST(req, context) {
  try {
    // Get all procurement plans for this company
    const plans = await db.query.procurementPlans.findMany({
      where: eq(procurementPlans.company_id, context.companyId),
      with: {
        demandForecast: true,
      },
    })

    let updatedCount = 0

    for (const plan of plans) {
      // Calculate days until target date
      const targetDate = new Date(plan.target_date)
      const daysUntilNeeded = Math.ceil((targetDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
      
      // Standard lead time (30 days for default supplier)
      const leadTime = parseInt(plan.estimated_lead_time || "30")
      
      // Determine new priority based on unified logic
      let newPriority: "low" | "normal" | "high" | "urgent"
      if (daysUntilNeeded <= leadTime * 0.8) {
        newPriority = "urgent"
      } else if (daysUntilNeeded <= leadTime) {
        newPriority = "high" 
      } else if (daysUntilNeeded <= leadTime * 1.5) {
        newPriority = "normal"
      } else {
        newPriority = "low"
      }

      // Adjust based on forecast confidence if available
      if (plan.demandForecast) {
        const confidenceLevel = plan.demandForecast.confidence_level
        if (confidenceLevel === "high") {
          // High confidence - increase urgency by one level
          if (newPriority === "low") newPriority = "normal"
          else if (newPriority === "normal") newPriority = "high"
          else if (newPriority === "high") newPriority = "urgent"
        } else if (confidenceLevel === "low") {
          // Low confidence - decrease urgency by one level
          if (newPriority === "urgent") newPriority = "high"
          else if (newPriority === "high") newPriority = "normal"
          else if (newPriority === "normal") newPriority = "low"
        }
      }

      // Update if priority changed
      if (plan.priority !== newPriority) {
        await db.update(procurementPlans)
          .set({ 
            priority: newPriority,
            updated_at: new Date(),
          })
          .where(and(
            eq(procurementPlans.id, plan.id),
            eq(procurementPlans.company_id, context.companyId)
          ))
        
        updatedCount++
      }
    }

    return jsonOk({
      message: `Successfully updated ${updatedCount} procurement plan priorities`,
      updatedCount,
      totalPlans: plans.length,
    })

  } catch (error) {
    console.error("Error refreshing priorities:", error)
    return jsonError("Failed to refresh priorities", 500)
  }
})
