/**
 * Manufacturing ERP - Individual Procurement Plan API Endpoints
 * 
 * Professional API endpoints for individual procurement plan operations
 * Implements multi-tenant security and comprehensive error handling
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { NextRequest } from "next/server"
import { revalidatePath } from "next/cache"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import {
  ProcurementPlanningService,
  updateProcurementPlanSchema
} from "@/lib/services/procurement-planning"
import { z } from "zod"

/**
 * ✅ GET /api/planning/procurement/[id]
 * Get specific procurement plan with details
 */
export const GET = withTenantAuth(async function GET(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Initialize service
    const procurementService = new ProcurementPlanningService()

    // Get procurement plan details
    const plan = await procurementService.getProcurementPlanById(
      context.companyId,
      id
    )

    if (!plan) {
      return jsonError("Procurement plan not found", 404)
    }

    return jsonOk({
      plan,
      metadata: {
        canEdit: plan.status === "draft",
        canApprove: plan.status === "pending",
        canOrder: plan.status === "approved",
        canReceive: plan.status === "ordered",
        canDelete: plan.status === "draft",
      },
    })
  } catch (error) {
    console.error("Error retrieving procurement plan:", error)

    return jsonError(
      "Failed to retrieve procurement plan",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ PATCH /api/planning/procurement/[id]
 * Update procurement plan
 */
export const PATCH = withTenantAuth(async function PATCH(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    console.log(`🔄 Procurement Plan Update Request:`, {
      planId: id,
      companyId: context.companyId,
      userId: context.userId,
      updateData: body,
      timestamp: new Date().toISOString(),
    })

    // Validate request body
    const validatedData = updateProcurementPlanSchema.parse(body)

    // Initialize service
    const procurementService = new ProcurementPlanningService()

    // Check if plan exists and can be updated
    const existingPlan = await procurementService.getProcurementPlanById(
      context.companyId,
      id
    )

    if (!existingPlan) {
      console.error(`❌ Procurement plan not found: ${id}`)
      return jsonError("Procurement plan not found", 404)
    }

    console.log(`📋 Existing Plan Status: ${existingPlan.status} → Requested: ${validatedData.status || 'no change'}`)

    // ✅ AUDIT TRAIL: Log status changes
    if (validatedData.status && validatedData.status !== existingPlan.status) {
      console.log(`🔄 Procurement Plan Status Change:`, {
        planId: id,
        companyId: context.companyId,
        userId: context.userId,
        oldStatus: existingPlan.status,
        newStatus: validatedData.status,
        timestamp: new Date().toISOString(),
      })
    }

    // Update the procurement plan using the service
    const updatedPlan = await procurementService.updateProcurementPlan(
      context.companyId,
      id,
      {
        ...validatedData,
        approvedBy: validatedData.status === "approved" ? context.userId : undefined
      }
    )

    console.log(`✅ Procurement plan updated successfully: ${id}`)

    // ✅ PROFESSIONAL ERP: Revalidate planning pages to refresh KPIs
    revalidatePath("/planning")
    revalidatePath("/planning/procurement")
    revalidatePath(`/planning/procurement/${id}`)

    return jsonOk({
      plan: updatedPlan,
      message: "Procurement plan updated successfully",
    })
  } catch (error) {
    console.error("❌ Error updating procurement plan:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to update procurement plan",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ DELETE /api/planning/procurement/[id]
 * Delete procurement plan (only if in draft status)
 */
export const DELETE = withTenantAuth(async function DELETE(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Initialize service
    const procurementService = new ProcurementPlanningService()

    // Check if plan exists and can be deleted
    const existingPlan = await procurementService.getProcurementPlanById(
      context.companyId,
      id
    )

    if (!existingPlan) {
      return jsonError("Procurement plan not found", 404)
    }

    // Business rule: Only draft plans can be deleted
    if (existingPlan.status !== "draft") {
      return jsonError(
        "Cannot delete procurement plan that is not in draft status",
        400,
        {
          currentStatus: existingPlan.status,
          allowedStatus: "draft"
        }
      )
    }

    // TODO: Implement procurement plan deletion method in service
    // For now, we'll return a placeholder response
    return jsonError(
      "Procurement plan deletion functionality not yet implemented",
      501,
      {
        message: "This feature will be implemented in the next iteration",
        planId: id
      }
    )
  } catch (error) {
    console.error("Error deleting procurement plan:", error)

    return jsonError(
      "Failed to delete procurement plan",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/procurement/[id]/approve
 * Approve procurement plan
 */
export const approvePlan = withTenantAuth(async function POST(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // Validate request body
    const schema = z.object({
      approvalNotes: z.string().optional(),
    })

    const validatedData = schema.parse(body)

    // Initialize service
    const procurementService = new ProcurementPlanningService()

    // Check if plan exists and can be approved
    const existingPlan = await procurementService.getProcurementPlanById(
      context.companyId,
      id
    )

    if (!existingPlan) {
      return jsonError("Procurement plan not found", 404)
    }

    // Business rule: Only pending plans can be approved
    if (existingPlan.status !== "pending") {
      return jsonError(
        "Cannot approve procurement plan that is not in pending status",
        400,
        {
          currentStatus: existingPlan.status,
          allowedStatus: "pending"
        }
      )
    }

    // TODO: Implement procurement plan approval method in service
    // For now, we'll return a placeholder response
    return jsonError(
      "Procurement plan approval functionality not yet implemented",
      501,
      {
        message: "This feature will be implemented in the next iteration",
        planId: id,
        approvalNotes: validatedData.approvalNotes
      }
    )
  } catch (error) {
    console.error("Error approving procurement plan:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to approve procurement plan",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/procurement/[id]/order
 * Create purchase order from approved procurement plan
 */
export const createPurchaseOrder = withTenantAuth(async function POST(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // Validate request body
    const schema = z.object({
      orderNotes: z.string().optional(),
      adjustedQty: z.number().optional(),
      adjustedTargetDate: z.string().optional(),
    })

    const validatedData = schema.parse(body)

    // Initialize service
    const procurementService = new ProcurementPlanningService()

    // Check if plan exists and can be ordered
    const existingPlan = await procurementService.getProcurementPlanById(
      context.companyId,
      id
    )

    if (!existingPlan) {
      return jsonError("Procurement plan not found", 404)
    }

    // Business rule: Only approved plans can be ordered
    if (existingPlan.status !== "approved") {
      return jsonError(
        "Cannot create purchase order from plan that is not approved",
        400,
        {
          currentStatus: existingPlan.status,
          allowedStatus: "approved"
        }
      )
    }

    // TODO: Implement purchase order creation from procurement plan
    // This would integrate with the existing purchase contract system
    const purchaseOrderPreview = {
      supplierId: existingPlan.supplierId,
      supplierName: existingPlan.supplierName,
      items: [{
        rawMaterialId: existingPlan.rawMaterialId,
        materialName: existingPlan.materialName,
        quantity: validatedData.adjustedQty || existingPlan.plannedQty,
        estimatedCost: existingPlan.estimatedCost,
      }],
      targetDate: validatedData.adjustedTargetDate || existingPlan.targetDate,
      containerOptimization: existingPlan.containerOptimization,
      notes: validatedData.orderNotes,
      createdFrom: `Procurement Plan ${id}`,
    }

    return jsonOk({
      purchaseOrderPreview,
      message: "Purchase order creation functionality not yet implemented",
      nextSteps: [
        "Integrate with existing purchase contract system",
        "Implement automatic purchase contract generation",
        "Add supplier notification system",
        "Update procurement plan status to 'ordered'",
      ],
    }, { status: 201 })
  } catch (error) {
    console.error("Error creating purchase order:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to create purchase order",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})
