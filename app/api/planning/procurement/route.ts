/**
 * Manufacturing ERP - Procurement Planning API Endpoints
 * 
 * Professional API endpoints for procurement planning with supplier optimization
 * Implements multi-tenant security and comprehensive error handling
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { NextRequest } from "next/server"
import { revalidatePath } from "next/cache"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import {
  ProcurementPlanningService,
  createProcurementPlanSchema
} from "@/lib/services/procurement-planning"
import { z } from "zod"

// ✅ PROFESSIONAL: Query parameter validation schema
const listProcurementPlansSchema = z.object({
  status: z.enum(["draft", "pending", "approved", "ordered", "received"]).optional(),
  priority: z.enum(["low", "normal", "high", "urgent"]).optional(),
  supplierId: z.string().optional(),
  rawMaterialId: z.string().optional(),
  demandForecastId: z.string().optional(),
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
})

/**
 * ✅ GET /api/planning/procurement
 * List procurement plans with filtering and pagination
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())

    // Validate query parameters
    const validatedParams = listProcurementPlansSchema.parse(queryParams)

    // Initialize service
    const procurementService = new ProcurementPlanningService()

    // Build filters
    const filters: any = {}
    if (validatedParams.status) filters.status = validatedParams.status
    if (validatedParams.priority) filters.priority = validatedParams.priority
    if (validatedParams.supplierId) filters.supplierId = validatedParams.supplierId
    if (validatedParams.rawMaterialId) filters.rawMaterialId = validatedParams.rawMaterialId
    if (validatedParams.demandForecastId) filters.demandForecastId = validatedParams.demandForecastId

    // Get procurement plans
    const plans = await procurementService.listProcurementPlans(
      context.companyId,
      filters
    )

    // Apply pagination (simple implementation)
    const page = validatedParams.page || 1
    const limit = validatedParams.limit || 50
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedPlans = plans.slice(startIndex, endIndex)

    return jsonOk({
      plans: paginatedPlans,
      pagination: {
        page,
        limit,
        total: plans.length,
        totalPages: Math.ceil(plans.length / limit),
      },
      filters: validatedParams,
    })
  } catch (error) {
    console.error("Error listing procurement plans:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Invalid query parameters", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to retrieve procurement plans",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/procurement
 * Create new procurement plan or handle bulk operations
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()

    // Check if this is a bulk approve request
    if (body.action === "bulk-approve") {
      // Validate request body for bulk approval
      const schema = z.object({
        action: z.literal("bulk-approve"),
        planIds: z.array(z.string()).min(1, "At least one plan ID is required"),
        approvalNotes: z.string().optional(),
      })

      const validatedData = schema.parse(body)

      // Initialize service
      const procurementService = new ProcurementPlanningService()

      // Perform bulk approval
      const result = await procurementService.bulkApproveProcurementPlans(
        context.companyId,
        validatedData.planIds,
        context.userId,
        validatedData.approvalNotes
      )

      // ✅ PROFESSIONAL ERP: Revalidate planning pages to refresh KPIs after bulk approval
      revalidatePath("/planning")
      revalidatePath("/planning/procurement")

      return jsonOk({
        approved: result.approved,
        failed: result.failed,
        summary: {
          totalPlans: validatedData.planIds.length,
          approved: result.approved.length,
          failed: result.failed.length,
        },
        message: `Successfully approved ${result.approved.length} of ${validatedData.planIds.length} procurement plans`,
      })
    }

    // Regular procurement plan creation
    const validatedData = createProcurementPlanSchema.parse(body)

    // Initialize service
    const procurementService = new ProcurementPlanningService()

    // TODO: Implement individual procurement plan creation
    // For now, return a structured response showing what would be created
    const planPreview = {
      companyId: context.companyId,
      rawMaterialId: validatedData.rawMaterialId,
      demandForecastId: validatedData.demandForecastId,
      plannedQty: validatedData.plannedQty,
      targetDate: validatedData.targetDate,
      supplierId: validatedData.supplierId,
      priority: validatedData.priority,
      notes: validatedData.notes,
      status: "draft",
      createdBy: context.userId,
    }

    return jsonOk({
      planPreview,
      message: "Individual procurement plan creation not yet implemented",
      recommendation: "Use /api/planning/procurement/generate-from-forecast for automated plan generation",
    }, { status: 201 })
  } catch (error) {
    console.error("Error processing procurement request:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to process procurement request",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ POST /api/planning/procurement/generate-from-forecast
 * Generate procurement plans from demand forecast
 */
export const generateFromForecast = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()

    // Validate request body
    const schema = z.object({
      demandForecastId: z.string().min(1, "Demand forecast ID is required"),
      containerOptimization: z.boolean().default(true),
      preferredSuppliers: z.array(z.string()).optional(),
      maxLeadTime: z.number().optional(),
    })

    const validatedData = schema.parse(body)

    // Initialize service
    const procurementService = new ProcurementPlanningService()

    // Generate procurement plans
    const procurementPlans = await procurementService.generateProcurementPlansFromForecast(
      context.companyId,
      validatedData.demandForecastId,
      context.userId,
      {
        containerOptimization: validatedData.containerOptimization,
        preferredSuppliers: validatedData.preferredSuppliers,
        maxLeadTime: validatedData.maxLeadTime,
      }
    )

    return jsonOk({
      procurementPlans,
      summary: {
        totalPlans: procurementPlans.length,
        totalMaterials: procurementPlans.length,
        estimatedTotalCost: procurementPlans.reduce((sum, plan) => sum + plan.estimatedCost, 0),
        averageLeadTime: procurementPlans.reduce((sum, plan) => sum + plan.estimatedLeadTime, 0) / procurementPlans.length,
      },
      message: "Procurement plans generated successfully from demand forecast",
    }, { status: 201 })
  } catch (error) {
    console.error("Error generating procurement plans from forecast:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to generate procurement plans",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})

/**
 * ✅ GET /api/planning/procurement/recommendations
 * Get purchase recommendations based on current plans and inventory
 */
export const getPurchaseRecommendations = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)

    // Validate query parameters
    const schema = z.object({
      daysAhead: z.string().optional().transform(val => val ? parseInt(val) : 90),
      includeBufferStock: z.string().optional().transform(val => val === "true"),
      containerOptimization: z.string().optional().transform(val => val === "true"),
    })

    const validatedParams = schema.parse(Object.fromEntries(searchParams.entries()))

    // Initialize service
    const procurementService = new ProcurementPlanningService()

    // Generate purchase recommendations
    const recommendations = await procurementService.generatePurchaseRecommendations(
      context.companyId,
      {
        daysAhead: validatedParams.daysAhead,
        includeBufferStock: validatedParams.includeBufferStock,
        containerOptimization: validatedParams.containerOptimization,
      }
    )

    // Categorize recommendations by urgency
    const categorized = {
      critical: recommendations.filter(r => r.urgencyLevel === "critical"),
      high: recommendations.filter(r => r.urgencyLevel === "high"),
      normal: recommendations.filter(r => r.urgencyLevel === "normal"),
      low: recommendations.filter(r => r.urgencyLevel === "low"),
    }

    return jsonOk({
      recommendations,
      categorized,
      summary: {
        total: recommendations.length,
        critical: categorized.critical.length,
        high: categorized.high.length,
        normal: categorized.normal.length,
        low: categorized.low.length,
        estimatedTotalCost: recommendations.reduce((sum, rec) => sum + rec.estimatedTotalCost, 0),
      },
      analysisParameters: validatedParams,
    })
  } catch (error) {
    console.error("Error getting purchase recommendations:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Invalid query parameters", 400, {
        validationErrors: error.errors,
      })
    }

    return jsonError(
      "Failed to get purchase recommendations",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})


