/**
 * Manufacturing ERP - Generate Procurement Plans from Forecast API
 * 
 * Professional API endpoint for generating procurement plans from approved demand forecasts
 * Implements multi-tenant security and comprehensive error handling
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1C MRP Implementation
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { z } from "zod"

/**
 * ✅ POST /api/planning/procurement/generate-from-forecast
 * Generate procurement plans from demand forecast
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()
    
    // Validate request body
    const schema = z.object({
      demandForecastId: z.string().min(1, "Demand forecast ID is required"),
      containerOptimization: z.boolean().default(true),
      preferredSuppliers: z.array(z.string()).optional(),
      maxLeadTime: z.number().optional(),
    })
    
    const validatedData = schema.parse(body)
    
    // Initialize service
    const procurementService = new ProcurementPlanningService()
    
    // Generate procurement plans
    const procurementPlans = await procurementService.generateProcurementPlansFromForecast(
      context.companyId,
      validatedData.demandForecastId,
      context.userId,
      {
        containerOptimization: validatedData.containerOptimization,
        preferredSuppliers: validatedData.preferredSuppliers,
        maxLeadTime: validatedData.maxLeadTime,
      }
    )
    
    return jsonOk({
      procurementPlans,
      summary: {
        totalPlans: procurementPlans.length,
        totalMaterials: procurementPlans.length,
        estimatedTotalCost: procurementPlans.reduce((sum, plan) => sum + plan.estimatedCost, 0),
        averageLeadTime: procurementPlans.length > 0 
          ? procurementPlans.reduce((sum, plan) => sum + plan.estimatedLeadTime, 0) / procurementPlans.length 
          : 0,
      },
      message: "Procurement plans generated successfully from demand forecast",
    }, { status: 201 })
  } catch (error) {
    console.error("Error generating procurement plans from forecast:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        validationErrors: error.errors,
      })
    }
    
    return jsonError(
      "Failed to generate procurement plans",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})
