/**
 * Manufacturing ERP - MRP Workflow Integration API
 * 
 * Professional API endpoints for MRP workflow integration with existing ERP systems.
 * Handles workflow triggers and business process automation.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Integration
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { mrpWorkflowIntegrationService, workflowTriggerSchema } from "@/lib/services/mrp-workflow-integration"
import { z } from "zod"

// ✅ PROFESSIONAL: Zod validation schemas
const triggerWorkflowSchema = z.object({
  eventType: z.enum(['contract_approved', 'forecast_approved', 'procurement_approved', 'work_order_completed', 'quality_approved']),
  entityId: z.string().min(1, "Entity ID is required"),
  entityType: z.string().min(1, "Entity type is required"),
  data: z.any().optional(),
})

/**
 * ✅ POST /api/planning/workflow - Trigger MRP workflow integration
 * 
 * Handles workflow triggers for MRP system integration with existing ERP workflows.
 * Automatically generates related entities based on business rules.
 * 
 * @param request - HTTP request with workflow trigger data
 * @returns WorkflowIntegrationResult with generated entities and actions
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()
    
    // Validate request body
    const validatedData = triggerWorkflowSchema.parse(body)
    
    // Create workflow trigger with tenant context
    const trigger = {
      ...validatedData,
      companyId: context.companyId,
      triggeredBy: context.userId,
    }
    
    // Execute workflow integration
    const result = await mrpWorkflowIntegrationService.handleWorkflowTrigger(trigger)
    
    if (!result.success) {
      return jsonError("Workflow integration failed", 400, {
        errors: result.errors,
        warnings: result.warnings
      })
    }
    
    return jsonOk(result, { status: 200 })
  } catch (error) {
    console.error("Error in workflow integration API:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        errors: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
      })
    }
    
    return jsonError("Internal server error", 500)
  }
})

/**
 * ✅ GET /api/planning/workflow - Get workflow integration status
 * 
 * Returns the current status of MRP workflow integrations and available triggers.
 * 
 * @param request - HTTP request
 * @returns Workflow integration status and available triggers
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const workflowStatus = {
      integrationActive: true,
      availableTriggers: [
        {
          eventType: 'contract_approved',
          description: 'Sales contract approval triggers demand forecasts and work orders',
          entityType: 'sales_contract',
          generatedEntities: ['demand_forecast', 'work_order', 'procurement_plan']
        },
        {
          eventType: 'forecast_approved',
          description: 'Demand forecast approval triggers procurement planning',
          entityType: 'demand_forecast',
          generatedEntities: ['procurement_plan']
        },
        {
          eventType: 'procurement_approved',
          description: 'Procurement plan approval triggers purchase order generation',
          entityType: 'procurement_plan',
          generatedEntities: ['purchase_order']
        },
        {
          eventType: 'work_order_completed',
          description: 'Work order completion triggers inventory updates',
          entityType: 'work_order',
          generatedEntities: ['stock_transaction']
        },
        {
          eventType: 'quality_approved',
          description: 'Quality approval clears items for shipping',
          entityType: 'quality_inspection',
          generatedEntities: ['shipping_clearance']
        }
      ],
      systemHealth: {
        mrpServicesActive: true,
        workflowEngineActive: true,
        integrationServicesActive: true,
        lastHealthCheck: new Date().toISOString()
      }
    }
    
    return jsonOk(workflowStatus)
  } catch (error) {
    console.error("Error getting workflow status:", error)
    return jsonError("Internal server error", 500)
  }
})

/**
 * ✅ OPTIONS /api/planning/workflow - CORS preflight
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
