/**
 * Inventory Transactions Overview API
 * 
 * Provides comprehensive transaction history and analytics
 * Following established patterns with multi-tenant security
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { InventoryTransactionService } from "@/lib/services/inventory-transactions"

// 🛡️ SECURE: Multi-tenant GET endpoint for all transaction types
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const url = new URL(request.url)
    const productId = url.searchParams.get("product_id")
    const transactionType = url.searchParams.get("transaction_type")
    const location = url.searchParams.get("location")
    const limit = url.searchParams.get("limit")
    const dateFrom = url.searchParams.get("date_from")
    const dateTo = url.searchParams.get("date_to")

    // Initialize transaction service with context
    const transactionService = new InventoryTransactionService({
      companyId: context.companyId,
      userId: context.userId,
    })

    // Build filters
    const filters: any = {}
    if (productId) filters.productId = productId
    if (transactionType) filters.transactionType = transactionType
    if (location) filters.location = location
    if (limit) filters.limit = parseInt(limit)
    if (dateFrom) filters.dateFrom = new Date(dateFrom)
    if (dateTo) filters.dateTo = new Date(dateTo)

    // Get transaction history
    const transactions = await transactionService.getTransactionHistory(filters)

    return jsonOk(transactions)
  } catch (error) {
    console.error("Transaction overview API error:", error)
    return jsonError(
      error instanceof Error ? error.message : "Internal server error",
      500
    )
  }
})

// 🛡️ SECURE: Multi-tenant GET endpoint for stock levels
export const POST = withTenantAuth(async function POST(request, context) {
  try {
    const body = await request.json()
    const { action, ...params } = body

    // Initialize transaction service with context
    const transactionService = new InventoryTransactionService({
      companyId: context.companyId,
      userId: context.userId,
    })

    switch (action) {
      case "stock_levels":
        const stockLevels = await transactionService.getStockLevels(
          params.productId,
          params.location
        )
        return jsonOk(stockLevels)

      case "transaction_summary":
        // Get transaction summary by type
        const allTransactions = await transactionService.getTransactionHistory({
          limit: 1000 // Get more for summary
        })
        
        const summary = {
          totalTransactions: allTransactions.length,
          byType: allTransactions.reduce((acc: any, txn: any) => {
            acc[txn.transaction_type] = (acc[txn.transaction_type] || 0) + 1
            return acc
          }, {}),
          byLocation: allTransactions.reduce((acc: any, txn: any) => {
            const location = txn.location || txn.from_location || txn.to_location
            if (location) {
              acc[location] = (acc[location] || 0) + 1
            }
            return acc
          }, {}),
          recentTransactions: allTransactions.slice(0, 10)
        }
        
        return jsonOk(summary)

      default:
        return jsonError("Invalid action", 400)
    }
  } catch (error) {
    console.error("Transaction action API error:", error)
    return jsonError(
      error instanceof Error ? error.message : "Internal server error",
      500
    )
  }
})
