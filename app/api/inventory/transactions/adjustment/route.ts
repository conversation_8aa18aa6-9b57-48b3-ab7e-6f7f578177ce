/**
 * Adjustment Inventory Transactions API
 * 
 * Handles inventory adjustments for cycle counts, damage, obsolescence, etc.
 * Following established patterns with multi-tenant security
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { InventoryTransactionService } from "@/lib/services/inventory-transactions"
import { adjustmentTransactionSchema } from "@/lib/validations"
import { z } from "zod"

// 🛡️ SECURE: Multi-tenant POST endpoint for adjustment transactions
export const POST = withTenantAuth(async function POST(request, context) {
  try {
    const body = await request.json()
    
    // Validate request body
    const validation = adjustmentTransactionSchema.safeParse(body)
    if (!validation.success) {
      return jsonError("Validation failed", 400, {
        issues: validation.error.issues
      })
    }

    // Initialize transaction service with context
    const transactionService = new InventoryTransactionService({
      companyId: context.companyId,
      userId: context.userId,
      userAgent: request.headers.get("user-agent") || undefined,
      ipAddress: request.headers.get("x-forwarded-for") || undefined,
    })

    // Process adjustment transaction
    const result = await transactionService.processAdjustment(validation.data)

    return jsonOk(result, { status: 201 })
  } catch (error) {
    console.error("Adjustment transaction API error:", error)
    return jsonError(
      error instanceof Error ? error.message : "Internal server error",
      500
    )
  }
})

// 🛡️ SECURE: Multi-tenant GET endpoint for adjustment transaction history
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const url = new URL(request.url)
    const productId = url.searchParams.get("product_id")
    const location = url.searchParams.get("location")
    const reasonCode = url.searchParams.get("reason_code")
    const limit = url.searchParams.get("limit")

    // Initialize transaction service with context
    const transactionService = new InventoryTransactionService({
      companyId: context.companyId,
      userId: context.userId,
    })

    // Get adjustment transaction history
    const transactions = await transactionService.getTransactionHistory({
      transactionType: "adjustment",
      productId: productId || undefined,
      location: location || undefined,
      limit: limit ? parseInt(limit) : undefined,
    })

    return jsonOk(transactions)
  } catch (error) {
    console.error("Adjustment transaction history API error:", error)
    return jsonError(
      error instanceof Error ? error.message : "Internal server error",
      500
    )
  }
})
