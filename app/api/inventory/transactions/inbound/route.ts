/**
 * Inbound Inventory Transactions API
 * 
 * Handles receiving stock into inventory with comprehensive validation
 * Following established patterns with multi-tenant security
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { InventoryTransactionService } from "@/lib/services/inventory-transactions"
import { inboundTransactionSchema } from "@/lib/validations"
import { z } from "zod"

// 🛡️ SECURE: Multi-tenant POST endpoint for inbound transactions
export const POST = withTenantAuth(async function POST(request, context) {
  try {
    const body = await request.json()
    
    // Validate request body
    const validation = inboundTransactionSchema.safeParse(body)
    if (!validation.success) {
      return jsonError("Validation failed", 400, {
        issues: validation.error.issues
      })
    }

    // Initialize transaction service with context
    const transactionService = new InventoryTransactionService({
      companyId: context.companyId,
      userId: context.userId,
      userAgent: request.headers.get("user-agent") || undefined,
      ipAddress: request.headers.get("x-forwarded-for") || undefined,
    })

    // Process inbound transaction
    const result = await transactionService.processInbound(validation.data)

    return jsonOk(result, { status: 201 })
  } catch (error) {
    console.error("Inbound transaction API error:", error)
    return jsonError(
      error instanceof Error ? error.message : "Internal server error",
      500
    )
  }
})

// 🛡️ SECURE: Multi-tenant GET endpoint for inbound transaction history
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const url = new URL(request.url)
    const productId = url.searchParams.get("product_id")
    const location = url.searchParams.get("location")
    const limit = url.searchParams.get("limit")

    // Initialize transaction service with context
    const transactionService = new InventoryTransactionService({
      companyId: context.companyId,
      userId: context.userId,
    })

    // Get inbound transaction history
    const transactions = await transactionService.getTransactionHistory({
      transactionType: "inbound",
      productId: productId || undefined,
      location: location || undefined,
      limit: limit ? parseInt(limit) : undefined,
    })

    return jsonOk(transactions)
  } catch (error) {
    console.error("Inbound transaction history API error:", error)
    return jsonError(
      error instanceof Error ? error.message : "Internal server error",
      500
    )
  }
})
