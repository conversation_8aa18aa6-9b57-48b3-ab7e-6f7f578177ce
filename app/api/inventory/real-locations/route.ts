/**
 * Real Locations API - Get only locations that actually have inventory
 * Zero breaking changes - only adds filtering capabilities
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { getUsedLocations, getRealLocationConfigs } from "@/lib/real-data-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint for real locations only
export const GET = withTenantAuth(async function GET(request: Request, context) {
  try {
    // Get locations that actually have inventory data
    const usedLocationIds = await getUsedLocations(context.companyId)
    
    // Get location configurations for used locations
    const realLocationConfigs = await getRealLocationConfigs(context.companyId)
    
    return jsonOk({
      locations: usedLocationIds,
      locationConfigs: realLocationConfigs,
      count: usedLocationIds.length,
      message: `Found ${usedLocationIds.length} locations with actual inventory data`
    })
  } catch (error) {
    console.error("Real Locations API Error:", error)
    return jsonError("Failed to fetch real locations", 500)
  }
})
