/**
 * Manufacturing ERP - Professional Inventory Costing API
 * 
 * API endpoint for professional inventory costing calculations.
 * Uses industrial-standard multi-tier pricing logic.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Professional Costing System
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { InventoryCostingService } from "@/lib/inventory-costing-service"
import { z } from "zod"

// ✅ PROFESSIONAL: Zod validation schema
const costingRequestSchema = z.object({
  inventoryLots: z.array(z.object({
    product_id: z.string(),
    qty: z.string(),
    location: z.string(),
    product: z.any().optional()
  }))
})

/**
 * ✅ POST /api/inventory/costing - Calculate professional inventory costs
 * 
 * Calculates inventory values using industrial-standard multi-tier pricing logic.
 * Integrates with sales contracts, location-based costing, and standard cost methods.
 * 
 * @param request - HTTP request with inventory lots data
 * @returns Professional costing calculations
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()
    
    // Validate request data
    const validatedData = costingRequestSchema.parse(body)
    
    console.log(`💰 Professional costing request for company ${context.companyId}:`, {
      lots: validatedData.inventoryLots.length,
      timestamp: new Date().toISOString()
    })

    // ✅ CALCULATE PROFESSIONAL COSTS
    const costingResults = await InventoryCostingService.calculateInventoryValues(
      validatedData.inventoryLots,
      context.companyId
    )

    // ✅ CALCULATE SUMMARY STATISTICS
    const summary = {
      totalLots: costingResults.length,
      totalValue: costingResults.reduce((sum, result) => sum + result.totalValue, 0),
      totalQuantity: validatedData.inventoryLots.reduce((sum, lot) => sum + parseFloat(lot.qty || '0'), 0),
      averageUnitCost: 0,
      
      pricingMethodBreakdown: {
        contract: costingResults.filter(r => r.costBreakdown.pricingMethod === 'contract').length,
        historical: costingResults.filter(r => r.costBreakdown.pricingMethod === 'historical').length,
        standard: costingResults.filter(r => r.costBreakdown.pricingMethod === 'standard').length,
        selling: costingResults.filter(r => r.costBreakdown.pricingMethod === 'selling').length,
        fallback: costingResults.filter(r => r.costBreakdown.pricingMethod === 'fallback').length,
      },
      
      confidenceBreakdown: {
        high: costingResults.filter(r => r.costBreakdown.confidence === 'high').length,
        medium: costingResults.filter(r => r.costBreakdown.confidence === 'medium').length,
        low: costingResults.filter(r => r.costBreakdown.confidence === 'low').length,
      }
    }

    // Calculate average unit cost
    if (summary.totalQuantity > 0) {
      summary.averageUnitCost = Math.round((summary.totalValue / summary.totalQuantity) * 100) / 100
    }

    // ✅ LOCATION-BASED ANALYSIS
    const locationAnalysis = validatedData.inventoryLots.reduce((acc, lot, index) => {
      const location = lot.location || 'Unknown'
      const result = costingResults[index]
      
      if (!acc[location]) {
        acc[location] = {
          location,
          totalValue: 0,
          totalQuantity: 0,
          lotCount: 0,
          products: new Set()
        }
      }
      
      acc[location].totalValue += result.totalValue
      acc[location].totalQuantity += parseFloat(lot.qty || '0')
      acc[location].lotCount++
      acc[location].products.add(lot.product_id)
      
      return acc
    }, {} as Record<string, any>)

    // Convert location analysis to array
    const locationBreakdown = Object.values(locationAnalysis).map((loc: any) => ({
      ...loc,
      totalValue: Math.round(loc.totalValue * 100) / 100,
      totalQuantity: Math.round(loc.totalQuantity * 100) / 100,
      uniqueProducts: loc.products.size,
      averageUnitCost: loc.totalQuantity > 0 ? 
        Math.round((loc.totalValue / loc.totalQuantity) * 100) / 100 : 0
    }))

    // ✅ GENERATE RECOMMENDATIONS
    const recommendations = []
    
    if (summary.pricingMethodBreakdown.fallback > 0) {
      recommendations.push({
        priority: 'high',
        category: 'pricing',
        title: `${summary.pricingMethodBreakdown.fallback} products using fallback pricing`,
        description: 'Set proper prices or create sales contracts for accurate valuation',
        impact: 'Improve inventory valuation accuracy'
      })
    }
    
    if (summary.confidenceBreakdown.low > summary.confidenceBreakdown.high) {
      recommendations.push({
        priority: 'medium',
        category: 'data_quality',
        title: 'Low confidence in pricing data',
        description: 'Review and update product pricing information',
        impact: 'Better financial reporting and decision making'
      })
    }
    
    if (summary.pricingMethodBreakdown.contract === 0 && summary.totalLots > 0) {
      recommendations.push({
        priority: 'medium',
        category: 'integration',
        title: 'No contract-based pricing found',
        description: 'Link products to sales contracts for dynamic pricing',
        impact: 'Real-time pricing based on actual sales'
      })
    }

    // ✅ RESPONSE DATA
    const response = {
      success: true,
      data: costingResults.map(result => ({
        productId: result.costBreakdown.productId,
        sku: result.costBreakdown.sku,
        name: result.costBreakdown.name,
        quantity: parseFloat(result.lot.qty || '0'),
        location: result.lot.location,
        unitCost: result.costBreakdown.calculatedPrice,
        totalValue: result.totalValue,
        pricingMethod: result.costBreakdown.pricingMethod,
        confidence: result.costBreakdown.confidence,
        costBreakdown: {
          material: result.costBreakdown.materialCost,
          labor: result.costBreakdown.laborCost,
          overhead: result.costBreakdown.overheadCost,
          standard: result.costBreakdown.standardCost
        }
      })),
      
      summary,
      locationBreakdown,
      recommendations,
      
      metadata: {
        calculatedAt: new Date().toISOString(),
        companyId: context.companyId,
        method: 'professional_multi_tier'
      }
    }

    console.log(`✅ Professional costing completed:`, {
      totalValue: summary.totalValue,
      averageUnitCost: summary.averageUnitCost,
      highConfidence: summary.confidenceBreakdown.high
    })

    return jsonOk(response)

  } catch (error) {
    console.error("Error in professional costing:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid request data", 400, error.errors)
    }
    
    return jsonError("Internal server error", 500)
  }
})
