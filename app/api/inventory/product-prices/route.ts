/**
 * Manufacturing ERP - Product Prices Debug API
 * 
 * Debug endpoint to check current product prices and suggest solutions.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Inventory Analytics Debug
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { products, stockLots } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"

/**
 * ✅ GET /api/inventory/product-prices - Check product prices and inventory values
 * 
 * Returns current product prices and calculates what inventory values should be
 * for troubleshooting analytics dashboard issues.
 * 
 * @param request - HTTP request
 * @returns Product pricing information and recommendations
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    console.log(`🔍 Checking product prices for company: ${context.companyId}`)

    // ✅ FETCH ALL PRODUCTS WITH THEIR CURRENT PRICES
    const allProducts = await db.query.products.findMany({
      where: eq(products.company_id, context.companyId)
    })

    console.log(`📦 Found ${allProducts.length} products`)

    // ✅ FETCH INVENTORY LOTS WITH PRODUCT INFO
    const inventoryLots = await db.query.stockLots.findMany({
      where: eq(stockLots.company_id, context.companyId),
      with: {
        product: true
      }
    })

    console.log(`📊 Found ${inventoryLots.length} inventory lots`)

    // ✅ ENHANCED: Analyze product pricing using pricing hierarchy
    const productAnalysis = allProducts.map(product => {
      // ✅ PRICING HIERARCHY: base_price → cost_price → legacy price → fallback
      let price = 0
      let pricingSource = 'fallback'
      let hasPrice = false

      if (product.base_price && parseFloat(product.base_price) > 0) {
        price = parseFloat(product.base_price)
        pricingSource = 'base_price'
        hasPrice = true
      } else if (product.cost_price && parseFloat(product.cost_price) > 0) {
        price = parseFloat(product.cost_price)
        pricingSource = 'cost_price'
        hasPrice = true
      } else if (product.price && parseFloat(product.price) > 0) {
        price = parseFloat(product.price)
        pricingSource = 'legacy_price'
        hasPrice = true
      } else {
        price = 1 // Fallback price for inventory valuation
        pricingSource = 'fallback'
        hasPrice = false
      }

      // Find inventory lots for this product
      const productLots = inventoryLots.filter(lot => lot.product_id === product.id)
      const totalQty = productLots.reduce((sum, lot) => sum + parseFloat(lot.qty || '0'), 0)
      const totalValue = totalQty * price

      return {
        id: product.id,
        sku: product.sku,
        name: product.name,
        price: product.price, // Legacy field for compatibility
        base_price: product.base_price, // Enhanced pricing
        cost_price: product.cost_price, // Enhanced pricing
        margin_percentage: product.margin_percentage, // Enhanced pricing
        currency: product.currency || 'USD', // Enhanced pricing
        parsedPrice: price,
        pricingSource,
        hasPrice,
        category: product.category,
        totalQty,
        totalValue,
        lotCount: productLots.length,
        locations: [...new Set(productLots.map(lot => lot.location))]
      }
    })

    // ✅ CALCULATE SUMMARY STATISTICS
    const summary = {
      totalProducts: allProducts.length,
      productsWithPrices: productAnalysis.filter(p => p.hasPrice).length,
      productsWithoutPrices: productAnalysis.filter(p => !p.hasPrice).length,
      totalInventoryQty: productAnalysis.reduce((sum, p) => sum + p.totalQty, 0),
      totalInventoryValue: productAnalysis.reduce((sum, p) => sum + p.totalValue, 0),
      averagePrice: productAnalysis.filter(p => p.hasPrice).length > 0 ?
        productAnalysis.filter(p => p.hasPrice).reduce((sum, p) => sum + p.parsedPrice, 0) /
        productAnalysis.filter(p => p.hasPrice).length : 0
    }

    // ✅ IDENTIFY ISSUES AND RECOMMENDATIONS
    const issues = []
    const recommendations = []

    if (summary.productsWithoutPrices > 0) {
      issues.push(`${summary.productsWithoutPrices} products don't have prices set`)
      recommendations.push({
        priority: 'high',
        action: 'Set product prices',
        description: 'Go to Products module and set prices for all products',
        impact: 'This will fix inventory valuation calculations'
      })
    }

    if (summary.totalInventoryValue === summary.totalInventoryQty) {
      issues.push('Inventory value equals quantity (indicating $1 fallback is being used)')
      recommendations.push({
        priority: 'high',
        action: 'Update product pricing',
        description: 'Set realistic prices for your products based on cost + margin',
        impact: 'Analytics will show meaningful financial values'
      })
    }

    // ✅ SUGGEST REALISTIC PRICES BASED ON PRODUCT TYPES
    const pricingSuggestions = productAnalysis.filter(p => !p.hasPrice).map(product => {
      let suggestedPrice = 10 // Default $10

      // Suggest prices based on product name/type
      const name = product.name.toLowerCase()
      if (name.includes('silk')) suggestedPrice = 25
      else if (name.includes('cotton')) suggestedPrice = 15
      else if (name.includes('velvet')) suggestedPrice = 30
      else if (name.includes('garment')) suggestedPrice = 50
      else if (name.includes('t-shirt') || name.includes('shirt')) suggestedPrice = 20

      return {
        ...product,
        suggestedPrice,
        suggestedTotalValue: product.totalQty * suggestedPrice
      }
    })

    // ✅ RESPONSE DATA
    const response = {
      summary,
      issues,
      recommendations,

      productAnalysis: productAnalysis.map(p => ({
        sku: p.sku,
        name: p.name,
        currentPrice: p.price || 'Not set',
        hasPrice: p.hasPrice,
        totalQty: p.totalQty,
        currentValue: p.totalValue,
        locations: p.locations
      })),

      pricingSuggestions: pricingSuggestions.map(p => ({
        sku: p.sku,
        name: p.name,
        currentQty: p.totalQty,
        suggestedPrice: p.suggestedPrice,
        potentialValue: p.suggestedTotalValue
      })),

      // ✅ QUICK FIXES
      quickFixes: [
        {
          title: 'Option 1: Set Product Prices (Recommended)',
          description: 'Go to Products module and set realistic prices for each product',
          pros: ['Accurate inventory valuation', 'Better financial reporting', 'Proper cost analysis'],
          cons: ['Requires manual input for each product']
        },
        {
          title: 'Option 2: Use Higher Default Price',
          description: 'Change the fallback price from $1 to a more realistic amount like $10-20',
          pros: ['Quick fix', 'Better than $1 values'],
          cons: ['Not accurate', 'All products valued the same']
        },
        {
          title: 'Option 3: Import Prices from Contracts',
          description: 'Use prices from sales contracts to estimate product values',
          pros: ['Based on actual business data', 'Automated approach'],
          cons: ['May not reflect current costs', 'Complex to implement']
        }
      ],

      calculatedAt: new Date().toISOString()
    }

    return jsonOk(response)

  } catch (error) {
    console.error("Error checking product prices:", error)
    return jsonError("Internal server error", 500)
  }
})
