import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { stockLots, rawMaterials, rawMaterialLots } from "@/lib/schema-postgres"
import { eq, and, desc } from "drizzle-orm"

interface UnifiedInventoryData {
  summary: {
    finishedGoods: {
      totalUnits: number
      totalValue: number
      totalLots: number
      totalProducts: number
      locations: string[]
    }
    rawMaterials: {
      totalUnits: number
      totalValue: number
      totalLots: number
      totalMaterials: number
      locations: string[]
    }
    combined: {
      totalValue: number
      totalLocations: number
      valueDistribution: {
        finishedGoodsPercentage: number
        rawMaterialsPercentage: number
      }
    }
  }
  finishedGoods: Array<{
    id: string
    lotNumber: string
    productName: string
    qty: number
    location: string
    value: number
    status: string
    category: 'finished_goods'
  }>
  rawMaterials: Array<{
    id: string
    lotNumber: string
    materialName: string
    qty: number
    location: string
    value: number
    status: string
    category: 'raw_materials'
  }>
  analytics: {
    locationUtilization: Array<{
      location: string
      category: 'finished_goods' | 'raw_materials'
      lotCount: number
      totalValue: number
      utilization: string
    }>
    valueAnalysis: {
      averageFinishedGoodsValue: number
      averageRawMaterialsValue: number
      highestValueLot: {
        type: 'finished_goods' | 'raw_materials'
        lotNumber: string
        value: number
      }
    }
  }
}

export const GET = withTenantAuth(async function GET(request, context) {
  try {
    console.log('🔄 Fetching unified inventory data...')

    // Fetch finished goods data
    const finishedGoodsLots = await db.query.stockLots.findMany({
      where: eq(stockLots.company_id, context.companyId),
      with: {
        product: true,
      },
      orderBy: [desc(stockLots.created_at)],
    })

    // Fetch raw materials data
    const rawMaterialsData = await db.query.rawMaterials.findMany({
      where: eq(rawMaterials.company_id, context.companyId),
      with: {
        lots: {
          where: (lots, { eq }) => eq(lots.status, "available"),
          orderBy: (lots, { desc }) => [desc(lots.created_at)],
        },
      },
      orderBy: [desc(rawMaterials.created_at)],
    })

    // Process finished goods
    const fgData = finishedGoodsLots.map(lot => {
      const qty = parseFloat(lot.qty || '0')
      const price = parseFloat(lot.product?.price || '20')
      return {
        id: lot.id,
        lotNumber: lot.lot_number || 'N/A',
        productName: lot.product?.name || 'Unknown Product',
        qty,
        location: lot.location || 'Unassigned',
        value: qty * price,
        status: lot.quality_status || 'available',
        category: 'finished_goods' as const,
      }
    })

    // Process raw materials
    const rmData: Array<{
      id: string
      lotNumber: string
      materialName: string
      qty: number
      location: string
      value: number
      status: string
      category: 'raw_materials'
    }> = []

    rawMaterialsData.forEach(material => {
      material.lots.forEach(lot => {
        if (lot.status === 'available') {
          rmData.push({
            id: lot.id,
            lotNumber: lot.lot_number || 'N/A',
            materialName: material.name,
            qty: parseFloat(lot.qty || '0'),
            location: lot.location || 'Unassigned',
            value: parseFloat(lot.total_cost || '0'),
            status: lot.status,
            category: 'raw_materials' as const,
          })
        }
      })
    })

    // Calculate summary statistics
    const fgTotalUnits = fgData.reduce((sum, item) => sum + item.qty, 0)
    const fgTotalValue = fgData.reduce((sum, item) => sum + item.value, 0)
    const fgLocations = [...new Set(fgData.map(item => item.location).filter(loc => loc !== 'Unassigned'))]
    const fgProducts = new Set(fgData.map(item => item.productName)).size

    const rmTotalUnits = rmData.reduce((sum, item) => sum + item.qty, 0)
    const rmTotalValue = rmData.reduce((sum, item) => sum + item.value, 0)
    const rmLocations = [...new Set(rmData.map(item => item.location).filter(loc => loc !== 'Unassigned'))]
    const rmMaterials = rawMaterialsData.length

    const combinedValue = fgTotalValue + rmTotalValue
    const combinedLocations = new Set([...fgLocations, ...rmLocations]).size

    // Calculate location utilization
    const locationUtilization = new Map<string, { category: 'finished_goods' | 'raw_materials', lotCount: number, totalValue: number }>()

    fgData.forEach(item => {
      if (item.location !== 'Unassigned') {
        const existing = locationUtilization.get(item.location) || { category: 'finished_goods' as const, lotCount: 0, totalValue: 0 }
        locationUtilization.set(item.location, {
          category: 'finished_goods',
          lotCount: existing.lotCount + 1,
          totalValue: existing.totalValue + item.value,
        })
      }
    })

    rmData.forEach(item => {
      if (item.location !== 'Unassigned') {
        const existing = locationUtilization.get(item.location) || { category: 'raw_materials' as const, lotCount: 0, totalValue: 0 }
        locationUtilization.set(item.location, {
          category: 'raw_materials',
          lotCount: existing.lotCount + 1,
          totalValue: existing.totalValue + item.value,
        })
      }
    })

    // Find highest value lot
    const allLots = [...fgData, ...rmData]
    const highestValueLot = allLots.reduce((highest, current) => 
      current.value > highest.value ? current : highest
    , allLots[0] || { value: 0, lotNumber: 'N/A', category: 'finished_goods' as const })

    // Build unified response
    const unifiedData: UnifiedInventoryData = {
      summary: {
        finishedGoods: {
          totalUnits: Math.round(fgTotalUnits),
          totalValue: Math.round(fgTotalValue),
          totalLots: fgData.length,
          totalProducts: fgProducts,
          locations: fgLocations,
        },
        rawMaterials: {
          totalUnits: Math.round(rmTotalUnits * 100) / 100,
          totalValue: Math.round(rmTotalValue),
          totalLots: rmData.length,
          totalMaterials: rmMaterials,
          locations: rmLocations,
        },
        combined: {
          totalValue: Math.round(combinedValue),
          totalLocations: combinedLocations,
          valueDistribution: {
            finishedGoodsPercentage: Math.round((fgTotalValue / combinedValue) * 100),
            rawMaterialsPercentage: Math.round((rmTotalValue / combinedValue) * 100),
          },
        },
      },
      finishedGoods: fgData,
      rawMaterials: rmData,
      analytics: {
        locationUtilization: Array.from(locationUtilization.entries()).map(([location, data]) => ({
          location,
          category: data.category,
          lotCount: data.lotCount,
          totalValue: Math.round(data.totalValue),
          utilization: data.lotCount > 10 ? 'High' : data.lotCount > 5 ? 'Medium' : 'Low',
        })),
        valueAnalysis: {
          averageFinishedGoodsValue: fgData.length > 0 ? Math.round(fgTotalValue / fgData.length) : 0,
          averageRawMaterialsValue: rmData.length > 0 ? Math.round(rmTotalValue / rmData.length) : 0,
          highestValueLot: {
            type: highestValueLot.category,
            lotNumber: highestValueLot.lotNumber,
            value: Math.round(highestValueLot.value),
          },
        },
      },
    }

    console.log('✅ Unified inventory data compiled successfully')
    console.log(`   FG: ${unifiedData.summary.finishedGoods.totalLots} lots, $${unifiedData.summary.finishedGoods.totalValue}`)
    console.log(`   RM: ${unifiedData.summary.rawMaterials.totalLots} lots, $${unifiedData.summary.rawMaterials.totalValue}`)
    console.log(`   Combined: $${unifiedData.summary.combined.totalValue}, ${unifiedData.summary.combined.totalLocations} locations`)

    return jsonOk(unifiedData)

  } catch (error) {
    console.error('❌ Error fetching unified inventory data:', error)
    return jsonError('Failed to fetch unified inventory data', 500, {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})
