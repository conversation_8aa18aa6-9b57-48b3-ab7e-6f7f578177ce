/**
 * Manufacturing ERP - Inventory Debug API
 * 
 * Debug endpoint to check current inventory data and location mapping.
 * Helps troubleshoot analytics integration issues.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 2 Advanced Inventory Management Debug
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { stockLots, stockTxns, products } from "@/lib/schema-postgres"
import { eq, and, sql } from "drizzle-orm"
import { LocationManager, getLocationForUI, LEGACY_LOCATION_MAPPING } from "@/lib/location-config"

/**
 * ✅ GET /api/inventory/debug - Debug inventory data and location mapping
 * 
 * Returns current inventory data, location mapping, and configuration
 * for troubleshooting analytics integration issues.
 * 
 * @param request - HTTP request
 * @returns Debug information about inventory and locations
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    console.log(`🔍 Debug request for company: ${context.companyId}`)

    // ✅ FETCH CURRENT STOCK LOTS
    const stockLots = await db.query.stockLots.findMany({
      where: eq(stockLots.company_id, context.companyId),
      with: {
        product: true
      }
    })

    console.log(`📦 Found ${stockLots.length} stock lots`)

    // ✅ FETCH STOCK TRANSACTIONS
    const stockTransactions = await db.query.stockTxns.findMany({
      where: eq(stockTxns.company_id, context.companyId),
      with: {
        product: true
      }
    })

    console.log(`📊 Found ${stockTransactions.length} stock transactions`)

    // ✅ ANALYZE LOCATIONS
    const locationStats = new Map<string, {
      stockLots: number
      totalQty: number
      totalValue: number
      products: Set<string>
    }>()

    stockLots.forEach(lot => {
      const location = lot.location
      const qty = parseFloat(lot.qty || '0')
      const cost = parseFloat(lot.product?.standard_cost || '0')
      const value = qty * cost

      if (!locationStats.has(location)) {
        locationStats.set(location, {
          stockLots: 0,
          totalQty: 0,
          totalValue: 0,
          products: new Set()
        })
      }

      const stats = locationStats.get(location)!
      stats.stockLots++
      stats.totalQty += qty
      stats.totalValue += value
      stats.products.add(lot.product_id)
    })

    // ✅ GET LOCATION CONFIGURATIONS
    const allLocationConfigs = LocationManager.getAllLocations()
    const locationMappings = []

    for (const [locationId, stats] of locationStats.entries()) {
      // Try to find location config
      let locationConfig = LocationManager.getLocationById(locationId)
      
      // Fallback: Try legacy mapping
      if (!locationConfig) {
        locationConfig = getLocationForUI(locationId)
      }
      
      // Additional fallback: Try by name
      if (!locationConfig) {
        locationConfig = allLocationConfigs.find(loc => 
          loc.name === locationId || 
          loc.displayName === locationId
        )
      }

      locationMappings.push({
        locationId,
        locationConfig: locationConfig ? {
          id: locationConfig.id,
          name: locationConfig.name,
          displayName: locationConfig.displayName,
          type: locationConfig.type,
          capacity: locationConfig.capacity
        } : null,
        stats: {
          stockLots: stats.stockLots,
          totalQty: Math.round(stats.totalQty * 100) / 100,
          totalValue: Math.round(stats.totalValue * 100) / 100,
          uniqueProducts: stats.products.size
        }
      })
    }

    // ✅ ANALYZE PRODUCT CATEGORIES
    const categoryStats = new Map<string, number>()
    stockLots.forEach(lot => {
      const category = lot.product?.category || 'uncategorized'
      categoryStats.set(category, (categoryStats.get(category) || 0) + 1)
    })

    // ✅ SAMPLE DATA
    const sampleStockLots = stockLots.slice(0, 5).map(lot => ({
      id: lot.id,
      productId: lot.product_id,
      productName: lot.product?.name,
      productCategory: lot.product?.category,
      qty: lot.qty,
      location: lot.location,
      standardCost: lot.product?.standard_cost,
      createdAt: lot.created_at
    }))

    const sampleTransactions = stockTransactions.slice(0, 5).map(txn => ({
      id: txn.id,
      type: txn.type,
      transactionType: txn.transaction_type,
      productId: txn.product_id,
      productName: txn.product?.name,
      qty: txn.qty,
      fromLocation: txn.from_location,
      toLocation: txn.to_location,
      createdAt: txn.created_at
    }))

    // ✅ DEBUG RESPONSE
    const debugInfo = {
      summary: {
        totalStockLots: stockLots.length,
        totalTransactions: stockTransactions.length,
        uniqueLocations: locationStats.size,
        totalInventoryValue: Array.from(locationStats.values()).reduce((sum, stats) => sum + stats.totalValue, 0)
      },
      
      locationAnalysis: {
        usedLocations: Array.from(locationStats.keys()),
        locationMappings,
        legacyLocationMapping: LEGACY_LOCATION_MAPPING
      },
      
      productAnalysis: {
        categoryDistribution: Object.fromEntries(categoryStats.entries()),
        totalUniqueProducts: new Set(stockLots.map(lot => lot.product_id)).size
      },
      
      configuredLocations: {
        total: allLocationConfigs.length,
        active: allLocationConfigs.filter(loc => loc.isActive).length,
        byType: allLocationConfigs.reduce((acc, loc) => {
          acc[loc.type] = (acc[loc.type] || 0) + 1
          return acc
        }, {} as Record<string, number>)
      },
      
      sampleData: {
        stockLots: sampleStockLots,
        transactions: sampleTransactions
      },
      
      recommendations: []
    }

    // ✅ GENERATE RECOMMENDATIONS
    if (locationStats.size === 0) {
      debugInfo.recommendations.push("No inventory data found. Check if stock lots exist for this company.")
    }

    if (locationMappings.some(mapping => !mapping.locationConfig)) {
      debugInfo.recommendations.push("Some locations in inventory data don't have corresponding location configurations.")
    }

    if (categoryStats.size === 1 && categoryStats.has('uncategorized')) {
      debugInfo.recommendations.push("All products are uncategorized. Consider setting product categories for better analytics.")
    }

    console.log(`✅ Debug analysis complete:`, {
      stockLots: stockLots.length,
      locations: locationStats.size,
      totalValue: debugInfo.summary.totalInventoryValue
    })

    return jsonOk(debugInfo)

  } catch (error) {
    console.error("Error in inventory debug:", error)
    return jsonError("Internal server error", 500)
  }
})
