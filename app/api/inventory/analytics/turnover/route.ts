/**
 * Manufacturing ERP - Inventory Analytics Turnover API
 * 
 * Professional API endpoint for inventory turnover analysis.
 * Provides ABC classification, turnover ratios, and movement analysis.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 2 Advanced Inventory Management
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { stockLots, stockTxns, products } from "@/lib/schema-postgres"
import { eq, and, gte, lte, sql } from "drizzle-orm"
import { z } from "zod"

// ✅ PROFESSIONAL: Zod validation schema
const turnoverQuerySchema = z.object({
  timeRange: z.enum(['30d', '90d', '6m', '1y']).default('90d'),
  classification: z.enum(['all', 'fast', 'medium', 'slow', 'dead']).optional(),
  sortBy: z.enum(['turnoverRatio', 'daysOnHand', 'productName', 'currentStock']).default('turnoverRatio'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

/**
 * ✅ GET /api/inventory/analytics/turnover - Get inventory turnover analysis
 * 
 * Analyzes inventory turnover rates, calculates ABC classification,
 * and provides recommendations for inventory optimization.
 * 
 * @param request - HTTP request with query parameters
 * @returns Inventory turnover analysis data
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    const queryParams = {
      timeRange: searchParams.get('timeRange') || '90d',
      classification: searchParams.get('classification') || undefined,
      sortBy: searchParams.get('sortBy') || 'turnoverRatio',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    }
    
    // Validate query parameters
    const validatedParams = turnoverQuerySchema.parse(queryParams)
    
    // Calculate date range
    const endDate = new Date()
    const startDate = new Date()
    
    switch (validatedParams.timeRange) {
      case '30d':
        startDate.setDate(endDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(endDate.getDate() - 90)
        break
      case '6m':
        startDate.setMonth(endDate.getMonth() - 6)
        break
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1)
        break
    }

    const startDateStr = startDate.toISOString().split('T')[0]
    const endDateStr = endDate.toISOString().split('T')[0]
    const daysInPeriod = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))

    // ✅ FETCH CURRENT STOCK LEVELS
    const currentStock = await db.query.stockLots.findMany({
      where: and(
        eq(stockLots.company_id, context.companyId),
        sql`CAST(${stockLots.qty} AS DECIMAL) > 0`
      ),
      with: {
        product: true
      }
    })

    // ✅ FETCH STOCK TRANSACTIONS FOR ANALYSIS PERIOD
    const transactions = await db.query.stockTxns.findMany({
      where: and(
        eq(stockTxns.company_id, context.companyId),
        gte(stockTxns.created_at, startDate),
        lte(stockTxns.created_at, endDate)
      ),
      with: {
        product: true
      }
    })

    // ✅ AGGREGATE DATA BY PRODUCT
    const productAnalysis = new Map<string, {
      productId: string
      productName: string
      sku: string
      currentStock: number
      totalInbound: number
      totalOutbound: number
      averageStock: number
      standardCost: number
      reorderPoint: number
    }>()

    // Process current stock
    currentStock.forEach(lot => {
      const productId = lot.product_id
      const qty = parseFloat(lot.qty)
      
      if (!productAnalysis.has(productId)) {
        productAnalysis.set(productId, {
          productId,
          productName: lot.product?.name || 'Unknown Product',
          sku: lot.product?.sku || productId.slice(-6).toUpperCase(),
          currentStock: 0,
          totalInbound: 0,
          totalOutbound: 0,
          averageStock: 0,
          standardCost: parseFloat(lot.product?.standard_cost || '0'),
          reorderPoint: parseFloat(lot.product?.reorder_point || '0')
        })
      }
      
      productAnalysis.get(productId)!.currentStock += qty
    })

    // Process transactions
    transactions.forEach(txn => {
      const productId = txn.product_id
      const qty = parseFloat(txn.qty)
      
      if (!productAnalysis.has(productId)) {
        productAnalysis.set(productId, {
          productId,
          productName: txn.product?.name || 'Unknown Product',
          sku: txn.product?.sku || productId.slice(-6).toUpperCase(),
          currentStock: 0,
          totalInbound: 0,
          totalOutbound: 0,
          averageStock: 0,
          standardCost: parseFloat(txn.product?.standard_cost || '0'),
          reorderPoint: parseFloat(txn.product?.reorder_point || '0')
        })
      }
      
      const productData = productAnalysis.get(productId)!
      
      if (txn.type === 'inbound') {
        productData.totalInbound += qty
      } else if (txn.type === 'outbound') {
        productData.totalOutbound += qty
      }
    })

    // ✅ CALCULATE TURNOVER METRICS
    const turnoverAnalysis = Array.from(productAnalysis.values()).map(product => {
      // Calculate average stock (simplified - would use more sophisticated method in production)
      const averageStock = (product.currentStock + product.totalInbound - product.totalOutbound) / 2
      product.averageStock = Math.max(averageStock, product.currentStock)
      
      // Calculate turnover ratio (annualized)
      const annualizedUsage = (product.totalOutbound / daysInPeriod) * 365
      const turnoverRatio = product.averageStock > 0 ? annualizedUsage / product.averageStock : 0
      
      // Calculate days on hand
      const dailyUsage = product.totalOutbound / daysInPeriod
      const daysOnHand = dailyUsage > 0 ? product.currentStock / dailyUsage : 999
      
      // Calculate average monthly usage
      const averageMonthlyUsage = (product.totalOutbound / daysInPeriod) * 30
      
      // Classify movement speed
      let classification: 'fast' | 'medium' | 'slow' | 'dead'
      if (turnoverRatio >= 6) classification = 'fast'
      else if (turnoverRatio >= 3) classification = 'medium'
      else if (turnoverRatio >= 1) classification = 'slow'
      else classification = 'dead'
      
      // Generate suggested action
      let suggestedAction = 'Monitor'
      if (classification === 'dead') {
        suggestedAction = 'Consider liquidation or discontinuation'
      } else if (classification === 'slow') {
        suggestedAction = 'Reduce stock levels, review demand'
      } else if (classification === 'fast' && product.currentStock < product.reorderPoint) {
        suggestedAction = 'Reorder immediately - low stock'
      } else if (classification === 'fast') {
        suggestedAction = 'Optimize reorder quantities'
      }
      
      return {
        productId: product.productId,
        productName: product.productName,
        sku: product.sku,
        currentStock: Math.round(product.currentStock * 100) / 100,
        averageMonthlyUsage: Math.round(averageMonthlyUsage * 100) / 100,
        turnoverRatio: Math.round(turnoverRatio * 100) / 100,
        daysOnHand: Math.round(Math.min(daysOnHand, 999) * 10) / 10,
        classification,
        reorderPoint: product.reorderPoint,
        suggestedAction,
        
        // ✅ ADDITIONAL METRICS
        totalValue: Math.round(product.currentStock * product.standardCost * 100) / 100,
        velocityScore: Math.round(turnoverRatio * 10), // 0-100 scale
        stockoutRisk: product.currentStock <= product.reorderPoint ? 'high' : 
                     product.currentStock <= product.reorderPoint * 1.5 ? 'medium' : 'low',
        
        // ✅ PERFORMANCE INDICATORS
        isSlowMoving: classification === 'slow' || classification === 'dead',
        isOverstocked: daysOnHand > 90,
        needsReorder: product.currentStock <= product.reorderPoint,
        
        // ✅ TREND ANALYSIS (simplified)
        trend: turnoverRatio > 4 ? 'increasing' : turnoverRatio < 2 ? 'decreasing' : 'stable'
      }
    })

    // ✅ FILTER BY CLASSIFICATION IF SPECIFIED
    const filteredAnalysis = validatedParams.classification && validatedParams.classification !== 'all' ?
      turnoverAnalysis.filter(item => item.classification === validatedParams.classification) :
      turnoverAnalysis

    // ✅ SORT RESULTS
    const sortedAnalysis = filteredAnalysis.sort((a, b) => {
      let aValue: number | string, bValue: number | string
      
      switch (validatedParams.sortBy) {
        case 'turnoverRatio':
          aValue = a.turnoverRatio
          bValue = b.turnoverRatio
          break
        case 'daysOnHand':
          aValue = a.daysOnHand
          bValue = b.daysOnHand
          break
        case 'currentStock':
          aValue = a.currentStock
          bValue = b.currentStock
          break
        case 'productName':
          aValue = a.productName.toLowerCase()
          bValue = b.productName.toLowerCase()
          break
        default:
          aValue = a.turnoverRatio
          bValue = b.turnoverRatio
      }
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return validatedParams.sortOrder === 'asc' ? 
          aValue.localeCompare(bValue) : bValue.localeCompare(aValue)
      } else {
        return validatedParams.sortOrder === 'asc' ? 
          (aValue as number) - (bValue as number) : (bValue as number) - (aValue as number)
      }
    })

    // ✅ CALCULATE SUMMARY STATISTICS
    const summary = {
      totalProducts: turnoverAnalysis.length,
      averageTurnoverRatio: turnoverAnalysis.length > 0 ? 
        Math.round((turnoverAnalysis.reduce((sum, p) => sum + p.turnoverRatio, 0) / turnoverAnalysis.length) * 100) / 100 : 0,
      
      classificationBreakdown: {
        fast: turnoverAnalysis.filter(p => p.classification === 'fast').length,
        medium: turnoverAnalysis.filter(p => p.classification === 'medium').length,
        slow: turnoverAnalysis.filter(p => p.classification === 'slow').length,
        dead: turnoverAnalysis.filter(p => p.classification === 'dead').length,
      },
      
      actionItems: {
        needReorder: turnoverAnalysis.filter(p => p.needsReorder).length,
        overstocked: turnoverAnalysis.filter(p => p.isOverstocked).length,
        slowMoving: turnoverAnalysis.filter(p => p.isSlowMoving).length,
        highRisk: turnoverAnalysis.filter(p => p.stockoutRisk === 'high').length,
      },
      
      totalInventoryValue: Math.round(turnoverAnalysis.reduce((sum, p) => sum + p.totalValue, 0) * 100) / 100,
      
      topPerformers: sortedAnalysis.slice(0, 5).map(p => ({
        productName: p.productName,
        turnoverRatio: p.turnoverRatio
      })),
      
      bottomPerformers: sortedAnalysis.slice(-5).reverse().map(p => ({
        productName: p.productName,
        turnoverRatio: p.turnoverRatio
      }))
    }

    // ✅ GENERATE RECOMMENDATIONS
    const recommendations = []
    
    if (summary.actionItems.slowMoving > 0) {
      recommendations.push({
        priority: 'high',
        category: 'inventory_optimization',
        title: `Address ${summary.actionItems.slowMoving} slow-moving items`,
        description: 'Review demand patterns and consider reducing stock levels for slow-moving products.',
        estimatedImpact: 'Reduce carrying costs by 15-25%'
      })
    }
    
    if (summary.actionItems.needReorder > 0) {
      recommendations.push({
        priority: 'urgent',
        category: 'stock_replenishment',
        title: `${summary.actionItems.needReorder} products need reordering`,
        description: 'Products have reached or fallen below reorder points.',
        estimatedImpact: 'Prevent stockouts and maintain service levels'
      })
    }
    
    if (summary.averageTurnoverRatio < 3) {
      recommendations.push({
        priority: 'medium',
        category: 'turnover_improvement',
        title: 'Overall turnover below industry average',
        description: 'Consider demand forecasting improvements and inventory optimization.',
        estimatedImpact: 'Improve cash flow and reduce carrying costs'
      })
    }

    // ✅ RESPONSE DATA
    const response = {
      analysis: sortedAnalysis,
      summary,
      recommendations,
      
      filters: {
        appliedTimeRange: validatedParams.timeRange,
        appliedClassification: validatedParams.classification,
        appliedSortBy: validatedParams.sortBy,
        appliedSortOrder: validatedParams.sortOrder,
      },
      
      metadata: {
        timeRange: validatedParams.timeRange,
        dateRange: {
          start: startDateStr,
          end: endDateStr
        },
        daysInPeriod,
        calculatedAt: new Date().toISOString(),
      }
    }

    return jsonOk(response)

  } catch (error) {
    console.error("Error fetching inventory turnover analysis:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid query parameters", 400, error.errors)
    }
    
    return jsonError("Internal server error", 500)
  }
})
