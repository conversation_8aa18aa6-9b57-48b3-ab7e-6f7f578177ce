/**
 * Manufacturing ERP - Inventory Analytics Locations API
 * 
 * Professional API endpoint for location-based inventory analysis.
 * Provides inventory distribution, utilization, and location performance metrics.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 2 Advanced Inventory Management
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { stockLots, stockTxns, products } from "@/lib/schema-postgres"
import { eq, and, sql } from "drizzle-orm"
import { z } from "zod"
import { LocationManager, getLocationForUI, LEGACY_LOCATION_MAPPING } from "@/lib/location-config"

// ✅ PROFESSIONAL: Zod validation schema
const locationsQuerySchema = z.object({
  includeEmpty: z.boolean().default(false),
  sortBy: z.enum(['totalValue', 'totalItems', 'utilizationRate', 'location']).default('totalValue'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

/**
 * ✅ GET /api/inventory/analytics/locations - Get location-based inventory analysis
 * 
 * Analyzes inventory distribution across locations, calculates utilization rates,
 * and provides location performance metrics for optimization.
 * 
 * @param request - HTTP request with query parameters
 * @returns Location-based inventory analysis data
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    const queryParams = {
      includeEmpty: searchParams.get('includeEmpty') === 'true',
      sortBy: searchParams.get('sortBy') || 'totalValue',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    }

    // Validate query parameters
    const validatedParams = locationsQuerySchema.parse(queryParams)

    // ✅ FETCH CURRENT STOCK BY LOCATION
    const stockByLocation = await db.query.stockLots.findMany({
      where: and(
        eq(stockLots.company_id, context.companyId),
        validatedParams.includeEmpty ? undefined : sql`CAST(${stockLots.qty} AS DECIMAL) > 0`
      ),
      with: {
        product: true
      }
    })

    // ✅ FETCH RECENT TRANSACTIONS FOR ACTIVITY ANALYSIS
    const recentDate = new Date()
    recentDate.setDate(recentDate.getDate() - 30) // Last 30 days

    const recentTransactions = await db.query.stockTxns.findMany({
      where: and(
        eq(stockTxns.company_id, context.companyId),
        sql`${stockTxns.created_at} >= ${recentDate.toISOString()}`
      ),
      with: {
        product: true
      }
    })

    // ✅ AGGREGATE DATA BY LOCATION WITH LOCATION CONFIG INTEGRATION
    const locationAnalysis = new Map<string, {
      location: string
      locationConfig?: any
      totalValue: number
      totalItems: number
      totalProducts: number
      productBreakdown: Map<string, { name: string, qty: number, value: number }>
      recentActivity: number
      inboundActivity: number
      outboundActivity: number
    }>()

    // Process current stock
    stockByLocation.forEach(lot => {
      const location = lot.location
      const qty = parseFloat(lot.qty)
      const standardCost = parseFloat(lot.product?.standard_cost || '0')
      const value = qty * standardCost

      if (!locationAnalysis.has(location)) {
        // ✅ GET LOCATION CONFIGURATION
        let locationConfig = LocationManager.getLocationById(location)

        // Fallback: Try legacy location mapping
        if (!locationConfig) {
          locationConfig = getLocationForUI(location)
        }

        // Additional fallback: Try by name
        if (!locationConfig) {
          const allLocations = LocationManager.getAllLocations()
          locationConfig = allLocations.find(loc =>
            loc.name === location ||
            loc.displayName === location ||
            loc.id === location
          )
        }

        locationAnalysis.set(location, {
          location,
          locationConfig,
          totalValue: 0,
          totalItems: 0,
          totalProducts: 0,
          productBreakdown: new Map(),
          recentActivity: 0,
          inboundActivity: 0,
          outboundActivity: 0
        })
      }

      const locationData = locationAnalysis.get(location)!
      locationData.totalValue += value
      locationData.totalItems += qty

      // Product breakdown
      const productId = lot.product_id
      const productName = lot.product?.name || 'Unknown Product'

      if (!locationData.productBreakdown.has(productId)) {
        locationData.productBreakdown.set(productId, {
          name: productName,
          qty: 0,
          value: 0
        })
        locationData.totalProducts++
      }

      const productData = locationData.productBreakdown.get(productId)!
      productData.qty += qty
      productData.value += value
    })

    // Process recent transactions for activity analysis
    recentTransactions.forEach(txn => {
      const location = txn.from_location || txn.to_location || 'Unknown'
      const qty = parseFloat(txn.qty)

      if (locationAnalysis.has(location)) {
        const locationData = locationAnalysis.get(location)!
        locationData.recentActivity += qty

        if (txn.type === 'inbound') {
          locationData.inboundActivity += qty
        } else if (txn.type === 'outbound') {
          locationData.outboundActivity += qty
        }
      }
    })

    // ✅ CALCULATE LOCATION METRICS
    const locations = Array.from(locationAnalysis.values()).map(location => {
      // Calculate utilization rate using actual location capacity if available
      const actualCapacity = location.locationConfig?.capacity || Math.max(location.totalItems * 1.5, 1000)
      const utilizationRate = Math.min((location.totalItems / actualCapacity) * 100, 100)

      // Get top products by value
      const topProducts = Array.from(location.productBreakdown.entries())
        .map(([productId, data]) => ({
          productId,
          productName: data.name,
          quantity: Math.round(data.qty * 100) / 100,
          value: Math.round(data.value * 100) / 100,
          percentage: location.totalValue > 0 ? Math.round((data.value / location.totalValue) * 1000) / 10 : 0
        }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 5)

      // Calculate activity metrics
      const activityScore = location.recentActivity > 0 ?
        Math.min(Math.round((location.recentActivity / 100) * 100), 100) : 0

      const turnoverRate = location.totalItems > 0 ?
        Math.round((location.outboundActivity / location.totalItems) * 1000) / 10 : 0

      return {
        location: location.location,
        locationName: location.locationConfig?.displayName || location.location,
        locationType: location.locationConfig?.type || 'unknown',
        locationIcon: location.locationConfig?.icon || '📦',
        capacity: location.locationConfig?.capacity || actualCapacity,
        totalValue: Math.round(location.totalValue * 100) / 100,
        totalItems: Math.round(location.totalItems * 100) / 100,
        totalProducts: location.totalProducts,
        utilizationRate: Math.round(utilizationRate * 10) / 10,
        topProducts,

        // ✅ ACTIVITY METRICS
        recentActivity: Math.round(location.recentActivity * 100) / 100,
        inboundActivity: Math.round(location.inboundActivity * 100) / 100,
        outboundActivity: Math.round(location.outboundActivity * 100) / 100,
        activityScore,
        turnoverRate,

        // ✅ PERFORMANCE INDICATORS
        performanceGrade: utilizationRate >= 80 ? 'excellent' :
          utilizationRate >= 60 ? 'good' :
            utilizationRate >= 40 ? 'fair' : 'poor',

        isHighActivity: activityScore >= 70,
        isUnderutilized: utilizationRate < 40,
        isOverutilized: utilizationRate > 90,

        // ✅ RECOMMENDATIONS
        recommendations: [
          ...(utilizationRate < 40 ? ['Consider consolidating inventory from underutilized locations'] : []),
          ...(utilizationRate > 90 ? ['Consider expanding capacity or redistributing inventory'] : []),
          ...(turnoverRate < 5 ? ['Low turnover - review product placement and demand'] : []),
          ...(location.recentActivity === 0 ? ['No recent activity - review location necessity'] : [])
        ]
      }
    })

    // ✅ SORT LOCATIONS
    const sortedLocations = locations.sort((a, b) => {
      let aValue: number | string, bValue: number | string

      switch (validatedParams.sortBy) {
        case 'totalValue':
          aValue = a.totalValue
          bValue = b.totalValue
          break
        case 'totalItems':
          aValue = a.totalItems
          bValue = b.totalItems
          break
        case 'utilizationRate':
          aValue = a.utilizationRate
          bValue = b.utilizationRate
          break
        case 'location':
          aValue = a.location.toLowerCase()
          bValue = b.location.toLowerCase()
          break
        default:
          aValue = a.totalValue
          bValue = b.totalValue
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return validatedParams.sortOrder === 'asc' ?
          aValue.localeCompare(bValue) : bValue.localeCompare(aValue)
      } else {
        return validatedParams.sortOrder === 'asc' ?
          (aValue as number) - (bValue as number) : (bValue as number) - (aValue as number)
      }
    })

    // ✅ CALCULATE SUMMARY STATISTICS
    const summary = {
      totalLocations: sortedLocations.length,
      totalInventoryValue: Math.round(sortedLocations.reduce((sum, loc) => sum + loc.totalValue, 0) * 100) / 100,
      totalInventoryItems: Math.round(sortedLocations.reduce((sum, loc) => sum + loc.totalItems, 0) * 100) / 100,
      averageUtilization: sortedLocations.length > 0 ?
        Math.round((sortedLocations.reduce((sum, loc) => sum + loc.utilizationRate, 0) / sortedLocations.length) * 10) / 10 : 0,

      performanceDistribution: {
        excellent: sortedLocations.filter(loc => loc.performanceGrade === 'excellent').length,
        good: sortedLocations.filter(loc => loc.performanceGrade === 'good').length,
        fair: sortedLocations.filter(loc => loc.performanceGrade === 'fair').length,
        poor: sortedLocations.filter(loc => loc.performanceGrade === 'poor').length,
      },

      utilizationAnalysis: {
        underutilized: sortedLocations.filter(loc => loc.isUnderutilized).length,
        optimal: sortedLocations.filter(loc => !loc.isUnderutilized && !loc.isOverutilized).length,
        overutilized: sortedLocations.filter(loc => loc.isOverutilized).length,
      },

      activityAnalysis: {
        highActivity: sortedLocations.filter(loc => loc.isHighActivity).length,
        lowActivity: sortedLocations.filter(loc => !loc.isHighActivity).length,
        noActivity: sortedLocations.filter(loc => loc.recentActivity === 0).length,
      },

      mostActiveLocations: [...sortedLocations]
        .sort((a, b) => b.recentActivity - a.recentActivity)
        .slice(0, 3)
        .map(loc => ({
          location: loc.location,
          activity: loc.recentActivity
        }))
    }

    // ✅ CALCULATE TOP LOCATIONS BY VALUE AFTER SUMMARY IS DEFINED
    summary.topLocationsByValue = sortedLocations.slice(0, 3).map(loc => ({
      location: loc.location,
      value: loc.totalValue,
      percentage: summary.totalInventoryValue > 0 ?
        Math.round((loc.totalValue / summary.totalInventoryValue) * 1000) / 10 : 0
    }))

    // ✅ GENERATE OPTIMIZATION RECOMMENDATIONS
    const optimizationRecommendations = []

    if (summary.utilizationAnalysis.underutilized > 0) {
      optimizationRecommendations.push({
        priority: 'medium',
        category: 'space_optimization',
        title: `${summary.utilizationAnalysis.underutilized} underutilized locations`,
        description: 'Consider consolidating inventory to improve space efficiency.',
        estimatedImpact: 'Reduce storage costs by 10-20%'
      })
    }

    if (summary.utilizationAnalysis.overutilized > 0) {
      optimizationRecommendations.push({
        priority: 'high',
        category: 'capacity_expansion',
        title: `${summary.utilizationAnalysis.overutilized} overutilized locations`,
        description: 'Consider expanding capacity or redistributing inventory.',
        estimatedImpact: 'Improve operational efficiency and reduce congestion'
      })
    }

    if (summary.activityAnalysis.noActivity > 0) {
      optimizationRecommendations.push({
        priority: 'low',
        category: 'location_review',
        title: `${summary.activityAnalysis.noActivity} locations with no recent activity`,
        description: 'Review necessity of inactive locations.',
        estimatedImpact: 'Potential cost savings from location consolidation'
      })
    }

    // ✅ RESPONSE DATA
    const response = {
      locations: sortedLocations,
      summary,
      optimizationRecommendations,

      filters: {
        appliedIncludeEmpty: validatedParams.includeEmpty,
        appliedSortBy: validatedParams.sortBy,
        appliedSortOrder: validatedParams.sortOrder,
      },

      metadata: {
        analysisDate: new Date().toISOString().split('T')[0],
        calculatedAt: new Date().toISOString(),
      }
    }

    return jsonOk(response)

  } catch (error) {
    console.error("Error fetching location analysis:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Invalid query parameters", 400, error.errors)
    }

    return jsonError("Internal server error", 500)
  }
})
