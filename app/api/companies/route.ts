import { createErrorResponse, createSuccessResponse } from '@/lib/api-helpers'
import { db, uid } from '@/lib/db'
import { companies } from '@/lib/schema-postgres'
import { getSession, withApiAuthRequired } from '@auth0/nextjs-auth0'
import { eq } from 'drizzle-orm'
import { NextRequest } from 'next/server'

// GET /api/companies - Get current user's company profile
export const GET = withApiAuthRequired(async function GET(request: NextRequest) {
  try {
    console.log('🔍 Companies API: Fetching company profile')
    const session = await getSession(request)
    if (!session?.user) {
      console.log('❌ Companies API: No session found')
      return createErrorResponse('Unauthorized', 401)
    }

    console.log('🔍 Companies API: User authenticated:', session.user.email)
    const company = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, session.user.sub),
    })

    console.log('🔍 Companies API: Company found:', company ? company.name : 'null')
    if (!company) {
      return createSuccessResponse({ company: null })
    }

    return createSuccessResponse({ company })
  } catch (error) {
    console.error('❌ Companies API: Error fetching company:', error)
    return createErrorResponse('Failed to fetch company profile')
  }
})

// POST /api/companies - Create or update company profile
export const POST = withApiAuthRequired(async function POST(request: NextRequest) {
  try {
    const session = await getSession(request)
    if (!session?.user) {
      return createErrorResponse('Unauthorized', 401)
    }

    const body = await request.json()
    console.log('🔍 Received body data:', JSON.stringify(body, null, 2))

    // Validate required fields
    if (!body.name || !body.email) {
      return createErrorResponse('Company name and email are required', 400)
    }

    // Check if company already exists
    const existingCompany = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, session.user.sub),
    })

    // Helper function to handle empty strings and null values
    const sanitizeField = (value: any): string | null => {
      if (value === undefined || value === null || value === '') {
        return null
      }
      return String(value).trim() || null
    }

    const companyData = {
      auth0_user_id: session.user.sub,
      name: body.name, // Required field, don't sanitize
      legal_name: sanitizeField(body.legal_name),
      email: body.email, // Required field, don't sanitize
      phone: sanitizeField(body.phone),
      website: sanitizeField(body.website),
      address_line1: sanitizeField(body.address_line1 || body.address), // Support both field names
      address_line2: sanitizeField(body.address_line2),
      city: sanitizeField(body.city),
      state_province: sanitizeField(body.state_province),
      postal_code: sanitizeField(body.postal_code),
      country: sanitizeField(body.country),
      industry: sanitizeField(body.industry),
      business_type: sanitizeField(body.business_type),
      employee_count: sanitizeField(body.employee_count),
      annual_revenue: sanitizeField(body.annual_revenue),
      registration_number: sanitizeField(body.registration_number),
      tax_id: sanitizeField(body.tax_id),
      vat_number: sanitizeField(body.vat_number),
      bank_name: sanitizeField(body.bank_name || body.bank), // Support both field names
      bank_account: sanitizeField(body.bank_account),
      bank_swift: sanitizeField(body.bank_swift),
      bank_address: sanitizeField(body.bank_address),
      bank_iban: sanitizeField(body.bank_iban),
      currency: body.currency || 'USD',
      timezone: body.timezone || 'UTC',
      language: body.language || 'en',
      export_license: sanitizeField(body.export_license),
      customs_code: sanitizeField(body.customs_code),
      preferred_incoterms: sanitizeField(body.preferred_incoterms || body.incoterm), // Support both field names
      preferred_payment_terms: sanitizeField(body.preferred_payment_terms || body.payment_term), // Support both field names
      onboarding_completed: body.onboarding_completed === 'true' || body.onboarding_completed === true ? 'true' : 'false',
      status: body.status || 'active',
      updated_at: new Date(),
    }

    console.log('🔍 Prepared companyData for database:', JSON.stringify(companyData, null, 2))

    let company
    if (existingCompany) {
      // Update existing company
      console.log('🔍 Updating existing company:', existingCompany.id)
      console.log('🔍 Fields being updated:', Object.keys(companyData).filter(key => companyData[key as keyof typeof companyData] !== null))

      const updateResult = await db.update(companies)
        .set(companyData)
        .where(eq(companies.id, existingCompany.id))
        .returning()

      console.log('🔍 Update result count:', updateResult.length)
      console.log('🔍 Updated company data:', JSON.stringify(updateResult[0], null, 2))

      // Fetch the updated company to ensure we have the latest data
      company = await db.query.companies.findFirst({
        where: eq(companies.id, existingCompany.id),
      })

      console.log('🔍 Final company state:', JSON.stringify(company, null, 2))
    } else {
      // Create new company
      const companyId = uid('company')
      console.log('🔍 Creating new company:', companyId)
      await db.insert(companies).values({
        id: companyId,
        ...companyData,
      })

      company = await db.query.companies.findFirst({
        where: eq(companies.id, companyId),
      })
    }

    return createSuccessResponse({
      company,
      message: existingCompany ? 'Company profile updated successfully' : 'Company profile created successfully'
    })
  } catch (error) {
    console.error('Error creating/updating company:', error)
    return createErrorResponse('Failed to save company profile')
  }
})

// PUT /api/companies - Update company profile (alias for POST)
export const PUT = withApiAuthRequired(async function PUT(request: NextRequest) {
  return POST(request)
})
