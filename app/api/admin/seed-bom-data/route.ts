/**
 * Manufacturing ERP - Seed BOM Data API
 * 
 * Creates sample Bill of Materials data for existing products to enable
 * demand forecasting and procurement planning functionality.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1C MRP Implementation
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db, uid } from "@/lib/db"
import { 
  products, 
  rawMaterials, 
  billOfMaterials, 
  suppliers 
} from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"

/**
 * ✅ POST /api/admin/seed-bom-data
 * Create sample BOM data for existing products
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    // Get existing products for this company
    const existingProducts = await db.query.products.findMany({
      where: eq(products.company_id, context.companyId),
      limit: 5, // Limit to first 5 products
    })

    if (existingProducts.length === 0) {
      return jsonError("No products found to create BOM data for", 400)
    }

    // Get or create raw materials
    let existingRawMaterials = await db.query.rawMaterials.findMany({
      where: eq(rawMaterials.company_id, context.companyId),
    })

    // Create sample raw materials if none exist
    if (existingRawMaterials.length === 0) {
      // First, get or create a supplier
      let supplier = await db.query.suppliers.findFirst({
        where: eq(suppliers.company_id, context.companyId),
      })

      if (!supplier) {
        const supplierId = uid("sup")
        await db.insert(suppliers).values({
          id: supplierId,
          company_id: context.companyId,
          name: "Premium Materials Supplier",
          email: "<EMAIL>",
          phone: "******-0456",
          address: "456 Supplier Ave",
          city: "Material City",
          state: "MC",
          postal_code: "12345",
          country: "USA",
        })
        
        supplier = { id: supplierId } as any
      }

      // Create sample raw materials
      const sampleRawMaterials = [
        {
          id: uid("rm"),
          company_id: context.companyId,
          sku: "YARN-COTTON-30S",
          name: "Cotton Yarn 30s",
          category: "yarn",
          unit: "kg",
          primary_supplier_id: supplier.id,
          composition: "100% Cotton",
          quality_grade: "Premium",
          standard_cost: "4.50",
          currency: "USD",
          reorder_point: "100",
          max_stock_level: "1000",
          inspection_required: "true",
        },
        {
          id: uid("rm"),
          company_id: context.companyId,
          sku: "FABRIC-POLY-150GSM",
          name: "Polyester Fabric 150GSM",
          category: "fabric",
          unit: "meters",
          primary_supplier_id: supplier.id,
          composition: "100% Polyester",
          quality_grade: "Standard",
          standard_cost: "3.20",
          currency: "USD",
          reorder_point: "50",
          max_stock_level: "500",
          inspection_required: "true",
        },
        {
          id: uid("rm"),
          company_id: context.companyId,
          sku: "DYE-REACTIVE-BLUE",
          name: "Reactive Blue Dye",
          category: "dyes",
          unit: "kg",
          primary_supplier_id: supplier.id,
          composition: "Reactive Dye",
          quality_grade: "Industrial",
          standard_cost: "12.00",
          currency: "USD",
          reorder_point: "25",
          max_stock_level: "200",
          inspection_required: "true",
        },
      ]

      await db.insert(rawMaterials).values(sampleRawMaterials)
      
      // Refresh the raw materials list
      existingRawMaterials = await db.query.rawMaterials.findMany({
        where: eq(rawMaterials.company_id, context.companyId),
      })
    }

    // Create BOM entries for each product
    const createdBomItems = []
    
    for (const product of existingProducts) {
      // Check if BOM already exists for this product
      const existingBom = await db.query.billOfMaterials.findFirst({
        where: and(
          eq(billOfMaterials.company_id, context.companyId),
          eq(billOfMaterials.product_id, product.id)
        ),
      })

      if (existingBom) {
        continue // Skip if BOM already exists
      }

      // Create BOM items for this product (use first 2-3 raw materials)
      const materialsToUse = existingRawMaterials.slice(0, Math.min(3, existingRawMaterials.length))
      
      for (let i = 0; i < materialsToUse.length; i++) {
        const material = materialsToUse[i]
        
        // Calculate realistic quantities based on material type
        let qtyRequired = "1.0"
        let wasteFactor = "0.05"
        
        if (material.category === "yarn") {
          qtyRequired = "0.5" // 0.5 kg yarn per product unit
          wasteFactor = "0.10" // 10% waste for yarn
        } else if (material.category === "fabric") {
          qtyRequired = "1.2" // 1.2 meters fabric per product unit
          wasteFactor = "0.05" // 5% waste for fabric
        } else if (material.category === "dyes") {
          qtyRequired = "0.05" // 0.05 kg dye per product unit
          wasteFactor = "0.02" // 2% waste for dyes
        }

        const bomItem = {
          id: uid("bom"),
          company_id: context.companyId,
          product_id: product.id,
          raw_material_id: material.id,
          qty_required: qtyRequired,
          unit: material.unit,
          waste_factor: wasteFactor,
          status: "active",
        }

        await db.insert(billOfMaterials).values(bomItem)
        createdBomItems.push({
          productName: product.name,
          productSku: product.sku,
          materialName: material.name,
          materialSku: material.sku,
          qtyRequired,
          unit: material.unit,
          wasteFactor,
        })
      }
    }

    return jsonOk({
      message: "BOM data seeded successfully",
      summary: {
        productsProcessed: existingProducts.length,
        rawMaterialsAvailable: existingRawMaterials.length,
        bomItemsCreated: createdBomItems.length,
      },
      createdBomItems,
      nextSteps: [
        "BOM data is now available for demand forecasting",
        "Approved forecasts will generate procurement plans",
        "Material requirements can be calculated from forecasts",
      ],
    }, { status: 201 })
  } catch (error) {
    console.error("Error seeding BOM data:", error)
    return jsonError(
      "Failed to seed BOM data",
      500,
      { error: error instanceof Error ? error.message : "Unknown error" }
    )
  }
})
