/**
 * Manufacturing ERP - Generate AR Invoice from Sales Contract
 * Professional contract-to-invoice workflow automation
 */

import { jsonError, jsonOk } from "@/lib/api-helpers"
import { withTenantAuth } from "@/lib/tenant-utils"
import { createFinancialService } from "@/lib/services/financial-integration"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const generateInvoiceSchema = z.object({
  invoice_number: z.string().optional(),
  due_date: z.string().optional(),
  payment_terms: z.string().default("Net 30"),
  notes: z.string().optional(),
  partial_amount: z.number().optional(),
  preview: z.boolean().default(false),
})

// ============================================================================
// API ENDPOINTS
// ============================================================================

/**
 * Generate AR Invoice from Sales Contract
 * POST /api/contracts/sales/[id]/generate-invoice
 */
export const POST = withTenantAuth(async function POST(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: contractId } = await params
    const body = await request.json()
    
    // Validate request body
    const data = generateInvoiceSchema.parse(body)

    // Initialize financial service
    const financialService = createFinancialService(context)

    // If preview mode, return preview without creating
    if (data.preview) {
      // Get contract details for preview
      const preview = await financialService.previewInvoiceFromContract(contractId, {
        partialAmount: data.partial_amount,
        paymentTerms: data.payment_terms,
      })
      
      return jsonOk({
        preview,
        message: `Preview: Invoice would be created for ${preview.currency} ${preview.amount}`,
      })
    }

    // Generate actual invoice
    const invoice = await financialService.generateARInvoiceFromContract({
      contractId,
      invoiceNumber: data.invoice_number,
      dueDate: data.due_date,
      paymentTerms: data.payment_terms,
      notes: data.notes,
      partialAmount: data.partial_amount,
    })

    return jsonOk({
      invoice,
      message: `Invoice ${invoice.number} generated successfully from contract`,
    }, { status: 201 })

  } catch (error) {
    console.error("Generate invoice error:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Invalid request data", 400, error.errors)
    }
    
    return jsonError(
      error instanceof Error ? error.message : "Failed to generate invoice",
      500
    )
  }
})

/**
 * Get invoice generation preview
 * GET /api/contracts/sales/[id]/generate-invoice
 */
export const GET = withTenantAuth(async function GET(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: contractId } = await params
    const url = new URL(request.url)
    const partialAmount = url.searchParams.get("partial_amount")

    const financialService = createFinancialService(context)
    
    const preview = await financialService.previewInvoiceFromContract(contractId, {
      partialAmount: partialAmount ? parseFloat(partialAmount) : undefined,
    })

    return jsonOk({
      preview,
      canGenerate: preview.status === "ready",
      message: preview.status === "ready" 
        ? "Contract is ready for invoice generation"
        : `Cannot generate invoice: ${preview.reason}`,
    })

  } catch (error) {
    console.error("Invoice preview error:", error)
    return jsonError(
      error instanceof Error ? error.message : "Failed to preview invoice",
      500
    )
  }
})
