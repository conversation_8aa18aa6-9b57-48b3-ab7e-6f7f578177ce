/**
 * Manufacturing ERP - Sales Contract MRP Integration API
 * 
 * Professional API endpoint for approving sales contracts with automatic MRP workflow integration.
 * Demonstrates seamless integration between contract approval and MRP system.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Integration
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { mrpWorkflowIntegrationService } from "@/lib/services/mrp-workflow-integration"
import { db } from "@/lib/db"
import { salesContracts } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"

// ✅ PROFESSIONAL: Zod validation schema
const approveContractSchema = z.object({
  notes: z.string().optional(),
  autoGenerateMRP: z.boolean().default(true),
  workOrderPriority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal'),
  autoCreateQualityInspections: z.boolean().default(true),
})

/**
 * ✅ POST /api/contracts/sales/[id]/approve-with-mrp - Approve contract with MRP integration
 * 
 * Approves a sales contract and automatically triggers MRP workflow integration.
 * Generates demand forecasts, work orders, and procurement plans.
 * 
 * @param request - HTTP request with approval options
 * @param context - Tenant context with company and user information
 * @param params - Route parameters containing contract ID
 * @returns Contract approval result with MRP integration details
 */
export const POST = withTenantAuth(async function POST(
  request: NextRequest, 
  context, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: contractId } = await params
    const body = await request.json()
    
    // Validate request body
    const validatedData = approveContractSchema.parse(body)
    
    // 1. Get contract details
    const contract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, contractId),
        eq(salesContracts.company_id, context.companyId)
      ),
      with: {
        items: {
          with: {
            product: true
          }
        },
        customer: true
      }
    })
    
    if (!contract) {
      return jsonError("Sales contract not found", 404)
    }
    
    if (contract.status === 'approved') {
      return jsonError("Contract is already approved", 400)
    }
    
    // 2. Update contract status to approved
    await db.update(salesContracts)
      .set({
        status: 'approved',
        approved_by: context.userId,
        approved_at: new Date().toISOString(),
        notes: validatedData.notes || contract.notes,
        updated_at: new Date()
      })
      .where(and(
        eq(salesContracts.id, contractId),
        eq(salesContracts.company_id, context.companyId)
      ))
    
    let mrpIntegrationResult = null
    
    // 3. Trigger MRP workflow integration if enabled
    if (validatedData.autoGenerateMRP) {
      try {
        mrpIntegrationResult = await mrpWorkflowIntegrationService.handleWorkflowTrigger({
          eventType: 'contract_approved',
          entityId: contractId,
          entityType: 'sales_contract',
          companyId: context.companyId,
          triggeredBy: context.userId,
          data: {
            workOrderPriority: validatedData.workOrderPriority,
            autoCreateQualityInspections: validatedData.autoCreateQualityInspections,
            contractNumber: contract.number,
            customerName: contract.customer?.name,
            totalValue: contract.total_value
          }
        })
      } catch (error) {
        console.error("Error in MRP workflow integration:", error)
        // Don't fail the contract approval if MRP integration fails
        mrpIntegrationResult = {
          success: false,
          errors: [error instanceof Error ? error.message : 'MRP integration failed'],
          warnings: [],
          triggeredActions: [],
          generatedEntities: []
        }
      }
    }
    
    // 4. Prepare response
    const response = {
      contractId,
      contractNumber: contract.number,
      status: 'approved',
      approvedBy: context.userId,
      approvedAt: new Date().toISOString(),
      mrpIntegration: mrpIntegrationResult ? {
        enabled: true,
        success: mrpIntegrationResult.success,
        generatedEntities: mrpIntegrationResult.generatedEntities,
        triggeredActions: mrpIntegrationResult.triggeredActions,
        warnings: mrpIntegrationResult.warnings,
        errors: mrpIntegrationResult.errors
      } : {
        enabled: false,
        reason: 'MRP integration disabled by user'
      }
    }
    
    return jsonOk(response, { status: 200 })
  } catch (error) {
    console.error("Error approving contract with MRP integration:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, {
        errors: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
      })
    }
    
    return jsonError("Internal server error", 500)
  }
})

/**
 * ✅ GET /api/contracts/sales/[id]/approve-with-mrp - Get approval preview
 * 
 * Returns a preview of what would be generated if the contract is approved with MRP integration.
 * 
 * @param request - HTTP request
 * @param context - Tenant context
 * @param params - Route parameters containing contract ID
 * @returns Preview of MRP integration results
 */
export const GET = withTenantAuth(async function GET(
  request: NextRequest, 
  context, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: contractId } = await params
    
    // Get contract details
    const contract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, contractId),
        eq(salesContracts.company_id, context.companyId)
      ),
      with: {
        items: {
          with: {
            product: {
              with: {
                bomItems: {
                  with: {
                    rawMaterial: true
                  }
                }
              }
            }
          }
        },
        customer: true
      }
    })
    
    if (!contract) {
      return jsonError("Sales contract not found", 404)
    }
    
    // Calculate what would be generated
    const preview = {
      contractId,
      contractNumber: contract.number,
      customerName: contract.customer?.name,
      totalValue: contract.total_value,
      estimatedGeneration: {
        demandForecasts: contract.items.length,
        workOrders: contract.items.length,
        procurementPlans: contract.items.reduce((total, item) => {
          return total + (item.product?.bomItems?.length || 0)
        }, 0),
        qualityInspections: contract.items.length * 3 // incoming, in-process, final
      },
      productDetails: contract.items.map(item => ({
        productId: item.product_id,
        productName: item.product?.name,
        quantity: item.qty,
        bomMaterials: item.product?.bomItems?.map(bomItem => ({
          materialId: bomItem.raw_material_id,
          materialName: bomItem.rawMaterial?.name,
          requiredQuantity: parseFloat(bomItem.qty_required) * parseInt(item.qty)
        })) || []
      })),
      warnings: contract.status === 'approved' ? ['Contract is already approved'] : []
    }
    
    return jsonOk(preview)
  } catch (error) {
    console.error("Error getting approval preview:", error)
    return jsonError("Internal server error", 500)
  }
})

/**
 * ✅ OPTIONS /api/contracts/sales/[id]/approve-with-mrp - CORS preflight
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
