/**
 * Manufacturing ERP - Raw Materials API
 * RESTful endpoints for raw materials management with multi-tenant security
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db, uid } from "@/lib/db"
import { rawMaterials, suppliers } from "@/lib/schema-postgres"
import { eq, and, desc, like, or } from "drizzle-orm"
import { z } from "zod"

// ✅ VALIDATION SCHEMA
const rawMaterialSchema = z.object({
  sku: z.string().min(1, "SKU is required"),
  name: z.string().min(1, "Name is required"),
  category: z.enum(["yarn", "fabric", "dyes", "chemicals", "accessories", "other"], {
    errorMap: () => ({ message: "Invalid category" })
  }),
  unit: z.string().min(1, "Unit is required"),
  primary_supplier_id: z.string().optional(),
  composition: z.string().optional(),
  quality_grade: z.string().optional(),
  specifications: z.string().optional(),
  standard_cost: z.string().optional(),
  currency: z.string().default("USD"),
  reorder_point: z.string().default("0"),
  max_stock_level: z.string().default("0"),
  lead_time_days: z.string().default("7"),
  inspection_required: z.enum(["true", "false"]).default("false"),
  quality_tolerance: z.string().optional(),
  quality_notes: z.string().optional(),
  status: z.enum(["active", "inactive", "discontinued"]).default("active"),
})

// ✅ GET - List raw materials with search and filtering
export const GET = withTenantAuth(async function GET(request: Request, context) {
  try {
    const url = new URL(request.url)
    const search = url.searchParams.get("search") || ""
    const category = url.searchParams.get("category") || ""
    const status = url.searchParams.get("status") || ""
    const limit = parseInt(url.searchParams.get("limit") || "50")
    const offset = parseInt(url.searchParams.get("offset") || "0")

    // Build where conditions
    const conditions = [eq(rawMaterials.company_id, context.companyId)]

    // Search filter
    if (search) {
      conditions.push(
        or(
          like(rawMaterials.name, `%${search}%`),
          like(rawMaterials.sku, `%${search}%`),
          like(rawMaterials.composition, `%${search}%`)
        )
      )
    }

    // Category filter
    if (category && category !== "all") {
      conditions.push(eq(rawMaterials.category, category))
    }

    // Status filter
    if (status && status !== "all") {
      conditions.push(eq(rawMaterials.status, status))
    }

    // Fetch raw materials with simplified relationships for debugging
    const materials = await db.query.rawMaterials.findMany({
      where: and(...conditions),
      orderBy: [desc(rawMaterials.created_at)],
      limit,
      offset,
      with: {
        primarySupplier: true,
        lots: {
          where: (lots, { eq }) => eq(lots.status, "available"),
          limit: 10, // More lots for KPI calculations
          orderBy: (lots, { desc }) => [desc(lots.created_at)],
        },
      },
    })

    // Calculate summary statistics
    const totalMaterials = await db.query.rawMaterials.findMany({
      where: and(...conditions),
    })

    return jsonOk({
      materials,
      pagination: {
        total: totalMaterials.length,
        limit,
        offset,
        hasMore: totalMaterials.length > offset + limit,
      },
    })
  } catch (error) {
    console.error("Raw Materials GET Error:", error)
    return jsonError("Failed to fetch raw materials", 500)
  }
})

// ✅ POST - Create new raw material
export const POST = withTenantAuth(async function POST(request: Request, context) {
  try {
    const body = await request.json()
    const validatedData = rawMaterialSchema.parse(body)

    // Check for duplicate SKU
    const existingMaterial = await db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.company_id, context.companyId),
        eq(rawMaterials.sku, validatedData.sku)
      ),
    })

    if (existingMaterial) {
      return jsonError("Raw material with this SKU already exists", 400)
    }

    // Validate supplier if provided
    if (validatedData.primary_supplier_id) {
      const supplier = await db.query.suppliers.findFirst({
        where: and(
          eq(suppliers.id, validatedData.primary_supplier_id),
          eq(suppliers.company_id, context.companyId)
        ),
      })

      if (!supplier) {
        return jsonError("Invalid supplier ID", 400)
      }
    }

    // Create raw material
    const newMaterial = {
      id: uid("rm"),
      company_id: context.companyId,
      ...validatedData,
    }

    await db.insert(rawMaterials).values(newMaterial)

    // Fetch the created material with relationships
    const createdMaterial = await db.query.rawMaterials.findFirst({
      where: eq(rawMaterials.id, newMaterial.id),
      with: {
        primarySupplier: true,
      },
    })

    return jsonOk(createdMaterial, { status: 201 })
  } catch (error) {
    console.error("Raw Materials POST Error:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    return jsonError("Failed to create raw material", 500)
  }
})
