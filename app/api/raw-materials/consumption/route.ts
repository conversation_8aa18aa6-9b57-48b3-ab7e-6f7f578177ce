/**
 * Manufacturing ERP - Material Consumption API
 * Track material consumption across work orders
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db, uid } from "@/lib/db"
import { materialConsumption, rawMaterialLots, workOrders, rawMaterials } from "@/lib/schema-postgres"
import { eq, and, desc, gte, lte, sum, sql } from "drizzle-orm"
import { z } from "zod"

// ✅ GET - Get consumption history with filtering
export const GET = withTenantAuth(async function GET(request: Request, context) {
  try {
    const url = new URL(request.url)
    const workOrderId = url.searchParams.get("work_order_id")
    const materialId = url.searchParams.get("material_id")
    const startDate = url.searchParams.get("start_date")
    const endDate = url.searchParams.get("end_date")
    const limit = parseInt(url.searchParams.get("limit") || "50")
    const offset = parseInt(url.searchParams.get("offset") || "0")

    // Build where conditions
    const conditions = [eq(materialConsumption.company_id, context.companyId)]

    if (workOrderId) {
      conditions.push(eq(materialConsumption.work_order_id, workOrderId))
    }

    if (startDate) {
      conditions.push(gte(materialConsumption.consumed_date, startDate))
    }

    if (endDate) {
      conditions.push(lte(materialConsumption.consumed_date, endDate))
    }

    // Fetch consumption records with relationships
    const consumptions = await db.query.materialConsumption.findMany({
      where: and(...conditions),
      orderBy: [desc(materialConsumption.created_at)],
      limit,
      offset,
      with: {
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true,
              },
            },
          },
        },
        rawMaterialLot: {
          with: {
            rawMaterial: true,
            supplier: true,
          },
        },
      },
    })

    // Filter by material if specified (after fetching to use relationships)
    let filteredConsumptions = consumptions
    if (materialId) {
      filteredConsumptions = consumptions.filter(
        c => c.rawMaterialLot?.raw_material_id === materialId
      )
    }

    // Calculate summary statistics
    const totalConsumptions = filteredConsumptions.length
    const totalCost = filteredConsumptions.reduce((sum, c) => {
      return sum + parseFloat(c.total_cost || "0")
    }, 0)
    const totalQty = filteredConsumptions.reduce((sum, c) => {
      return sum + parseFloat(c.qty_consumed || "0")
    }, 0)

    // Group by material for summary
    const materialSummary = filteredConsumptions.reduce((acc, c) => {
      const materialId = c.rawMaterialLot?.raw_material_id
      const materialName = c.rawMaterialLot?.rawMaterial?.name

      if (materialId && materialName) {
        if (!acc[materialId]) {
          acc[materialId] = {
            materialId,
            materialName,
            totalQty: 0,
            totalCost: 0,
            consumptionCount: 0,
          }
        }

        acc[materialId].totalQty += parseFloat(c.qty_consumed || "0")
        acc[materialId].totalCost += parseFloat(c.total_cost || "0")
        acc[materialId].consumptionCount += 1
      }

      return acc
    }, {} as Record<string, any>)

    return jsonOk({
      consumptions: filteredConsumptions,
      summary: {
        totalConsumptions,
        totalCost,
        totalQty,
        materialSummary: Object.values(materialSummary),
      },
      pagination: {
        limit,
        offset,
        hasMore: consumptions.length === limit, // Simplified check
      },
    })
  } catch (error) {
    console.error("Material Consumption GET Error:", error)
    return jsonError("Failed to fetch material consumption", 500)
  }
})

// ✅ POST - Record material consumption (typically called by work order completion)
export const POST = withTenantAuth(async function POST(request: Request, context) {
  try {
    const body = await request.json()

    const consumptionSchema = z.object({
      work_order_id: z.string().min(1, "Work order ID is required"),
      consumptions: z.array(z.object({
        raw_material_lot_id: z.string().min(1, "Raw material lot ID is required"),
        qty_consumed: z.string().min(1, "Quantity consumed is required"),
        notes: z.string().optional(),
      })).min(1, "At least one consumption record is required"),
    })

    const validatedData = consumptionSchema.parse(body)

    // Verify work order exists and belongs to company
    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, validatedData.work_order_id),
        eq(workOrders.company_id, context.companyId)
      ),
    })

    if (!workOrder) {
      return jsonError("Work order not found", 404)
    }

    const consumptionRecords = []

    // Process each consumption record
    for (const consumption of validatedData.consumptions) {
      // Verify lot exists and belongs to company
      const lot = await db.query.rawMaterialLots.findFirst({
        where: and(
          eq(rawMaterialLots.id, consumption.raw_material_lot_id),
          eq(rawMaterialLots.company_id, context.companyId)
        ),
      })

      if (!lot) {
        return jsonError(`Raw material lot ${consumption.raw_material_lot_id} not found`, 404)
      }

      // Check if sufficient quantity is available
      const qtyConsumed = parseFloat(consumption.qty_consumed)
      const availableQty = parseFloat(lot.qty || "0")

      if (qtyConsumed > availableQty) {
        return jsonError(
          `Insufficient quantity in lot ${lot.lot_number}. Available: ${availableQty}, Requested: ${qtyConsumed}`,
          400
        )
      }

      // Calculate costs
      const unitCost = parseFloat(lot.unit_cost || "0")
      const totalCost = (qtyConsumed * unitCost).toString()

      // Create consumption record
      const consumptionRecord = {
        id: uid("mc"),
        company_id: context.companyId,
        work_order_id: validatedData.work_order_id,
        raw_material_lot_id: consumption.raw_material_lot_id,
        qty_consumed: consumption.qty_consumed,
        unit_cost: lot.unit_cost,
        total_cost: totalCost,
        consumed_date: new Date().toISOString().split('T')[0],
        consumed_by: context.userId,
        notes: consumption.notes,
      }

      consumptionRecords.push(consumptionRecord)

      // Update lot quantity
      const newQty = (availableQty - qtyConsumed).toString()
      await db
        .update(rawMaterialLots)
        .set({
          qty: newQty,
          status: parseFloat(newQty) <= 0 ? "consumed" : "available",
          updated_at: new Date(),
        })
        .where(eq(rawMaterialLots.id, consumption.raw_material_lot_id))
    }

    // Insert all consumption records
    await db.insert(materialConsumption).values(consumptionRecords)

    // Fetch created records with relationships
    const createdConsumptions = await db.query.materialConsumption.findMany({
      where: and(
        eq(materialConsumption.company_id, context.companyId),
        eq(materialConsumption.work_order_id, validatedData.work_order_id)
      ),
      orderBy: [desc(materialConsumption.created_at)],
      limit: consumptionRecords.length,
      with: {
        rawMaterialLot: {
          with: {
            rawMaterial: true,
          },
        },
      },
    })

    return jsonOk({
      consumptions: createdConsumptions,
      message: `Successfully recorded ${consumptionRecords.length} material consumption(s)`,
    }, { status: 201 })
  } catch (error) {
    console.error("Material Consumption POST Error:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    return jsonError("Failed to record material consumption", 500)
  }
})
