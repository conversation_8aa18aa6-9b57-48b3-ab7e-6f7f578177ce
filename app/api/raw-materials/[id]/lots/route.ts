/**
 * Manufacturing ERP - Raw Material Lots API
 * Manage raw material lots for individual materials
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db, uid } from "@/lib/db"
import { rawMaterials, rawMaterialLots, suppliers, purchaseContracts } from "@/lib/schema-postgres"
import { eq, and, desc } from "drizzle-orm"
import { z } from "zod"
import { inventoryEventEmitter, InventoryEventType } from "@/lib/events/inventory-events"
import "@/lib/events/inventory-event-init" // Ensure event system is initialized

// ✅ VALIDATION SCHEMA
const rawMaterialLotSchema = z.object({
  lot_number: z.string().optional(),
  supplier_id: z.string().optional(), // Made optional
  purchase_contract_id: z.string().optional(),
  qty: z.string().min(1, "Quantity is required"),
  location: z.string().min(1, "Location is required"),
  unit_cost: z.string().min(1, "Unit cost is required"),
  currency: z.string().default("USD"),
  received_date: z.string().optional(),
  expiry_date: z.string().optional(),
  quality_status: z.enum(["pending", "approved", "rejected", "quarantined"]).default("pending"),
  status: z.enum(["available", "reserved", "consumed", "expired"]).default("available"),
  batch_notes: z.string().optional(),
  inspection_notes: z.string().optional(),
})

// ✅ GET - List lots for a raw material
export const GET = withTenantAuth(async function GET(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Verify raw material exists and belongs to company
    const material = await db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ),
    })

    if (!material) {
      return jsonError("Raw material not found", 404)
    }

    // Fetch lots with relationships
    const lots = await db.query.rawMaterialLots.findMany({
      where: and(
        eq(rawMaterialLots.raw_material_id, id),
        eq(rawMaterialLots.company_id, context.companyId)
      ),
      orderBy: [desc(rawMaterialLots.created_at)],
      with: {
        supplier: true,
        purchaseContract: true,
        inspection: true,
        consumptions: {
          with: {
            workOrder: {
              with: {
                product: true,
              },
            },
          },
        },
      },
    })

    // Calculate summary statistics
    const totalQty = lots.reduce((sum, lot) => sum + parseFloat(lot.qty || "0"), 0)
    const availableQty = lots
      .filter(lot => lot.status === "available")
      .reduce((sum, lot) => sum + parseFloat(lot.qty || "0"), 0)
    const totalValue = lots.reduce((sum, lot) => {
      return sum + (parseFloat(lot.qty || "0") * parseFloat(lot.unit_cost || "0"))
    }, 0)

    return jsonOk({
      lots,
      summary: {
        totalLots: lots.length,
        totalQty,
        availableQty,
        totalValue,
        unit: material.unit,
      },
    })
  } catch (error) {
    console.error("Raw Material Lots GET Error:", error)
    return jsonError("Failed to fetch raw material lots", 500)
  }
})

// ✅ POST - Create new lot for raw material
export const POST = withTenantAuth(async function POST(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = rawMaterialLotSchema.parse(body)

    // Verify raw material exists and belongs to company
    const material = await db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ),
    })

    if (!material) {
      return jsonError("Raw material not found", 404)
    }

    // Validate supplier (if provided)
    let supplier = null
    if (validatedData.supplier_id) {
      supplier = await db.query.suppliers.findFirst({
        where: and(
          eq(suppliers.id, validatedData.supplier_id),
          eq(suppliers.company_id, context.companyId)
        ),
      })

      if (!supplier) {
        return jsonError("Invalid supplier ID", 400)
      }
    }

    // Validate purchase contract if provided
    if (validatedData.purchase_contract_id) {
      const contract = await db.query.purchaseContracts.findFirst({
        where: and(
          eq(purchaseContracts.id, validatedData.purchase_contract_id),
          eq(purchaseContracts.company_id, context.companyId)
        ),
      })

      if (!contract) {
        return jsonError("Invalid purchase contract ID", 400)
      }
    }

    // Calculate total cost
    const qty = parseFloat(validatedData.qty)
    const unitCost = parseFloat(validatedData.unit_cost)
    const totalCost = (qty * unitCost).toString()

    // Generate lot number if not provided
    const lotNumber = validatedData.lot_number || `LOT-${Date.now()}`

    // Create lot (handle empty strings as null for foreign keys)
    const newLot = {
      id: uid("rml"),
      company_id: context.companyId,
      raw_material_id: id,
      lot_number: lotNumber,
      total_cost: totalCost,
      received_date: validatedData.received_date || new Date().toISOString().split('T')[0],
      status: "available",
      ...validatedData,
      // Convert empty strings to null for foreign keys
      supplier_id: validatedData.supplier_id || null,
      purchase_contract_id: validatedData.purchase_contract_id || null,
    }

    await db.insert(rawMaterialLots).values(newLot)

    // Fetch created lot with relationships
    const createdLot = await db.query.rawMaterialLots.findFirst({
      where: eq(rawMaterialLots.id, newLot.id),
      with: {
        supplier: true,
        purchaseContract: true,
        rawMaterial: true,
      },
    })

    // ✅ PROFESSIONAL: Emit raw material lot created event
    if (createdLot) {
      try {
        await inventoryEventEmitter.emitRawMaterialLotEvent(
          InventoryEventType.RAW_MATERIAL_LOT_CREATED,
          {
            lotId: createdLot.id,
            materialId: createdLot.raw_material_id,
            materialName: createdLot.rawMaterial?.name || "Unknown Material",
            materialSku: createdLot.rawMaterial?.sku || "Unknown SKU",
            supplierId: createdLot.supplier_id || undefined,
            supplierName: createdLot.supplier?.name || undefined,
            quantity: parseFloat(createdLot.qty || "0"),
            unitCost: parseFloat(createdLot.unit_cost || "0"),
            totalCost: parseFloat(createdLot.total_cost || "0"),
            currency: createdLot.currency || "USD",
            status: createdLot.status as any,
            qualityStatus: createdLot.quality_status as any,
            location: createdLot.location,
            lotNumber: createdLot.lot_number || undefined,
            receivedDate: createdLot.received_date || undefined,
            expiryDate: createdLot.expiry_date || undefined,
          },
          {
            companyId: context.companyId,
            userId: context.userId,
            source: "POST /api/raw-materials/[id]/lots",
          }
        )
      } catch (eventError) {
        console.error("❌ Failed to emit lot created event:", eventError)
        // Don't fail the API call if event emission fails
      }
    }

    return jsonOk(createdLot, { status: 201 })
  } catch (error) {
    console.error("Raw Material Lots POST Error:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    return jsonError("Failed to create raw material lot", 500)
  }
})
