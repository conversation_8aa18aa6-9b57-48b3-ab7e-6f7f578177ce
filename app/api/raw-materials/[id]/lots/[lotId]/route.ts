/**
 * Manufacturing ERP - Raw Material Lot Individual Management API
 * DELETE /api/raw-materials/[id]/lots/[lotId] - Delete specific lot
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { rawMaterials, rawMaterialLots } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { inventoryEventEmitter, InventoryEventType } from "@/lib/events/inventory-events"
import "@/lib/events/inventory-event-init" // Ensure event system is initialized

// DELETE /api/raw-materials/[id]/lots/[lotId] - Delete specific lot
export const DELETE = withTenantAuth(async function DELETE(
  request,
  context,
  { params }: { params: Promise<{ id: string; lotId: string }> }
) {
  try {
    const { id: materialId, lotId } = await params

    // Verify raw material exists and belongs to company
    const material = await db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, materialId),
        eq(rawMaterials.company_id, context.companyId)
      ),
    })

    if (!material) {
      return jsonError("Raw material not found", 404)
    }

    // Verify lot exists and belongs to the material and company
    const lot = await db.query.rawMaterialLots.findFirst({
      where: and(
        eq(rawMaterialLots.id, lotId),
        eq(rawMaterialLots.raw_material_id, materialId),
        eq(rawMaterialLots.company_id, context.companyId)
      ),
      with: {
        rawMaterial: true,
        supplier: true,
      },
    })

    if (!lot) {
      return jsonError("Inventory lot not found", 404)
    }

    // ✅ PROFESSIONAL: Emit lot deletion event before deletion
    try {
      await inventoryEventEmitter.emitRawMaterialLotEvent(
        InventoryEventType.RAW_MATERIAL_LOT_DELETED,
        {
          lotId: lot.id,
          materialId: lot.raw_material_id,
          materialName: lot.rawMaterial?.name || "Unknown Material",
          materialSku: lot.rawMaterial?.sku || "Unknown SKU",
          supplierId: lot.supplier_id || undefined,
          supplierName: lot.supplier?.name || undefined,
          quantity: parseFloat(lot.qty || "0"),
          unitCost: parseFloat(lot.unit_cost || "0"),
          totalCost: parseFloat(lot.total_cost || "0"),
          currency: lot.currency || "USD",
          status: lot.status as any,
          qualityStatus: lot.quality_status as any,
          location: lot.location,
          lotNumber: lot.lot_number || undefined,
          receivedDate: lot.received_date || undefined,
          expiryDate: lot.expiry_date || undefined,
        },
        {
          companyId: context.companyId,
          userId: context.userId,
          source: "DELETE /api/raw-materials/[id]/lots/[lotId]",
        }
      )
    } catch (eventError) {
      console.error("❌ Failed to emit lot deletion event:", eventError)
      // Don't fail the API call if event emission fails
    }

    // Check if lot has been consumed (has consumption transactions)
    // For now, we'll allow deletion of any lot, but in a full ERP system
    // you might want to check for consumption history

    // Delete the lot
    await db.delete(rawMaterialLots)
      .where(and(
        eq(rawMaterialLots.id, lotId),
        eq(rawMaterialLots.company_id, context.companyId)
      ))

    return jsonOk({
      message: `Inventory lot ${lot.lot_number || `LOT-${lot.id.slice(-8)}`} deleted successfully`
    })
  } catch (error) {
    console.error("Raw Material Lot DELETE Error:", error)
    return jsonError("Failed to delete inventory lot", 500)
  }
})
