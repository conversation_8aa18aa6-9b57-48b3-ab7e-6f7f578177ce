/**
 * Manufacturing ERP - Individual Raw Material API
 * CRUD operations for individual raw materials with multi-tenant security
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { rawMaterials, suppliers } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"

// ✅ VALIDATION SCHEMA
const updateRawMaterialSchema = z.object({
  sku: z.string().min(1, "SKU is required").optional(),
  name: z.string().min(1, "Name is required").optional(),
  category: z.enum(["yarn", "fabric", "dyes", "chemicals", "accessories", "other"]).optional(),
  unit: z.string().min(1, "Unit is required").optional(),
  primary_supplier_id: z.string().optional(),
  composition: z.string().optional(),
  quality_grade: z.string().optional(),
  specifications: z.string().optional(),
  standard_cost: z.string().optional(),
  currency: z.string().optional(),
  reorder_point: z.string().optional(),
  max_stock_level: z.string().optional(),
  lead_time_days: z.string().optional(),
  inspection_required: z.enum(["true", "false"]).optional(),
  quality_tolerance: z.string().optional(),
  quality_notes: z.string().optional(),
  status: z.enum(["active", "inactive", "discontinued"]).optional(),
})

// ✅ GET - Get individual raw material
export const GET = withTenantAuth(async function GET(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const material = await db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ),
      with: {
        primarySupplier: true,
        lots: {
          orderBy: (lots, { desc }) => [desc(lots.created_at)],
          limit: 10,
          with: {
            supplier: true,
            purchaseContract: true,
            inspection: true,
          },
        },
        bomItems: {
          with: {
            product: true,
          },
        },
      },
    })

    if (!material) {
      return jsonError("Raw material not found", 404)
    }

    return jsonOk(material)
  } catch (error) {
    console.error("Raw Material GET Error:", error)
    return jsonError("Failed to fetch raw material", 500)
  }
})

// ✅ PATCH - Update raw material
export const PATCH = withTenantAuth(async function PATCH(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = updateRawMaterialSchema.parse(body)

    // Check if material exists
    const existingMaterial = await db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ),
    })

    if (!existingMaterial) {
      return jsonError("Raw material not found", 404)
    }

    // Check for duplicate SKU if updating SKU
    if (validatedData.sku && validatedData.sku !== existingMaterial.sku) {
      const duplicateMaterial = await db.query.rawMaterials.findFirst({
        where: and(
          eq(rawMaterials.company_id, context.companyId),
          eq(rawMaterials.sku, validatedData.sku)
        ),
      })

      if (duplicateMaterial) {
        return jsonError("Raw material with this SKU already exists", 400)
      }
    }

    // Validate supplier if provided
    if (validatedData.primary_supplier_id) {
      const supplier = await db.query.suppliers.findFirst({
        where: and(
          eq(suppliers.id, validatedData.primary_supplier_id),
          eq(suppliers.company_id, context.companyId)
        ),
      })

      if (!supplier) {
        return jsonError("Invalid supplier ID", 400)
      }
    }

    // Update raw material
    await db
      .update(rawMaterials)
      .set({
        ...validatedData,
        updated_at: new Date(),
      })
      .where(eq(rawMaterials.id, id))

    // Fetch updated material with relationships
    const updatedMaterial = await db.query.rawMaterials.findFirst({
      where: eq(rawMaterials.id, id),
      with: {
        primarySupplier: true,
        lots: {
          orderBy: (lots, { desc }) => [desc(lots.created_at)],
          limit: 5,
        },
      },
    })

    return jsonOk(updatedMaterial)
  } catch (error) {
    console.error("Raw Material PATCH Error:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    return jsonError("Failed to update raw material", 500)
  }
})

// ✅ DELETE - Delete raw material (soft delete by setting status to discontinued)
export const DELETE = withTenantAuth(async function DELETE(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Check if material exists
    const existingMaterial = await db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ),
    })

    if (!existingMaterial) {
      return jsonError("Raw material not found", 404)
    }

    // Check if material has active lots or BOM entries
    const material = await db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ),
      with: {
        lots: {
          where: (lots, { eq }) => eq(lots.status, "available"),
        },
        bomItems: {
          where: (bomItems, { eq }) => eq(bomItems.status, "active"),
        },
      },
    })

    if (!material) {
      return jsonError("Raw material not found", 404)
    }

    // Check if material has active dependencies
    const hasActiveDependencies = material.lots.length > 0 || material.bomItems.length > 0

    if (hasActiveDependencies) {
      const reasons = []
      if (material.lots.length > 0) {
        reasons.push(`${material.lots.length} active inventory lot(s)`)
      }
      if (material.bomItems.length > 0) {
        reasons.push(`${material.bomItems.length} active BOM entry(ies)`)
      }

      return jsonError(
        `Cannot delete raw material. It has ${reasons.join(' and ')}. Please consume/remove all lots and remove from BOMs first.`,
        400
      )
    }

    // If material is active, set to discontinued first
    if (material.status === "active") {
      await db
        .update(rawMaterials)
        .set({
          status: "discontinued",
          updated_at: new Date(),
        })
        .where(eq(rawMaterials.id, id))

      return jsonOk({
        message: "Raw material status changed to discontinued. You can now delete it if needed.",
        action: "discontinued"
      })
    }

    // If material is already discontinued and has no dependencies, actually delete it
    await db.delete(rawMaterials)
      .where(and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ))

    return jsonOk({
      message: "Raw material deleted successfully",
      action: "deleted"
    })
  } catch (error) {
    console.error("Raw Material DELETE Error:", error)
    return jsonError("Failed to delete raw material", 500)
  }
})
