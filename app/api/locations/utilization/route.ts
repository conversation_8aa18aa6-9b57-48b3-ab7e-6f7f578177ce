/**
 * Manufacturing ERP - Location Utilization API
 * 
 * Professional API endpoint for location capacity utilization data.
 * Provides real-time utilization metrics, capacity validation, and
 * utilization analytics for warehouse management.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Location-Inventory Integration
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { 
  calculateLocationUtilization,
  calculateAllLocationUtilization,
  validateCapacity,
  getLocationUtilizationSummary
} from "@/lib/location-utilization"
import { z } from "zod"

// ✅ PROFESSIONAL: Zod validation schemas
const utilizationQuerySchema = z.object({
  locationId: z.string().optional(),
  includeEmpty: z.boolean().default(false),
  sortBy: z.enum(['utilization', 'capacity', 'available', 'name']).default('utilization'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

const capacityValidationSchema = z.object({
  locationId: z.string(),
  quantity: z.number().positive()
})

/**
 * ✅ GET /api/locations/utilization - Get location utilization data
 * 
 * Query Parameters:
 * - locationId: Get utilization for specific location
 * - includeEmpty: Include locations with zero inventory
 * - sortBy: Sort by utilization, capacity, available, or name
 * - sortOrder: asc or desc
 * 
 * @param request - HTTP request with query parameters
 * @returns Location utilization data
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    const queryParams = {
      locationId: searchParams.get('locationId') || undefined,
      includeEmpty: searchParams.get('includeEmpty') === 'true',
      sortBy: searchParams.get('sortBy') || 'utilization',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    }

    // Validate query parameters
    const validatedParams = utilizationQuerySchema.parse(queryParams)

    // Get utilization data
    if (validatedParams.locationId) {
      // Single location utilization
      const utilization = await calculateLocationUtilization(
        validatedParams.locationId,
        context.companyId
      )

      if (!utilization) {
        return jsonError("Location not found or no inventory data", 404)
      }

      return jsonOk({
        location: utilization,
        timestamp: new Date().toISOString()
      })
    } else {
      // All locations utilization with summary
      const summary = await getLocationUtilizationSummary(context.companyId)
      
      // Apply sorting
      let sortedLocations = [...summary.locations]
      switch (validatedParams.sortBy) {
        case 'utilization':
          sortedLocations.sort((a, b) => 
            validatedParams.sortOrder === 'desc' 
              ? b.utilizationPercentage - a.utilizationPercentage
              : a.utilizationPercentage - b.utilizationPercentage
          )
          break
        case 'capacity':
          sortedLocations.sort((a, b) => 
            validatedParams.sortOrder === 'desc' 
              ? b.capacity - a.capacity
              : a.capacity - b.capacity
          )
          break
        case 'available':
          sortedLocations.sort((a, b) => 
            validatedParams.sortOrder === 'desc' 
              ? b.availableCapacity - a.availableCapacity
              : a.availableCapacity - b.availableCapacity
          )
          break
        case 'name':
          sortedLocations.sort((a, b) => 
            validatedParams.sortOrder === 'desc' 
              ? b.locationName.localeCompare(a.locationName)
              : a.locationName.localeCompare(b.locationName)
          )
          break
      }

      // Filter empty locations if requested
      if (!validatedParams.includeEmpty) {
        sortedLocations = sortedLocations.filter(loc => loc.currentStock > 0)
      }

      return jsonOk({
        summary: {
          totalLocations: summary.totalLocations,
          totalCapacity: summary.totalCapacity,
          totalUsed: summary.totalUsed,
          availableCapacity: summary.availableCapacity,
          overallUtilization: summary.overallUtilization,
          statusCounts: summary.statusCounts
        },
        locations: sortedLocations,
        timestamp: new Date().toISOString(),
        queryParams: validatedParams
      })
    }
  } catch (error) {
    console.error("Location Utilization API Error:", error)
    if (error instanceof z.ZodError) {
      return jsonError("Invalid query parameters", 400, error.errors)
    }
    return jsonError("Failed to fetch location utilization", 500)
  }
})

/**
 * ✅ POST /api/locations/utilization - Validate capacity for receiving stock
 * 
 * Body:
 * - locationId: Location to validate
 * - quantity: Quantity to receive
 * 
 * @param request - HTTP request with validation data
 * @returns Capacity validation result
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()
    const validatedData = capacityValidationSchema.parse(body)

    const validation = await validateCapacity(
      validatedData.locationId,
      validatedData.quantity,
      context.companyId
    )

    return jsonOk({
      validation,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error("Capacity Validation API Error:", error)
    if (error instanceof z.ZodError) {
      return jsonError("Invalid validation data", 400, error.errors)
    }
    return jsonError("Failed to validate capacity", 500)
  }
})
