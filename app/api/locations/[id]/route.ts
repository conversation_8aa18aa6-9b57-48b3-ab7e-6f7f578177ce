import { db } from "@/lib/db"
import { json<PERSON><PERSON>r, jsonOk } from "@/lib/api-helpers"
import { locations } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const updateLocationSchema = z.object({
  name: z.string().min(1, "Location name is required").optional(),
  type: z.enum([
    'warehouse', 'raw_materials', 'finished_goods', 'work_in_progress',
    'quality_control', 'shipping', 'receiving', 'quarantine', 'returns'
  ]).optional(),
  description: z.string().nullable().optional(),
  capacity: z.number().int().min(0).optional(),
  location_code: z.string().nullable().optional(),
  is_active: z.boolean().optional(),
  temperature_controlled: z.boolean().optional(), // Match frontend field name
  security_level: z.enum(['low', 'medium', 'high']).optional(),
  parent_location_id: z.string().nullable().optional(),
  address: z.string().nullable().optional(),
  floor_level: z.number().int().nullable().optional(),
  zone: z.string().nullable().optional(),
  allows_mixed_products: z.boolean().optional(),
  requires_quality_check: z.boolean().optional(),
  automated_handling: z.boolean().optional(),
})

// ============================================================================
// API ENDPOINTS
// ============================================================================

// 🛡️ SECURE: Get individual location with multi-tenant isolation
export const GET = withTenantAuth(async function GET(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    console.log("🔍 LOCATIONS API: Fetching location", id, "for company:", context.companyId)

    const location = await db.query.locations.findFirst({
      where: and(
        eq(locations.id, id),
        eq(locations.company_id, context.companyId) // 🛡️ CRITICAL: Multi-tenant isolation
      ),
      with: {
        parentLocation: true,
        childLocations: true,
      },
    })

    if (!location) {
      return jsonError("Location not found", 404)
    }

    console.log("✅ LOCATIONS API: Location found:", location.name)
    return jsonOk(location)
  } catch (error) {
    console.error("❌ LOCATIONS API: Fetch failed:", error)
    return jsonError("Failed to fetch location", 500)
  }
})

// 🛡️ SECURE: Update location with multi-tenant isolation
export const PATCH = withTenantAuth(async function PATCH(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    console.log("📝 LOCATIONS API: Updating location", id, "for company:", context.companyId)

    const body = await request.json()
    const validatedData = updateLocationSchema.parse(body)

    // Verify location exists and belongs to company
    const existingLocation = await db.query.locations.findFirst({
      where: and(
        eq(locations.id, id),
        eq(locations.company_id, context.companyId) // 🛡️ CRITICAL: Multi-tenant isolation
      ),
    })

    if (!existingLocation) {
      return jsonError("Location not found", 404)
    }

    // Update location - map frontend field names to database column names and clean null values
    const updateData: any = { ...validatedData }

    // Remove null values (convert to undefined for database)
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === null) {
        delete updateData[key]
      }
    })

    // Map frontend field names to database column names
    if (validatedData.temperature_controlled !== undefined) {
      updateData.is_temperature_controlled = validatedData.temperature_controlled
      delete updateData.temperature_controlled
    }

    const updatedLocation = await db.update(locations)
      .set({
        ...updateData,
        updated_at: new Date(),
      })
      .where(and(
        eq(locations.id, id),
        eq(locations.company_id, context.companyId) // 🛡️ CRITICAL: Multi-tenant isolation
      ))
      .returning()

    console.log("✅ LOCATIONS API: Location updated successfully:", id)
    return jsonOk(updatedLocation[0])
  } catch (error) {
    console.error("❌ LOCATIONS API: Update failed:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('unique')) {
      return jsonError("Location code already exists", 409)
    }

    return jsonError("Failed to update location", 500)
  }
})

// 🛡️ SECURE: Delete location with multi-tenant isolation
export const DELETE = withTenantAuth(async function DELETE(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    console.log("🗑️ LOCATIONS API: Deleting location", id, "for company:", context.companyId)

    // Verify location exists and belongs to company
    const existingLocation = await db.query.locations.findFirst({
      where: and(
        eq(locations.id, id),
        eq(locations.company_id, context.companyId) // 🛡️ CRITICAL: Multi-tenant isolation
      ),
    })

    if (!existingLocation) {
      return jsonError("Location not found", 404)
    }

    // Check if location has child locations
    const childLocations = await db.query.locations.findMany({
      where: and(
        eq(locations.parent_location_id, id),
        eq(locations.company_id, context.companyId)
      ),
    })

    if (childLocations.length > 0) {
      return jsonError("Cannot delete location with child locations", 400)
    }

    // Delete location
    await db.delete(locations)
      .where(and(
        eq(locations.id, id),
        eq(locations.company_id, context.companyId) // 🛡️ CRITICAL: Multi-tenant isolation
      ))

    console.log("✅ LOCATIONS API: Location deleted successfully:", id)
    return jsonOk({ message: "Location deleted successfully" })
  } catch (error) {
    console.error("❌ LOCATIONS API: Delete failed:", error)
    return jsonError("Failed to delete location", 500)
  }
})
