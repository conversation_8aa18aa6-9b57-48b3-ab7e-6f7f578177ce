// ============================================================================
// MANUFACTURING ERP - PRICING ANALYTICS API
// ============================================================================
//
// Professional API endpoint for advanced pricing analytics leveraging Phase 2
// pricing context enhancements. Provides comprehensive margin analysis, cost
// tracking, and pricing optimization insights.
//
// FEATURES:
// ✅ Contract Margin Analysis - GET /api/analytics/pricing?type=margins
// ✅ Pricing Method Performance - GET /api/analytics/pricing?type=methods
// ✅ Cost Tracking Reports - GET /api/analytics/pricing?type=costs
// ✅ Pricing Optimization - GET /api/analytics/pricing?type=optimization
// ✅ Multi-Tenant Security - Proper company isolation
//
// <AUTHOR> ERP Developer
// @version 1.0.0 - Phase 2 Enhanced Reporting Services
// @date 2024-01-XX
// ============================================================================

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { PricingAnalyticsService } from "@/lib/services/pricing-analytics"
import { z } from "zod"

// ✅ VALIDATION: Query parameter schema
const pricingAnalyticsQuerySchema = z.object({
  type: z.enum(["margins", "methods", "costs", "optimization"]).default("margins"),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  currency: z.string().length(3).optional(),
  format: z.enum(["json", "csv"]).default("json"),
})

/**
 * ✅ GET /api/analytics/pricing - Advanced pricing analytics endpoint
 * 
 * Provides comprehensive pricing analytics using Phase 2 pricing context data.
 * Supports multiple analysis types and flexible filtering options.
 * 
 * Query Parameters:
 * - type: Analysis type (margins, methods, costs, optimization)
 * - date_from: Start date filter (YYYY-MM-DD)
 * - date_to: End date filter (YYYY-MM-DD)
 * - currency: Currency filter (ISO 4217 code)
 * - format: Response format (json, csv)
 * 
 * @param request - HTTP request with query parameters
 * @returns Pricing analytics data based on requested type
 */
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    // Parse and validate query parameters
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    
    const validation = pricingAnalyticsQuerySchema.safeParse(queryParams)
    if (!validation.success) {
      return jsonError("Invalid query parameters", 400, validation.error.errors)
    }

    const { type, date_from, date_to, currency, format } = validation.data

    // Initialize pricing analytics service
    const analyticsService = new PricingAnalyticsService({
      companyId: context.companyId,
      userId: context.userId,
    })

    let data: any
    let filename: string

    // Execute requested analysis type
    switch (type) {
      case "margins":
        data = await analyticsService.getContractMarginAnalysis(date_from, date_to, currency)
        filename = `contract-margins-${new Date().toISOString().split('T')[0]}.csv`
        break

      case "methods":
        data = await analyticsService.getPricingMethodPerformance(date_from, date_to)
        filename = `pricing-methods-${new Date().toISOString().split('T')[0]}.csv`
        break

      case "costs":
        data = await analyticsService.getCostTrackingReport()
        filename = `cost-tracking-${new Date().toISOString().split('T')[0]}.csv`
        break

      case "optimization":
        data = await analyticsService.getPricingOptimizationRecommendations()
        filename = `pricing-optimization-${new Date().toISOString().split('T')[0]}.csv`
        break

      default:
        return jsonError("Invalid analysis type", 400)
    }

    // Return CSV format if requested
    if (format === "csv") {
      const csv = convertToCSV(data, type)
      return new Response(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}"`,
        },
      })
    }

    // Return JSON format with metadata
    return jsonOk({
      type,
      data,
      metadata: {
        generated_at: new Date().toISOString(),
        company_id: context.companyId,
        filters: {
          date_from,
          date_to,
          currency,
        },
        record_count: Array.isArray(data) ? data.length : Object.keys(data).length,
      },
    })

  } catch (error) {
    console.error("Pricing Analytics API Error:", error)
    return jsonError("Internal server error", 500)
  }
})

/**
 * ✅ UTILITY: Convert analytics data to CSV format
 */
function convertToCSV(data: any, type: string): string {
  if (!data || (Array.isArray(data) && data.length === 0)) {
    return "No data available"
  }

  let headers: string[]
  let rows: string[][]

  switch (type) {
    case "margins":
      headers = [
        "Contract ID", "Contract Number", "Total Revenue", "Total Cost", 
        "Total Margin", "Margin %", "Currency", "Item Count", "Average Margin"
      ]
      rows = data.map((item: any) => [
        item.contractId,
        item.contractNumber,
        item.totalRevenue.toFixed(2),
        item.totalCost.toFixed(2),
        item.totalMargin.toFixed(2),
        item.marginPercentage.toFixed(2),
        item.currency,
        item.itemCount.toString(),
        item.averageMargin.toFixed(2),
      ])
      break

    case "methods":
      headers = [
        "Pricing Method", "Contract Count", "Total Revenue", 
        "Average Margin %", "Min Margin %", "Max Margin %", "Currency"
      ]
      rows = data.map((item: any) => [
        item.method,
        item.contractCount.toString(),
        item.totalRevenue.toFixed(2),
        item.averageMargin.toFixed(2),
        item.marginRange.min.toFixed(2),
        item.marginRange.max.toFixed(2),
        item.currency,
      ])
      break

    case "costs":
      headers = [
        "Product ID", "Product Name", "SKU", "Current Cost Price", 
        "Current Base Price", "Average Contract Price", "Last Transaction Cost",
        "Cost Variance %", "Price Variance %", "Currency"
      ]
      rows = data.map((item: any) => [
        item.productId,
        item.productName,
        item.productSku,
        item.currentCostPrice?.toFixed(2) || "N/A",
        item.currentBasePrice?.toFixed(2) || "N/A",
        item.averageContractPrice.toFixed(2),
        item.lastTransactionCost?.toFixed(2) || "N/A",
        item.costVariance.toFixed(2),
        item.priceVariance.toFixed(2),
        item.currency,
      ])
      break

    case "optimization":
      headers = ["Analysis Type", "Method/Product", "Current Value", "Recommendation"]
      rows = [
        ...data.overperformingMethods.map((item: any) => [
          "High-Performing Method",
          item.method,
          `${item.averageMargin.toFixed(1)}% margin`,
          item.recommendation,
        ]),
        ...data.underperformingProducts.map((item: any) => [
          "Underperforming Product",
          item.productName,
          `${item.currentMargin.toFixed(1)}% margin`,
          `Consider increasing price to $${item.recommendedPrice.toFixed(2)}`,
        ]),
      ]
      break

    default:
      return "Invalid data type"
  }

  // Convert to CSV format
  const csvContent = [
    headers.join(","),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(","))
  ].join("\n")

  return csvContent
}

/**
 * ✅ POST /api/analytics/pricing - Batch analytics processing
 * 
 * Process multiple analytics requests in a single API call for dashboard
 * components that need comprehensive pricing insights.
 */
export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()
    const { requests } = body

    if (!Array.isArray(requests) || requests.length === 0) {
      return jsonError("Invalid request format. Expected array of analysis requests.", 400)
    }

    const analyticsService = new PricingAnalyticsService({
      companyId: context.companyId,
      userId: context.userId,
    })

    const results = await Promise.all(
      requests.map(async (req: any) => {
        try {
          const { type, date_from, date_to, currency } = req

          switch (type) {
            case "margins":
              return {
                type,
                data: await analyticsService.getContractMarginAnalysis(date_from, date_to, currency),
              }
            case "methods":
              return {
                type,
                data: await analyticsService.getPricingMethodPerformance(date_from, date_to),
              }
            case "costs":
              return {
                type,
                data: await analyticsService.getCostTrackingReport(),
              }
            case "optimization":
              return {
                type,
                data: await analyticsService.getPricingOptimizationRecommendations(),
              }
            default:
              return {
                type,
                error: "Invalid analysis type",
              }
          }
        } catch (error) {
          return {
            type: req.type,
            error: "Analysis failed",
          }
        }
      })
    )

    return jsonOk({
      results,
      metadata: {
        generated_at: new Date().toISOString(),
        company_id: context.companyId,
        request_count: requests.length,
      },
    })

  } catch (error) {
    console.error("Batch Pricing Analytics API Error:", error)
    return jsonError("Internal server error", 500)
  }
})
