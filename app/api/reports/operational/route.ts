/**
 * Manufacturing ERP - Operational Reports API
 * Enterprise-grade operational reporting and analytics
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { 
  workOrders,
  qualityInspections,
  stockLots,
  stockTxns,
  shipments,
  products,
  samples
} from "@/lib/schema-postgres"
import { eq, and, gte, lte, desc, count, sum, sql, avg } from "drizzle-orm"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface OperationalMetrics {
  production: {
    totalWorkOrders: number
    completedOrders: number
    pendingOrders: number
    inProgressOrders: number
    completionRate: number
    averageLeadTime: number
    onTimeDelivery: number
  }
  quality: {
    totalInspections: number
    passedInspections: number
    failedInspections: number
    pendingInspections: number
    passRate: number
    defectRate: number
    averageInspectionTime: number
  }
  inventory: {
    totalStockLots: number
    totalInventoryValue: number
    lowStockItems: number
    stockTurnover: number
    averageStockAge: number
    totalTransactions: number
    inboundTransactions: number
    outboundTransactions: number
  }
  shipping: {
    totalShipments: number
    deliveredShipments: number
    pendingShipments: number
    delayedShipments: number
    onTimeDeliveryRate: number
    averageShippingTime: number
    shippingCosts: number
  }
  samples: {
    totalSamples: number
    approvedSamples: number
    rejectedSamples: number
    pendingSamples: number
    approvalRate: number
    averageApprovalTime: number
  }
}

// ============================================================================
// API ENDPOINTS
// ============================================================================

// 🛡️ SECURE: Multi-tenant GET endpoint for operational reports
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const { searchParams } = new URL(request.url)
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')

    // Default to current month if no date range provided
    const defaultDateFrom = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString()
    const defaultDateTo = new Date().toISOString()

    const fromDate = dateFrom || defaultDateFrom
    const toDate = dateTo || defaultDateTo

    // Parallel data fetching for performance
    const [
      workOrdersData,
      qualityData,
      inventoryData,
      transactionsData,
      shipmentsData,
      samplesData
    ] = await Promise.all([
      // Work Orders Analysis
      db.select({
        total: count(),
        completed: count(sql`CASE WHEN ${workOrders.status} = 'completed' THEN 1 END`),
        pending: count(sql`CASE WHEN ${workOrders.status} = 'pending' THEN 1 END`),
        inProgress: count(sql`CASE WHEN ${workOrders.status} = 'in_progress' THEN 1 END`),
        avgLeadTime: avg(sql`CASE WHEN ${workOrders.status} = 'completed' THEN EXTRACT(DAY FROM (${workOrders.updated_at} - ${workOrders.created_at})) END`),
      }).from(workOrders)
        .where(and(
          eq(workOrders.company_id, context.companyId),
          gte(workOrders.created_at, new Date(fromDate)),
          lte(workOrders.created_at, new Date(toDate))
        )),

      // Quality Inspections Analysis
      db.select({
        total: count(),
        passed: count(sql`CASE WHEN ${qualityInspections.status} = 'approved' THEN 1 END`),
        failed: count(sql`CASE WHEN ${qualityInspections.status} = 'rejected' THEN 1 END`),
        pending: count(sql`CASE WHEN ${qualityInspections.status} = 'pending' THEN 1 END`),
        avgInspectionTime: avg(sql`CASE WHEN ${qualityInspections.status} != 'pending' THEN EXTRACT(DAY FROM (${qualityInspections.updated_at} - ${qualityInspections.created_at})) END`),
      }).from(qualityInspections)
        .where(and(
          eq(qualityInspections.company_id, context.companyId),
          gte(qualityInspections.created_at, new Date(fromDate)),
          lte(qualityInspections.created_at, new Date(toDate))
        )),

      // Inventory Analysis
      db.select({
        totalLots: count(),
        totalValue: sum(sql`CAST(${stockLots.qty} AS DECIMAL) * CAST(${stockLots.unit_cost} AS DECIMAL)`),
        lowStock: count(sql`CASE WHEN CAST(${stockLots.qty} AS DECIMAL) < 10 THEN 1 END`), // Simplified low stock threshold
        avgAge: avg(sql`EXTRACT(DAY FROM (NOW() - ${stockLots.created_at}))`),
      }).from(stockLots)
        .where(eq(stockLots.company_id, context.companyId)),

      // Stock Transactions Analysis
      db.select({
        total: count(),
        inbound: count(sql`CASE WHEN ${stockTxns.type} = 'in' THEN 1 END`),
        outbound: count(sql`CASE WHEN ${stockTxns.type} = 'out' THEN 1 END`),
        totalValue: sum(sql`CAST(${stockTxns.qty} AS DECIMAL) * CAST(${stockTxns.unit_cost} AS DECIMAL)`),
      }).from(stockTxns)
        .where(and(
          eq(stockTxns.company_id, context.companyId),
          gte(stockTxns.created_at, new Date(fromDate)),
          lte(stockTxns.created_at, new Date(toDate))
        )),

      // Shipments Analysis
      db.select({
        total: count(),
        delivered: count(sql`CASE WHEN ${shipments.status} = 'delivered' THEN 1 END`),
        pending: count(sql`CASE WHEN ${shipments.status} = 'pending' THEN 1 END`),
        delayed: count(sql`CASE WHEN ${shipments.status} = 'delayed' THEN 1 END`),
        avgShippingTime: avg(sql`CASE WHEN ${shipments.status} = 'delivered' THEN EXTRACT(DAY FROM (${shipments.updated_at} - ${shipments.created_at})) END`),
        totalCost: sum(sql`CAST(${shipments.cost} AS DECIMAL)`),
      }).from(shipments)
        .where(and(
          eq(shipments.company_id, context.companyId),
          gte(shipments.created_at, new Date(fromDate)),
          lte(shipments.created_at, new Date(toDate))
        )),

      // Samples Analysis
      db.select({
        total: count(),
        approved: count(sql`CASE WHEN ${samples.status} = 'approved' THEN 1 END`),
        rejected: count(sql`CASE WHEN ${samples.status} = 'rejected' THEN 1 END`),
        pending: count(sql`CASE WHEN ${samples.status} = 'pending' THEN 1 END`),
        avgApprovalTime: avg(sql`CASE WHEN ${samples.status} != 'pending' THEN EXTRACT(DAY FROM (${samples.updated_at} - ${samples.created_at})) END`),
      }).from(samples)
        .where(and(
          eq(samples.company_id, context.companyId),
          gte(samples.created_at, new Date(fromDate)),
          lte(samples.created_at, new Date(toDate))
        ))
    ])

    // Calculate operational metrics
    const totalWorkOrders = Number(workOrdersData[0]?.total || 0)
    const completedOrders = Number(workOrdersData[0]?.completed || 0)
    const pendingOrders = Number(workOrdersData[0]?.pending || 0)
    const inProgressOrders = Number(workOrdersData[0]?.inProgress || 0)
    const completionRate = totalWorkOrders > 0 ? (completedOrders / totalWorkOrders) * 100 : 0

    const totalInspections = Number(qualityData[0]?.total || 0)
    const passedInspections = Number(qualityData[0]?.passed || 0)
    const failedInspections = Number(qualityData[0]?.failed || 0)
    const pendingInspections = Number(qualityData[0]?.pending || 0)
    const passRate = totalInspections > 0 ? (passedInspections / totalInspections) * 100 : 0
    const defectRate = totalInspections > 0 ? (failedInspections / totalInspections) * 100 : 0

    const totalShipments = Number(shipmentsData[0]?.total || 0)
    const deliveredShipments = Number(shipmentsData[0]?.delivered || 0)
    const pendingShipments = Number(shipmentsData[0]?.pending || 0)
    const delayedShipments = Number(shipmentsData[0]?.delayed || 0)
    const onTimeDeliveryRate = totalShipments > 0 ? (deliveredShipments / totalShipments) * 100 : 0

    const totalSamples = Number(samplesData[0]?.total || 0)
    const approvedSamples = Number(samplesData[0]?.approved || 0)
    const rejectedSamples = Number(samplesData[0]?.rejected || 0)
    const pendingSamples = Number(samplesData[0]?.pending || 0)
    const approvalRate = totalSamples > 0 ? (approvedSamples / totalSamples) * 100 : 0

    // Build operational metrics response
    const operationalMetrics: OperationalMetrics = {
      production: {
        totalWorkOrders,
        completedOrders,
        pendingOrders,
        inProgressOrders,
        completionRate,
        averageLeadTime: Number(workOrdersData[0]?.avgLeadTime || 0),
        onTimeDelivery: completionRate // Simplified - same as completion rate
      },
      quality: {
        totalInspections,
        passedInspections,
        failedInspections,
        pendingInspections,
        passRate,
        defectRate,
        averageInspectionTime: Number(qualityData[0]?.avgInspectionTime || 0)
      },
      inventory: {
        totalStockLots: Number(inventoryData[0]?.totalLots || 0),
        totalInventoryValue: Number(inventoryData[0]?.totalValue || 0),
        lowStockItems: Number(inventoryData[0]?.lowStock || 0),
        stockTurnover: 0, // Would need more complex calculation
        averageStockAge: Number(inventoryData[0]?.avgAge || 0),
        totalTransactions: Number(transactionsData[0]?.total || 0),
        inboundTransactions: Number(transactionsData[0]?.inbound || 0),
        outboundTransactions: Number(transactionsData[0]?.outbound || 0)
      },
      shipping: {
        totalShipments,
        deliveredShipments,
        pendingShipments,
        delayedShipments,
        onTimeDeliveryRate,
        averageShippingTime: Number(shipmentsData[0]?.avgShippingTime || 0),
        shippingCosts: Number(shipmentsData[0]?.totalCost || 0)
      },
      samples: {
        totalSamples,
        approvedSamples,
        rejectedSamples,
        pendingSamples,
        approvalRate,
        averageApprovalTime: Number(samplesData[0]?.avgApprovalTime || 0)
      }
    }

    return jsonOk(operationalMetrics)
  } catch (error) {
    console.error('Operational reports error:', error)
    return jsonError(error)
  }
})

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Calculate operational efficiency score
 */
function calculateEfficiencyScore(metrics: OperationalMetrics): number {
  const weights = {
    completionRate: 0.3,
    passRate: 0.25,
    onTimeDelivery: 0.25,
    approvalRate: 0.2
  }

  const score = (
    metrics.production.completionRate * weights.completionRate +
    metrics.quality.passRate * weights.passRate +
    metrics.shipping.onTimeDeliveryRate * weights.onTimeDelivery +
    metrics.samples.approvalRate * weights.approvalRate
  )

  return Math.round(score)
}

/**
 * Calculate inventory turnover ratio
 */
function calculateInventoryTurnover(totalValue: number, transactions: number): number {
  if (totalValue === 0) return 0
  return transactions / totalValue
}

/**
 * Determine performance status based on metrics
 */
function getPerformanceStatus(value: number, thresholds: { excellent: number; good: number; warning: number }): string {
  if (value >= thresholds.excellent) return 'excellent'
  if (value >= thresholds.good) return 'good'
  if (value >= thresholds.warning) return 'warning'
  return 'critical'
}
