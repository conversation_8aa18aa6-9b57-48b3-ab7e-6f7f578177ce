/**
 * Manufacturing ERP - Reports Cache Management API
 * 
 * Enterprise-grade cache management endpoint for reports module
 * Provides cache metrics, manual invalidation, and monitoring capabilities
 * 
 * ✅ SECURED: Multi-tenant cache isolation
 * ✅ MAINTAINED: Zero breaking changes to existing functionality
 * ✅ ENHANCED: Performance monitoring and manual cache control
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { reportsCacheService } from "@/lib/cache/reports-cache"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface CacheManagementRequest {
  action: 'metrics' | 'invalidate' | 'clear' | 'invalidate-company'
  reportType?: string
  params?: Record<string, any>
}

interface CacheManagementResponse {
  success: boolean
  action: string
  metrics?: any
  message?: string
  timestamp: string
}

// ============================================================================
// API ENDPOINTS
// ============================================================================

/**
 * GET /api/reports/cache - Get cache metrics and status
 * 
 * Returns current cache performance metrics including:
 * - Hit/miss rates
 * - Cache size
 * - Performance statistics
 */
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const { searchParams } = new URL(request.url)
    const includeDetails = searchParams.get('details') === 'true'

    const metrics = reportsCacheService.getMetrics()

    const response: CacheManagementResponse = {
      success: true,
      action: 'metrics',
      metrics: {
        ...metrics,
        companyId: context.companyId,
        ...(includeDetails && {
          cacheConfiguration: {
            defaultTTL: 300,
            reportTTLs: {
              'reports-overview': 180,
              'financial-performance': 600,
              'production-analytics': 300,
              'business-intelligence': 900,
              'quality-metrics': 300,
              'inventory-intelligence': 240,
              'mrp-planning': 1800,
            },
            maxCacheSize: 1000,
            cleanupInterval: 300000,
          }
        })
      },
      timestamp: new Date().toISOString()
    }

    return jsonOk(response)
  } catch (error) {
    console.error("Cache metrics error:", error)
    return jsonError("Failed to fetch cache metrics", 500)
  }
})

/**
 * POST /api/reports/cache - Cache management operations
 * 
 * Supports the following actions:
 * - invalidate: Clear cache for specific report type
 * - clear: Clear all cache entries for current company
 * - invalidate-company: Clear all cache entries for current company (alias)
 */
export const POST = withTenantAuth(async function POST(request, context) {
  try {
    const body: CacheManagementRequest = await request.json()
    const { action, reportType, params = {} } = body

    let response: CacheManagementResponse

    switch (action) {
      case 'metrics':
        const metrics = reportsCacheService.getMetrics()
        response = {
          success: true,
          action: 'metrics',
          metrics: {
            ...metrics,
            companyId: context.companyId,
          },
          timestamp: new Date().toISOString()
        }
        break

      case 'invalidate':
        if (!reportType) {
          return jsonError("Report type is required for invalidate action", 400)
        }
        
        await reportsCacheService.invalidate(reportType, context, params)
        response = {
          success: true,
          action: 'invalidate',
          message: `Cache invalidated for report type: ${reportType}`,
          timestamp: new Date().toISOString()
        }
        break

      case 'clear':
      case 'invalidate-company':
        await reportsCacheService.invalidateCompany(context.companyId)
        response = {
          success: true,
          action: action,
          message: `All cache entries cleared for company: ${context.companyId}`,
          timestamp: new Date().toISOString()
        }
        break

      default:
        return jsonError(`Invalid action: ${action}. Supported actions: metrics, invalidate, clear, invalidate-company`, 400)
    }

    console.log(`📋 Cache Management: ${action} executed for company ${context.companyId}`)
    return jsonOk(response)

  } catch (error) {
    console.error("Cache management error:", error)
    return jsonError("Failed to execute cache management operation", 500)
  }
})

/**
 * DELETE /api/reports/cache - Clear all cache entries for current company
 * 
 * Convenience endpoint for clearing all cached reports for the current tenant
 */
export const DELETE = withTenantAuth(async function DELETE(request, context) {
  try {
    await reportsCacheService.invalidateCompany(context.companyId)

    const response: CacheManagementResponse = {
      success: true,
      action: 'clear-all',
      message: `All cache entries cleared for company: ${context.companyId}`,
      timestamp: new Date().toISOString()
    }

    console.log(`📋 Cache Management: DELETE executed for company ${context.companyId}`)
    return jsonOk(response)

  } catch (error) {
    console.error("Cache clear error:", error)
    return jsonError("Failed to clear cache", 500)
  }
})

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Validate cache management request
 */
function validateCacheRequest(body: any): CacheManagementRequest {
  const { action, reportType, params = {} } = body

  if (!action) {
    throw new Error("Action is required")
  }

  const validActions = ['metrics', 'invalidate', 'clear', 'invalidate-company']
  if (!validActions.includes(action)) {
    throw new Error(`Invalid action. Supported actions: ${validActions.join(', ')}`)
  }

  if (action === 'invalidate' && !reportType) {
    throw new Error("Report type is required for invalidate action")
  }

  return { action, reportType, params }
}

/**
 * Format cache metrics for display
 */
function formatCacheMetrics(metrics: any, companyId: string) {
  return {
    ...metrics,
    companyId,
    hitRateFormatted: `${metrics.hitRate.toFixed(2)}%`,
    lastUpdated: new Date().toISOString(),
    performance: {
      excellent: metrics.hitRate >= 80,
      good: metrics.hitRate >= 60 && metrics.hitRate < 80,
      needsImprovement: metrics.hitRate < 60,
    }
  }
}
