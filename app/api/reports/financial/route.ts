/**
 * Manufacturing ERP - Financial Reports API
 * Enterprise-grade financial reporting and analytics
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import {
  arInvoices,
  apInvoices,
  salesContracts,
  purchaseContracts,
  customers,
  suppliers
} from "@/lib/schema-postgres"
import { eq, and, gte, lte, desc, count, sum, sql } from "drizzle-orm"
import { withReportsCache } from "@/lib/cache/reports-cache"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface FinancialMetrics {
  profitLoss: {
    revenue: { current: number; previous: number; growth: number }
    expenses: { current: number; previous: number; growth: number }
    grossProfit: { current: number; previous: number; margin: number }
    netProfit: { current: number; previous: number; margin: number }
  }
  balanceSheet: {
    assets: { current: number; fixed: number; total: number }
    liabilities: { current: number; longTerm: number; total: number }
    equity: { retained: number; total: number }
  }
  cashFlow: {
    operating: { inflow: number; outflow: number; net: number }
    investing: { inflow: number; outflow: number; net: number }
    financing: { inflow: number; outflow: number; net: number }
    netCashFlow: number
  }
  arAging: {
    current: { count: number; amount: number }
    overdue30: { count: number; amount: number }
    overdue60: { count: number; amount: number }
    overdue90: { count: number; amount: number }
    total: { count: number; amount: number }
  }
  apAging: {
    current: { count: number; amount: number }
    overdue30: { count: number; amount: number }
    overdue60: { count: number; amount: number }
    overdue90: { count: number; amount: number }
    total: { count: number; amount: number }
  }
}

// ============================================================================
// API ENDPOINTS
// ============================================================================

// 🛡️ SECURE: Multi-tenant GET endpoint for financial reports with caching
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const { searchParams } = new URL(request.url)
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const period = searchParams.get('period') || 'month'
    const forceRefresh = searchParams.get('forceRefresh') === 'true'

    // Calculate date ranges
    const { currentPeriod, previousPeriod } = getFinancialPeriods(period, dateFrom, dateTo)

    // ✅ PERFORMANCE: Cache parameters for multi-tenant isolation
    const cacheParams = {
      dateFrom: dateFrom || 'default',
      dateTo: dateTo || 'default',
      period,
    }

    // ✅ PERFORMANCE: Use caching wrapper with automatic cache management
    const financialReport = await withReportsCache(
      'financial-performance',
      context,
      async () => {
        console.log(`📋 Fetching fresh financial performance data for company ${context.companyId}`)
        return await fetchFinancialPerformanceData(context, currentPeriod, previousPeriod)
      },
      cacheParams,
      { forceRefresh }
    )

    return jsonOk(financialReport)
  } catch (error) {
    console.error("Financial performance error:", error)
    return jsonError("Failed to fetch financial performance", 500)
  }
})

// ✅ PERFORMANCE: Extracted data fetching logic for caching
async function fetchFinancialPerformanceData(context: any, currentPeriod: any, previousPeriod: any) {

  // Parallel data fetching for performance
  const [
    currentRevenue,
    previousRevenue,
    currentExpenses,
    previousExpenses,
    arAgingData,
    apAgingData
  ] = await Promise.all([
    // Current period revenue (AR invoices)
    db.select({
      count: count(),
      total: sum(sql`CAST(${arInvoices.amount} AS DECIMAL)`),
      paid: sum(sql`CASE WHEN ${arInvoices.status} = 'paid' THEN CAST(${arInvoices.amount} AS DECIMAL) ELSE 0 END`),
    }).from(arInvoices)
      .where(and(
        eq(arInvoices.company_id, context.companyId),
        gte(arInvoices.date, currentPeriod.from),
        lte(arInvoices.date, currentPeriod.to)
      )),

    // Previous period revenue
    db.select({
      count: count(),
      total: sum(sql`CAST(${arInvoices.amount} AS DECIMAL)`),
      paid: sum(sql`CASE WHEN ${arInvoices.status} = 'paid' THEN CAST(${arInvoices.amount} AS DECIMAL) ELSE 0 END`),
    }).from(arInvoices)
      .where(and(
        eq(arInvoices.company_id, context.companyId),
        gte(arInvoices.date, previousPeriod.from),
        lte(arInvoices.date, previousPeriod.to)
      )),

    // Current period expenses (AP invoices)
    db.select({
      count: count(),
      total: sum(sql`CAST(${apInvoices.amount} AS DECIMAL)`),
      paid: sum(sql`CASE WHEN ${apInvoices.status} = 'paid' THEN CAST(${apInvoices.amount} AS DECIMAL) ELSE 0 END`),
    }).from(apInvoices)
      .where(and(
        eq(apInvoices.company_id, context.companyId),
        gte(apInvoices.date, currentPeriod.from),
        lte(apInvoices.date, currentPeriod.to)
      )),

    // Previous period expenses
    db.select({
      count: count(),
      total: sum(sql`CAST(${apInvoices.amount} AS DECIMAL)`),
      paid: sum(sql`CASE WHEN ${apInvoices.status} = 'paid' THEN CAST(${apInvoices.amount} AS DECIMAL) ELSE 0 END`),
    }).from(apInvoices)
      .where(and(
        eq(apInvoices.company_id, context.companyId),
        gte(apInvoices.date, previousPeriod.from),
        lte(apInvoices.date, previousPeriod.to)
      )),

    // AR Aging Analysis
    db.select({
      id: arInvoices.id,
      amount: arInvoices.amount,
      date: arInvoices.date,
      due_date: arInvoices.due_date,
      status: arInvoices.status,
      customer_id: arInvoices.customer_id,
    }).from(arInvoices)
      .leftJoin(customers, eq(arInvoices.customer_id, customers.id))
      .where(and(
        eq(arInvoices.company_id, context.companyId),
        eq(arInvoices.status, 'draft') // Unpaid invoices
      )),

    // AP Aging Analysis
    db.select({
      id: apInvoices.id,
      amount: apInvoices.amount,
      date: apInvoices.date,
      due_date: apInvoices.due_date,
      status: apInvoices.status,
      supplier_id: apInvoices.supplier_id,
    }).from(apInvoices)
      .leftJoin(suppliers, eq(apInvoices.supplier_id, suppliers.id))
      .where(and(
        eq(apInvoices.company_id, context.companyId),
        eq(apInvoices.status, 'draft') // Unpaid invoices
      ))
  ])

  // Calculate financial metrics
  const currentRevenueTotal = Number(currentRevenue[0]?.total || 0)
  const previousRevenueTotal = Number(previousRevenue[0]?.total || 0)
  const currentExpensesTotal = Number(currentExpenses[0]?.total || 0)
  const previousExpensesTotal = Number(previousExpenses[0]?.total || 0)

  const revenueGrowth = calculatePercentageChange(currentRevenueTotal, previousRevenueTotal)
  const expensesGrowth = calculatePercentageChange(currentExpensesTotal, previousExpensesTotal)

  const currentGrossProfit = currentRevenueTotal - currentExpensesTotal
  const previousGrossProfit = previousRevenueTotal - previousExpensesTotal
  const grossMargin = currentRevenueTotal > 0 ? (currentGrossProfit / currentRevenueTotal) * 100 : 0

  // Calculate aging buckets
  const arAging = calculateAgingBuckets(arAgingData)
  const apAging = calculateAgingBuckets(apAgingData)

  // Build financial metrics response
  const financialMetrics: FinancialMetrics = {
    profitLoss: {
      revenue: {
        current: currentRevenueTotal,
        previous: previousRevenueTotal,
        growth: revenueGrowth
      },
      expenses: {
        current: currentExpensesTotal,
        previous: previousExpensesTotal,
        growth: expensesGrowth
      },
      grossProfit: {
        current: currentGrossProfit,
        previous: previousGrossProfit,
        margin: grossMargin
      },
      netProfit: {
        current: currentGrossProfit, // Simplified - same as gross for now
        previous: previousGrossProfit,
        margin: grossMargin
      }
    },
    balanceSheet: {
      assets: {
        current: currentRevenueTotal, // Simplified calculation
        fixed: 0,
        total: currentRevenueTotal
      },
      liabilities: {
        current: currentExpensesTotal,
        longTerm: 0,
        total: currentExpensesTotal
      },
      equity: {
        retained: currentGrossProfit,
        total: currentGrossProfit
      }
    },
    cashFlow: {
      operating: {
        inflow: Number(currentRevenue[0]?.paid || 0),
        outflow: Number(currentExpenses[0]?.paid || 0),
        net: Number(currentRevenue[0]?.paid || 0) - Number(currentExpenses[0]?.paid || 0)
      },
      investing: { inflow: 0, outflow: 0, net: 0 },
      financing: { inflow: 0, outflow: 0, net: 0 },
      netCashFlow: Number(currentRevenue[0]?.paid || 0) - Number(currentExpenses[0]?.paid || 0)
    },
    arAging,
    apAging
  }

  return financialMetrics
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Calculate financial reporting periods
 */
function getFinancialPeriods(period: string, dateFrom?: string | null, dateTo?: string | null) {
  const now = new Date()
  let currentFrom: Date, currentTo: Date, previousFrom: Date, previousTo: Date

  if (dateFrom && dateTo) {
    currentFrom = new Date(dateFrom)
    currentTo = new Date(dateTo)
    const periodLength = currentTo.getTime() - currentFrom.getTime()
    previousTo = new Date(currentFrom.getTime() - 1)
    previousFrom = new Date(previousTo.getTime() - periodLength)
  } else {
    switch (period) {
      case 'month':
        currentFrom = new Date(now.getFullYear(), now.getMonth(), 1)
        currentTo = new Date(now.getFullYear(), now.getMonth() + 1, 0)
        previousFrom = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        previousTo = new Date(now.getFullYear(), now.getMonth(), 0)
        break
      case 'quarter':
        const currentQuarter = Math.floor(now.getMonth() / 3)
        currentFrom = new Date(now.getFullYear(), currentQuarter * 3, 1)
        currentTo = new Date(now.getFullYear(), (currentQuarter + 1) * 3, 0)
        previousFrom = new Date(now.getFullYear(), (currentQuarter - 1) * 3, 1)
        previousTo = new Date(now.getFullYear(), currentQuarter * 3, 0)
        break
      case 'year':
        currentFrom = new Date(now.getFullYear(), 0, 1)
        currentTo = new Date(now.getFullYear(), 11, 31)
        previousFrom = new Date(now.getFullYear() - 1, 0, 1)
        previousTo = new Date(now.getFullYear() - 1, 11, 31)
        break
      default:
        currentFrom = new Date(now.getFullYear(), now.getMonth(), 1)
        currentTo = new Date(now.getFullYear(), now.getMonth() + 1, 0)
        previousFrom = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        previousTo = new Date(now.getFullYear(), now.getMonth(), 0)
    }
  }

  return {
    currentPeriod: { from: currentFrom.toISOString().split('T')[0], to: currentTo.toISOString().split('T')[0] },
    previousPeriod: { from: previousFrom.toISOString().split('T')[0], to: previousTo.toISOString().split('T')[0] }
  }
}

/**
 * Calculate aging buckets for AR/AP
 */
function calculateAgingBuckets(invoices: any[]) {
  const now = new Date()
  const buckets = {
    current: { count: 0, amount: 0 },
    overdue30: { count: 0, amount: 0 },
    overdue60: { count: 0, amount: 0 },
    overdue90: { count: 0, amount: 0 },
    total: { count: 0, amount: 0 }
  }

  invoices.forEach(invoice => {
    const amount = Number(invoice.amount || 0)
    const dueDate = new Date(invoice.due_date || invoice.date)
    const daysPastDue = Math.floor((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24))

    buckets.total.count++
    buckets.total.amount += amount

    if (daysPastDue <= 0) {
      buckets.current.count++
      buckets.current.amount += amount
    } else if (daysPastDue <= 30) {
      buckets.overdue30.count++
      buckets.overdue30.amount += amount
    } else if (daysPastDue <= 60) {
      buckets.overdue60.count++
      buckets.overdue60.amount += amount
    } else {
      buckets.overdue90.count++
      buckets.overdue90.amount += amount
    }
  })

  return buckets
}

/**
 * Calculate percentage change
 */
function calculatePercentageChange(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0
  return ((current - previous) / previous) * 100
}
