/**
 * Manufacturing ERP - Reports API Endpoint
 * Enterprise-grade reporting data aggregation and analytics
 *
 * ✅ ENHANCED: Performance optimization with query result caching
 * ✅ MAINTAINED: 100% backward compatibility with existing APIs
 * ✅ SECURED: Multi-tenant cache isolation
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import {
  arInvoices,
  apInvoices,
  salesContracts,
  purchaseContracts,
  workOrders,
  qualityInspections,
  stockLots,
  stockTxns,
  shipments,
  products
} from "@/lib/schema-postgres"
import { eq, and, gte, lte, desc, count, sum, sql } from "drizzle-orm"
import { withReportsCache, reportsCacheService } from "@/lib/cache/reports-cache"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ReportsOverview {
  summary: {
    totalReports: number
    availableCategories: number
    lastUpdated: string
  }
  quickMetrics: {
    totalRevenue: number
    totalExpenses: number
    activeContracts: number
    completedWorkOrders: number
    pendingInspections: number
    currentInventoryValue: number
  }
  recentActivity: {
    newInvoices: number
    completedOrders: number
    qualityIssues: number
    shipments: number
  }
}

// ============================================================================
// API ENDPOINTS
// ============================================================================

// 🛡️ SECURE: Multi-tenant GET endpoint for reports overview with caching
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const { searchParams } = new URL(request.url)
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const forceRefresh = searchParams.get('forceRefresh') === 'true'

    // Default to current month if no date range provided
    const defaultDateFrom = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString()
    const defaultDateTo = new Date().toISOString()

    const fromDate = dateFrom || defaultDateFrom
    const toDate = dateTo || defaultDateTo

    // ✅ PERFORMANCE: Cache parameters for multi-tenant isolation
    const cacheParams = {
      dateFrom: fromDate,
      dateTo: toDate,
    }

    // ✅ PERFORMANCE: Use caching wrapper with automatic cache management
    const reportsOverview = await withReportsCache(
      'reports-overview',
      context,
      async () => {
        console.log(`📋 Fetching fresh reports overview data for company ${context.companyId}`)
        return await fetchReportsOverviewData(context, fromDate, toDate)
      },
      cacheParams,
      { forceRefresh }
    )

    return jsonOk(reportsOverview)
  } catch (error) {
    console.error("Reports overview error:", error)
    return jsonError("Failed to fetch reports overview", 500)
  }
})

// ✅ PERFORMANCE: Extracted data fetching logic for caching
async function fetchReportsOverviewData(context: any, fromDate: string, toDate: string): Promise<ReportsOverview> {
  // Parallel data fetching for performance
  const [
    arInvoicesData,
    apInvoicesData,
    salesContractsData,
    purchaseContractsData,
    workOrdersData,
    qualityInspectionsData,
    stockLotsData,
    shipmentsData
  ] = await Promise.all([
    // AR Invoices aggregation
    db.select({
      count: count(),
      total: sum(sql`CAST(${arInvoices.amount} AS DECIMAL)`),
    }).from(arInvoices)
      .where(and(
        eq(arInvoices.company_id, context.companyId),
        gte(arInvoices.created_at, new Date(fromDate)),
        lte(arInvoices.created_at, new Date(toDate))
      )),

    // AP Invoices aggregation
    db.select({
      count: count(),
      total: sum(sql`CAST(${apInvoices.amount} AS DECIMAL)`),
    }).from(apInvoices)
      .where(and(
        eq(apInvoices.company_id, context.companyId),
        gte(apInvoices.created_at, new Date(fromDate)),
        lte(apInvoices.created_at, new Date(toDate))
      )),

    // Sales Contracts
    db.select({
      count: count(),
      active: count(sql`CASE WHEN ${salesContracts.status} = 'active' THEN 1 END`),
    }).from(salesContracts)
      .where(eq(salesContracts.company_id, context.companyId)),

    // Purchase Contracts
    db.select({
      count: count(),
      active: count(sql`CASE WHEN ${purchaseContracts.status} = 'active' THEN 1 END`),
    }).from(purchaseContracts)
      .where(eq(purchaseContracts.company_id, context.companyId)),

    // Work Orders
    db.select({
      count: count(),
      completed: count(sql`CASE WHEN ${workOrders.status} = 'completed' THEN 1 END`),
      pending: count(sql`CASE WHEN ${workOrders.status} = 'pending' THEN 1 END`),
    }).from(workOrders)
      .where(eq(workOrders.company_id, context.companyId)),

    // Quality Inspections
    db.select({
      count: count(),
      pending: count(sql`CASE WHEN ${qualityInspections.status} = 'pending' THEN 1 END`),
      passed: count(sql`CASE WHEN ${qualityInspections.status} = 'approved' THEN 1 END`),
    }).from(qualityInspections)
      .where(eq(qualityInspections.company_id, context.companyId)),

    // Stock Lots (Inventory) - JOIN with products for pricing
    db.select({
      count: count(),
      totalValue: sum(sql`CAST(${stockLots.qty} AS DECIMAL) * CAST(${products.price} AS DECIMAL)`),
    }).from(stockLots)
      .leftJoin(products, eq(stockLots.product_id, products.id))
      .where(eq(stockLots.company_id, context.companyId)),

    // Shipments
    db.select({
      count: count(),
      delivered: count(sql`CASE WHEN ${shipments.status} = 'delivered' THEN 1 END`),
    }).from(shipments)
      .where(and(
        eq(shipments.company_id, context.companyId),
        gte(shipments.created_at, new Date(fromDate)),
        lte(shipments.created_at, new Date(toDate))
      ))
  ])

  // Calculate metrics
  const totalRevenue = Number(arInvoicesData[0]?.total || 0)
  const totalExpenses = Number(apInvoicesData[0]?.total || 0)
  const activeContracts = Number(salesContractsData[0]?.active || 0) + Number(purchaseContractsData[0]?.active || 0)
  const completedWorkOrders = Number(workOrdersData[0]?.completed || 0)
  const pendingInspections = Number(qualityInspectionsData[0]?.pending || 0)
  const currentInventoryValue = Number(stockLotsData[0]?.totalValue || 0)

  // Build response
  const reportsOverview: ReportsOverview = {
    summary: {
      totalReports: 14, // Total available reports
      availableCategories: 3, // Financial, Operational, Management
      lastUpdated: new Date().toISOString()
    },
    quickMetrics: {
      totalRevenue,
      totalExpenses,
      activeContracts,
      completedWorkOrders,
      pendingInspections,
      currentInventoryValue
    },
    recentActivity: {
      newInvoices: Number(arInvoicesData[0]?.count || 0) + Number(apInvoicesData[0]?.count || 0),
      completedOrders: completedWorkOrders,
      qualityIssues: Number(qualityInspectionsData[0]?.count || 0) - Number(qualityInspectionsData[0]?.passed || 0),
      shipments: Number(shipmentsData[0]?.count || 0)
    }
  }

  return reportsOverview
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Calculate date range for reports
 */
function getDateRange(period: string = 'month') {
  const now = new Date()
  let fromDate: Date
  let toDate: Date = now

  switch (period) {
    case 'week':
      fromDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case 'month':
      fromDate = new Date(now.getFullYear(), now.getMonth(), 1)
      break
    case 'quarter':
      const quarterStart = Math.floor(now.getMonth() / 3) * 3
      fromDate = new Date(now.getFullYear(), quarterStart, 1)
      break
    case 'year':
      fromDate = new Date(now.getFullYear(), 0, 1)
      break
    default:
      fromDate = new Date(now.getFullYear(), now.getMonth(), 1)
  }

  return { fromDate, toDate }
}

/**
 * Format currency values
 */
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value)
}

/**
 * Calculate percentage change
 */
function calculatePercentageChange(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0
  return ((current - previous) / previous) * 100
}
