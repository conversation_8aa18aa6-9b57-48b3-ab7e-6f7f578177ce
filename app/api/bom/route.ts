/**
 * Manufacturing ERP - BOM Overview API
 * 
 * Comprehensive API for Bill of Materials overview and statistics
 * Provides aggregated BOM data across all products for management dashboard
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - BOM Management Implementation
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { billOfMaterials, products, rawMaterials, suppliers } from "@/lib/schema-postgres"
import { eq, and, desc, like, or, count, sql } from "drizzle-orm"

// ✅ PROFESSIONAL: BOM Overview API
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'
    const includeDetails = searchParams.get('details') === 'true'

    // ✅ PROFESSIONAL: Base query for BOM overview
    const bomOverviewQuery = db
      .select({
        productId: products.id,
        productName: products.name,
        productSku: products.sku,
        productUnit: products.unit,
        productStatus: products.status,
        productCreatedAt: products.created_at,
        productBasePrice: products.base_price,
        productCostPrice: products.cost_price,
        productCurrency: products.currency,
        bomId: billOfMaterials.id,
        bomQtyRequired: billOfMaterials.qty_required,
        bomWasteFactor: billOfMaterials.waste_factor,
        bomStatus: billOfMaterials.status,
        bomUpdatedAt: billOfMaterials.updated_at,
        materialId: rawMaterials.id,
        materialName: rawMaterials.name,
        materialSku: rawMaterials.sku,
        materialUnit: rawMaterials.unit,
        materialCost: rawMaterials.standard_cost,
        materialCategory: rawMaterials.category,
        supplierId: suppliers.id,
        supplierName: suppliers.name,
      })
      .from(products)
      .leftJoin(
        billOfMaterials,
        and(
          eq(billOfMaterials.product_id, products.id),
          eq(billOfMaterials.company_id, context.companyId),
          eq(billOfMaterials.status, 'active')
        )
      )
      .leftJoin(rawMaterials, eq(rawMaterials.id, billOfMaterials.raw_material_id))
      .leftJoin(suppliers, eq(suppliers.id, rawMaterials.primary_supplier_id))
      .where(
        and(
          eq(products.company_id, context.companyId),
          eq(products.status, 'active'),
          search ? or(
            like(products.name, `%${search}%`),
            like(products.sku, `%${search}%`)
          ) : undefined
        )
      )
      .orderBy(desc(products.created_at))

    const bomOverviewData = await bomOverviewQuery

    // ✅ PROFESSIONAL: Process data into structured format
    const bomSummaryMap = new Map()

    bomOverviewData.forEach((row) => {
      if (!bomSummaryMap.has(row.productId)) {
        bomSummaryMap.set(row.productId, {
          productId: row.productId,
          productName: row.productName,
          productSku: row.productSku,
          productUnit: row.productUnit,
          productStatus: row.productStatus,
          productBasePrice: row.productBasePrice,
          productCostPrice: row.productCostPrice,
          productCurrency: row.productCurrency || 'USD',
          bomItemCount: 0,
          totalEstimatedCost: 0,
          hasIncompleteBOM: false,
          lastUpdated: new Date(0),
          materials: includeDetails ? [] : undefined,
          categories: new Set(),
          suppliers: new Set(),
        })
      }

      const summary = bomSummaryMap.get(row.productId)

      if (row.bomId) {
        summary.bomItemCount++

        // Calculate cost
        if (row.materialCost && row.bomQtyRequired) {
          const qty = parseFloat(row.bomQtyRequired)
          const wasteFactor = parseFloat(row.bomWasteFactor || '0.05')
          const totalQty = qty * (1 + wasteFactor)
          const cost = parseFloat(row.materialCost)
          summary.totalEstimatedCost += totalQty * cost
        }

        // Check for incomplete BOM (missing cost data)
        if (!row.materialCost) {
          summary.hasIncompleteBOM = true
        }

        // Track categories and suppliers
        if (row.materialCategory) {
          summary.categories.add(row.materialCategory)
        }
        if (row.supplierName) {
          summary.suppliers.add(row.supplierName)
        }

        // Track last updated
        if (row.bomUpdatedAt && new Date(row.bomUpdatedAt) > summary.lastUpdated) {
          summary.lastUpdated = new Date(row.bomUpdatedAt)
        }

        // Include material details if requested
        if (includeDetails && row.materialId) {
          summary.materials.push({
            id: row.materialId,
            name: row.materialName,
            sku: row.materialSku,
            unit: row.materialUnit,
            category: row.materialCategory,
            qtyRequired: row.bomQtyRequired,
            wasteFactor: row.bomWasteFactor,
            estimatedCost: row.materialCost ?
              (parseFloat(row.bomQtyRequired) * (1 + parseFloat(row.bomWasteFactor || '0.05')) * parseFloat(row.materialCost)) : 0,
            supplier: row.supplierName,
          })
        }
      }
    })

    // ✅ PROFESSIONAL: Convert sets to arrays and calculate profitability metrics
    const bomList = Array.from(bomSummaryMap.values()).map(item => {
      // ✅ PROFESSIONAL: Calculate profit and margin metrics
      const basePrice = item.productBasePrice ? parseFloat(item.productBasePrice) : 0
      const materialCost = item.totalEstimatedCost
      const profit = basePrice - materialCost
      const marginPercentage = basePrice > 0 ? (profit / basePrice) * 100 : 0

      return {
        ...item,
        categories: Array.from(item.categories),
        suppliers: Array.from(item.suppliers),
        // ✅ NEW: Profitability analysis fields
        sellingPrice: basePrice,
        materialCost: materialCost,
        profit: profit,
        marginPercentage: marginPercentage,
        profitabilityStatus: marginPercentage >= 30 ? 'excellent' :
          marginPercentage >= 20 ? 'good' :
            marginPercentage >= 10 ? 'fair' : 'poor'
      }
    })

    // ✅ PROFESSIONAL: Apply status filter
    const filteredBomList = bomList.filter((item) => {
      if (status === 'complete') return item.bomItemCount > 0 && !item.hasIncompleteBOM
      if (status === 'incomplete') return item.bomItemCount === 0 || item.hasIncompleteBOM
      if (status === 'empty') return item.bomItemCount === 0
      return true // 'all'
    })

    // ✅ PROFESSIONAL: Calculate comprehensive statistics
    const stats = {
      totalProducts: bomList.length,
      productsWithBOM: bomList.filter(item => item.bomItemCount > 0).length,
      productsWithoutBOM: bomList.filter(item => item.bomItemCount === 0).length,
      incompleteBOMs: bomList.filter(item => item.hasIncompleteBOM).length,
      totalBOMItems: bomList.reduce((sum, item) => sum + item.bomItemCount, 0),
      totalEstimatedValue: bomList.reduce((sum, item) => sum + item.totalEstimatedCost, 0),
      averageBOMComplexity: bomList.length > 0 ?
        bomList.reduce((sum, item) => sum + item.bomItemCount, 0) / bomList.length : 0,
      topCategories: await getTopMaterialCategories(context.companyId),
      topSuppliers: await getTopSuppliers(context.companyId),
    }

    return jsonOk({
      bomOverview: filteredBomList,
      statistics: stats,
      filters: {
        search,
        status,
        includeDetails,
      },
      pagination: {
        total: filteredBomList.length,
        page: 1,
        limit: filteredBomList.length,
      }
    })

  } catch (error) {
    console.error("BOM Overview API Error:", error)
    return jsonError("Failed to fetch BOM overview", 500)
  }
})

// ✅ HELPER: Get top material categories
async function getTopMaterialCategories(companyId: string) {
  try {
    const categories = await db
      .select({
        category: rawMaterials.category,
        count: count(billOfMaterials.id),
      })
      .from(billOfMaterials)
      .innerJoin(rawMaterials, eq(rawMaterials.id, billOfMaterials.raw_material_id))
      .where(
        and(
          eq(billOfMaterials.company_id, companyId),
          eq(billOfMaterials.status, 'active')
        )
      )
      .groupBy(rawMaterials.category)
      .orderBy(desc(count(billOfMaterials.id)))
      .limit(5)

    return categories
  } catch (error) {
    console.error("Error fetching top categories:", error)
    return []
  }
}

// ✅ HELPER: Get top suppliers
async function getTopSuppliers(companyId: string) {
  try {
    const topSuppliers = await db
      .select({
        supplierId: suppliers.id,
        supplierName: suppliers.name,
        materialCount: count(billOfMaterials.id),
      })
      .from(billOfMaterials)
      .innerJoin(rawMaterials, eq(rawMaterials.id, billOfMaterials.raw_material_id))
      .innerJoin(suppliers, eq(suppliers.id, rawMaterials.primary_supplier_id))
      .where(
        and(
          eq(billOfMaterials.company_id, companyId),
          eq(billOfMaterials.status, 'active')
        )
      )
      .groupBy(suppliers.id, suppliers.name)
      .orderBy(desc(count(billOfMaterials.id)))
      .limit(5)

    return topSuppliers
  } catch (error) {
    console.error("Error fetching top suppliers:", error)
    return []
  }
}
