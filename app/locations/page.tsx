"use client"

import { AppShell } from "@/components/app-shell"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { useState, useEffect } from "react"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Building,
  MapPin,
  Package,
  CheckCircle,
  Warehouse
} from "lucide-react"

// ============================================================================
// TYPES - Database-driven location types
// ============================================================================
interface Location {
  id: string
  company_id: string
  name: string
  type: LocationType
  description?: string
  capacity: number
  current_utilization: number
  is_active: boolean
  is_temperature_controlled: boolean
  security_level: 'low' | 'medium' | 'high'
  parent_location_id?: string
  location_code?: string
  address?: string
  floor_level?: number
  zone?: string
  allows_mixed_products: boolean
  requires_quality_check: boolean
  automated_handling: boolean
  created_at: string
  updated_at: string
}

type LocationType =
  | 'warehouse'
  | 'raw_materials'
  | 'finished_goods'
  | 'work_in_progress'
  | 'quality_control'
  | 'shipping'
  | 'receiving'
  | 'quarantine'
  | 'returns'

export default function LocationManagementPage() {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [locations, setLocations] = useState<Location[]>([])
  const [editingLocation, setEditingLocation] = useState<Location | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [loading, setLoading] = useState(true)
  const [utilizationData, setUtilizationData] = useState<any>(null)
  const [utilizationLoading, setUtilizationLoading] = useState(false)

  const locationTypes: Array<{ value: LocationType, label: string, description: string }> = [
    { value: 'warehouse', label: 'Warehouse', description: 'General warehouse storage' },
    { value: 'raw_materials', label: 'Raw Materials', description: 'Storage for incoming raw materials' },
    { value: 'finished_goods', label: 'Finished Goods', description: 'Completed products storage' },
    { value: 'work_in_progress', label: 'Work in Progress', description: 'Production and assembly areas' },
    { value: 'quality_control', label: 'Quality Control', description: 'Quality inspection areas' },
    { value: 'shipping', label: 'Shipping', description: 'Outbound shipping areas' },
    { value: 'receiving', label: 'Receiving', description: 'Inbound receiving areas' },
    { value: 'quarantine', label: 'Quarantine', description: 'Isolated storage for quality issues' },
    { value: 'returns', label: 'Returns', description: 'Returns processing area' }
  ]

  const [newLocation, setNewLocation] = useState({
    name: '',
    type: 'warehouse' as LocationType,
    description: '',
    capacity: 1000,
    is_active: true,
    is_temperature_controlled: false,
    security_level: 'medium' as const,
    allows_mixed_products: true,
    requires_quality_check: false,
    automated_handling: false,
    location_code: '',
    zone: ''
  })

  useEffect(() => {
    loadLocations()
    loadUtilizationData()
  }, [])

  async function loadUtilizationData() {
    setUtilizationLoading(true)
    try {
      console.log("🔍 Loading utilization data...")
      const response = await fetch("/api/locations/utilization")
      if (response.ok) {
        const data = await response.json()
        console.log("✅ Utilization data loaded:", data)
        setUtilizationData(data)
      } else {
        console.log("⚠️ Utilization API not available:", response.status)
        // Don't show error for utilization - it's optional
      }
    } catch (error) {
      console.log('⚠️ Utilization data unavailable:', error)
      // Don't show error for utilization - it's optional
    } finally {
      setUtilizationLoading(false)
    }
  }

  async function loadLocations() {
    setLoading(true)
    try {
      console.log("🔍 Loading locations from database...")
      const response = await fetch("/api/locations")

      if (response.ok) {
        const data = await response.json()
        console.log("✅ Locations loaded:", data.length)
        setLocations(data)
      } else {
        console.error("❌ Failed to load locations:", response.status)
        toast({
          title: "Error",
          description: "Failed to load locations",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('❌ Error loading locations:', error)
      toast({
        title: "Error",
        description: "Failed to load locations",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  async function handleCreateLocation() {
    if (!newLocation.name || !newLocation.type) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      })
      return
    }

    try {
      console.log("📝 Creating location:", newLocation.name)
      const response = await fetch("/api/locations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newLocation)
      })

      if (response.ok) {
        const createdLocation = await response.json()
        console.log("✅ Location created:", createdLocation.id)

        toast({
          title: "Success",
          description: `Location "${newLocation.name}" created successfully`
        })

        loadLocations()
        setIsCreating(false)

        // Reset form
        setNewLocation({
          name: '',
          type: 'warehouse' as LocationType,
          description: '',
          capacity: 1000,
          is_active: true,
          is_temperature_controlled: false,
          security_level: 'medium' as const,
          allows_mixed_products: true,
          requires_quality_check: false,
          automated_handling: false,
          location_code: '',
          zone: ''
        })
      } else {
        const error = await response.json()
        console.error("❌ Failed to create location:", error)
        toast({
          title: "Error",
          description: error.message || "Failed to create location",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('❌ Error creating location:', error)
      toast({
        title: "Error",
        description: "Failed to create location",
        variant: "destructive"
      })
    }
  }

  async function handleUpdateLocation() {
    if (!editingLocation) return

    try {
      console.log("📝 Updating location:", editingLocation.id)
      console.log("📝 Sending data:", editingLocation)
      const response = await fetch(`/api/locations/${editingLocation.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(editingLocation)
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Location updated successfully"
        })
        loadLocations()
        setEditingLocation(null)
      } else {
        const error = await response.json()
        console.error("❌ Update failed:", error)
        toast({
          title: "Error",
          description: error.message || "Failed to update location",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('❌ Error updating location:', error)
      toast({
        title: "Error",
        description: "Failed to update location",
        variant: "destructive"
      })
    }
  }

  async function handleDeleteLocation(id: string) {
    if (!confirm('Are you sure you want to delete this location?')) return

    try {
      console.log("🗑️ Deleting location:", id)
      const response = await fetch(`/api/locations/${id}`, {
        method: "DELETE"
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Location deleted successfully"
        })
        loadLocations()
      } else {
        const error = await response.json()
        toast({
          title: "Error",
          description: error.message || "Failed to delete location",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('❌ Error deleting location:', error)
      toast({
        title: "Error",
        description: "Failed to delete location",
        variant: "destructive"
      })
    }
  }

  if (loading) {
    return (
      <AppShell>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading locations...</p>
          </div>
        </div>
      </AppShell>
    )
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Professional Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Warehouse className="h-8 w-8" />
              Location Management
            </h1>
            <p className="text-muted-foreground">
              Configure warehouse locations, capacities, and operational attributes
            </p>
          </div>
          <Button onClick={() => setIsCreating(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Location
          </Button>
        </div>

        {/* Location Overview KPIs */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">Total Locations</p>
                  <p className="text-2xl font-bold">{locations.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Package className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">Total Capacity</p>
                  <p className="text-2xl font-bold">
                    {locations.reduce((sum, loc) => sum + loc.capacity, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">Active Locations</p>
                  <p className="text-2xl font-bold">
                    {locations.filter(loc => loc.is_active).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">Overall Utilization</p>
                  <p className="text-2xl font-bold">
                    {utilizationData?.summary?.overallUtilization?.toFixed(1) || '0'}%
                  </p>
                  {utilizationData?.summary && (
                    <div className="mt-2">
                      <Progress
                        value={utilizationData.summary.overallUtilization}
                        className="h-2"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        {utilizationData.summary.totalUsed?.toLocaleString()} / {utilizationData.summary.totalCapacity?.toLocaleString()} units
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Create New Location Form */}
        {isCreating && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Create New Location
              </CardTitle>
              <CardDescription>
                Add a new location to your facility management system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label>Location Name (ID)</Label>
                  <Input
                    value={newLocation.name}
                    onChange={(e) => setNewLocation({ ...newLocation, name: e.target.value })}
                    placeholder="e.g., warehouse_north"
                  />
                </div>
                <div className="grid gap-2">
                  <Label>Location Code</Label>
                  <Input
                    value={newLocation.location_code}
                    onChange={(e) => setNewLocation({ ...newLocation, location_code: e.target.value })}
                    placeholder="e.g., WH-001"
                  />
                </div>
              </div>

              <div className="grid gap-2">
                <Label>Description</Label>
                <Input
                  value={newLocation.description}
                  onChange={(e) => setNewLocation({ ...newLocation, description: e.target.value })}
                  placeholder="Brief description of the location purpose"
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="grid gap-2">
                  <Label>Type</Label>
                  <Select
                    value={newLocation.type}
                    onValueChange={(v) => setNewLocation({ ...newLocation, type: v as LocationType })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {locationTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label>Capacity</Label>
                  <Input
                    type="number"
                    value={newLocation.capacity}
                    onChange={(e) => setNewLocation({ ...newLocation, capacity: Number(e.target.value) })}
                    min="1"
                  />
                </div>
                <div className="grid gap-2">
                  <Label>Zone</Label>
                  <Input
                    value={newLocation.zone}
                    onChange={(e) => setNewLocation({ ...newLocation, zone: e.target.value })}
                    placeholder="e.g., A1, B2"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreating(false)}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button onClick={handleCreateLocation}>
                  <Save className="mr-2 h-4 w-4" />
                  Create Location
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Locations Table */}
        <Card>
          <CardHeader>
            <CardTitle>Location Directory</CardTitle>
            <CardDescription>
              Manage all facility locations, capacities, and configurations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Location</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Capacity</TableHead>
                  <TableHead>Utilization</TableHead>
                  <TableHead>Details</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {locations.map((location) => (
                  <TableRow key={location.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">📦</span>
                        <div>
                          <p className="font-medium">{location.name}</p>
                          <p className="text-xs text-muted-foreground">{location.description}</p>
                          {location.location_code && (
                            <p className="text-xs text-blue-600">{location.location_code}</p>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {locationTypes.find(t => t.value === location.type)?.label || location.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="font-mono">{location.capacity.toLocaleString()}</span>
                    </TableCell>
                    <TableCell>
                      {(() => {
                        const locationUtil = utilizationData?.locations?.find(
                          (util: any) => util.locationId === location.id || util.locationId === location.name
                        )
                        if (locationUtil) {
                          return (
                            <div className="space-y-1">
                              <div className="flex items-center justify-between text-sm">
                                <span>{locationUtil.utilizationPercentage}%</span>
                                <span className="text-muted-foreground">
                                  {locationUtil.currentStock.toLocaleString()}
                                </span>
                              </div>
                              <Progress
                                value={locationUtil.utilizationPercentage}
                                className="h-2"
                              />
                              <div className="text-xs text-muted-foreground">
                                {locationUtil.availableCapacity.toLocaleString()} available
                              </div>
                            </div>
                          )
                        }
                        return (
                          <div className="text-sm text-muted-foreground">
                            {utilizationLoading ? 'Loading...' : 'No data'}
                          </div>
                        )
                      })()}
                    </TableCell>
                    <TableCell>
                      <div className="text-xs">
                        {location.zone && <p>{location.zone}</p>}
                        <p className="text-muted-foreground">
                          Security: {location.security_level}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      {location.is_active ? (
                        <Badge className="bg-green-100 text-green-800">Active</Badge>
                      ) : (
                        <Badge variant="secondary">Inactive</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingLocation(location)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteLocation(location.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Create Location Dialog */}
        <Dialog open={isCreating} onOpenChange={setIsCreating}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Location</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="create-name">Location Name</Label>
                  <Input
                    id="create-name"
                    placeholder="Enter location name"
                  />
                </div>
                <div>
                  <Label htmlFor="create-type">Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="finished_goods">Finished Goods</SelectItem>
                      <SelectItem value="raw_materials">Raw Materials</SelectItem>
                      <SelectItem value="work_in_progress">Work in Progress</SelectItem>
                      <SelectItem value="quality_control">Quality Control</SelectItem>
                      <SelectItem value="shipping">Shipping</SelectItem>
                      <SelectItem value="receiving">Receiving</SelectItem>
                      <SelectItem value="returns">Returns</SelectItem>
                      <SelectItem value="quarantine">Quarantine</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="create-description">Description</Label>
                <Textarea
                  id="create-description"
                  placeholder="Enter location description"
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="create-capacity">Capacity</Label>
                  <Input
                    id="create-capacity"
                    type="number"
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="create-location-code">Location Code</Label>
                  <Input
                    id="create-location-code"
                    placeholder="e.g., WH-001"
                  />
                </div>
                <div>
                  <Label htmlFor="create-zone">Zone</Label>
                  <Input
                    id="create-zone"
                    placeholder="e.g., A1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="create-security-level">Security Level</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select security level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox id="create-temperature-controlled" />
                  <Label htmlFor="create-temperature-controlled">Temperature Controlled</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="create-allows-mixed-products" />
                  <Label htmlFor="create-allows-mixed-products">Allows Mixed Products</Label>
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setIsCreating(false)}>
                  Cancel
                </Button>
                <Button onClick={() => {
                  // Create location logic would go here
                  setIsCreating(false)
                  toast({
                    title: "Success",
                    description: "Location created successfully"
                  })
                }}>
                  Create Location
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Edit Location Dialog */}
        <Dialog open={!!editingLocation} onOpenChange={() => setEditingLocation(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Location</DialogTitle>
            </DialogHeader>
            {editingLocation && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-name">Location Name</Label>
                    <Input
                      id="edit-name"
                      value={editingLocation.name}
                      onChange={(e) => setEditingLocation({
                        ...editingLocation,
                        name: e.target.value
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-type">Type</Label>
                    <Select
                      value={editingLocation.type}
                      onValueChange={(value) => setEditingLocation({
                        ...editingLocation,
                        type: value
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="finished_goods">Finished Goods</SelectItem>
                        <SelectItem value="raw_materials">Raw Materials</SelectItem>
                        <SelectItem value="work_in_progress">Work in Progress</SelectItem>
                        <SelectItem value="quality_control">Quality Control</SelectItem>
                        <SelectItem value="shipping">Shipping</SelectItem>
                        <SelectItem value="receiving">Receiving</SelectItem>
                        <SelectItem value="returns">Returns</SelectItem>
                        <SelectItem value="quarantine">Quarantine</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    value={editingLocation.description || ''}
                    onChange={(e) => setEditingLocation({
                      ...editingLocation,
                      description: e.target.value
                    })}
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="edit-capacity">Capacity</Label>
                    <Input
                      id="edit-capacity"
                      type="number"
                      value={editingLocation.capacity}
                      onChange={(e) => setEditingLocation({
                        ...editingLocation,
                        capacity: parseInt(e.target.value) || 0
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-location-code">Location Code</Label>
                    <Input
                      id="edit-location-code"
                      value={editingLocation.location_code || ''}
                      onChange={(e) => setEditingLocation({
                        ...editingLocation,
                        location_code: e.target.value
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-zone">Zone</Label>
                    <Input
                      id="edit-zone"
                      value={editingLocation.zone || ''}
                      onChange={(e) => setEditingLocation({
                        ...editingLocation,
                        zone: e.target.value
                      })}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="edit-security-level">Security Level</Label>
                  <Select
                    value={editingLocation.security_level || 'medium'}
                    onValueChange={(value) => setEditingLocation({
                      ...editingLocation,
                      security_level: value
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="edit-temperature-controlled"
                      checked={editingLocation.temperature_controlled || false}
                      onCheckedChange={(checked) => setEditingLocation({
                        ...editingLocation,
                        temperature_controlled: checked as boolean
                      })}
                    />
                    <Label htmlFor="edit-temperature-controlled">Temperature Controlled</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="edit-allows-mixed-products"
                      checked={editingLocation.allows_mixed_products || false}
                      onCheckedChange={(checked) => setEditingLocation({
                        ...editingLocation,
                        allows_mixed_products: checked as boolean
                      })}
                    />
                    <Label htmlFor="edit-allows-mixed-products">Allows Mixed Products</Label>
                  </div>
                </div>

                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={() => setEditingLocation(null)}>
                    Cancel
                  </Button>
                  <Button onClick={handleUpdateLocation}>
                    Update Location
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AppShell>
  )
}
