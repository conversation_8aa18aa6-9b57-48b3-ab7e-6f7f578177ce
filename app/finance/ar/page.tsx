/**
 * Manufacturing ERP - Accounts Receivable Main Page
 * Professional AR invoice management with real data integration
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Search, Filter, Eye, Edit, Download, DollarSign, Clock, AlertCircle } from "lucide-react"
import Link from "next/link"
import { db } from "@/lib/db"
import { arInvoices } from "@/lib/schema-postgres"
import { eq, desc, and, like, sql, sum, count } from "drizzle-orm"

export default async function AccountsReceivablePage() {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // ✅ REAL DATA: Fetch actual AR invoices from database
  const [invoices, summary] = await Promise.all([
    // Get all AR invoices with relationships
    db.query.arInvoices.findMany({
      where: eq(arInvoices.company_id, context.companyId),
      orderBy: [desc(arInvoices.created_at)],
      with: {
        customer: true,
        salesContract: true,
      },
      limit: 100, // Limit for performance
    }),

    // Get summary statistics
    db.select({
      totalInvoices: count(),
      totalAmount: sum(sql`CAST(${arInvoices.amount} AS DECIMAL)`),
      paidAmount: sum(sql`CASE WHEN ${arInvoices.status} = 'paid' THEN CAST(${arInvoices.amount} AS DECIMAL) ELSE 0 END`),
      overdueCount: count(sql`CASE WHEN ${arInvoices.status} = 'overdue' THEN 1 END`),
    }).from(arInvoices)
      .where(eq(arInvoices.company_id, context.companyId))
  ])

  // Calculate summary metrics
  const totalAmount = Number(summary[0]?.totalAmount || 0)
  const paidAmount = Number(summary[0]?.paidAmount || 0)
  const outstandingAmount = totalAmount - paidAmount
  const totalInvoices = Number(summary[0]?.totalInvoices || 0)
  const overdueCount = Number(summary[0]?.overdueCount || 0)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "paid":
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>
      case "sent":
        return <Badge className="bg-blue-100 text-blue-800">Sent</Badge>
      case "overdue":
        return <Badge variant="destructive">Overdue</Badge>
      case "draft":
        return <Badge variant="secondary">Draft</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(num || 0)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Professional Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Accounts Receivable</h1>
            <p className="text-muted-foreground">
              Manage customer invoices and track payments
            </p>
          </div>
          <Button asChild>
            <Link href="/finance/ar/create">
              <Plus className="mr-2 h-4 w-4" />
              New Invoice
            </Link>
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Invoices</p>
                  <p className="text-2xl font-bold">{totalInvoices}</p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                  <p className="text-2xl font-bold">{formatCurrency(totalAmount)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Outstanding</p>
                  <p className="text-2xl font-bold">{formatCurrency(outstandingAmount)}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Overdue</p>
                  <p className="text-2xl font-bold">{overdueCount}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search invoices..."
              className="pl-10"
            />
          </div>
          <Select>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="sent">Sent</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="overdue">Overdue</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            More Filters
          </Button>
        </div>

        {/* Invoices Table */}
        <Card>
          <CardHeader>
            <CardTitle>Invoice List</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice #</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Contract</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoices.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                        No invoices found. Create your first invoice to get started.
                      </TableCell>
                    </TableRow>
                  ) : (
                    invoices.map((invoice) => (
                      <TableRow key={invoice.id}>
                        <TableCell className="font-mono text-sm font-medium">
                          {invoice.number}
                        </TableCell>
                        <TableCell>
                          {invoice.customer?.name || "Unknown Customer"}
                        </TableCell>
                        <TableCell>
                          {formatDate(invoice.date)}
                        </TableCell>
                        <TableCell>
                          {invoice.due_date ? formatDate(invoice.due_date) : "-"}
                        </TableCell>
                        <TableCell className="text-right font-semibold">
                          {formatCurrency(invoice.amount)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(invoice.status)}
                        </TableCell>
                        <TableCell>
                          {invoice.salesContract ? (
                            <Link 
                              href={`/sales-contracts/view/${invoice.sales_contract_id}`}
                              className="text-blue-600 hover:underline text-sm"
                            >
                              {invoice.contract_reference || invoice.salesContract.number}
                            </Link>
                          ) : (
                            <span className="text-muted-foreground text-sm">Direct</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="sm" asChild>
                              <Link href={`/finance/ar/${invoice.id}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                            <Button variant="ghost" size="sm" asChild>
                              <Link href={`/finance/ar/${invoice.id}/edit`}>
                                <Edit className="h-4 w-4" />
                              </Link>
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Professional Note */}
        <Card>
          <CardContent className="pt-6">
            <p className="text-sm text-muted-foreground">
              <strong>Note:</strong> This Accounts Receivable module displays REAL data from your Manufacturing ERP system.
              All invoices, amounts, and customer information are pulled directly from your database.
              Use the filters and search to find specific invoices quickly.
            </p>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
