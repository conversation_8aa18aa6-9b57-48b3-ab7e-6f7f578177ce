import { redirect } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { customers, salesContracts } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { AppShell } from "@/components/app-shell"
import { ARInvoiceCreateClient } from "./ar-invoice-create-client"

export default async function ARInvoiceCreatePage() {
  const context = await getTenantContext()
  
  if (!context) {
    redirect('/api/auth/login')
  }

  // 🛡️ SECURE: Fetch supporting data for the current company only
  const [allCustomers, allSalesContracts] = await Promise.all([
    db.query.customers.findMany({
      where: eq(customers.company_id, context.companyId),
      orderBy: (customers, { asc }) => [asc(customers.name)],
    }),
    db.query.salesContracts.findMany({
      where: eq(salesContracts.company_id, context.companyId),
      orderBy: (salesContracts, { desc }) => [desc(salesContracts.created_at)],
      with: {
        customer: true
      }
    })
  ])

  return (
    <AppShell>
      <ARInvoiceCreateClient
        customers={allCustomers}
        salesContracts={allSalesContracts}
        companyId={context.companyId}
      />
    </AppShell>
  )
}
