import { notFound, redirect } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { arInvoices } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { AppShell } from "@/components/app-shell"
import { ARInvoiceViewClient } from "./ar-invoice-view-client"

interface ARInvoiceViewPageProps {
  params: Promise<{ id: string }>
}

export default async function ARInvoiceViewPage({ params }: ARInvoiceViewPageProps) {
  const { id } = await params
  const context = await getTenantContext()
  
  if (!context) {
    redirect('/api/auth/login')
  }

  // 🛡️ SECURE: Fetch the AR invoice with related data and multi-tenant isolation
  const invoice = await db.query.arInvoices.findFirst({
    where: and(
      eq(arInvoices.id, id),
      eq(arInvoices.company_id, context.companyId)
    ),
    with: {
      customer: true,
      salesContract: {
        with: {
          items: {
            with: {
              product: true
            }
          }
        }
      }
    }
  })

  if (!invoice) {
    notFound()
  }

  return (
    <AppShell>
      <ARInvoiceViewClient 
        invoice={invoice}
        companyId={context.companyId}
      />
    </AppShell>
  )
}
