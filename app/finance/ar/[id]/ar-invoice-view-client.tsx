"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import {
  ArrowLeft,
  Edit,
  FileText,
  DollarSign,
  Calendar,
  User,
  Building2,
  CreditCard,
  Clock,
  Receipt
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ARInvoiceViewProps {
  invoice: {
    id: string
    number: string
    customer_id: string
    sales_contract_id?: string
    amount: string
    received: string
    currency: string
    status: string
    date: string
    due_date: string
    payment_terms: string
    notes?: string
    created_at?: string
    updated_at?: string
    customer?: {
      id: string
      name: string
      email: string
      phone?: string
      address?: string
    }
    salesContract?: {
      id: string
      number: string
      title: string
      total_amount?: string
      currency?: string
      items?: Array<{
        id: string
        product_id: string
        qty: number
        price: string
        product?: {
          id: string
          name: string
          sku: string
        }
      }>
    }
  }
  companyId: string
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const formatCurrency = (amount: string, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
  }).format(parseFloat(amount || '0'))
}

const formatDate = (dateString: string): string => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString()
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'paid': return 'bg-green-100 text-green-800'
    case 'deposit_received': return 'bg-blue-100 text-blue-800'
    case 'partial_paid': return 'bg-yellow-100 text-yellow-800'
    case 'sent': return 'bg-purple-100 text-purple-800'
    case 'overdue': return 'bg-red-100 text-red-800'
    case 'draft': return 'bg-gray-100 text-gray-800'
    case 'cancelled': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const calculateAgingDays = (dateString: string): number => {
  if (!dateString) return 0
  const invoiceDate = new Date(dateString)
  const today = new Date()
  const diffTime = today.getTime() - invoiceDate.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}

const getAgingColor = (days: number): string => {
  if (days <= 30) return 'text-green-600'
  if (days <= 60) return 'text-yellow-600'
  return 'text-red-600'
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function ARInvoiceViewClient({ invoice, companyId }: ARInvoiceViewProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  // Calculate financial metrics
  const totalAmount = parseFloat(invoice.amount || '0')
  const receivedAmount = parseFloat(invoice.received || '0')
  const balanceAmount = totalAmount - receivedAmount
  const agingDays = calculateAgingDays(invoice.date)

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL HEADER WITH BREADCRUMB */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/finance">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Finance
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">AR Invoice {invoice.number}</h1>
            <p className="text-muted-foreground">
              Accounts Receivable Invoice Details
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href={`/finance/ar/${invoice.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Invoice
            </Link>
          </Button>
          <Button
            variant="outline"
            onClick={async () => {
              setLoading(true)
              try {
                // Fetch invoice data for PDF generation
                const response = await fetch(`/api/finance/ar/${invoice.id}/pdf`)
                if (!response.ok) {
                  throw new Error('Failed to fetch invoice data')
                }

                const { invoiceData } = await response.json()

                // Import PDF generation function dynamically
                const { generateInvoicePDF } = await import('@/components/pdf-invoice-document')

                // Generate and download PDF
                await generateInvoicePDF(invoiceData, 'ar')

                toast({
                  title: "PDF Generated",
                  description: `AR Invoice ${invoice.number} has been exported as PDF`,
                  variant: "default"
                })
              } catch (error) {
                console.error('PDF generation error:', error)
                toast({
                  title: "PDF Generation Failed",
                  description: "Failed to generate PDF. Please try again.",
                  variant: "destructive"
                })
              } finally {
                setLoading(false)
              }
            }}
            disabled={loading}
          >
            <FileText className="mr-2 h-4 w-4" />
            {loading ? 'Generating...' : 'Generate PDF'}
          </Button>
        </div>
      </div>

      {/* ✅ INVOICE OVERVIEW CARDS */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Invoice Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(invoice.amount, invoice.currency)}</div>
            <p className="text-xs text-muted-foreground">Total invoice value</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Amount Received</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(invoice.received, invoice.currency)}</div>
            <p className="text-xs text-muted-foreground">Payments received</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Balance Due</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(balanceAmount.toString(), invoice.currency)}</div>
            <p className="text-xs text-muted-foreground">Outstanding balance</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aging</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getAgingColor(agingDays)}`}>
              {agingDays} days
            </div>
            <p className="text-xs text-muted-foreground">Since invoice date</p>
          </CardContent>
        </Card>
      </div>

      {/* ✅ INVOICE DETAILS */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Invoice Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Invoice Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Invoice Number</label>
                <p className="font-medium">{invoice.number}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Status</label>
                <div className="mt-1">
                  <Badge className={getStatusColor(invoice.status)}>
                    {invoice.status.toUpperCase()}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Invoice Date</label>
                <p className="font-medium">{formatDate(invoice.date)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Due Date</label>
                <p className="font-medium">{formatDate(invoice.due_date)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Payment Terms</label>
                <p className="font-medium">{invoice.payment_terms || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Currency</label>
                <p className="font-medium">{invoice.currency}</p>
              </div>
            </div>
            {invoice.notes && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Notes</label>
                <p className="text-sm mt-1 p-2 bg-gray-50 rounded-md">{invoice.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {invoice.customer ? (
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Customer Name</label>
                  <p className="font-medium">{invoice.customer.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email</label>
                  <p className="font-medium">{invoice.customer.email}</p>
                </div>
                {invoice.customer.phone && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Phone</label>
                    <p className="font-medium">{invoice.customer.phone}</p>
                  </div>
                )}
                {invoice.customer.address && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Address</label>
                    <p className="text-sm mt-1 p-2 bg-gray-50 rounded-md">{invoice.customer.address}</p>
                  </div>
                )}
                <div className="pt-2">
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/crm/${invoice.customer.id}`}>
                      <Building2 className="mr-2 h-4 w-4" />
                      View Customer Details
                    </Link>
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">Customer information not available</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* ✅ SALES CONTRACT INFORMATION (if linked) */}
      {invoice.salesContract && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Linked Sales Contract
            </CardTitle>
            <CardDescription>
              This invoice is linked to sales contract {invoice.salesContract.number}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Contract Number</label>
                <p className="font-medium">{invoice.salesContract.number}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Contract Title</label>
                <p className="font-medium">{invoice.salesContract.title}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Contract Value</label>
                <p className="font-medium">
                  {formatCurrency(invoice.salesContract.total_amount || '0', invoice.salesContract.currency || invoice.currency)}
                </p>
              </div>
            </div>

            {/* Contract Items */}
            {invoice.salesContract.items && invoice.salesContract.items.length > 0 && (
              <div>
                <label className="text-sm font-medium text-muted-foreground mb-2 block">Contract Items</label>
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead className="text-right">Quantity</TableHead>
                        <TableHead className="text-right">Unit Price</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {invoice.salesContract.items.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell className="font-medium">
                            {item.product?.name || 'Unknown Product'}
                          </TableCell>
                          <TableCell className="text-muted-foreground">
                            {item.product?.sku || 'N/A'}
                          </TableCell>
                          <TableCell className="text-right">{item.qty}</TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(item.price, invoice.currency)}
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency((item.qty * parseFloat(item.price)).toString(), invoice.currency)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}

            <div className="pt-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/sales-contracts/view/${invoice.salesContract.id}`}>
                  <FileText className="mr-2 h-4 w-4" />
                  View Full Contract
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
