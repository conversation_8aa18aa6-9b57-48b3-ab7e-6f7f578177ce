import { notFound, redirect } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { arInvoices, customers, salesContracts } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { AppShell } from "@/components/app-shell"
import { ARInvoiceEditClient } from "./ar-invoice-edit-client"

interface ARInvoiceEditPageProps {
  params: Promise<{ id: string }>
}

export default async function ARInvoiceEditPage({ params }: ARInvoiceEditPageProps) {
  const { id } = await params
  const context = await getTenantContext()

  if (!context) {
    redirect('/api/auth/login')
  }

  // 🛡️ SECURE: Fetch the AR invoice with related data and multi-tenant isolation
  const invoice = await db.query.arInvoices.findFirst({
    where: and(
      eq(arInvoices.id, id),
      eq(arInvoices.company_id, context.companyId)
    ),
    with: {
      customer: true,
      salesContract: true
    }
  })

  if (!invoice) {
    notFound()
  }

  // 🛡️ SECURE: Fetch supporting data for the current company only
  const [allCustomers, allSalesContracts] = await Promise.all([
    db.query.customers.findMany({
      where: eq(customers.company_id, context.companyId),
      orderBy: (customers, { asc }) => [asc(customers.name)],
    }),
    db.query.salesContracts.findMany({
      where: eq(salesContracts.company_id, context.companyId),
      orderBy: (salesContracts, { desc }) => [desc(salesContracts.created_at)],
      with: {
        customer: true
      }
    })
  ])

  return (
    <AppShell>
      <ARInvoiceEditClient
        invoice={invoice}
        customers={allCustomers}
        salesContracts={allSalesContracts}
        companyId={context.companyId}
      />
    </AppShell>
  )
}
