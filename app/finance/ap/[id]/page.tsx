import { notFound, redirect } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { apInvoices } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { AppShell } from "@/components/app-shell"
import { APInvoiceViewClient } from "./ap-invoice-view-client"

interface APInvoiceViewPageProps {
  params: Promise<{ id: string }>
}

export default async function APInvoiceViewPage({ params }: APInvoiceViewPageProps) {
  const { id } = await params
  const context = await getTenantContext()

  if (!context) {
    redirect('/api/auth/login')
  }

  // 🛡️ SECURE: Fetch the AP invoice with related data and multi-tenant isolation
  const invoice = await db.query.apInvoices.findFirst({
    where: and(
      eq(apInvoices.id, id),
      eq(apInvoices.company_id, context.companyId)
    ),
    with: {
      supplier: true,
      purchaseContract: {
        with: {
          items: true // ✅ FIXED: Removed product relation since it can be products or raw materials
        }
      }
    }
  })

  if (!invoice) {
    notFound()
  }

  return (
    <AppShell>
      <APInvoiceViewClient
        invoice={invoice}
        companyId={context.companyId}
      />
    </AppShell>
  )
}
