import { notFound, redirect } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { apInvoices, suppliers, purchaseContracts } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { AppShell } from "@/components/app-shell"
import { APInvoiceEditClient } from "./ap-invoice-edit-client"

interface APInvoiceEditPageProps {
  params: Promise<{ id: string }>
}

export default async function APInvoiceEditPage({ params }: APInvoiceEditPageProps) {
  const { id } = await params
  const context = await getTenantContext()

  if (!context) {
    redirect('/api/auth/login')
  }

  // 🛡️ SECURE: Fetch the AP invoice with related data and multi-tenant isolation
  const invoice = await db.query.apInvoices.findFirst({
    where: and(
      eq(apInvoices.id, id),
      eq(apInvoices.company_id, context.companyId)
    ),
    with: {
      supplier: true,
      purchaseContract: true
    }
  })

  if (!invoice) {
    notFound()
  }

  // 🛡️ SECURE: Fetch supporting data for the current company only
  const [allSuppliers, allPurchaseContracts] = await Promise.all([
    db.query.suppliers.findMany({
      where: eq(suppliers.company_id, context.companyId),
      orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
    }),
    db.query.purchaseContracts.findMany({
      where: eq(purchaseContracts.company_id, context.companyId),
      orderBy: (purchaseContracts, { desc }) => [desc(purchaseContracts.created_at)],
      with: {
        supplier: true
      }
    })
  ])

  return (
    <AppShell>
      <APInvoiceEditClient
        invoice={invoice}
        suppliers={allSuppliers}
        purchaseContracts={allPurchaseContracts}
        companyId={context.companyId}
      />
    </AppShell>
  )
}
