"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, Save } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import Link from "next/link"

interface Supplier {
  id: string
  name: string
  company?: string
}

interface PurchaseContract {
  id: string
  number: string
  title?: string
  supplier: Supplier
}

interface APInvoiceCreateClientProps {
  suppliers: Supplier[]
  purchaseContracts: PurchaseContract[]
  companyId: string
}

export function APInvoiceCreateClient({
  suppliers,
  purchaseContracts,
  companyId
}: APInvoiceCreateClientProps) {
  const router = useRouter()
  const { toast } = useSafeToast()
  const [loading, setLoading] = useState(false)
  const [selectedSupplierId, setSelectedSupplierId] = useState<string>("")

  // Form state
  const [formData, setFormData] = useState({
    number: "",
    supplier_id: "",
    purchase_contract_id: "",
    amount: "",
    currency: "USD",
    date: new Date().toISOString().split('T')[0],
    due_date: "",
    payment_terms: "",
    status: "pending",
    notes: "",
  })

  // Filter contracts by selected supplier
  const filteredContracts = selectedSupplierId
    ? purchaseContracts.filter(contract => contract.supplier.id === selectedSupplierId)
    : []

  // ✅ SEARCHABLE OPTIONS: Convert suppliers to searchable options
  const supplierOptions: SearchableSelectOption[] = suppliers.map((supplier) => ({
    value: supplier.id,
    label: supplier.name,
    subtitle: supplier.company ? `Company: ${supplier.company}` : 'Individual supplier',
    description: '🏭 Supplier for invoice',
  }))

  // ✅ SEARCHABLE OPTIONS: Convert contracts to searchable options
  const contractOptions: SearchableSelectOption[] = [
    {
      value: '',
      label: 'No Contract',
      subtitle: 'Create invoice without linking to contract',
      description: '📝 Manual invoice entry',
    },
    ...filteredContracts.map((contract) => ({
      value: contract.id,
      label: contract.number,
      subtitle: contract.title || 'Untitled Contract',
      description: '📄 Link to purchase contract',
    }))
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('/api/finance/ap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          amount: parseFloat(formData.amount),
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create invoice')
      }

      const result = await response.json()

      toast({
        title: "Invoice Created",
        description: `AP Invoice ${result.number} has been created successfully`,
        variant: "default"
      })

      router.push(`/finance/ap/${result.id}`)
    } catch (error) {
      console.error('Create invoice error:', error)
      toast({
        title: "Creation Failed",
        description: error instanceof Error ? error.message : "Failed to create invoice",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSupplierChange = (supplierId: string) => {
    setSelectedSupplierId(supplierId)
    setFormData(prev => ({
      ...prev,
      supplier_id: supplierId,
      purchase_contract_id: "", // Reset contract selection
    }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/finance">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Finance
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create AP Invoice</h1>
          <p className="text-muted-foreground">Create a new accounts payable invoice</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Invoice Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Invoice Number */}
              <div className="space-y-2">
                <Label htmlFor="number">Invoice Number *</Label>
                <Input
                  id="number"
                  value={formData.number}
                  onChange={(e) => setFormData(prev => ({ ...prev, number: e.target.value }))}
                  placeholder="AP-2024-001"
                  required
                />
              </div>

              {/* Supplier */}
              <div className="space-y-2">
                <Label htmlFor="supplier">Supplier *</Label>
                <SearchableSelect
                  options={supplierOptions}
                  value={formData.supplier_id}
                  onValueChange={handleSupplierChange}
                  placeholder="Search suppliers..."
                  searchPlaceholder="Search by supplier name..."
                  emptyMessage="No suppliers found."
                  allowClear={false}
                />
              </div>

              {/* Purchase Contract (Optional) */}
              <div className="space-y-2">
                <Label htmlFor="contract">Purchase Contract (Optional)</Label>
                <SearchableSelect
                  options={contractOptions}
                  value={formData.purchase_contract_id}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, purchase_contract_id: value }))}
                  placeholder={selectedSupplierId ? "Search contracts..." : "Select supplier first"}
                  searchPlaceholder="Search by contract number..."
                  emptyMessage="No contracts found for this supplier."
                  disabled={!selectedSupplierId}
                  allowClear={true}
                />
              </div>

              {/* Amount */}
              <div className="space-y-2">
                <Label htmlFor="amount">Amount *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.amount}
                  onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                  placeholder="0.00"
                  required
                />
              </div>

              {/* Currency */}
              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select value={formData.currency} onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="CNY">CNY</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Invoice Date */}
              <div className="space-y-2">
                <Label htmlFor="date">Invoice Date *</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                  required
                />
              </div>

              {/* Due Date */}
              <div className="space-y-2">
                <Label htmlFor="due_date">Due Date</Label>
                <Input
                  id="due_date"
                  type="date"
                  value={formData.due_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, due_date: e.target.value }))}
                />
              </div>

              {/* Payment Terms */}
              <div className="space-y-2">
                <Label htmlFor="payment_terms">Payment Terms</Label>
                <Select value={formData.payment_terms} onValueChange={(value) => setFormData(prev => ({ ...prev, payment_terms: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment terms" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No Terms</SelectItem>
                    <SelectItem value="TT">TT (Telegraphic Transfer)</SelectItem>
                    <SelectItem value="DP">DP (Documents against Payment)</SelectItem>
                    <SelectItem value="LC">LC (Letter of Credit)</SelectItem>
                    <SelectItem value="deposit">Deposit</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="sent">Sent</SelectItem>
                    <SelectItem value="partial">Partial</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="overdue">Overdue</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Additional notes or comments..."
                rows={3}
              />
            </div>

            {/* Actions */}
            <div className="flex items-center gap-4">
              <Button type="submit" disabled={loading}>
                <Save className="mr-2 h-4 w-4" />
                {loading ? 'Creating...' : 'Create Invoice'}
              </Button>
              <Button type="button" variant="outline" asChild>
                <Link href="/finance">Cancel</Link>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div >
  )
}
