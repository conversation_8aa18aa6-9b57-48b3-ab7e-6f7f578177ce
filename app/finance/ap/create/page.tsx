import { redirect } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { suppliers, purchaseContracts } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { AppShell } from "@/components/app-shell"
import { APInvoiceCreateClient } from "./ap-invoice-create-client"

export default async function APInvoiceCreatePage() {
  const context = await getTenantContext()
  
  if (!context) {
    redirect('/api/auth/login')
  }

  // 🛡️ SECURE: Fetch supporting data for the current company only
  const [allSuppliers, allPurchaseContracts] = await Promise.all([
    db.query.suppliers.findMany({
      where: eq(suppliers.company_id, context.companyId),
      orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
    }),
    db.query.purchaseContracts.findMany({
      where: eq(purchaseContracts.company_id, context.companyId),
      orderBy: (purchaseContracts, { desc }) => [desc(purchaseContracts.created_at)],
      with: {
        supplier: true
      }
    })
  ])

  return (
    <AppShell>
      <APInvoiceCreateClient
        suppliers={allSuppliers}
        purchaseContracts={allPurchaseContracts}
        companyId={context.companyId}
      />
    </AppShell>
  )
}
