"use client"

import Link from "next/link"
import { AppShell } from "@/components/app-shell"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useEffect, useState } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { ManufacturingKPIDashboard } from "@/components/finance/manufacturing-kpi-dashboard"
import { EnhancedARCard } from "@/components/finance/enhanced-ar-card"
import { EnhancedAPCard } from "@/components/finance/enhanced-ap-card"
import {
  Coins,
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  AlertTriangle,
  Plus,
  Eye,
  Receipt,
  FileText,
  BarChart3,
  Ship,
  Activity
} from "lucide-react"

export default function FinancePage() {
  const { t } = useI18n()
  const [manufacturingKPIs, setManufacturingKPIs] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  // Load manufacturing financial KPIs
  useEffect(() => {
    async function loadKPIs() {
      try {
        const data = await safeJson("/api/finance/summary?type=kpis", {})
        setManufacturingKPIs(data.kpis)
      } catch (error) {
        console.error("Failed to load manufacturing KPIs:", error)
      } finally {
        setLoading(false)
      }
    }
    loadKPIs()
  }, [])

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Professional Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center">
              <Coins className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{t("finance.title")}</h1>
              <p className="text-muted-foreground">{t("finance.description")}</p>
            </div>
          </div>
        </div>

        {/* ✅ REMOVED: Redundant Executive Financial Dashboard card - KPIs are displayed in the main dashboard below */}

        {/* Manufacturing Financial KPI Dashboard */}
        <ManufacturingKPIDashboard
          kpis={manufacturingKPIs}
          loading={loading}
        />

        {/* AR/AP Management */}
        <div className="grid gap-6">
          <EnhancedARCard />
          <EnhancedAPCard />
        </div>
      </div>
    </AppShell>
  )
}

// ============================================================================
// FINANCIAL SUMMARY CARDS COMPONENT
// ============================================================================

function FinancialSummaryCards({ summary }: { summary: any }) {
  const { t } = useI18n()

  const cards = [
    {
      title: t("finance.summary.totalAR"),
      value: `$${summary.totalAR.toFixed(2)}`,
      icon: TrendingUp,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: t("finance.summary.outstandingAR"),
      value: `$${summary.outstandingAR.toFixed(2)}`,
      icon: DollarSign,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: t("finance.summary.totalAP"),
      value: `$${summary.totalAP.toFixed(2)}`,
      icon: TrendingDown,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
    {
      title: t("finance.summary.netCashFlow"),
      value: `$${summary.netCashFlow.toFixed(2)}`,
      icon: summary.netCashFlow >= 0 ? TrendingUp : TrendingDown,
      color: summary.netCashFlow >= 0 ? "text-green-600" : "text-red-600",
      bgColor: summary.netCashFlow >= 0 ? "bg-green-100" : "bg-red-100",
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card, index) => {
        const Icon = card.icon
        return (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {card.title}
                  </p>
                  <p className="text-2xl font-bold">{card.value}</p>
                </div>
                <div className={`w-12 h-12 rounded-lg ${card.bgColor} flex items-center justify-center`}>
                  <Icon className={`h-6 w-6 ${card.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}

// ============================================================================
// AR CARD COMPONENT (ENHANCED)
// ============================================================================

function ARCard() {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [customers, setCustomers] = useState<any[]>([])
  const [salesContracts, setSalesContracts] = useState<any[]>([])
  const [rows, setRows] = useState<any[]>([])
  const [form, setForm] = useState({
    number: "",
    customer_id: "",
    sales_contract_id: "",
    currency: "USD",
    amount: 1000,
    received: 0,
    payment_terms: "Net 30",
    notes: ""
  })

  async function load() {
    try {
      const [cs, scs, rs] = await Promise.all([
        safeJson("/api/customers", []),
        safeJson("/api/contracts/sales", []),
        safeJson("/api/finance/ar", [])
      ])

      // Ensure data is arrays before processing
      setCustomers(Array.isArray(cs) ? cs : [])
      setSalesContracts(
        Array.isArray(scs)
          ? scs.filter((c: any) => ["approved", "active", "in_production", "shipped"].includes(c.status))
          : []
      )
      setRows(Array.isArray(rs) ? rs : [])
    } catch (error) {
      console.error("Failed to load finance data:", error)
      // Set empty arrays as fallback
      setCustomers([])
      setSalesContracts([])
      setRows([])
    }
  }
  useEffect(() => {
    load()
  }, [])

  async function create() {
    try {
      if (form.sales_contract_id) {
        // Contract-based invoice generation
        const response = await fetch(`/api/contracts/sales/${form.sales_contract_id}/generate-invoice`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            invoice_number: form.number,
            payment_terms: form.payment_terms,
            notes: form.notes,
          })
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.message || "Failed to generate invoice")
        }

        toast({
          title: "Success",
          description: "Invoice generated from contract successfully",
        })
      } else {
        // Manual invoice creation
        if (!form.number || !form.customer_id || !form.amount) {
          toast({
            title: "Error",
            description: "Please fill in all required fields",
            variant: "destructive",
          })
          return
        }

        const response = await fetch("/api/finance/ar", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(form)
        })

        if (!response.ok) {
          throw new Error("Failed to create invoice")
        }

        toast({
          title: "Success",
          description: "Invoice created successfully",
        })
      }

      // Reset form
      setForm({
        number: "",
        customer_id: "",
        sales_contract_id: "",
        currency: "USD",
        amount: 1000,
        received: 0,
        payment_terms: "Net 30",
        notes: ""
      })
      await load()
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create invoice",
        variant: "destructive",
      })
    }
  }

  async function remove(id: string) {
    await fetch(`/api/finance/ar/${id}`, { method: "DELETE" })
    await load()
  }

  const aging = (date: string) => {
    const days = Math.floor((Date.now() - new Date(date).getTime()) / (1000 * 60 * 60 * 24))
    if (days <= 30) return "0–30"
    if (days <= 60) return "31–60"
    if (days <= 90) return "61–90"
    return "90+"
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t("finance.ar.title")}</CardTitle>
            <CardDescription>{t("finance.ar.desc")}</CardDescription>
          </div>
          <Button asChild>
            <Link href="/finance/ar/create">
              <Plus className="mr-2 h-4 w-4" />
              New AR Invoice
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-5">
          <div className="grid gap-1">
            <Label>{t("field.invoiceNo")}</Label>
            <Input value={form.number} onChange={(e) => setForm({ ...form, number: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.customer")}</Label>
            <Select value={form.customer_id} onValueChange={(v) => setForm({ ...form, customer_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {customers.map((c) => (
                  <SelectItem key={c.id} value={c.id}>
                    {c.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.amount")}</Label>
            <Input
              type="number"
              value={form.amount}
              onChange={(e) => setForm({ ...form, amount: Number(e.target.value) })}
            />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.received")}</Label>
            <Input
              type="number"
              value={form.received}
              onChange={(e) => setForm({ ...form, received: Number(e.target.value) })}
            />
          </div>
          <div className="flex items-end">
            <Button onClick={create}>{t("action.createAR")}</Button>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("field.invoiceNo")}</TableHead>
              <TableHead>{t("field.customer")}</TableHead>
              <TableHead>{t("field.amount")}</TableHead>
              <TableHead>{t("field.received")}</TableHead>
              <TableHead>Aging</TableHead>
              <TableHead>{t("table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((r) => (
              <TableRow key={r.id}>
                <TableCell>{r.number}</TableCell>
                <TableCell>{customers.find((c) => c.id === r.customer_id)?.name || r.customer_id}</TableCell>
                <TableCell>{Number(r.amount).toFixed(2)}</TableCell>
                <TableCell>{Number(r.received).toFixed(2)}</TableCell>
                <TableCell>{aging(r.date)}</TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center gap-1">
                    <Button size="sm" variant="ghost" asChild>
                      <Link href={`/finance/ar/${r.id}`}>
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={async () => {
                        try {
                          const response = await fetch(`/api/finance/ar/${r.id}/pdf`)
                          if (!response.ok) throw new Error('Failed to fetch invoice data')

                          const { invoiceData } = await response.json()
                          const { generateInvoicePDF } = await import('@/components/pdf-invoice-document')
                          await generateInvoicePDF(invoiceData, 'ar')
                        } catch (error) {
                          console.error('PDF generation error:', error)
                        }
                      }}
                    >
                      <FileText className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost" onClick={() => remove(r.id)}>
                      {t("action.delete")}
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {rows.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} className="text-muted-foreground">
                  {t("finance.ar.empty")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

function APCard() {
  const { t } = useI18n()
  const [suppliers, setSuppliers] = useState<any[]>([])
  const [rows, setRows] = useState<any[]>([])
  const [form, setForm] = useState({ number: "", supplier_id: "", currency: "USD", amount: 800, paid: 0 })

  async function load() {
    const [ss, rs] = await Promise.all([safeJson("/api/suppliers", []), safeJson("/api/finance/ap", [])])
    setSuppliers(ss)
    setRows(rs)
  }
  useEffect(() => {
    load()
  }, [])

  async function create() {
    if (!form.number || !form.supplier_id || !form.amount) return
    await fetch("/api/finance/ap", { method: "POST", body: JSON.stringify(form) })
    setForm({ number: "", supplier_id: "", currency: "USD", amount: 800, paid: 0 })
    await load()
  }

  async function remove(id: string) {
    await fetch(`/api/finance/ap/${id}`, { method: "DELETE" })
    await load()
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t("finance.ap.title")}</CardTitle>
            <CardDescription>{t("finance.ap.desc")}</CardDescription>
          </div>
          <Button asChild>
            <Link href="/finance/ap/create">
              <Plus className="mr-2 h-4 w-4" />
              New AP Invoice
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid gap-3 md:grid-cols-5">
          <div className="grid gap-1">
            <Label>{t("field.invoiceNo")}</Label>
            <Input value={form.number} onChange={(e) => setForm({ ...form, number: e.target.value })} />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.supplier")}</Label>
            <Select value={form.supplier_id} onValueChange={(v) => setForm({ ...form, supplier_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {suppliers.map((s) => (
                  <SelectItem key={s.id} value={s.id}>
                    {s.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.amount")}</Label>
            <Input
              type="number"
              value={form.amount}
              onChange={(e) => setForm({ ...form, amount: Number(e.target.value) })}
            />
          </div>
          <div className="grid gap-1">
            <Label>{t("field.paid")}</Label>
            <Input
              type="number"
              value={form.paid}
              onChange={(e) => setForm({ ...form, paid: Number(e.target.value) })}
            />
          </div>
          <div className="flex items-end">
            <Button onClick={create}>{t("action.createAP")}</Button>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("field.invoiceNo")}</TableHead>
              <TableHead>{t("field.supplier")}</TableHead>
              <TableHead>{t("field.amount")}</TableHead>
              <TableHead>{t("field.paid")}</TableHead>
              <TableHead>{t("table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((r) => (
              <TableRow key={r.id}>
                <TableCell>{r.number}</TableCell>
                <TableCell>{suppliers.find((s) => s.id === r.supplier_id)?.name || r.supplier_id}</TableCell>
                <TableCell>{Number(r.amount).toFixed(2)}</TableCell>
                <TableCell>{Number(r.paid).toFixed(2)}</TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center gap-1">
                    <Button size="sm" variant="ghost" asChild>
                      <Link href={`/finance/ap/${r.id}`}>
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={async () => {
                        try {
                          const response = await fetch(`/api/finance/ap/${r.id}/pdf`)
                          if (!response.ok) throw new Error('Failed to fetch invoice data')

                          const { invoiceData } = await response.json()
                          const { generateInvoicePDF } = await import('@/components/pdf-invoice-document')
                          await generateInvoicePDF(invoiceData, 'ap')
                        } catch (error) {
                          console.error('PDF generation error:', error)
                        }
                      }}
                    >
                      <FileText className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost" onClick={() => remove(r.id)}>
                      {t("action.delete")}
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {rows.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="text-muted-foreground">
                  {t("finance.ap.empty")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
