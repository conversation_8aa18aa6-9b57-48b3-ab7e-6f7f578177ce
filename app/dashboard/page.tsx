"use client"

import { AppShell } from "@/components/app-shell"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useI18n } from "@/components/i18n-provider"
import { useUser } from '@auth0/nextjs-auth0/client'
import {
  AlertCircle,
  BarChart3,
  CheckCircle,
  Clock,
  DollarSign,
  Factory,
  Package,
  ShieldCheck,
  Ship,
  TrendingUp,
  Users
} from "lucide-react"
import { useEffect, useState } from 'react'
import Link from 'next/link'

// ============================================================================
// TYPES AND INTERFACES - PROFESSIONAL ERP DASHBOARD
// ============================================================================

interface TextileExportKPIs {
  // Financial Performance
  totalRevenue: number
  totalExpenses: number
  profitMargin: number
  pendingReceivables: number

  // Production Operations
  activeWorkOrders: number
  completedWorkOrders: number
  productionEfficiency: number

  // Quality Control
  qualityPassRate: number
  pendingInspections: number
  qualityScore: number

  // Export Operations
  activeShipments: number
  exportDeclarations: number
  onTimeDelivery: number

  // Business Relationships
  activeCustomers: number
  activeSalesContracts: number
  totalProducts: number
}

interface CompanyInfo {
  id: string
  name: string
  legal_name?: string
  industry?: string
  business_type?: string
  city?: string
  country?: string
  email?: string
  phone?: string
  website?: string
  onboarding_completed?: string
}

export default function DashboardPage() {
  const { user, isLoading } = useUser()
  const { t, locale } = useI18n()
  const [kpis, setKpis] = useState<TextileExportKPIs>({
    totalRevenue: 0,
    totalExpenses: 0,
    profitMargin: 0,
    pendingReceivables: 0,
    activeWorkOrders: 0,
    completedWorkOrders: 0,
    productionEfficiency: 0,
    qualityPassRate: 0,
    pendingInspections: 0,
    qualityScore: 0,
    activeShipments: 0,
    exportDeclarations: 0,
    onTimeDelivery: 0,
    activeCustomers: 0,
    activeSalesContracts: 0,
    totalProducts: 0
  })
  const [kpisLoading, setKpisLoading] = useState(true)
  const [kpisError, setKpisError] = useState<string | null>(null)

  // ✅ ENHANCED: Company information state
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null)
  const [companyLoading, setCompanyLoading] = useState(true)

  // ✅ ENHANCED: Fetch company information
  useEffect(() => {
    if (!user) return

    const fetchCompanyInfo = async () => {
      try {
        setCompanyLoading(true)
        console.log('🔍 Dashboard: Fetching company info for user:', user.email)

        const response = await fetch('/api/companies')
        console.log('🔍 Dashboard: Company API response status:', response.status)

        if (response.ok) {
          const data = await response.json()
          console.log('🔍 Dashboard: Company API response data:', data)

          // ✅ FIX: Handle both response formats for compatibility
          const companyData = data.company || data.data?.company || null
          console.log('🔍 Dashboard: Extracted company data:', companyData)

          setCompanyInfo(companyData)
        } else {
          const errorData = await response.json()
          console.error('❌ Dashboard: Company API error:', errorData)
        }
      } catch (error) {
        console.error('❌ Dashboard: Error fetching company info:', error)
      } finally {
        setCompanyLoading(false)
      }
    }

    fetchCompanyInfo()
  }, [user])

  // ✅ PROFESSIONAL: Fetch textile export manufacturing KPIs
  useEffect(() => {
    if (!user) return

    const fetchKPIs = async () => {
      try {
        setKpisLoading(true)
        setKpisError(null)

        // ✅ ENTERPRISE: Parallel fetch of essential business metrics
        const [
          financialRes,
          operationalRes,
          qualityRes,
          businessRes
        ] = await Promise.allSettled([
          // Financial performance data
          fetch('/api/finance/ar').then(res => res.json()),

          // Production and operational data
          Promise.all([
            fetch('/api/production/work-orders').then(res => res.json()),
            fetch('/api/shipping').then(res => res.json()),
            fetch('/api/export/declarations').then(res => res.json())
          ]),

          // Quality control metrics
          fetch('/api/quality/inspections').then(res => res.json()),

          // Business relationship data
          Promise.all([
            fetch('/api/customers').then(res => res.json()),
            fetch('/api/contracts/sales').then(res => res.json()),
            fetch('/api/products').then(res => res.json())
          ])
        ])

        // ✅ PROFESSIONAL: Calculate textile export manufacturing KPIs
        const newKPIs: TextileExportKPIs = {
          totalRevenue: 0,
          totalExpenses: 0,
          profitMargin: 0,
          pendingReceivables: 0,
          activeWorkOrders: 0,
          completedWorkOrders: 0,
          productionEfficiency: 0,
          qualityPassRate: 0,
          pendingInspections: 0,
          qualityScore: 0,
          activeShipments: 0,
          exportDeclarations: 0,
          onTimeDelivery: 0,
          activeCustomers: 0,
          activeSalesContracts: 0,
          totalProducts: 0
        }

        // ✅ FINANCIAL PERFORMANCE METRICS
        if (financialRes.status === 'fulfilled') {
          const arInvoices = financialRes.value || []
          newKPIs.totalRevenue = Array.isArray(arInvoices)
            ? arInvoices.reduce((sum, inv) => sum + parseFloat(inv.amount || '0'), 0)
            : 0
          newKPIs.pendingReceivables = Array.isArray(arInvoices)
            ? arInvoices.filter(inv => inv.status === 'pending').length
            : 0
        }

        // ✅ OPERATIONAL METRICS
        if (operationalRes.status === 'fulfilled') {
          const [workOrders, shipments, declarations] = operationalRes.value

          // Work Orders
          if (Array.isArray(workOrders)) {
            newKPIs.activeWorkOrders = workOrders.filter(wo => wo.status === 'in_progress').length
            newKPIs.completedWorkOrders = workOrders.filter(wo => wo.status === 'completed').length
            newKPIs.productionEfficiency = workOrders.length > 0
              ? Math.round((newKPIs.completedWorkOrders / workOrders.length) * 100)
              : 0
          }

          // Shipments
          if (Array.isArray(shipments)) {
            newKPIs.activeShipments = shipments.filter(s => s.status === 'in_transit').length
            const deliveredOnTime = shipments.filter(s => s.status === 'delivered').length
            newKPIs.onTimeDelivery = shipments.length > 0
              ? Math.round((deliveredOnTime / shipments.length) * 100)
              : 0
          }

          // Export Declarations
          if (Array.isArray(declarations)) {
            newKPIs.exportDeclarations = declarations.length
          }
        }

        // ✅ QUALITY CONTROL METRICS
        if (qualityRes.status === 'fulfilled') {
          const inspections = qualityRes.value || []
          if (Array.isArray(inspections)) {
            const passedInspections = inspections.filter(i => i.status === 'passed').length
            newKPIs.qualityPassRate = inspections.length > 0
              ? Math.round((passedInspections / inspections.length) * 100)
              : 0
            newKPIs.pendingInspections = inspections.filter(i => i.status === 'pending').length
            newKPIs.qualityScore = newKPIs.qualityPassRate
          }
        }

        // ✅ BUSINESS RELATIONSHIP METRICS
        if (businessRes.status === 'fulfilled') {
          const [customers, salesContracts, products] = businessRes.value

          newKPIs.activeCustomers = Array.isArray(customers) ? customers.length : 0

          if (Array.isArray(salesContracts)) {
            const contractsData = salesContracts.data || salesContracts
            newKPIs.activeSalesContracts = Array.isArray(contractsData)
              ? contractsData.filter(c => c.status === 'active').length
              : 0
          }

          newKPIs.totalProducts = Array.isArray(products) ? products.length : 0
        }

        // ✅ CALCULATE PROFIT MARGIN
        if (newKPIs.totalRevenue > 0) {
          newKPIs.profitMargin = Math.round(((newKPIs.totalRevenue - newKPIs.totalExpenses) / newKPIs.totalRevenue) * 100)
        }

        setKpis(newKPIs)
      } catch (error) {
        console.error('❌ Error fetching textile export KPIs:', error)
        setKpisError('Failed to load manufacturing dashboard metrics')
      } finally {
        setKpisLoading(false)
      }
    }

    fetchKPIs()
  }, [user])

  // ✅ LOADING STATE
  if (isLoading || kpisLoading || companyLoading) {
    return (
      <AppShell>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading manufacturing dashboard...</p>
          </div>
        </div>
      </AppShell>
    )
  }

  // ✅ ERROR STATE
  if (kpisError) {
    return (
      <AppShell>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Dashboard Error</h2>
            <p className="text-muted-foreground mb-4">{kpisError}</p>
            <p className="text-sm text-muted-foreground">
              Please check your connection and try again.
            </p>
            <Button onClick={() => window.location.reload()} className="mt-4">
              Retry
            </Button>
          </div>
        </div>
      </AppShell>
    )
  }

  // ✅ PROFESSIONAL TEXTILE EXPORT MANUFACTURING DASHBOARD
  return (
    <AppShell>
      <div className="container mx-auto px-4 py-8">
        {/* ✅ PERSONALIZED WELCOME SECTION */}
        <div className="mb-8">
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                {/* User Welcome */}
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-sm text-muted-foreground">
                        {new Date().toLocaleDateString(
                          locale === 'zh' ? 'zh-CN' : 'en-US',
                          {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }
                        )}
                      </span>
                    </div>
                  </div>
                  <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-1">
                    {t("common.welcome_back")}, {user?.name || user?.email?.split('@')[0] || 'User'}!
                  </h1>
                  <p className="text-muted-foreground">
                    {t("common.ready_to_manage_textile_export_operations")}
                  </p>
                </div>

                {/* Company Information */}
                <div className="lg:text-right">
                  <div className="bg-white rounded-lg p-4 border border-blue-100 shadow-sm">
                    <div className="flex items-center gap-2 mb-2 lg:justify-end">
                      <Factory className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-gray-600">{t("common.company")}</span>
                    </div>

                    {/* ✅ ENHANCED: Better company name display with debugging */}
                    <h3 className="font-semibold text-gray-900 text-lg">
                      {companyLoading ? (
                        <div className="animate-pulse bg-gray-200 h-6 w-32 rounded"></div>
                      ) : companyInfo?.name ? (
                        companyInfo.name
                      ) : (
                        <span className="text-muted-foreground italic">
                          {user?.email ? `${user.email.split('@')[0]}'s Company` : 'Your Company'}
                        </span>
                      )}
                    </h3>

                    {/* Legal name display */}
                    {!companyLoading && companyInfo?.legal_name && companyInfo.legal_name !== companyInfo.name && (
                      <p className="text-sm text-muted-foreground">
                        {companyInfo.legal_name}
                      </p>
                    )}

                    {/* Company details */}
                    {!companyLoading && (
                      <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground lg:justify-end">
                        {companyInfo?.business_type && (
                          <span className="flex items-center gap-1">
                            <Badge variant="secondary" className="text-xs">
                              {companyInfo.business_type}
                            </Badge>
                          </span>
                        )}
                        {companyInfo?.city && companyInfo?.country && (
                          <span>
                            {companyInfo.city}, {companyInfo.country}
                          </span>
                        )}
                        {!companyInfo && (
                          <Link href="/company-profile" className="text-blue-600 hover:text-blue-800 text-xs">
                            Complete Profile →
                          </Link>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* ✅ FINANCIAL PERFORMANCE KPIs */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            {t("common.financial_performance")}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t("common.total_revenue")}</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  ${kpis.totalRevenue.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t("common.export_sales_revenue")}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t("common.profit_margin")}</CardTitle>
                <BarChart3 className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {kpis.profitMargin}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {t("common.manufacturing_margin")}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t("common.pending_receivables")}</CardTitle>
                <Clock className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {kpis.pendingReceivables}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t("common.outstanding_invoices")}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t("common.active_customers")}</CardTitle>
                <Users className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {kpis.activeCustomers}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t("common.business_relationships")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* ✅ PRODUCTION & OPERATIONS KPIs */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <Factory className="h-5 w-5" />
            {t("common.production_operations")}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t("common.active_work_orders")}</CardTitle>
                <Factory className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {kpis.activeWorkOrders}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t("common.in_production")}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t("common.production_efficiency")}</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {kpis.productionEfficiency}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {t("common.completion_rate")}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t("common.active_shipments")}</CardTitle>
                <Ship className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {kpis.activeShipments}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t("common.in_transit")}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t("common.ontime_delivery")}</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {kpis.onTimeDelivery}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {t("common.delivery_performance")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* ✅ QUALITY CONTROL & EXPORT KPIs */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Quality Control */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShieldCheck className="h-5 w-5" />
                {t("common.quality_control")}
              </CardTitle>
              <CardDescription>
                {t("common.manufacturing_quality_metrics")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{t("common.quality_pass_rate")}</span>
                <div className="flex items-center gap-2">
                  <div className="text-lg font-bold text-green-600">
                    {kpis.qualityPassRate}%
                  </div>
                  <Badge variant={kpis.qualityPassRate >= 95 ? "default" : "secondary"}>
                    {kpis.qualityPassRate >= 95 ? "Excellent" : "Good"}
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{t("common.pending_inspections")}</span>
                <div className="text-lg font-bold text-orange-600">
                  {kpis.pendingInspections}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{t("common.quality_score")}</span>
                <div className="text-lg font-bold text-blue-600">
                  {kpis.qualityScore}/100
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Export Operations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Ship className="h-5 w-5" />
                {t("common.export_operations")}
              </CardTitle>
              <CardDescription>
                {t("common.international_trade_metrics")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{t("common.export_declarations")}</span>
                <div className="text-lg font-bold text-blue-600">
                  {kpis.exportDeclarations}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{t("common.active_sales_contracts")}</span>
                <div className="text-lg font-bold text-green-600">
                  {kpis.activeSalesContracts}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{t("common.product_catalog")}</span>
                <div className="text-lg font-bold text-purple-600">
                  {kpis.totalProducts}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* ✅ QUICK ACCESS NAVIGATION */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {t("common.quick_access")}
            </CardTitle>
            <CardDescription>
              {t("common.navigate_to_key_manufacturing_modules")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button asChild variant="outline" className="h-16 flex-col">
                <Link href="/reports">
                  <BarChart3 className="h-6 w-6 mb-2" />
                  <span className="text-sm">{t("common.reports")}</span>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-16 flex-col">
                <Link href="/production/work-orders">
                  <Factory className="h-6 w-6 mb-2" />
                  <span className="text-sm">{t("common.production")}</span>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-16 flex-col">
                <Link href="/quality/inspections">
                  <ShieldCheck className="h-6 w-6 mb-2" />
                  <span className="text-sm">{t("common.quality")}</span>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-16 flex-col">
                <Link href="/shipping">
                  <Ship className="h-6 w-6 mb-2" />
                  <span className="text-sm">{t("common.shipping")}</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

    </AppShell>
  )
}
