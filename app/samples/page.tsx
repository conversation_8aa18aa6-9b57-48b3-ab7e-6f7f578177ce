"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Plus, Package, Clock, CheckCircle, XCircle, AlertTriangle, AlertCircle, RefreshCw } from "lucide-react"
import { AppShell } from "@/components/app-shell"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { SamplesTable } from "@/components/samples/samples-table"
import { SamplesFilters, type SamplesFilters as FiltersType } from "@/components/samples/samples-filters"
import Link from "next/link"

export default function SamplesPage() {
  const { t } = useI18n()
  const { success: toastSuccess, error: toastError } = useSafeToast()

  // ✅ STATE MANAGEMENT
  const [samples, setSamples] = useState<any[]>([])
  const [customers, setCustomers] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [suppliers, setSuppliers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
  })

  // ✅ PROFESSIONAL BIDIRECTIONAL FILTERS STATE
  const [filters, setFilters] = useState<FiltersType>({
    search: "",
    customer_id: "",
    product_id: "",
    supplier_id: "",
    approval_status: "",
    sample_type: "",
    priority: "",
    status: "",

    // ✅ BIDIRECTIONAL WORKFLOW FILTERS
    sample_direction: "",
    sample_purpose: "",
    testing_status: "",

    date_from: "",
    date_to: "",
    created_from: "",
    created_to: "",
    sortBy: "created_at",
    sortOrder: "desc",
  })

  // ✅ FETCH SAMPLES WITH ENHANCED API
  const fetchSamples = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      console.log("🔄 Loading samples data...")

      // Build query parameters
      const queryParams = new URLSearchParams()
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value)
      })

      // Add enhanced query options
      queryParams.append('include_count', 'true')
      queryParams.append('include_relationships', 'true')

      const url = `/api/samples?${queryParams.toString()}`
      console.log("📡 Fetching samples from:", url)

      const response = await fetch(url)
      console.log("📊 Samples API response:", response.status, response.statusText)

      if (!response.ok) {
        console.error("❌ Samples API failed:", response.status, response.statusText)
        throw new Error(`Failed to load samples data: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log("📦 Samples data loaded:", data)

      // Handle enhanced API response format
      if (data.data) {
        setSamples(data.data)
        // Update stats from pagination info
        if (data.pagination?.total !== undefined) {
          setStats(prev => ({ ...prev, total: data.pagination.total }))
        }
      } else {
        setSamples(Array.isArray(data) ? data : [])
      }

      // Calculate stats from samples
      const samplesArray = data.data || data
      if (Array.isArray(samplesArray)) {
        setStats({
          total: samplesArray.length,
          pending: samplesArray.filter(s => s.approval_status === 'pending').length,
          approved: samplesArray.filter(s => s.approval_status === 'approved').length,
          rejected: samplesArray.filter(s => s.approval_status === 'rejected').length,
        })
      }

    } catch (err) {
      console.error('Error fetching samples:', err)
      setError('Failed to load samples data. Please check your authentication.')
      setSamples([])
      toastError("Error Loading Samples", "Failed to load samples data. Please try again.")
    } finally {
      setLoading(false)
    }
  }, [filters])

  // ✅ FETCH RELATIONSHIP DATA
  const fetchRelationshipData = useCallback(async () => {
    try {
      const [customersRes, productsRes, suppliersRes] = await Promise.all([
        fetch('/api/customers'),
        fetch('/api/products'),
        fetch('/api/suppliers'),
      ])

      if (customersRes.ok) {
        const customersData = await customersRes.json()
        setCustomers(Array.isArray(customersData) ? customersData : [])
      }

      if (productsRes.ok) {
        const productsData = await productsRes.json()
        setProducts(Array.isArray(productsData) ? productsData : [])
      }

      if (suppliersRes.ok) {
        const suppliersData = await suppliersRes.json()
        setSuppliers(Array.isArray(suppliersData) ? suppliersData : [])
      }
    } catch (err) {
      console.error('Error fetching relationship data:', err)
    }
  }, [])

  // ✅ INITIAL DATA LOADING
  useEffect(() => {
    fetchSamples()
    fetchRelationshipData()
  }, [fetchSamples, fetchRelationshipData])

  // ✅ HANDLE FILTER CHANGES
  const handleFiltersChange = (newFilters: FiltersType) => {
    setFilters(newFilters)
  }

  // ✅ HANDLE REFRESH
  const handleRefresh = () => {
    fetchSamples()
  }

  // Show loading state
  if (loading && samples.length === 0) {
    return (
      <AppShell>
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{t("samples.title")}</h1>
              <p className="text-muted-foreground">{t("common.loading")}</p>
            </div>
          </div>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </AppShell>
    )
  }

  // Show error state
  if (error) {
    return (
      <AppShell>
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{t("samples.title")}</h1>
              <p className="text-muted-foreground">{t("samples.subtitle")}</p>
            </div>
          </div>
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Unable to Load Samples</h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              {t("common.retry")}
            </Button>
          </div>
        </div>
      </AppShell>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved": return "default"
      case "pending approval": return "secondary"
      case "in production": return "outline"
      case "rejected": return "destructive"
      default: return "secondary"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "high": return "destructive"
      case "medium": return "secondary"
      case "low": return "outline"
      default: return "secondary"
    }
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ PROFESSIONAL HEADER */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("samples.title")}</h1>
            <p className="text-muted-foreground">
              {t("samples.subtitle")}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleRefresh} disabled={loading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              {t("samples.refresh")}
            </Button>
            <Button asChild>
              <Link href="/samples/create">
                <Plus className="mr-2 h-4 w-4" />
                {t("samples.add")}
              </Link>
            </Button>
          </div>
        </div>

        {/* ✅ PROFESSIONAL BIDIRECTIONAL OVERVIEW CARDS */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("samples.cards.outbound.title")}</CardTitle>
              <Package className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {loading ? '...' : samples.filter(s => s.sample_direction === 'outbound').length}
              </div>
              <p className="text-xs text-muted-foreground">{t("samples.cards.outbound.description")}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("samples.cards.inbound.title")}</CardTitle>
              <Clock className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {loading ? '...' : samples.filter(s => s.sample_direction === 'inbound').length}
              </div>
              <p className="text-xs text-muted-foreground">{t("samples.cards.inbound.description")}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("samples.cards.internal.title")}</CardTitle>
              <CheckCircle className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {loading ? '...' : samples.filter(s => s.sample_direction === 'internal').length}
              </div>
              <p className="text-xs text-muted-foreground">{t("samples.cards.internal.description")}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("samples.cards.quality.title")}</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {loading ? '...' : samples.filter(s => s.approval_status === 'pending').length}
              </div>
              <p className="text-xs text-muted-foreground">{t("samples.cards.quality.description")}</p>
            </CardContent>
          </Card>
        </div>

        {/* ✅ ENHANCED FILTERS */}
        <SamplesFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          customers={customers}
          products={products}
          suppliers={suppliers}
          loading={loading}
        />

        {/* ✅ ENHANCED SAMPLES TABLE */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold">Samples</h2>
              <p className="text-sm text-muted-foreground">
                {loading ? t("samples.loading") : t("samples.found", { count: samples.length })}
              </p>
            </div>
          </div>

          <SamplesTable
            samples={samples}
            onRefresh={handleRefresh}
            onSampleDeleted={handleRefresh}
            loading={loading}
          />
        </div>
      </div>
    </AppShell>
  )
}