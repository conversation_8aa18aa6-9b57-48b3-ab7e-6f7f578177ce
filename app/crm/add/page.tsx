import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { AddCustomerForm } from "../add-customer-form"

export default async function AddCustomerPage() {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext()

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login')
  }

  return (
    <AppShell>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Add New Customer</h1>
          <p className="text-muted-foreground">
            Enter the details of the new customer below.
          </p>
        </div>

        <div className="max-w-2xl">
          <AddCustomerForm setOpen={() => {}} />
        </div>
      </div>
    </AppShell>
  )
}
