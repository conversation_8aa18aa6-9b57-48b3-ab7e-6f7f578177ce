"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "sonner"
import { useTabNavigation } from "@/components/tab-navigation"
import { CustomerFromApi } from "./page" // Assuming the type is exported from the page

const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  contact_name: z.string().optional(),
  contact_phone: z.string().optional(),
  contact_email: z.string().email({ message: "Invalid email address." }).optional().or(z.literal("")),
  address: z.string().optional(),
  tax_id: z.string().optional(),
  bank: z.string().optional(),
  incoterm: z.string().optional(),
  payment_term: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

export function EditCustomerForm({
  setOpen,
  customer,
}: {
  setOpen?: (open: boolean) => void
  customer: CustomerFromApi
}) {
  const router = useRouter()
  const { openCustomerViewTab, openCustomerListTab } = useTabNavigation()
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: customer.name || "",
      contact_name: customer.contact_name || "",
      contact_phone: customer.contact_phone || "",
      contact_email: customer.contact_email || "",
      address: customer.address || "",
      tax_id: (customer as any).tax_id || "",
      bank: (customer as any).bank || "",
      incoterm: customer.incoterm || "",
      payment_term: customer.payment_term || "",
    },
  })

  async function onSubmit(values: FormValues) {
    const response = await fetch(`/api/customers/${customer.id}`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(values),
    })

    if (response.ok) {
      const updatedCustomer = await response.json()
      toast.success("Customer updated successfully.")

      if (setOpen) {
        // Dialog context: close dialog and open view tab
        setOpen(false)
        openCustomerViewTab(updatedCustomer.id, updatedCustomer.name)
      } else {
        // Page context: navigate to view tab
        openCustomerViewTab(updatedCustomer.id, updatedCustomer.name)
      }
    } else {
      const errorData = await response.json().catch(() => ({}))
      toast.error("Failed to update customer.", {
        description: errorData.error || "An unexpected error occurred.",
      })
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Customer Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Acme Inc." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="contact_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Contact Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g. John Doe" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="contact_email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Contact Email</FormLabel>
              <FormControl>
                <Input placeholder="e.g. <EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="contact_phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Contact Phone</FormLabel>
              <FormControl>
                <Input placeholder="e.g. ******-123-4567" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address</FormLabel>
              <FormControl>
                <Input placeholder="e.g. 123 Main St, Anytown USA" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="incoterm"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Incoterm</FormLabel>
              <FormControl>
                <Input placeholder="e.g. FOB" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="payment_term"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Payment Term</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Net 30" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="ghost"
            onClick={() => {
              if (setOpen) {
                // Dialog context: close dialog
                setOpen(false)
              } else {
                // Page context: navigate back to customer view
                openCustomerViewTab(customer.id, customer.name)
              }
            }}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
