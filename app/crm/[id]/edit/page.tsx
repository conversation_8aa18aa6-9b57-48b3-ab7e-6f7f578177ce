import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { customers } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { EditCustomerForm } from "../../edit-customer-form"

export default async function EditCustomerPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params

  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext()

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login')
  }

  // 🛡️ SECURE: Only fetch customer for the current company
  const customer = await db.query.customers.findFirst({
    where: and(
      eq(customers.id, id),
      eq(customers.company_id, context.companyId)
    ),
  })

  if (!customer) {
    notFound()
  }

  return (
    <AppShell>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Customer</h1>
          <p className="text-muted-foreground">
            Update the details of {customer.name}.
          </p>
        </div>

        <div className="max-w-2xl">
          <EditCustomerForm
            customer={{
              id: customer.id,
              name: customer.name,
              contact_name: customer.contact_name,
              contact_phone: customer.contact_phone,
              contact_email: customer.contact_email,
              address: customer.address,
              incoterm: customer.incoterm,
              payment_term: customer.payment_term,
              status: customer.status,
              created_at: customer.created_at?.toISOString() || ""
            }}
          />
        </div>
      </div>
    </AppShell>
  )
}
