/**
 * Manufacturing ERP - Demand Forecasting List Page
 * 
 * Professional page for viewing and managing all demand forecasts.
 * Includes filtering, editing, and deletion capabilities.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1C MRP Implementation
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DemandForecastingService } from "@/lib/services/demand-forecasting"
import { ForecastProfitabilityService } from "@/lib/services/forecast-profitability"
import { ProfitMarginDisplay } from "@/components/planning/profit-margin-display"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { db } from "@/lib/db"
import { suppliers } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { Plus, TrendingUp, Edit, Trash2, Eye, Search, Filter, CheckCircle } from "lucide-react"
import Link from "next/link"

// ✅ PROFESSIONAL: Delete forecast action
async function deleteForecast(forecastId: string) {
  "use server"

  try {
    // Get tenant context for security
    const context = await getTenantContext()
    if (!context) {
      throw new Error("Unauthorized")
    }

    // Use service directly (more efficient than HTTP call)
    const forecastingService = new DemandForecastingService()
    await forecastingService.deleteDemandForecast(context.companyId, forecastId)

    // Redirect to refresh the page
    redirect("/planning/forecasting")
  } catch (error) {
    console.error("Error deleting forecast:", error)
    throw error
  }
}

// ✅ PROFESSIONAL: Approve forecast action
async function approveForecast(forecastId: string) {
  "use server"

  try {
    // Get tenant context for security
    const context = await getTenantContext()
    if (!context) {
      throw new Error("Unauthorized")
    }

    // 1. Update forecast status to approved
    const forecastingService = new DemandForecastingService()
    await forecastingService.updateDemandForecast(
      context.companyId,
      forecastId,
      { approvalStatus: "approved" }
    )

    // 2. Trigger procurement plan generation automatically
    try {
      const procurementService = new ProcurementPlanningService()
      await procurementService.generateProcurementPlansFromForecast(
        context.companyId,
        forecastId,
        context.userId,
        {
          containerOptimization: true,
          maxLeadTime: 60, // 60 days max lead time
        }
      )
    } catch (procurementError) {
      console.error("Error generating procurement plans:", procurementError)
      // Don't fail the approval if procurement generation fails
    }

    // Redirect to refresh the page
    redirect("/planning/forecasting")
  } catch (error) {
    console.error("Error approving forecast:", error)
    throw error
  }
}

export default async function ForecastingPage() {
  // ✅ PROFESSIONAL: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // ✅ PROFESSIONAL: Fetch demand forecasts, suppliers, and profitability data
  const forecastingService = new DemandForecastingService()
  const profitabilityService = new ForecastProfitabilityService()

  const [forecasts, supplierList] = await Promise.all([
    forecastingService.listDemandForecasts(context.companyId).catch(() => []),
    db.query.suppliers.findMany({
      where: eq(suppliers.company_id, context.companyId),
      columns: { id: true, name: true },
    }).catch(() => [])
  ])

  // Calculate profitability for all forecasts
  const forecastIds = forecasts.map(f => f.id)
  const profitabilityData = await profitabilityService.calculateMultipleForecastsProfitability(
    context.companyId,
    forecastIds
  )

  // Create a map for quick lookup
  const profitabilityMap = new Map(
    profitabilityData.map(p => [p.forecastId, p])
  )

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ PROFESSIONAL: Page header with actions */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Demand Forecasting</h1>
            <p className="text-muted-foreground">
              Manage demand forecasts and planning scenarios
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href="/planning">
                <TrendingUp className="mr-2 h-4 w-4" />
                Back to Planning
              </Link>
            </Button>
            <Button asChild>
              <Link href="/planning/forecasting/create">
                <Plus className="mr-2 h-4 w-4" />
                New Forecast
              </Link>
            </Button>
          </div>
        </div>

        {/* ✅ PROFESSIONAL: Filters and search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
            <CardDescription>
              Filter and search demand forecasts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search forecasts..." className="pl-9" />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Method</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All methods" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Methods</SelectItem>
                    <SelectItem value="pipeline">Pipeline Analysis</SelectItem>
                    <SelectItem value="manual">Manual Entry</SelectItem>
                    <SelectItem value="historical">Historical Data</SelectItem>
                    <SelectItem value="hybrid">Hybrid Method</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Confidence</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All levels" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* ✅ PROFESSIONAL: Forecasts table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Demand Forecasts ({forecasts.length})
            </CardTitle>
            <CardDescription>
              All demand forecasts for your company
            </CardDescription>
          </CardHeader>
          <CardContent>
            {forecasts.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Period</TableHead>
                      <TableHead>Demand</TableHead>
                      <TableHead>Profit Margin</TableHead>
                      <TableHead>Confidence</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead>Supplier Prefs</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {forecasts.map((forecast) => (
                      <TableRow key={forecast.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{forecast.productName}</div>
                            <div className="text-sm text-muted-foreground">{forecast.productSku}</div>
                          </div>
                        </TableCell>
                        <TableCell>{forecast.forecastPeriod}</TableCell>
                        <TableCell>
                          <div className="font-medium">{forecast.forecastedDemand.toLocaleString()} units</div>
                        </TableCell>
                        <TableCell>
                          {(() => {
                            const profitability = profitabilityMap.get(forecast.id)
                            if (profitability && profitability.sellingPrice > 0) {
                              return (
                                <ProfitMarginDisplay
                                  marginPercentage={profitability.marginPercentage}
                                  profitabilityStatus={profitability.profitabilityStatus}
                                  profit={profitability.totalProfit}
                                  revenue={profitability.totalRevenue}
                                  materialCost={profitability.totalMaterialCost}
                                  currency={profitability.currency}
                                  size="sm"
                                  showDetails={false}
                                />
                              )
                            }
                            return (
                              <span className="text-sm text-muted-foreground">
                                No BOM/Price
                              </span>
                            )
                          })()}
                        </TableCell>
                        <TableCell>
                          <Badge variant={
                            forecast.confidenceLevel === 'high' ? 'default' :
                              forecast.confidenceLevel === 'medium' ? 'secondary' : 'outline'
                          }>
                            {forecast.confidenceLevel}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {forecast.forecastMethod}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {(() => {
                            try {
                              const prefs = forecast.supplierPreferences ? JSON.parse(forecast.supplierPreferences) : null
                              if (!prefs || !prefs.preferredSupplierId) {
                                return <span className="text-muted-foreground text-sm">Auto</span>
                              }

                              // Find supplier name
                              const supplier = supplierList.find(s => s.id === prefs.preferredSupplierId)
                              const supplierName = supplier ? supplier.name : 'Unknown Supplier'

                              return (
                                <Badge variant="default" className="text-xs">
                                  {supplierName}
                                </Badge>
                              )
                            } catch {
                              return <span className="text-muted-foreground text-sm">Auto</span>
                            }
                          })()}
                        </TableCell>
                        <TableCell>
                          <Badge variant={
                            forecast.approvalStatus === 'approved' ? 'default' :
                              forecast.approvalStatus === 'pending' ? 'secondary' :
                                forecast.approvalStatus === 'rejected' ? 'destructive' : 'outline'
                          }>
                            {forecast.approvalStatus}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(forecast.createdAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button variant="ghost" size="sm" asChild>
                              <Link href={`/planning/forecasting/${forecast.id}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                            <Button variant="ghost" size="sm" asChild>
                              <Link href={`/planning/forecasting/${forecast.id}`}>
                                <Edit className="h-4 w-4" />
                              </Link>
                            </Button>
                            {forecast.approvalStatus === "draft" && (
                              <form action={approveForecast.bind(null, forecast.id)} className="inline">
                                <Button variant="ghost" size="sm" type="submit" className="text-green-600 hover:text-green-700">
                                  <CheckCircle className="h-4 w-4" />
                                </Button>
                              </form>
                            )}
                            <form action={deleteForecast.bind(null, forecast.id)} className="inline">
                              <Button variant="ghost" size="sm" type="submit">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </form>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <TrendingUp className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No Forecasts Created</h3>
                <p className="text-muted-foreground mb-6">
                  Start by creating your first demand forecast to enable material planning
                </p>
                <Button asChild>
                  <Link href="/planning/forecasting/create">
                    <Plus className="mr-2 h-4 w-4" />
                    Create First Forecast
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
