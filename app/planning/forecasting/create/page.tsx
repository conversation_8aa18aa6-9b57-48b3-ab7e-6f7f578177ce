/**
 * Manufacturing ERP - Create Demand Forecast Page
 * 
 * Professional page for creating new demand forecasts using the DemandForecastForm component.
 * Demonstrates the professional UI components and workflow integration.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP UI Components
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { DemandForecastForm } from "@/components/planning/demand-forecast-form"
import { db } from "@/lib/db"
import { products, suppliers } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"

// ✅ PROFESSIONAL: Server component with tenant context validation
export default async function CreateDemandForecastPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // Fetch products and suppliers for the form
  const [productList, supplierList] = await Promise.all([
    db.query.products.findMany({
      where: eq(products.company_id, context.companyId),
      columns: {
        id: true,
        name: true,
        sku: true,
      },
      orderBy: (products, { asc }) => [asc(products.name)],
    }),
    // ✅ PRIORITY 3: Fetch suppliers for optional preferences
    db.query.suppliers.findMany({
      where: eq(suppliers.company_id, context.companyId),
      columns: {
        id: true,
        name: true,
      },
      orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
    })
  ])

  // Handle form submission
  const handleSubmit = async (data: any) => {
    "use server"

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/planning/demand-forecast`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error("Failed to create demand forecast")
      }

      redirect("/planning")
    } catch (error) {
      console.error("Error creating demand forecast:", error)
      throw error
    }
  }

  return (
    <AppShell>
      <div className="container mx-auto py-6">
        <DemandForecastForm
          products={productList}
          suppliers={supplierList}
          mode="create"
        />
      </div>
    </AppShell>
  )
}
