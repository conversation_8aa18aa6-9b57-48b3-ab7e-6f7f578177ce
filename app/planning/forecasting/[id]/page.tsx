/**
 * Manufacturing ERP - Simplified Demand Forecast Detail Page
 * 
 * Clean, intuitive interface focused on essential information
 * 
 * <AUTHOR> ERP Developer
 * @version 2.0.0 - Simplified UX Design
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DemandForecastingService } from "@/lib/services/demand-forecasting"
import { ForecastProfitabilityService } from "@/lib/services/forecast-profitability"
import { ProfitMarginCard } from "@/components/planning/profit-margin-display"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { ArrowLeft, Edit, Package, TrendingUp, CheckCircle } from "lucide-react"
import Link from "next/link"

interface PageProps {
  params: Promise<{ id: string }>
}

export default async function SimplifiedForecastDetailPage({ params }: PageProps) {
  // ✅ PROFESSIONAL: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // ✅ ENHANCED: Fetch essential data including profitability
  const forecastingService = new DemandForecastingService()
  const procurementService = new ProcurementPlanningService()
  const profitabilityService = new ForecastProfitabilityService()

  let forecast
  let procurementPlans = []
  let profitability = null

  try {
    forecast = await forecastingService.getDemandForecastById(context.companyId, id)

    // Calculate profitability for this forecast
    profitability = await profitabilityService.calculateForecastProfitability(context.companyId, id)

    // Only fetch procurement plans if forecast is approved
    if (forecast.approvalStatus === 'approved') {
      procurementPlans = await procurementService.listProcurementPlans(context.companyId, {
        demandForecastId: id
      }).catch(() => [])
    }
  } catch (error) {
    console.error("Error fetching forecast:", error)
    redirect('/planning/forecasting')
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ SIMPLIFIED: Clean header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" asChild>
              <Link href="/planning/forecasting">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Forecasts
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{forecast.productName}</h1>
              <p className="text-muted-foreground">
                {forecast.forecastedDemand.toLocaleString()} units • {forecast.forecastPeriod}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={
              forecast.approvalStatus === 'approved' ? 'default' :
                forecast.approvalStatus === 'pending' ? 'secondary' :
                  forecast.approvalStatus === 'rejected' ? 'destructive' : 'outline'
            }>
              {forecast.approvalStatus}
            </Badge>
            <Button variant="outline" asChild>
              <Link href={`/planning/forecasting/${forecast.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </Button>
          </div>
        </div>

        {/* ✅ SIMPLIFIED: Essential information only */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Forecast Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Forecast Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Product</p>
                  <p className="font-medium">{forecast.productName}</p>
                  <p className="text-xs text-muted-foreground">{forecast.productSku}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Quantity</p>
                  <p className="font-medium">{forecast.forecastedDemand.toLocaleString()} units</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Period</p>
                  <p className="font-medium">{forecast.forecastPeriod}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Confidence</p>
                  <Badge variant="outline">{forecast.confidenceLevel}</Badge>
                </div>
              </div>

              {forecast.notes && (
                <div>
                  <p className="text-sm text-muted-foreground">Notes</p>
                  <p className="text-sm">{forecast.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* ✅ NEW: Profit Margin Analysis */}
          {profitability && profitability.sellingPrice > 0 ? (
            <ProfitMarginCard
              title="Forecast Profitability"
              marginPercentage={profitability.marginPercentage}
              profitabilityStatus={profitability.profitabilityStatus}
              profit={profitability.totalProfit}
              revenue={profitability.totalRevenue}
              materialCost={profitability.totalMaterialCost}
              currency={profitability.currency}
              icon={<TrendingUp className="h-4 w-4 text-muted-foreground" />}
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Profit Margin Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">
                    Profit margin analysis unavailable
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Product needs BOM and selling price configuration
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="grid grid-cols-1 gap-6">
          {/* Status & Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Status & Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">Current Status</p>
                <Badge variant={
                  forecast.approvalStatus === 'approved' ? 'default' :
                    forecast.approvalStatus === 'pending' ? 'secondary' :
                      forecast.approvalStatus === 'rejected' ? 'destructive' : 'outline'
                } className="mt-1">
                  {forecast.approvalStatus}
                </Badge>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Created</p>
                <p className="font-medium">{new Date(forecast.createdAt).toLocaleDateString()}</p>
              </div>

              {forecast.approvalStatus === 'approved' && (
                <div>
                  <p className="text-sm text-muted-foreground">Procurement Plans</p>
                  <p className="font-medium">{procurementPlans.length} plans generated</p>
                </div>
              )}

              <div className="pt-2">
                <Button asChild className="w-full">
                  <Link href="/planning">
                    <CheckCircle className="mr-2 h-4 w-4" />
                    View in Planning Dashboard
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* ✅ SIMPLIFIED: Procurement plans (only if approved) */}
        {forecast.approvalStatus === 'approved' && procurementPlans.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Generated Procurement Plans</CardTitle>
              <CardDescription>
                Materials needed to fulfill this forecast
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {procurementPlans.map((plan, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{plan.materialName}</p>
                      <p className="text-sm text-muted-foreground">
                        {plan.plannedQty.toLocaleString()} units • Target: {new Date(plan.targetDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${plan.estimatedCost.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">{plan.supplierName || 'No supplier'}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* ✅ SIMPLIFIED: Help text for draft forecasts */}
        {forecast.approvalStatus === 'draft' && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-blue-900">Forecast is in draft status</p>
                  <p className="text-sm text-blue-700">
                    Approve this forecast to generate procurement plans and start the production workflow.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppShell>
  )
}
