/**
 * Manufacturing ERP - Procurement Plan Detail Page
 * 
 * Professional detail view for individual procurement plans
 * Displays comprehensive plan information with action buttons
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  Package,
  Calendar,
  DollarSign,
  Clock,
  User,
  Building2,
  ArrowLeft,
  Edit,
  CheckCircle,
  ShoppingCart
} from "lucide-react"
import Link from "next/link"

interface ProcurementPlanDetailPageProps {
  params: Promise<{ id: string }>
}

export default async function ProcurementPlanDetailPage({
  params
}: ProcurementPlanDetailPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // Fetch procurement plan details
  const procurementService = new ProcurementPlanningService()
  let plan = null

  try {
    plan = await procurementService.getProcurementPlanById(context.companyId, id)
  } catch (error) {
    console.error("Error fetching procurement plan:", error)
    redirect("/planning/procurement?error=plan-not-found")
  }

  if (!plan) {
    redirect("/planning/procurement?error=plan-not-found")
  }

  // Server actions for plan actions
  const handleApprove = async () => {
    "use server"

    try {
      console.log(`🔄 Server Action: Approving individual plan ${id}`)

      const context = await getTenantContext()
      if (!context) {
        redirect('/api/auth/login')
        return
      }

      // ✅ PROFESSIONAL ERP: Use service directly for better error handling
      const procurementService = new ProcurementPlanningService()
      await procurementService.updateProcurementPlan(
        context.companyId,
        id,
        {
          status: "approved",
          approvedBy: context.userId
        }
      )

      console.log(`✅ Plan approved successfully for plan ${id}`)

      redirect(`/planning/procurement/${id}`)
    } catch (error) {
      console.error("❌ Error approving plan:", error)
      redirect(`/planning/procurement/${id}?error=approval-failed`)
    }
  }

  const handleMarkAsOrdered = async () => {
    "use server"

    try {
      console.log(`🔄 Server Action: Marking plan ${id} as ordered`)

      const context = await getTenantContext()
      if (!context) {
        redirect('/api/auth/login')
        return
      }

      const procurementService = new ProcurementPlanningService()
      await procurementService.updateProcurementPlan(
        context.companyId,
        id,
        {
          status: "ordered"
        }
      )

      console.log(`✅ Plan marked as ordered successfully for plan ${id}`)

      redirect(`/planning/procurement/${id}`)
    } catch (error) {
      console.error("❌ Error marking as ordered:", error)
      redirect(`/planning/procurement/${id}?error=order-failed`)
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "draft": return "secondary"
      case "pending": return "outline"
      case "approved": return "default"
      case "ordered": return "default"
      case "received": return "default"
      default: return "secondary"
    }
  }

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case "urgent": return "destructive"
      case "high": return "destructive"
      case "normal": return "default"
      case "low": return "secondary"
      default: return "secondary"
    }
  }

  return (
    <AppShell>
      <div className="container mx-auto py-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/planning/procurement">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Procurement Plans
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Procurement Plan Details</h1>
              <p className="text-muted-foreground">
                {plan.materialName} - {plan.materialSku}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href={`/planning/procurement/${id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Plan
              </Link>
            </Button>
            {plan.status === "draft" && (
              <form action={handleApprove}>
                <Button type="submit">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Plan
                </Button>
              </form>
            )}
            {plan.status === "approved" && (
              <form action={handleMarkAsOrdered}>
                <Button type="submit">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Mark as Ordered
                </Button>
              </form>
            )}
          </div>
        </div>

        {/* Plan Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <Badge variant={getStatusBadgeVariant(plan.status)}>
                {plan.status}
              </Badge>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Priority</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <Badge variant={getPriorityBadgeVariant(plan.priority)}>
                {plan.priority}
              </Badge>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Planned Quantity</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{plan.plannedQty.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">units</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Estimated Cost</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${plan.estimatedCost.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">total estimated</p>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Material Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="font-medium">{plan.materialName}</p>
                  <p className="text-sm text-muted-foreground">{plan.materialSku}</p>
                </div>
              </div>
              <Separator />
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">Target Date</p>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {new Date(plan.targetDate).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Lead Time</p>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {plan.estimatedLeadTime} days
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Supplier Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {plan.supplierName ? (
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{plan.supplierName}</p>
                    <p className="text-sm text-muted-foreground">Primary Supplier</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">No supplier assigned</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Notes */}
        {plan.notes && (
          <Card>
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">{plan.notes}</p>
            </CardContent>
          </Card>
        )}

        {/* Audit Information */}
        <Card>
          <CardHeader>
            <CardTitle>Audit Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium">Created</p>
                <p className="text-sm text-muted-foreground">
                  {new Date(plan.createdAt).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium">Last Updated</p>
                <p className="text-sm text-muted-foreground">
                  {new Date(plan.updatedAt).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
