/**
 * Manufacturing ERP - Procurement Plan Edit Page
 * 
 * Professional edit form for procurement plans
 * Allows updating plan details with validation
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { ProcurementPlanEditClient } from "@/components/planning/procurement-plan-edit-client"

interface ProcurementPlanEditPageProps {
  params: Promise<{ id: string }>
}

export default async function ProcurementPlanEditPage({
  params
}: ProcurementPlanEditPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // Fetch procurement plan details
  const procurementService = new ProcurementPlanningService()
  let plan = null

  try {
    plan = await procurementService.getProcurementPlanById(context.companyId, id)
  } catch (error) {
    console.error("Error fetching procurement plan:", error)
    redirect("/planning/procurement?error=plan-not-found")
  }

  if (!plan) {
    redirect("/planning/procurement?error=plan-not-found")
  }

  return (
    <AppShell>
      <ProcurementPlanEditClient plan={plan} />
    </AppShell>
  )
}
