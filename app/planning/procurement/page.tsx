/**
 * Manufacturing ERP - Procurement Planning Page
 * 
 * Professional page for managing procurement plans using the ProcurementPlanningTable component.
 * Demonstrates advanced table functionality with filtering, sorting, and bulk actions.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP UI Components
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { revalidatePath } from "next/cache"
import { ProcurementPlanningTable } from "@/components/planning/procurement-planning-table"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { getSession } from '@auth0/nextjs-auth0'
import { ProcurementStatusHandler } from "@/components/planning/procurement-status-handler"

// ✅ PROFESSIONAL: Server component with tenant context validation
export default async function ProcurementPlanningPage({
  searchParams
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // Handle URL parameters for success/error messages
  const params = await searchParams
  const successMessage = params.success
  const errorMessage = params.error

  // Fetch procurement plans
  const procurementService = new ProcurementPlanningService()
  let procurementPlans = []

  try {
    procurementPlans = await procurementService.listProcurementPlans(context.companyId)
  } catch (error) {
    console.error("Error fetching procurement plans:", error)
    // Continue with empty array - component will handle empty state
  }

  // Transform data for the table component
  const tableData = procurementPlans.map(plan => ({
    id: plan.id,
    rawMaterialId: plan.rawMaterialId,
    materialName: plan.materialName || 'Unknown Material',
    materialSku: plan.materialSku,
    demandForecastId: plan.demandForecastId,
    plannedQty: plan.plannedQty,
    targetDate: plan.targetDate,
    supplierId: plan.supplierId,
    supplierName: plan.supplierName,
    estimatedCost: plan.estimatedCost,
    estimatedLeadTime: plan.estimatedLeadTime || '30',
    priority: plan.priority as "low" | "normal" | "high" | "urgent",
    status: plan.status as "draft" | "pending" | "approved" | "ordered" | "received",
    notes: plan.notes,
    createdAt: plan.createdAt,
    updatedAt: plan.updatedAt,
  }))

  // Handle table actions
  const handleRefresh = async () => {
    "use server"
    redirect("/planning/procurement")
  }

  const handleStatusChange = async (id: string, status: string) => {
    "use server"

    try {
      console.log(`🔄 Server Action: Status change for plan ${id} to ${status}`)
      console.log(`📋 Request details:`, {
        planId: id,
        newStatus: status,
        timestamp: new Date().toISOString()
      })

      const context = await getTenantContext()
      if (!context) {
        console.error("❌ Authentication failed - no tenant context")
        throw new Error("Authentication required")
      }

      console.log(`✅ Tenant context established: Company ${context.companyId}`)

      // ✅ PROFESSIONAL ERP: Use service directly for better error handling
      const procurementService = new ProcurementPlanningService()

      // First verify the plan exists and belongs to this company
      const existingPlan = await procurementService.getProcurementPlanById(context.companyId, id)
      if (!existingPlan) {
        console.error(`❌ Procurement plan not found: ${id}`)
        throw new Error(`Procurement plan ${id} not found`)
      }

      console.log(`📋 Existing plan found:`, {
        id: existingPlan.id,
        currentStatus: existingPlan.status,
        materialName: existingPlan.materialName
      })

      await procurementService.updateProcurementPlan(
        context.companyId,
        id,
        {
          status: status as "draft" | "pending" | "approved" | "ordered" | "received",
          approvedBy: status === "approved" ? context.userId : undefined
        }
      )

      console.log(`✅ Status updated successfully for plan ${id} from ${existingPlan.status} to ${status}`)

      // ✅ PROFESSIONAL ERP: Revalidate pages to refresh data
      revalidatePath("/planning")
      revalidatePath("/planning/procurement")

      // Redirect to refresh the page with success indicator
      redirect("/planning/procurement?success=status-updated")
    } catch (error) {
      console.error("❌ Error updating status:", error)
      console.error("❌ Error details:", {
        planId: id,
        requestedStatus: status,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString()
      })
      // Redirect with error parameter
      redirect("/planning/procurement?error=status-update-failed")
    }
  }

  const handleBulkAction = async (ids: string[], action: string) => {
    "use server"

    try {
      const context = await getTenantContext()
      if (!context) {
        throw new Error("Authentication required")
      }

      const procurementService = new ProcurementPlanningService()

      // Handle different bulk actions
      switch (action) {
        case "approve":
          await procurementService.bulkApproveProcurementPlans(
            context.companyId,
            ids,
            context.userId,
            "Bulk approved from procurement table"
          )
          break
        case "export":
          // Handle export functionality
          console.log("Exporting items:", ids)
          break
        default:
          throw new Error(`Unsupported bulk action: ${action}`)
      }

      // Redirect to refresh the page
      redirect("/planning/procurement")
    } catch (error) {
      console.error("Error performing bulk action:", error)
      // Redirect with error parameter
      redirect("/planning/procurement?error=bulk-action-failed")
    }
  }

  const handleView = async (id: string) => {
    "use server"
    redirect(`/planning/procurement/${id}`)
  }

  const handleEdit = async (id: string) => {
    "use server"
    redirect(`/planning/procurement/${id}/edit`)
  }

  const handleDelete = async (id: string) => {
    "use server"

    try {
      const context = await getTenantContext()
      if (!context) {
        throw new Error("Authentication required")
      }

      // TODO: Implement delete functionality in service
      console.log("Delete functionality not yet implemented for plan:", id)

      // For now, redirect with not implemented message
      redirect("/planning/procurement?error=delete-not-implemented")
    } catch (error) {
      console.error("Error deleting procurement plan:", error)
      redirect("/planning/procurement?error=delete-failed")
    }
  }

  return (
    <AppShell>
      <div className="container mx-auto py-6">
        {/* Status Handler for Toast Notifications */}
        <ProcurementStatusHandler />

        <ProcurementPlanningTable
          data={tableData}
          onRefresh={handleRefresh}
          onStatusChange={handleStatusChange}
          onBulkAction={handleBulkAction}
          onView={handleView}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      </div>
    </AppShell>
  )
}
