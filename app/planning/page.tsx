/**
 * Manufacturing ERP - MRP Planning Dashboard
 * 
 * Professional MRP planning interface with demand forecasting and material requirements
 * Implements enterprise-grade UI/UX with bilingual support and responsive design
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { RefreshPrioritiesButton } from "@/components/planning/refresh-priorities-button"
import { PriorityBadge } from "@/components/planning/priority-badge"
import { ProfitabilityOverviewCard } from "@/components/planning/profit-margin-display"
import { ForecastProfitabilityService } from "@/lib/services/forecast-profitability"
import {
  Plus,
  TrendingUp,
  Package,
  AlertTriangle,
  Calendar,
  BarChart3,
  Truck,
  Clock,
  DollarSign,
  Target,
  Activity,
  ShoppingCart,
  Info,
  CheckCircle,
  XCircle,
  Zap
} from "lucide-react"
import Link from "next/link"
import { revalidatePath } from "next/cache"
import { DemandForecastingService } from "@/lib/services/demand-forecasting"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { SupplierLeadTimeService } from "@/lib/services/supplier-lead-time"
import { MRPClientContent } from "@/components/planning/mrp-client-content"

// ✅ PROFESSIONAL: Server component with tenant context validation
export default async function PlanningPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // ✅ PROFESSIONAL: Fetch actual MRP data from services
  const forecastingService = new DemandForecastingService()
  const procurementService = new ProcurementPlanningService()
  const leadTimeService = new SupplierLeadTimeService()
  const profitabilityService = new ForecastProfitabilityService()

  // Get MRP dashboard data
  const [
    demandForecasts,
    procurementPlans,
    supplierLeadTimes,
    purchaseRecommendations,
    profitabilityOverview
  ] = await Promise.allSettled([
    forecastingService.listDemandForecasts(context.companyId).catch(() => []),
    procurementService.listProcurementPlans(context.companyId).catch(() => []),
    leadTimeService.listSupplierLeadTimes(context.companyId).catch(() => []),
    procurementService.generatePurchaseRecommendations(context.companyId).catch(() => []),
    profitabilityService.getProfitabilityOverview(context.companyId).catch(() => ({
      totalForecasts: 0,
      totalRevenue: 0,
      totalMaterialCost: 0,
      totalProfit: 0,
      averageMargin: 0,
      profitabilityDistribution: { excellent: 0, good: 0, fair: 0, poor: 0 },
      topProfitableForecasts: [],
      lowProfitableForecasts: [],
    }))
  ])

  // Extract successful results
  const forecasts = demandForecasts.status === 'fulfilled' ? demandForecasts.value : []
  const plans = procurementPlans.status === 'fulfilled' ? procurementPlans.value : []
  const allLeadTimes = supplierLeadTimes.status === 'fulfilled' ? supplierLeadTimes.value : []
  const recommendations = purchaseRecommendations.status === 'fulfilled' ? purchaseRecommendations.value : []
  const profitabilityData = profitabilityOverview.status === 'fulfilled' ? profitabilityOverview.value : {
    totalForecasts: 0,
    totalRevenue: 0,
    totalMaterialCost: 0,
    totalProfit: 0,
    averageMargin: 0,
    profitabilityDistribution: { excellent: 0, good: 0, fair: 0, poor: 0 },
    topProfitableForecasts: [],
    lowProfitableForecasts: [],
  }



  // ✅ PRIORITY 1: Filter suppliers to show only those relevant to active forecasts
  const getRelevantSuppliers = () => {
    // Get materials from active forecasts via procurement plans
    const activeMaterialIds = new Set(plans.map(p => p.rawMaterialId))

    if (activeMaterialIds.size === 0) {
      // No active materials, show all suppliers for setup purposes
      return allLeadTimes
    }

    // Filter to suppliers that handle materials needed by active forecasts
    const relevantSuppliers = allLeadTimes.filter(lt =>
      // Include if supplier handles a needed material
      activeMaterialIds.has(lt.rawMaterialId) ||
      // Include generic suppliers (no specific material)
      !lt.rawMaterialId
    )

    return relevantSuppliers
  }

  const leadTimes = getRelevantSuppliers()

  // ✅ PROFESSIONAL: Calculate accurate supplier metrics from procurement plans
  const getSupplierMetrics = () => {
    // Get unique suppliers from active procurement plans
    const activeSupplierIds = new Set(
      plans
        .filter(p => p.supplierId) // Only plans with assigned suppliers
        .map(p => p.supplierId)
    )

    // Get supplier performance from lead times
    const supplierPerformance = new Map()
    leadTimes.forEach(lt => {
      if (lt.supplierId && activeSupplierIds.has(lt.supplierId)) {
        if (!supplierPerformance.has(lt.supplierId)) {
          supplierPerformance.set(lt.supplierId, {
            supplierId: lt.supplierId,
            supplierName: lt.supplierName,
            reliability: lt.reliability,
            leadTimeDays: parseInt(lt.leadTimeDays || '30'),
            materialsSupplied: 0
          })
        }
        supplierPerformance.get(lt.supplierId).materialsSupplied++
      }
    })

    return {
      totalSuppliers: activeSupplierIds.size,
      excellentSuppliers: Array.from(supplierPerformance.values())
        .filter(s => s.reliability === 'excellent').length,
      averageLeadTime: supplierPerformance.size > 0
        ? Array.from(supplierPerformance.values())
          .reduce((sum, s) => sum + s.leadTimeDays, 0) / supplierPerformance.size
        : 30
    }
  }

  const supplierMetrics = getSupplierMetrics()

  // ✅ ENHANCED KPIs: Clear, actionable business insights
  const kpis = {
    // Forecast Status
    activeForecasts: forecasts.length,
    approvedForecasts: forecasts.filter(f => f.approvalStatus === 'approved').length,

    // Procurement Status - Clear actionable categories
    totalProcurementPlans: plans.length,
    pendingApproval: plans.filter(p => p.status === 'draft' || p.status === 'pending').length,
    approvedPlans: plans.filter(p => p.status === 'approved').length,
    orderedPlans: plans.filter(p => p.status === 'ordered').length,
    receivedPlans: plans.filter(p => p.status === 'received').length,

    // Time-based Urgency (separate from approval status)
    urgentByTime: plans.filter(p => p.priority === 'urgent').length,
    highPriorityByTime: plans.filter(p => p.priority === 'high').length,

    // Critical Actions - Plans that need immediate attention
    criticalActions: plans.filter(p =>
      (p.status === 'draft' || p.status === 'pending') &&
      (p.priority === 'urgent' || p.priority === 'high')
    ).length,

    // Ready to Order - Approved plans that can be ordered
    readyToOrder: plans.filter(p => p.status === 'approved').length,

    // Overdue Plans - Plans past target date that aren't received
    overduePlans: plans.filter(p => {
      const targetDate = new Date(p.targetDate)
      const today = new Date()
      return targetDate < today && p.status !== 'received'
    }).length,

    // Financial
    totalEstimatedCost: plans.reduce((sum, p) => sum + parseFloat(p.estimatedCost || '0'), 0),
    pendingCost: plans.filter(p => p.status === 'draft' || p.status === 'pending')
      .reduce((sum, p) => sum + parseFloat(p.estimatedCost || '0'), 0),

    // Supplier Metrics
    totalSuppliers: supplierMetrics.totalSuppliers,
    excellentSuppliers: supplierMetrics.excellentSuppliers,
    averageLeadTime: supplierMetrics.averageLeadTime,
  }

  return (
    <AppShell>
      <MRPClientContent
        kpis={kpis}
        forecasts={forecasts}
        plans={plans}
        profitabilityData={profitabilityData}
      />



      {/* ✅ PROFESSIONAL: Enhanced KPI Cards with Real Data */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Forecasts</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.activeForecasts}</div>
            <p className="text-xs text-muted-foreground">
              {kpis.approvedForecasts} approved
            </p>
            {kpis.activeForecasts > 0 && (
              <Progress
                value={(kpis.approvedForecasts / kpis.activeForecasts) * 100}
                className="mt-2 h-1"
              />
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Procurement Status</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.totalProcurementPlans}</div>
            <p className="text-xs text-muted-foreground">
              {kpis.pendingApproval} pending approval • {kpis.approvedPlans} approved • {kpis.orderedPlans} ordered
            </p>
            {kpis.pendingApproval > 0 ? (
              <Badge variant="secondary" className="mt-1 text-xs bg-orange-100 text-orange-800 hover:bg-orange-200">
                {kpis.pendingApproval} Need Approval
              </Badge>
            ) : kpis.readyToOrder > 0 ? (
              <Badge variant="default" className="mt-1 text-xs bg-blue-600 text-white hover:bg-blue-700">
                {kpis.readyToOrder} Ready to Order
              </Badge>
            ) : (
              <Badge variant="outline" className="mt-1 text-xs bg-green-50 text-green-700 border-green-200">
                All Up to Date
              </Badge>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Procurement Plans</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.totalProcurementPlans}</div>
            <p className="text-xs text-muted-foreground">
              ${kpis.totalEstimatedCost.toLocaleString()} estimated
            </p>
            {kpis.pendingCost > 0 ? (
              <Badge variant="secondary" className="mt-1 text-xs bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
                ${kpis.pendingCost.toLocaleString()} Pending
              </Badge>
            ) : (
              <Badge variant="outline" className="mt-1 text-xs bg-green-50 text-green-700 border-green-200">
                All Approved
              </Badge>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Action Required</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.criticalActions}</div>
            <p className="text-xs text-muted-foreground">
              Urgent/high priority plans needing approval
            </p>
            {kpis.criticalActions > 0 ? (
              <Badge variant="destructive" className="mt-1 text-xs bg-red-600 text-white hover:bg-red-700">
                Immediate Action
              </Badge>
            ) : kpis.overduePlans > 0 ? (
              <Badge variant="destructive" className="mt-1 text-xs bg-orange-600 text-white hover:bg-orange-700">
                {kpis.overduePlans} Overdue
              </Badge>
            ) : (
              <Badge variant="outline" className="mt-1 text-xs bg-green-50 text-green-700 border-green-200">
                No Action Needed
              </Badge>
            )}
          </CardContent>
        </Card>
      </div>

      {/* ✅ PROFESSIONAL: Secondary KPI Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Supplier Network</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.totalSuppliers}</div>
            <p className="text-xs text-muted-foreground">
              {kpis.excellentSuppliers} excellent rated
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Lead Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(kpis.averageLeadTime)}</div>
            <p className="text-xs text-muted-foreground">
              days average delivery
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ready to Order</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.readyToOrder}</div>
            <p className="text-xs text-muted-foreground">
              approved plans ready for ordering
            </p>
            {kpis.readyToOrder > 0 ? (
              <Badge variant="default" className="mt-1 text-xs bg-blue-600 text-white hover:bg-blue-700">
                Action Available
              </Badge>
            ) : (
              <Badge variant="outline" className="mt-1 text-xs">
                None Ready
              </Badge>
            )}
          </CardContent>
        </Card>

        {/* ✅ NEW: Forecast Profitability Overview */}
        <ProfitabilityOverviewCard
          totalForecasts={profitabilityData.totalForecasts}
          averageMargin={profitabilityData.averageMargin}
          totalProfit={profitabilityData.totalProfit}
          totalRevenue={profitabilityData.totalRevenue}
          profitabilityDistribution={profitabilityData.profitabilityDistribution}
          currency="USD"
        />
      </div>

      {/* ✅ PROFESSIONAL: Streamlined MRP Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="forecasting">Forecasting</TabsTrigger>
          <TabsTrigger value="procurement">Procurement</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Demand Forecasts - Enhanced View */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Demand Forecasts & Production Planning
                </CardTitle>
                <CardDescription>
                  Active forecasts driving material requirements • {kpis.approvedForecasts} approved, {kpis.activeForecasts - kpis.approvedForecasts} pending
                </CardDescription>
              </CardHeader>
              <CardContent>
                {forecasts.length > 0 ? (
                  <div className="space-y-3">
                    {forecasts.slice(0, 5).map((forecast) => (
                      <div key={forecast.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{forecast.productName}</p>
                            <Badge variant={
                              forecast.approvalStatus === 'approved' ? 'default' :
                                forecast.approvalStatus === 'pending' ? 'secondary' : 'outline'
                            } className="text-xs">
                              {forecast.approvalStatus}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {forecast.forecastedDemand} units • {forecast.forecastPeriod}
                          </p>
                          {(() => {
                            const profitability = profitabilityData.topProfitableForecasts.find(p => p.forecastId === forecast.id) ||
                              profitabilityData.lowProfitableForecasts.find(p => p.forecastId === forecast.id)

                            if (profitability && profitability.sellingPrice > 0) {
                              return (
                                <div className="flex items-center gap-2 mt-1">
                                  <span className={`text-xs font-medium ${profitability.profitabilityStatus === 'excellent' ? 'text-green-600' :
                                    profitability.profitabilityStatus === 'good' ? 'text-blue-600' :
                                      profitability.profitabilityStatus === 'fair' ? 'text-yellow-600' : 'text-red-600'
                                    }`}>
                                    {profitability.marginPercentage.toFixed(1)}% margin
                                  </span>
                                  <Badge variant="outline" className="text-xs px-1 py-0">
                                    {profitability.profitabilityStatus}
                                  </Badge>
                                </div>
                              )
                            }
                            return null
                          })()}
                          {forecast.approvalStatus === 'approved' && (
                            <p className="text-xs text-green-600 font-medium">
                              ✓ Generating procurement plans
                            </p>
                          )}
                        </div>
                        <div className="text-right">
                          {forecast.approvalStatus === 'approved' && (
                            <div className="text-xs text-muted-foreground">
                              {plans.filter(p => p.demandForecastId === forecast.id).length} materials
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1" asChild>
                        <Link href="/planning/forecasting">View All Forecasts</Link>
                      </Button>
                      <Button size="sm" className="flex-1" asChild>
                        <Link href="/planning/forecasting/create">New Forecast</Link>
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Forecasts Yet</h3>
                    <p className="text-muted-foreground mb-4">
                      Create your first demand forecast to start planning
                    </p>
                    <Button asChild>
                      <Link href="/planning/forecasting/create">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Forecast
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Procurement Actions - Integrated View */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Material Procurement Status
                </CardTitle>
                <CardDescription>
                  Materials needed for approved forecasts • {plans.filter(p => p.priority === 'urgent' || p.priority === 'high').length} require immediate action
                </CardDescription>
              </CardHeader>
              <CardContent>
                {plans.length > 0 ? (
                  <div className="space-y-3">
                    {plans.slice(0, 5).map((plan, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{plan.materialName}</p>
                            <PriorityBadge priority={plan.priority as any} size="sm" />
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {parseFloat(plan.plannedQty).toLocaleString()} units • Target: {new Date(plan.targetDate).toLocaleDateString()}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Estimated Cost: ${parseFloat(plan.estimatedCost || '0').toLocaleString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant={plan.status === 'draft' ? 'outline' : 'default'} className="text-xs">
                            {plan.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                    {plans.length > 5 && (
                      <div className="text-center py-2">
                        <Badge variant="secondary" className="text-xs">
                          +{plans.length - 5} more procurement plans
                        </Badge>
                      </div>
                    )}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="w-full" asChild>
                        <Link href="/planning/procurement">View All Plans</Link>
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Active Plans</h3>
                    <p className="text-muted-foreground">
                      Create demand forecasts to generate procurement plans
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Supplier Summary - Integrated into Overview */}
          {kpis.totalSuppliers > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Supplier Network Summary
                </CardTitle>
                <CardDescription>
                  Key suppliers supporting your active procurement plans
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{kpis.totalSuppliers}</div>
                    <p className="text-sm text-muted-foreground">Active Suppliers</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{Math.round(kpis.averageLeadTime)}</div>
                    <p className="text-sm text-muted-foreground">Avg Lead Time (days)</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">${kpis.totalEstimatedCost.toLocaleString()}</div>
                    <p className="text-sm text-muted-foreground">Total Procurement Value</p>
                  </div>
                </div>

                {plans.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Active Suppliers:</h4>
                    <div className="flex flex-wrap gap-2">
                      {Array.from(new Set(plans.map(p => p.supplierName).filter(Boolean))).map((supplierName, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {supplierName}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Forecasting Tab */}
        <TabsContent value="forecasting" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Demand Forecasting Management
              </CardTitle>
              <CardDescription>
                Create and manage demand forecasts based on sales pipeline analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">Pipeline-Based Forecasting</h3>
                    <p className="text-muted-foreground">Generate forecasts from sales contract pipeline</p>
                  </div>
                  <Button asChild>
                    <Link href="/planning/forecasting/create">
                      <Plus className="mr-2 h-4 w-4" />
                      New Forecast
                    </Link>
                  </Button>
                </div>

                {forecasts.length > 0 ? (
                  <div className="border rounded-lg">
                    <div className="p-4 border-b bg-muted/50">
                      <div className="grid grid-cols-5 gap-4 text-sm font-medium">
                        <div>Product</div>
                        <div>Period</div>
                        <div>Demand</div>
                        <div>Confidence</div>
                        <div>Status</div>
                      </div>
                    </div>
                    <div className="divide-y">
                      {forecasts.slice(0, 10).map((forecast) => (
                        <div key={forecast.id} className="p-4 hover:bg-muted/50">
                          <div className="grid grid-cols-5 gap-4 items-center">
                            <div>
                              <p className="font-medium">{forecast.productName}</p>
                              <p className="text-sm text-muted-foreground">{forecast.productSku}</p>
                            </div>
                            <div className="text-sm">{forecast.forecastPeriod}</div>
                            <div className="text-sm font-medium">{forecast.forecastedDemand} units</div>
                            <div>
                              <Badge variant={
                                forecast.confidenceLevel === 'high' ? 'default' :
                                  forecast.confidenceLevel === 'medium' ? 'secondary' : 'outline'
                              }>
                                {forecast.confidenceLevel}
                              </Badge>
                            </div>
                            <div>
                              <Badge variant={
                                forecast.approvalStatus === 'approved' ? 'default' :
                                  forecast.approvalStatus === 'pending' ? 'secondary' : 'outline'
                              }>
                                {forecast.approvalStatus}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12 border rounded-lg">
                    <TrendingUp className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">No Forecasts Created</h3>
                    <p className="text-muted-foreground mb-6">
                      Start by creating your first demand forecast to enable material planning
                    </p>
                    <Button asChild>
                      <Link href="/planning/forecasting/create">
                        <Plus className="mr-2 h-4 w-4" />
                        Create First Forecast
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Procurement Tab */}
        <TabsContent value="procurement" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Procurement Planning
              </CardTitle>
              <CardDescription>
                Automated procurement recommendations and container optimization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">

                {plans.length > 0 ? (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Purchase Recommendations</h3>
                    <div className="border rounded-lg">
                      <div className="p-4 border-b bg-muted/50">
                        <div className="grid grid-cols-6 gap-4 text-sm font-medium">
                          <div>Material</div>
                          <div>Quantity</div>
                          <div>Supplier</div>
                          <div>Target Date</div>
                          <div>Cost</div>
                          <div>Urgency</div>
                        </div>
                      </div>
                      <div className="divide-y">
                        {plans.slice(0, 10).map((plan, index) => (
                          <div key={index} className="p-4 hover:bg-muted/50">
                            <div className="grid grid-cols-6 gap-4 items-center">
                              <div>
                                <p className="font-medium">{plan.materialName}</p>
                              </div>
                              <div className="text-sm">{parseFloat(plan.plannedQty).toLocaleString()} units</div>
                              <div className="text-sm">{plan.supplierName || "Premium Materials Supplier"}</div>
                              <div className="text-sm">{new Date(plan.targetDate).toLocaleDateString()}</div>
                              <div className="text-sm font-medium">${parseFloat(plan.estimatedCost).toLocaleString()}</div>
                              <div>
                                <PriorityBadge priority={plan.priority as any} size="sm" />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12 border rounded-lg">
                    <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">No Procurement Plans</h3>
                    <p className="text-muted-foreground mb-6">
                      Procurement plans will be generated automatically from approved demand forecasts
                    </p>
                    <Badge variant="outline">Requires approved forecasts</Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                MRP System Analytics
              </CardTitle>
              <CardDescription>
                Real-time analytics and performance insights from your MRP operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Real MRP Performance Metrics */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Performance Overview</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">{kpis.activeForecasts}</div>
                          <p className="text-sm text-muted-foreground">Active Forecasts</p>
                          <div className="text-xs text-muted-foreground mt-1">
                            {kpis.approvedForecasts} approved ({kpis.activeForecasts > 0 ? Math.round((kpis.approvedForecasts / kpis.activeForecasts) * 100) : 0}%)
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">{kpis.activeProcurementPlans}</div>
                          <p className="text-sm text-muted-foreground">Procurement Plans</p>
                          <div className="text-xs text-muted-foreground mt-1">
                            ${Math.round(kpis.totalEstimatedCost / 1000)}K total value
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">{kpis.totalSuppliers}</div>
                          <p className="text-sm text-muted-foreground">Active Suppliers</p>
                          <div className="text-xs text-muted-foreground mt-1">
                            {kpis.excellentSuppliers} excellent rated
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">{Math.round(kpis.averageLeadTime)}</div>
                          <p className="text-sm text-muted-foreground">Avg Lead Time</p>
                          <div className="text-xs text-muted-foreground mt-1">
                            days across all suppliers
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                {/* MRP Workflow Status */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">MRP Workflow Status</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Demand Forecasting</p>
                            <p className="text-sm text-muted-foreground">{kpis.activeForecasts} active forecasts</p>
                          </div>
                          <Badge variant={kpis.activeForecasts > 0 ? "default" : "outline"}>
                            {kpis.activeForecasts > 0 ? (
                              <>
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Active
                              </>
                            ) : (
                              "Inactive"
                            )}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Procurement Planning</p>
                            <p className="text-sm text-muted-foreground">{kpis.activeProcurementPlans} active plans</p>
                          </div>
                          <Badge variant={kpis.activeProcurementPlans > 0 ? "default" : "outline"}>
                            {kpis.activeProcurementPlans > 0 ? (
                              <>
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Active
                              </>
                            ) : (
                              "Inactive"
                            )}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Supplier Integration</p>
                            <p className="text-sm text-muted-foreground">{kpis.totalSuppliers} suppliers configured</p>
                          </div>
                          <Badge variant={kpis.totalSuppliers > 0 ? "default" : "outline"}>
                            {kpis.totalSuppliers > 0 ? (
                              <>
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Connected
                              </>
                            ) : (
                              "Setup Required"
                            )}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Priority Management</p>
                            <p className="text-sm text-muted-foreground">{kpis.criticalRecommendations} critical items</p>
                          </div>
                          <Badge variant={kpis.criticalRecommendations > 0 ? "destructive" : "default"}>
                            {kpis.criticalRecommendations > 0 ? (
                              <>
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Attention Required
                              </>
                            ) : (
                              <>
                                <CheckCircle className="h-3 w-3 mr-1" />
                                All Clear
                              </>
                            )}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                {/* Future Analytics Placeholder */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Advanced Analytics</h3>
                  <Card className="border-dashed">
                    <CardContent className="p-8">
                      <div className="text-center">
                        <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h4 className="text-lg font-semibold mb-2">Advanced Analytics Coming Soon</h4>
                        <p className="text-muted-foreground mb-4">
                          Enhanced charts, trend analysis, and predictive insights will be available in future updates.
                        </p>
                        <div className="flex flex-wrap justify-center gap-2">
                          <Badge variant="outline">Demand Trends</Badge>
                          <Badge variant="outline">Cost Analysis</Badge>
                          <Badge variant="outline">Supplier Performance Charts</Badge>
                          <Badge variant="outline">Lead Time Optimization</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* ✅ SUBTLE: Priority Reference - Non-distracting footer */}
      <div className="mt-12 pt-4 border-t border-border/20">
        <div className="flex items-center gap-2 mb-4">
          <Info className="h-3 w-3 text-muted-foreground/60" />
          <span className="text-xs font-medium text-muted-foreground/80">Priority Levels</span>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors">
            <PriorityBadge priority="urgent" size="sm" />
            <span className="text-xs text-muted-foreground">Immediate</span>
          </div>
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors">
            <PriorityBadge priority="high" size="sm" />
            <span className="text-xs text-muted-foreground">Soon</span>
          </div>
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors">
            <PriorityBadge priority="normal" size="sm" />
            <span className="text-xs text-muted-foreground">Standard</span>
          </div>
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors">
            <PriorityBadge priority="low" size="sm" />
            <span className="text-xs text-muted-foreground">Future</span>
          </div>
        </div>
      </div>

    </div>
    </AppShell >
  )
}
