/**
 * Manufacturing ERP - Container Optimization Demo Page
 * 
 * Professional page demonstrating container optimization visualization.
 * Shows the ContainerOptimizationViz component with sample data.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP UI Components
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { ContainerOptimizationViz } from "@/components/planning/container-optimization-viz"

// ✅ PROFESSIONAL: Server component with tenant context validation
export default async function ContainerOptimizationPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // Sample optimization result for demonstration
  const sampleOptimizationResult = {
    containers: [
      {
        containerId: "CONT-001",
        containerType: "40ft-hc" as const,
        items: [
          {
            cargoItemId: "item-1",
            name: "Steel Pipes",
            sku: "SP-001",
            loadedQuantity: 500,
            totalWeight: 2500,
            totalVolume: 15.5,
            totalValue: 12500
          },
          {
            cargoItemId: "item-2",
            name: "Aluminum Sheets",
            sku: "AS-002",
            loadedQuantity: 200,
            totalWeight: 800,
            totalVolume: 8.2,
            totalValue: 8000
          },
          {
            cargoItemId: "item-3",
            name: "Copper Wire",
            sku: "CW-003",
            loadedQuantity: 1000,
            totalWeight: 1200,
            totalVolume: 5.8,
            totalValue: 15000
          }
        ],
        totalWeight: 4500,
        totalVolume: 29.5,
        totalValue: 35500,
        weightUtilization: 0.87,
        volumeUtilization: 0.92,
        overallUtilization: 0.92,
        estimatedCost: 2000,
        estimatedLoadingTime: 4.5
      },
      {
        containerId: "CONT-002",
        containerType: "40ft" as const,
        items: [
          {
            cargoItemId: "item-4",
            name: "Plastic Components",
            sku: "PC-004",
            loadedQuantity: 800,
            totalWeight: 1600,
            totalVolume: 25.3,
            totalValue: 9600
          },
          {
            cargoItemId: "item-5",
            name: "Electronic Parts",
            sku: "EP-005",
            loadedQuantity: 300,
            totalWeight: 450,
            totalVolume: 3.2,
            totalValue: 18000
          }
        ],
        totalWeight: 2050,
        totalVolume: 28.5,
        totalValue: 27600,
        weightUtilization: 0.78,
        volumeUtilization: 0.85,
        overallUtilization: 0.85,
        estimatedCost: 1800,
        estimatedLoadingTime: 3.2
      },
      {
        containerId: "CONT-003",
        containerType: "20ft" as const,
        items: [
          {
            cargoItemId: "item-6",
            name: "Precision Tools",
            sku: "PT-006",
            loadedQuantity: 150,
            totalWeight: 750,
            totalVolume: 8.5,
            totalValue: 22500
          }
        ],
        totalWeight: 750,
        totalVolume: 8.5,
        totalValue: 22500,
        weightUtilization: 0.65,
        volumeUtilization: 0.72,
        overallUtilization: 0.72,
        estimatedCost: 1200,
        estimatedLoadingTime: 2.1
      }
    ],
    unloadedItems: [],
    summary: {
      totalContainers: 3,
      totalWeight: 7300,
      totalVolume: 66.5,
      totalValue: 85600,
      totalCost: 5000,
      averageUtilization: 0.83,
      estimatedShippingTime: 12,
      co2Emissions: 145.5
    },
    efficiency: {
      weightEfficiency: 0.85,
      volumeEfficiency: 0.88,
      costEfficiency: 0.92,
      overallScore: 0.88
    },
    recommendations: [
      "Consider consolidating CONT-003 items into larger containers for better efficiency",
      "Current utilization is excellent for CONT-001 and CONT-002",
      "Weight distribution is optimal across all containers",
      "Loading sequence optimized for operational efficiency"
    ],
    warnings: [
      "CONT-003 has lower utilization - consider combining with other shipments",
      "Ensure proper weight distribution during loading"
    ]
  }

  // Handle actions
  const handleReoptimize = async () => {
    "use server"
    // In a real implementation, this would trigger re-optimization
    console.log("Re-optimizing container loading...")
  }

  const handleExport = async () => {
    "use server"
    // In a real implementation, this would export the optimization plan
    console.log("Exporting container optimization plan...")
  }

  return (
    <AppShell>
      <div className="container mx-auto py-6">
        <ContainerOptimizationViz
          optimizationResult={sampleOptimizationResult}
          onReoptimize={handleReoptimize}
          onExport={handleExport}
        />
      </div>
    </AppShell>
  )
}
