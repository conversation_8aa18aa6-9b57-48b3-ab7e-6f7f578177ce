import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { purchaseContracts, suppliers, products, rawMaterials } from "@/lib/schema-postgres";
import { desc, eq } from "drizzle-orm";
import { PurchaseContractsClientPage } from "./purchase-contracts-client";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect } from "next/navigation";

export default async function PurchaseContractsPage() {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch data for the current company
  const allPurchaseContracts = await db.query.purchaseContracts.findMany({
    where: eq(purchaseContracts.company_id, context.companyId),
    orderBy: [desc(purchaseContracts.created_at)],
    with: {
      supplier: true,
      items: true, // ✅ FIXED: Removed product relation since it can be products or raw materials
    },
  });

  const allSuppliers = await db.query.suppliers.findMany({
    where: eq(suppliers.company_id, context.companyId),
  });

  const allProducts = await db.query.products.findMany({
    where: eq(products.company_id, context.companyId),
  });

  const allRawMaterials = await db.query.rawMaterials.findMany({
    where: eq(rawMaterials.company_id, context.companyId),
  });

  return (
    <AppShell>
      <PurchaseContractsClientPage
        initialContracts={allPurchaseContracts}
        suppliers={allSuppliers}
        products={allProducts}
        rawMaterials={allRawMaterials}
      />
    </AppShell>
  );
}
