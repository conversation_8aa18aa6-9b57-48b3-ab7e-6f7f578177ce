import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { suppliers, products, contractTemplates, purchaseContracts, rawMaterials } from "@/lib/schema-postgres";
import { eq, and } from "drizzle-orm";
import { EditPurchaseContractPage } from "./edit-purchase-contract-client";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect, notFound } from "next/navigation";

export default async function EditPurchaseContractPageServer({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  const { id } = await params;

  // 🛡️ SECURE: Get the contract with tenant isolation
  const contract = await db.query.purchaseContracts.findFirst({
    where: and(
      eq(purchaseContracts.id, id),
      eq(purchaseContracts.company_id, context.companyId)
    ),
    with: {
      supplier: true,
      items: true, // ✅ FIXED: Removed product relation since it can be products or raw materials
    },
  });

  if (!contract) {
    notFound();
  }

  // 🛡️ SECURE: Only fetch data for the current company
  const allSuppliers = await db.query.suppliers.findMany({
    where: eq(suppliers.company_id, context.companyId),
    orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
  });

  // ✅ ENHANCED: Fetch both products AND raw materials for purchase contracts
  const allProducts = await db.query.products.findMany({
    where: eq(products.company_id, context.companyId),
    orderBy: (products, { asc }) => [asc(products.name)],
  });

  const allRawMaterials = await db.query.rawMaterials.findMany({
    where: eq(rawMaterials.company_id, context.companyId),
    orderBy: (rawMaterials, { asc }) => [asc(rawMaterials.name)],
  });

  // ✅ ENHANCED: Combine products and raw materials into unified item list
  const allItems = [
    ...allProducts.map(product => ({
      id: product.id,
      name: product.name,
      sku: product.sku,
      unit: product.unit,
      price: product.price,
      description: product.description,
      category: product.category,
      type: 'product' as const
    })),
    ...allRawMaterials.map(material => ({
      id: material.id,
      name: material.name,
      sku: material.sku,
      unit: material.unit,
      price: material.standard_cost, // ✅ FIXED: Use standard_cost for raw materials
      description: material.composition,
      category: material.category,
      type: 'raw_material' as const
    }))
  ];

  const allTemplates = await db.query.contractTemplates.findMany({
    where: eq(contractTemplates.company_id, context.companyId),
    orderBy: (contractTemplates, { asc }) => [asc(contractTemplates.name)],
  });

  return (
    <AppShell>
      <EditPurchaseContractPage
        contract={contract}
        suppliers={allSuppliers}
        products={allItems}
        templates={allTemplates}
      />
    </AppShell>
  );
}
