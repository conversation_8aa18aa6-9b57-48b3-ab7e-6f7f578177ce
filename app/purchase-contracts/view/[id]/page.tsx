import { notFound } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { eq, and } from "drizzle-orm"
import { purchaseContracts, products, rawMaterials } from "@/lib/schema-postgres"
import { PurchaseContractViewClient } from "./purchase-contract-view-client"

interface PurchaseContractViewPageProps {
  params: Promise<{ id: string }>
}

export default async function PurchaseContractViewPage({ params }: PurchaseContractViewPageProps) {
  const { id } = await params
  const context = await getTenantContext()

  if (!context) {
    notFound()
  }

  // Fetch the purchase contract with related data
  const contract = await db.query.purchaseContracts.findFirst({
    where: and(
      eq(purchaseContracts.id, id),
      eq(purchaseContracts.company_id, context.companyId)
    ),
    with: {
      supplier: true,
      template: true,
      items: true, // ✅ FIXED: Removed product relation since it can be products or raw materials
    }
  })

  if (!contract) {
    notFound()
  }

  // Fetch products and raw materials for item lookup
  const allProducts = await db.query.products.findMany({
    where: eq(products.company_id, context.companyId),
  });

  const allRawMaterials = await db.query.rawMaterials.findMany({
    where: eq(rawMaterials.company_id, context.companyId),
  });

  return (
    <PurchaseContractViewClient
      contract={contract}
      companyId={context.companyId}
      products={allProducts}
      rawMaterials={allRawMaterials}
    />
  )
}
