"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Eye, FilePen, Plus, Trash2, Search } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import { useI18n } from "@/components/i18n-provider";


// Type definitions matching the schema
type PurchaseContract = {
  id: string;
  number: string;
  supplier_id: string;
  template_id?: string;
  date: string;
  currency?: string;
  status: string;
  created_at: Date | null;
  supplier: {
    id: string;
    name: string;
    contact_name: string | null;
    contact_phone: string | null;
    contact_email: string | null;
    address: string | null;
    tax_id: string | null;
    bank: string | null;
    status: string | null;
    created_at: Date | null;
  };
  items: {
    id: string;
    contract_id: string;
    product_id: string; // ✅ FIXED: Can be either product ID or raw material ID
    qty: string;
    price: string;
    // ✅ FIXED: Removed product relation since it can be products or raw materials
  }[];
};

type Supplier = {
  id: string;
  name: string;
  contact_name: string | null;
  contact_phone: string | null;
  contact_email: string | null;
  address: string | null;
  tax_id: string | null;
  bank: string | null;
  status: string | null;
  created_at: Date | null;
};

type Product = {
  id: string;
  sku: string;
  name: string;
  unit: string;
  hs_code: string | null;
  origin: string | null;
  package: string | null;
  image: string | null;
  created_at: Date | null;
};

type RawMaterial = {
  id: string;
  name: string;
  unit: string;
  standard_cost: string | null;
  supplier_id: string | null;
  hs_code: string | null;
  origin: string | null;
  package: string | null;
  image: string | null;
  created_at: Date | null;
};

// Contract templates are loaded dynamically for the view functionality





export function PurchaseContractsClientPage({
  initialContracts,
  suppliers,
  products,
  rawMaterials,
}: {
  initialContracts: PurchaseContract[];
  suppliers: Supplier[];
  products: Product[];
  rawMaterials: RawMaterial[];
}) {
  const router = useRouter();
  const { t } = useI18n();
  const [contractToDelete, setContractToDelete] = useState<PurchaseContract | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // ✅ HELPER: Look up product or raw material name and unit
  const getItemDetails = (productId: string) => {
    // Check if it's a product (starts with 'prod_')
    if (productId.startsWith('prod_')) {
      const product = products.find(p => p.id === productId);
      return product ? { name: product.name, unit: product.unit } : { name: 'Unknown Product', unit: 'unit' };
    }
    // Check if it's a raw material (starts with 'rm_')
    if (productId.startsWith('rm_')) {
      const rawMaterial = rawMaterials.find(rm => rm.id === productId);
      return rawMaterial ? { name: rawMaterial.name, unit: rawMaterial.unit } : { name: 'Unknown Raw Material', unit: 'unit' };
    }
    // Fallback
    return { name: 'Unknown Item', unit: 'unit' };
  };

  const handleViewDocument = (contract: PurchaseContract) => {
    // Navigate to the dedicated contract view page
    router.push(`/purchase-contracts/view/${contract.id}`);
  };

  const handleDelete = async () => {
    if (!contractToDelete) return;
    const response = await fetch(`/api/contracts/purchase/${contractToDelete.id}`, { method: "DELETE" });
    if (response.ok) {
      toast.success(t("purchase_contracts.success.deleted"));
      setContractToDelete(null);
      router.refresh();
    } else {
      toast.error(t("purchase_contracts.error.delete"));
    }
  };

  // Filter contracts based on search term
  const filteredContracts = initialContracts.filter(contract => {
    const search = searchTerm.toLowerCase();
    const contractNumber = contract.number.toLowerCase();
    const supplierName = contract.supplier.name.toLowerCase();
    const status = contract.status.toLowerCase();
    const currency = contract.currency?.toLowerCase() || "";

    return contractNumber.includes(search) ||
      supplierName.includes(search) ||
      status.includes(search) ||
      currency.includes(search);
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("purchase_contracts.title")}</h1>
          <p className="text-muted-foreground">{t("purchase_contracts.subtitle")}</p>
        </div>
        <Link href="/purchase-contracts/add">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            {t("purchase_contracts.add")}
          </Button>
        </Link>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t("purchase_contracts.search_placeholder")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("purchase_contracts.table.contract_number")}</TableHead>
              <TableHead>{t("purchase_contracts.table.supplier")}</TableHead>
              <TableHead>{t("purchase_contracts.table.date")}</TableHead>
              <TableHead>{t("purchase_contracts.table.currency")}</TableHead>
              <TableHead>{t("purchase_contracts.table.items")}</TableHead>
              <TableHead>{t("purchase_contracts.table.status")}</TableHead>
              <TableHead className="text-right">{t("purchase_contracts.table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredContracts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchTerm ? (
                      <>No contracts found matching "{searchTerm}"</>
                    ) : (
                      <>{t("purchase_contracts.empty")}</>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredContracts.map((contract) => (
                <TableRow key={contract.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">{contract.number}</TableCell>
                  <TableCell>{contract.supplier.name}</TableCell>
                  <TableCell>{new Date(contract.date).toLocaleDateString()}</TableCell>
                  <TableCell>{contract.currency || "-"}</TableCell>
                  <TableCell>
                    <div className="max-w-[200px]">
                      <span className="text-sm text-muted-foreground">
                        {contract.items.length} {contract.items.length === 1 ? "item" : "items"}
                      </span>
                      <div className="text-xs text-muted-foreground truncate" title={contract.items.map(item => {
                        const itemDetails = getItemDetails(item.product_id);
                        return `${itemDetails.name} (${item.qty} ${itemDetails.unit})`;
                      }).join(", ")}>
                        {contract.items.slice(0, 2).map(item => {
                          const itemDetails = getItemDetails(item.product_id);
                          return itemDetails.name;
                        }).join(", ")}
                        {contract.items.length > 2 && ` +${contract.items.length - 2} more`}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={
                      contract.status === "approved" ? "default" :
                        contract.status === "draft" ? "secondary" :
                          contract.status === "pending" ? "destructive" :
                            "outline"
                    } className={
                      contract.status === "approved" ? "bg-green-600 hover:bg-green-700 text-white" :
                        contract.status === "draft" ? "bg-gray-600 hover:bg-gray-700 text-white" :
                          contract.status === "pending" ? "bg-yellow-600 hover:bg-yellow-700 text-white" :
                            "bg-slate-600 hover:bg-slate-700 text-white"
                    }>
                      {contract.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex gap-2 justify-end">
                      {contract.template_id && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleViewDocument(contract)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          {t("common.view")}
                        </Button>
                      )}
                      <Link href={`/purchase-contracts/edit/${contract.id}`}>
                        <Button
                          size="sm"
                          variant="outline"
                        >
                          <FilePen className="h-4 w-4 mr-1" />
                          {t("common.edit")}
                        </Button>
                      </Link>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => setContractToDelete(contract)}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        {t("common.delete")}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>



      {/* Delete Alert Dialog */}
      <AlertDialog open={!!contractToDelete} onOpenChange={(open) => !open && setContractToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("purchase_contracts.delete.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("purchase_contracts.delete.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>{t("common.delete")}</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
