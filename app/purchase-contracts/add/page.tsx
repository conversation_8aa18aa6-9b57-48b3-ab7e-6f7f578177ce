import { AppShell } from "@/components/app-shell";
import { db } from "@/lib/db";
import { suppliers, products, contractTemplates, rawMaterials } from "@/lib/schema-postgres";
import { eq } from "drizzle-orm";
import { AddPurchaseContractPage } from "./add-purchase-contract-client";
import { getTenantContext } from "@/lib/tenant-utils";
import { redirect } from "next/navigation";

export default async function AddPurchaseContractPageServer() {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch data for the current company
  const allSuppliers = await db.query.suppliers.findMany({
    where: eq(suppliers.company_id, context.companyId),
    orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
  });

  // ✅ ENHANCED: Fetch both products AND raw materials for purchase contracts
  const allProducts = await db.query.products.findMany({
    where: eq(products.company_id, context.companyId),
    orderBy: (products, { asc }) => [asc(products.name)],
  });

  const allRawMaterials = await db.query.rawMaterials.findMany({
    where: eq(rawMaterials.company_id, context.companyId),
    orderBy: (rawMaterials, { asc }) => [asc(rawMaterials.name)],
  });

  // ✅ ENHANCED: Combine products and raw materials into unified item list
  const allItems = [
    ...allProducts.map(product => ({
      id: product.id,
      name: product.name,
      sku: product.sku,
      unit: product.unit,
      price: product.price,
      cost_price: product.cost_price, // ✅ FIXED: Include cost_price for purchase pricing
      base_price: product.base_price, // ✅ FIXED: Include base_price for purchase pricing
      description: product.description,
      category: product.category,
      type: 'product' as const
    })),
    ...allRawMaterials.map(material => ({
      id: material.id,
      name: material.name,
      sku: material.sku,
      unit: material.unit,
      price: material.standard_cost, // ✅ FIXED: Use standard_cost for raw materials
      description: material.composition,
      category: material.category,
      type: 'raw_material' as const
    }))
  ];

  const allTemplates = await db.query.contractTemplates.findMany({
    where: eq(contractTemplates.company_id, context.companyId),
    orderBy: (contractTemplates, { asc }) => [asc(contractTemplates.name)],
  });

  return (
    <AppShell>
      <AddPurchaseContractPage
        suppliers={allSuppliers}
        products={allItems}
        templates={allTemplates}
      />
    </AppShell>
  );
}
