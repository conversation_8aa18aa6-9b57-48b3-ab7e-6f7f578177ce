import { Suspense } from "react"
import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { db } from "@/lib/db"
import { qualityInspections, workOrders } from "@/lib/schema-postgres"
import { eq, and, desc, not } from "drizzle-orm"
import { QualityInspectionsContent } from "./quality-inspections-content"
import { QualityAnalyticsDashboard } from "@/components/quality/quality-analytics-dashboard"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

async function getQualityInspections(companyId: string) {
  try {
    const inspections = await db.query.qualityInspections.findMany({
      where: eq(qualityInspections.company_id, companyId),
      with: {
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true
              }
            }
          }
        }
      },
      orderBy: [desc(qualityInspections.created_at)]
    })

    return inspections
  } catch (error) {
    console.error("Error fetching quality inspections:", error)
    return []
  }
}

async function getWorkOrders(companyId: string) {
  try {
    // Get work orders that are completed or in progress (ready for quality inspection)
    const workOrdersList = await db.query.workOrders.findMany({
      where: and(
        eq(workOrders.company_id, companyId),
        // Only show work orders that could need quality inspection
      ),
      with: {
        product: true,
        salesContract: {
          with: {
            customer: true
          }
        }
      },
      orderBy: [desc(workOrders.created_at)]
    })

    return workOrdersList
  } catch (error) {
    console.error("Error fetching work orders:", error)
    return []
  }
}

export default async function QualityPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const [inspections, workOrdersList] = await Promise.all([
    getQualityInspections(context.companyId),
    getWorkOrders(context.companyId)
  ])

  return (
    <AppShell>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quality Control</h1>
          <p className="text-muted-foreground">
            Comprehensive quality management and analytics
          </p>
        </div>

        <Tabs defaultValue="inspections" className="space-y-6">
          <TabsList>
            <TabsTrigger value="inspections">Quality Inspections</TabsTrigger>
            <TabsTrigger value="analytics">Analytics Dashboard</TabsTrigger>
          </TabsList>

          <TabsContent value="inspections" className="space-y-6">
            <Suspense fallback={<div>Loading quality inspections...</div>}>
              <QualityInspectionsContent
                initialInspections={inspections}
                workOrders={workOrdersList}
              />
            </Suspense>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Suspense fallback={<div>Loading quality analytics...</div>}>
              <QualityAnalyticsDashboard companyId={context.companyId} />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </AppShell>
  )
}