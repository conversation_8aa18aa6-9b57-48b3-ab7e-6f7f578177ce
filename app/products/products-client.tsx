"use client";

import { useState } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, Package, Search, Eye, Pencil } from "lucide-react";
import { useSafeToast } from "@/hooks/use-safe-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";
import { useI18n } from "@/components/i18n-provider";
import { useTabNavigation } from "@/components/tab-context";
import type { products } from "@/lib/schema-postgres";

// Use Drizzle-inferred type to ensure perfect schema matching
type Product = typeof products.$inferSelect;

export function ProductsClientPage({ initialProducts }: { initialProducts: Product[] }) {
  const router = useRouter();
  const { t } = useI18n();
  const { success, error } = useSafeToast();
  const { openProductListTab, openProductViewTab, openProductAddTab, openProductEditTab } = useTabNavigation();
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [searchTerm, setSearchTerm] = useState("");



  const handleDelete = async () => {
    if (!productToDelete) return;

    const productName = productToDelete.name;

    try {
      const response = await fetch(`/api/products/${productToDelete.id}`, {
        method: "DELETE",
      });

      // Close dialog immediately
      setProductToDelete(null);

      if (response.ok) {
        success(t("products.success.deleted"));
        router.refresh();
      } else {
        let errorData = {};
        try {
          const responseText = await response.text();
          if (responseText) {
            errorData = JSON.parse(responseText);
          }
        } catch (parseError) {
          console.warn("Failed to parse error response:", parseError);
        }

        if (response.status === 409 || errorData.error?.includes("FOREIGN KEY")) {
          error(t("products.error.delete"), "This product is being used in contracts, work orders, or inventory. Please remove all references first.");
        } else {
          error(t("products.error.delete"), errorData.error || "An unexpected error occurred.");
        }
      }
    } catch (error) {
      // Close dialog immediately
      setProductToDelete(null);

      error("Failed to delete product", "Network error. Please check your connection and try again.");
    }
  };

  const filteredProducts = initialProducts.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.hs_code && product.hs_code.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (product.origin && product.origin.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("products.title")}</h1>
          <p className="text-muted-foreground">
            {t("products.subtitle")}
          </p>
        </div>
        <Button onClick={() => openProductAddTab()}>
          <Plus className="mr-2 h-4 w-4" />
          {t("products.add")}
        </Button>
      </div>

      {/* Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t("common.search") + " " + t("products.title").toLowerCase() + "..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>



      {/* Professional Products Table */}
      {filteredProducts.length === 0 ? (
        <div className="border rounded-lg">
          <div className="flex flex-col items-center justify-center py-12">
            <Package className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t("products.empty")}</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchTerm ? t("products.empty") : t("products.empty.description")}
            </p>
            <Button onClick={() => openProductAddTab()}>
              <Plus className="mr-2 h-4 w-4" />
              {t("products.add")}
            </Button>
          </div>
        </div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("products.table.name")}</TableHead>
                <TableHead>{t("products.table.sku")}</TableHead>
                <TableHead>{t("products.table.unit")}</TableHead>
                <TableHead>{t("products.table.hs_code")}</TableHead>
                <TableHead>{t("products.table.origin")}</TableHead>
                <TableHead>{t("products.table.package")}</TableHead>
                <TableHead>{t("products.table.price")}</TableHead>
                <TableHead>{t("products.form.inspection_required")}</TableHead>
                <TableHead>{t("products.table.status")}</TableHead>
                <TableHead className="text-right">{t("products.table.actions")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.map((product) => (
                <TableRow key={product.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-muted-foreground" />
                      <button
                        type="button"
                        onClick={() => openProductViewTab(product.id, product.name)}
                        className="hover:underline text-blue-600 hover:text-blue-800 text-left"
                      >
                        {product.name}
                      </button>
                    </div>
                  </TableCell>
                  <TableCell className="font-mono text-sm">{product.sku}</TableCell>
                  <TableCell>{product.unit}</TableCell>
                  <TableCell>{product.hs_code || "-"}</TableCell>
                  <TableCell>{product.origin || "-"}</TableCell>
                  <TableCell>{product.package || "-"}</TableCell>
                  <TableCell>
                    {product.base_price ? (
                      <div className="text-sm">
                        <div className="font-medium">
                          {product.currency || "USD"} {parseFloat(product.base_price).toFixed(2)}
                        </div>
                        {product.cost_price && (
                          <div className="text-xs text-muted-foreground">
                            Cost: {product.currency || "USD"} {parseFloat(product.cost_price).toFixed(2)}
                          </div>
                        )}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {product.inspection_required === "true" ? (
                      <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                        ✓ {t("products.form.inspection_required")}
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-muted-foreground">
                        - {t("products.quality.not_required")}
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant="default">{t("status.active")}</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex gap-2 justify-end">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => openProductViewTab(product.id, product.name)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => openProductEditTab(product.id, product.name)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setProductToDelete(product)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Delete Alert Dialog */}
      <AlertDialog
        open={!!productToDelete}
        onOpenChange={(open) => !open && setProductToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("products.delete.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("products.delete.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>{t("common.delete")}</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
