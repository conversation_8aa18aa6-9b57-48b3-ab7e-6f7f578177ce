"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { productSchema } from "@/lib/validations"
import { useRouter } from "next/navigation"

import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"

// ✅ USE MAIN VALIDATION SCHEMA (without margin validation issues)
type FormValues = z.infer<typeof productSchema>

export function AddProductForm({ setOpen }: { setOpen?: (open: boolean) => void }) {
  const router = useRouter()
  const { t } = useI18n()
  const { success, error } = useSafeToast()
  const form = useForm<FormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      sku: "",
      unit: "",
      hs_code: "",
      origin: "",
      package: "",
      image: "",

      // ✅ PRICING DEFAULTS
      base_price: "",
      cost_price: "",
      margin_percentage: "",
      currency: "USD",

      inspection_required: "false",
      quality_tolerance: "",
      quality_notes: "",
    },
  })

  async function onSubmit(values: FormValues) {
    const requestBody = {
      ...values,
      // inspection_required is already a string from the form
    }

    const response = await fetch("/api/products", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    })

    if (response.ok) {
      success(t("products.success.created"), t("products.success.created_desc"))

      if (setOpen) {
        setOpen(false) // Close dialog if in modal context
      } else {
        router.push('/products') // Navigate back to products list if in page context
      }

      // Refresh after a delay to ensure state updates complete
      setTimeout(() => {
        router.refresh()
      }, 100)
    } else {
      const errorData = await response.json().catch(() => ({}))

      error(t("products.error.create"), errorData.error || "An unexpected error occurred.")
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.name")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. High-Grade Widget" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="sku"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.sku")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. WID-HG-001" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="unit"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.unit")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. pcs, kg, meters" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="hs_code"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.hs_code")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. 847990" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="origin"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.origin")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. China" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="package"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.package")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Carton, Bag, Pallet" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Pricing Section */}
        <div className="border-t pt-4 mt-4">
          <h3 className="text-lg font-medium mb-4">{t("products.form.pricing_information")}</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="base_price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("products.form.base_price")}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="cost_price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("products.form.cost_price")}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="margin_percentage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("products.form.margin_percentage")}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      placeholder="0.0"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="currency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("products.form.currency")}</FormLabel>
                  <FormControl>
                    <select
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      {...field}
                    >
                      <option value="USD">USD - US Dollar</option>
                      <option value="EUR">EUR - Euro</option>
                      <option value="CNY">CNY - Chinese Yuan</option>
                      <option value="GBP">GBP - British Pound</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Quality Requirements Section */}
        <div className="border-t pt-4 mt-4">
          <h3 className="text-lg font-medium mb-4">{t("products.form.quality_requirements")}</h3>

          <FormField
            control={form.control}
            name="inspection_required"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">{t("products.form.inspection_required")}</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    {t("products.form.inspection_required_desc")}
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value === "true"}
                    onCheckedChange={(checked) => field.onChange(checked ? "true" : "false")}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="quality_tolerance"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("products.form.quality_tolerance")}</FormLabel>
                <FormControl>
                  <Input placeholder="e.g. ±5%, 0.1mm tolerance" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="quality_notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("products.form.quality_notes")}</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={t("products.form.quality_notes_placeholder")}
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="ghost"
            onClick={() => {
              if (setOpen) {
                setOpen(false) // Close dialog if in modal context
              } else {
                router.push('/products') // Navigate back if in page context
              }
            }}
          >
            {t("common.cancel")}
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? t("common.loading") : t("common.create") + " " + t("field.product")}
          </Button>
        </div>
      </form>
    </Form>
  )
}
