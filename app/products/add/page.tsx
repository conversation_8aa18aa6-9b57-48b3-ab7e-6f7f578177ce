import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { AddProductForm } from "../add-product-form"

export default async function AddProductPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  return (
    <AppShell>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Add New Product</h1>
          <p className="text-muted-foreground">
            Create a new product record with specifications and pricing information.
          </p>
        </div>

        <div className="max-w-2xl">
          <AddProductForm />
        </div>
      </div>
    </AppShell>
  )
}
