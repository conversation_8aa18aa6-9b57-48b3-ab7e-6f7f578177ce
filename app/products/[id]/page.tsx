import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { products, billOfMaterials } from "@/lib/schema-postgres"
import { eq, and, desc } from "drizzle-orm"
import { ProductViewClient } from "./product-view-client"

export default async function ProductDetailPage({
    params,
}: {
    params: Promise<{ id: string }>
}) {
    const context = await getTenantContext()
    if (!context) {
        redirect('/api/auth/login')
    }

    const { id } = await params

    // Fetch product with BOM data
    const product = await db.query.products.findFirst({
        where: and(
            eq(products.id, id),
            eq(products.company_id, context.companyId)
        ),
        with: {
            billOfMaterials: {
                orderBy: [desc(billOfMaterials.created_at)],
                with: {
                    rawMaterial: {
                        with: {
                            primarySupplier: true,
                        },
                    },
                },
            },
        },
    })

    if (!product) {
        notFound()
    }

    return (
        <AppShell>
            <ProductViewClient product={product} />
        </AppShell>
    )
}