"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Package, ArrowLeft, Edit } from "lucide-react"
import { useTabNavigation } from "@/components/tab-navigation"
import { useI18n } from "@/components/i18n-provider"
import { BOMManagement } from "./bom-management"

type Product = {
  id: string
  name: string
  sku: string
  unit: string
  base_price: string | null
  cost_price: string | null
  currency: string | null
  margin_percentage: string | null
  price_updated_at: Date | null
  hs_code: string | null
  origin: string | null
  package: string | null
  billOfMaterials: Array<{
    id: string
    qty_required: string | null
    waste_factor: string | null
    status: string
    created_at: Date | null
    rawMaterial: {
      id: string
      name: string
      unit: string | null
      standard_cost: string | null
      primarySupplier: {
        id: string
        name: string
      } | null
    } | null
  }>
}

interface ProductViewClientProps {
  product: Product
}

export function ProductViewClient({ product }: ProductViewClientProps) {
  const { t } = useI18n()
  const { openProductListTab, openProductEditTab } = useTabNavigation()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={openProductListTab}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t("common.backTo")} {t("products.title")}
        </Button>
        <Button onClick={() => openProductEditTab(product.id, product.name)}>
          <Edit className="mr-2 h-4 w-4" />
          {t("common.edit")} {t("products.product")}
        </Button>
      </div>

      {/* Product Header */}
      <div className="flex items-center gap-3">
        <Package className="h-8 w-8 text-muted-foreground" />
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{product.name}</h1>
          <p className="text-muted-foreground">
            SKU: {product.sku} • Unit: {product.unit}
          </p>
        </div>
      </div>

      {/* Product Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">{t("products.details")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="text-sm text-muted-foreground">{t("products.name")}</p>
              <p className="font-medium">{product.name}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t("products.sku")}</p>
              <p className="font-mono text-sm">{product.sku}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t("products.unit")}</p>
              <p>{product.unit}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">{t("products.pricing")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="text-sm text-muted-foreground">{t("products.basePrice")}</p>
              <p className="font-medium">
                {product.base_price ?
                  `${product.currency || 'USD'} ${parseFloat(product.base_price).toFixed(2)}` :
                  '-'
                }
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t("products.costPrice")}</p>
              <p className="font-medium">
                {product.cost_price ?
                  `${product.currency || 'USD'} ${parseFloat(product.cost_price).toFixed(2)}` :
                  '-'
                }
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t("products.margin")}</p>
              <p className="font-medium">
                {product.margin_percentage ?
                  `${parseFloat(product.margin_percentage).toFixed(1)}%` :
                  (product.base_price && product.cost_price ?
                    `${(((parseFloat(product.base_price) - parseFloat(product.cost_price)) / parseFloat(product.cost_price)) * 100).toFixed(1)}%` :
                    '-'
                  )
                }
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">{t("products.specifications")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="text-sm text-muted-foreground">{t("products.hsCode")}</p>
              <p>{product.hs_code || "-"}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t("products.origin")}</p>
              <p>{product.origin || "-"}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t("products.package")}</p>
              <p>{product.package || "-"}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="bom" className="space-y-4">
        <TabsList>
          <TabsTrigger value="bom">
            {t("products.billOfMaterials")} ({product.billOfMaterials.length})
          </TabsTrigger>
          <TabsTrigger value="work-orders">{t("workOrders.title")}</TabsTrigger>
          <TabsTrigger value="quality">{t("quality.standards")}</TabsTrigger>
        </TabsList>

        <TabsContent value="bom">
          <BOMManagement
            productId={product.id}
            productName={product.name}
            bomItems={product.billOfMaterials}
          />
        </TabsContent>

        <TabsContent value="work-orders">
          <Card>
            <CardHeader>
              <CardTitle>{t("workOrders.title")}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                {t("workOrders.forProduct")}
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quality">
          <Card>
            <CardHeader>
              <CardTitle>{t("quality.standards")}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                {t("quality.forProduct")}
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
