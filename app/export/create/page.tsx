/**
 * Manufacturing ERP - Create Export Declaration Page
 * Professional form with breadcrumb navigation and ERP standards
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { CreateDeclarationForm } from "@/components/export/create-declaration-form"
import { 
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Globe, FileText } from "lucide-react"

export default async function CreateDeclarationPage() {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext()
  
  if (!context) {
    redirect('/api/auth/login')
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Breadcrumb Navigation */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/export" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Export Trade
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                New Declaration
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Professional Header */}
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
            <FileText className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create Export Declaration</h1>
            <p className="text-muted-foreground">Add products and generate export documentation</p>
          </div>
        </div>

        {/* Create Declaration Form */}
        <CreateDeclarationForm />
      </div>
    </AppShell>
  )
}
