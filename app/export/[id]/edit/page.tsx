/**
 * Manufacturing ERP - Export Declaration Edit Page
 * Professional edit form following ERP standards
 */

import { db } from "@/lib/db"
import { declarations } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { notFound, redirect } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { AppShell } from "@/components/app-shell"
import { DeclarationEditForm } from "@/components/export/declaration-edit-form"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

interface PageProps {
  params: Promise<{ id: string }>
}

export default async function EditDeclarationPage({ params }: PageProps) {
  const { id } = await params
  
  // ✅ SECURITY: Validate tenant context
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // ✅ SECURITY: Fetch declaration with company isolation
  const declaration = await db.query.declarations.findFirst({
    where: and(
      eq(declarations.id, id),
      eq(declarations.company_id, context.companyId)
    ),
    with: {
      items: {
        with: {
          product: true,
        },
      },
    },
  })

  if (!declaration) {
    notFound()
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ PROFESSIONAL: Navigation breadcrumb */}
        <div className="flex items-center gap-4">
          <Button asChild variant="ghost">
            <Link href="/export">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Declarations
            </Link>
          </Button>
          <div className="text-sm text-muted-foreground">
            Edit Declaration: {declaration.number}
          </div>
        </div>

        {/* ✅ PROFESSIONAL: Edit form component */}
        <DeclarationEditForm declaration={declaration} />
      </div>
    </AppShell>
  )
}
