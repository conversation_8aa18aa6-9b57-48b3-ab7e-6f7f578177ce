import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { db } from "@/lib/db"
import { declarations } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { notFound, redirect } from "next/navigation"
import { DocumentManager } from "./document-manager"
import Link from "next/link"
import { ArrowLeft, Edit, FileText, Package } from "lucide-react"
import { getTenantContext } from "@/lib/tenant-utils"

export default async function DeclarationDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch declaration for the current company
  const declaration = await db.query.declarations.findFirst({
    where: and(
      eq(declarations.id, id),
      eq(declarations.company_id, context.companyId)
    ),
    with: {
      items: {
        with: {
          product: true,
        },
      },
      documents: true,
    },
  })

  if (!declaration) {
    notFound()
  }

  // ✅ PROFESSIONAL: Status helper functions
  const getStatusVariant = (status: string) => {
    switch (status) {
      case "draft": return "secondary"
      case "submitted": return "outline"
      case "approved": return "default"
      case "cleared": return "default"
      case "rejected": return "destructive"
      default: return "secondary"
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "draft": return "Draft"
      case "submitted": return "Submitted"
      case "approved": return "Approved"
      case "cleared": return "Cleared"
      case "rejected": return "Rejected"
      default: return status
    }
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ PROFESSIONAL: Navigation and actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button asChild variant="ghost">
              <Link href="/export">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Declarations
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Declaration {declaration.number}</h1>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={getStatusVariant(declaration.status)}>
                  {getStatusLabel(declaration.status)}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Created {new Date(declaration.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button asChild variant="outline">
              <Link href={`/export/${declaration.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Declaration
              </Link>
            </Button>
          </div>
        </div>

        {/* ✅ PROFESSIONAL: Declaration Details */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Declaration Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Declaration Number</label>
                <div className="font-mono text-lg">{declaration.number}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Status</label>
                <div className="mt-1">
                  <Badge variant={getStatusVariant(declaration.status)}>
                    {getStatusLabel(declaration.status)}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Created Date</label>
                <div>{new Date(declaration.created_at).toLocaleDateString()}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                <div>{new Date(declaration.updated_at).toLocaleDateString()}</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Total Items</label>
                <div className="text-2xl font-bold">{declaration.items?.length || 0}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Declaration ID</label>
                <div className="font-mono text-sm text-muted-foreground">{declaration.id}</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* ✅ PROFESSIONAL: Declaration Items Table */}
        <Card>
          <CardHeader>
            <CardTitle>Declaration Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>HS Code</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {declaration.items?.map((item: any) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{item.product?.name || "N/A"}</div>
                          {item.product?.description && (
                            <div className="text-sm text-muted-foreground">
                              {item.product.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {item.product?.sku || "N/A"}
                      </TableCell>
                      <TableCell>
                        {item.qty} {item.product?.unit || "units"}
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {item.hs_code || "N/A"}
                      </TableCell>
                    </TableRow>
                  ))}
                  {(!declaration.items || declaration.items.length === 0) && (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                        No items found for this declaration
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* ✅ PROFESSIONAL: Document Management */}
        <DocumentManager declaration={declaration} />
      </div>
    </AppShell>
  )
}
