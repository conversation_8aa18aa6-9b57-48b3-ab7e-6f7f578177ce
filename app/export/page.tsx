"use client"

import { useState, useEffect, Suspense } from "react"
import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useSearchParams } from "next/navigation"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { ExportStats } from "@/components/export/export-stats"
import { InlineStatusEditor } from "@/components/export/inline-status-editor"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertDialog<PERSON><PERSON>le,
} from "@/components/ui/alert-dialog"
import Link from "next/link"
import { Plus, Search, Globe, Eye, Edit, Trash2 } from "lucide-react"

function ExportPageContent() {
  const [declarations, setDeclarations] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const searchParams = useSearchParams()
  const statusFilter = searchParams.get('status')

  // Load declarations data
  useEffect(() => {
    async function loadDeclarations() {
      setLoading(true)
      try {
        const data = await safeJson("/api/export/declarations", [])
        setDeclarations(data)
      } catch (error) {
        console.error("Failed to load declarations:", error)
      } finally {
        setLoading(false)
      }
    }
    loadDeclarations()
  }, [])

  // Calculate statistics
  const stats = {
    total: declarations.length,
    draft: declarations.filter(d => d.status === 'draft').length,
    submitted: declarations.filter(d => d.status === 'submitted').length,
    processing: declarations.filter(d => d.status === 'processing').length,
    approved: declarations.filter(d => d.status === 'approved').length,
    cleared: declarations.filter(d => d.status === 'cleared').length,
    rejected: declarations.filter(d => d.status === 'rejected').length,
  }

  // Filter declarations based on status
  const filteredDeclarations = statusFilter
    ? declarations.filter(d => d.status === statusFilter)
    : declarations

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Professional Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
              <Globe className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Export Trade</h1>
              <p className="text-muted-foreground">Manage export declarations and customs documentation</p>
            </div>
          </div>
          <Button asChild>
            <Link href="/export/create">
              <Plus className="mr-2 h-4 w-4" />
              New Declaration
            </Link>
          </Button>
        </div>

        {/* Statistics Dashboard */}
        <ExportStats stats={stats} />

        {/* Main Content */}
        <div className="grid gap-6">
          <DeclarationsCard
            declarations={filteredDeclarations}
            loading={loading}
            onRefresh={() => window.location.reload()}
          />
        </div>
      </div>
    </AppShell>
  )
}

interface DeclarationsCardProps {
  declarations: any[]
  loading: boolean
  onRefresh: () => void
}

function DeclarationsCard({ declarations, loading, onRefresh }: DeclarationsCardProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [declarationToDelete, setDeclarationToDelete] = useState<string | null>(null)

  // ✅ PROFESSIONAL ERP: Modal dialog confirmation for delete
  function handleDelete(id: string) {
    setDeclarationToDelete(id)
    setDeleteDialogOpen(true)
  }

  // ✅ PROFESSIONAL ERP: Execute delete after confirmation
  async function executeDelete() {
    if (!declarationToDelete) return

    try {
      const response = await fetch(`/api/export/declarations/${declarationToDelete}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to delete declaration")
      }

      toast({
        title: "Success",
        description: "Declaration deleted successfully",
      })

      onRefresh()
    } catch (error) {
      console.error("Delete error:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete declaration",
        variant: "destructive",
      })
    } finally {
      setDeleteDialogOpen(false)
      setDeclarationToDelete(null)
    }
  }

  // Filter declarations based on search term
  const filteredDeclarations = declarations.filter(declaration =>
    declaration.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    declaration.status.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search declarations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Declarations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Export Declarations</CardTitle>
          <CardDescription>
            {filteredDeclarations.length} of {declarations.length} declarations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8 text-muted-foreground">Loading declarations...</div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("field.declarationNo")}</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Items</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>{t("table.actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDeclarations.map((declaration) => (
                    <TableRow key={declaration.id} className="group">
                      <TableCell>
                        <Link href={`/export/${declaration.id}`} className="hover:underline font-medium">
                          {declaration.number}
                        </Link>
                      </TableCell>
                      <TableCell>
                        <InlineStatusEditor
                          declarationId={declaration.id}
                          currentStatus={declaration.status}
                          declarationNumber={declaration.number}
                          onStatusChange={() => onRefresh()}
                        />
                      </TableCell>
                      <TableCell>{declaration.items?.length || 0}</TableCell>
                      <TableCell className="text-muted-foreground">
                        {new Date(declaration.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/export/${declaration.id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/export/${declaration.id}/edit`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(declaration.id)}
                            className="text-destructive hover:text-destructive"
                            disabled={declaration.status === 'cleared'}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredDeclarations.length === 0 && !loading && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                        {searchTerm ? 'No declarations match your search.' : t("export.empty")}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* ✅ PROFESSIONAL: Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this declaration? This action cannot be undone and will permanently remove the declaration and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={executeDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Declaration
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

export default function ExportPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ExportPageContent />
    </Suspense>
  )
}
