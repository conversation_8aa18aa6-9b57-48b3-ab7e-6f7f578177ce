/**
 * Test script to verify procurement button functionality
 * Run this with: node test-procurement-buttons.js
 */

const BASE_URL = 'http://localhost:3000'

async function testProcurementAPI() {
  console.log('🧪 Testing Procurement Button Functionality...\n')

  try {
    // Test 1: Get procurement plans
    console.log('1️⃣ Testing GET /api/planning/procurement')
    const getResponse = await fetch(`${BASE_URL}/api/planning/procurement`, {
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (getResponse.ok) {
      const data = await getResponse.json()
      console.log('✅ GET request successful')
      console.log(`   Found ${data.plans?.length || 0} procurement plans`)
      
      if (data.plans && data.plans.length > 0) {
        const draftPlans = data.plans.filter(p => p.status === 'draft')
        console.log(`   ${draftPlans.length} plans are in draft status`)
        
        if (draftPlans.length > 0) {
          // Test 2: Update individual plan status
          console.log('\n2️⃣ Testing PATCH /api/planning/procurement/[id]')
          const planId = draftPlans[0].id
          
          const patchResponse = await fetch(`${BASE_URL}/api/planning/procurement/${planId}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              status: 'approved'
            })
          })
          
          if (patchResponse.ok) {
            console.log('✅ PATCH request successful - individual plan approval works')
          } else {
            const error = await patchResponse.text()
            console.log('❌ PATCH request failed:', error)
          }
          
          // Test 3: Bulk approve
          console.log('\n3️⃣ Testing POST /api/planning/procurement (bulk approve)')
          const bulkResponse = await fetch(`${BASE_URL}/api/planning/procurement`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              action: 'bulk-approve',
              planIds: draftPlans.slice(0, 2).map(p => p.id),
              approvalNotes: 'Test bulk approval'
            })
          })
          
          if (bulkResponse.ok) {
            const bulkData = await bulkResponse.json()
            console.log('✅ Bulk approve request successful')
            console.log(`   Approved: ${bulkData.summary?.approved || 0}`)
            console.log(`   Failed: ${bulkData.summary?.failed || 0}`)
          } else {
            const error = await bulkResponse.text()
            console.log('❌ Bulk approve request failed:', error)
          }
        } else {
          console.log('⚠️  No draft plans found to test approval functionality')
        }
      } else {
        console.log('⚠️  No procurement plans found')
      }
    } else {
      console.log('❌ GET request failed:', getResponse.status, getResponse.statusText)
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message)
  }
  
  console.log('\n🏁 Test completed!')
}

// Run the test
testProcurementAPI()
