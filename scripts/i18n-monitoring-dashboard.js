#!/usr/bin/env node

/**
 * Manufacturing ERP i18n Monitoring Dashboard
 * 
 * Comprehensive monitoring for translation quality and workflow efficiency
 * with continuous optimization recommendations.
 * 
 * ZERO BREAKING CHANGES: Monitoring only, no system modifications.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const MONITORING_CONFIG = {
  // Data collection intervals
  collectInterval: 24 * 60 * 60 * 1000, // 24 hours
  retentionDays: 30,

  // Quality thresholds
  qualityThresholds: {
    excellent: 90,
    good: 80,
    acceptable: 70,
    needsImprovement: 60
  },

  // Performance thresholds
  performanceThresholds: {
    processingTime: 60000, // 1 minute
    integrationTime: 10000, // 10 seconds
    validationTime: 30000,  // 30 seconds
    rollbackTime: 1000      // 1 second
  },

  // Workflow efficiency targets
  efficiencyTargets: {
    timeReduction: 80,      // 80% time reduction target
    qualityScore: 85,       // 85% quality score target
    teamSatisfaction: 90,   // 90% team satisfaction target
    errorRate: 5            // <5% error rate target
  }
};

// Monitoring data directory
const MONITORING_DIR = 'i18n-monitoring';
const METRICS_DIR = path.join(MONITORING_DIR, 'metrics');
const REPORTS_DIR = path.join(MONITORING_DIR, 'reports');
const TRENDS_DIR = path.join(MONITORING_DIR, 'trends');

// Ensure monitoring directories exist
function ensureMonitoringDirectories() {
  [MONITORING_DIR, METRICS_DIR, REPORTS_DIR, TRENDS_DIR].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// Collect current system metrics
function collectSystemMetrics() {
  console.log('📊 Collecting system metrics...');

  const metrics = {
    timestamp: new Date().toISOString(),
    system: {
      translationCount: 0,
      fileSize: 0,
      loadTime: 0,
      memoryUsage: 0
    },
    workflow: {
      pendingTranslations: 0,
      approvedTranslations: 0,
      integratedTranslations: 0,
      csvExports: 0
    },
    quality: {
      averageScore: 0,
      validationPassed: 0,
      validationFailed: 0,
      issuesFound: 0
    },
    performance: {
      detectionTime: 0,
      processingTime: 0,
      integrationTime: 0,
      rollbackTime: 0
    },
    team: {
      csvExportsUsed: 0,
      reviewCycles: 0,
      collaborationScore: 0
    }
  };

  try {
    // System metrics
    if (fs.existsSync('components/i18n-provider.tsx')) {
      const i18nContent = fs.readFileSync('components/i18n-provider.tsx', 'utf8');
      metrics.system.fileSize = Buffer.byteLength(i18nContent, 'utf8');

      // Count translations
      const enMatches = i18nContent.match(/"[^"]+"\s*:\s*"[^"]+"/g) || [];
      metrics.system.translationCount = enMatches.length;

      // Measure load time
      const startTime = Date.now();
      try {
        require.resolve('../components/i18n-provider.tsx');
        metrics.system.loadTime = Date.now() - startTime;
      } catch (error) {
        // File might not be a valid module
        metrics.system.loadTime = 0;
      }
    }

    // Workflow metrics
    const workflowDirs = {
      pending: 'i18n-parallel/pending',
      approved: 'i18n-parallel/approved',
      integrated: 'i18n-parallel/integrated',
      csv: 'i18n-parallel/csv'
    };

    Object.entries(workflowDirs).forEach(([key, dir]) => {
      if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir).filter(f => f.endsWith('.json') || f.endsWith('.csv'));
        metrics.workflow[`${key}Translations`] = files.length;
      }
    });

    // Quality metrics from recent validation reports
    const validationFiles = fs.existsSync('i18n-parallel/validation')
      ? fs.readdirSync('i18n-parallel/validation').filter(f => f.endsWith('.json'))
      : [];

    if (validationFiles.length > 0) {
      let totalScore = 0;
      let validCount = 0;

      validationFiles.slice(-5).forEach(file => { // Last 5 reports
        try {
          const report = JSON.parse(fs.readFileSync(path.join('i18n-parallel/validation', file), 'utf8'));
          if (report.overallScore) {
            totalScore += report.overallScore;
            validCount++;
          }
        } catch (error) {
          // Skip invalid files
        }
      });

      if (validCount > 0) {
        metrics.quality.averageScore = Math.round(totalScore / validCount);
        metrics.quality.validationPassed = validCount;
      }
    }

    // Performance metrics from recent CI/CD reports
    const cicdFiles = fs.existsSync('i18n-cicd')
      ? fs.readdirSync('i18n-cicd').filter(f => f.endsWith('.json'))
      : [];

    if (cicdFiles.length > 0) {
      const recentReport = cicdFiles.sort().pop();
      try {
        const report = JSON.parse(fs.readFileSync(path.join('i18n-cicd', recentReport), 'utf8'));
        if (report.results) {
          // Extract performance metrics from CI/CD report
          metrics.performance.detectionTime = extractMetric(report, 'detection_time', 0);
          metrics.performance.processingTime = extractMetric(report, 'processing_time', 0);
        }
      } catch (error) {
        // Skip invalid files
      }
    }

    // Memory usage (current process)
    const memUsage = process.memoryUsage();
    metrics.system.memoryUsage = Math.round(memUsage.heapUsed / 1024 / 1024); // MB

  } catch (error) {
    console.warn('⚠️  Some metrics collection failed:', error.message);
  }

  return metrics;
}

// Extract metric from report
function extractMetric(report, metricName, defaultValue) {
  try {
    // Try to find metric in various report structures
    if (report.performance && report.performance[metricName]) {
      return report.performance[metricName];
    }
    if (report.results && report.results[metricName]) {
      return report.results[metricName];
    }
    return defaultValue;
  } catch (error) {
    return defaultValue;
  }
}

// Analyze trends from historical data
function analyzeTrends() {
  console.log('📈 Analyzing trends...');

  const trends = {
    timestamp: new Date().toISOString(),
    period: '30 days',
    quality: {
      trend: 'stable',
      averageScore: 0,
      improvement: 0,
      recommendation: ''
    },
    performance: {
      trend: 'stable',
      averageTime: 0,
      improvement: 0,
      recommendation: ''
    },
    workflow: {
      trend: 'stable',
      efficiency: 0,
      improvement: 0,
      recommendation: ''
    },
    team: {
      trend: 'stable',
      collaboration: 0,
      improvement: 0,
      recommendation: ''
    }
  };

  try {
    // Load historical metrics
    const metricsFiles = fs.existsSync(METRICS_DIR)
      ? fs.readdirSync(METRICS_DIR).filter(f => f.endsWith('.json')).sort()
      : [];

    if (metricsFiles.length >= 2) {
      const recentMetrics = metricsFiles.slice(-7); // Last 7 days
      const historicalData = [];

      recentMetrics.forEach(file => {
        try {
          const data = JSON.parse(fs.readFileSync(path.join(METRICS_DIR, file), 'utf8'));
          historicalData.push(data);
        } catch (error) {
          // Skip invalid files
        }
      });

      if (historicalData.length >= 2) {
        // Analyze quality trends
        const qualityScores = historicalData.map(d => d.quality.averageScore).filter(s => s > 0);
        if (qualityScores.length >= 2) {
          const avgQuality = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length;
          const firstScore = qualityScores[0];
          const lastScore = qualityScores[qualityScores.length - 1];

          trends.quality.averageScore = Math.round(avgQuality);
          trends.quality.improvement = Math.round(((lastScore - firstScore) / firstScore) * 100);
          trends.quality.trend = trends.quality.improvement > 5 ? 'improving' :
            trends.quality.improvement < -5 ? 'declining' : 'stable';
        }

        // Analyze performance trends
        const processingTimes = historicalData.map(d => d.performance.processingTime).filter(t => t > 0);
        if (processingTimes.length >= 2) {
          const avgTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
          const firstTime = processingTimes[0];
          const lastTime = processingTimes[processingTimes.length - 1];

          trends.performance.averageTime = Math.round(avgTime);
          trends.performance.improvement = Math.round(((firstTime - lastTime) / firstTime) * 100);
          trends.performance.trend = trends.performance.improvement > 10 ? 'improving' :
            trends.performance.improvement < -10 ? 'declining' : 'stable';
        }

        // Analyze workflow trends
        const workflowEfficiency = historicalData.map(d => {
          const total = d.workflow.pendingTranslations + d.workflow.approvedTranslations + d.workflow.integratedTranslations;
          return total > 0 ? (d.workflow.integratedTranslations / total) * 100 : 0;
        }).filter(e => e > 0);

        if (workflowEfficiency.length >= 2) {
          const avgEfficiency = workflowEfficiency.reduce((sum, eff) => sum + eff, 0) / workflowEfficiency.length;
          const firstEff = workflowEfficiency[0];
          const lastEff = workflowEfficiency[workflowEfficiency.length - 1];

          trends.workflow.efficiency = Math.round(avgEfficiency);
          trends.workflow.improvement = Math.round(((lastEff - firstEff) / firstEff) * 100);
          trends.workflow.trend = trends.workflow.improvement > 10 ? 'improving' :
            trends.workflow.improvement < -10 ? 'declining' : 'stable';
        }
      }
    }

    // Generate recommendations
    trends.quality.recommendation = generateQualityRecommendation(trends.quality);
    trends.performance.recommendation = generatePerformanceRecommendation(trends.performance);
    trends.workflow.recommendation = generateWorkflowRecommendation(trends.workflow);
    trends.team.recommendation = generateTeamRecommendation(trends.team);

  } catch (error) {
    console.warn('⚠️  Trend analysis failed:', error.message);
  }

  return trends;
}

// Generate quality recommendations
function generateQualityRecommendation(quality) {
  if (quality.averageScore >= MONITORING_CONFIG.qualityThresholds.excellent) {
    return 'Excellent quality maintained - continue current practices';
  } else if (quality.averageScore >= MONITORING_CONFIG.qualityThresholds.good) {
    return 'Good quality - consider minor optimizations for excellence';
  } else if (quality.averageScore >= MONITORING_CONFIG.qualityThresholds.acceptable) {
    return 'Acceptable quality - focus on terminology consistency improvements';
  } else {
    return 'Quality needs improvement - review translation validation process';
  }
}

// Generate performance recommendations
function generatePerformanceRecommendation(performance) {
  if (performance.trend === 'improving') {
    return 'Performance improving - continue optimization efforts';
  } else if (performance.trend === 'declining') {
    return 'Performance declining - investigate bottlenecks and optimize';
  } else if (performance.averageTime > MONITORING_CONFIG.performanceThresholds.processingTime) {
    return 'Processing time high - consider workflow optimization';
  } else {
    return 'Performance stable - monitor for continued efficiency';
  }
}

// Generate workflow recommendations
function generateWorkflowRecommendation(workflow) {
  if (workflow.efficiency >= 90) {
    return 'Excellent workflow efficiency - maintain current processes';
  } else if (workflow.efficiency >= 70) {
    return 'Good workflow efficiency - minor optimizations possible';
  } else if (workflow.efficiency >= 50) {
    return 'Moderate efficiency - review bottlenecks in approval process';
  } else {
    return 'Low efficiency - comprehensive workflow review needed';
  }
}

// Generate team recommendations
function generateTeamRecommendation(team) {
  if (team.collaboration >= 90) {
    return 'Excellent team collaboration - continue current practices';
  } else if (team.collaboration >= 70) {
    return 'Good collaboration - consider additional training or tools';
  } else {
    return 'Team collaboration needs improvement - review training and processes';
  }
}

// Generate comprehensive monitoring report
function generateMonitoringReport(metrics, trends) {
  const report = {
    timestamp: new Date().toISOString(),
    period: 'Current Status',
    summary: {
      overallHealth: 'good',
      qualityScore: metrics.quality.averageScore,
      performanceRating: 'acceptable',
      workflowEfficiency: 0,
      teamCollaboration: 0,
      recommendations: []
    },
    metrics,
    trends,
    alerts: [],
    optimizations: []
  };

  // Calculate overall health
  const healthFactors = [];

  if (metrics.quality.averageScore >= MONITORING_CONFIG.qualityThresholds.good) {
    healthFactors.push('quality');
  }

  if (metrics.performance.processingTime <= MONITORING_CONFIG.performanceThresholds.processingTime) {
    healthFactors.push('performance');
  }

  if (metrics.workflow.integratedTranslations > metrics.workflow.pendingTranslations) {
    healthFactors.push('workflow');
  }

  // Determine overall health
  if (healthFactors.length >= 3) {
    report.summary.overallHealth = 'excellent';
  } else if (healthFactors.length >= 2) {
    report.summary.overallHealth = 'good';
  } else if (healthFactors.length >= 1) {
    report.summary.overallHealth = 'acceptable';
  } else {
    report.summary.overallHealth = 'needs-attention';
  }

  // Generate alerts
  if (metrics.quality.averageScore < MONITORING_CONFIG.qualityThresholds.acceptable) {
    report.alerts.push({
      type: 'quality',
      severity: 'high',
      message: `Quality score ${metrics.quality.averageScore}% below acceptable threshold`,
      action: 'Review translation validation process'
    });
  }

  if (metrics.performance.processingTime > MONITORING_CONFIG.performanceThresholds.processingTime) {
    report.alerts.push({
      type: 'performance',
      severity: 'medium',
      message: `Processing time ${metrics.performance.processingTime}ms exceeds threshold`,
      action: 'Optimize workflow performance'
    });
  }

  // Generate optimization recommendations
  report.optimizations = [
    {
      category: 'quality',
      recommendation: trends.quality.recommendation,
      priority: metrics.quality.averageScore < MONITORING_CONFIG.qualityThresholds.good ? 'high' : 'medium'
    },
    {
      category: 'performance',
      recommendation: trends.performance.recommendation,
      priority: metrics.performance.processingTime > MONITORING_CONFIG.performanceThresholds.processingTime ? 'high' : 'low'
    },
    {
      category: 'workflow',
      recommendation: trends.workflow.recommendation,
      priority: 'medium'
    }
  ];

  // Summary recommendations
  report.summary.recommendations = report.optimizations
    .filter(opt => opt.priority === 'high')
    .map(opt => opt.recommendation);

  if (report.summary.recommendations.length === 0) {
    report.summary.recommendations.push('System operating well - continue monitoring');
  }

  return report;
}

// Display monitoring dashboard
function displayMonitoringDashboard(report) {
  console.log('\n📊 MANUFACTURING ERP I18N MONITORING DASHBOARD');
  console.log('='.repeat(60));
  console.log(`Report Generated: ${new Date().toLocaleString()}`);
  console.log(`Overall Health: ${report.summary.overallHealth.toUpperCase()}`);

  // System Status
  console.log('\n🔍 SYSTEM STATUS');
  console.log('-'.repeat(30));
  console.log(`Translation Count: ${report.metrics.system.translationCount.toLocaleString()}`);
  console.log(`File Size: ${Math.round(report.metrics.system.fileSize / 1024)} KB`);
  console.log(`Memory Usage: ${report.metrics.system.memoryUsage} MB`);
  console.log(`Load Time: ${report.metrics.system.loadTime}ms`);

  // Quality Metrics
  console.log('\n🎯 QUALITY METRICS');
  console.log('-'.repeat(30));
  console.log(`Average Score: ${report.metrics.quality.averageScore}%`);
  console.log(`Validation Passed: ${report.metrics.quality.validationPassed}`);
  console.log(`Issues Found: ${report.metrics.quality.issuesFound}`);

  const qualityStatus = report.metrics.quality.averageScore >= MONITORING_CONFIG.qualityThresholds.excellent ? '🟢 Excellent' :
    report.metrics.quality.averageScore >= MONITORING_CONFIG.qualityThresholds.good ? '🟡 Good' :
      report.metrics.quality.averageScore >= MONITORING_CONFIG.qualityThresholds.acceptable ? '🟠 Acceptable' : '🔴 Needs Improvement';
  console.log(`Quality Status: ${qualityStatus}`);

  // Workflow Efficiency
  console.log('\n⚡ WORKFLOW EFFICIENCY');
  console.log('-'.repeat(30));
  console.log(`Pending: ${report.metrics.workflow.pendingTranslations}`);
  console.log(`Approved: ${report.metrics.workflow.approvedTranslations}`);
  console.log(`Integrated: ${report.metrics.workflow.integratedTranslations}`);
  console.log(`CSV Exports: ${report.metrics.workflow.csvExports}`);

  // Performance Metrics
  console.log('\n🚀 PERFORMANCE METRICS');
  console.log('-'.repeat(30));
  console.log(`Detection Time: ${report.metrics.performance.detectionTime}ms`);
  console.log(`Processing Time: ${report.metrics.performance.processingTime}ms`);
  console.log(`Integration Time: ${report.metrics.performance.integrationTime}ms`);

  // Trends
  if (report.trends.quality.trend !== 'stable') {
    console.log('\n📈 TRENDS');
    console.log('-'.repeat(30));
    console.log(`Quality Trend: ${report.trends.quality.trend} (${report.trends.quality.improvement > 0 ? '+' : ''}${report.trends.quality.improvement}%)`);
    console.log(`Performance Trend: ${report.trends.performance.trend} (${report.trends.performance.improvement > 0 ? '+' : ''}${report.trends.performance.improvement}%)`);
    console.log(`Workflow Trend: ${report.trends.workflow.trend} (${report.trends.workflow.improvement > 0 ? '+' : ''}${report.trends.workflow.improvement}%)`);
  }

  // Alerts
  if (report.alerts.length > 0) {
    console.log('\n🚨 ALERTS');
    console.log('-'.repeat(30));
    report.alerts.forEach(alert => {
      const icon = alert.severity === 'high' ? '🔴' : alert.severity === 'medium' ? '🟡' : '🟢';
      console.log(`${icon} ${alert.message}`);
      console.log(`   Action: ${alert.action}`);
    });
  }

  // Recommendations
  console.log('\n🎯 RECOMMENDATIONS');
  console.log('-'.repeat(30));
  report.summary.recommendations.forEach(rec => console.log(`• ${rec}`));

  // Optimization Opportunities
  const highPriorityOpts = report.optimizations.filter(opt => opt.priority === 'high');
  if (highPriorityOpts.length > 0) {
    console.log('\n🔧 HIGH PRIORITY OPTIMIZATIONS');
    console.log('-'.repeat(30));
    highPriorityOpts.forEach(opt => console.log(`• ${opt.recommendation}`));
  }

  console.log('\n✅ Monitoring dashboard updated successfully');
}

// Save monitoring data
function saveMonitoringData(metrics, trends, report) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

  // Save metrics
  const metricsFile = path.join(METRICS_DIR, `metrics-${timestamp}.json`);
  fs.writeFileSync(metricsFile, JSON.stringify(metrics, null, 2));

  // Save trends
  const trendsFile = path.join(TRENDS_DIR, `trends-${timestamp}.json`);
  fs.writeFileSync(trendsFile, JSON.stringify(trends, null, 2));

  // Save report
  const reportFile = path.join(REPORTS_DIR, `report-${timestamp}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

  // Save latest report for easy access
  const latestReportFile = path.join(MONITORING_DIR, 'latest-report.json');
  fs.writeFileSync(latestReportFile, JSON.stringify(report, null, 2));

  return {
    metricsFile,
    trendsFile,
    reportFile,
    latestReportFile
  };
}

// Main monitoring function
async function runMonitoringDashboard() {
  console.log('🚀 Manufacturing ERP i18n Monitoring Dashboard\n');
  console.log('⚠️  ZERO BREAKING CHANGES: Monitoring only, no system modifications\n');

  try {
    // Setup
    ensureMonitoringDirectories();

    // Collect data
    const metrics = collectSystemMetrics();
    const trends = analyzeTrends();
    const report = generateMonitoringReport(metrics, trends);

    // Display dashboard
    displayMonitoringDashboard(report);

    // Save data
    const savedFiles = saveMonitoringData(metrics, trends, report);

    console.log('\n📁 MONITORING DATA SAVED');
    console.log('-'.repeat(30));
    console.log(`Latest Report: ${savedFiles.latestReportFile}`);
    console.log(`Detailed Report: ${savedFiles.reportFile}`);
    console.log(`Metrics: ${savedFiles.metricsFile}`);
    console.log(`Trends: ${savedFiles.trendsFile}`);

    return {
      success: true,
      report,
      files: savedFiles
    };

  } catch (error) {
    console.error('❌ Monitoring dashboard failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];

  switch (command) {
    case 'run':
      runMonitoringDashboard();
      break;

    case 'metrics':
      ensureMonitoringDirectories();
      const metrics = collectSystemMetrics();
      console.log('📊 Current Metrics:');
      console.log(JSON.stringify(metrics, null, 2));
      break;

    case 'trends':
      ensureMonitoringDirectories();
      const trends = analyzeTrends();
      console.log('📈 Trend Analysis:');
      console.log(JSON.stringify(trends, null, 2));
      break;

    case 'latest':
      const latestFile = path.join(MONITORING_DIR, 'latest-report.json');
      if (fs.existsSync(latestFile)) {
        const latest = JSON.parse(fs.readFileSync(latestFile, 'utf8'));
        displayMonitoringDashboard(latest);
      } else {
        console.log('No latest report found. Run monitoring first.');
      }
      break;

    default:
      console.log('📖 Manufacturing ERP i18n Monitoring Dashboard');
      console.log('');
      console.log('Commands:');
      console.log('  run     - Run complete monitoring dashboard');
      console.log('  metrics - Collect current system metrics');
      console.log('  trends  - Analyze historical trends');
      console.log('  latest  - Display latest monitoring report');
      console.log('');
      console.log('Features:');
      console.log('  - Real-time system metrics');
      console.log('  - Quality trend analysis');
      console.log('  - Performance monitoring');
      console.log('  - Workflow efficiency tracking');
      console.log('  - Automated recommendations');
  }
}

module.exports = { runMonitoringDashboard, collectSystemMetrics, analyzeTrends };
