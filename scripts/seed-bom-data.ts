/**
 * Manufacturing ERP - Seed BOM Data Script
 * 
 * Creates sample Bill of Materials data for existing products to enable
 * demand forecasting and procurement planning functionality.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1C MRP Implementation
 */

import { db, uid } from "../lib/db"
import { 
  products, 
  rawMaterials, 
  billOfMaterials, 
  suppliers,
  companies
} from "../lib/schema-postgres"
import { eq, and } from "drizzle-orm"

async function seedBOMData() {
  try {
    console.log("🌱 Starting BOM data seeding...")

    // Get all companies to seed data for each
    const allCompanies = await db.query.companies.findMany({
      limit: 5, // Limit to first 5 companies
    })

    if (allCompanies.length === 0) {
      console.log("❌ No companies found")
      return
    }

    for (const company of allCompanies) {
      console.log(`\n🏢 Processing company: ${company.name} (${company.id})`)

      // Get existing products for this company
      const existingProducts = await db.query.products.findMany({
        where: eq(products.company_id, company.id),
        limit: 5, // Limit to first 5 products per company
      })

      if (existingProducts.length === 0) {
        console.log(`   ⚠️  No products found for company ${company.name}`)
        continue
      }

      console.log(`   📦 Found ${existingProducts.length} products`)

      // Get or create raw materials
      let existingRawMaterials = await db.query.rawMaterials.findMany({
        where: eq(rawMaterials.company_id, company.id),
      })

      // Create sample raw materials if none exist
      if (existingRawMaterials.length === 0) {
        console.log(`   🔧 Creating sample raw materials...`)

        // First, get or create a supplier
        let supplier = await db.query.suppliers.findFirst({
          where: eq(suppliers.company_id, company.id),
        })

        if (!supplier) {
          const supplierId = uid("sup")
          await db.insert(suppliers).values({
            id: supplierId,
            company_id: company.id,
            name: "Premium Materials Supplier",
            email: "<EMAIL>",
            phone: "******-0456",
            address: "456 Supplier Ave",
            city: "Material City",
            state: "MC",
            postal_code: "12345",
            country: "USA",
          })
          
          supplier = { id: supplierId } as any
          console.log(`   ✅ Created supplier: ${supplierId}`)
        }

        // Create sample raw materials
        const sampleRawMaterials = [
          {
            id: uid("rm"),
            company_id: company.id,
            sku: "YARN-COTTON-30S",
            name: "Cotton Yarn 30s",
            category: "yarn",
            unit: "kg",
            primary_supplier_id: supplier.id,
            composition: "100% Cotton",
            quality_grade: "Premium",
            standard_cost: "4.50",
            currency: "USD",
            reorder_point: "100",
            max_stock_level: "1000",
            inspection_required: "true",
          },
          {
            id: uid("rm"),
            company_id: company.id,
            sku: "FABRIC-POLY-150GSM",
            name: "Polyester Fabric 150GSM",
            category: "fabric",
            unit: "meters",
            primary_supplier_id: supplier.id,
            composition: "100% Polyester",
            quality_grade: "Standard",
            standard_cost: "3.20",
            currency: "USD",
            reorder_point: "50",
            max_stock_level: "500",
            inspection_required: "true",
          },
          {
            id: uid("rm"),
            company_id: company.id,
            sku: "DYE-REACTIVE-BLUE",
            name: "Reactive Blue Dye",
            category: "dyes",
            unit: "kg",
            primary_supplier_id: supplier.id,
            composition: "Reactive Dye",
            quality_grade: "Industrial",
            standard_cost: "12.00",
            currency: "USD",
            reorder_point: "25",
            max_stock_level: "200",
            inspection_required: "true",
          },
        ]

        await db.insert(rawMaterials).values(sampleRawMaterials)
        console.log(`   ✅ Created ${sampleRawMaterials.length} raw materials`)
        
        // Refresh the raw materials list
        existingRawMaterials = await db.query.rawMaterials.findMany({
          where: eq(rawMaterials.company_id, company.id),
        })
      }

      console.log(`   🧱 Found ${existingRawMaterials.length} raw materials`)

      // Create BOM entries for each product
      let bomItemsCreated = 0
      
      for (const product of existingProducts) {
        // Check if BOM already exists for this product
        const existingBom = await db.query.billOfMaterials.findFirst({
          where: and(
            eq(billOfMaterials.company_id, company.id),
            eq(billOfMaterials.product_id, product.id)
          ),
        })

        if (existingBom) {
          console.log(`   ⏭️  BOM already exists for product ${product.sku}`)
          continue
        }

        // Create BOM items for this product (use first 2-3 raw materials)
        const materialsToUse = existingRawMaterials.slice(0, Math.min(3, existingRawMaterials.length))
        
        for (let i = 0; i < materialsToUse.length; i++) {
          const material = materialsToUse[i]
          
          // Calculate realistic quantities based on material type
          let qtyRequired = "1.0"
          let wasteFactor = "0.05"
          
          if (material.category === "yarn") {
            qtyRequired = "0.5" // 0.5 kg yarn per product unit
            wasteFactor = "0.10" // 10% waste for yarn
          } else if (material.category === "fabric") {
            qtyRequired = "1.2" // 1.2 meters fabric per product unit
            wasteFactor = "0.05" // 5% waste for fabric
          } else if (material.category === "dyes") {
            qtyRequired = "0.05" // 0.05 kg dye per product unit
            wasteFactor = "0.02" // 2% waste for dyes
          }

          const bomItem = {
            id: uid("bom"),
            company_id: company.id,
            product_id: product.id,
            raw_material_id: material.id,
            qty_required: qtyRequired,
            unit: material.unit,
            waste_factor: wasteFactor,
            status: "active",
          }

          await db.insert(billOfMaterials).values(bomItem)
          bomItemsCreated++
        }

        console.log(`   ✅ Created BOM for product ${product.sku} (${materialsToUse.length} materials)`)
      }

      console.log(`   🎯 Total BOM items created for ${company.name}: ${bomItemsCreated}`)
    }

    console.log("\n🎉 BOM data seeding completed successfully!")
    console.log("\n📋 Next steps:")
    console.log("   1. Approved demand forecasts will now generate procurement plans")
    console.log("   2. Material requirements can be calculated from forecasts")
    console.log("   3. Procurement planning dashboard will show active plans")

  } catch (error) {
    console.error("❌ Error seeding BOM data:", error)
    throw error
  }
}

// Run the seeding script
seedBOMData()
  .then(() => {
    console.log("✅ Script completed successfully")
    process.exit(0)
  })
  .catch((error) => {
    console.error("❌ Script failed:", error)
    process.exit(1)
  })
