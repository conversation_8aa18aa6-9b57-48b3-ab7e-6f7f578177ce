-- Manufacturing ERP - Financial Integration Migration
-- Add contract integration fields to AR/AP invoices
-- Professional ERP workflow enhancement

-- ============================================================================
-- AR INVOICES ENHANCEMENTS
-- ============================================================================

-- Add contract integration fields to ar_invoices
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS sales_contract_id TEXT REFERENCES sales_contracts(id);
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS contract_reference TEXT;
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS due_date TEXT;
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS received TEXT DEFAULT '0';
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'USD';
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS payment_terms TEXT;
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS ar_invoices_contract_id_idx ON ar_invoices(sales_contract_id);

-- Update existing records to have default values
UPDATE ar_invoices SET 
  received = '0' WHERE received IS NULL;
UPDATE ar_invoices SET 
  currency = 'USD' WHERE currency IS NULL;
UPDATE ar_invoices SET 
  updated_at = NOW() WHERE updated_at IS NULL;

-- ============================================================================
-- AP INVOICES ENHANCEMENTS  
-- ============================================================================

-- Add contract integration fields to ap_invoices
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS purchase_contract_id TEXT REFERENCES purchase_contracts(id);
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS contract_reference TEXT;
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS due_date TEXT;
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS paid TEXT DEFAULT '0';
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'USD';
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS payment_terms TEXT;
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS ap_invoices_contract_id_idx ON ap_invoices(purchase_contract_id);

-- Update existing records to have default values
UPDATE ap_invoices SET 
  paid = '0' WHERE paid IS NULL;
UPDATE ap_invoices SET 
  currency = 'USD' WHERE currency IS NULL;
UPDATE ap_invoices SET 
  updated_at = NOW() WHERE updated_at IS NULL;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify AR invoices structure
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'ar_invoices' 
ORDER BY ordinal_position;

-- Verify AP invoices structure  
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'ap_invoices' 
ORDER BY ordinal_position;

-- Verify indexes
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename IN ('ar_invoices', 'ap_invoices')
ORDER BY tablename, indexname;

-- ============================================================================
-- SAMPLE DATA VERIFICATION
-- ============================================================================

-- Check existing AR invoices
SELECT id, number, customer_id, sales_contract_id, amount, received, status, created_at
FROM ar_invoices 
ORDER BY created_at DESC 
LIMIT 5;

-- Check existing AP invoices
SELECT id, number, supplier_id, purchase_contract_id, amount, paid, status, created_at
FROM ap_invoices 
ORDER BY created_at DESC 
LIMIT 5;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- This migration adds:
-- 1. Contract integration fields (sales_contract_id, purchase_contract_id)
-- 2. Enhanced payment tracking (received, paid, due_date)
-- 3. Multi-currency support (currency field)
-- 4. Payment terms and notes
-- 5. Updated timestamp tracking
-- 6. Performance indexes
-- 7. Default values for existing records

SELECT 'Financial Integration Migration Completed Successfully' as status;
