#!/usr/bin/env node

/**
 * Manufacturing ERP i18n Production Deployment & Validation
 * 
 * Comprehensive production deployment with validation and monitoring.
 * Ensures zero breaking changes and maintains system stability.
 * 
 * ZERO BREAKING CHANGES: Safe deployment with complete rollback capability.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Production deployment configuration
const PRODUCTION_CONFIG = {
  // Deployment phases
  phases: [
    'pre_deployment_validation',
    'backup_creation',
    'system_health_check',
    'deployment_execution',
    'post_deployment_validation',
    'monitoring_activation',
    'rollback_verification'
  ],
  
  // Validation thresholds
  validationThresholds: {
    qualityScore: 70,        // Minimum quality score
    integrationTests: 100,   // 100% integration test pass rate
    performanceImpact: 10,   // Max 10% performance degradation
    rollbackTime: 5000,      // Max 5 seconds rollback time
    systemAvailability: 99   // Min 99% system availability
  },
  
  // Production environment checks
  environmentChecks: [
    'node_version',
    'npm_dependencies',
    'file_permissions',
    'directory_structure',
    'backup_integrity',
    'monitoring_setup'
  ],
  
  // Rollback triggers
  rollbackTriggers: [
    'quality_score_below_threshold',
    'integration_test_failures',
    'performance_degradation',
    'system_errors',
    'user_reported_issues'
  ]
};

// Production deployment directory
const DEPLOYMENT_DIR = 'i18n-production';
const VALIDATION_DIR = path.join(DEPLOYMENT_DIR, 'validation');
const BACKUP_DIR = path.join(DEPLOYMENT_DIR, 'backups');
const LOGS_DIR = path.join(DEPLOYMENT_DIR, 'logs');

// Ensure production directories exist
function ensureProductionDirectories() {
  [DEPLOYMENT_DIR, VALIDATION_DIR, BACKUP_DIR, LOGS_DIR].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// Pre-deployment validation
function runPreDeploymentValidation() {
  console.log('🔍 Running pre-deployment validation...');
  
  const validation = {
    timestamp: new Date().toISOString(),
    phase: 'pre_deployment_validation',
    checks: [],
    passed: true,
    issues: [],
    warnings: []
  };
  
  try {
    // 1. System health check
    console.log('   Checking system health...');
    try {
      const healthResult = execSync('node scripts/i18n-integration-tester.js test', { 
        encoding: 'utf8',
        timeout: 60000 
      });
      
      if (healthResult.includes('5/5 tests passed')) {
        validation.checks.push('✅ System health check passed');
      } else {
        validation.issues.push('System health check failed');
        validation.passed = false;
      }
    } catch (error) {
      validation.issues.push(`System health check error: ${error.message}`);
      validation.passed = false;
    }
    
    // 2. Quality validation
    console.log('   Validating translation quality...');
    try {
      const qualityResult = execSync('node scripts/i18n-translation-validator.js validate batch-1-2025-09-15T08-13-43-289Z.json', {
        encoding: 'utf8',
        timeout: 30000
      });
      
      const qualityMatch = qualityResult.match(/(\d+)%.*quality.*score/i);
      const qualityScore = qualityMatch ? parseInt(qualityMatch[1]) : 0;
      
      if (qualityScore >= PRODUCTION_CONFIG.validationThresholds.qualityScore) {
        validation.checks.push(`✅ Quality validation passed (${qualityScore}%)`);
      } else {
        validation.issues.push(`Quality score ${qualityScore}% below threshold ${PRODUCTION_CONFIG.validationThresholds.qualityScore}%`);
        validation.passed = false;
      }
    } catch (error) {
      validation.warnings.push('Quality validation could not be completed');
    }
    
    // 3. Performance assessment
    console.log('   Assessing performance impact...');
    try {
      const performanceResult = execSync('node scripts/i18n-performance-assessor.js assess', {
        encoding: 'utf8',
        timeout: 45000
      });
      
      if (performanceResult.includes('POSITIVE')) {
        validation.checks.push('✅ Performance impact assessment passed');
      } else {
        validation.warnings.push('Performance impact needs monitoring');
      }
    } catch (error) {
      validation.warnings.push('Performance assessment could not be completed');
    }
    
    // 4. Rollback capability verification
    console.log('   Verifying rollback capability...');
    try {
      const rollbackResult = execSync('node scripts/i18n-rollback-validator.js validate', {
        encoding: 'utf8',
        timeout: 60000
      });
      
      if (rollbackResult.includes('VERIFIED')) {
        validation.checks.push('✅ Rollback capability verified');
      } else {
        validation.issues.push('Rollback capability verification failed');
        validation.passed = false;
      }
    } catch (error) {
      validation.issues.push(`Rollback verification error: ${error.message}`);
      validation.passed = false;
    }
    
    // 5. Environment checks
    console.log('   Checking production environment...');
    
    // Node.js version check
    try {
      const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
      validation.checks.push(`✅ Node.js version: ${nodeVersion}`);
    } catch (error) {
      validation.issues.push('Node.js version check failed');
      validation.passed = false;
    }
    
    // NPM dependencies check
    try {
      execSync('npm list --depth=0', { encoding: 'utf8', stdio: 'pipe' });
      validation.checks.push('✅ NPM dependencies verified');
    } catch (error) {
      validation.warnings.push('Some NPM dependencies may have issues');
    }
    
    // File structure check
    const requiredFiles = [
      'components/i18n-provider.tsx',
      'scripts/i18n-ai-processor.js',
      'scripts/i18n-sync-mechanism.js',
      'scripts/i18n-rollback-validator.js'
    ];
    
    const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
    if (missingFiles.length === 0) {
      validation.checks.push('✅ Required files present');
    } else {
      validation.issues.push(`Missing required files: ${missingFiles.join(', ')}`);
      validation.passed = false;
    }
    
    // Directory structure check
    const requiredDirs = [
      'i18n-parallel',
      'i18n-backup',
      'i18n-temp',
      'scripts'
    ];
    
    const missingDirs = requiredDirs.filter(dir => !fs.existsSync(dir));
    if (missingDirs.length === 0) {
      validation.checks.push('✅ Directory structure verified');
    } else {
      validation.issues.push(`Missing required directories: ${missingDirs.join(', ')}`);
      validation.passed = false;
    }
    
  } catch (error) {
    validation.issues.push(`Pre-deployment validation failed: ${error.message}`);
    validation.passed = false;
  }
  
  return validation;
}

// Create production backup
function createProductionBackup() {
  console.log('💾 Creating production backup...');
  
  const backup = {
    timestamp: new Date().toISOString(),
    phase: 'backup_creation',
    backupId: `production-backup-${Date.now()}`,
    files: [],
    success: true,
    error: null
  };
  
  try {
    const backupDir = path.join(BACKUP_DIR, backup.backupId);
    fs.mkdirSync(backupDir, { recursive: true });
    
    // Critical files to backup
    const criticalFiles = [
      'components/i18n-provider.tsx',
      'components/language-switcher.tsx',
      'package.json',
      'package-lock.json'
    ];
    
    criticalFiles.forEach(file => {
      if (fs.existsSync(file)) {
        const backupFile = path.join(backupDir, file);
        const backupFileDir = path.dirname(backupFile);
        
        if (!fs.existsSync(backupFileDir)) {
          fs.mkdirSync(backupFileDir, { recursive: true });
        }
        
        fs.copyFileSync(file, backupFile);
        backup.files.push(file);
      }
    });
    
    // Create backup metadata
    const metadata = {
      backupId: backup.backupId,
      timestamp: backup.timestamp,
      files: backup.files,
      purpose: 'production_deployment',
      systemState: 'pre_deployment'
    };
    
    const metadataFile = path.join(backupDir, 'backup-metadata.json');
    fs.writeFileSync(metadataFile, JSON.stringify(metadata, null, 2));
    
    // Create restore script
    const restoreScript = `#!/bin/bash
# Production Restore Script - ${backup.backupId}
echo "🔄 Restoring production backup..."

${backup.files.map(file => `cp "${path.join(backupDir, file)}" "${file}"`).join('\n')}

echo "✅ Production backup restored successfully"
`;
    
    const restoreFile = path.join(backupDir, 'restore.sh');
    fs.writeFileSync(restoreFile, restoreScript);
    fs.chmodSync(restoreFile, '755');
    
    backup.backupPath = backupDir;
    backup.restoreScript = restoreFile;
    
  } catch (error) {
    backup.success = false;
    backup.error = error.message;
  }
  
  return backup;
}

// Execute production deployment
function executeProductionDeployment() {
  console.log('🚀 Executing production deployment...');
  
  const deployment = {
    timestamp: new Date().toISOString(),
    phase: 'deployment_execution',
    steps: [],
    success: true,
    error: null
  };
  
  try {
    // Step 1: Activate monitoring
    console.log('   Activating monitoring systems...');
    try {
      execSync('node scripts/i18n-monitoring-dashboard.js run', { 
        encoding: 'utf8',
        timeout: 30000,
        stdio: 'pipe'
      });
      deployment.steps.push('✅ Monitoring systems activated');
    } catch (error) {
      deployment.steps.push('⚠️  Monitoring activation had issues (non-blocking)');
    }
    
    // Step 2: Initialize CI/CD integration
    console.log('   Initializing CI/CD integration...');
    try {
      execSync('node scripts/i18n-cicd-integration.js setup', {
        encoding: 'utf8',
        timeout: 15000,
        stdio: 'pipe'
      });
      deployment.steps.push('✅ CI/CD integration initialized');
    } catch (error) {
      deployment.steps.push('⚠️  CI/CD integration setup had issues (non-blocking)');
    }
    
    // Step 3: Validate workflow systems
    console.log('   Validating workflow systems...');
    const workflowDirs = [
      'i18n-parallel/pending',
      'i18n-parallel/approved',
      'i18n-parallel/integrated',
      'i18n-parallel/csv'
    ];
    
    workflowDirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
    deployment.steps.push('✅ Workflow systems validated');
    
    // Step 4: Initialize optimization engine
    console.log('   Initializing optimization engine...');
    try {
      execSync('node scripts/i18n-optimization-engine.js analyze', {
        encoding: 'utf8',
        timeout: 30000,
        stdio: 'pipe'
      });
      deployment.steps.push('✅ Optimization engine initialized');
    } catch (error) {
      deployment.steps.push('⚠️  Optimization engine initialization had issues (non-blocking)');
    }
    
    // Step 5: Create deployment marker
    const deploymentMarker = {
      deploymentId: `deployment-${Date.now()}`,
      timestamp: deployment.timestamp,
      version: '1.0.0',
      status: 'deployed',
      features: [
        'AI-powered translation acceleration',
        'CSV collaboration workflow',
        'Quality validation system',
        'Performance monitoring',
        'Rollback capability',
        'CI/CD integration',
        'Optimization engine'
      ]
    };
    
    const markerFile = path.join(DEPLOYMENT_DIR, 'deployment-marker.json');
    fs.writeFileSync(markerFile, JSON.stringify(deploymentMarker, null, 2));
    deployment.steps.push('✅ Deployment marker created');
    
  } catch (error) {
    deployment.success = false;
    deployment.error = error.message;
  }
  
  return deployment;
}

// Post-deployment validation
function runPostDeploymentValidation() {
  console.log('✅ Running post-deployment validation...');
  
  const validation = {
    timestamp: new Date().toISOString(),
    phase: 'post_deployment_validation',
    checks: [],
    passed: true,
    issues: [],
    warnings: []
  };
  
  try {
    // 1. System functionality check
    console.log('   Checking system functionality...');
    if (fs.existsSync('components/i18n-provider.tsx')) {
      const i18nContent = fs.readFileSync('components/i18n-provider.tsx', 'utf8');
      if (i18nContent.includes('const en:') && i18nContent.includes('const zh:')) {
        validation.checks.push('✅ i18n system functional');
      } else {
        validation.issues.push('i18n system structure appears invalid');
        validation.passed = false;
      }
    } else {
      validation.issues.push('i18n provider file missing');
      validation.passed = false;
    }
    
    // 2. Workflow systems check
    console.log('   Checking workflow systems...');
    const workflowDirs = ['i18n-parallel', 'i18n-backup', 'i18n-temp'];
    const missingWorkflowDirs = workflowDirs.filter(dir => !fs.existsSync(dir));
    
    if (missingWorkflowDirs.length === 0) {
      validation.checks.push('✅ Workflow systems operational');
    } else {
      validation.issues.push(`Missing workflow directories: ${missingWorkflowDirs.join(', ')}`);
      validation.passed = false;
    }
    
    // 3. Script availability check
    console.log('   Checking script availability...');
    const requiredScripts = [
      'scripts/i18n-ai-processor.js',
      'scripts/i18n-csv-workflow.js',
      'scripts/i18n-sync-mechanism.js',
      'scripts/i18n-monitoring-dashboard.js'
    ];
    
    const missingScripts = requiredScripts.filter(script => !fs.existsSync(script));
    if (missingScripts.length === 0) {
      validation.checks.push('✅ All scripts available');
    } else {
      validation.issues.push(`Missing scripts: ${missingScripts.join(', ')}`);
      validation.passed = false;
    }
    
    // 4. Monitoring system check
    console.log('   Checking monitoring system...');
    if (fs.existsSync('i18n-monitoring')) {
      validation.checks.push('✅ Monitoring system deployed');
    } else {
      validation.warnings.push('Monitoring system not fully initialized');
    }
    
    // 5. CI/CD integration check
    console.log('   Checking CI/CD integration...');
    if (fs.existsSync('.github/workflows/i18n-quality-check.yml')) {
      validation.checks.push('✅ GitHub Actions integration deployed');
    } else {
      validation.warnings.push('GitHub Actions integration not deployed');
    }
    
    // 6. Deployment marker check
    console.log('   Checking deployment marker...');
    const markerFile = path.join(DEPLOYMENT_DIR, 'deployment-marker.json');
    if (fs.existsSync(markerFile)) {
      const marker = JSON.parse(fs.readFileSync(markerFile, 'utf8'));
      if (marker.status === 'deployed') {
        validation.checks.push('✅ Deployment marker verified');
      } else {
        validation.issues.push('Deployment marker indicates incomplete deployment');
        validation.passed = false;
      }
    } else {
      validation.issues.push('Deployment marker missing');
      validation.passed = false;
    }
    
  } catch (error) {
    validation.issues.push(`Post-deployment validation failed: ${error.message}`);
    validation.passed = false;
  }
  
  return validation;
}

// Generate production deployment report
function generateDeploymentReport(preValidation, backup, deployment, postValidation) {
  const report = {
    timestamp: new Date().toISOString(),
    deploymentId: `production-deployment-${Date.now()}`,
    status: 'unknown',
    phases: {
      preValidation,
      backup,
      deployment,
      postValidation
    },
    summary: {
      overallSuccess: false,
      criticalIssues: [],
      warnings: [],
      successfulSteps: 0,
      totalSteps: 0
    },
    recommendations: [],
    nextSteps: []
  };
  
  // Calculate overall success
  const phaseResults = [
    preValidation.passed,
    backup.success,
    deployment.success,
    postValidation.passed
  ];
  
  report.summary.overallSuccess = phaseResults.every(result => result === true);
  report.status = report.summary.overallSuccess ? 'success' : 'partial_success';
  
  // Collect issues and warnings
  [preValidation, postValidation].forEach(validation => {
    report.summary.criticalIssues.push(...validation.issues);
    report.summary.warnings.push(...validation.warnings);
  });
  
  if (!backup.success) {
    report.summary.criticalIssues.push(`Backup creation failed: ${backup.error}`);
  }
  
  if (!deployment.success) {
    report.summary.criticalIssues.push(`Deployment execution failed: ${deployment.error}`);
  }
  
  // Count successful steps
  report.summary.successfulSteps = [
    preValidation.checks.length,
    backup.success ? 1 : 0,
    deployment.steps.length,
    postValidation.checks.length
  ].reduce((sum, count) => sum + count, 0);
  
  report.summary.totalSteps = report.summary.successfulSteps + report.summary.criticalIssues.length;
  
  // Generate recommendations
  if (report.summary.overallSuccess) {
    report.recommendations = [
      'Deployment completed successfully - monitor system performance',
      'Run regular quality checks using monitoring dashboard',
      'Train team on new workflow capabilities',
      'Schedule optimization reviews based on usage patterns'
    ];
    
    report.nextSteps = [
      'Monitor system performance for 24-48 hours',
      'Conduct team training on new features',
      'Set up regular monitoring and optimization schedules',
      'Document any production-specific configurations'
    ];
  } else {
    report.recommendations = [
      'Address critical issues before full production use',
      'Consider rollback if issues cannot be resolved quickly',
      'Review deployment process for improvements',
      'Ensure all validation steps pass before retry'
    ];
    
    report.nextSteps = [
      'Resolve critical issues identified in validation',
      'Re-run deployment validation',
      'Consider partial rollback if necessary',
      'Update deployment procedures based on lessons learned'
    ];
  }
  
  return report;
}

// Display deployment report
function displayDeploymentReport(report) {
  console.log('\n🚀 PRODUCTION DEPLOYMENT REPORT');
  console.log('='.repeat(60));
  console.log(`Deployment ID: ${report.deploymentId}`);
  console.log(`Timestamp: ${new Date().toLocaleString()}`);
  console.log(`Status: ${report.status.toUpperCase()}`);
  console.log(`Overall Success: ${report.summary.overallSuccess ? '✅ YES' : '❌ NO'}`);
  
  // Phase Results
  console.log('\n📊 DEPLOYMENT PHASES');
  console.log('-'.repeat(30));
  console.log(`Pre-validation: ${report.phases.preValidation.passed ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Backup Creation: ${report.phases.backup.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`Deployment Execution: ${report.phases.deployment.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`Post-validation: ${report.phases.postValidation.passed ? '✅ PASSED' : '❌ FAILED'}`);
  
  // Success Summary
  console.log('\n📈 SUCCESS SUMMARY');
  console.log('-'.repeat(30));
  console.log(`Successful Steps: ${report.summary.successfulSteps}`);
  console.log(`Total Steps: ${report.summary.totalSteps}`);
  console.log(`Success Rate: ${Math.round((report.summary.successfulSteps / report.summary.totalSteps) * 100)}%`);
  
  // Critical Issues
  if (report.summary.criticalIssues.length > 0) {
    console.log('\n🚨 CRITICAL ISSUES');
    console.log('-'.repeat(30));
    report.summary.criticalIssues.forEach(issue => console.log(`❌ ${issue}`));
  }
  
  // Warnings
  if (report.summary.warnings.length > 0) {
    console.log('\n⚠️  WARNINGS');
    console.log('-'.repeat(30));
    report.summary.warnings.forEach(warning => console.log(`⚠️  ${warning}`));
  }
  
  // Recommendations
  console.log('\n🎯 RECOMMENDATIONS');
  console.log('-'.repeat(30));
  report.recommendations.forEach(rec => console.log(`• ${rec}`));
  
  // Next Steps
  console.log('\n📋 NEXT STEPS');
  console.log('-'.repeat(30));
  report.nextSteps.forEach(step => console.log(`• ${step}`));
  
  if (report.summary.overallSuccess) {
    console.log('\n🎉 DEPLOYMENT SUCCESSFUL!');
    console.log('Your Manufacturing ERP i18n acceleration system is now live in production.');
  } else {
    console.log('\n⚠️  DEPLOYMENT PARTIALLY SUCCESSFUL');
    console.log('Please address the identified issues before full production use.');
  }
}

// Main production deployment function
async function runProductionDeployment() {
  console.log('🚀 Manufacturing ERP i18n Production Deployment\n');
  console.log('⚠️  ZERO BREAKING CHANGES: Safe deployment with complete rollback capability\n');
  
  try {
    // Setup
    ensureProductionDirectories();
    
    // Phase 1: Pre-deployment validation
    const preValidation = runPreDeploymentValidation();
    
    if (!preValidation.passed) {
      console.log('\n❌ Pre-deployment validation failed. Deployment aborted.');
      console.log('Issues found:');
      preValidation.issues.forEach(issue => console.log(`  • ${issue}`));
      return { success: false, phase: 'pre_validation', issues: preValidation.issues };
    }
    
    // Phase 2: Backup creation
    const backup = createProductionBackup();
    
    if (!backup.success) {
      console.log('\n❌ Backup creation failed. Deployment aborted.');
      console.log(`Error: ${backup.error}`);
      return { success: false, phase: 'backup', error: backup.error };
    }
    
    // Phase 3: Deployment execution
    const deployment = executeProductionDeployment();
    
    // Phase 4: Post-deployment validation
    const postValidation = runPostDeploymentValidation();
    
    // Generate comprehensive report
    const report = generateDeploymentReport(preValidation, backup, deployment, postValidation);
    
    // Display report
    displayDeploymentReport(report);
    
    // Save report
    const reportFile = path.join(DEPLOYMENT_DIR, `deployment-report-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    const latestReportFile = path.join(DEPLOYMENT_DIR, 'latest-deployment.json');
    fs.writeFileSync(latestReportFile, JSON.stringify(report, null, 2));
    
    console.log(`\n📁 Deployment report saved: ${reportFile}`);
    
    return {
      success: report.summary.overallSuccess,
      report,
      reportFile
    };
    
  } catch (error) {
    console.error('❌ Production deployment failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'deploy':
      runProductionDeployment();
      break;
      
    case 'validate':
      ensureProductionDirectories();
      const validation = runPreDeploymentValidation();
      console.log('🔍 Pre-deployment Validation Results:');
      console.log(`Passed: ${validation.passed}`);
      console.log(`Checks: ${validation.checks.length}`);
      console.log(`Issues: ${validation.issues.length}`);
      console.log(`Warnings: ${validation.warnings.length}`);
      break;
      
    case 'backup':
      ensureProductionDirectories();
      const backup = createProductionBackup();
      console.log('💾 Production Backup Results:');
      console.log(`Success: ${backup.success}`);
      console.log(`Files: ${backup.files ? backup.files.length : 0}`);
      if (backup.backupPath) {
        console.log(`Backup Path: ${backup.backupPath}`);
      }
      break;
      
    case 'status':
      const markerFile = path.join(DEPLOYMENT_DIR, 'deployment-marker.json');
      if (fs.existsSync(markerFile)) {
        const marker = JSON.parse(fs.readFileSync(markerFile, 'utf8'));
        console.log('📊 Production Deployment Status:');
        console.log(JSON.stringify(marker, null, 2));
      } else {
        console.log('No production deployment found.');
      }
      break;
      
    default:
      console.log('📖 Manufacturing ERP i18n Production Deployment');
      console.log('');
      console.log('Commands:');
      console.log('  deploy   - Run complete production deployment');
      console.log('  validate - Run pre-deployment validation only');
      console.log('  backup   - Create production backup only');
      console.log('  status   - Check current deployment status');
      console.log('');
      console.log('Features:');
      console.log('  - Comprehensive pre-deployment validation');
      console.log('  - Automatic production backup creation');
      console.log('  - Safe deployment with rollback capability');
      console.log('  - Post-deployment validation and monitoring');
      console.log('  - Detailed deployment reporting');
  }
}

module.exports = { runProductionDeployment };
