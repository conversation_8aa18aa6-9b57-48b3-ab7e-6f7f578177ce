#!/usr/bin/env node

/**
 * Manufacturing ERP Automated String Detection
 * 
 * Automatically detects new hardcoded strings in future development
 * without breaking existing code. Designed for CI/CD integration.
 * 
 * ZERO BREAKING CHANGES: Detection only, no code modifications.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const SCAN_PATTERNS = [
  'app/**/*.tsx',
  'app/**/*.ts',
  'components/**/*.tsx',
  'components/**/*.ts'
];

const IGNORE_PATTERNS = [
  'node_modules/**',
  '.next/**',
  'dist/**',
  'build/**',
  'components/i18n-provider.tsx',
  'i18n-parallel/**',
  'i18n-temp/**',
  'i18n-backup/**',
  'scripts/**',
  '**/*.d.ts'
];

// Detection patterns for different types of hardcoded strings
const DETECTION_PATTERNS = [
  {
    name: 'Form Labels',
    regex: /<(?:Label|FormLabel)[^>]*>([^<{]+)<\/(?:Label|FormLabel)>/g,
    priority: 'high',
    category: 'forms'
  },
  {
    name: 'Placeholders',
    regex: /placeholder="([^"]+)"/g,
    priority: 'high',
    category: 'forms'
  },
  {
    name: 'Button Text',
    regex: /<Button[^>]*>([^<{]+)<\/Button>/g,
    priority: 'high',
    category: 'actions'
  },
  {
    name: 'Table Headers',
    regex: /<TableHead[^>]*>([^<{]+)<\/TableHead>/g,
    priority: 'medium',
    category: 'tables'
  },
  {
    name: 'Card Titles',
    regex: /<CardTitle[^>]*>([^<{]+)<\/CardTitle>/g,
    priority: 'medium',
    category: 'content'
  },
  {
    name: 'Select Options',
    regex: /<SelectItem[^>]*value="[^"]*">([^<{]+)<\/SelectItem>/g,
    priority: 'medium',
    category: 'options'
  },
  {
    name: 'Toast Messages',
    regex: /toast\.(?:error|success|warning|info)\("([^"]+)"/g,
    priority: 'low',
    category: 'messages'
  },
  {
    name: 'Validation Messages',
    regex: /message:\s*"([^"]+)"/g,
    priority: 'low',
    category: 'validation'
  }
];

// Check if string should be flagged as hardcoded
function isHardcodedString(text) {
  // Skip if empty or too short
  if (!text || text.length < 2) return false;
  
  // Skip if it's a variable or expression
  if (text.includes('{') || text.includes('$') || text.includes('`')) return false;
  
  // Skip if it's already using t() function
  if (text.includes('t(')) return false;
  
  // Skip if it's a technical identifier
  if (/^[a-z_]+$/.test(text) || /^[A-Z_]+$/.test(text)) return false;
  
  // Skip if it's a number or date
  if (/^\d+$/.test(text) || /^\d{4}-\d{2}-\d{2}/.test(text)) return false;
  
  // Skip if it's a URL or path
  if (text.startsWith('/') || text.startsWith('http') || text.includes('://')) return false;
  
  // Skip common technical terms
  const technicalTerms = ['id', 'uuid', 'api', 'url', 'json', 'xml', 'css', 'html', 'js', 'ts', 'tsx'];
  if (technicalTerms.includes(text.toLowerCase())) return false;
  
  return true;
}

// Get list of files to scan
function getFilesToScan() {
  try {
    const glob = require('glob');
    const allFiles = [];
    
    for (const pattern of SCAN_PATTERNS) {
      const files = glob.sync(pattern, { ignore: IGNORE_PATTERNS });
      allFiles.push(...files);
    }
    
    return [...new Set(allFiles)]; // Remove duplicates
  } catch (error) {
    console.error('❌ Failed to get files to scan:', error.message);
    return [];
  }
}

// Scan a single file for hardcoded strings
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const detections = [];
    
    // Check if file has i18n import
    const hasI18nImport = content.includes('useI18n') || content.includes('from "@/components/i18n-provider"');
    
    // Scan for each pattern
    DETECTION_PATTERNS.forEach(pattern => {
      let match;
      while ((match = pattern.regex.exec(content)) !== null) {
        const text = match[1];
        
        if (isHardcodedString(text)) {
          const lineNumber = content.substring(0, match.index).split('\n').length;
          
          detections.push({
            type: pattern.name,
            text: text,
            line: lineNumber,
            priority: pattern.priority,
            category: pattern.category,
            context: match[0],
            hasI18nImport: hasI18nImport
          });
        }
      }
    });
    
    return {
      file: filePath,
      hasI18nImport,
      detections,
      scannedAt: new Date().toISOString()
    };
    
  } catch (error) {
    console.warn(`⚠️  Could not scan ${filePath}:`, error.message);
    return null;
  }
}

// Compare with previous scan results
function compareWithPrevious(currentResults) {
  const previousFile = 'i18n-temp/last-detection-results.json';
  
  if (!fs.existsSync(previousFile)) {
    console.log('📝 No previous scan results found - this is the first scan');
    return {
      newStrings: currentResults.flatMap(r => r.detections),
      removedStrings: [],
      isFirstScan: true
    };
  }
  
  try {
    const previousResults = JSON.parse(fs.readFileSync(previousFile, 'utf8'));
    const previousStrings = new Set();
    const currentStrings = new Set();
    
    // Build sets of string identifiers
    previousResults.forEach(result => {
      result.detections.forEach(detection => {
        previousStrings.add(`${result.file}:${detection.line}:${detection.text}`);
      });
    });
    
    currentResults.forEach(result => {
      result.detections.forEach(detection => {
        currentStrings.add(`${result.file}:${detection.line}:${detection.text}`);
      });
    });
    
    // Find new and removed strings
    const newStrings = [];
    const removedStrings = [];
    
    currentResults.forEach(result => {
      result.detections.forEach(detection => {
        const identifier = `${result.file}:${detection.line}:${detection.text}`;
        if (!previousStrings.has(identifier)) {
          newStrings.push({
            ...detection,
            file: result.file
          });
        }
      });
    });
    
    previousResults.forEach(result => {
      result.detections.forEach(detection => {
        const identifier = `${result.file}:${detection.line}:${detection.text}`;
        if (!currentStrings.has(identifier)) {
          removedStrings.push({
            ...detection,
            file: result.file
          });
        }
      });
    });
    
    return {
      newStrings,
      removedStrings,
      isFirstScan: false
    };
    
  } catch (error) {
    console.warn('⚠️  Could not compare with previous results:', error.message);
    return {
      newStrings: currentResults.flatMap(r => r.detections),
      removedStrings: [],
      isFirstScan: true
    };
  }
}

// Save current scan results
function saveResults(results) {
  try {
    const outputFile = 'i18n-temp/last-detection-results.json';
    fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));
    
    const summaryFile = 'i18n-temp/detection-summary.json';
    const summary = {
      scanDate: new Date().toISOString(),
      totalFiles: results.length,
      totalDetections: results.reduce((sum, r) => sum + r.detections.length, 0),
      byPriority: {
        high: results.reduce((sum, r) => sum + r.detections.filter(d => d.priority === 'high').length, 0),
        medium: results.reduce((sum, r) => sum + r.detections.filter(d => d.priority === 'medium').length, 0),
        low: results.reduce((sum, r) => sum + r.detections.filter(d => d.priority === 'low').length, 0)
      }
    };
    
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
    
    console.log(`✅ Saved scan results: ${outputFile}`);
    console.log(`✅ Saved summary: ${summaryFile}`);
    
    return { outputFile, summaryFile };
  } catch (error) {
    console.error('❌ Failed to save results:', error.message);
    return null;
  }
}

// Generate report for CI/CD
function generateCIReport(comparison, results) {
  const report = {
    status: comparison.newStrings.length === 0 ? 'PASS' : 'ATTENTION_NEEDED',
    summary: {
      newHardcodedStrings: comparison.newStrings.length,
      removedStrings: comparison.removedStrings.length,
      totalDetections: results.reduce((sum, r) => sum + r.detections.length, 0),
      filesScanned: results.length
    },
    newStrings: comparison.newStrings.slice(0, 10), // Limit for CI output
    recommendations: []
  };
  
  if (comparison.newStrings.length > 0) {
    report.recommendations.push('Review new hardcoded strings and add to i18n system');
    report.recommendations.push('Consider using t() function for user-facing text');
    report.recommendations.push('Run i18n-ai-processor.js to generate translations');
  }
  
  if (comparison.newStrings.length === 0 && !comparison.isFirstScan) {
    report.recommendations.push('Great! No new hardcoded strings detected');
  }
  
  return report;
}

// Main detection function
async function runAutomatedDetection() {
  console.log('🔍 Running Automated Hardcoded String Detection...\n');
  console.log('⚠️  ZERO BREAKING CHANGES: Detection only, no code modifications\n');
  
  // Get files to scan
  const files = getFilesToScan();
  if (files.length === 0) {
    console.error('❌ No files found to scan');
    process.exit(1);
  }
  
  console.log(`📁 Scanning ${files.length} files...`);
  
  // Scan all files
  const results = [];
  let scannedCount = 0;
  
  for (const file of files) {
    const result = scanFile(file);
    if (result && result.detections.length > 0) {
      results.push(result);
    }
    scannedCount++;
    
    if (scannedCount % 50 === 0) {
      console.log(`   Scanned ${scannedCount}/${files.length} files...`);
    }
  }
  
  console.log(`✅ Completed scanning ${scannedCount} files`);
  
  // Compare with previous results
  console.log('\n🔄 Comparing with previous scan...');
  const comparison = compareWithPrevious(results);
  
  // Save current results
  console.log('\n💾 Saving scan results...');
  const savedFiles = saveResults(results);
  
  // Generate CI report
  const ciReport = generateCIReport(comparison, results);
  
  // Output results
  console.log('\n📊 DETECTION RESULTS');
  console.log('='.repeat(50));
  console.log(`Files scanned: ${scannedCount}`);
  console.log(`Files with detections: ${results.length}`);
  console.log(`Total detections: ${results.reduce((sum, r) => sum + r.detections.length, 0)}`);
  
  if (!comparison.isFirstScan) {
    console.log(`New hardcoded strings: ${comparison.newStrings.length}`);
    console.log(`Removed strings: ${comparison.removedStrings.length}`);
  }
  
  // Show new strings if any
  if (comparison.newStrings.length > 0) {
    console.log('\n🆕 NEW HARDCODED STRINGS DETECTED:');
    comparison.newStrings.slice(0, 5).forEach((detection, index) => {
      console.log(`   ${index + 1}. ${detection.file}:${detection.line}`);
      console.log(`      Text: "${detection.text}"`);
      console.log(`      Type: ${detection.type} (${detection.priority} priority)`);
    });
    
    if (comparison.newStrings.length > 5) {
      console.log(`   ... and ${comparison.newStrings.length - 5} more`);
    }
  }
  
  // CI/CD status
  console.log(`\n🎯 CI/CD STATUS: ${ciReport.status}`);
  if (ciReport.recommendations.length > 0) {
    console.log('📋 Recommendations:');
    ciReport.recommendations.forEach(rec => console.log(`   - ${rec}`));
  }
  
  // Save CI report
  if (savedFiles) {
    const ciReportFile = 'i18n-temp/ci-detection-report.json';
    fs.writeFileSync(ciReportFile, JSON.stringify(ciReport, null, 2));
    console.log(`✅ CI report saved: ${ciReportFile}`);
  }
  
  return ciReport;
}

// Run detection if called directly
if (require.main === module) {
  runAutomatedDetection().catch(console.error);
}

module.exports = { runAutomatedDetection };
