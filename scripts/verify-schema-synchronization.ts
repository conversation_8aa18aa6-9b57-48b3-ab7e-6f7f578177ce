/**
 * Manufacturing ERP - Database Schema Synchronization Verification
 * 
 * Compares local PostgreSQL schema with production Supabase schema
 * Ensures zero production downtime during deployment
 */

import { drizzle } from "drizzle-orm/postgres-js"
import postgres from "postgres"
import { sql } from "drizzle-orm"

interface TableSchema {
  table_name: string
  column_name: string
  data_type: string
  is_nullable: string
  column_default: string | null
  character_maximum_length: number | null
}

interface IndexSchema {
  table_name: string
  index_name: string
  column_name: string
  is_unique: boolean
}

interface ForeignKeySchema {
  table_name: string
  column_name: string
  foreign_table_name: string
  foreign_column_name: string
  constraint_name: string
}

interface SchemaComparison {
  localTables: string[]
  productionTables: string[]
  missingInProduction: string[]
  missingInLocal: string[]
  columnDifferences: {
    table: string
    differences: {
      missingInProduction: string[]
      missingInLocal: string[]
      typeMismatches: Array<{
        column: string
        localType: string
        productionType: string
      }>
    }
  }[]
  indexDifferences: {
    missingInProduction: IndexSchema[]
    missingInLocal: IndexSchema[]
  }
  foreignKeyDifferences: {
    missingInProduction: ForeignKeySchema[]
    missingInLocal: ForeignKeySchema[]
  }
}

export class DatabaseSchemaSynchronizer {
  private localDb: ReturnType<typeof drizzle>
  private productionDb: ReturnType<typeof drizzle>

  constructor() {
    // Local PostgreSQL connection
    const localConnection = postgres(
      process.env.DATABASE_URL || "postgresql://localhost:5432/manufacturing_erp",
      { max: 1 }
    )
    this.localDb = drizzle(localConnection)

    // Production Supabase connection
    const productionConnection = postgres(
      process.env.DATABASE_URL_POSTGRESQL ||
      "postgresql://postgres.jwdryepnhrigricdxenc:<EMAIL>:6543/postgres",
      { max: 1 }
    )
    this.productionDb = drizzle(productionConnection)
  }

  async verifySchemaSync(): Promise<SchemaComparison> {
    console.log('🔍 Starting Comprehensive Database Schema Synchronization Verification...')
    console.log('='.repeat(80))

    try {
      // 1. Compare table structures
      const localTables = await this.getTableList(this.localDb, 'LOCAL')
      const productionTables = await this.getTableList(this.productionDb, 'PRODUCTION')

      // 2. Compare column structures
      const columnComparisons = await this.compareColumns(localTables, productionTables)

      // 3. Compare indexes
      const indexComparisons = await this.compareIndexes()

      // 4. Compare foreign keys
      const foreignKeyComparisons = await this.compareForeignKeys()

      const comparison: SchemaComparison = {
        localTables: localTables.map(t => t.table_name),
        productionTables: productionTables.map(t => t.table_name),
        missingInProduction: localTables
          .filter(lt => !productionTables.find(pt => pt.table_name === lt.table_name))
          .map(t => t.table_name),
        missingInLocal: productionTables
          .filter(pt => !localTables.find(lt => lt.table_name === pt.table_name))
          .map(t => t.table_name),
        columnDifferences: columnComparisons,
        indexDifferences: indexComparisons,
        foreignKeyDifferences: foreignKeyComparisons
      }

      this.printComparisonReport(comparison)
      await this.generateMigrationScripts(comparison)

      return comparison

    } catch (error) {
      console.error('❌ Schema synchronization verification failed:', error)
      throw error
    }
  }

  private async getTableList(db: any, environment: string): Promise<Array<{ table_name: string }>> {
    console.log(`\n📊 Fetching ${environment} table list...`)

    const tables = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        AND table_name NOT LIKE '%_pkey'
        AND table_name NOT LIKE 'drizzle%'
      ORDER BY table_name
    `)

    console.log(`   Found ${tables.length} tables in ${environment}`)
    tables.forEach((table: any) => {
      console.log(`   - ${table.table_name}`)
    })

    return tables
  }

  private async getTableColumns(db: any, tableName: string): Promise<TableSchema[]> {
    const columns = await db.execute(sql`
      SELECT 
        table_name,
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
        AND table_name = ${tableName}
      ORDER BY ordinal_position
    `)

    return columns
  }

  private async compareColumns(localTables: Array<{ table_name: string }>, productionTables: Array<{ table_name: string }>) {
    console.log('\n🔍 Comparing column structures...')

    const columnDifferences = []

    // Compare columns for tables that exist in both environments
    const commonTables = localTables.filter(lt =>
      productionTables.find(pt => pt.table_name === lt.table_name)
    )

    for (const table of commonTables) {
      const localColumns = await this.getTableColumns(this.localDb, table.table_name)
      const productionColumns = await this.getTableColumns(this.productionDb, table.table_name)

      const missingInProduction = localColumns
        .filter(lc => !productionColumns.find(pc => pc.column_name === lc.column_name))
        .map(c => c.column_name)

      const missingInLocal = productionColumns
        .filter(pc => !localColumns.find(lc => lc.column_name === pc.column_name))
        .map(c => c.column_name)

      const typeMismatches = localColumns
        .filter(lc => {
          const pc = productionColumns.find(pc => pc.column_name === lc.column_name)
          return pc && pc.data_type !== lc.data_type
        })
        .map(lc => {
          const pc = productionColumns.find(pc => pc.column_name === lc.column_name)!
          return {
            column: lc.column_name,
            localType: lc.data_type,
            productionType: pc.data_type
          }
        })

      if (missingInProduction.length > 0 || missingInLocal.length > 0 || typeMismatches.length > 0) {
        columnDifferences.push({
          table: table.table_name,
          differences: {
            missingInProduction,
            missingInLocal,
            typeMismatches
          }
        })
      }

      console.log(`   ✅ ${table.table_name}: ${localColumns.length} local, ${productionColumns.length} production`)
    }

    return columnDifferences
  }

  private async compareIndexes() {
    console.log('\n🔍 Comparing index structures...')

    // Get indexes from both environments
    const localIndexes = await this.getIndexes(this.localDb, 'LOCAL')
    const productionIndexes = await this.getIndexes(this.productionDb, 'PRODUCTION')

    const missingInProduction = localIndexes.filter(li =>
      !productionIndexes.find(pi =>
        pi.table_name === li.table_name &&
        pi.index_name === li.index_name
      )
    )

    const missingInLocal = productionIndexes.filter(pi =>
      !localIndexes.find(li =>
        li.table_name === pi.table_name &&
        li.index_name === pi.index_name
      )
    )

    return {
      missingInProduction,
      missingInLocal
    }
  }

  private async getIndexes(db: any, environment: string): Promise<IndexSchema[]> {
    const indexes = await db.execute(sql`
      SELECT 
        t.relname as table_name,
        i.relname as index_name,
        a.attname as column_name,
        ix.indisunique as is_unique
      FROM pg_class t
      JOIN pg_index ix ON t.oid = ix.indrelid
      JOIN pg_class i ON i.oid = ix.indexrelid
      JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
      WHERE t.relkind = 'r'
        AND t.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
        AND i.relname NOT LIKE '%_pkey'
      ORDER BY t.relname, i.relname
    `)

    console.log(`   Found ${indexes.length} indexes in ${environment}`)
    return indexes
  }

  private async compareForeignKeys() {
    console.log('\n🔍 Comparing foreign key constraints...')

    const localForeignKeys = await this.getForeignKeys(this.localDb, 'LOCAL')
    const productionForeignKeys = await this.getForeignKeys(this.productionDb, 'PRODUCTION')

    const missingInProduction = localForeignKeys.filter(lfk =>
      !productionForeignKeys.find(pfk =>
        pfk.table_name === lfk.table_name &&
        pfk.column_name === lfk.column_name &&
        pfk.foreign_table_name === lfk.foreign_table_name
      )
    )

    const missingInLocal = productionForeignKeys.filter(pfk =>
      !localForeignKeys.find(lfk =>
        lfk.table_name === pfk.table_name &&
        lfk.column_name === pfk.column_name &&
        lfk.foreign_table_name === pfk.foreign_table_name
      )
    )

    return {
      missingInProduction,
      missingInLocal
    }
  }

  private async getForeignKeys(db: any, environment: string): Promise<ForeignKeySchema[]> {
    const foreignKeys = await db.execute(sql`
      SELECT 
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name,
        tc.constraint_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
      ORDER BY tc.table_name, kcu.column_name
    `)

    console.log(`   Found ${foreignKeys.length} foreign keys in ${environment}`)
    return foreignKeys
  }

  private printComparisonReport(comparison: SchemaComparison): void {
    console.log('\n' + '='.repeat(80))
    console.log('📋 DATABASE SCHEMA SYNCHRONIZATION REPORT')
    console.log('='.repeat(80))

    // Table comparison
    console.log(`\n📊 TABLE COMPARISON:`)
    console.log(`   Local Tables: ${comparison.localTables.length}`)
    console.log(`   Production Tables: ${comparison.productionTables.length}`)

    if (comparison.missingInProduction.length > 0) {
      console.log(`\n❌ MISSING IN PRODUCTION (${comparison.missingInProduction.length}):`)
      comparison.missingInProduction.forEach(table => {
        console.log(`   - ${table}`)
      })
    }

    if (comparison.missingInLocal.length > 0) {
      console.log(`\n⚠️  MISSING IN LOCAL (${comparison.missingInLocal.length}):`)
      comparison.missingInLocal.forEach(table => {
        console.log(`   - ${table}`)
      })
    }

    // Column differences
    if (comparison.columnDifferences.length > 0) {
      console.log(`\n🔍 COLUMN DIFFERENCES:`)
      comparison.columnDifferences.forEach(diff => {
        console.log(`\n   Table: ${diff.table}`)
        if (diff.differences.missingInProduction.length > 0) {
          console.log(`     Missing in Production: ${diff.differences.missingInProduction.join(', ')}`)
        }
        if (diff.differences.missingInLocal.length > 0) {
          console.log(`     Missing in Local: ${diff.differences.missingInLocal.join(', ')}`)
        }
        if (diff.differences.typeMismatches.length > 0) {
          console.log(`     Type Mismatches:`)
          diff.differences.typeMismatches.forEach(mismatch => {
            console.log(`       ${mismatch.column}: ${mismatch.localType} (local) vs ${mismatch.productionType} (production)`)
          })
        }
      })
    }

    // Index differences
    if (comparison.indexDifferences.missingInProduction.length > 0) {
      console.log(`\n📇 INDEXES MISSING IN PRODUCTION (${comparison.indexDifferences.missingInProduction.length}):`)
      comparison.indexDifferences.missingInProduction.forEach(index => {
        console.log(`   - ${index.table_name}.${index.index_name}`)
      })
    }

    // Foreign key differences
    if (comparison.foreignKeyDifferences.missingInProduction.length > 0) {
      console.log(`\n🔗 FOREIGN KEYS MISSING IN PRODUCTION (${comparison.foreignKeyDifferences.missingInProduction.length}):`)
      comparison.foreignKeyDifferences.missingInProduction.forEach(fk => {
        console.log(`   - ${fk.table_name}.${fk.column_name} → ${fk.foreign_table_name}.${fk.foreign_column_name}`)
      })
    }

    // Overall assessment
    const hasIssues = comparison.missingInProduction.length > 0 ||
      comparison.columnDifferences.length > 0 ||
      comparison.indexDifferences.missingInProduction.length > 0 ||
      comparison.foreignKeyDifferences.missingInProduction.length > 0

    console.log(`\n🎯 SYNCHRONIZATION STATUS: ${hasIssues ? '❌ ISSUES FOUND' : '✅ SYNCHRONIZED'}`)

    if (hasIssues) {
      console.log(`\n⚠️  DEPLOYMENT RECOMMENDATION: DO NOT COMMIT`)
      console.log(`   Production database must be synchronized before GitHub commit`)
      console.log(`   Run generated migration scripts in production Supabase first`)
    } else {
      console.log(`\n✅ DEPLOYMENT RECOMMENDATION: SAFE TO COMMIT`)
      console.log(`   Schemas are synchronized - deployment will be seamless`)
    }

    console.log('='.repeat(80))
  }

  private async generateMigrationScripts(comparison: SchemaComparison): Promise<void> {
    console.log('\n📝 Generating migration scripts...')

    let migrationSQL = `-- Manufacturing ERP - Production Database Migration Script
-- Generated: ${new Date().toISOString()}
-- Purpose: Synchronize production Supabase with local development schema

BEGIN;

`

    // Add missing tables
    if (comparison.missingInProduction.length > 0) {
      migrationSQL += `-- Missing Tables in Production\n`
      for (const tableName of comparison.missingInProduction) {
        migrationSQL += `-- TODO: Add CREATE TABLE statement for ${tableName}\n`
        migrationSQL += `-- Review local schema and add complete table definition\n\n`
      }
    }

    // Add missing columns
    for (const diff of comparison.columnDifferences) {
      if (diff.differences.missingInProduction.length > 0) {
        migrationSQL += `-- Missing columns in ${diff.table}\n`
        for (const column of diff.differences.missingInProduction) {
          migrationSQL += `-- ALTER TABLE ${diff.table} ADD COLUMN ${column} [TYPE]; -- TODO: Add correct type\n`
        }
        migrationSQL += `\n`
      }
    }

    // Add missing indexes
    if (comparison.indexDifferences.missingInProduction.length > 0) {
      migrationSQL += `-- Missing Indexes in Production\n`
      for (const index of comparison.indexDifferences.missingInProduction) {
        const uniqueClause = index.is_unique ? 'UNIQUE ' : ''
        migrationSQL += `CREATE ${uniqueClause}INDEX ${index.index_name} ON ${index.table_name}(${index.column_name});\n`
      }
      migrationSQL += `\n`
    }

    // Add missing foreign keys
    if (comparison.foreignKeyDifferences.missingInProduction.length > 0) {
      migrationSQL += `-- Missing Foreign Keys in Production\n`
      for (const fk of comparison.foreignKeyDifferences.missingInProduction) {
        migrationSQL += `ALTER TABLE ${fk.table_name} ADD CONSTRAINT ${fk.constraint_name} 
  FOREIGN KEY (${fk.column_name}) REFERENCES ${fk.foreign_table_name}(${fk.foreign_column_name});\n`
      }
      migrationSQL += `\n`
    }

    migrationSQL += `COMMIT;

-- Rollback script (run if migration fails)
-- BEGIN;
-- [Add rollback statements here]
-- COMMIT;
`

    // Save migration script
    const fs = require('fs')
    const migrationPath = `scripts/production-migration-${Date.now()}.sql`
    fs.writeFileSync(migrationPath, migrationSQL)

    console.log(`   ✅ Migration script saved: ${migrationPath}`)
  }
}

/**
 * Execute verification if run directly
 */
if (require.main === module) {
  const synchronizer = new DatabaseSchemaSynchronizer()
  synchronizer.verifySchemaSync()
    .then((comparison) => {
      const hasIssues = comparison.missingInProduction.length > 0 ||
        comparison.columnDifferences.length > 0 ||
        comparison.indexDifferences.missingInProduction.length > 0 ||
        comparison.foreignKeyDifferences.missingInProduction.length > 0

      if (hasIssues) {
        console.log('\n❌ Schema synchronization issues found!')
        console.log('🚫 DO NOT COMMIT TO GITHUB until production is synchronized')
        process.exit(1)
      } else {
        console.log('\n✅ Schemas are synchronized!')
        console.log('🚀 SAFE TO COMMIT TO GITHUB')
        process.exit(0)
      }
    })
    .catch((error) => {
      console.error('\n💥 Schema verification failed:', error)
      process.exit(1)
    })
}
