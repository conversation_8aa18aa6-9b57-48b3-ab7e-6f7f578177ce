-- Manufacturing ERP - Financial Data Seeding Script
-- Phase 1B: Enhanced Financial Integration
-- 
-- This script seeds the financial analytics tables with sample data
-- for development and testing purposes.
--
-- Author: Professional ERP Developer
-- Version: 1.0.0 - Phase 1B Financial Dashboard & Integration

-- ============================================================================
-- SEED SAMPLE CURRENCIES
-- ============================================================================

-- Get the first company ID for seeding
DO $$
DECLARE
    company_uuid TEXT;
BEGIN
    -- Get the first company ID
    SELECT id INTO company_uuid FROM companies LIMIT 1;
    
    IF company_uuid IS NOT NULL THEN
        -- Insert sample currencies
        INSERT INTO currencies (
            id, company_id, code, name, symbol, decimal_places, is_base_currency, is_active, exchange_rate, last_rate_update
        ) VALUES 
            ('curr-usd-' || company_uuid, company_uuid, 'USD', 'US Dollar', '$', '2', 'true', 'true', '1.0', NOW()),
            ('curr-eur-' || company_uuid, company_uuid, 'EUR', 'Euro', '€', '2', 'false', 'true', '0.85', NOW()),
            ('curr-cny-' || company_uuid, company_uuid, 'CNY', 'Chinese Yuan', '¥', '2', 'false', 'true', '7.25', NOW()),
            ('curr-gbp-' || company_uuid, company_uuid, 'GBP', 'British Pound', '£', '2', 'false', 'true', '0.75', NOW())
        ON CONFLICT (company_id, code) DO UPDATE SET
            exchange_rate = EXCLUDED.exchange_rate,
            last_rate_update = EXCLUDED.last_rate_update;

        -- Insert sample exchange rate history
        INSERT INTO exchange_rate_history (
            id, company_id, from_currency_code, to_currency_code, exchange_rate, effective_date, rate_source, rate_type
        ) VALUES 
            ('exr-001-' || company_uuid, company_uuid, 'EUR', 'USD', '1.18', '2025-01-01', 'api', 'spot'),
            ('exr-002-' || company_uuid, company_uuid, 'CNY', 'USD', '0.138', '2025-01-01', 'api', 'spot'),
            ('exr-003-' || company_uuid, company_uuid, 'GBP', 'USD', '1.33', '2025-01-01', 'api', 'spot'),
            ('exr-004-' || company_uuid, company_uuid, 'EUR', 'USD', '1.17', '2025-01-02', 'api', 'spot'),
            ('exr-005-' || company_uuid, company_uuid, 'CNY', 'USD', '0.137', '2025-01-02', 'api', 'spot')
        ON CONFLICT DO NOTHING;

        -- Insert sample export revenue analytics
        INSERT INTO export_revenue_analytics (
            id, company_id, period_type, period_start, period_end, 
            total_revenue_usd, total_revenue_local, local_currency_code,
            export_volume_containers, export_volume_weight, export_destinations_count,
            top_export_destination, currency_breakdown, exchange_rate_impact,
            average_order_value, revenue_per_container, profit_margin_percentage,
            currency_exposure_risk, concentration_risk
        ) VALUES 
            ('era-001-' || company_uuid, company_uuid, 'monthly', '2025-01-01', '2025-01-31', 
             '2847392', '2847392', 'USD', '247', '2470000', '23',
             'Germany', '{"USD": 1847392, "EUR": 750000, "CNY": 180000, "GBP": 70000}', '0',
             '11532', '11532', '15.2', 'medium', 'low'),
            ('era-002-' || company_uuid, company_uuid, 'monthly', '2024-12-01', '2024-12-31', 
             '2534821', '2534821', 'USD', '221', '2210000', '21',
             'Germany', '{"USD": 1634821, "EUR": 680000, "CNY": 160000, "GBP": 60000}', '0',
             '11472', '11472', '14.8', 'medium', 'low'),
            ('era-003-' || company_uuid, company_uuid, 'monthly', '2024-11-01', '2024-11-30', 
             '2123456', '2123456', 'USD', '189', '1890000', '19',
             'Netherlands', '{"USD": 1423456, "EUR": 520000, "CNY": 140000, "GBP": 40000}', '0',
             '11238', '11238', '13.9', 'medium', 'medium'),
            ('era-004-' || company_uuid, company_uuid, 'weekly', '2025-01-01', '2025-01-07', 
             '712000', '712000', 'USD', '62', '620000', '8',
             'Germany', '{"USD": 462000, "EUR": 187500, "CNY": 45000, "GBP": 17500}', '0',
             '11484', '11484', '15.5', 'medium', 'low'),
            ('era-005-' || company_uuid, company_uuid, 'weekly', '2025-01-08', '2025-01-14', 
             '698000', '698000', 'USD', '58', '580000', '7',
             'France', '{"USD": 448000, "EUR": 175000, "CNY": 52500, "GBP": 22500}', '0',
             '12034', '12034', '14.9', 'medium', 'low')
        ON CONFLICT DO NOTHING;

        -- Insert sample container cost allocations
        INSERT INTO container_cost_allocation (
            id, company_id, container_id, container_type, container_number,
            sales_contract_id, base_shipping_cost, fuel_surcharge, port_charges,
            customs_fees, insurance_cost, handling_fees, documentation_fees, other_charges,
            cost_currency, base_cost_amount, base_currency_code, exchange_rate,
            total_container_weight, total_container_volume, utilization_percentage,
            allocation_method, allocated_products, shipping_date, cost_date
        ) VALUES 
            ('cca-001-' || company_uuid, company_uuid, 'CONT-001', '40ft', 'MSKU1234567',
             NULL, '3500', '525', '420', '280', '175', '140', '105', '70',
             'USD', '5215', 'USD', '1.0', '28500', '67.2', '89.2',
             'weight', '{"product1": {"weight": 15000, "allocation": 2800}, "product2": {"weight": 13500, "allocation": 2415}}',
             '2025-01-15', '2025-01-15'),
            ('cca-002-' || company_uuid, company_uuid, 'CONT-002', '20ft', 'MSKU2345678',
             NULL, '2800', '420', '350', '210', '140', '105', '70', '35',
             'USD', '4130', 'USD', '1.0', '18200', '33.1', '82.5',
             'weight', '{"product3": {"weight": 10000, "allocation": 2270}, "product4": {"weight": 8200, "allocation": 1860}}',
             '2025-01-16', '2025-01-16'),
            ('cca-003-' || company_uuid, company_uuid, 'CONT-003', '40ft_hc', 'MSKU3456789',
             NULL, '4200', '630', '490', '315', '210', '175', '140', '105',
             'USD', '6265', 'USD', '1.0', '29800', '76.3', '91.8',
             'volume', '{"product5": {"volume": 40.2, "allocation": 3300}, "product6": {"volume": 36.1, "allocation": 2965}}',
             '2025-01-17', '2025-01-17')
        ON CONFLICT DO NOTHING;

        -- Insert sample cash flow forecasts
        INSERT INTO cash_flow_forecasts (
            id, company_id, forecast_date, forecast_type, forecast_horizon_days,
            expected_ar_collections, expected_contract_payments, expected_export_receipts,
            other_inflows, total_inflows, expected_ap_payments, expected_payroll,
            expected_operating_expenses, expected_shipping_costs, expected_raw_material_costs,
            other_outflows, total_outflows, net_cash_flow, cumulative_cash_flow,
            currency_risk_adjustment, confidence_level, best_case_scenario,
            worst_case_scenario, most_likely_scenario, created_by, forecast_method
        ) VALUES 
            ('cff-001-' || company_uuid, company_uuid, '2025-01-20', 'weekly', '7',
             '450000', '200000', '150000', '50000', '850000',
             '320000', '120000', '80000', '150000', '100000',
             '30000', '800000', '50000', '50000', '5000',
             'high', '75000', '25000', '50000', 'system', 'historical'),
            ('cff-002-' || company_uuid, company_uuid, '2025-01-27', 'weekly', '7',
             '380000', '180000', '120000', '40000', '720000',
             '290000', '120000', '75000', '140000', '95000',
             '25000', '745000', '-25000', '25000', '3000',
             'medium', '20000', '-70000', '-25000', 'system', 'historical'),
            ('cff-003-' || company_uuid, company_uuid, '2025-02-03', 'monthly', '30',
             '1800000', '800000', '600000', '200000', '3400000',
             '1200000', '480000', '320000', '600000', '400000',
             '120000', '3120000', '280000', '305000', '15000',
             'medium', '420000', '140000', '280000', 'system', 'historical')
        ON CONFLICT DO NOTHING;

        -- Insert sample currency risk assessments
        INSERT INTO currency_risk_assessments (
            id, company_id, assessment_date, assessment_type, base_currency_code,
            currency_exposures, total_exposure_base_currency, value_at_risk_1day,
            value_at_risk_1week, value_at_risk_1month, overall_risk_level,
            highest_risk_currency, risk_concentration_ratio, hedged_exposure_percentage,
            unhedged_exposure, hedging_effectiveness, risk_mitigation_recommendations,
            hedging_recommendations, created_by, assessment_method
        ) VALUES 
            ('cra-001-' || company_uuid, company_uuid, '2025-01-20', 'weekly', 'USD',
             '{"EUR": {"exposure": 750000, "risk": "medium"}, "CNY": {"exposure": 1800000, "risk": "high"}, "GBP": {"exposure": 320000, "risk": "medium"}}',
             '1557296', '25504', '56859', '127693', 'medium', 'CNY', '0.45',
             '60', '624518', '0.75',
             '["Consider increasing hedge ratio to 75%", "Monitor PBOC policy changes", "Diversify supplier base"]',
             '["CNY forward contracts", "EUR options for downside protection", "GBP currency swaps"]',
             'system', 'historical')
        ON CONFLICT DO NOTHING;

        RAISE NOTICE 'Sample financial data seeded successfully for company: %', company_uuid;
    ELSE
        RAISE NOTICE 'No companies found. Please create a company first.';
    END IF;
END $$;
