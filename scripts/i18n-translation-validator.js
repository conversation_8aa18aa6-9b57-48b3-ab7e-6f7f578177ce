#!/usr/bin/env node

/**
 * Manufacturing ERP Translation Accuracy Validator
 * 
 * Comprehensive validation of AI translations for Manufacturing ERP
 * technical terminology accuracy and professional quality standards.
 * 
 * ZERO BREAKING CHANGES: Validation only, no system modifications.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const PARALLEL_DIR = 'i18n-parallel';
const VALIDATION_DIR = path.join(PARALLEL_DIR, 'validation');

// Manufacturing ERP Quality Standards
const QUALITY_STANDARDS = {
  // Technical terminology that must be translated consistently
  criticalTerms: {
    'Work Order': '工单',
    'Bill of Materials': '物料清单',
    'Quality Control': '质量控制',
    'Quality Inspection': '质量检验',
    'Raw Materials': '原材料',
    'Finished Goods': '成品',
    'Production Planning': '生产计划',
    'Sample Approval': '样品审批',
    'Export Declaration': '出口报关',
    'Sales Contract': '销售合同',
    'Purchase Contract': '采购合同',
    'Inventory Management': '库存管理',
    'Supplier Management': '供应商管理',
    'Customer Relationship': '客户关系',
    'Financial Management': '财务管理'
  },
  
  // Business process terms
  businessTerms: {
    'Pending': '待处理',
    'Approved': '已审批',
    'Rejected': '已拒绝',
    'In Progress': '进行中',
    'Completed': '已完成',
    'Draft': '草稿',
    'Active': '活跃',
    'Inactive': '非活跃',
    'Cancelled': '已取消',
    'On Hold': '暂停'
  },
  
  // Form and UI terms
  uiTerms: {
    'Name': '名称',
    'Description': '描述',
    'Status': '状态',
    'Date': '日期',
    'Quantity': '数量',
    'Price': '价格',
    'Total': '总计',
    'Actions': '操作',
    'Edit': '编辑',
    'Delete': '删除',
    'Save': '保存',
    'Cancel': '取消',
    'Create': '创建',
    'Update': '更新',
    'Search': '搜索',
    'Filter': '筛选'
  },
  
  // Financial terms
  financialTerms: {
    'Revenue': '收入',
    'Profit': '利润',
    'Cost': '成本',
    'Expense': '费用',
    'Invoice': '发票',
    'Payment': '付款',
    'Receipt': '收据',
    'Balance': '余额',
    'Account': '账户',
    'Transaction': '交易'
  }
};

// Validation rules
const VALIDATION_RULES = {
  // Check for consistent terminology
  terminologyConsistency: {
    weight: 0.4,
    description: 'Manufacturing ERP terminology must be consistent'
  },
  
  // Check for professional language
  professionalLanguage: {
    weight: 0.3,
    description: 'Business language must be professional and formal'
  },
  
  // Check for completeness
  translationCompleteness: {
    weight: 0.2,
    description: 'All text must be properly translated'
  },
  
  // Check for context appropriateness
  contextAppropriateness: {
    weight: 0.1,
    description: 'Translation must fit the UI context'
  }
};

// Ensure validation directory exists
function ensureValidationDirectory() {
  if (!fs.existsSync(VALIDATION_DIR)) {
    fs.mkdirSync(VALIDATION_DIR, { recursive: true });
  }
}

// Load translations for validation
function loadTranslationsForValidation(sourceFile) {
  try {
    let sourcePath;
    
    // Try different locations
    const possiblePaths = [
      sourceFile,
      path.join(PARALLEL_DIR, 'pending', sourceFile),
      path.join(PARALLEL_DIR, 'approved', sourceFile),
      path.join(PARALLEL_DIR, 'csv', sourceFile)
    ];
    
    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        sourcePath = testPath;
        break;
      }
    }
    
    if (!sourcePath) {
      throw new Error(`Translation file not found: ${sourceFile}`);
    }
    
    const content = fs.readFileSync(sourcePath, 'utf8');
    let data;
    
    // Handle different file formats
    if (sourcePath.endsWith('.json')) {
      data = JSON.parse(content);
      return {
        translations: data.translations || data,
        metadata: data.metadata || {},
        sourcePath
      };
    } else if (sourcePath.endsWith('.csv')) {
      // Handle CSV format (simplified for validation)
      const lines = content.split('\n').filter(line => line.trim());
      const headers = lines[0].split(',');
      const translations = {};
      
      for (let i = 1; i < lines.length; i++) {
        const row = lines[i].split(',');
        if (row.length >= 3) {
          const key = row[0].replace(/"/g, '');
          const english = row[1].replace(/"/g, '');
          const chinese = row[2].replace(/"/g, '');
          
          translations[key] = {
            english,
            chinese,
            context: row[3] || '',
            file: row[4] || '',
            category: row[6] || 'unknown'
          };
        }
      }
      
      return {
        translations,
        metadata: { format: 'csv' },
        sourcePath
      };
    }
    
    throw new Error('Unsupported file format');
    
  } catch (error) {
    console.error('❌ Failed to load translations:', error.message);
    return null;
  }
}

// Validate terminology consistency
function validateTerminologyConsistency(translations) {
  const results = {
    score: 1.0,
    issues: [],
    suggestions: [],
    validTerms: 0,
    totalTerms: 0
  };
  
  // Combine all quality standards
  const allStandardTerms = {
    ...QUALITY_STANDARDS.criticalTerms,
    ...QUALITY_STANDARDS.businessTerms,
    ...QUALITY_STANDARDS.uiTerms,
    ...QUALITY_STANDARDS.financialTerms
  };
  
  Object.entries(translations).forEach(([key, translationData]) => {
    const english = typeof translationData === 'string' ? translationData : translationData.english;
    const chinese = typeof translationData === 'string' ? translationData : translationData.chinese;
    
    if (!english || !chinese) return;
    
    // Check if this translation contains any standard terms
    Object.entries(allStandardTerms).forEach(([standardEn, standardZh]) => {
      if (english.toLowerCase().includes(standardEn.toLowerCase())) {
        results.totalTerms++;
        
        if (chinese.includes(standardZh)) {
          results.validTerms++;
        } else {
          results.issues.push({
            key,
            english,
            chinese,
            expected: standardZh,
            actual: chinese,
            severity: 'high',
            message: `Expected "${standardEn}" to be translated as "${standardZh}"`
          });
          
          results.suggestions.push({
            key,
            suggestion: chinese.replace(new RegExp(standardEn, 'gi'), standardZh),
            reason: 'Consistent terminology'
          });
        }
      }
    });
  });
  
  if (results.totalTerms > 0) {
    results.score = results.validTerms / results.totalTerms;
  }
  
  return results;
}

// Validate professional language quality
function validateProfessionalLanguage(translations) {
  const results = {
    score: 1.0,
    issues: [],
    suggestions: [],
    professionalCount: 0,
    totalCount: 0
  };
  
  // Professional language patterns
  const unprofessionalPatterns = [
    { pattern: /很好|不错|挺好/, message: 'Use more formal language', severity: 'medium' },
    { pattern: /搞定|弄好/, message: 'Use professional business terms', severity: 'high' },
    { pattern: /点击|点一下/, message: 'Use "选择" or "操作" instead', severity: 'low' },
    { pattern: /^[a-zA-Z\s]+$/, message: 'Translation appears to be in English', severity: 'high' }
  ];
  
  // Professional alternatives
  const professionalAlternatives = {
    '点击': '选择',
    '搞定': '完成',
    '弄好': '设置',
    '很好': '良好',
    '不错': '满意'
  };
  
  Object.entries(translations).forEach(([key, translationData]) => {
    const chinese = typeof translationData === 'string' ? translationData : translationData.chinese;
    
    if (!chinese) return;
    
    results.totalCount++;
    let isProfessional = true;
    
    unprofessionalPatterns.forEach(({ pattern, message, severity }) => {
      if (pattern.test(chinese)) {
        isProfessional = false;
        results.issues.push({
          key,
          chinese,
          pattern: pattern.toString(),
          message,
          severity
        });
      }
    });
    
    // Suggest professional alternatives
    Object.entries(professionalAlternatives).forEach(([informal, formal]) => {
      if (chinese.includes(informal)) {
        results.suggestions.push({
          key,
          suggestion: chinese.replace(new RegExp(informal, 'g'), formal),
          reason: `Replace "${informal}" with more professional "${formal}"`
        });
      }
    });
    
    if (isProfessional) {
      results.professionalCount++;
    }
  });
  
  if (results.totalCount > 0) {
    results.score = results.professionalCount / results.totalCount;
  }
  
  return results;
}

// Validate translation completeness
function validateTranslationCompleteness(translations) {
  const results = {
    score: 1.0,
    issues: [],
    suggestions: [],
    completeCount: 0,
    totalCount: 0
  };
  
  Object.entries(translations).forEach(([key, translationData]) => {
    const english = typeof translationData === 'string' ? translationData : translationData.english;
    const chinese = typeof translationData === 'string' ? translationData : translationData.chinese;
    
    results.totalCount++;
    
    // Check for missing translations
    if (!chinese || chinese.trim() === '') {
      results.issues.push({
        key,
        english,
        chinese: chinese || '',
        message: 'Missing Chinese translation',
        severity: 'high'
      });
      return;
    }
    
    // Check for untranslated English
    if (chinese === english) {
      results.issues.push({
        key,
        english,
        chinese,
        message: 'Text appears to be untranslated',
        severity: 'high'
      });
      return;
    }
    
    // Check for partial translations (mixed English/Chinese)
    const hasEnglish = /[a-zA-Z]/.test(chinese);
    const hasChinese = /[\u4e00-\u9fff]/.test(chinese);
    
    if (hasEnglish && hasChinese) {
      // This might be acceptable for technical terms
      const technicalTermsPattern = /\b(API|URL|ID|UUID|JSON|XML|HTTP|HTTPS|CSS|HTML|JS|TS)\b/i;
      if (!technicalTermsPattern.test(chinese)) {
        results.issues.push({
          key,
          english,
          chinese,
          message: 'Mixed English/Chinese - consider full translation',
          severity: 'medium'
        });
      }
    }
    
    results.completeCount++;
  });
  
  if (results.totalCount > 0) {
    results.score = results.completeCount / results.totalCount;
  }
  
  return results;
}

// Validate context appropriateness
function validateContextAppropriateness(translations) {
  const results = {
    score: 1.0,
    issues: [],
    suggestions: [],
    appropriateCount: 0,
    totalCount: 0
  };
  
  // Context-specific validation rules
  const contextRules = {
    placeholder: {
      pattern: /^e\.g\.|^Enter|^Search|^Select/i,
      expectedChinese: /^例如|^输入|^搜索|^选择/,
      message: 'Placeholder should start with appropriate Chinese prefix'
    },
    button: {
      pattern: /^(Save|Cancel|Delete|Edit|Create|Update)$/i,
      expectedLength: { min: 1, max: 4 },
      message: 'Button text should be concise (1-4 characters)'
    },
    label: {
      pattern: /\*\s*$/,
      expectedChinese: /\s*\*\s*$/,
      message: 'Required field marker (*) should be preserved'
    }
  };
  
  Object.entries(translations).forEach(([key, translationData]) => {
    const english = typeof translationData === 'string' ? translationData : translationData.english;
    const chinese = typeof translationData === 'string' ? translationData : translationData.chinese;
    const context = typeof translationData === 'object' ? translationData.context : '';
    
    if (!english || !chinese) return;
    
    results.totalCount++;
    let isAppropriate = true;
    
    // Determine context from key or context field
    let detectedContext = '';
    if (key.includes('placeholder') || context.includes('placeholder')) {
      detectedContext = 'placeholder';
    } else if (key.includes('button') || context.includes('Button')) {
      detectedContext = 'button';
    } else if (key.includes('label') || context.includes('Label')) {
      detectedContext = 'label';
    }
    
    // Apply context-specific rules
    if (detectedContext && contextRules[detectedContext]) {
      const rule = contextRules[detectedContext];
      
      if (rule.pattern && rule.pattern.test(english)) {
        if (rule.expectedChinese && !rule.expectedChinese.test(chinese)) {
          isAppropriate = false;
          results.issues.push({
            key,
            english,
            chinese,
            context: detectedContext,
            message: rule.message,
            severity: 'medium'
          });
        }
        
        if (rule.expectedLength) {
          const chineseLength = chinese.replace(/[^\u4e00-\u9fff]/g, '').length;
          if (chineseLength < rule.expectedLength.min || chineseLength > rule.expectedLength.max) {
            results.suggestions.push({
              key,
              suggestion: `Consider shorter translation (current: ${chineseLength} characters)`,
              reason: 'UI space constraints'
            });
          }
        }
      }
    }
    
    if (isAppropriate) {
      results.appropriateCount++;
    }
  });
  
  if (results.totalCount > 0) {
    results.score = results.appropriateCount / results.totalCount;
  }
  
  return results;
}

// Perform comprehensive validation
async function performComprehensiveValidation(sourceFile) {
  console.log(`🔍 Starting comprehensive translation validation for: ${sourceFile}`);
  console.log('⚠️  ZERO BREAKING CHANGES: Validation only, no system modifications\n');
  
  ensureValidationDirectory();
  
  // Load translations
  console.log('📖 Loading translations for validation...');
  const translationData = loadTranslationsForValidation(sourceFile);
  if (!translationData) {
    return { success: false, error: 'Failed to load translations' };
  }
  
  const { translations, metadata, sourcePath } = translationData;
  console.log(`✅ Loaded ${Object.keys(translations).length} translations from ${sourcePath}`);
  
  // Run all validation checks
  console.log('\n🔍 Running validation checks...');
  
  const validationResults = {
    terminology: validateTerminologyConsistency(translations),
    professional: validateProfessionalLanguage(translations),
    completeness: validateTranslationCompleteness(translations),
    context: validateContextAppropriateness(translations)
  };
  
  // Calculate overall score
  let overallScore = 0;
  Object.entries(VALIDATION_RULES).forEach(([ruleName, rule]) => {
    const result = validationResults[ruleName.replace('Consistency', '').replace('Language', '').replace('Completeness', '').replace('Appropriateness', '').toLowerCase()];
    if (result) {
      overallScore += result.score * rule.weight;
    }
  });
  
  // Generate comprehensive report
  const report = {
    sourceFile,
    sourcePath,
    validatedAt: new Date().toISOString(),
    totalTranslations: Object.keys(translations).length,
    overallScore: Math.round(overallScore * 100) / 100,
    overallGrade: getQualityGrade(overallScore),
    validationResults,
    summary: {
      totalIssues: Object.values(validationResults).reduce((sum, r) => sum + r.issues.length, 0),
      totalSuggestions: Object.values(validationResults).reduce((sum, r) => sum + r.suggestions.length, 0),
      highSeverityIssues: Object.values(validationResults).reduce((sum, r) => 
        sum + r.issues.filter(i => i.severity === 'high').length, 0
      )
    },
    recommendations: generateRecommendations(validationResults, overallScore)
  };
  
  // Save validation report
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = path.join(VALIDATION_DIR, `validation-report-${timestamp}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  
  // Display results
  console.log('\n📊 VALIDATION RESULTS');
  console.log('='.repeat(50));
  console.log(`Overall Score: ${Math.round(overallScore * 100)}% (${report.overallGrade})`);
  console.log(`Total Issues: ${report.summary.totalIssues}`);
  console.log(`High Severity: ${report.summary.highSeverityIssues}`);
  console.log(`Suggestions: ${report.summary.totalSuggestions}`);
  
  // Show detailed results
  Object.entries(validationResults).forEach(([category, result]) => {
    const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
    console.log(`\n${categoryName}: ${Math.round(result.score * 100)}%`);
    
    if (result.issues.length > 0) {
      console.log(`  Issues (${result.issues.length}):`);
      result.issues.slice(0, 3).forEach(issue => {
        console.log(`    - ${issue.message} (${issue.severity})`);
      });
      if (result.issues.length > 3) {
        console.log(`    ... and ${result.issues.length - 3} more`);
      }
    }
  });
  
  // Show recommendations
  if (report.recommendations.length > 0) {
    console.log('\n🎯 RECOMMENDATIONS:');
    report.recommendations.forEach(rec => console.log(`   - ${rec}`));
  }
  
  console.log(`\n📋 Detailed report saved: ${reportFile}`);
  
  return {
    success: true,
    report,
    reportFile,
    overallScore,
    grade: report.overallGrade
  };
}

// Get quality grade based on score
function getQualityGrade(score) {
  if (score >= 0.9) return 'Excellent';
  if (score >= 0.8) return 'Good';
  if (score >= 0.7) return 'Acceptable';
  if (score >= 0.6) return 'Needs Improvement';
  return 'Poor';
}

// Generate recommendations based on validation results
function generateRecommendations(results, overallScore) {
  const recommendations = [];
  
  if (results.terminology.score < 0.8) {
    recommendations.push('Review and standardize Manufacturing ERP terminology');
  }
  
  if (results.professional.score < 0.8) {
    recommendations.push('Improve professional language quality');
  }
  
  if (results.completeness.score < 0.9) {
    recommendations.push('Complete missing translations');
  }
  
  if (results.context.score < 0.8) {
    recommendations.push('Adjust translations for UI context appropriateness');
  }
  
  if (overallScore < 0.7) {
    recommendations.push('Consider manual review by Manufacturing ERP expert');
  }
  
  if (overallScore >= 0.9) {
    recommendations.push('Excellent quality - ready for production integration');
  }
  
  return recommendations;
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  const filename = process.argv[3];
  
  switch (command) {
    case 'validate':
      if (!filename) {
        console.error('❌ Please provide translation filename to validate');
        console.log('Usage: node i18n-translation-validator.js validate <translation-file>');
        process.exit(1);
      }
      performComprehensiveValidation(filename);
      break;
      
    case 'list':
      const validationFiles = fs.existsSync(VALIDATION_DIR) 
        ? fs.readdirSync(VALIDATION_DIR).filter(f => f.startsWith('validation-report-'))
        : [];
      
      if (validationFiles.length === 0) {
        console.log('📋 No validation reports found');
      } else {
        console.log('📋 Validation Reports:');
        validationFiles.forEach((file, index) => {
          console.log(`   ${index + 1}. ${file}`);
        });
      }
      break;
      
    default:
      console.log('📖 Manufacturing ERP Translation Accuracy Validator');
      console.log('');
      console.log('Commands:');
      console.log('  validate <file> - Perform comprehensive translation validation');
      console.log('  list           - List validation reports');
      console.log('');
      console.log('Examples:');
      console.log('  node i18n-translation-validator.js validate batch-1-2025-09-15.json');
      console.log('  node i18n-translation-validator.js validate translations.csv');
      console.log('  node i18n-translation-validator.js list');
  }
}

module.exports = { performComprehensiveValidation };
