/**
 * Manufacturing ERP - Comprehensive Workflow Integration Verification
 * 
 * Verifies the complete manufacturing workflow chain:
 * Sales Contract → Work Order → Quality Inspection → Inventory → (Ready for Shipping)
 */

import { db } from "@/lib/db"
import { 
  salesContracts, 
  workOrders, 
  qualityInspections, 
  stockLots, 
  stockTxns,
  products,
  companies
} from "@/lib/schema-postgres"
import { eq, and, sql } from "drizzle-orm"

interface WorkflowVerificationResult {
  totalContracts: number
  totalWorkOrders: number
  totalInspections: number
  totalStockLots: number
  totalTransactions: number
  workflowIntegrity: {
    contractsWithWorkOrders: number
    workOrdersWithInspections: number
    inspectionsWithStockLots: number
    workOrdersWithStockLots: number
    completedWorkflowChains: number
  }
  locationAnalysis: {
    professionalLocations: number
    legacyLocations: number
    locationDistribution: Record<string, number>
  }
  qualityIntegration: {
    pendingInspections: number
    approvedStock: number
    rejectedStock: number
    quarantinedStock: number
  }
  dataIntegrity: {
    orphanedWorkOrders: number
    orphanedInspections: number
    orphanedStockLots: number
    missingRelationships: string[]
  }
}

export class WorkflowIntegrationVerifier {
  
  async verifyCompleteWorkflow(): Promise<WorkflowVerificationResult> {
    console.log('🔍 Starting Comprehensive Workflow Integration Verification...')
    
    const result: WorkflowVerificationResult = {
      totalContracts: 0,
      totalWorkOrders: 0,
      totalInspections: 0,
      totalStockLots: 0,
      totalTransactions: 0,
      workflowIntegrity: {
        contractsWithWorkOrders: 0,
        workOrdersWithInspections: 0,
        inspectionsWithStockLots: 0,
        workOrdersWithStockLots: 0,
        completedWorkflowChains: 0
      },
      locationAnalysis: {
        professionalLocations: 0,
        legacyLocations: 0,
        locationDistribution: {}
      },
      qualityIntegration: {
        pendingInspections: 0,
        approvedStock: 0,
        rejectedStock: 0,
        quarantinedStock: 0
      },
      dataIntegrity: {
        orphanedWorkOrders: 0,
        orphanedInspections: 0,
        orphanedStockLots: 0,
        missingRelationships: []
      }
    }

    try {
      // 1. Basic Entity Counts
      await this.verifyBasicCounts(result)
      
      // 2. Workflow Integration Analysis
      await this.verifyWorkflowIntegrity(result)
      
      // 3. Location Integration Analysis
      await this.verifyLocationIntegration(result)
      
      // 4. Quality Integration Analysis
      await this.verifyQualityIntegration(result)
      
      // 5. Data Integrity Analysis
      await this.verifyDataIntegrity(result)
      
      // 6. Print Comprehensive Report
      this.printVerificationReport(result)
      
      return result
      
    } catch (error) {
      console.error('❌ Workflow verification failed:', error)
      throw error
    }
  }

  private async verifyBasicCounts(result: WorkflowVerificationResult): Promise<void> {
    console.log('\n📊 Phase 1: Basic Entity Counts')
    
    // Count all entities
    const contractCount = await db.select({ count: sql<number>`count(*)` }).from(salesContracts)
    const workOrderCount = await db.select({ count: sql<number>`count(*)` }).from(workOrders)
    const inspectionCount = await db.select({ count: sql<number>`count(*)` }).from(qualityInspections)
    const stockLotCount = await db.select({ count: sql<number>`count(*)` }).from(stockLots)
    const transactionCount = await db.select({ count: sql<number>`count(*)` }).from(stockTxns)
    
    result.totalContracts = contractCount[0]?.count || 0
    result.totalWorkOrders = workOrderCount[0]?.count || 0
    result.totalInspections = inspectionCount[0]?.count || 0
    result.totalStockLots = stockLotCount[0]?.count || 0
    result.totalTransactions = transactionCount[0]?.count || 0
    
    console.log(`   📋 Sales Contracts: ${result.totalContracts}`)
    console.log(`   🏭 Work Orders: ${result.totalWorkOrders}`)
    console.log(`   🔬 Quality Inspections: ${result.totalInspections}`)
    console.log(`   📦 Stock Lots: ${result.totalStockLots}`)
    console.log(`   💰 Stock Transactions: ${result.totalTransactions}`)
  }

  private async verifyWorkflowIntegrity(result: WorkflowVerificationResult): Promise<void> {
    console.log('\n🔗 Phase 2: Workflow Integration Analysis')
    
    // Contracts with work orders
    const contractsWithWorkOrders = await db
      .select({ count: sql<number>`count(distinct ${salesContracts.id})` })
      .from(salesContracts)
      .innerJoin(workOrders, eq(workOrders.sales_contract_id, salesContracts.id))
    
    result.workflowIntegrity.contractsWithWorkOrders = contractsWithWorkOrders[0]?.count || 0
    
    // Work orders with inspections
    const workOrdersWithInspections = await db
      .select({ count: sql<number>`count(distinct ${workOrders.id})` })
      .from(workOrders)
      .innerJoin(qualityInspections, eq(qualityInspections.work_order_id, workOrders.id))
    
    result.workflowIntegrity.workOrdersWithInspections = workOrdersWithInspections[0]?.count || 0
    
    // Inspections with stock lots
    const inspectionsWithStockLots = await db
      .select({ count: sql<number>`count(distinct ${qualityInspections.id})` })
      .from(qualityInspections)
      .innerJoin(stockLots, eq(stockLots.inspection_id, qualityInspections.id))
    
    result.workflowIntegrity.inspectionsWithStockLots = inspectionsWithStockLots[0]?.count || 0
    
    // Work orders with stock lots (direct relationship)
    const workOrdersWithStockLots = await db
      .select({ count: sql<number>`count(distinct ${workOrders.id})` })
      .from(workOrders)
      .innerJoin(stockLots, eq(stockLots.work_order_id, workOrders.id))
    
    result.workflowIntegrity.workOrdersWithStockLots = workOrdersWithStockLots[0]?.count || 0
    
    // Complete workflow chains (Contract → Work Order → Inspection → Stock Lot)
    const completeChains = await db
      .select({ count: sql<number>`count(distinct ${salesContracts.id})` })
      .from(salesContracts)
      .innerJoin(workOrders, eq(workOrders.sales_contract_id, salesContracts.id))
      .innerJoin(qualityInspections, eq(qualityInspections.work_order_id, workOrders.id))
      .innerJoin(stockLots, eq(stockLots.work_order_id, workOrders.id))
    
    result.workflowIntegrity.completedWorkflowChains = completeChains[0]?.count || 0
    
    console.log(`   📋→🏭 Contracts with Work Orders: ${result.workflowIntegrity.contractsWithWorkOrders}`)
    console.log(`   🏭→🔬 Work Orders with Inspections: ${result.workflowIntegrity.workOrdersWithInspections}`)
    console.log(`   🔬→📦 Inspections with Stock Lots: ${result.workflowIntegrity.inspectionsWithStockLots}`)
    console.log(`   🏭→📦 Work Orders with Stock Lots: ${result.workflowIntegrity.workOrdersWithStockLots}`)
    console.log(`   🔗 Complete Workflow Chains: ${result.workflowIntegrity.completedWorkflowChains}`)
  }

  private async verifyLocationIntegration(result: WorkflowVerificationResult): Promise<void> {
    console.log('\n📍 Phase 3: Location Integration Analysis')
    
    // Professional locations (post-migration)
    const professionalLocations = ['fg_main_warehouse', 'rm_building_a', 'wip_production_line_1', 'dist_shanghai', 'dist_beijing']
    
    // Legacy locations (pre-migration)
    const legacyLocations = ['finished_goods', 'raw_materials', 'work_in_progress', 'warehouse_a', 'warehouse_b']
    
    // Check stock lots location distribution
    const locationDistribution = await db
      .select({ 
        location: stockLots.location,
        count: sql<number>`count(*)` 
      })
      .from(stockLots)
      .groupBy(stockLots.location)
    
    let professionalCount = 0
    let legacyCount = 0
    
    locationDistribution.forEach(row => {
      result.locationAnalysis.locationDistribution[row.location] = row.count
      
      if (professionalLocations.includes(row.location)) {
        professionalCount += row.count
      } else if (legacyLocations.includes(row.location)) {
        legacyCount += row.count
      }
    })
    
    result.locationAnalysis.professionalLocations = professionalCount
    result.locationAnalysis.legacyLocations = legacyCount
    
    console.log(`   ✅ Professional Location Records: ${professionalCount}`)
    console.log(`   ⚠️  Legacy Location Records: ${legacyCount}`)
    console.log(`   📊 Location Distribution:`)
    Object.entries(result.locationAnalysis.locationDistribution).forEach(([location, count]) => {
      const status = professionalLocations.includes(location) ? '✅' : 
                    legacyLocations.includes(location) ? '⚠️' : '❓'
      console.log(`      ${status} ${location}: ${count} records`)
    })
  }

  private async verifyQualityIntegration(result: WorkflowVerificationResult): Promise<void> {
    console.log('\n🔬 Phase 4: Quality Integration Analysis')
    
    // Quality status distribution
    const qualityStats = await db
      .select({ 
        quality_status: stockLots.quality_status,
        count: sql<number>`count(*)` 
      })
      .from(stockLots)
      .groupBy(stockLots.quality_status)
    
    qualityStats.forEach(row => {
      switch (row.quality_status) {
        case 'pending':
          result.qualityIntegration.pendingInspections = row.count
          break
        case 'approved':
          result.qualityIntegration.approvedStock = row.count
          break
        case 'rejected':
          result.qualityIntegration.rejectedStock = row.count
          break
        case 'quarantined':
          result.qualityIntegration.quarantinedStock = row.count
          break
      }
    })
    
    console.log(`   ⏳ Pending Quality Inspections: ${result.qualityIntegration.pendingInspections}`)
    console.log(`   ✅ Approved Stock: ${result.qualityIntegration.approvedStock}`)
    console.log(`   ❌ Rejected Stock: ${result.qualityIntegration.rejectedStock}`)
    console.log(`   ⚠️  Quarantined Stock: ${result.qualityIntegration.quarantinedStock}`)
  }

  private async verifyDataIntegrity(result: WorkflowVerificationResult): Promise<void> {
    console.log('\n🔍 Phase 5: Data Integrity Analysis')
    
    // Check for orphaned records
    
    // Orphaned work orders (no sales contract)
    const orphanedWorkOrders = await db
      .select({ count: sql<number>`count(*)` })
      .from(workOrders)
      .leftJoin(salesContracts, eq(salesContracts.id, workOrders.sales_contract_id))
      .where(sql`${workOrders.sales_contract_id} IS NOT NULL AND ${salesContracts.id} IS NULL`)
    
    result.dataIntegrity.orphanedWorkOrders = orphanedWorkOrders[0]?.count || 0
    
    // Orphaned inspections (no work order)
    const orphanedInspections = await db
      .select({ count: sql<number>`count(*)` })
      .from(qualityInspections)
      .leftJoin(workOrders, eq(workOrders.id, qualityInspections.work_order_id))
      .where(sql`${qualityInspections.work_order_id} IS NOT NULL AND ${workOrders.id} IS NULL`)
    
    result.dataIntegrity.orphanedInspections = orphanedInspections[0]?.count || 0
    
    // Orphaned stock lots (no work order)
    const orphanedStockLots = await db
      .select({ count: sql<number>`count(*)` })
      .from(stockLots)
      .leftJoin(workOrders, eq(workOrders.id, stockLots.work_order_id))
      .where(sql`${stockLots.work_order_id} IS NOT NULL AND ${workOrders.id} IS NULL`)
    
    result.dataIntegrity.orphanedStockLots = orphanedStockLots[0]?.count || 0
    
    console.log(`   🔗 Orphaned Work Orders: ${result.dataIntegrity.orphanedWorkOrders}`)
    console.log(`   🔗 Orphaned Inspections: ${result.dataIntegrity.orphanedInspections}`)
    console.log(`   🔗 Orphaned Stock Lots: ${result.dataIntegrity.orphanedStockLots}`)
    
    // Collect missing relationships
    if (result.dataIntegrity.orphanedWorkOrders > 0) {
      result.dataIntegrity.missingRelationships.push('Work Orders missing Sales Contracts')
    }
    if (result.dataIntegrity.orphanedInspections > 0) {
      result.dataIntegrity.missingRelationships.push('Quality Inspections missing Work Orders')
    }
    if (result.dataIntegrity.orphanedStockLots > 0) {
      result.dataIntegrity.missingRelationships.push('Stock Lots missing Work Orders')
    }
  }

  private printVerificationReport(result: WorkflowVerificationResult): void {
    console.log('\n' + '='.repeat(80))
    console.log('📋 MANUFACTURING ERP WORKFLOW INTEGRATION VERIFICATION REPORT')
    console.log('='.repeat(80))
    
    // Overall Health Score
    const totalPossibleChains = Math.max(result.totalContracts, result.totalWorkOrders, 1)
    const integrationScore = Math.round((result.workflowIntegrity.completedWorkflowChains / totalPossibleChains) * 100)
    
    console.log(`\n🎯 OVERALL INTEGRATION HEALTH: ${integrationScore}%`)
    console.log(`   Complete Workflow Chains: ${result.workflowIntegrity.completedWorkflowChains}/${totalPossibleChains}`)
    
    // Location Migration Status
    const locationMigrationScore = Math.round((result.locationAnalysis.professionalLocations / 
      (result.locationAnalysis.professionalLocations + result.locationAnalysis.legacyLocations)) * 100)
    
    console.log(`\n📍 LOCATION MIGRATION STATUS: ${locationMigrationScore}%`)
    console.log(`   Professional Locations: ${result.locationAnalysis.professionalLocations}`)
    console.log(`   Legacy Locations: ${result.locationAnalysis.legacyLocations}`)
    
    // Quality Integration Status
    const totalStock = result.qualityIntegration.pendingInspections + 
                      result.qualityIntegration.approvedStock + 
                      result.qualityIntegration.rejectedStock + 
                      result.qualityIntegration.quarantinedStock
    
    console.log(`\n🔬 QUALITY INTEGRATION STATUS:`)
    console.log(`   Total Stock Under Quality Management: ${totalStock}`)
    console.log(`   Quality Approval Rate: ${totalStock > 0 ? Math.round((result.qualityIntegration.approvedStock / totalStock) * 100) : 0}%`)
    
    // Data Integrity Status
    const totalOrphans = result.dataIntegrity.orphanedWorkOrders + 
                        result.dataIntegrity.orphanedInspections + 
                        result.dataIntegrity.orphanedStockLots
    
    console.log(`\n🔍 DATA INTEGRITY STATUS: ${totalOrphans === 0 ? '✅ EXCELLENT' : '⚠️ NEEDS ATTENTION'}`)
    console.log(`   Total Orphaned Records: ${totalOrphans}`)
    
    if (result.dataIntegrity.missingRelationships.length > 0) {
      console.log(`   Missing Relationships:`)
      result.dataIntegrity.missingRelationships.forEach(issue => {
        console.log(`      - ${issue}`)
      })
    }
    
    // Recommendations
    console.log(`\n💡 RECOMMENDATIONS:`)
    
    if (result.locationAnalysis.legacyLocations > 0) {
      console.log(`   🔧 Run location migration again to update remaining ${result.locationAnalysis.legacyLocations} legacy location records`)
    }
    
    if (integrationScore < 80) {
      console.log(`   🔗 Improve workflow integration - only ${integrationScore}% of workflows are complete`)
    }
    
    if (totalOrphans > 0) {
      console.log(`   🧹 Clean up ${totalOrphans} orphaned records to improve data integrity`)
    }
    
    if (integrationScore >= 90 && locationMigrationScore >= 95 && totalOrphans === 0) {
      console.log(`   🎉 SYSTEM IS PRODUCTION-READY! All core workflows are properly integrated.`)
    }
    
    console.log('='.repeat(80))
  }
}

/**
 * Execute verification if run directly
 */
if (require.main === module) {
  const verifier = new WorkflowIntegrationVerifier()
  verifier.verifyCompleteWorkflow()
    .then((result) => {
      console.log('\n✅ Workflow verification completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n❌ Workflow verification failed:', error)
      process.exit(1)
    })
}
