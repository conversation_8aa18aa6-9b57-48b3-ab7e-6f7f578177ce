#!/usr/bin/env node

/**
 * Manufacturing ERP CI/CD Integration Manager
 * 
 * Integrates i18n workflow into existing CI/CD pipeline without disrupting
 * current deployment process. Supports multiple CI platforms.
 * 
 * ZERO BREAKING CHANGES: Non-blocking integration with comprehensive reporting.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CI_CONFIG = {
  // Non-blocking policy - never fail builds
  policy: 'warn',
  
  // Quality thresholds
  maxNewStrings: parseInt(process.env.I18N_MAX_NEW_STRINGS) || 20,
  maxValidationIssues: parseInt(process.env.I18N_MAX_VALIDATION_ISSUES) || 10,
  
  // Output formats
  outputFormat: process.env.I18N_OUTPUT_FORMAT || 'json',
  createArtifacts: process.env.I18N_CREATE_ARTIFACTS !== 'false',
  
  // Notification settings
  slackWebhook: process.env.I18N_SLACK_WEBHOOK,
  teamsWebhook: process.env.I18N_TEAMS_WEBHOOK,
  emailNotifications: process.env.I18N_EMAIL_NOTIFICATIONS === 'true'
};

// Detect CI environment
function detectCIEnvironment() {
  const ciInfo = {
    platform: 'unknown',
    isPR: false,
    prNumber: null,
    commitSha: null,
    branch: null,
    buildNumber: null,
    buildUrl: null
  };
  
  if (process.env.GITHUB_ACTIONS) {
    ciInfo.platform = 'github';
    ciInfo.isPR = process.env.GITHUB_EVENT_NAME === 'pull_request';
    ciInfo.prNumber = process.env.GITHUB_EVENT_NUMBER;
    ciInfo.commitSha = process.env.GITHUB_SHA;
    ciInfo.branch = process.env.GITHUB_REF_NAME;
    ciInfo.buildNumber = process.env.GITHUB_RUN_NUMBER;
    ciInfo.buildUrl = `https://github.com/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}`;
  } else if (process.env.GITLAB_CI) {
    ciInfo.platform = 'gitlab';
    ciInfo.isPR = !!process.env.CI_MERGE_REQUEST_ID;
    ciInfo.prNumber = process.env.CI_MERGE_REQUEST_ID;
    ciInfo.commitSha = process.env.CI_COMMIT_SHA;
    ciInfo.branch = process.env.CI_COMMIT_REF_NAME;
    ciInfo.buildNumber = process.env.CI_PIPELINE_ID;
    ciInfo.buildUrl = process.env.CI_PIPELINE_URL;
  } else if (process.env.JENKINS_URL) {
    ciInfo.platform = 'jenkins';
    ciInfo.isPR = !!process.env.CHANGE_ID;
    ciInfo.prNumber = process.env.CHANGE_ID;
    ciInfo.commitSha = process.env.GIT_COMMIT;
    ciInfo.branch = process.env.BRANCH_NAME;
    ciInfo.buildNumber = process.env.BUILD_NUMBER;
    ciInfo.buildUrl = process.env.BUILD_URL;
  } else if (process.env.VERCEL) {
    ciInfo.platform = 'vercel';
    ciInfo.commitSha = process.env.VERCEL_GIT_COMMIT_SHA;
    ciInfo.branch = process.env.VERCEL_GIT_COMMIT_REF;
    ciInfo.buildUrl = `https://vercel.com/${process.env.VERCEL_URL}`;
  }
  
  return ciInfo;
}

// Setup CI/CD environment
function setupCICDEnvironment() {
  console.log('🔧 Setting up i18n CI/CD environment...');
  
  try {
    // Ensure required directories exist
    const directories = [
      'i18n-parallel/pending',
      'i18n-parallel/approved',
      'i18n-parallel/integrated',
      'i18n-parallel/csv',
      'i18n-parallel/logs',
      'i18n-temp',
      'i18n-cicd'
    ];
    
    directories.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
    
    // Set script permissions (Unix-like systems)
    try {
      const scriptFiles = fs.readdirSync('scripts')
        .filter(file => file.startsWith('i18n-') && file.endsWith('.js'));
      
      scriptFiles.forEach(file => {
        try {
          fs.chmodSync(path.join('scripts', file), '755');
        } catch (error) {
          // Ignore permission errors on Windows
        }
      });
    } catch (error) {
      console.warn('⚠️  Could not set script permissions:', error.message);
    }
    
    console.log('✅ CI/CD environment setup completed');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to setup CI/CD environment:', error.message);
    return false;
  }
}

// Run i18n quality checks
async function runQualityChecks(ciInfo) {
  console.log('🔍 Running i18n quality checks...');
  
  const results = {
    detection: null,
    validation: null,
    integration: null,
    performance: null,
    overall: {
      passed: true,
      score: 100,
      issues: [],
      warnings: []
    }
  };
  
  try {
    // 1. Hardcoded string detection
    console.log('   Running hardcoded string detection...');
    try {
      const detectionOutput = execSync('node scripts/i18n-auto-detect.js', { 
        encoding: 'utf8',
        timeout: 60000 
      });
      
      results.detection = {
        success: true,
        output: detectionOutput,
        newStrings: extractNewStringsCount(detectionOutput)
      };
      
      if (results.detection.newStrings > CI_CONFIG.maxNewStrings) {
        results.overall.warnings.push(`High number of new hardcoded strings: ${results.detection.newStrings}`);
        results.overall.score -= 10;
      }
      
    } catch (error) {
      results.detection = {
        success: false,
        error: error.message,
        newStrings: 0
      };
      results.overall.warnings.push('Hardcoded string detection failed');
    }
    
    // 2. Translation validation (if pending translations exist)
    console.log('   Checking for pending translations...');
    const pendingDir = 'i18n-parallel/pending';
    if (fs.existsSync(pendingDir)) {
      const pendingFiles = fs.readdirSync(pendingDir).filter(f => f.endsWith('.json'));
      
      if (pendingFiles.length > 0) {
        console.log(`   Validating ${pendingFiles.length} pending translation files...`);
        
        try {
          const validationOutput = execSync(`node scripts/i18n-translation-validator.js validate ${pendingFiles[0]}`, {
            encoding: 'utf8',
            timeout: 30000
          });
          
          results.validation = {
            success: true,
            output: validationOutput,
            filesValidated: pendingFiles.length
          };
          
        } catch (error) {
          results.validation = {
            success: false,
            error: error.message,
            filesValidated: 0
          };
          results.overall.warnings.push('Translation validation failed');
        }
      }
    }
    
    // 3. Integration testing (if significant changes detected)
    if (results.detection && results.detection.newStrings > 5) {
      console.log('   Running integration tests...');
      
      try {
        const integrationOutput = execSync('node scripts/i18n-integration-tester.js test', {
          encoding: 'utf8',
          timeout: 45000
        });
        
        results.integration = {
          success: true,
          output: integrationOutput,
          testsRun: extractTestCount(integrationOutput)
        };
        
      } catch (error) {
        results.integration = {
          success: false,
          error: error.message,
          testsRun: 0
        };
        results.overall.warnings.push('Integration testing failed');
      }
    }
    
    // 4. Performance assessment (comprehensive mode only)
    if (process.env.I18N_CHECK_LEVEL === 'comprehensive') {
      console.log('   Running performance assessment...');
      
      try {
        const performanceOutput = execSync('node scripts/i18n-performance-assessor.js assess', {
          encoding: 'utf8',
          timeout: 60000
        });
        
        results.performance = {
          success: true,
          output: performanceOutput,
          impact: extractPerformanceImpact(performanceOutput)
        };
        
      } catch (error) {
        results.performance = {
          success: false,
          error: error.message,
          impact: 'unknown'
        };
        results.overall.warnings.push('Performance assessment failed');
      }
    }
    
    // Calculate overall score
    const successfulChecks = Object.values(results).filter(r => r && r.success).length - 1; // Exclude overall
    const totalChecks = Object.keys(results).length - 1; // Exclude overall
    
    if (totalChecks > 0) {
      results.overall.score = Math.round((successfulChecks / totalChecks) * 100);
    }
    
    results.overall.passed = results.overall.score >= 70; // Minimum acceptable score
    
  } catch (error) {
    console.error('❌ Quality checks failed:', error.message);
    results.overall.passed = false;
    results.overall.issues.push(`Quality check execution failed: ${error.message}`);
  }
  
  return results;
}

// Extract metrics from command outputs
function extractNewStringsCount(output) {
  const match = output.match(/(\d+)\s+new\s+hardcoded\s+strings/i);
  return match ? parseInt(match[1]) : 0;
}

function extractTestCount(output) {
  const match = output.match(/(\d+)\/(\d+)/);
  return match ? parseInt(match[2]) : 0;
}

function extractPerformanceImpact(output) {
  if (output.includes('POSITIVE')) return 'positive';
  if (output.includes('NEGATIVE')) return 'negative';
  return 'neutral';
}

// Generate CI/CD report
function generateCICDReport(results, ciInfo) {
  const report = {
    timestamp: new Date().toISOString(),
    ciInfo,
    config: CI_CONFIG,
    results,
    summary: {
      overallPassed: results.overall.passed,
      score: results.overall.score,
      newStrings: results.detection ? results.detection.newStrings : 0,
      issues: results.overall.issues.length,
      warnings: results.overall.warnings.length
    },
    recommendations: generateRecommendations(results),
    artifacts: []
  };
  
  // Save detailed report
  const reportFile = `i18n-cicd/cicd-report-${Date.now()}.json`;
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  report.artifacts.push(reportFile);
  
  // Generate markdown summary
  const markdownReport = generateMarkdownReport(report);
  const markdownFile = `i18n-cicd/cicd-summary-${Date.now()}.md`;
  fs.writeFileSync(markdownFile, markdownReport);
  report.artifacts.push(markdownFile);
  
  return report;
}

// Generate markdown report
function generateMarkdownReport(report) {
  const { results, summary, ciInfo } = report;
  
  let markdown = `# Manufacturing ERP i18n CI/CD Report

**Build:** ${ciInfo.buildNumber || 'N/A'}  
**Branch:** ${ciInfo.branch || 'N/A'}  
**Commit:** ${ciInfo.commitSha ? ciInfo.commitSha.substring(0, 8) : 'N/A'}  
**Platform:** ${ciInfo.platform}  
**Timestamp:** ${new Date().toLocaleString()}

## 📊 Quality Score: ${summary.score}%

${summary.overallPassed ? '✅' : '❌'} **Overall Status:** ${summary.overallPassed ? 'PASSED' : 'ATTENTION NEEDED'}

## 🔍 Check Results

`;

  // Detection results
  if (results.detection) {
    markdown += `### Hardcoded String Detection
${results.detection.success ? '✅' : '❌'} **Status:** ${results.detection.success ? 'Success' : 'Failed'}  
📊 **New Strings:** ${results.detection.newStrings}

`;
  }
  
  // Validation results
  if (results.validation) {
    markdown += `### Translation Validation
${results.validation.success ? '✅' : '❌'} **Status:** ${results.validation.success ? 'Success' : 'Failed'}  
📋 **Files Validated:** ${results.validation.filesValidated}

`;
  }
  
  // Integration results
  if (results.integration) {
    markdown += `### Integration Testing
${results.integration.success ? '✅' : '❌'} **Status:** ${results.integration.success ? 'Success' : 'Failed'}  
🔧 **Tests Run:** ${results.integration.testsRun}

`;
  }
  
  // Performance results
  if (results.performance) {
    markdown += `### Performance Assessment
${results.performance.success ? '✅' : '❌'} **Status:** ${results.performance.success ? 'Success' : 'Failed'}  
⚡ **Impact:** ${results.performance.impact}

`;
  }
  
  // Issues and warnings
  if (summary.issues > 0 || summary.warnings > 0) {
    markdown += `## ⚠️ Issues & Warnings

`;
    
    results.overall.issues.forEach(issue => {
      markdown += `❌ ${issue}\n`;
    });
    
    results.overall.warnings.forEach(warning => {
      markdown += `⚠️ ${warning}\n`;
    });
    
    markdown += '\n';
  }
  
  // Recommendations
  if (report.recommendations.length > 0) {
    markdown += `## 🎯 Recommendations

`;
    report.recommendations.forEach(rec => {
      markdown += `- ${rec}\n`;
    });
    markdown += '\n';
  }
  
  markdown += `## 🚀 Next Steps

${summary.newStrings > 0 ? 
  `1. **Review Detected Strings:** ${summary.newStrings} new hardcoded strings found
2. **Process Translations:** Use \`node scripts/i18n-ai-processor.js\`
3. **Team Review:** Export to CSV for collaborative editing
4. **Safe Integration:** Use validated sync mechanism` :
  `1. **Continue Development:** No immediate i18n actions required
2. **Maintain Quality:** Keep using \`t()\` function for new text
3. **Monitor Progress:** Regular quality checks are active`}

---
*Generated by Manufacturing ERP i18n CI/CD Integration*
`;
  
  return markdown;
}

// Generate recommendations
function generateRecommendations(results) {
  const recommendations = [];
  
  if (results.detection && results.detection.newStrings > 10) {
    recommendations.push('Consider processing detected hardcoded strings with i18n workflow');
  }
  
  if (results.validation && !results.validation.success) {
    recommendations.push('Review and fix translation validation issues');
  }
  
  if (results.integration && !results.integration.success) {
    recommendations.push('Address integration testing failures before proceeding');
  }
  
  if (results.performance && results.performance.impact === 'negative') {
    recommendations.push('Investigate performance impact and optimize if necessary');
  }
  
  if (results.overall.score < 80) {
    recommendations.push('Improve i18n quality score by addressing detected issues');
  }
  
  if (results.overall.passed) {
    recommendations.push('Quality checks passed - continue with current development practices');
  }
  
  return recommendations;
}

// Main CI/CD integration function
async function runCICDIntegration() {
  console.log('🚀 Manufacturing ERP i18n CI/CD Integration\n');
  console.log('⚠️  ZERO BREAKING CHANGES: Non-blocking quality checks\n');
  
  // Detect CI environment
  const ciInfo = detectCIEnvironment();
  console.log(`🔍 Detected CI platform: ${ciInfo.platform}`);
  
  if (ciInfo.isPR) {
    console.log(`📋 Pull Request: #${ciInfo.prNumber}`);
  }
  
  // Setup environment
  if (!setupCICDEnvironment()) {
    console.error('❌ Failed to setup CI/CD environment');
    process.exit(1);
  }
  
  // Run quality checks
  const results = await runQualityChecks(ciInfo);
  
  // Generate report
  const report = generateCICDReport(results, ciInfo);
  
  // Display summary
  console.log('\n📊 CI/CD INTEGRATION SUMMARY');
  console.log('='.repeat(50));
  console.log(`Overall Status: ${report.summary.overallPassed ? '✅ PASSED' : '⚠️  ATTENTION NEEDED'}`);
  console.log(`Quality Score: ${report.summary.score}%`);
  console.log(`New Strings: ${report.summary.newStrings}`);
  console.log(`Issues: ${report.summary.issues}`);
  console.log(`Warnings: ${report.summary.warnings}`);
  
  if (report.recommendations.length > 0) {
    console.log('\n🎯 RECOMMENDATIONS:');
    report.recommendations.forEach(rec => console.log(`   - ${rec}`));
  }
  
  console.log(`\n📋 Reports generated:`);
  report.artifacts.forEach(artifact => console.log(`   - ${artifact}`));
  
  // Set CI outputs (GitHub Actions)
  if (ciInfo.platform === 'github') {
    console.log(`::set-output name=i18n-status::${report.summary.overallPassed ? 'passed' : 'attention-needed'}`);
    console.log(`::set-output name=i18n-score::${report.summary.score}`);
    console.log(`::set-output name=i18n-new-strings::${report.summary.newStrings}`);
  }
  
  // Always exit with success (non-blocking)
  console.log('\n✅ CI/CD integration completed successfully');
  console.log('🚀 Build continues normally (non-blocking quality check)');
  
  return {
    success: true,
    report,
    passed: report.summary.overallPassed
  };
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'run':
      runCICDIntegration();
      break;
      
    case 'setup':
      setupCICDEnvironment();
      break;
      
    case 'detect':
      const ciInfo = detectCIEnvironment();
      console.log('🔍 CI Environment Detection:');
      console.log(JSON.stringify(ciInfo, null, 2));
      break;
      
    default:
      console.log('📖 Manufacturing ERP i18n CI/CD Integration');
      console.log('');
      console.log('Commands:');
      console.log('  run    - Run complete CI/CD integration');
      console.log('  setup  - Setup CI/CD environment only');
      console.log('  detect - Detect CI environment');
      console.log('');
      console.log('Environment Variables:');
      console.log('  I18N_MAX_NEW_STRINGS=20      - Max new strings before warning');
      console.log('  I18N_OUTPUT_FORMAT=json      - Output format (json/github)');
      console.log('  I18N_CHECK_LEVEL=standard    - Check level (standard/comprehensive)');
      console.log('  I18N_CREATE_ARTIFACTS=true   - Create CI artifacts');
  }
}

module.exports = { runCICDIntegration };
