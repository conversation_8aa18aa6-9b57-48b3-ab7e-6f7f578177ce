#!/usr/bin/env node

/**
 * Manufacturing ERP CSV Translation Workflow
 * 
 * Provides CSV export/import workflow for manual review and editing
 * of AI translations before integration into the system.
 * 
 * ZERO BREAKING CHANGES: All operations work with parallel workflow.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const PARALLEL_DIR = 'i18n-parallel';
const CSV_DIR = path.join(PARALLEL_DIR, 'csv');

// Ensure CSV directory exists
function ensureCSVDirectory() {
  if (!fs.existsSync(CSV_DIR)) {
    fs.mkdirSync(CSV_DIR, { recursive: true });
  }
}

// Convert JSON translations to CSV format
function jsonToCSV(translations, metadata = {}) {
  const headers = [
    'Key',
    'English',
    'Chinese',
    'Context',
    'File',
    'Line',
    'Category',
    'Priority',
    'Confidence',
    'Needs Review',
    'Comments'
  ];
  
  const rows = [headers];
  
  Object.entries(translations).forEach(([key, data]) => {
    // Handle both simple string and object format
    if (typeof data === 'string') {
      rows.push([
        key,
        data,
        data, // Default to same as English for manual editing
        '',
        '',
        '',
        'unknown',
        'medium',
        '0.5',
        'true',
        'Manual translation required'
      ]);
    } else {
      rows.push([
        key,
        data.english || '',
        data.chinese || data.english || '',
        data.context || '',
        data.file || '',
        data.line || '',
        data.category || 'unknown',
        data.priority || 'medium',
        data.confidence || '0.5',
        data.needsReview ? 'true' : 'false',
        data.comments || ''
      ]);
    }
  });
  
  // Convert to CSV string
  return rows.map(row => 
    row.map(cell => {
      // Escape quotes and wrap in quotes if contains comma, quote, or newline
      const cellStr = String(cell || '');
      if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
        return `"${cellStr.replace(/"/g, '""')}"`;
      }
      return cellStr;
    }).join(',')
  ).join('\n');
}

// Convert CSV format back to JSON translations
function csvToJSON(csvContent) {
  try {
    const lines = csvContent.split('\n').filter(line => line.trim());
    if (lines.length < 2) {
      throw new Error('CSV must have at least header and one data row');
    }
    
    // Parse header
    const headers = parseCSVRow(lines[0]);
    const expectedHeaders = ['Key', 'English', 'Chinese', 'Context', 'File', 'Line', 'Category', 'Priority', 'Confidence', 'Needs Review', 'Comments'];
    
    // Validate headers
    const missingHeaders = expectedHeaders.filter(h => !headers.includes(h));
    if (missingHeaders.length > 0) {
      console.warn(`⚠️  Missing headers: ${missingHeaders.join(', ')}`);
    }
    
    // Parse data rows
    const translations = {};
    const metadata = {
      importedAt: new Date().toISOString(),
      totalRows: lines.length - 1,
      method: 'csv_import'
    };
    
    for (let i = 1; i < lines.length; i++) {
      try {
        const row = parseCSVRow(lines[i]);
        if (row.length < headers.length) {
          console.warn(`⚠️  Row ${i + 1} has fewer columns than expected`);
          continue;
        }
        
        const rowData = {};
        headers.forEach((header, index) => {
          rowData[header.toLowerCase().replace(/\s+/g, '')] = row[index] || '';
        });
        
        const key = rowData.key;
        if (!key) {
          console.warn(`⚠️  Row ${i + 1} missing key, skipping`);
          continue;
        }
        
        translations[key] = {
          english: rowData.english || '',
          chinese: rowData.chinese || '',
          context: rowData.context || '',
          file: rowData.file || '',
          line: parseInt(rowData.line) || 0,
          category: rowData.category || 'unknown',
          priority: rowData.priority || 'medium',
          confidence: parseFloat(rowData.confidence) || 0.5,
          needsReview: rowData.needsreview === 'true',
          comments: rowData.comments || ''
        };
        
      } catch (error) {
        console.warn(`⚠️  Error parsing row ${i + 1}:`, error.message);
      }
    }
    
    return { translations, metadata };
    
  } catch (error) {
    console.error('❌ Failed to parse CSV:', error.message);
    return null;
  }
}

// Parse a single CSV row handling quotes and commas
function parseCSVRow(row) {
  const result = [];
  let current = '';
  let inQuotes = false;
  let i = 0;
  
  while (i < row.length) {
    const char = row[i];
    
    if (char === '"') {
      if (inQuotes && row[i + 1] === '"') {
        // Escaped quote
        current += '"';
        i += 2;
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
        i++;
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      result.push(current);
      current = '';
      i++;
    } else {
      current += char;
      i++;
    }
  }
  
  // Add the last field
  result.push(current);
  
  return result;
}

// Export translations to CSV
function exportToCSV(sourceFile, outputName) {
  try {
    ensureCSVDirectory();
    
    // Determine source path
    let sourcePath;
    if (fs.existsSync(sourceFile)) {
      sourcePath = sourceFile;
    } else {
      // Try in pending directory
      sourcePath = path.join(PARALLEL_DIR, 'pending', sourceFile);
      if (!fs.existsSync(sourcePath)) {
        // Try in approved directory
        sourcePath = path.join(PARALLEL_DIR, 'approved', sourceFile);
      }
    }
    
    if (!fs.existsSync(sourcePath)) {
      throw new Error(`Source file not found: ${sourceFile}`);
    }
    
    // Load source data
    const sourceData = JSON.parse(fs.readFileSync(sourcePath, 'utf8'));
    const translations = sourceData.translations || sourceData;
    const metadata = sourceData.metadata || {};
    
    // Generate CSV content
    const csvContent = jsonToCSV(translations, metadata);
    
    // Generate output filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const csvFilename = outputName || `translations-${timestamp}.csv`;
    const csvPath = path.join(CSV_DIR, csvFilename);
    
    // Write CSV file
    fs.writeFileSync(csvPath, csvContent);
    
    // Create metadata file
    const metadataFile = path.join(CSV_DIR, csvFilename.replace('.csv', '-metadata.json'));
    const csvMetadata = {
      ...metadata,
      exportedAt: new Date().toISOString(),
      sourceFile: sourcePath,
      csvFile: csvPath,
      totalTranslations: Object.keys(translations).length,
      instructions: {
        editing: 'Edit the Chinese column to provide better translations',
        review: 'Set Needs Review to false when translation is approved',
        comments: 'Add comments for complex translations or context',
        import: `Use: node i18n-csv-workflow.js import ${csvFilename}`
      }
    };
    
    fs.writeFileSync(metadataFile, JSON.stringify(csvMetadata, null, 2));
    
    console.log(`✅ Exported ${Object.keys(translations).length} translations to CSV`);
    console.log(`📄 CSV file: ${csvPath}`);
    console.log(`📋 Metadata: ${metadataFile}`);
    
    return { csvPath, metadataFile, translations: Object.keys(translations).length };
    
  } catch (error) {
    console.error('❌ Export failed:', error.message);
    return null;
  }
}

// Import translations from CSV
function importFromCSV(csvFile, outputName) {
  try {
    ensureCSVDirectory();
    
    // Determine CSV path
    let csvPath;
    if (fs.existsSync(csvFile)) {
      csvPath = csvFile;
    } else {
      csvPath = path.join(CSV_DIR, csvFile);
    }
    
    if (!fs.existsSync(csvPath)) {
      throw new Error(`CSV file not found: ${csvFile}`);
    }
    
    // Read and parse CSV
    const csvContent = fs.readFileSync(csvPath, 'utf8');
    const parsedData = csvToJSON(csvContent);
    
    if (!parsedData) {
      throw new Error('Failed to parse CSV content');
    }
    
    // Generate output filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const jsonFilename = outputName || `imported-${timestamp}.json`;
    
    // Prepare output data
    const outputData = {
      metadata: {
        ...parsedData.metadata,
        importedFrom: csvPath,
        importedAt: new Date().toISOString(),
        method: 'csv_import'
      },
      translations: parsedData.translations
    };
    
    // Write to approved directory (ready for sync)
    const approvedDir = path.join(PARALLEL_DIR, 'approved');
    fs.mkdirSync(approvedDir, { recursive: true });
    
    const outputPath = path.join(approvedDir, jsonFilename);
    fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2));
    
    // Generate import summary
    const stats = {
      totalTranslations: Object.keys(parsedData.translations).length,
      needsReview: Object.values(parsedData.translations).filter(t => t.needsReview).length,
      highPriority: Object.values(parsedData.translations).filter(t => t.priority === 'high').length,
      withComments: Object.values(parsedData.translations).filter(t => t.comments).length
    };
    
    console.log(`✅ Imported ${stats.totalTranslations} translations from CSV`);
    console.log(`📄 Output file: ${outputPath}`);
    console.log(`📊 Statistics:`);
    console.log(`   Needs review: ${stats.needsReview}`);
    console.log(`   High priority: ${stats.highPriority}`);
    console.log(`   With comments: ${stats.withComments}`);
    
    return { outputPath, stats, translations: parsedData.translations };
    
  } catch (error) {
    console.error('❌ Import failed:', error.message);
    return null;
  }
}

// List available CSV files
function listCSVFiles() {
  try {
    ensureCSVDirectory();
    
    const csvFiles = fs.readdirSync(CSV_DIR)
      .filter(file => file.endsWith('.csv'))
      .map(file => {
        const filepath = path.join(CSV_DIR, file);
        const stats = fs.statSync(filepath);
        const metadataFile = path.join(CSV_DIR, file.replace('.csv', '-metadata.json'));
        
        let metadata = {};
        if (fs.existsSync(metadataFile)) {
          try {
            metadata = JSON.parse(fs.readFileSync(metadataFile, 'utf8'));
          } catch (error) {
            // Ignore metadata parsing errors
          }
        }
        
        return {
          file,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          translations: metadata.totalTranslations || 'unknown',
          hasMetadata: fs.existsSync(metadataFile)
        };
      })
      .sort((a, b) => b.modified - a.modified);
    
    return csvFiles;
    
  } catch (error) {
    console.error('❌ Failed to list CSV files:', error.message);
    return [];
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  const filename = process.argv[3];
  const outputName = process.argv[4];
  
  switch (command) {
    case 'export':
      if (!filename) {
        console.error('❌ Please provide source filename to export');
        console.log('Usage: node i18n-csv-workflow.js export <source-file> [output-name.csv]');
        process.exit(1);
      }
      exportToCSV(filename, outputName);
      break;
      
    case 'import':
      if (!filename) {
        console.error('❌ Please provide CSV filename to import');
        console.log('Usage: node i18n-csv-workflow.js import <csv-file> [output-name.json]');
        process.exit(1);
      }
      importFromCSV(filename, outputName);
      break;
      
    case 'list':
      const csvFiles = listCSVFiles();
      if (csvFiles.length === 0) {
        console.log('📋 No CSV files found');
      } else {
        console.log('📋 Available CSV files:');
        csvFiles.forEach((file, index) => {
          console.log(`   ${index + 1}. ${file.file}`);
          console.log(`      Size: ${Math.round(file.size / 1024)}KB`);
          console.log(`      Translations: ${file.translations}`);
          console.log(`      Modified: ${file.modified.toLocaleDateString()}`);
          console.log(`      Metadata: ${file.hasMetadata ? '✅' : '❌'}`);
        });
      }
      break;
      
    default:
      console.log('📖 Manufacturing ERP CSV Translation Workflow');
      console.log('');
      console.log('Commands:');
      console.log('  export <source> [output.csv] - Export translations to CSV for manual editing');
      console.log('  import <csv> [output.json]   - Import edited CSV back to approved translations');
      console.log('  list                         - List available CSV files');
      console.log('');
      console.log('Workflow:');
      console.log('  1. Export pending translations to CSV');
      console.log('  2. Edit CSV file manually (Excel, Google Sheets, etc.)');
      console.log('  3. Import edited CSV back to approved translations');
      console.log('  4. Use sync mechanism to integrate into system');
      console.log('');
      console.log('Examples:');
      console.log('  node i18n-csv-workflow.js export batch-1-2025-09-15.json');
      console.log('  node i18n-csv-workflow.js import translations-2025-09-15.csv');
      console.log('  node i18n-csv-workflow.js list');
  }
}

module.exports = { exportToCSV, importFromCSV, listCSVFiles };
