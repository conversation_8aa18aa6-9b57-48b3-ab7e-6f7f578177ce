#!/usr/bin/env node

/**
 * Manufacturing ERP Rollback Strategy Validator
 * 
 * Tests and validates complete rollback capability to ensure existing
 * system can be restored instantly if needed.
 * 
 * ZERO BREAKING CHANGES: Comprehensive rollback testing with full safety.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const EXISTING_I18N = 'components/i18n-provider.tsx';
const BACKUP_DIR = 'i18n-backup';
const PARALLEL_DIR = 'i18n-parallel';
const ROLLBACK_DIR = path.join(PARALLEL_DIR, 'rollback-tests');

// Rollback test scenarios
const ROLLBACK_SCENARIOS = [
  {
    name: 'Complete System Restore',
    description: 'Test full system restore from backup',
    category: 'critical'
  },
  {
    name: 'Partial Rollback',
    description: 'Test selective rollback of specific changes',
    category: 'important'
  },
  {
    name: 'Backup Integrity Verification',
    description: 'Verify all backups are complete and valid',
    category: 'critical'
  },
  {
    name: 'Rollback Speed Test',
    description: 'Measure rollback execution time',
    category: 'performance'
  },
  {
    name: 'Data Consistency Check',
    description: 'Ensure data consistency after rollback',
    category: 'critical'
  },
  {
    name: 'System Functionality Verification',
    description: 'Verify system works correctly after rollback',
    category: 'critical'
  }
];

// Ensure rollback test directory exists
function ensureRollbackDirectory() {
  if (!fs.existsSync(ROLLBACK_DIR)) {
    fs.mkdirSync(ROLLBACK_DIR, { recursive: true });
  }
}

// Create test backup for rollback validation
function createTestBackup() {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const testBackupDir = path.join(ROLLBACK_DIR, `test-backup-${timestamp}`);

    fs.mkdirSync(testBackupDir, { recursive: true });

    // Backup existing i18n file
    if (fs.existsSync(EXISTING_I18N)) {
      const backupFile = path.join(testBackupDir, 'i18n-provider.tsx');
      fs.copyFileSync(EXISTING_I18N, backupFile);

      // Create checksum for integrity verification
      const content = fs.readFileSync(EXISTING_I18N, 'utf8');
      const checksum = generateChecksum(content);

      // Create restore script
      const restoreScript = `#!/bin/bash
# Test Rollback Script - ${timestamp}
echo "🔄 Testing rollback restore..."
cp "${backupFile}" "${EXISTING_I18N}"
echo "✅ Test rollback completed"
`;

      const restoreFile = path.join(testBackupDir, 'test-restore.sh');
      fs.writeFileSync(restoreFile, restoreScript);
      fs.chmodSync(restoreFile, '755');

      // Create metadata
      const metadata = {
        created: new Date().toISOString(),
        originalFile: EXISTING_I18N,
        backupFile,
        restoreScript: restoreFile,
        checksum,
        fileSize: fs.statSync(EXISTING_I18N).size,
        purpose: 'rollback_validation_test'
      };

      const metadataFile = path.join(testBackupDir, 'metadata.json');
      fs.writeFileSync(metadataFile, JSON.stringify(metadata, null, 2));

      return {
        success: true,
        backupDir: testBackupDir,
        metadata,
        restoreScript: restoreFile
      };
    }

    throw new Error('Original i18n file not found');

  } catch (error) {
    console.error('❌ Failed to create test backup:', error.message);
    return { success: false, error: error.message };
  }
}

// Generate checksum for file integrity
function generateChecksum(content) {
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return hash.toString(16);
}

// Test complete system restore
function testCompleteSystemRestore(testBackup) {
  console.log('🔄 Testing complete system restore...');

  const results = {
    passed: true,
    checks: [],
    warnings: [],
    errors: [],
    restoreTime: 0,
    integrityVerified: false
  };

  try {
    // Record original state
    const originalContent = fs.readFileSync(EXISTING_I18N, 'utf8');
    const originalChecksum = generateChecksum(originalContent);

    // Create a temporary modification to test restore
    const modifiedContent = originalContent + '\n// TEMPORARY TEST MODIFICATION';
    fs.writeFileSync(EXISTING_I18N, modifiedContent);

    // Verify modification was applied
    const modifiedChecksum = generateChecksum(fs.readFileSync(EXISTING_I18N, 'utf8'));
    if (modifiedChecksum === originalChecksum) {
      results.errors.push('Failed to apply test modification');
      results.passed = false;
      return results;
    }

    results.checks.push('✅ Test modification applied successfully');

    // Perform restore
    const restoreStart = Date.now();
    try {
      execSync(`bash "${testBackup.restoreScript}"`, { stdio: 'pipe' });
      results.restoreTime = Date.now() - restoreStart;
    } catch (error) {
      results.errors.push(`Restore script failed: ${error.message}`);
      results.passed = false;
      return results;
    }

    results.checks.push(`✅ Restore completed in ${results.restoreTime}ms`);

    // Verify restore integrity
    const restoredContent = fs.readFileSync(EXISTING_I18N, 'utf8');
    const restoredChecksum = generateChecksum(restoredContent);

    if (restoredChecksum === originalChecksum) {
      results.integrityVerified = true;
      results.checks.push('✅ File integrity verified after restore');
    } else {
      results.errors.push('File integrity check failed after restore');
      results.passed = false;
    }

    // Verify file size
    const restoredSize = fs.statSync(EXISTING_I18N).size;
    if (restoredSize === testBackup.metadata.fileSize) {
      results.checks.push('✅ File size verified after restore');
    } else {
      results.warnings.push(`File size mismatch: expected ${testBackup.metadata.fileSize}, got ${restoredSize}`);
    }

  } catch (error) {
    results.errors.push(`System restore test failed: ${error.message}`);
    results.passed = false;
  }

  return results;
}

// Test backup integrity verification
function testBackupIntegrityVerification() {
  console.log('🔍 Testing backup integrity verification...');

  const results = {
    passed: true,
    checks: [],
    warnings: [],
    errors: [],
    backupsFound: 0,
    validBackups: 0,
    corruptBackups: 0
  };

  try {
    if (!fs.existsSync(BACKUP_DIR)) {
      results.warnings.push('No backup directory found');
      return results;
    }

    // Find all backup directories
    const backupDirs = fs.readdirSync(BACKUP_DIR)
      .filter(item => {
        const itemPath = path.join(BACKUP_DIR, item);
        return fs.statSync(itemPath).isDirectory();
      });

    results.backupsFound = backupDirs.length;
    results.checks.push(`✅ Found ${results.backupsFound} backup directories`);

    // Verify each backup
    backupDirs.forEach(backupDir => {
      const backupPath = path.join(BACKUP_DIR, backupDir);
      const i18nBackup = path.join(backupPath, 'components', 'i18n-provider.tsx');
      const metadataFile = path.join(backupPath, 'translation-stats.json'); // Use actual metadata file

      let isValid = true;

      // Check if backup file exists
      if (!fs.existsSync(i18nBackup)) {
        results.errors.push(`Missing backup file in ${backupDir}`);
        isValid = false;
      }

      // Check if metadata exists
      if (!fs.existsSync(metadataFile)) {
        results.warnings.push(`Missing metadata in ${backupDir}`);
      } else {
        try {
          const metadata = JSON.parse(fs.readFileSync(metadataFile, 'utf8'));

          // Verify backup file integrity using file size (since our backup doesn't store checksum)
          if (metadata.fileSize && fs.existsSync(i18nBackup)) {
            const backupStats = fs.statSync(i18nBackup);

            if (backupStats.size !== metadata.fileSize) {
              results.warnings.push(`File size mismatch in ${backupDir}: expected ${metadata.fileSize}, got ${backupStats.size}`);
            }
          }
        } catch (error) {
          results.warnings.push(`Invalid metadata in ${backupDir}: ${error.message}`);
        }
      }

      if (isValid) {
        results.validBackups++;
      } else {
        results.corruptBackups++;
      }
    });

    results.checks.push(`✅ Valid backups: ${results.validBackups}`);

    if (results.corruptBackups > 0) {
      results.errors.push(`Found ${results.corruptBackups} corrupt backups`);
      results.passed = false;
    }

    if (results.validBackups === 0) {
      results.errors.push('No valid backups found');
      results.passed = false;
    }

  } catch (error) {
    results.errors.push(`Backup integrity verification failed: ${error.message}`);
    results.passed = false;
  }

  return results;
}

// Test rollback speed performance
function testRollbackSpeedPerformance(testBackup) {
  console.log('⚡ Testing rollback speed performance...');

  const results = {
    passed: true,
    checks: [],
    warnings: [],
    errors: [],
    averageRestoreTime: 0,
    fastestRestore: Infinity,
    slowestRestore: 0,
    testRuns: 3
  };

  try {
    const restoreTimes = [];

    // Run multiple restore tests
    for (let i = 0; i < results.testRuns; i++) {
      // Create test modification
      const originalContent = fs.readFileSync(EXISTING_I18N, 'utf8');
      const testContent = originalContent + `\n// TEST RUN ${i + 1}`;
      fs.writeFileSync(EXISTING_I18N, testContent);

      // Measure restore time
      const startTime = Date.now();
      execSync(`bash "${testBackup.restoreScript}"`, { stdio: 'pipe' });
      const restoreTime = Date.now() - startTime;

      restoreTimes.push(restoreTime);

      // Verify restore worked
      const restoredContent = fs.readFileSync(EXISTING_I18N, 'utf8');
      if (restoredContent !== originalContent) {
        results.errors.push(`Restore verification failed on run ${i + 1}`);
        results.passed = false;
      }
    }

    // Calculate statistics
    results.averageRestoreTime = restoreTimes.reduce((sum, time) => sum + time, 0) / restoreTimes.length;
    results.fastestRestore = Math.min(...restoreTimes);
    results.slowestRestore = Math.max(...restoreTimes);

    results.checks.push(`✅ Completed ${results.testRuns} restore speed tests`);
    results.checks.push(`✅ Average restore time: ${Math.round(results.averageRestoreTime)}ms`);
    results.checks.push(`✅ Fastest restore: ${results.fastestRestore}ms`);
    results.checks.push(`✅ Slowest restore: ${results.slowestRestore}ms`);

    // Performance thresholds
    if (results.averageRestoreTime > 1000) {
      results.warnings.push('Restore time may be slow for production use');
    }

    if (results.slowestRestore > 2000) {
      results.warnings.push('Worst-case restore time exceeds 2 seconds');
    }

  } catch (error) {
    results.errors.push(`Rollback speed test failed: ${error.message}`);
    results.passed = false;
  }

  return results;
}

// Test system functionality after rollback
function testSystemFunctionalityAfterRollback() {
  console.log('🔧 Testing system functionality after rollback...');

  const results = {
    passed: true,
    checks: [],
    warnings: [],
    errors: [],
    functionalityTests: {
      fileExists: false,
      fileReadable: false,
      contentValid: false,
      syntaxValid: false
    }
  };

  try {
    // Test 1: File exists
    if (fs.existsSync(EXISTING_I18N)) {
      results.functionalityTests.fileExists = true;
      results.checks.push('✅ i18n provider file exists');
    } else {
      results.errors.push('i18n provider file missing after rollback');
      results.passed = false;
      return results;
    }

    // Test 2: File is readable
    try {
      const content = fs.readFileSync(EXISTING_I18N, 'utf8');
      results.functionalityTests.fileReadable = true;
      results.checks.push('✅ i18n provider file is readable');

      // Test 3: Content appears valid
      if (content.includes('const en:') && content.includes('const zh:')) {
        results.functionalityTests.contentValid = true;
        results.checks.push('✅ i18n provider content appears valid');
      } else {
        results.errors.push('i18n provider content appears invalid');
        results.passed = false;
      }

    } catch (error) {
      results.errors.push(`Cannot read i18n provider file: ${error.message}`);
      results.passed = false;
      return results;
    }

    // Test 4: TypeScript syntax validation
    try {
      execSync(`npx tsc --noEmit --skipLibCheck "${EXISTING_I18N}"`, {
        stdio: 'pipe',
        timeout: 10000
      });
      results.functionalityTests.syntaxValid = true;
      results.checks.push('✅ TypeScript syntax validation passed');
    } catch (error) {
      results.warnings.push('TypeScript syntax validation had issues (may be unrelated)');
    }

    // Overall functionality assessment
    const passedTests = Object.values(results.functionalityTests).filter(Boolean).length;
    const totalTests = Object.keys(results.functionalityTests).length;

    if (passedTests === totalTests) {
      results.checks.push('✅ All functionality tests passed');
    } else {
      results.warnings.push(`${passedTests}/${totalTests} functionality tests passed`);
    }

  } catch (error) {
    results.errors.push(`System functionality test failed: ${error.message}`);
    results.passed = false;
  }

  return results;
}

// Run comprehensive rollback validation
async function runRollbackValidation() {
  console.log('🚀 Starting Manufacturing ERP Rollback Strategy Validation...\n');
  console.log('⚠️  ZERO BREAKING CHANGES: Rollback testing only, system will be restored\n');

  ensureRollbackDirectory();

  // Create test backup
  console.log('💾 Creating test backup for validation...');
  const testBackup = createTestBackup();
  if (!testBackup.success) {
    return { success: false, error: testBackup.error };
  }

  console.log(`✅ Test backup created: ${testBackup.backupDir}`);

  // Run all rollback tests
  console.log('\n🔍 Running rollback validation tests...\n');

  const testResults = {
    systemRestore: testCompleteSystemRestore(testBackup),
    backupIntegrity: testBackupIntegrityVerification(),
    rollbackSpeed: testRollbackSpeedPerformance(testBackup),
    systemFunctionality: testSystemFunctionalityAfterRollback()
  };

  // Calculate overall results
  const totalTests = Object.keys(testResults).length;
  const passedTests = Object.values(testResults).filter(r => r.passed).length;
  const overallPassed = passedTests === totalTests;

  // Generate comprehensive report
  const report = {
    validationDate: new Date().toISOString(),
    testBackup: {
      location: testBackup.backupDir,
      checksum: testBackup.metadata.checksum,
      fileSize: testBackup.metadata.fileSize
    },
    testResults,
    summary: {
      totalTests,
      passedTests,
      overallPassed,
      successRate: Math.round((passedTests / totalTests) * 100),
      rollbackCapability: overallPassed ? 'VERIFIED' : 'ISSUES_FOUND'
    },
    recommendations: generateRollbackRecommendations(testResults, overallPassed)
  };

  // Save validation report
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = path.join(ROLLBACK_DIR, `rollback-validation-report-${timestamp}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

  // Display results
  console.log('\n📊 ROLLBACK VALIDATION RESULTS');
  console.log('='.repeat(50));
  console.log(`Overall Status: ${overallPassed ? '✅ VERIFIED' : '❌ ISSUES FOUND'}`);
  console.log(`Success Rate: ${report.summary.successRate}% (${passedTests}/${totalTests})`);
  console.log(`Rollback Capability: ${report.summary.rollbackCapability}`);

  Object.entries(testResults).forEach(([testName, result]) => {
    const status = result.passed ? '✅' : '❌';
    console.log(`\n${status} ${testName.toUpperCase().replace(/([A-Z])/g, ' $1').trim()}:`);

    result.checks.forEach(check => console.log(`   ${check}`));

    if (result.warnings.length > 0) {
      result.warnings.forEach(warning => console.log(`   ⚠️  ${warning}`));
    }

    if (result.errors.length > 0) {
      result.errors.forEach(error => console.log(`   ❌ ${error}`));
    }
  });

  if (report.recommendations.length > 0) {
    console.log('\n🎯 RECOMMENDATIONS:');
    report.recommendations.forEach(rec => console.log(`   - ${rec}`));
  }

  console.log(`\n📋 Detailed report saved: ${reportFile}`);

  // Cleanup test backup
  try {
    fs.rmSync(testBackup.backupDir, { recursive: true, force: true });
    console.log('🧹 Test backup cleaned up');
  } catch (error) {
    console.warn('⚠️  Could not clean up test backup:', error.message);
  }

  return {
    success: true,
    report,
    reportFile,
    overallPassed,
    rollbackCapability: report.summary.rollbackCapability
  };
}

// Generate rollback recommendations
function generateRollbackRecommendations(testResults, overallPassed) {
  const recommendations = [];

  if (!testResults.systemRestore.passed) {
    recommendations.push('CRITICAL: Fix system restore functionality before proceeding');
  }

  if (!testResults.backupIntegrity.passed) {
    recommendations.push('CRITICAL: Address backup integrity issues');
  }

  if (testResults.rollbackSpeed.averageRestoreTime > 1000) {
    recommendations.push('Consider optimizing rollback speed for production use');
  }

  if (!testResults.systemFunctionality.passed) {
    recommendations.push('Verify system functionality after rollback operations');
  }

  if (testResults.backupIntegrity.validBackups === 0) {
    recommendations.push('Create at least one verified backup before proceeding');
  }

  if (overallPassed) {
    recommendations.push('Rollback capability verified - safe to proceed with confidence');
    recommendations.push('Maintain regular backup schedule for continued protection');
  } else {
    recommendations.push('Address failing tests before proceeding with system changes');
  }

  return recommendations;
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];

  switch (command) {
    case 'validate':
      runRollbackValidation();
      break;

    case 'backup-check':
      const integrityResults = testBackupIntegrityVerification();
      console.log('📊 Backup Integrity Check:');
      console.log(`   Backups found: ${integrityResults.backupsFound}`);
      console.log(`   Valid backups: ${integrityResults.validBackups}`);
      console.log(`   Corrupt backups: ${integrityResults.corruptBackups}`);
      break;

    default:
      console.log('📖 Manufacturing ERP Rollback Strategy Validator');
      console.log('');
      console.log('Commands:');
      console.log('  validate     - Run comprehensive rollback validation');
      console.log('  backup-check - Quick backup integrity check');
      console.log('');
      console.log('Examples:');
      console.log('  node i18n-rollback-validator.js validate');
      console.log('  node i18n-rollback-validator.js backup-check');
  }
}

module.exports = { runRollbackValidation };
