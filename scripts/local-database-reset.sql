-- ============================================================================
-- MANUFACTURING ERP - LOCAL DATABASE RESET SCRIPT
-- ============================================================================
-- 
-- PURPOSE: Safely reset local PostgreSQL database (localhost:5432/manufacturing_erp)
-- APPROACH: Zero breaking changes - preserve structure, clear business data only
-- USAGE: Execute in PostgreSQL client connected to manufacturing_erp database
--
-- ⚠️  CRITICAL: ONLY USE ON LOCAL DEVELOPMENT DATABASE
-- ⚠️  DO NOT RUN ON PRODUCTION SUPABASE DATABASE
-- ============================================================================

-- Step 1: Verify we're connected to the correct database
SELECT current_database() as connected_database, 
       current_user as database_user,
       inet_server_addr() as server_address;

-- Expected result: 
-- connected_database: manufacturing_erp
-- server_address: NULL (indicates localhost)

-- ============================================================================
-- STEP 2: PRESERVE SYSTEM DATA (DO NOT CLEAR)
-- ============================================================================

-- ✅ PRESERVE: System and configuration tables
-- These tables contain essential system data and should NOT be cleared:
-- - companies (user company data and Auth0 integration)
-- - All table structures and indexes
-- - Database schema and constraints

SELECT 'PRESERVING SYSTEM DATA' as status;

-- Verify companies table exists and has data
SELECT COUNT(*) as company_count FROM companies;

-- ============================================================================
-- STEP 3: CLEAR BUSINESS DATA IN DEPENDENCY ORDER
-- ============================================================================

-- IMPORTANT: Tables must be cleared in reverse dependency order to avoid
-- foreign key constraint violations

SELECT 'STARTING BUSINESS DATA RESET' as status;

-- ============================================================================
-- LEVEL 1: LEAF TABLES (No dependencies on other business tables)
-- ============================================================================

-- Financial Analytics and Reporting Tables
TRUNCATE TABLE currency_conversion_cache CASCADE;
TRUNCATE TABLE exchange_rate_history CASCADE;
TRUNCATE TABLE export_revenue_analytics CASCADE;
TRUNCATE TABLE cash_flow_forecasts CASCADE;
TRUNCATE TABLE currency_risk_assessments CASCADE;
TRUNCATE TABLE financial_alerts CASCADE;
TRUNCATE TABLE multi_currency_transactions CASCADE;
TRUNCATE TABLE currency_hedging_positions CASCADE;
TRUNCATE TABLE financial_transactions CASCADE;
TRUNCATE TABLE currency_exposure CASCADE;

-- Advanced MRP System Tables
TRUNCATE TABLE forecast_parameters CASCADE;
TRUNCATE TABLE supplier_lead_times CASCADE;
TRUNCATE TABLE safety_stock_levels CASCADE;
TRUNCATE TABLE container_load_optimization CASCADE;
TRUNCATE TABLE mrp_recommendations CASCADE;
TRUNCATE TABLE procurement_alerts CASCADE;

-- Quality Control Detail Tables
TRUNCATE TABLE inspection_results CASCADE;
TRUNCATE TABLE quality_defects CASCADE;
TRUNCATE TABLE quality_certificates CASCADE;
TRUNCATE TABLE quality_standards CASCADE;

-- Raw Materials Detail Tables
TRUNCATE TABLE material_consumption CASCADE;
TRUNCATE TABLE bill_of_materials CASCADE;

-- Shipping Detail Tables
TRUNCATE TABLE shipping_tracking CASCADE;
TRUNCATE TABLE shipping_documents CASCADE;

-- Export Documentation Detail Tables
TRUNCATE TABLE documents CASCADE;

-- ============================================================================
-- LEVEL 2: INTERMEDIATE TABLES (Depend on Level 1)
-- ============================================================================

-- Invoice Tables (depend on contracts and customers/suppliers)
TRUNCATE TABLE ar_invoices CASCADE;
TRUNCATE TABLE ap_invoices CASCADE;

-- Export Declaration Items (depend on declarations and products)
TRUNCATE TABLE declaration_items CASCADE;

-- Shipment Items (depend on shipments and products)
TRUNCATE TABLE shipment_items CASCADE;

-- Container Cost Allocation (depends on shipments and contracts)
TRUNCATE TABLE container_cost_allocation CASCADE;

-- Stock Transactions (depend on stock lots and work orders)
TRUNCATE TABLE stock_txns CASCADE;

-- Work Operations (depend on work orders)
TRUNCATE TABLE work_operations CASCADE;

-- Contract Items (depend on contracts and products)
TRUNCATE TABLE sales_contract_items CASCADE;
TRUNCATE TABLE purchase_contract_items CASCADE;

-- Raw Material Lots (depend on raw materials)
TRUNCATE TABLE raw_material_lots CASCADE;

-- Quality Inspections (depend on work orders)
TRUNCATE TABLE quality_inspections CASCADE;

-- Advanced MRP Tables (depend on products and forecasts)
TRUNCATE TABLE demand_forecasts CASCADE;
TRUNCATE TABLE procurement_plans CASCADE;

-- Product Pricing History (depends on products)
TRUNCATE TABLE product_pricing_history CASCADE;

-- ============================================================================
-- LEVEL 3: CORE BUSINESS TABLES (Depend on Level 2)
-- ============================================================================

-- Export Declarations (depend on sales contracts)
TRUNCATE TABLE declarations CASCADE;

-- Shipments (depend on sales contracts and customers)
TRUNCATE TABLE shipments CASCADE;

-- Stock Lots (depend on products and work orders)
TRUNCATE TABLE stock_lots CASCADE;

-- Work Orders (depend on sales contracts and products)
TRUNCATE TABLE work_orders CASCADE;

-- Contracts (depend on customers/suppliers and products)
TRUNCATE TABLE sales_contracts CASCADE;
TRUNCATE TABLE purchase_contracts CASCADE;

-- Contract Templates (depend on companies only)
TRUNCATE TABLE contract_templates CASCADE;

-- Samples (depend on customers and products)
TRUNCATE TABLE samples CASCADE;

-- Currency System Tables (depend on companies only)
TRUNCATE TABLE currencies CASCADE;

-- ============================================================================
-- LEVEL 4: FOUNDATION TABLES (Master Data)
-- ============================================================================

-- Raw Materials (depend on suppliers and companies)
TRUNCATE TABLE raw_materials CASCADE;

-- Products (depend on companies only)
TRUNCATE TABLE products CASCADE;

-- Customers (depend on companies only)
TRUNCATE TABLE customers CASCADE;

-- Suppliers (depend on companies only)
TRUNCATE TABLE suppliers CASCADE;

-- ============================================================================
-- STEP 4: VERIFICATION
-- ============================================================================

SELECT 'RESET VERIFICATION' as status;

-- Verify all business tables are empty
SELECT 
  'customers' as table_name, COUNT(*) as record_count FROM customers
UNION ALL SELECT 'suppliers', COUNT(*) FROM suppliers
UNION ALL SELECT 'products', COUNT(*) FROM products
UNION ALL SELECT 'samples', COUNT(*) FROM samples
UNION ALL SELECT 'sales_contracts', COUNT(*) FROM sales_contracts
UNION ALL SELECT 'purchase_contracts', COUNT(*) FROM purchase_contracts
UNION ALL SELECT 'work_orders', COUNT(*) FROM work_orders
UNION ALL SELECT 'quality_inspections', COUNT(*) FROM quality_inspections
UNION ALL SELECT 'stock_lots', COUNT(*) FROM stock_lots
UNION ALL SELECT 'shipments', COUNT(*) FROM shipments
UNION ALL SELECT 'declarations', COUNT(*) FROM declarations
UNION ALL SELECT 'ar_invoices', COUNT(*) FROM ar_invoices
UNION ALL SELECT 'ap_invoices', COUNT(*) FROM ap_invoices
ORDER BY table_name;

-- Expected result: All counts should be 0

-- Verify system tables are preserved
SELECT 
  'companies' as table_name, COUNT(*) as record_count FROM companies;

-- Expected result: Should show your company records (NOT 0)

-- ============================================================================
-- STEP 5: RESET SEQUENCES (Optional)
-- ============================================================================

-- Reset any sequences to start fresh (if using sequences for numbering)
-- Note: Manufacturing ERP uses custom numbering, so this is optional

SELECT 'RESET COMPLETE' as status;

-- ============================================================================
-- RESET SUMMARY
-- ============================================================================

SELECT 
  'DATABASE RESET COMPLETED SUCCESSFULLY' as message,
  current_timestamp as completed_at,
  current_database() as database_name;

-- ============================================================================
-- NEXT STEPS
-- ============================================================================

-- After successful reset:
-- 1. Verify all business tables show 0 records
-- 2. Verify companies table still has your data
-- 3. Start comprehensive testing using COMPREHENSIVE_ERP_TESTING_GUIDE.md
-- 4. Begin with fresh customer, supplier, and product creation
-- 5. Follow the complete business workflow testing protocol

-- ============================================================================
