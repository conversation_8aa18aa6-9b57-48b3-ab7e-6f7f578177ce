#!/usr/bin/env node

/**
 * Manufacturing ERP AI Translation Script
 * 
 * Uses i18n-ai to automatically translate hardcoded strings with
 * Manufacturing ERP-specific context and terminology.
 * 
 * ZERO BREAKING CHANGES: Creates new translations alongside existing system.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG_FILE = 'i18n-ai.config.js';
const OUTPUT_DIR = 'i18n-temp/ai-generated';
const BATCH_SIZE = 50;

// Load hardcoded strings analysis
function loadHardcodedStrings() {
  try {
    const analysisFile = 'i18n-temp/hardcoded-strings-detailed.json';
    if (!fs.existsSync(analysisFile)) {
      console.error('❌ Hardcoded strings analysis not found. Run i18n-audit.js first.');
      process.exit(1);
    }

    const analysis = JSON.parse(fs.readFileSync(analysisFile, 'utf8'));
    console.log(`✅ Loaded analysis: ${analysis.summary.totalHardcodedStrings} strings to process`);

    return analysis;
  } catch (error) {
    console.error('❌ Failed to load hardcoded strings analysis:', error.message);
    process.exit(1);
  }
}

// Create output directory
function createOutputDirectory() {
  try {
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }
    console.log(`✅ Created output directory: ${OUTPUT_DIR}`);
    return true;
  } catch (error) {
    console.error('❌ Failed to create output directory:', error.message);
    return false;
  }
}

// Check if OpenAI API key is available
function checkApiKey() {
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    console.log('⚠️  OpenAI API key not found in environment variables.');
    console.log('');
    console.log('To use AI translation, please:');
    console.log('1. Get an OpenAI API key from https://platform.openai.com/api-keys');
    console.log('2. Set it as an environment variable:');
    console.log('   export OPENAI_API_KEY="your-api-key-here"');
    console.log('');
    console.log('For now, we\'ll create translation templates for manual processing.');
    return false;
  }

  console.log('✅ OpenAI API key found');
  return true;
}

// Create translation templates for manual processing
function createTranslationTemplates(analysis) {
  try {
    const templates = {
      critical: [],
      high: [],
      medium: [],
      low: []
    };

    // Process each priority category
    Object.entries(analysis.priorityCategories).forEach(([priority, category]) => {
      category.examples.forEach(example => {
        templates[priority].push({
          file: example.file,
          line: example.line,
          text: example.text,
          context: example.context,
          suggestedKey: example.suggestedKey,
          englishText: example.suggestedTranslation.en,
          chineseText: example.suggestedTranslation.zh,
          needsReview: true,
          category: priority
        });
      });
    });

    // Save templates
    Object.entries(templates).forEach(([priority, items]) => {
      if (items.length > 0) {
        const templateFile = path.join(OUTPUT_DIR, `${priority}-priority-translations.json`);
        fs.writeFileSync(templateFile, JSON.stringify(items, null, 2));
        console.log(`✅ Created ${priority} priority template: ${templateFile} (${items.length} items)`);
      }
    });

    // Create CSV for manual review
    const csvRows = ['Priority,File,Line,English Text,Suggested Chinese,Suggested Key,Context'];

    Object.entries(templates).forEach(([priority, items]) => {
      items.forEach(item => {
        const row = [
          priority,
          item.file,
          item.line,
          `"${item.englishText.replace(/"/g, '""')}"`,
          `"${item.chineseText.replace(/"/g, '""')}"`,
          item.suggestedKey,
          `"${item.context.replace(/"/g, '""')}"`
        ].join(',');
        csvRows.push(row);
      });
    });

    const csvFile = path.join(OUTPUT_DIR, 'translation-templates.csv');
    fs.writeFileSync(csvFile, csvRows.join('\n'));
    console.log(`✅ Created CSV template: ${csvFile}`);

    return templates;

  } catch (error) {
    console.error('❌ Failed to create translation templates:', error.message);
    return null;
  }
}

// Run AI translation (if API key available)
async function runAiTranslation() {
  try {
    console.log('🤖 Running AI translation...');

    // Check if i18n-ai is properly installed
    try {
      execSync('npx i18n-ai --version', { stdio: 'pipe' });
    } catch (error) {
      console.log('⚠️  i18n-ai not found or not working properly.');
      console.log('Creating manual translation templates instead...');
      return false;
    }

    // Run i18n-ai with our configuration
    const command = `npx i18n-ai scan --config ${CONFIG_FILE} --output ${OUTPUT_DIR}`;
    console.log(`Running: ${command}`);

    const result = execSync(command, {
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 300000 // 5 minutes timeout
    });

    console.log('✅ AI translation completed');
    console.log(result);

    return true;

  } catch (error) {
    console.log('⚠️  AI translation failed:', error.message);
    console.log('Creating manual translation templates instead...');
    return false;
  }
}

// Create integration script
function createIntegrationScript() {
  const integrationScript = `#!/usr/bin/env node

/**
 * Manufacturing ERP Translation Integration Script
 * 
 * Integrates AI-generated or manually reviewed translations back into
 * the existing i18n-provider.tsx system.
 * 
 * ZERO BREAKING CHANGES: Only adds new translations, preserves existing ones.
 */

const fs = require('fs');
const path = require('path');

const AI_OUTPUT_DIR = '${OUTPUT_DIR}';
const I18N_PROVIDER = 'components/i18n-provider.tsx';
const BACKUP_DIR = 'i18n-backup';

console.log('🔄 Starting translation integration...');

// Load AI-generated translations
const translationFiles = fs.readdirSync(AI_OUTPUT_DIR)
  .filter(file => file.endsWith('.json'))
  .map(file => path.join(AI_OUTPUT_DIR, file));

console.log(\`📁 Found \${translationFiles.length} translation files\`);

// TODO: Implement integration logic
// 1. Load existing i18n-provider.tsx
// 2. Parse new translations
// 3. Merge without conflicts
// 4. Update i18n-provider.tsx
// 5. Create backup

console.log('⚠️  Integration script template created.');
console.log('Manual implementation required based on translation results.');
`;

  const scriptPath = path.join(OUTPUT_DIR, 'integrate-translations.js');
  fs.writeFileSync(scriptPath, integrationScript);
  fs.chmodSync(scriptPath, '755');

  console.log(`✅ Created integration script: ${scriptPath}`);
  return scriptPath;
}

// Main execution function
async function main() {
  console.log('🤖 Starting Manufacturing ERP AI Translation Process...\n');

  // Load analysis
  const analysis = loadHardcodedStrings();

  // Create output directory
  if (!createOutputDirectory()) {
    process.exit(1);
  }

  // Check API key availability
  const hasApiKey = checkApiKey();

  // Create translation templates (always useful)
  console.log('\n📝 Creating translation templates...');
  const templates = createTranslationTemplates(analysis);

  if (!templates) {
    process.exit(1);
  }

  // Try AI translation if API key is available
  let aiSuccess = false;
  if (hasApiKey) {
    console.log('\n🤖 Attempting AI translation...');
    aiSuccess = await runAiTranslation();
  }

  // Create integration script
  console.log('\n🔧 Creating integration tools...');
  const integrationScript = createIntegrationScript();

  // Summary
  console.log('\n📋 TRANSLATION SETUP SUMMARY');
  console.log('='.repeat(50));
  console.log('Output directory:', OUTPUT_DIR);
  console.log('Translation templates:', Object.keys(templates).length, 'priority levels');
  console.log('AI translation:', aiSuccess ? 'Completed' : 'Manual templates created');
  console.log('Integration script:', integrationScript);

  console.log('\n🎯 NEXT STEPS:');
  if (aiSuccess) {
    console.log('1. Review AI-generated translations in output directory');
    console.log('2. Validate technical terminology accuracy');
    console.log('3. Run integration script to merge translations');
  } else {
    console.log('1. Review translation templates in output directory');
    console.log('2. Complete manual translations using provided suggestions');
    console.log('3. Set up OpenAI API key for future AI assistance');
  }

  console.log('4. Test translations in development environment');
  console.log('5. Deploy incrementally to production');

  console.log('\n✅ Translation setup completed successfully!');
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
