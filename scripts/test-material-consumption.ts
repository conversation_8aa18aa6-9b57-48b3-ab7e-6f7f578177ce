/**
 * Manufacturing ERP - Material Consumption Integration Test
 * 
 * Professional test script to demonstrate the complete material consumption workflow
 * This script creates test data and demonstrates the integration between:
 * - Raw Materials
 * - Bill of Materials (BOM)
 * - Work Orders
 * - Material Consumption
 * 
 * <AUTHOR> ERP Developer
 */

import { db } from "../lib/db"
import { 
  companies, 
  suppliers, 
  rawMaterials, 
  rawMaterialLots, 
  products, 
  billOfMaterials,
  customers,
  salesContracts,
  workOrders
} from "../lib/schema-postgres"
import { MaterialConsumptionService } from "../lib/services/material-consumption-service"
import { uid } from "../lib/utils"

// Test configuration
const TEST_COMPANY_ID = "comp_test_material_consumption"
const TEST_USER_ID = "user_test_material_consumption"

async function setupTestData() {
  console.log("🏗️  Setting up test data for Material Consumption workflow...")

  // 1. Create test company
  await db.insert(companies).values({
    id: TEST_COMPANY_ID,
    name: "Test Manufacturing Co",
    email: "<EMAIL>",
    phone: "******-0123",
    address: "123 Test Street",
    city: "Test City",
    state: "TS",
    postal_code: "12345",
    country: "Test Country"
  }).onConflictDoNothing()

  // 2. Create test supplier
  const supplierId = uid("sup")
  await db.insert(suppliers).values({
    id: supplierId,
    company_id: TEST_COMPANY_ID,
    name: "Premium Materials Supplier",
    email: "<EMAIL>",
    phone: "******-0456",
    address: "456 Supplier Ave"
  })

  // 3. Create raw materials
  const cottonYarnId = uid("rm")
  const polyesterFabricId = uid("rm")
  
  await db.insert(rawMaterials).values([
    {
      id: cottonYarnId,
      company_id: TEST_COMPANY_ID,
      sku: "YARN-COTTON-30S",
      name: "Cotton Yarn 30s",
      category: "yarn",
      unit: "kg",
      primary_supplier_id: supplierId,
      composition: "100% Cotton",
      quality_grade: "Premium",
      standard_cost: "4.50",
      currency: "USD",
      reorder_point: "100",
      max_stock_level: "1000",
      inspection_required: "true"
    },
    {
      id: polyesterFabricId,
      company_id: TEST_COMPANY_ID,
      sku: "FABRIC-POLY-150GSM",
      name: "Polyester Fabric 150GSM",
      category: "fabric",
      unit: "meters",
      primary_supplier_id: supplierId,
      composition: "100% Polyester",
      quality_grade: "Standard",
      standard_cost: "3.20",
      currency: "USD",
      reorder_point: "50",
      max_stock_level: "500",
      inspection_required: "true"
    }
  ])

  // 4. Create material lots (inventory)
  await db.insert(rawMaterialLots).values([
    {
      id: uid("rml"),
      company_id: TEST_COMPANY_ID,
      raw_material_id: cottonYarnId,
      lot_number: "LOT-COTTON-001",
      supplier_id: supplierId,
      qty: "500", // 500 kg available
      location: "Warehouse-A-01",
      unit_cost: "4.50",
      total_cost: "2250.00",
      currency: "USD",
      received_date: "2024-01-15",
      quality_status: "approved",
      status: "available"
    },
    {
      id: uid("rml"),
      company_id: TEST_COMPANY_ID,
      raw_material_id: polyesterFabricId,
      lot_number: "LOT-POLY-001",
      supplier_id: supplierId,
      qty: "200", // 200 meters available
      location: "Warehouse-A-02",
      unit_cost: "3.20",
      total_cost: "640.00",
      currency: "USD",
      received_date: "2024-01-16",
      quality_status: "approved",
      status: "available"
    }
  ])

  // 5. Create test product
  const productId = uid("prod")
  await db.insert(products).values({
    id: productId,
    company_id: TEST_COMPANY_ID,
    sku: "SHIRT-COTTON-M",
    name: "Cotton Shirt - Medium",
    category: "Apparel",
    unit: "pieces",
    description: "Premium cotton shirt, medium size"
  })

  // 6. Create Bill of Materials (BOM)
  await db.insert(billOfMaterials).values([
    {
      id: uid("bom"),
      company_id: TEST_COMPANY_ID,
      product_id: productId,
      raw_material_id: cottonYarnId,
      qty_required: "0.5", // 0.5 kg cotton yarn per shirt
      unit: "kg",
      waste_factor: "0.10", // 10% waste factor
      status: "active"
    },
    {
      id: uid("bom"),
      company_id: TEST_COMPANY_ID,
      product_id: productId,
      raw_material_id: polyesterFabricId,
      qty_required: "1.2", // 1.2 meters polyester fabric per shirt
      unit: "meters",
      waste_factor: "0.05", // 5% waste factor
      status: "active"
    }
  ])

  // 7. Create test customer and sales contract
  const customerId = uid("cust")
  await db.insert(customers).values({
    id: customerId,
    company_id: TEST_COMPANY_ID,
    name: "Fashion Retailer Inc",
    email: "<EMAIL>",
    phone: "******-0789"
  })

  const contractId = uid("sc")
  await db.insert(salesContracts).values({
    id: contractId,
    company_id: TEST_COMPANY_ID,
    customer_id: customerId,
    contract_number: "SC-2024-001",
    total_value: "5000.00",
    currency: "USD",
    status: "approved"
  })

  // 8. Create work order
  const workOrderId = uid("wo")
  await db.insert(workOrders).values({
    id: workOrderId,
    company_id: TEST_COMPANY_ID,
    number: "WO-2024-001",
    sales_contract_id: contractId,
    product_id: productId,
    qty: "100", // Produce 100 shirts
    due_date: "2024-02-15",
    status: "pending",
    priority: "normal",
    notes: "Test production run for material consumption"
  })

  console.log("✅ Test data setup completed!")
  
  return {
    companyId: TEST_COMPANY_ID,
    userId: TEST_USER_ID,
    workOrderId,
    productId,
    cottonYarnId,
    polyesterFabricId
  }
}

async function testMaterialAvailabilityCheck(companyId: string, productId: string) {
  console.log("\n🔍 Testing Material Availability Check...")
  
  const plannedQty = 100 // 100 shirts
  const availabilityResult = await MaterialConsumptionService.checkMaterialAvailability(
    companyId,
    productId,
    plannedQty
  )

  console.log("📊 Material Availability Results:")
  console.log(`   Available: ${availabilityResult.available}`)
  console.log(`   Shortages: ${availabilityResult.shortages.length}`)
  
  if (availabilityResult.shortages.length > 0) {
    console.log("   Shortage Details:")
    availabilityResult.shortages.forEach(shortage => {
      console.log(`     - ${shortage.materialName}: Need ${shortage.requiredQty}, Have ${shortage.availableQty}, Short ${shortage.shortageQty}`)
    })
  }

  return availabilityResult
}

async function testMaterialConsumption(companyId: string, userId: string, workOrderId: string) {
  console.log("\n🏭 Testing Material Consumption Process...")
  
  const materialConsumptionService = new MaterialConsumptionService({
    companyId,
    userId,
    workOrderId,
    completedQty: 100 // Complete 100 shirts
  })

  const consumptionResult = await materialConsumptionService.processWorkOrderMaterialConsumption()

  console.log("📈 Material Consumption Results:")
  console.log(`   Success: ${consumptionResult.success}`)
  console.log(`   Total Material Cost: $${consumptionResult.totalMaterialCost}`)
  console.log(`   Materials Consumed: ${consumptionResult.consumedMaterials.length}`)
  
  if (consumptionResult.consumedMaterials.length > 0) {
    console.log("   Consumption Details:")
    consumptionResult.consumedMaterials.forEach(material => {
      console.log(`     - ${material.materialName}: ${material.totalQtyConsumed} units, $${material.totalCost}`)
      material.allocations.forEach(allocation => {
        console.log(`       * Lot ${allocation.lotNumber}: ${allocation.allocatedQty} units @ $${allocation.unitCost}/unit`)
      })
    })
  }

  if (consumptionResult.shortages.length > 0) {
    console.log("   Shortages:")
    consumptionResult.shortages.forEach(shortage => {
      console.log(`     - ${shortage.materialName}: Short ${shortage.shortageQty} units`)
    })
  }

  if (consumptionResult.warnings.length > 0) {
    console.log("   Warnings:")
    consumptionResult.warnings.forEach(warning => {
      console.log(`     - ${warning}`)
    })
  }

  return consumptionResult
}

async function cleanupTestData() {
  console.log("\n🧹 Cleaning up test data...")
  
  try {
    // Delete in reverse order of dependencies
    await db.delete(workOrders).where(eq(workOrders.company_id, TEST_COMPANY_ID))
    await db.delete(salesContracts).where(eq(salesContracts.company_id, TEST_COMPANY_ID))
    await db.delete(customers).where(eq(customers.company_id, TEST_COMPANY_ID))
    await db.delete(billOfMaterials).where(eq(billOfMaterials.company_id, TEST_COMPANY_ID))
    await db.delete(rawMaterialLots).where(eq(rawMaterialLots.company_id, TEST_COMPANY_ID))
    await db.delete(rawMaterials).where(eq(rawMaterials.company_id, TEST_COMPANY_ID))
    await db.delete(suppliers).where(eq(suppliers.company_id, TEST_COMPANY_ID))
    await db.delete(products).where(eq(products.company_id, TEST_COMPANY_ID))
    await db.delete(companies).where(eq(companies.id, TEST_COMPANY_ID))
    
    console.log("✅ Test data cleanup completed!")
  } catch (error) {
    console.error("❌ Error during cleanup:", error)
  }
}

// Main test execution
async function runMaterialConsumptionTest() {
  console.log("🚀 Starting Material Consumption Integration Test")
  console.log("=" .repeat(60))

  try {
    // Setup test data
    const testData = await setupTestData()

    // Test material availability check
    const availabilityResult = await testMaterialAvailabilityCheck(
      testData.companyId, 
      testData.productId
    )

    // Test material consumption (only if materials are available)
    if (availabilityResult.available) {
      const consumptionResult = await testMaterialConsumption(
        testData.companyId,
        testData.userId,
        testData.workOrderId
      )

      console.log("\n🎉 Material Consumption Integration Test PASSED!")
      console.log(`   Total Cost Allocated: $${consumptionResult.totalMaterialCost}`)
    } else {
      console.log("\n⚠️  Material shortages detected - consumption test skipped")
    }

  } catch (error) {
    console.error("\n❌ Material Consumption Integration Test FAILED:")
    console.error(error)
  } finally {
    // Always cleanup test data
    await cleanupTestData()
  }

  console.log("=" .repeat(60))
  console.log("🏁 Material Consumption Integration Test Complete")
}

// Export for use in other scripts
export { runMaterialConsumptionTest }

// Run test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMaterialConsumptionTest()
}
