-- Manufacturing ERP - CRITICAL Production Database Migration
-- Generated: January 2025
-- Purpose: Synchronize production Supabase with local development schema
-- 
-- ⚠️  CRITICAL: This migration MUST be executed in production BEFORE GitHub commit
-- ⚠️  FAILURE TO RUN THIS WILL CAUSE COMPLETE SYSTEM FAILURE IN PRODUCTION

BEGIN;

-- ============================================================================
-- CRITICAL MIGRATION: STOCK_LOTS TABLE ENHANCEMENTS
-- ============================================================================

-- Add missing columns to stock_lots table
ALTER TABLE stock_lots ADD COLUMN IF NOT EXISTS status text DEFAULT 'available';
ALTER TABLE stock_lots ADD COLUMN IF NOT EXISTS quality_status text DEFAULT 'pending';
ALTER TABLE stock_lots ADD COLUMN IF NOT EXISTS inspection_id text;
ALTER TABLE stock_lots ADD COLUMN IF NOT EXISTS quality_approved_date text;
ALTER TABLE stock_lots ADD COLUMN IF NOT EXISTS quality_approved_by text;
ALTER TABLE stock_lots ADD COLUMN IF NOT EXISTS quality_notes text;
ALTER TABLE stock_lots ADD COLUMN IF NOT EXISTS work_order_id text;

-- Add foreign key constraints for stock_lots
ALTER TABLE stock_lots ADD CONSTRAINT fk_stock_lots_inspection_id 
  FOREIGN KEY (inspection_id) REFERENCES quality_inspections(id);

ALTER TABLE stock_lots ADD CONSTRAINT fk_stock_lots_work_order_id 
  FOREIGN KEY (work_order_id) REFERENCES work_orders(id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_stock_lots_quality_status ON stock_lots(quality_status);
CREATE INDEX IF NOT EXISTS idx_stock_lots_inspection_id ON stock_lots(inspection_id);
CREATE INDEX IF NOT EXISTS idx_stock_lots_work_order_id ON stock_lots(work_order_id);
CREATE INDEX IF NOT EXISTS idx_stock_lots_status ON stock_lots(status);

-- ============================================================================
-- CRITICAL MIGRATION: STOCK_TXNS TABLE ENHANCEMENTS
-- ============================================================================

-- Add missing columns to stock_txns table
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS workflow_trigger text;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS reference_id text;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS location text;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS notes text;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS transaction_type text DEFAULT 'manual';
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS from_location text;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS to_location text;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS reason_code text;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS approval_status text DEFAULT 'approved';
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS approved_by text;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS approved_at timestamp with time zone;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS created_by text;
ALTER TABLE stock_txns ADD COLUMN IF NOT EXISTS updated_at timestamp with time zone DEFAULT now();

-- Create indexes for performance on stock_txns
CREATE INDEX IF NOT EXISTS idx_stock_txns_location ON stock_txns(location);
CREATE INDEX IF NOT EXISTS idx_stock_txns_from_location ON stock_txns(from_location);
CREATE INDEX IF NOT EXISTS idx_stock_txns_to_location ON stock_txns(to_location);
CREATE INDEX IF NOT EXISTS idx_stock_txns_workflow_trigger ON stock_txns(workflow_trigger);
CREATE INDEX IF NOT EXISTS idx_stock_txns_reference_id ON stock_txns(reference_id);
CREATE INDEX IF NOT EXISTS idx_stock_txns_transaction_type ON stock_txns(transaction_type);
CREATE INDEX IF NOT EXISTS idx_stock_txns_approval_status ON stock_txns(approval_status);

-- ============================================================================
-- DATA INTEGRITY VERIFICATION
-- ============================================================================

-- Verify all critical columns exist
DO $$
DECLARE
    missing_columns text[] := ARRAY[]::text[];
BEGIN
    -- Check stock_lots columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stock_lots' AND column_name = 'quality_status') THEN
        missing_columns := array_append(missing_columns, 'stock_lots.quality_status');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stock_lots' AND column_name = 'work_order_id') THEN
        missing_columns := array_append(missing_columns, 'stock_lots.work_order_id');
    END IF;
    
    -- Check stock_txns columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stock_txns' AND column_name = 'location') THEN
        missing_columns := array_append(missing_columns, 'stock_txns.location');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stock_txns' AND column_name = 'from_location') THEN
        missing_columns := array_append(missing_columns, 'stock_txns.from_location');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stock_txns' AND column_name = 'to_location') THEN
        missing_columns := array_append(missing_columns, 'stock_txns.to_location');
    END IF;
    
    -- Report missing columns
    IF array_length(missing_columns, 1) > 0 THEN
        RAISE EXCEPTION 'Migration failed: Missing columns: %', array_to_string(missing_columns, ', ');
    ELSE
        RAISE NOTICE 'SUCCESS: All critical columns added successfully';
    END IF;
END $$;

-- ============================================================================
-- PRODUCTION DATA COMPATIBILITY
-- ============================================================================

-- Update existing stock_lots records with default values
UPDATE stock_lots 
SET 
    status = 'available',
    quality_status = 'approved'  -- Assume existing inventory is approved
WHERE status IS NULL OR quality_status IS NULL;

-- Update existing stock_txns records with default values
UPDATE stock_txns 
SET 
    transaction_type = 'manual',
    approval_status = 'approved'  -- Assume existing transactions are approved
WHERE transaction_type IS NULL OR approval_status IS NULL;

-- ============================================================================
-- FINAL VERIFICATION
-- ============================================================================

-- Count records to ensure no data loss
SELECT 
    'stock_lots' as table_name,
    count(*) as record_count,
    count(CASE WHEN quality_status IS NOT NULL THEN 1 END) as quality_status_count,
    count(CASE WHEN status IS NOT NULL THEN 1 END) as status_count
FROM stock_lots

UNION ALL

SELECT 
    'stock_txns' as table_name,
    count(*) as record_count,
    count(CASE WHEN transaction_type IS NOT NULL THEN 1 END) as transaction_type_count,
    count(CASE WHEN approval_status IS NOT NULL THEN 1 END) as approval_status_count
FROM stock_txns;

COMMIT;

-- ============================================================================
-- POST-MIGRATION VERIFICATION QUERIES
-- ============================================================================

-- Run these queries after migration to verify success:

-- 1. Verify stock_lots schema
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'stock_lots' 
-- ORDER BY ordinal_position;

-- 2. Verify stock_txns schema  
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'stock_txns' 
-- ORDER BY ordinal_position;

-- 3. Verify data integrity
-- SELECT 'stock_lots' as table, count(*) as total_records, 
--        count(quality_status) as quality_status_populated,
--        count(status) as status_populated
-- FROM stock_lots
-- UNION ALL
-- SELECT 'stock_txns' as table, count(*) as total_records,
--        count(location) as location_populated,
--        count(transaction_type) as transaction_type_populated  
-- FROM stock_txns;

-- ============================================================================
-- ROLLBACK SCRIPT (Emergency Use Only)
-- ============================================================================

-- IF MIGRATION FAILS, RUN THIS ROLLBACK:
-- 
-- BEGIN;
-- 
-- -- Remove added columns from stock_lots
-- ALTER TABLE stock_lots DROP COLUMN IF EXISTS status;
-- ALTER TABLE stock_lots DROP COLUMN IF EXISTS quality_status;
-- ALTER TABLE stock_lots DROP COLUMN IF EXISTS inspection_id;
-- ALTER TABLE stock_lots DROP COLUMN IF EXISTS quality_approved_date;
-- ALTER TABLE stock_lots DROP COLUMN IF EXISTS quality_approved_by;
-- ALTER TABLE stock_lots DROP COLUMN IF EXISTS quality_notes;
-- ALTER TABLE stock_lots DROP COLUMN IF EXISTS work_order_id;
-- 
-- -- Remove added columns from stock_txns
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS workflow_trigger;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS reference_id;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS location;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS notes;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS transaction_type;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS from_location;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS to_location;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS reason_code;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS approval_status;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS approved_by;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS approved_at;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE stock_txns DROP COLUMN IF EXISTS updated_at;
-- 
-- COMMIT;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================
