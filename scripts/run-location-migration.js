#!/usr/bin/env node

/**
 * Manufacturing ERP - Location Migration Execution Script
 * 
 * Simple Node.js script to execute the location data migration
 * Can be run from command line: node scripts/run-location-migration.js
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Manufacturing ERP - Location Data Migration');
console.log('=' .repeat(60));

try {
  // Change to project root directory
  const projectRoot = path.resolve(__dirname, '..');
  process.chdir(projectRoot);
  
  console.log('📁 Project directory:', projectRoot);
  console.log('⏰ Starting migration at:', new Date().toISOString());
  
  // Execute the TypeScript migration script
  console.log('\n🔄 Executing location data migration...');
  
  const result = execSync('npx tsx scripts/migrate-location-data.ts', {
    stdio: 'inherit',
    encoding: 'utf8'
  });
  
  console.log('\n✅ Migration completed successfully!');
  console.log('⏰ Completed at:', new Date().toISOString());
  
} catch (error) {
  console.error('\n❌ Migration failed:', error.message);
  console.error('⏰ Failed at:', new Date().toISOString());
  process.exit(1);
}
