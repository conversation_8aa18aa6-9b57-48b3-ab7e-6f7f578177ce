#!/usr/bin/env node

/**
 * Manufacturing ERP Translation Extraction Script
 * 
 * Extracts all translations from components/i18n-provider.tsx into separate
 * JSON files for easier management and AI processing.
 * 
 * ZERO BREAKING CHANGES: Creates new files alongside existing system.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const I18N_FILE = 'components/i18n-provider.tsx';
const OUTPUT_DIR = 'locales';
const TEMP_DIR = 'i18n-temp';

// Create output directories
function createDirectories() {
  try {
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }
    
    if (!fs.existsSync(TEMP_DIR)) {
      fs.mkdirSync(TEMP_DIR, { recursive: true });
    }
    
    console.log(`✅ Created directories: ${OUTPUT_DIR}, ${TEMP_DIR}`);
    return true;
  } catch (error) {
    console.error('❌ Failed to create directories:', error.message);
    return false;
  }
}

// Parse translation object from TypeScript code
function parseTranslationObject(content, objectName) {
  try {
    // Find the object definition
    const regex = new RegExp(`const ${objectName}: Dict = \\{([\\s\\S]*?)\\}\\s*(?=const|$)`, 'm');
    const match = content.match(regex);
    
    if (!match) {
      console.error(`❌ Could not find ${objectName} object in i18n file`);
      return null;
    }
    
    const objectContent = match[1];
    const translations = {};
    
    // Parse key-value pairs
    const keyValueRegex = /"([^"]+)":\s*"([^"\\]*(\\.[^"\\]*)*)"/g;
    let keyMatch;
    
    while ((keyMatch = keyValueRegex.exec(objectContent)) !== null) {
      const key = keyMatch[1];
      const value = keyMatch[2].replace(/\\"/g, '"').replace(/\\n/g, '\n');
      translations[key] = value;
    }
    
    console.log(`✅ Extracted ${Object.keys(translations).length} keys from ${objectName}`);
    return translations;
    
  } catch (error) {
    console.error(`❌ Failed to parse ${objectName} object:`, error.message);
    return null;
  }
}

// Organize translations by category
function organizeTranslations(translations) {
  const organized = {
    common: {},
    navigation: {},
    dashboard: {},
    customers: {},
    suppliers: {},
    products: {},
    contracts: {},
    samples: {},
    workOrders: {},
    quality: {},
    inventory: {},
    shipping: {},
    export: {},
    financial: {},
    landing: {},
    errors: {},
    status: {},
    loading: {},
    other: {}
  };
  
  Object.entries(translations).forEach(([key, value]) => {
    // Categorize based on key prefix
    if (key.startsWith('common.')) {
      organized.common[key] = value;
    } else if (key.startsWith('nav.')) {
      organized.navigation[key] = value;
    } else if (key.startsWith('dashboard.')) {
      organized.dashboard[key] = value;
    } else if (key.startsWith('customers.')) {
      organized.customers[key] = value;
    } else if (key.startsWith('suppliers.')) {
      organized.suppliers[key] = value;
    } else if (key.startsWith('products.')) {
      organized.products[key] = value;
    } else if (key.startsWith('contracts.')) {
      organized.contracts[key] = value;
    } else if (key.startsWith('samples.')) {
      organized.samples[key] = value;
    } else if (key.startsWith('workOrders.')) {
      organized.workOrders[key] = value;
    } else if (key.startsWith('quality.')) {
      organized.quality[key] = value;
    } else if (key.startsWith('inventory.')) {
      organized.inventory[key] = value;
    } else if (key.startsWith('shipping.')) {
      organized.shipping[key] = value;
    } else if (key.startsWith('export.')) {
      organized.export[key] = value;
    } else if (key.startsWith('financial.') || key.startsWith('finance.')) {
      organized.financial[key] = value;
    } else if (key.startsWith('landing.')) {
      organized.landing[key] = value;
    } else if (key.startsWith('error.') || key.includes('error')) {
      organized.errors[key] = value;
    } else if (key.startsWith('status.')) {
      organized.status[key] = value;
    } else if (key.startsWith('loading.') || key.includes('loading')) {
      organized.loading[key] = value;
    } else {
      organized.other[key] = value;
    }
  });
  
  // Remove empty categories
  Object.keys(organized).forEach(category => {
    if (Object.keys(organized[category]).length === 0) {
      delete organized[category];
    }
  });
  
  return organized;
}

// Save translations to JSON files
function saveTranslations(translations, language) {
  try {
    const organized = organizeTranslations(translations);
    const savedFiles = [];
    
    // Save complete translation file
    const completeFile = path.join(OUTPUT_DIR, `${language}.json`);
    fs.writeFileSync(completeFile, JSON.stringify(translations, null, 2));
    savedFiles.push(completeFile);
    
    // Save organized category files
    Object.entries(organized).forEach(([category, categoryTranslations]) => {
      const categoryFile = path.join(OUTPUT_DIR, `${language}-${category}.json`);
      fs.writeFileSync(categoryFile, JSON.stringify(categoryTranslations, null, 2));
      savedFiles.push(categoryFile);
    });
    
    console.log(`✅ Saved ${savedFiles.length} ${language} translation files`);
    return savedFiles;
    
  } catch (error) {
    console.error(`❌ Failed to save ${language} translations:`, error.message);
    return [];
  }
}

// Create translation mapping for AI processing
function createTranslationMapping(enTranslations, zhTranslations) {
  try {
    const mapping = [];
    
    Object.keys(enTranslations).forEach(key => {
      const englishText = enTranslations[key];
      const chineseText = zhTranslations[key] || '';
      
      mapping.push({
        key,
        english: englishText,
        chinese: chineseText,
        category: key.split('.')[0],
        hasTranslation: !!chineseText,
        needsReview: false
      });
    });
    
    // Save mapping for AI processing
    const mappingFile = path.join(TEMP_DIR, 'translation-mapping.json');
    fs.writeFileSync(mappingFile, JSON.stringify(mapping, null, 2));
    
    console.log(`✅ Created translation mapping: ${mappingFile}`);
    console.log(`   Total entries: ${mapping.length}`);
    console.log(`   With Chinese: ${mapping.filter(m => m.hasTranslation).length}`);
    console.log(`   Missing Chinese: ${mapping.filter(m => !m.hasTranslation).length}`);
    
    return mappingFile;
    
  } catch (error) {
    console.error('❌ Failed to create translation mapping:', error.message);
    return null;
  }
}

// Generate CSV for manual review
function generateCSV(enTranslations, zhTranslations) {
  try {
    const csvRows = ['Key,English,Chinese,Category,Status'];
    
    Object.keys(enTranslations).forEach(key => {
      const english = enTranslations[key].replace(/"/g, '""'); // Escape quotes
      const chinese = (zhTranslations[key] || '').replace(/"/g, '""');
      const category = key.split('.')[0];
      const status = zhTranslations[key] ? 'Complete' : 'Missing';
      
      csvRows.push(`"${key}","${english}","${chinese}","${category}","${status}"`);
    });
    
    const csvFile = path.join(TEMP_DIR, 'translations.csv');
    fs.writeFileSync(csvFile, csvRows.join('\n'));
    
    console.log(`✅ Generated CSV file: ${csvFile}`);
    return csvFile;
    
  } catch (error) {
    console.error('❌ Failed to generate CSV:', error.message);
    return null;
  }
}

// Main extraction function
async function extractTranslations() {
  console.log('📤 Starting Translation Extraction...\n');
  
  // Check if i18n file exists
  if (!fs.existsSync(I18N_FILE)) {
    console.error(`❌ i18n file not found: ${I18N_FILE}`);
    process.exit(1);
  }
  
  // Create directories
  if (!createDirectories()) {
    process.exit(1);
  }
  
  // Read i18n file
  console.log('📖 Reading i18n file...');
  const content = fs.readFileSync(I18N_FILE, 'utf8');
  
  // Extract English translations
  console.log('🇺🇸 Extracting English translations...');
  const enTranslations = parseTranslationObject(content, 'en');
  if (!enTranslations) {
    process.exit(1);
  }
  
  // Extract Chinese translations
  console.log('🇨🇳 Extracting Chinese translations...');
  const zhTranslations = parseTranslationObject(content, 'zh');
  if (!zhTranslations) {
    process.exit(1);
  }
  
  // Save translations
  console.log('\n💾 Saving translation files...');
  const enFiles = saveTranslations(enTranslations, 'en');
  const zhFiles = saveTranslations(zhTranslations, 'zh');
  
  // Create mapping for AI processing
  console.log('\n🤖 Creating AI processing files...');
  const mappingFile = createTranslationMapping(enTranslations, zhTranslations);
  const csvFile = generateCSV(enTranslations, zhTranslations);
  
  // Summary
  console.log('\n📋 EXTRACTION SUMMARY');
  console.log('='.repeat(50));
  console.log(`Source file: ${I18N_FILE}`);
  console.log(`English keys: ${Object.keys(enTranslations).length}`);
  console.log(`Chinese keys: ${Object.keys(zhTranslations).length}`);
  console.log(`English files: ${enFiles.length}`);
  console.log(`Chinese files: ${zhFiles.length}`);
  console.log(`Mapping file: ${mappingFile || 'Failed'}`);
  console.log(`CSV file: ${csvFile || 'Failed'}`);
  
  console.log('\n✅ Translation extraction completed!');
  console.log('🔄 Original i18n-provider.tsx remains unchanged.');
  console.log('📁 New files created in locales/ and i18n-temp/ directories.');
  
  return {
    enTranslations,
    zhTranslations,
    enFiles,
    zhFiles,
    mappingFile,
    csvFile
  };
}

// Run extraction if called directly
if (require.main === module) {
  extractTranslations().catch(console.error);
}

module.exports = { extractTranslations };
