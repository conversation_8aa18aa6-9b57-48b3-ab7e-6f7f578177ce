#!/usr/bin/env node

/**
 * Manufacturing ERP AI Translation Processor
 * 
 * Processes identified hardcoded strings using AI translation with
 * Manufacturing ERP-specific context and terminology.
 * 
 * ZERO BREAKING CHANGES: All output goes to parallel workflow, existing system untouched.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const HARDCODED_ANALYSIS = 'i18n-temp/hardcoded-strings-detailed.json';
const PARALLEL_DIR = 'i18n-parallel';
const BATCH_SIZE = 20; // Process in small batches for quality

// Manufacturing ERP terminology database
const ERP_TERMINOLOGY = {
  // Manufacturing Terms
  'Work Order': '工单',
  'Bill of Materials': '物料清单',
  'BOM': 'BOM',
  'Quality Control': '质量控制',
  'Quality Inspection': '质量检验',
  'Raw Materials': '原材料',
  'Finished Goods': '成品',
  'Production Planning': '生产计划',
  'Manufacturing': '制造',
  'Production': '生产',
  'Sample': '样品',
  'Supplier': '供应商',
  'Customer': '客户',
  'Contract': '合同',
  'Invoice': '发票',
  'Inventory': '库存',
  'Stock': '库存',
  'Warehouse': '仓库',
  'Shipment': '货运',
  'Export': '出口',
  'Import': '进口',
  
  // Form Elements
  'Name': '名称',
  'Description': '描述',
  'Status': '状态',
  'Date': '日期',
  'Quantity': '数量',
  'Price': '价格',
  'Total': '总计',
  'Actions': '操作',
  'Edit': '编辑',
  'Delete': '删除',
  'Save': '保存',
  'Cancel': '取消',
  'Create': '创建',
  'Update': '更新',
  'Search': '搜索',
  'Filter': '筛选',
  
  // Status Values
  'Active': '活跃',
  'Inactive': '非活跃',
  'Pending': '待处理',
  'Approved': '已审批',
  'Rejected': '已拒绝',
  'Completed': '已完成',
  'In Progress': '进行中',
  'Draft': '草稿',
  
  // Business Terms
  'Revenue': '收入',
  'Profit': '利润',
  'Margin': '利润率',
  'Cost': '成本',
  'Expense': '费用',
  'Budget': '预算',
  'Forecast': '预测',
  'Analysis': '分析',
  'Report': '报告',
  'Dashboard': '仪表板'
};

// Load hardcoded strings analysis
function loadHardcodedAnalysis() {
  try {
    if (!fs.existsSync(HARDCODED_ANALYSIS)) {
      console.error('❌ Hardcoded strings analysis not found. Run i18n-audit.js first.');
      return null;
    }
    
    const analysis = JSON.parse(fs.readFileSync(HARDCODED_ANALYSIS, 'utf8'));
    console.log(`✅ Loaded analysis: ${analysis.summary.totalHardcodedStrings} strings to process`);
    
    return analysis;
  } catch (error) {
    console.error('❌ Failed to load analysis:', error.message);
    return null;
  }
}

// Generate translation key from text and context
function generateTranslationKey(text, context, category) {
  // Clean text for key generation
  let key = text
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .substring(0, 50); // Limit length
  
  // Add category prefix
  const prefixes = {
    'critical': 'forms',
    'high': 'dashboard',
    'medium': 'tables',
    'low': 'validation'
  };
  
  const prefix = prefixes[category] || 'common';
  
  // Determine specific prefix based on context
  if (context.includes('Label') || context.includes('FormLabel')) {
    return `${prefix}.labels.${key}`;
  } else if (context.includes('placeholder')) {
    return `${prefix}.placeholders.${key}`;
  } else if (context.includes('Button')) {
    return `buttons.${key}`;
  } else if (context.includes('TableHead')) {
    return `tables.headers.${key}`;
  } else if (context.includes('CardTitle')) {
    return `cards.titles.${key}`;
  } else if (context.includes('SelectItem')) {
    return `options.${key}`;
  } else if (context.includes('toast')) {
    return `messages.${key}`;
  } else if (context.includes('message:')) {
    return `validation.${key}`;
  }
  
  return `${prefix}.${key}`;
}

// Translate text using ERP terminology
function translateWithTerminology(englishText) {
  let chineseText = englishText;
  
  // Apply direct terminology mappings
  Object.entries(ERP_TERMINOLOGY).forEach(([english, chinese]) => {
    const regex = new RegExp(`\\b${english}\\b`, 'gi');
    chineseText = chineseText.replace(regex, chinese);
  });
  
  // Handle common patterns
  const patterns = [
    // Required field markers
    { pattern: /\s*\*\s*$/, replacement: ' *' },
    
    // Placeholder patterns
    { pattern: /^e\.g\.,?\s*/i, replacement: '例如：' },
    { pattern: /^Enter\s+/i, replacement: '输入' },
    { pattern: /^Search\s+/i, replacement: '搜索' },
    { pattern: /^Select\s+/i, replacement: '选择' },
    
    // Form patterns
    { pattern: /\s+\(Optional\)$/i, replacement: '（可选）' },
    { pattern: /\s+\(Required\)$/i, replacement: '（必填）' },
    
    // Action patterns
    { pattern: /^Add\s+New\s+/i, replacement: '添加新' },
    { pattern: /^Create\s+New\s+/i, replacement: '创建新' },
    { pattern: /^Edit\s+/i, replacement: '编辑' },
    { pattern: /^Delete\s+/i, replacement: '删除' },
    
    // Status patterns
    { pattern: /\s+Rate$/i, replacement: '率' },
    { pattern: /^Total\s+/i, replacement: '总' },
    { pattern: /^Average\s+/i, replacement: '平均' },
    { pattern: /^Overall\s+/i, replacement: '总体' }
  ];
  
  patterns.forEach(({ pattern, replacement }) => {
    chineseText = chineseText.replace(pattern, replacement);
  });
  
  // If no translation occurred, provide a basic translation
  if (chineseText === englishText) {
    // For very simple cases, provide basic translations
    const simpleTranslations = {
      'Loading...': '加载中...',
      'Error': '错误',
      'Success': '成功',
      'Warning': '警告',
      'Info': '信息',
      'Close': '关闭',
      'Open': '打开',
      'Yes': '是',
      'No': '否',
      'OK': '确定',
      'Apply': '应用',
      'Reset': '重置',
      'Clear': '清除',
      'Refresh': '刷新',
      'Export': '导出',
      'Import': '导入',
      'Print': '打印',
      'Download': '下载',
      'Upload': '上传'
    };
    
    chineseText = simpleTranslations[englishText] || englishText;
  }
  
  return chineseText;
}

// Process a batch of hardcoded strings
function processBatch(strings, batchNumber) {
  console.log(`🔄 Processing batch ${batchNumber} (${strings.length} strings)...`);
  
  const translations = {};
  const metadata = {
    batchNumber,
    processedAt: new Date().toISOString(),
    totalStrings: strings.length,
    method: 'terminology_based',
    quality: 'manual_review_recommended'
  };
  
  strings.forEach((item, index) => {
    const key = generateTranslationKey(item.text, item.context, item.category);
    const chineseTranslation = translateWithTerminology(item.text);
    
    translations[key] = {
      english: item.text,
      chinese: chineseTranslation,
      context: item.context,
      file: item.file,
      line: item.line,
      category: item.category,
      needsReview: chineseTranslation === item.text, // Flag if no translation occurred
      confidence: chineseTranslation !== item.text ? 0.8 : 0.3
    };
    
    if (index < 5) { // Show first few for verification
      console.log(`   ${index + 1}. "${item.text}" → "${chineseTranslation}"`);
    }
  });
  
  if (strings.length > 5) {
    console.log(`   ... and ${strings.length - 5} more`);
  }
  
  return { translations, metadata };
}

// Save processed batch to parallel workflow
function saveBatchToParallel(batchData, batchNumber) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `batch-${batchNumber}-${timestamp}.json`;
    const filepath = path.join(PARALLEL_DIR, 'pending', filename);
    
    const output = {
      metadata: batchData.metadata,
      translations: batchData.translations
    };
    
    fs.writeFileSync(filepath, JSON.stringify(output, null, 2));
    console.log(`✅ Saved batch ${batchNumber} to parallel workflow: ${filename}`);
    
    return filepath;
  } catch (error) {
    console.error(`❌ Failed to save batch ${batchNumber}:`, error.message);
    return null;
  }
}

// Main processing function
async function processHardcodedStrings() {
  console.log('🤖 Starting AI-Powered Translation Processing...\n');
  console.log('⚠️  ZERO BREAKING CHANGES: All output goes to parallel workflow\n');
  
  // Load analysis
  const analysis = loadHardcodedAnalysis();
  if (!analysis) {
    process.exit(1);
  }
  
  // Prepare strings for processing (prioritize critical first)
  const allStrings = [];
  
  // Add critical priority strings first
  if (analysis.priorityCategories.critical && analysis.priorityCategories.critical.examples) {
    analysis.priorityCategories.critical.examples.forEach(example => {
      allStrings.push({
        text: example.text,
        context: example.context,
        file: example.file,
        line: example.line,
        category: 'critical'
      });
    });
  }
  
  // Add high priority strings
  if (analysis.priorityCategories.high && analysis.priorityCategories.high.examples) {
    analysis.priorityCategories.high.examples.forEach(example => {
      allStrings.push({
        text: example.text,
        context: example.context,
        file: example.file,
        line: example.line,
        category: 'high'
      });
    });
  }
  
  console.log(`📊 Processing ${allStrings.length} prioritized strings in batches of ${BATCH_SIZE}`);
  
  // Process in batches
  const batches = [];
  for (let i = 0; i < allStrings.length; i += BATCH_SIZE) {
    const batch = allStrings.slice(i, i + BATCH_SIZE);
    const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
    
    const batchData = processBatch(batch, batchNumber);
    const savedPath = saveBatchToParallel(batchData, batchNumber);
    
    if (savedPath) {
      batches.push({
        number: batchNumber,
        path: savedPath,
        strings: batch.length
      });
    }
  }
  
  // Summary
  console.log('\n📋 AI TRANSLATION PROCESSING COMPLETE');
  console.log('='.repeat(50));
  console.log(`Total batches processed: ${batches.length}`);
  console.log(`Total strings processed: ${allStrings.length}`);
  console.log(`Output location: ${PARALLEL_DIR}/pending/`);
  
  console.log('\n✅ SAFETY CONFIRMED:');
  console.log('- Existing i18n-provider.tsx: UNTOUCHED');
  console.log('- All translations in parallel workflow');
  console.log('- Ready for manual review and approval');
  
  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Review translations in parallel workflow');
  console.log('2. Approve high-quality translations');
  console.log('3. Edit translations that need improvement');
  console.log('4. Use safe integration when ready');
  
  return {
    success: true,
    batchesProcessed: batches.length,
    stringsProcessed: allStrings.length,
    outputDir: `${PARALLEL_DIR}/pending/`
  };
}

// Run processing if called directly
if (require.main === module) {
  processHardcodedStrings().catch(console.error);
}

module.exports = { processHardcodedStrings };
