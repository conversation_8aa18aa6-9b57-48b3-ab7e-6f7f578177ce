#!/usr/bin/env node

/**
 * Manufacturing ERP i18n CI/CD Integration
 * 
 * Integrates hardcoded string detection into CI/CD pipeline
 * with configurable policies and reporting.
 * 
 * ZERO BREAKING CHANGES: Configurable to warn or fail builds.
 */

const fs = require('fs');
const path = require('path');
const { runAutomatedDetection } = require('./i18n-auto-detect');

// CI/CD Configuration
const CI_CONFIG = {
  // Policy: 'warn' (allow build) or 'fail' (block build)
  policy: process.env.I18N_POLICY || 'warn',
  
  // Maximum allowed new hardcoded strings before failing
  maxNewStrings: parseInt(process.env.I18N_MAX_NEW_STRINGS) || 10,
  
  // Whether to create GitHub/GitLab comments
  createComments: process.env.I18N_CREATE_COMMENTS === 'true',
  
  // Slack webhook for notifications
  slackWebhook: process.env.I18N_SLACK_WEBHOOK,
  
  // Output format: 'json', 'junit', 'github'
  outputFormat: process.env.I18N_OUTPUT_FORMAT || 'json'
};

// Detect CI environment
function detectCIEnvironment() {
  if (process.env.GITHUB_ACTIONS) {
    return {
      platform: 'github',
      isPR: !!process.env.GITHUB_EVENT_NAME && process.env.GITHUB_EVENT_NAME === 'pull_request',
      prNumber: process.env.GITHUB_EVENT_NUMBER,
      commitSha: process.env.GITHUB_SHA,
      branch: process.env.GITHUB_REF_NAME
    };
  }
  
  if (process.env.GITLAB_CI) {
    return {
      platform: 'gitlab',
      isPR: !!process.env.CI_MERGE_REQUEST_ID,
      prNumber: process.env.CI_MERGE_REQUEST_ID,
      commitSha: process.env.CI_COMMIT_SHA,
      branch: process.env.CI_COMMIT_REF_NAME
    };
  }
  
  if (process.env.JENKINS_URL) {
    return {
      platform: 'jenkins',
      isPR: !!process.env.CHANGE_ID,
      prNumber: process.env.CHANGE_ID,
      commitSha: process.env.GIT_COMMIT,
      branch: process.env.BRANCH_NAME
    };
  }
  
  return {
    platform: 'unknown',
    isPR: false,
    prNumber: null,
    commitSha: null,
    branch: null
  };
}

// Generate GitHub Actions output
function generateGitHubOutput(report, ciEnv) {
  const output = {
    status: report.status,
    newStrings: report.summary.newHardcodedStrings,
    totalDetections: report.summary.totalDetections,
    message: report.status === 'PASS' 
      ? '✅ No new hardcoded strings detected'
      : `⚠️ ${report.summary.newHardcodedStrings} new hardcoded strings detected`
  };
  
  // Set GitHub Actions outputs
  if (process.env.GITHUB_ACTIONS) {
    console.log(`::set-output name=i18n-status::${output.status}`);
    console.log(`::set-output name=i18n-new-strings::${output.newStrings}`);
    console.log(`::set-output name=i18n-message::${output.message}`);
    
    // Create annotations for new strings
    if (report.newStrings.length > 0) {
      report.newStrings.slice(0, 10).forEach(detection => {
        const level = detection.priority === 'high' ? 'error' : 'warning';
        console.log(`::${level} file=${detection.file},line=${detection.line}::Hardcoded string detected: "${detection.text}"`);
      });
    }
  }
  
  return output;
}

// Generate JUnit XML output
function generateJUnitOutput(report) {
  const testCases = report.newStrings.map(detection => {
    const failure = `<failure message="Hardcoded string detected" type="i18n">
File: ${detection.file}:${detection.line}
Text: "${detection.text}"
Type: ${detection.type}
Priority: ${detection.priority}
</failure>`;
    
    return `<testcase classname="i18n.detection" name="${detection.file}:${detection.line}" time="0">
${report.status === 'ATTENTION_NEEDED' ? failure : ''}
</testcase>`;
  });
  
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="i18n-detection" tests="${report.newStrings.length}" failures="${report.status === 'ATTENTION_NEEDED' ? report.newStrings.length : 0}" time="0">
${testCases.join('\n')}
</testsuite>`;
  
  fs.writeFileSync('i18n-temp/junit-report.xml', xml);
  console.log('✅ JUnit report saved: i18n-temp/junit-report.xml');
  
  return xml;
}

// Send Slack notification
async function sendSlackNotification(report, ciEnv) {
  if (!CI_CONFIG.slackWebhook) return;
  
  try {
    const color = report.status === 'PASS' ? 'good' : 'warning';
    const emoji = report.status === 'PASS' ? '✅' : '⚠️';
    
    const message = {
      attachments: [{
        color: color,
        title: `${emoji} Manufacturing ERP i18n Detection`,
        fields: [
          {
            title: 'Status',
            value: report.status,
            short: true
          },
          {
            title: 'New Hardcoded Strings',
            value: report.summary.newHardcodedStrings.toString(),
            short: true
          },
          {
            title: 'Branch',
            value: ciEnv.branch || 'unknown',
            short: true
          },
          {
            title: 'Platform',
            value: ciEnv.platform,
            short: true
          }
        ],
        footer: 'Manufacturing ERP i18n Detection',
        ts: Math.floor(Date.now() / 1000)
      }]
    };
    
    if (report.newStrings.length > 0) {
      const stringsList = report.newStrings.slice(0, 5).map(s => 
        `• ${s.file}:${s.line} - "${s.text}"`
      ).join('\\n');
      
      message.attachments[0].fields.push({
        title: 'New Strings (first 5)',
        value: stringsList,
        short: false
      });
    }
    
    // Note: In a real implementation, you would use fetch or axios to send to Slack
    console.log('📢 Slack notification prepared:', JSON.stringify(message, null, 2));
    
  } catch (error) {
    console.warn('⚠️  Failed to send Slack notification:', error.message);
  }
}

// Main CI integration function
async function runCIIntegration() {
  console.log('🚀 Manufacturing ERP i18n CI/CD Integration\n');
  
  const ciEnv = detectCIEnvironment();
  console.log(`🔍 Detected CI environment: ${ciEnv.platform}`);
  
  if (ciEnv.isPR) {
    console.log(`📋 Pull Request: #${ciEnv.prNumber}`);
  }
  
  console.log(`⚙️  Policy: ${CI_CONFIG.policy.toUpperCase()}`);
  console.log(`📊 Max new strings: ${CI_CONFIG.maxNewStrings}`);
  console.log('');
  
  try {
    // Run detection
    const report = await runAutomatedDetection();
    
    // Generate outputs based on format
    switch (CI_CONFIG.outputFormat) {
      case 'github':
        generateGitHubOutput(report, ciEnv);
        break;
      case 'junit':
        generateJUnitOutput(report);
        break;
      case 'json':
      default:
        const jsonOutput = {
          ...report,
          ciEnvironment: ciEnv,
          config: CI_CONFIG,
          timestamp: new Date().toISOString()
        };
        fs.writeFileSync('i18n-temp/ci-output.json', JSON.stringify(jsonOutput, null, 2));
        console.log('✅ JSON output saved: i18n-temp/ci-output.json');
        break;
    }
    
    // Send notifications
    if (CI_CONFIG.slackWebhook) {
      await sendSlackNotification(report, ciEnv);
    }
    
    // Determine exit code based on policy
    let exitCode = 0;
    let shouldFail = false;
    
    if (CI_CONFIG.policy === 'fail') {
      if (report.summary.newHardcodedStrings > CI_CONFIG.maxNewStrings) {
        shouldFail = true;
        exitCode = 1;
      }
    }
    
    // Final summary
    console.log('\n📋 CI/CD INTEGRATION SUMMARY');
    console.log('='.repeat(50));
    console.log(`Status: ${report.status}`);
    console.log(`New hardcoded strings: ${report.summary.newHardcodedStrings}`);
    console.log(`Policy: ${CI_CONFIG.policy}`);
    console.log(`Will fail build: ${shouldFail ? 'YES' : 'NO'}`);
    
    if (shouldFail) {
      console.log('\n❌ BUILD FAILED: Too many new hardcoded strings detected');
      console.log(`Limit: ${CI_CONFIG.maxNewStrings}, Found: ${report.summary.newHardcodedStrings}`);
      console.log('\n🎯 TO FIX:');
      console.log('1. Review and localize the detected hardcoded strings');
      console.log('2. Use t() function for user-facing text');
      console.log('3. Run: node scripts/i18n-ai-processor.js');
      console.log('4. Commit the localization changes');
    } else if (report.summary.newHardcodedStrings > 0) {
      console.log('\n⚠️  BUILD PASSED WITH WARNINGS');
      console.log('New hardcoded strings detected but within policy limits');
      console.log('\n🎯 RECOMMENDED:');
      console.log('1. Review and localize the detected strings');
      console.log('2. Run: node scripts/i18n-ai-processor.js');
    } else {
      console.log('\n✅ BUILD PASSED: No new hardcoded strings detected');
    }
    
    process.exit(exitCode);
    
  } catch (error) {
    console.error('❌ CI integration failed:', error.message);
    
    // In CI, we might want to fail on errors
    if (CI_CONFIG.policy === 'fail') {
      process.exit(1);
    } else {
      console.log('⚠️  Continuing despite error (warn policy)');
      process.exit(0);
    }
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'run':
      runCIIntegration();
      break;
    case 'config':
      console.log('📋 Current CI Configuration:');
      console.log(JSON.stringify(CI_CONFIG, null, 2));
      break;
    default:
      console.log('📖 Manufacturing ERP i18n CI/CD Integration');
      console.log('');
      console.log('Commands:');
      console.log('  run    - Run i18n detection in CI/CD environment');
      console.log('  config - Show current configuration');
      console.log('');
      console.log('Environment Variables:');
      console.log('  I18N_POLICY=warn|fail           - Build policy (default: warn)');
      console.log('  I18N_MAX_NEW_STRINGS=10         - Max new strings before fail');
      console.log('  I18N_CREATE_COMMENTS=true       - Create PR comments');
      console.log('  I18N_SLACK_WEBHOOK=url          - Slack notification webhook');
      console.log('  I18N_OUTPUT_FORMAT=json|junit   - Output format');
  }
}

module.exports = { runCIIntegration };
