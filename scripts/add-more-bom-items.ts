/**
 * Manufacturing ERP - Add More BOM Items
 * 
 * Adds additional BOM items to make the procurement planning more realistic
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1C MRP Implementation
 */

import { db, uid } from "../lib/db"
import { 
  rawMaterials, 
  billOfMaterials, 
  suppliers,
  demandForecasts
} from "../lib/schema-postgres"
import { eq, and } from "drizzle-orm"

async function addMoreBOMItems() {
  try {
    console.log("🔧 Adding more BOM items for realistic procurement planning...")

    // Find the company with forecasts
    const forecast = await db.query.demandForecasts.findFirst({
      with: {
        company: true,
        product: true,
      },
    })

    if (!forecast) {
      console.log("❌ No forecast found")
      return
    }

    const companyId = forecast.company_id
    const productId = forecast.product_id

    console.log(`🏢 Company: ${forecast.company.name}`)
    console.log(`📦 Product: ${forecast.product.name} (${forecast.product.sku})`)

    // Get existing raw materials
    const existingRawMaterials = await db.query.rawMaterials.findMany({
      where: eq(rawMaterials.company_id, companyId),
    })

    console.log(`🧱 Found ${existingRawMaterials.length} existing raw materials`)

    // Get supplier
    const supplier = await db.query.suppliers.findFirst({
      where: eq(suppliers.company_id, companyId),
    })

    if (!supplier) {
      console.log("❌ No supplier found")
      return
    }

    // Create additional raw materials if needed
    if (existingRawMaterials.length < 3) {
      console.log("🔧 Creating additional raw materials...")

      const additionalMaterials = [
        {
          id: uid("rm"),
          company_id: companyId,
          sku: "FABRIC-SILK-200GSM",
          name: "Premium Silk Fabric 200GSM",
          category: "fabric",
          unit: "meters",
          primary_supplier_id: supplier.id,
          composition: "100% Silk",
          quality_grade: "Premium",
          standard_cost: "8.50",
          currency: "USD",
          reorder_point: "50",
          max_stock_level: "500",
          inspection_required: "true",
        },
        {
          id: uid("rm"),
          company_id: companyId,
          sku: "DYE-REACTIVE-BLUE",
          name: "Reactive Blue Dye",
          category: "dyes",
          unit: "kg",
          primary_supplier_id: supplier.id,
          composition: "Reactive Dye",
          quality_grade: "Industrial",
          standard_cost: "12.00",
          currency: "USD",
          reorder_point: "25",
          max_stock_level: "200",
          inspection_required: "true",
        },
      ]

      await db.insert(rawMaterials).values(additionalMaterials)
      console.log(`✅ Created ${additionalMaterials.length} additional raw materials`)
    }

    // Get all raw materials now
    const allRawMaterials = await db.query.rawMaterials.findMany({
      where: eq(rawMaterials.company_id, companyId),
    })

    // Get existing BOM items
    const existingBOM = await db.query.billOfMaterials.findMany({
      where: and(
        eq(billOfMaterials.company_id, companyId),
        eq(billOfMaterials.product_id, productId)
      ),
    })

    console.log(`📋 Found ${existingBOM.length} existing BOM items`)

    // Create BOM items for materials that don't have them yet
    let bomItemsCreated = 0

    for (const material of allRawMaterials) {
      // Check if BOM item already exists for this material
      const existingBOMItem = existingBOM.find(bom => bom.raw_material_id === material.id)
      
      if (existingBOMItem) {
        console.log(`   ⏭️  BOM already exists for ${material.name}`)
        continue
      }

      // Calculate realistic quantities based on material type
      let qtyRequired = "1.0"
      let wasteFactor = "0.05"
      
      if (material.category === "yarn") {
        qtyRequired = "0.8" // 0.8 kg yarn per product unit
        wasteFactor = "0.10" // 10% waste for yarn
      } else if (material.category === "fabric") {
        qtyRequired = "1.5" // 1.5 meters fabric per product unit
        wasteFactor = "0.05" // 5% waste for fabric
      } else if (material.category === "dyes") {
        qtyRequired = "0.08" // 0.08 kg dye per product unit
        wasteFactor = "0.02" // 2% waste for dyes
      }

      const bomItem = {
        id: uid("bom"),
        company_id: companyId,
        product_id: productId,
        raw_material_id: material.id,
        qty_required: qtyRequired,
        unit: material.unit,
        waste_factor: wasteFactor,
        status: "active",
      }

      await db.insert(billOfMaterials).values(bomItem)
      bomItemsCreated++
      
      console.log(`   ✅ Created BOM: ${material.name} - ${qtyRequired} ${material.unit} (${wasteFactor} waste)`)
    }

    console.log(`\n🎯 Total new BOM items created: ${bomItemsCreated}`)

    // Show final BOM summary
    const finalBOM = await db.query.billOfMaterials.findMany({
      where: and(
        eq(billOfMaterials.company_id, companyId),
        eq(billOfMaterials.product_id, productId)
      ),
      with: {
        rawMaterial: true,
      },
    })

    console.log(`\n📋 Final BOM for ${forecast.product.name}:`)
    for (const bomItem of finalBOM) {
      const totalQty = parseFloat(bomItem.qty_required) * (1 + parseFloat(bomItem.waste_factor || "0"))
      const cost = parseFloat(bomItem.rawMaterial.standard_cost || "0") * totalQty
      console.log(`   • ${bomItem.rawMaterial.name}: ${bomItem.qty_required} ${bomItem.unit} + ${bomItem.waste_factor} waste = ${totalQty.toFixed(2)} ${bomItem.unit} ($${cost.toFixed(2)})`)
    }

    console.log("\n🎉 BOM enhancement completed successfully!")
    console.log("\n📋 Next steps:")
    console.log("   1. The approved forecast should now generate procurement plans")
    console.log("   2. Material requirements will be calculated from the BOM")
    console.log("   3. Check the procurement planning dashboard")

  } catch (error) {
    console.error("❌ Error adding BOM items:", error)
    throw error
  }
}

// Run the script
addMoreBOMItems()
  .then(() => {
    console.log("✅ Script completed successfully")
    process.exit(0)
  })
  .catch((error) => {
    console.error("❌ Script failed:", error)
    process.exit(1)
  })
