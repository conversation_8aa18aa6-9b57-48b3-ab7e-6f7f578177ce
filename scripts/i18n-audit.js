#!/usr/bin/env node

/**
 * Manufacturing ERP i18n Audit Script
 * 
 * This script performs a comprehensive audit of the Manufacturing ERP codebase
 * to identify all hardcoded English strings that should be localized.
 * 
 * ZERO BREAKING CHANGES: This script only analyzes code, it doesn't modify anything.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration
const SCAN_PATTERNS = [
  'app/**/*.tsx',
  'app/**/*.ts',
  'components/**/*.tsx',
  'components/**/*.ts',
  'lib/**/*.tsx',
  'lib/**/*.ts'
];

const IGNORE_PATTERNS = [
  'node_modules/**',
  '.next/**',
  'dist/**',
  'build/**',
  'components/i18n-provider.tsx', // Skip the main i18n file
  '**/*.d.ts'
];

// Patterns to detect hardcoded strings
const HARDCODED_PATTERNS = [
  // Form labels and placeholders
  /<FormLabel[^>]*>([^<]+)<\/FormLabel>/g,
  /<Label[^>]*>([^<]+)<\/Label>/g,
  /placeholder="([^"]+)"/g,
  
  // Button text
  /<Button[^>]*>([^<{]+)<\/Button>/g,
  
  // Error messages and toast notifications
  /toast\.error\("([^"]+)"/g,
  /toast\.success\("([^"]+)"/g,
  /toast\.warning\("([^"]+)"/g,
  /toast\.info\("([^"]+)"/g,
  
  // Dialog and modal titles
  /<DialogTitle[^>]*>([^<]+)<\/DialogTitle>/g,
  /<CardTitle[^>]*>([^<]+)<\/CardTitle>/g,
  
  // Loading and status messages
  /message\s*=\s*"([^"]+)"/g,
  /loadingText\s*=\s*"([^"]+)"/g,
  
  // Validation messages
  /message:\s*"([^"]+)"/g,
  
  // Table headers and content
  /<TableHead[^>]*>([^<]+)<\/TableHead>/g,
  
  // Select options
  /<SelectItem[^>]*value="[^"]*">([^<]+)<\/SelectItem>/g,
];

// Known translation keys to avoid false positives
const EXISTING_TRANSLATIONS = new Set();

// Load existing translations from i18n-provider.tsx
function loadExistingTranslations() {
  try {
    const i18nFile = fs.readFileSync('components/i18n-provider.tsx', 'utf8');
    const keyMatches = i18nFile.match(/"([^"]+)":\s*"[^"]*"/g);
    
    if (keyMatches) {
      keyMatches.forEach(match => {
        const key = match.match(/"([^"]+)":/)[1];
        EXISTING_TRANSLATIONS.add(key);
      });
    }
    
    console.log(`✅ Loaded ${EXISTING_TRANSLATIONS.size} existing translation keys`);
  } catch (error) {
    console.warn('⚠️  Could not load existing translations:', error.message);
  }
}

// Check if a string is likely a hardcoded user-facing text
function isHardcodedString(text) {
  // Skip if empty or too short
  if (!text || text.length < 2) return false;
  
  // Skip if it's a variable or expression
  if (text.includes('{') || text.includes('$') || text.includes('`')) return false;
  
  // Skip if it's a technical identifier
  if (/^[a-z_]+$/.test(text) || /^[A-Z_]+$/.test(text)) return false;
  
  // Skip if it's a number or date
  if (/^\d+$/.test(text) || /^\d{4}-\d{2}-\d{2}/.test(text)) return false;
  
  // Skip if it's a URL or path
  if (text.startsWith('/') || text.startsWith('http') || text.includes('://')) return false;
  
  // Skip if it's already using t() function
  if (text.includes('t(')) return false;
  
  // Skip common technical terms that shouldn't be translated
  const technicalTerms = ['id', 'uuid', 'api', 'url', 'json', 'xml', 'css', 'html', 'js', 'ts', 'tsx'];
  if (technicalTerms.includes(text.toLowerCase())) return false;
  
  // This looks like user-facing text
  return true;
}

// Analyze a single file for hardcoded strings
function analyzeFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // Check if file imports useI18n
    const hasI18nImport = content.includes('useI18n') || content.includes('from "@/components/i18n-provider"');
    const usesI18n = content.includes('const { t }') || content.includes('t(');
    
    // Scan for hardcoded patterns
    HARDCODED_PATTERNS.forEach((pattern, index) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const text = match[1];
        
        if (isHardcodedString(text)) {
          const lineNumber = content.substring(0, match.index).split('\n').length;
          
          issues.push({
            type: 'hardcoded_string',
            text: text,
            line: lineNumber,
            pattern: index,
            context: match[0]
          });
        }
      }
    });
    
    // Check for missing i18n import in components with UI elements
    const hasUIComponents = content.includes('Button') || content.includes('Input') || 
                           content.includes('Label') || content.includes('Dialog');
    
    if (hasUIComponents && !hasI18nImport && filePath.includes('components/')) {
      issues.push({
        type: 'missing_i18n_import',
        text: 'Component uses UI elements but missing useI18n import',
        line: 1
      });
    }
    
    return {
      file: filePath,
      hasI18nImport,
      usesI18n,
      issues
    };
    
  } catch (error) {
    console.warn(`⚠️  Could not analyze ${filePath}:`, error.message);
    return null;
  }
}

// Main audit function
async function runAudit() {
  console.log('🔍 Starting Manufacturing ERP i18n Audit...\n');
  
  // Load existing translations
  loadExistingTranslations();
  
  // Get all files to scan
  const allFiles = [];
  for (const pattern of SCAN_PATTERNS) {
    const files = glob.sync(pattern, { ignore: IGNORE_PATTERNS });
    allFiles.push(...files);
  }
  
  console.log(`📁 Scanning ${allFiles.length} files...\n`);
  
  // Analyze each file
  const results = [];
  let totalIssues = 0;
  
  for (const file of allFiles) {
    const analysis = analyzeFile(file);
    if (analysis && analysis.issues.length > 0) {
      results.push(analysis);
      totalIssues += analysis.issues.length;
    }
  }
  
  // Generate report
  console.log('📊 AUDIT RESULTS\n');
  console.log('='.repeat(80));
  
  if (results.length === 0) {
    console.log('✅ No localization issues found!');
    return;
  }
  
  console.log(`❌ Found ${totalIssues} localization issues in ${results.length} files:\n`);
  
  // Group issues by type
  const issuesByType = {
    hardcoded_string: [],
    missing_i18n_import: []
  };
  
  results.forEach(result => {
    result.issues.forEach(issue => {
      issuesByType[issue.type].push({
        ...issue,
        file: result.file
      });
    });
  });
  
  // Report hardcoded strings
  if (issuesByType.hardcoded_string.length > 0) {
    console.log(`🔤 HARDCODED STRINGS (${issuesByType.hardcoded_string.length}):`);
    console.log('-'.repeat(50));
    
    issuesByType.hardcoded_string.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.file}:${issue.line}`);
      console.log(`   Text: "${issue.text}"`);
      console.log(`   Context: ${issue.context}`);
      console.log('');
    });
  }
  
  // Report missing imports
  if (issuesByType.missing_i18n_import.length > 0) {
    console.log(`📦 MISSING I18N IMPORTS (${issuesByType.missing_i18n_import.length}):`);
    console.log('-'.repeat(50));
    
    issuesByType.missing_i18n_import.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.file}`);
      console.log(`   Issue: ${issue.text}`);
      console.log('');
    });
  }
  
  // Summary and recommendations
  console.log('📋 SUMMARY & RECOMMENDATIONS');
  console.log('='.repeat(80));
  console.log(`Total files scanned: ${allFiles.length}`);
  console.log(`Files with issues: ${results.length}`);
  console.log(`Total issues found: ${totalIssues}`);
  console.log(`Existing translations: ${EXISTING_TRANSLATIONS.size}`);
  console.log('');
  console.log('🎯 NEXT STEPS:');
  console.log('1. Review hardcoded strings and add to i18n-provider.tsx');
  console.log('2. Add missing useI18n imports to components');
  console.log('3. Replace hardcoded strings with t() function calls');
  console.log('4. Test all changes in development environment');
  console.log('');
  console.log('⚠️  IMPORTANT: Make changes incrementally to avoid breaking the system');
}

// Run the audit
if (require.main === module) {
  runAudit().catch(console.error);
}

module.exports = { runAudit, analyzeFile };
