/**
 * Manufacturing ERP - Location Data Migration Script
 * 
 * Migrates existing legacy location data to the new professional location system
 * Ensures production-ready integration with zero data loss
 */

import { db } from "@/lib/db"
import { stockLots, stockTxns } from "@/lib/schema-postgres"
import { eq, sql } from "drizzle-orm"
import { LEGACY_LOCATION_MAPPING } from "@/lib/location-config"

interface MigrationStats {
  stockLotsUpdated: number
  stockTxnsUpdated: number
  stockTxnsFromLocationUpdated: number
  stockTxnsToLocationUpdated: number
  errors: string[]
  warnings: string[]
}

/**
 * Professional Location Data Migration
 * Maps legacy location strings to new professional location IDs
 */
export class LocationDataMigration {
  private stats: MigrationStats = {
    stockLotsUpdated: 0,
    stockTxnsUpdated: 0,
    stockTxnsFromLocationUpdated: 0,
    stockTxnsToLocationUpdated: 0,
    errors: [],
    warnings: []
  }

  /**
   * Execute complete location data migration
   */
  async executeMigration(): Promise<MigrationStats> {
    console.log('🚀 Starting Location Data Migration...')
    console.log('📊 Legacy → Professional Location Mapping:')

    Object.entries(LEGACY_LOCATION_MAPPING).forEach(([legacy, professional]) => {
      console.log(`   ${legacy} → ${professional}`)
    })

    try {
      // Phase 1: Migrate stock_lots table
      await this.migrateStockLots()

      // Phase 2: Migrate stock_txns table
      await this.migrateStockTxns()

      // Phase 3: Verify migration results
      await this.verifyMigration()

      console.log('✅ Location Data Migration Completed Successfully!')
      this.printMigrationSummary()

      return this.stats
    } catch (error) {
      console.error('❌ Migration failed:', error)
      this.stats.errors.push(`Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      throw error
    }
  }

  /**
   * Migrate stock_lots table location field
   */
  private async migrateStockLots(): Promise<void> {
    console.log('\n📦 Migrating stock_lots table...')

    for (const [legacyLocation, professionalLocation] of Object.entries(LEGACY_LOCATION_MAPPING)) {
      try {
        // Check how many records need migration
        const countResult = await db
          .select({ count: sql<number>`count(*)` })
          .from(stockLots)
          .where(eq(stockLots.location, legacyLocation))

        const recordCount = countResult[0]?.count || 0

        if (recordCount > 0) {
          console.log(`   Updating ${recordCount} stock lots: ${legacyLocation} → ${professionalLocation}`)

          // Update records
          const updateResult = await db
            .update(stockLots)
            .set({ location: professionalLocation })
            .where(eq(stockLots.location, legacyLocation))

          this.stats.stockLotsUpdated += recordCount
          console.log(`   ✅ Updated ${recordCount} stock lot records`)
        } else {
          console.log(`   ⏭️  No stock lots found with location: ${legacyLocation}`)
        }
      } catch (error) {
        const errorMsg = `Failed to migrate stock_lots location ${legacyLocation}: ${error instanceof Error ? error.message : 'Unknown error'}`
        console.error(`   ❌ ${errorMsg}`)
        this.stats.errors.push(errorMsg)
      }
    }
  }

  /**
   * Migrate stock_txns table location fields
   */
  private async migrateStockTxns(): Promise<void> {
    console.log('\n📋 Migrating stock_txns table...')

    // Migrate main location field
    await this.migrateStockTxnsField('location', 'location')

    // Migrate from_location field
    await this.migrateStockTxnsField('from_location', 'from_location')

    // Migrate to_location field
    await this.migrateStockTxnsField('to_location', 'to_location')
  }

  /**
   * Migrate specific field in stock_txns table
   */
  private async migrateStockTxnsField(fieldName: string, displayName: string): Promise<void> {
    console.log(`\n   📍 Migrating ${displayName} field...`)

    for (const [legacyLocation, professionalLocation] of Object.entries(LEGACY_LOCATION_MAPPING)) {
      try {
        // Build dynamic query based on field name
        const whereCondition = sql`${sql.identifier(fieldName)} = ${legacyLocation}`

        // Check how many records need migration
        const countResult = await db
          .select({ count: sql<number>`count(*)` })
          .from(stockTxns)
          .where(whereCondition)

        const recordCount = countResult[0]?.count || 0

        if (recordCount > 0) {
          console.log(`      Updating ${recordCount} transactions: ${legacyLocation} → ${professionalLocation}`)

          // Update records using dynamic field update
          const updateSql = sql`
            UPDATE stock_txns 
            SET ${sql.identifier(fieldName)} = ${professionalLocation}
            WHERE ${sql.identifier(fieldName)} = ${legacyLocation}
          `

          await db.execute(updateSql)

          // Update stats based on field type
          if (fieldName === 'location') {
            this.stats.stockTxnsUpdated += recordCount
          } else if (fieldName === 'from_location') {
            this.stats.stockTxnsFromLocationUpdated += recordCount
          } else if (fieldName === 'to_location') {
            this.stats.stockTxnsToLocationUpdated += recordCount
          }

          console.log(`      ✅ Updated ${recordCount} transaction records`)
        } else {
          console.log(`      ⏭️  No transactions found with ${displayName}: ${legacyLocation}`)
        }
      } catch (error) {
        const errorMsg = `Failed to migrate stock_txns ${fieldName} ${legacyLocation}: ${error instanceof Error ? error.message : 'Unknown error'}`
        console.error(`      ❌ ${errorMsg}`)
        this.stats.errors.push(errorMsg)
      }
    }
  }

  /**
   * Verify migration results
   */
  private async verifyMigration(): Promise<void> {
    console.log('\n🔍 Verifying migration results...')

    try {
      // Check for any remaining legacy locations in stock_lots
      const legacyStockLots = await db
        .select({
          location: stockLots.location,
          count: sql<number>`count(*)`
        })
        .from(stockLots)
        .where(sql`${stockLots.location} IN (${Object.keys(LEGACY_LOCATION_MAPPING).map(loc => `'${loc}'`).join(',')})`)
        .groupBy(stockLots.location)

      if (legacyStockLots.length > 0) {
        legacyStockLots.forEach(result => {
          const warning = `Found ${result.count} stock lots still using legacy location: ${result.location}`
          console.log(`   ⚠️  ${warning}`)
          this.stats.warnings.push(warning)
        })
      } else {
        console.log('   ✅ All stock lots successfully migrated to professional locations')
      }

      // Check current location distribution
      const locationDistribution = await db
        .select({
          location: stockLots.location,
          count: sql<number>`count(*)`
        })
        .from(stockLots)
        .groupBy(stockLots.location)

      console.log('\n📊 Current location distribution:')
      locationDistribution.forEach(result => {
        console.log(`   ${result.location}: ${result.count} stock lots`)
      })

    } catch (error) {
      const errorMsg = `Verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      console.error(`❌ ${errorMsg}`)
      this.stats.errors.push(errorMsg)
    }
  }

  /**
   * Print migration summary
   */
  private printMigrationSummary(): void {
    console.log('\n📋 MIGRATION SUMMARY:')
    console.log('='.repeat(50))
    console.log(`✅ Stock Lots Updated: ${this.stats.stockLotsUpdated}`)
    console.log(`✅ Stock Transactions Updated: ${this.stats.stockTxnsUpdated}`)
    console.log(`✅ From Location Fields Updated: ${this.stats.stockTxnsFromLocationUpdated}`)
    console.log(`✅ To Location Fields Updated: ${this.stats.stockTxnsToLocationUpdated}`)
    console.log(`📊 Total Records Migrated: ${this.stats.stockLotsUpdated +
      this.stats.stockTxnsUpdated +
      this.stats.stockTxnsFromLocationUpdated +
      this.stats.stockTxnsToLocationUpdated
      }`)

    if (this.stats.warnings.length > 0) {
      console.log(`\n⚠️  Warnings: ${this.stats.warnings.length}`)
      this.stats.warnings.forEach(warning => console.log(`   ${warning}`))
    }

    if (this.stats.errors.length > 0) {
      console.log(`\n❌ Errors: ${this.stats.errors.length}`)
      this.stats.errors.forEach(error => console.log(`   ${error}`))
    }

    console.log('='.repeat(50))
  }

  /**
   * Rollback migration (emergency use only)
   */
  async rollbackMigration(): Promise<void> {
    console.log('🔄 Rolling back location migration...')
    console.log('⚠️  WARNING: This will revert all locations to legacy format!')

    try {
      // Rollback stock_lots
      for (const [legacyLocation, professionalLocation] of Object.entries(LEGACY_LOCATION_MAPPING)) {
        await db
          .update(stockLots)
          .set({ location: legacyLocation })
          .where(eq(stockLots.location, professionalLocation))
      }

      // Rollback stock_txns (all location fields)
      for (const [legacyLocation, professionalLocation] of Object.entries(LEGACY_LOCATION_MAPPING)) {
        // Rollback location field
        await db.execute(sql`
          UPDATE stock_txns 
          SET location = ${legacyLocation}
          WHERE location = ${professionalLocation}
        `)

        // Rollback from_location field
        await db.execute(sql`
          UPDATE stock_txns 
          SET from_location = ${legacyLocation}
          WHERE from_location = ${professionalLocation}
        `)

        // Rollback to_location field
        await db.execute(sql`
          UPDATE stock_txns 
          SET to_location = ${legacyLocation}
          WHERE to_location = ${professionalLocation}
        `)
      }

      console.log('✅ Migration rollback completed')
    } catch (error) {
      console.error('❌ Rollback failed:', error)
      throw error
    }
  }
}

/**
 * Execute migration if run directly
 */
if (require.main === module) {
  const migration = new LocationDataMigration()
  migration.executeMigration()
    .then((stats) => {
      console.log('\n🎉 Migration completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Migration failed:', error)
      process.exit(1)
    })
}

// Export is handled by the class declaration above
