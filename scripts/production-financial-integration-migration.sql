-- PRODUCTION MIGRATION: Financial Integration Enhancement
-- Execute this in Supabase SQL Editor BEFORE deploying code changes
-- Manufacturing ERP - Phase 3A Financial Integration

-- ============================================================================
-- STEP 1: ENHANCE AR INVOICES TABLE
-- ============================================================================

-- Add contract integration fields to ar_invoices
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS sales_contract_id TEXT;
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS contract_reference TEXT;
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS due_date TEXT;
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS received TEXT DEFAULT '0';
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'USD';
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS payment_terms TEXT;
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE ar_invoices ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add foreign key constraint for sales contract relationship
ALTER TABLE ar_invoices ADD CONSTRAINT fk_ar_invoices_sales_contract 
  FOREIGN KEY (sales_contract_id) REFERENCES sales_contracts(id);

-- Add performance index
CREATE INDEX IF NOT EXISTS ar_invoices_contract_id_idx ON ar_invoices(sales_contract_id);

-- Update existing records with default values
UPDATE ar_invoices SET 
  received = '0' WHERE received IS NULL;
UPDATE ar_invoices SET 
  currency = 'USD' WHERE currency IS NULL;
UPDATE ar_invoices SET 
  updated_at = NOW() WHERE updated_at IS NULL;

-- ============================================================================
-- STEP 2: ENHANCE AP INVOICES TABLE
-- ============================================================================

-- Add contract integration fields to ap_invoices
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS purchase_contract_id TEXT;
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS contract_reference TEXT;
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS due_date TEXT;
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS paid TEXT DEFAULT '0';
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'USD';
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS payment_terms TEXT;
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE ap_invoices ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add foreign key constraint for purchase contract relationship
ALTER TABLE ap_invoices ADD CONSTRAINT fk_ap_invoices_purchase_contract 
  FOREIGN KEY (purchase_contract_id) REFERENCES purchase_contracts(id);

-- Add performance index
CREATE INDEX IF NOT EXISTS ap_invoices_contract_id_idx ON ap_invoices(purchase_contract_id);

-- Update existing records with default values
UPDATE ap_invoices SET 
  paid = '0' WHERE paid IS NULL;
UPDATE ap_invoices SET 
  currency = 'USD' WHERE currency IS NULL;
UPDATE ap_invoices SET 
  updated_at = NOW() WHERE updated_at IS NULL;

-- ============================================================================
-- STEP 3: VERIFICATION QUERIES
-- ============================================================================

-- Verify AR invoices table structure
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default 
FROM information_schema.columns 
WHERE table_name = 'ar_invoices' 
  AND column_name IN ('sales_contract_id', 'contract_reference', 'due_date', 'received', 'currency', 'payment_terms', 'notes', 'updated_at')
ORDER BY ordinal_position;

-- Verify AP invoices table structure
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default 
FROM information_schema.columns 
WHERE table_name = 'ap_invoices' 
  AND column_name IN ('purchase_contract_id', 'contract_reference', 'due_date', 'paid', 'currency', 'payment_terms', 'notes', 'updated_at')
ORDER BY ordinal_position;

-- Verify foreign key constraints
SELECT 
  tc.constraint_name, 
  tc.table_name, 
  kcu.column_name, 
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name IN ('ar_invoices', 'ap_invoices')
  AND tc.constraint_name LIKE '%contract%';

-- Verify indexes
SELECT 
  indexname, 
  tablename,
  indexdef 
FROM pg_indexes 
WHERE tablename IN ('ar_invoices', 'ap_invoices')
  AND indexname LIKE '%contract%'
ORDER BY tablename, indexname;

-- ============================================================================
-- STEP 4: SAMPLE DATA CHECK
-- ============================================================================

-- Check existing AR invoices (should show new columns)
SELECT 
  id, 
  number, 
  customer_id, 
  sales_contract_id, 
  amount, 
  received, 
  currency,
  status, 
  created_at
FROM ar_invoices 
ORDER BY created_at DESC 
LIMIT 3;

-- Check existing AP invoices (should show new columns)
SELECT 
  id, 
  number, 
  supplier_id, 
  purchase_contract_id, 
  amount, 
  paid, 
  currency,
  status, 
  created_at
FROM ap_invoices 
ORDER BY created_at DESC 
LIMIT 3;

-- ============================================================================
-- MIGRATION COMPLETION CONFIRMATION
-- ============================================================================

SELECT 
  'Financial Integration Migration Completed Successfully - Ready for Code Deployment' as status,
  NOW() as completed_at;

-- ============================================================================
-- IMPORTANT NOTES FOR PRODUCTION DEPLOYMENT:
-- ============================================================================

-- 1. Execute this entire script in Supabase SQL Editor
-- 2. Verify all queries return expected results
-- 3. Confirm no errors in the execution
-- 4. Only after successful execution, deploy the code changes via GitHub
-- 5. Test the finance page functionality after deployment
-- 6. Verify contract-to-invoice workflow works correctly

-- This migration adds:
-- ✅ Contract integration fields (sales_contract_id, purchase_contract_id)
-- ✅ Enhanced payment tracking (received, paid, due_date)
-- ✅ Multi-currency support (currency field)
-- ✅ Payment terms and notes fields
-- ✅ Updated timestamp tracking
-- ✅ Foreign key constraints for data integrity
-- ✅ Performance indexes for query optimization
-- ✅ Default values for existing records
