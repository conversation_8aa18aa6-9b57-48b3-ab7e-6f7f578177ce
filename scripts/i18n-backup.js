#!/usr/bin/env node

/**
 * Manufacturing ERP i18n Backup Script
 * 
 * Creates a complete backup of the existing i18n system before any modifications.
 * This ensures we can restore the system to its current working state if needed.
 * 
 * ZERO BREAKING CHANGES: This script only creates backups, no modifications.
 */

const fs = require('fs');
const path = require('path');

// Backup configuration
const BACKUP_DIR = 'i18n-backup';
const TIMESTAMP = new Date().toISOString().replace(/[:.]/g, '-');
const BACKUP_PATH = path.join(BACKUP_DIR, `backup-${TIMESTAMP}`);

// Files to backup
const BACKUP_FILES = [
  'components/i18n-provider.tsx',
  'components/language-switcher.tsx',
  'package.json',
  'package-lock.json'
];

// Create backup directory structure
function createBackupStructure() {
  try {
    if (!fs.existsSync(BACKUP_DIR)) {
      fs.mkdirSync(BACKUP_DIR, { recursive: true });
    }
    
    fs.mkdirSync(BACKUP_PATH, { recursive: true });
    console.log(`✅ Created backup directory: ${BACKUP_PATH}`);
    
    return true;
  } catch (error) {
    console.error('❌ Failed to create backup directory:', error.message);
    return false;
  }
}

// Backup a single file
function backupFile(sourceFile) {
  try {
    if (!fs.existsSync(sourceFile)) {
      console.warn(`⚠️  File not found: ${sourceFile}`);
      return false;
    }
    
    const targetFile = path.join(BACKUP_PATH, sourceFile);
    const targetDir = path.dirname(targetFile);
    
    // Create target directory if it doesn't exist
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }
    
    // Copy file
    fs.copyFileSync(sourceFile, targetFile);
    console.log(`✅ Backed up: ${sourceFile} → ${targetFile}`);
    
    return true;
  } catch (error) {
    console.error(`❌ Failed to backup ${sourceFile}:`, error.message);
    return false;
  }
}

// Extract translation statistics
function extractTranslationStats() {
  try {
    const i18nFile = fs.readFileSync('components/i18n-provider.tsx', 'utf8');
    
    // Count English translations
    const enSection = i18nFile.match(/const en: Dict = \{([\s\S]*?)\}/);
    const enKeys = enSection ? (enSection[1].match(/"[^"]+"\s*:/g) || []).length : 0;
    
    // Count Chinese translations
    const zhSection = i18nFile.match(/const zh: Dict = \{([\s\S]*?)\}/);
    const zhKeys = zhSection ? (zhSection[1].match(/"[^"]+"\s*:/g) || []).length : 0;
    
    // File size
    const fileSize = fs.statSync('components/i18n-provider.tsx').size;
    const fileSizeKB = Math.round(fileSize / 1024);
    
    const stats = {
      timestamp: new Date().toISOString(),
      englishKeys: enKeys,
      chineseKeys: zhKeys,
      totalKeys: enKeys + zhKeys,
      fileSizeKB: fileSizeKB,
      fileLines: i18nFile.split('\n').length
    };
    
    // Save stats to backup
    const statsFile = path.join(BACKUP_PATH, 'translation-stats.json');
    fs.writeFileSync(statsFile, JSON.stringify(stats, null, 2));
    
    console.log('📊 Translation Statistics:');
    console.log(`   English keys: ${stats.englishKeys}`);
    console.log(`   Chinese keys: ${stats.chineseKeys}`);
    console.log(`   Total keys: ${stats.totalKeys}`);
    console.log(`   File size: ${stats.fileSizeKB} KB`);
    console.log(`   File lines: ${stats.fileLines}`);
    console.log(`   Stats saved: ${statsFile}`);
    
    return stats;
  } catch (error) {
    console.error('❌ Failed to extract translation stats:', error.message);
    return null;
  }
}

// Create restore script
function createRestoreScript() {
  const restoreScript = `#!/usr/bin/env node

/**
 * Manufacturing ERP i18n Restore Script
 * Generated on: ${new Date().toISOString()}
 * 
 * This script restores the i18n system to its backed-up state.
 * Use this if you need to rollback changes and return to the working system.
 */

const fs = require('fs');
const path = require('path');

const BACKUP_PATH = '${BACKUP_PATH}';
const FILES_TO_RESTORE = ${JSON.stringify(BACKUP_FILES, null, 2)};

console.log('🔄 Restoring Manufacturing ERP i18n system...');
console.log('Backup source:', BACKUP_PATH);

let restored = 0;
let failed = 0;

FILES_TO_RESTORE.forEach(file => {
  try {
    const backupFile = path.join(BACKUP_PATH, file);
    
    if (!fs.existsSync(backupFile)) {
      console.warn(\`⚠️  Backup file not found: \${backupFile}\`);
      failed++;
      return;
    }
    
    // Create target directory if needed
    const targetDir = path.dirname(file);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }
    
    // Restore file
    fs.copyFileSync(backupFile, file);
    console.log(\`✅ Restored: \${file}\`);
    restored++;
    
  } catch (error) {
    console.error(\`❌ Failed to restore \${file}:\`, error.message);
    failed++;
  }
});

console.log(\`\\n📊 Restore Summary:\`);
console.log(\`   Restored: \${restored} files\`);
console.log(\`   Failed: \${failed} files\`);

if (failed === 0) {
  console.log('\\n✅ i18n system successfully restored to backed-up state!');
  console.log('🔄 Please restart your development server: npm run dev');
} else {
  console.log('\\n⚠️  Some files failed to restore. Please check manually.');
}
`;

  const restoreScriptPath = path.join(BACKUP_PATH, 'restore.js');
  fs.writeFileSync(restoreScriptPath, restoreScript);
  fs.chmodSync(restoreScriptPath, '755'); // Make executable
  
  console.log(`✅ Created restore script: ${restoreScriptPath}`);
  return restoreScriptPath;
}

// Main backup function
async function createBackup() {
  console.log('💾 Starting Manufacturing ERP i18n Backup...\n');
  
  // Create backup structure
  if (!createBackupStructure()) {
    process.exit(1);
  }
  
  // Backup files
  let backedUp = 0;
  let failed = 0;
  
  console.log('📁 Backing up files...');
  BACKUP_FILES.forEach(file => {
    if (backupFile(file)) {
      backedUp++;
    } else {
      failed++;
    }
  });
  
  // Extract statistics
  console.log('\n📊 Extracting translation statistics...');
  const stats = extractTranslationStats();
  
  // Create restore script
  console.log('\n🔄 Creating restore script...');
  const restoreScript = createRestoreScript();
  
  // Summary
  console.log('\n📋 BACKUP SUMMARY');
  console.log('='.repeat(50));
  console.log(`Backup location: ${BACKUP_PATH}`);
  console.log(`Files backed up: ${backedUp}`);
  console.log(`Files failed: ${failed}`);
  
  if (stats) {
    console.log(`Translation keys: ${stats.totalKeys}`);
    console.log(`File size: ${stats.fileSizeKB} KB`);
  }
  
  console.log(`Restore script: ${restoreScript}`);
  
  if (failed === 0) {
    console.log('\n✅ Backup completed successfully!');
    console.log('🔒 Your current i18n system is now safely backed up.');
    console.log('🚀 You can proceed with i18n-ai implementation.');
  } else {
    console.log('\n⚠️  Backup completed with some failures.');
    console.log('Please review the failed files before proceeding.');
  }
  
  return {
    success: failed === 0,
    backupPath: BACKUP_PATH,
    restoreScript,
    stats
  };
}

// Run backup if called directly
if (require.main === module) {
  createBackup().catch(console.error);
}

module.exports = { createBackup };
