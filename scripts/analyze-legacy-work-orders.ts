/**
 * Manufacturing ERP - Legacy Work Order Analysis
 * 
 * Identifies work orders that need retroactive workflow completion
 * Provides detailed analysis and recommendations for data cleanup
 */

import { db } from "@/lib/db"
import {
  workOrders,
  qualityInspections,
  stockLots,
  products,
  salesContracts
} from "@/lib/schema-postgres"
import { eq, and, sql, isNull } from "drizzle-orm"

interface LegacyWorkOrderAnalysis {
  workOrderId: string
  workOrderNumber: string
  productName: string
  status: string
  createdAt: string
  hasInspection: boolean
  hasStockLot: boolean
  hasSalesContract: boolean
  missingComponents: string[]
  recommendedActions: string[]
}

export class LegacyWorkOrderAnalyzer {

  async analyzeLegacyWorkOrders(): Promise<LegacyWorkOrderAnalysis[]> {
    console.log('🔍 Analyzing Legacy Work Orders for Workflow Completion...')

    // Get all work orders with their relationships
    const allWorkOrders = await db.query.workOrders.findMany({
      with: {
        product: true,
        salesContract: true,
        qualityInspections: true,
        stockLots: true,
      },
      orderBy: (workOrders, { asc }) => [asc(workOrders.created_at)]
    })

    const analysis: LegacyWorkOrderAnalysis[] = []

    console.log(`\n📊 Found ${allWorkOrders.length} work orders to analyze`)
    console.log('='.repeat(80))

    for (const workOrder of allWorkOrders) {
      const hasInspection = workOrder.qualityInspections.length > 0
      const hasStockLot = workOrder.stockLots.length > 0
      const hasSalesContract = workOrder.salesContract !== null

      const missingComponents: string[] = []
      const recommendedActions: string[] = []

      // ✅ PRODUCT-AWARE ANALYSIS: Check requirements based on product inspection settings
      const requiresInspection = workOrder.product?.inspection_required === "true"

      // For products requiring inspection
      if (requiresInspection) {
        if (!hasInspection) {
          missingComponents.push("Quality Inspection (Required)")
          recommendedActions.push("Create mandatory quality inspection")
        } else {
          // Check if inspections are completed
          const pendingInspections = workOrder.qualityInspections.filter(qi => qi.status === "pending")
          const passedInspections = workOrder.qualityInspections.filter(qi => qi.status === "passed")

          if (workOrder.status === "completed" && pendingInspections.length > 0) {
            missingComponents.push("Pending Quality Approval")
            recommendedActions.push("Complete pending quality inspections")
          }

          if (workOrder.status === "completed" && passedInspections.length === 0) {
            missingComponents.push("Quality Approval")
            recommendedActions.push("Approve at least one quality inspection")
          }
        }
      }

      // For all completed work orders (regardless of inspection requirements)
      if (!hasStockLot && workOrder.status === "completed") {
        missingComponents.push("Stock Lot")
        recommendedActions.push("Create finished goods stock lot")
      }

      // Integration check: completed work orders should have triggered inventory creation
      if (workOrder.status === "completed" && !hasStockLot) {
        missingComponents.push("Inventory Integration")
        recommendedActions.push("Run work order inventory integration")
      }

      const workOrderAnalysis: LegacyWorkOrderAnalysis = {
        workOrderId: workOrder.id,
        workOrderNumber: workOrder.number,
        productName: workOrder.product?.name || "Unknown Product",
        status: workOrder.status,
        createdAt: workOrder.created_at?.toISOString().split('T')[0] || "Unknown",
        hasInspection,
        hasStockLot,
        hasSalesContract,
        missingComponents,
        recommendedActions
      }

      analysis.push(workOrderAnalysis)

      // Print individual analysis
      const statusIcon = missingComponents.length === 0 ? '✅' : '⚠️'
      const inspectionRequiredIcon = requiresInspection ? '🔬' : '⚡'
      const inspectionRequiredText = requiresInspection ? 'Required' : 'Not Required'

      console.log(`${statusIcon} Work Order: ${workOrder.number}`)
      console.log(`   📅 Created: ${workOrderAnalysis.createdAt}`)
      console.log(`   📦 Product: ${workOrderAnalysis.productName}`)
      console.log(`   📊 Status: ${workOrder.status}`)
      console.log(`   ${inspectionRequiredIcon} Inspection: ${inspectionRequiredText}`)
      console.log(`   🔬 Has Inspection: ${hasInspection ? '✅' : '❌'}`)
      console.log(`   📦 Has Stock Lot: ${hasStockLot ? '✅' : '❌'}`)
      console.log(`   📋 Has Sales Contract: ${hasSalesContract ? '✅' : '❌'}`)

      if (missingComponents.length > 0) {
        console.log(`   ⚠️  Missing: ${missingComponents.join(', ')}`)
        console.log(`   💡 Actions: ${recommendedActions.join(', ')}`)
      }
      console.log('')
    }

    this.printSummaryReport(analysis)
    return analysis
  }

  private printSummaryReport(analysis: LegacyWorkOrderAnalysis[]): void {
    console.log('='.repeat(80))
    console.log('📋 LEGACY WORK ORDER ANALYSIS SUMMARY')
    console.log('='.repeat(80))

    const totalWorkOrders = analysis.length
    const completeWorkOrders = analysis.filter(wo => wo.missingComponents.length === 0).length
    const incompleteWorkOrders = totalWorkOrders - completeWorkOrders

    console.log(`\n📊 OVERVIEW:`)
    console.log(`   Total Work Orders: ${totalWorkOrders}`)
    console.log(`   Complete Workflows: ${completeWorkOrders} (${Math.round((completeWorkOrders / totalWorkOrders) * 100)}%)`)
    console.log(`   Incomplete Workflows: ${incompleteWorkOrders} (${Math.round((incompleteWorkOrders / totalWorkOrders) * 100)}%)`)

    // Breakdown by missing components
    const missingInspections = analysis.filter(wo => wo.missingComponents.includes("Quality Inspection")).length
    const missingStockLots = analysis.filter(wo => wo.missingComponents.includes("Stock Lot")).length
    const missingIntegration = analysis.filter(wo => wo.missingComponents.includes("Inventory Integration")).length

    console.log(`\n🔍 MISSING COMPONENTS:`)
    console.log(`   Missing Quality Inspections: ${missingInspections}`)
    console.log(`   Missing Stock Lots: ${missingStockLots}`)
    console.log(`   Missing Integration: ${missingIntegration}`)

    // Status breakdown
    const statusBreakdown = analysis.reduce((acc, wo) => {
      acc[wo.status] = (acc[wo.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    console.log(`\n📊 STATUS BREAKDOWN:`)
    Object.entries(statusBreakdown).forEach(([status, count]) => {
      console.log(`   ${status}: ${count}`)
    })

    // Recommendations
    console.log(`\n💡 RECOMMENDATIONS:`)

    if (incompleteWorkOrders === 0) {
      console.log(`   🎉 All work orders have complete workflows! System is ready for production.`)
    } else {
      console.log(`   🔧 ${incompleteWorkOrders} work orders need workflow completion`)

      if (missingInspections > 0) {
        console.log(`   1. Create ${missingInspections} missing quality inspections`)
      }

      if (missingStockLots > 0) {
        console.log(`   2. Create ${missingStockLots} missing stock lots for completed work orders`)
      }

      if (missingIntegration > 0) {
        console.log(`   3. Link ${missingIntegration} inspections to their stock lots`)
      }

      console.log(`   4. Run retroactive workflow completion script`)
      console.log(`   5. Verify all workflows are complete before proceeding to new modules`)
    }

    console.log('='.repeat(80))
  }

  /**
   * Get specific work orders that need attention
   */
  async getIncompleteWorkOrders(): Promise<LegacyWorkOrderAnalysis[]> {
    const analysis = await this.analyzeLegacyWorkOrders()
    return analysis.filter(wo => wo.missingComponents.length > 0)
  }

  /**
   * Get work orders that are ready for retroactive completion
   */
  async getCompletableWorkOrders(): Promise<LegacyWorkOrderAnalysis[]> {
    const analysis = await this.analyzeLegacyWorkOrders()
    return analysis.filter(wo =>
      wo.status === "completed" &&
      wo.missingComponents.includes("Stock Lot")
    )
  }
}

/**
 * Execute analysis if run directly
 */
if (require.main === module) {
  const analyzer = new LegacyWorkOrderAnalyzer()
  analyzer.analyzeLegacyWorkOrders()
    .then((analysis) => {
      console.log('\n✅ Legacy work order analysis completed!')

      const incompleteCount = analysis.filter(wo => wo.missingComponents.length > 0).length
      if (incompleteCount > 0) {
        console.log(`\n⚠️  ${incompleteCount} work orders need attention before proceeding to new modules`)
        console.log('💡 Run the retroactive completion script to fix these issues')
      } else {
        console.log('\n🎉 All work orders have complete workflows! Ready for new modules.')
      }

      process.exit(0)
    })
    .catch((error) => {
      console.error('\n❌ Analysis failed:', error)
      process.exit(1)
    })
}
