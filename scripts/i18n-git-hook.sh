#!/bin/bash

# Manufacturing ERP i18n Git Hook
# 
# Automatically detects hardcoded strings in commits to prevent
# new localization issues from entering the codebase.
# 
# ZERO BREAKING CHANGES: Detection only, commits are not blocked.

set -e

echo "🔍 Manufacturing ERP i18n Detection Hook"
echo "⚠️  ZERO BREAKING CHANGES: Detection only, no code modifications"
echo ""

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
DETECTION_SCRIPT="$SCRIPT_DIR/i18n-auto-detect.js"
LOG_FILE="i18n-temp/git-hook.log"

# Ensure log directory exists
mkdir -p "$(dirname "$LOG_FILE")"

# Log function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
    echo "$1"
}

# Check if detection script exists
if [ ! -f "$DETECTION_SCRIPT" ]; then
    log "⚠️  i18n detection script not found: $DETECTION_SCRIPT"
    log "Skipping i18n detection for this commit"
    exit 0
fi

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    log "⚠️  Node.js not found, skipping i18n detection"
    exit 0
fi

# Check if this is a relevant commit (contains .tsx or .ts files)
CHANGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(tsx?|jsx?)$' || true)

if [ -z "$CHANGED_FILES" ]; then
    log "ℹ️  No TypeScript/JavaScript files changed, skipping i18n detection"
    exit 0
fi

log "📁 Detected changes in TypeScript/JavaScript files:"
echo "$CHANGED_FILES" | while read -r file; do
    log "   - $file"
done

# Run detection
log "🔄 Running hardcoded string detection..."

cd "$PROJECT_ROOT"

# Run detection and capture output
if DETECTION_OUTPUT=$(node "$DETECTION_SCRIPT" 2>&1); then
    log "✅ i18n detection completed successfully"
    
    # Parse detection results
    if echo "$DETECTION_OUTPUT" | grep -q "NEW HARDCODED STRINGS DETECTED"; then
        log "⚠️  New hardcoded strings detected in this commit!"
        log ""
        log "📋 DETECTION RESULTS:"
        echo "$DETECTION_OUTPUT" | grep -A 20 "NEW HARDCODED STRINGS DETECTED" >> "$LOG_FILE"
        
        echo ""
        echo "⚠️  NEW HARDCODED STRINGS DETECTED!"
        echo ""
        echo "This commit introduces new hardcoded strings that should be localized."
        echo "While your commit will proceed (ZERO BREAKING CHANGES policy),"
        echo "please consider addressing these strings:"
        echo ""
        echo "$DETECTION_OUTPUT" | grep -A 10 "NEW HARDCODED STRINGS DETECTED" || true
        echo ""
        echo "🎯 RECOMMENDED ACTIONS:"
        echo "1. Review the detected strings above"
        echo "2. Add them to the i18n system using t() function"
        echo "3. Run: node scripts/i18n-ai-processor.js"
        echo "4. Use the parallel workflow for safe integration"
        echo ""
        echo "📖 For help: see i18n-parallel/README.md"
        echo ""
        
        # Create a reminder file for the developer
        REMINDER_FILE="i18n-temp/commit-reminder-$(date +%s).txt"
        cat > "$REMINDER_FILE" << EOF
Manufacturing ERP i18n Reminder
Generated: $(date)
Commit: $(git rev-parse HEAD 2>/dev/null || echo "pre-commit")

New hardcoded strings detected in your recent commit.
Please review and localize these strings when convenient.

Detection Results:
$DETECTION_OUTPUT

Recommended Actions:
1. Review the detected strings
2. Add them to the i18n system using t() function  
3. Run: node scripts/i18n-ai-processor.js
4. Use the parallel workflow for safe integration

For help: see i18n-parallel/README.md
EOF
        
        log "📝 Created reminder file: $REMINDER_FILE"
        
    else
        log "✅ No new hardcoded strings detected - great job!"
        echo "✅ i18n Check: No new hardcoded strings detected"
    fi
    
else
    log "❌ i18n detection failed:"
    log "$DETECTION_OUTPUT"
    echo "⚠️  i18n detection encountered an error, but commit will proceed"
    echo "Check $LOG_FILE for details"
fi

log "🎯 i18n detection hook completed"
echo ""

# Always exit 0 to allow commit (ZERO BREAKING CHANGES policy)
exit 0
