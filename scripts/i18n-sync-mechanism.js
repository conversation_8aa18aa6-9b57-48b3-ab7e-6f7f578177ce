#!/usr/bin/env node

/**
 * Manufacturing ERP Translation Sync Mechanism
 * 
 * Safely syncs approved translations from parallel workflow back into
 * the existing i18n-provider.tsx format with comprehensive safety checks.
 * 
 * ZERO BREAKING CHANGES: Full backup, validation, and rollback capability.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const EXISTING_I18N = 'components/i18n-provider.tsx';
const PARALLEL_DIR = 'i18n-parallel';
const BACKUP_DIR = 'i18n-backup';

// Load existing i18n provider content
function loadExistingI18n() {
  try {
    if (!fs.existsSync(EXISTING_I18N)) {
      throw new Error(`Existing i18n file not found: ${EXISTING_I18N}`);
    }
    
    const content = fs.readFileSync(EXISTING_I18N, 'utf8');
    
    // Extract current translations using regex
    const enMatch = content.match(/const\s+en\s*=\s*({[\s\S]*?})\s*;/);
    const zhMatch = content.match(/const\s+zh\s*=\s*({[\s\S]*?})\s*;/);
    
    if (!enMatch || !zhMatch) {
      throw new Error('Could not parse existing translations from i18n-provider.tsx');
    }
    
    // Parse the translation objects (this is safe because we control the format)
    const enTranslations = eval(`(${enMatch[1]})`);
    const zhTranslations = eval(`(${zhMatch[1]})`);
    
    return {
      content,
      enTranslations,
      zhTranslations,
      enMatch: enMatch[0],
      zhMatch: zhMatch[0]
    };
    
  } catch (error) {
    console.error('❌ Failed to load existing i18n:', error.message);
    return null;
  }
}

// Load approved translations from parallel workflow
function loadApprovedTranslations(approvedFile) {
  try {
    const filepath = path.join(PARALLEL_DIR, 'approved', approvedFile);
    
    if (!fs.existsSync(filepath)) {
      throw new Error(`Approved file not found: ${filepath}`);
    }
    
    const content = fs.readFileSync(filepath, 'utf8');
    const data = JSON.parse(content);
    
    // Handle both direct translations and metadata format
    const translations = data.translations || data;
    
    return {
      translations,
      metadata: data.metadata || {},
      filepath
    };
    
  } catch (error) {
    console.error('❌ Failed to load approved translations:', error.message);
    return null;
  }
}

// Create backup before sync
function createSyncBackup() {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(BACKUP_DIR, `sync-backup-${timestamp}`);
    
    fs.mkdirSync(backupDir, { recursive: true });
    
    // Backup existing i18n file
    const backupFile = path.join(backupDir, 'i18n-provider.tsx');
    fs.copyFileSync(EXISTING_I18N, backupFile);
    
    // Create restore script
    const restoreScript = `#!/bin/bash
# Restore script for sync backup ${timestamp}
echo "🔄 Restoring i18n-provider.tsx from sync backup..."
cp "${backupFile}" "${EXISTING_I18N}"
echo "✅ Restored successfully"
`;
    
    const restoreFile = path.join(backupDir, 'restore.sh');
    fs.writeFileSync(restoreFile, restoreScript);
    fs.chmodSync(restoreFile, '755');
    
    console.log(`✅ Created sync backup: ${backupDir}`);
    return backupDir;
    
  } catch (error) {
    console.error('❌ Failed to create sync backup:', error.message);
    return null;
  }
}

// Validate translations before sync
function validateTranslationsForSync(existingData, newTranslations) {
  const validation = {
    valid: true,
    warnings: [],
    errors: [],
    conflicts: [],
    stats: {
      newKeys: 0,
      conflictKeys: 0,
      validKeys: 0
    }
  };
  
  Object.entries(newTranslations).forEach(([key, translationData]) => {
    // Handle both simple string and object format
    const englishText = typeof translationData === 'string' ? translationData : translationData.english;
    const chineseText = typeof translationData === 'string' ? translationData : translationData.chinese;
    
    if (!englishText || !chineseText) {
      validation.errors.push(`Invalid translation data for key: ${key}`);
      return;
    }
    
    // Check for conflicts with existing translations
    if (existingData.enTranslations[key]) {
      validation.conflicts.push({
        key,
        existing: existingData.enTranslations[key],
        new: englishText,
        action: 'skip' // Default safe action
      });
      validation.stats.conflictKeys++;
    } else {
      validation.stats.newKeys++;
      validation.stats.validKeys++;
    }
    
    // Validate translation quality
    if (englishText === chineseText) {
      validation.warnings.push(`No translation provided for key: ${key}`);
    }
    
    if (key.length > 100) {
      validation.warnings.push(`Long key name: ${key}`);
    }
  });
  
  if (validation.errors.length > 0) {
    validation.valid = false;
  }
  
  return validation;
}

// Generate new i18n content with merged translations
function generateMergedContent(existingData, newTranslations, validation) {
  try {
    const mergedEn = { ...existingData.enTranslations };
    const mergedZh = { ...existingData.zhTranslations };
    
    // Add only non-conflicting translations
    Object.entries(newTranslations).forEach(([key, translationData]) => {
      // Skip if there's a conflict (safe approach)
      const hasConflict = validation.conflicts.some(c => c.key === key);
      if (hasConflict) {
        console.log(`⚠️  Skipping conflicting key: ${key}`);
        return;
      }
      
      // Handle both simple string and object format
      const englishText = typeof translationData === 'string' ? translationData : translationData.english;
      const chineseText = typeof translationData === 'string' ? translationData : translationData.chinese;
      
      mergedEn[key] = englishText;
      mergedZh[key] = chineseText;
    });
    
    // Generate new content by replacing the translation objects
    const newEnObject = JSON.stringify(mergedEn, null, 2);
    const newZhObject = JSON.stringify(mergedZh, null, 2);
    
    let newContent = existingData.content;
    
    // Replace English translations
    newContent = newContent.replace(
      existingData.enMatch,
      `const en = ${newEnObject};`
    );
    
    // Replace Chinese translations
    newContent = newContent.replace(
      existingData.zhMatch,
      `const zh = ${newZhObject};`
    );
    
    return {
      content: newContent,
      mergedEn,
      mergedZh,
      addedKeys: Object.keys(newTranslations).filter(key => 
        !validation.conflicts.some(c => c.key === key)
      )
    };
    
  } catch (error) {
    console.error('❌ Failed to generate merged content:', error.message);
    return null;
  }
}

// Perform safe sync operation
async function performSafeSync(approvedFile, options = {}) {
  console.log(`🔄 Starting safe sync for: ${approvedFile}`);
  console.log('⚠️  ZERO BREAKING CHANGES: Full validation and backup\n');
  
  // Load existing i18n data
  console.log('📖 Loading existing i18n data...');
  const existingData = loadExistingI18n();
  if (!existingData) {
    return { success: false, error: 'Failed to load existing i18n data' };
  }
  
  console.log(`✅ Loaded ${Object.keys(existingData.enTranslations).length} existing translations`);
  
  // Load approved translations
  console.log('📥 Loading approved translations...');
  const approvedData = loadApprovedTranslations(approvedFile);
  if (!approvedData) {
    return { success: false, error: 'Failed to load approved translations' };
  }
  
  console.log(`✅ Loaded ${Object.keys(approvedData.translations).length} approved translations`);
  
  // Validate translations
  console.log('🔍 Validating translations for sync...');
  const validation = validateTranslationsForSync(existingData, approvedData.translations);
  
  console.log('📊 Validation Results:');
  console.log(`   New keys: ${validation.stats.newKeys}`);
  console.log(`   Conflicts: ${validation.stats.conflictKeys}`);
  console.log(`   Warnings: ${validation.warnings.length}`);
  console.log(`   Errors: ${validation.errors.length}`);
  
  if (!validation.valid) {
    console.log('❌ Validation failed:');
    validation.errors.forEach(error => console.log(`   - ${error}`));
    return { success: false, error: 'Validation failed', validation };
  }
  
  if (validation.warnings.length > 0) {
    console.log('⚠️  Warnings:');
    validation.warnings.forEach(warning => console.log(`   - ${warning}`));
  }
  
  if (validation.conflicts.length > 0) {
    console.log('⚠️  Conflicts (will be skipped):');
    validation.conflicts.forEach(conflict => console.log(`   - ${conflict.key}`));
  }
  
  // Create backup
  console.log('\n💾 Creating backup...');
  const backupDir = createSyncBackup();
  if (!backupDir) {
    return { success: false, error: 'Failed to create backup' };
  }
  
  // Generate merged content
  console.log('🔄 Generating merged content...');
  const mergedData = generateMergedContent(existingData, approvedData.translations, validation);
  if (!mergedData) {
    return { success: false, error: 'Failed to generate merged content' };
  }
  
  // Dry run check
  if (options.dryRun) {
    console.log('\n🧪 DRY RUN - No changes will be made');
    console.log(`Would add ${mergedData.addedKeys.length} new translations:`);
    mergedData.addedKeys.slice(0, 5).forEach(key => console.log(`   - ${key}`));
    if (mergedData.addedKeys.length > 5) {
      console.log(`   ... and ${mergedData.addedKeys.length - 5} more`);
    }
    return { success: true, dryRun: true, validation, mergedData };
  }
  
  // Write merged content
  console.log('💾 Writing merged content...');
  try {
    fs.writeFileSync(EXISTING_I18N, mergedData.content);
    console.log('✅ Successfully updated i18n-provider.tsx');
  } catch (error) {
    console.error('❌ Failed to write merged content:', error.message);
    return { success: false, error: 'Failed to write merged content' };
  }
  
  // Move approved file to integrated
  const integratedDir = path.join(PARALLEL_DIR, 'integrated');
  const integratedFile = path.join(integratedDir, approvedFile);
  
  try {
    fs.mkdirSync(integratedDir, { recursive: true });
    fs.copyFileSync(approvedData.filepath, integratedFile);
    console.log(`✅ Moved to integrated: ${integratedFile}`);
  } catch (error) {
    console.warn('⚠️  Could not move to integrated:', error.message);
  }
  
  // Create sync log
  const syncLog = {
    timestamp: new Date().toISOString(),
    approvedFile,
    backupDir,
    addedKeys: mergedData.addedKeys,
    skippedKeys: validation.conflicts.map(c => c.key),
    validation,
    success: true
  };
  
  const logFile = path.join(PARALLEL_DIR, 'logs', `sync-${Date.now()}.json`);
  fs.mkdirSync(path.dirname(logFile), { recursive: true });
  fs.writeFileSync(logFile, JSON.stringify(syncLog, null, 2));
  
  console.log('\n📋 SYNC COMPLETED SUCCESSFULLY');
  console.log('='.repeat(50));
  console.log(`Added translations: ${mergedData.addedKeys.length}`);
  console.log(`Skipped conflicts: ${validation.conflicts.length}`);
  console.log(`Backup location: ${backupDir}`);
  console.log(`Sync log: ${logFile}`);
  
  console.log('\n✅ SAFETY CONFIRMED:');
  console.log('- Full backup created before changes');
  console.log('- Conflicting keys safely skipped');
  console.log('- Existing translations preserved');
  console.log('- Rollback capability maintained');
  
  return { 
    success: true, 
    validation, 
    mergedData, 
    backupDir, 
    syncLog 
  };
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  const filename = process.argv[3];
  const isDryRun = process.argv.includes('--dry-run');
  
  switch (command) {
    case 'sync':
      if (!filename) {
        console.error('❌ Please provide approved filename to sync');
        console.log('Usage: node i18n-sync-mechanism.js sync <approved-file> [--dry-run]');
        process.exit(1);
      }
      performSafeSync(filename, { dryRun: isDryRun });
      break;
      
    case 'list':
      const approvedDir = path.join(PARALLEL_DIR, 'approved');
      if (fs.existsSync(approvedDir)) {
        const files = fs.readdirSync(approvedDir).filter(f => f.endsWith('.json'));
        console.log('📋 Approved translations ready for sync:');
        files.forEach((file, index) => {
          console.log(`   ${index + 1}. ${file}`);
        });
      } else {
        console.log('📋 No approved translations found');
      }
      break;
      
    default:
      console.log('📖 Manufacturing ERP Translation Sync Mechanism');
      console.log('');
      console.log('Commands:');
      console.log('  sync <file> [--dry-run] - Sync approved translations to existing system');
      console.log('  list                    - List approved translations ready for sync');
      console.log('');
      console.log('Examples:');
      console.log('  node i18n-sync-mechanism.js list');
      console.log('  node i18n-sync-mechanism.js sync batch-1-2025-09-15.json --dry-run');
      console.log('  node i18n-sync-mechanism.js sync batch-1-2025-09-15.json');
  }
}

module.exports = { performSafeSync };
