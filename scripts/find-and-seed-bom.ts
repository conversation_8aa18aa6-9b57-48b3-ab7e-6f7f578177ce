/**
 * Manufacturing ERP - Find Active Company and Seed BOM Data
 * 
 * Finds the company with active demand forecasts and creates BOM data for their products
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1C MRP Implementation
 */

import { db, uid } from "../lib/db"
import { 
  products, 
  rawMaterials, 
  billOfMaterials, 
  suppliers,
  companies,
  demandForecasts
} from "../lib/schema-postgres"
import { eq, and } from "drizzle-orm"

async function findAndSeedBOM() {
  try {
    console.log("🔍 Finding active companies with demand forecasts...")

    // Find companies that have demand forecasts
    const companiesWithForecasts = await db.query.demandForecasts.findMany({
      with: {
        company: true,
        product: true,
      },
      limit: 10,
    })

    if (companiesWithForecasts.length === 0) {
      console.log("❌ No companies with demand forecasts found")
      return
    }

    console.log(`📊 Found ${companiesWithForecasts.length} demand forecasts`)

    // Group by company
    const companiesByForecast = new Map()
    for (const forecast of companiesWithForecasts) {
      const companyId = forecast.company_id
      if (!companiesByForecast.has(companyId)) {
        companiesByForecast.set(companyId, {
          company: forecast.company,
          forecasts: [],
          products: new Set(),
        })
      }
      companiesByForecast.get(companyId).forecasts.push(forecast)
      companiesByForecast.get(companyId).products.add(forecast.product)
    }

    console.log(`🏢 Found ${companiesByForecast.size} companies with forecasts`)

    // Process each company
    for (const [companyId, data] of companiesByForecast) {
      console.log(`\n🏢 Processing company: ${data.company.name} (${companyId})`)
      console.log(`   📦 Products with forecasts: ${data.products.size}`)
      console.log(`   📊 Total forecasts: ${data.forecasts.length}`)

      // Check if BOM already exists for any product
      const existingBom = await db.query.billOfMaterials.findFirst({
        where: eq(billOfMaterials.company_id, companyId),
      })

      if (existingBom) {
        console.log(`   ✅ BOM data already exists for this company`)
        continue
      }

      // Get or create raw materials for this company
      let existingRawMaterials = await db.query.rawMaterials.findMany({
        where: eq(rawMaterials.company_id, companyId),
      })

      if (existingRawMaterials.length === 0) {
        console.log(`   🔧 Creating raw materials and supplier...`)

        // Create a supplier first
        let supplier = await db.query.suppliers.findFirst({
          where: eq(suppliers.company_id, companyId),
        })

        if (!supplier) {
          const supplierId = uid("sup")
          await db.insert(suppliers).values({
            id: supplierId,
            company_id: companyId,
            name: "Premium Materials Supplier",
            email: "<EMAIL>",
            phone: "******-0456",
            address: "456 Supplier Ave",
            city: "Material City",
            state: "MC",
            postal_code: "12345",
            country: "USA",
          })
          
          supplier = { id: supplierId } as any
          console.log(`   ✅ Created supplier: ${supplierId}`)
        }

        // Create raw materials
        const sampleRawMaterials = [
          {
            id: uid("rm"),
            company_id: companyId,
            sku: "YARN-COTTON-30S",
            name: "Cotton Yarn 30s",
            category: "yarn",
            unit: "kg",
            primary_supplier_id: supplier.id,
            composition: "100% Cotton",
            quality_grade: "Premium",
            standard_cost: "4.50",
            currency: "USD",
            reorder_point: "100",
            max_stock_level: "1000",
            inspection_required: "true",
          },
          {
            id: uid("rm"),
            company_id: companyId,
            sku: "FABRIC-SILK-200GSM",
            name: "Premium Silk Fabric 200GSM",
            category: "fabric",
            unit: "meters",
            primary_supplier_id: supplier.id,
            composition: "100% Silk",
            quality_grade: "Premium",
            standard_cost: "8.50",
            currency: "USD",
            reorder_point: "50",
            max_stock_level: "500",
            inspection_required: "true",
          },
          {
            id: uid("rm"),
            company_id: companyId,
            sku: "DYE-REACTIVE-BLUE",
            name: "Reactive Blue Dye",
            category: "dyes",
            unit: "kg",
            primary_supplier_id: supplier.id,
            composition: "Reactive Dye",
            quality_grade: "Industrial",
            standard_cost: "12.00",
            currency: "USD",
            reorder_point: "25",
            max_stock_level: "200",
            inspection_required: "true",
          },
        ]

        await db.insert(rawMaterials).values(sampleRawMaterials)
        console.log(`   ✅ Created ${sampleRawMaterials.length} raw materials`)
        
        existingRawMaterials = sampleRawMaterials
      }

      // Create BOM for each product with forecasts
      let bomItemsCreated = 0
      
      for (const product of data.products) {
        console.log(`   🔧 Creating BOM for product: ${product.name} (${product.sku})`)

        // Create BOM items for this product
        const materialsToUse = existingRawMaterials.slice(0, Math.min(3, existingRawMaterials.length))
        
        for (let i = 0; i < materialsToUse.length; i++) {
          const material = materialsToUse[i]
          
          // Calculate realistic quantities based on material type
          let qtyRequired = "1.0"
          let wasteFactor = "0.05"
          
          if (material.category === "yarn") {
            qtyRequired = "0.8" // 0.8 kg yarn per product unit
            wasteFactor = "0.10" // 10% waste for yarn
          } else if (material.category === "fabric") {
            qtyRequired = "1.5" // 1.5 meters fabric per product unit
            wasteFactor = "0.05" // 5% waste for fabric
          } else if (material.category === "dyes") {
            qtyRequired = "0.08" // 0.08 kg dye per product unit
            wasteFactor = "0.02" // 2% waste for dyes
          }

          const bomItem = {
            id: uid("bom"),
            company_id: companyId,
            product_id: product.id,
            raw_material_id: material.id,
            qty_required: qtyRequired,
            unit: material.unit,
            waste_factor: wasteFactor,
            status: "active",
          }

          await db.insert(billOfMaterials).values(bomItem)
          bomItemsCreated++
          
          console.log(`     ✅ ${material.name}: ${qtyRequired} ${material.unit} (${wasteFactor} waste)`)
        }
      }

      console.log(`   🎯 Total BOM items created: ${bomItemsCreated}`)
    }

    console.log("\n🎉 BOM data seeding completed successfully!")
    console.log("\n📋 Next steps:")
    console.log("   1. Approved demand forecasts will now generate procurement plans")
    console.log("   2. Material requirements can be calculated from forecasts")
    console.log("   3. Procurement planning dashboard will show active plans")

  } catch (error) {
    console.error("❌ Error finding and seeding BOM data:", error)
    throw error
  }
}

// Run the script
findAndSeedBOM()
  .then(() => {
    console.log("✅ Script completed successfully")
    process.exit(0)
  })
  .catch((error) => {
    console.error("❌ Script failed:", error)
    process.exit(1)
  })
