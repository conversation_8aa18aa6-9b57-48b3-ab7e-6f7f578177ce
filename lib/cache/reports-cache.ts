/**
 * Manufacturing ERP - Reports Caching Service
 * 
 * Enterprise-grade caching system for reports module to improve performance
 * while maintaining 100% backward compatibility and multi-tenant security.
 * 
 * Features:
 * - Multi-tenant cache isolation
 * - Configurable TTL per report type
 * - Automatic cache invalidation
 * - Memory-based caching (production-ready for Redis upgrade)
 * - Zero breaking changes to existing APIs
 */

import { TenantContext } from "@/lib/tenant-utils"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface CacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number
  companyId: string
  cacheKey: string
}

export interface CacheOptions {
  ttl?: number // Time to live in seconds
  forceRefresh?: boolean
  includeMetadata?: boolean
}

export interface CacheMetrics {
  hits: number
  misses: number
  totalRequests: number
  hitRate: number
  cacheSize: number
  lastCleared: string | null
}

// ============================================================================
// CACHE CONFIGURATION
// ============================================================================

const CACHE_CONFIG = {
  // Default TTL values for different report types (in seconds)
  DEFAULT_TTL: 300, // 5 minutes
  REPORT_TTLS: {
    'reports-overview': 180, // 3 minutes - frequently updated
    'financial-performance': 600, // 10 minutes - financial data
    'production-analytics': 300, // 5 minutes - production data
    'business-intelligence': 900, // 15 minutes - complex analytics
    'quality-metrics': 300, // 5 minutes - quality data
    'inventory-intelligence': 240, // 4 minutes - inventory changes frequently
    'mrp-planning': 1800, // 30 minutes - planning data is more stable
  },
  MAX_CACHE_SIZE: 1000, // Maximum number of cache entries
  CLEANUP_INTERVAL: 300000, // 5 minutes in milliseconds
} as const

// ============================================================================
// IN-MEMORY CACHE IMPLEMENTATION
// ============================================================================

class ReportsCacheService {
  private cache = new Map<string, CacheEntry>()
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    totalRequests: 0,
    hitRate: 0,
    cacheSize: 0,
    lastCleared: null,
  }
  private cleanupTimer: NodeJS.Timeout | null = null

  constructor() {
    this.startCleanupTimer()
  }

  /**
   * Generate cache key with multi-tenant isolation
   */
  private generateCacheKey(
    reportType: string,
    companyId: string,
    params: Record<string, any> = {}
  ): string {
    const paramString = Object.keys(params)
      .sort()
      .map(key => `${key}:${params[key]}`)
      .join('|')
    
    return `reports:${companyId}:${reportType}:${paramString}`
  }

  /**
   * Get TTL for specific report type
   */
  private getTTL(reportType: string): number {
    return CACHE_CONFIG.REPORT_TTLS[reportType as keyof typeof CACHE_CONFIG.REPORT_TTLS] 
           || CACHE_CONFIG.DEFAULT_TTL
  }

  /**
   * Check if cache entry is valid (not expired)
   */
  private isValidEntry(entry: CacheEntry): boolean {
    const now = Date.now()
    return (now - entry.timestamp) < (entry.ttl * 1000)
  }

  /**
   * Get cached data for a report
   */
  async get<T>(
    reportType: string,
    context: TenantContext,
    params: Record<string, any> = {},
    options: CacheOptions = {}
  ): Promise<T | null> {
    this.metrics.totalRequests++

    if (options.forceRefresh) {
      this.metrics.misses++
      this.updateHitRate()
      return null
    }

    const cacheKey = this.generateCacheKey(reportType, context.companyId, params)
    const entry = this.cache.get(cacheKey)

    if (!entry || !this.isValidEntry(entry)) {
      this.metrics.misses++
      this.updateHitRate()
      return null
    }

    // Verify tenant isolation
    if (entry.companyId !== context.companyId) {
      console.warn(`🚨 Cache security violation: Key ${cacheKey} accessed by wrong tenant`)
      this.metrics.misses++
      this.updateHitRate()
      return null
    }

    this.metrics.hits++
    this.updateHitRate()

    console.log(`📋 Cache HIT: ${reportType} for company ${context.companyId}`)
    
    if (options.includeMetadata) {
      return {
        ...entry.data,
        _cacheMetadata: {
          cached: true,
          timestamp: entry.timestamp,
          ttl: entry.ttl,
          age: Math.floor((Date.now() - entry.timestamp) / 1000),
        }
      } as T
    }

    return entry.data
  }

  /**
   * Set cached data for a report
   */
  async set<T>(
    reportType: string,
    context: TenantContext,
    data: T,
    params: Record<string, any> = {},
    options: CacheOptions = {}
  ): Promise<void> {
    const cacheKey = this.generateCacheKey(reportType, context.companyId, params)
    const ttl = options.ttl || this.getTTL(reportType)

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      companyId: context.companyId,
      cacheKey,
    }

    this.cache.set(cacheKey, entry)
    this.metrics.cacheSize = this.cache.size

    console.log(`📋 Cache SET: ${reportType} for company ${context.companyId} (TTL: ${ttl}s)`)

    // Cleanup if cache is too large
    if (this.cache.size > CACHE_CONFIG.MAX_CACHE_SIZE) {
      this.cleanup()
    }
  }

  /**
   * Invalidate cache for specific report type and company
   */
  async invalidate(
    reportType: string,
    context: TenantContext,
    params: Record<string, any> = {}
  ): Promise<void> {
    const cacheKey = this.generateCacheKey(reportType, context.companyId, params)
    const deleted = this.cache.delete(cacheKey)
    
    if (deleted) {
      this.metrics.cacheSize = this.cache.size
      console.log(`📋 Cache INVALIDATED: ${reportType} for company ${context.companyId}`)
    }
  }

  /**
   * Invalidate all cache entries for a company
   */
  async invalidateCompany(companyId: string): Promise<void> {
    let deletedCount = 0
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.companyId === companyId) {
        this.cache.delete(key)
        deletedCount++
      }
    }

    this.metrics.cacheSize = this.cache.size
    console.log(`📋 Cache COMPANY INVALIDATED: ${deletedCount} entries for company ${companyId}`)
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    this.cache.clear()
    this.metrics.cacheSize = 0
    this.metrics.lastCleared = new Date().toISOString()
    console.log(`📋 Cache CLEARED: All entries removed`)
  }

  /**
   * Get cache metrics
   */
  getMetrics(): CacheMetrics {
    return { ...this.metrics }
  }

  /**
   * Update hit rate calculation
   */
  private updateHitRate(): void {
    this.metrics.hitRate = this.metrics.totalRequests > 0 
      ? (this.metrics.hits / this.metrics.totalRequests) * 100 
      : 0
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now()
    let cleanedCount = 0

    for (const [key, entry] of this.cache.entries()) {
      if (!this.isValidEntry(entry)) {
        this.cache.delete(key)
        cleanedCount++
      }
    }

    this.metrics.cacheSize = this.cache.size
    
    if (cleanedCount > 0) {
      console.log(`📋 Cache CLEANUP: Removed ${cleanedCount} expired entries`)
    }
  }

  /**
   * Start automatic cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, CACHE_CONFIG.CLEANUP_INTERVAL)
  }

  /**
   * Stop cleanup timer (for testing or shutdown)
   */
  stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const reportsCacheService = new ReportsCacheService()

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Wrapper function for caching report data with automatic cache management
 */
export async function withReportsCache<T>(
  reportType: string,
  context: TenantContext,
  dataFetcher: () => Promise<T>,
  params: Record<string, any> = {},
  options: CacheOptions = {}
): Promise<T> {
  // Try to get from cache first
  const cachedData = await reportsCacheService.get<T>(reportType, context, params, options)
  
  if (cachedData !== null) {
    return cachedData
  }

  // Cache miss - fetch fresh data
  console.log(`📋 Cache MISS: Fetching fresh data for ${reportType}`)
  const freshData = await dataFetcher()

  // Cache the fresh data
  await reportsCacheService.set(reportType, context, freshData, params, options)

  return freshData
}

/**
 * Generate cache key for external use (debugging, monitoring)
 */
export function generateReportsCacheKey(
  reportType: string,
  companyId: string,
  params: Record<string, any> = {}
): string {
  const paramString = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|')
  
  return `reports:${companyId}:${reportType}:${paramString}`
}
