/**
 * Manufacturing ERP - Structured Workflow Logging Service
 * 
 * Enterprise-grade structured logging for business workflows to improve
 * debugging and monitoring capabilities without affecting current operations.
 * 
 * Features:
 * - Multi-tenant logging isolation
 * - Structured log format for easy parsing
 * - Business workflow event tracking
 * - Performance monitoring integration
 * - Zero breaking changes to existing functionality
 */

import { TenantContext } from "@/lib/tenant-utils"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'critical'

export type WorkflowStep = 
  | 'contract_created' | 'contract_approved' | 'contract_completed'
  | 'work_order_generated' | 'work_order_started' | 'work_order_completed'
  | 'quality_inspection_created' | 'quality_inspection_completed' | 'quality_approved' | 'quality_rejected'
  | 'inventory_updated' | 'stock_lot_created' | 'stock_transaction_recorded'
  | 'shipment_created' | 'shipment_dispatched' | 'shipment_delivered'
  | 'export_declaration_created' | 'export_declaration_submitted'
  | 'invoice_generated' | 'payment_received' | 'payment_overdue'

export type EntityType = 
  | 'sales_contract' | 'purchase_contract' | 'work_order' | 'quality_inspection'
  | 'stock_lot' | 'shipment' | 'export_declaration' | 'ar_invoice' | 'ap_invoice'
  | 'customer' | 'supplier' | 'product' | 'raw_material'

export interface WorkflowLogEntry {
  timestamp: string
  level: LogLevel
  companyId: string
  userId?: string
  workflowStep: WorkflowStep
  entityType: EntityType
  entityId: string
  message: string
  metadata?: Record<string, any>
  performance?: {
    duration?: number
    startTime?: number
    endTime?: number
  }
  context?: {
    userAgent?: string
    ipAddress?: string
    sessionId?: string
  }
  correlationId?: string
  parentEntityType?: EntityType
  parentEntityId?: string
}

export interface WorkflowMetrics {
  totalEvents: number
  eventsByLevel: Record<LogLevel, number>
  eventsByWorkflowStep: Record<WorkflowStep, number>
  eventsByEntityType: Record<EntityType, number>
  averageProcessingTime: number
  errorRate: number
  lastUpdated: string
}

// ============================================================================
// WORKFLOW LOGGER SERVICE
// ============================================================================

class WorkflowLoggerService {
  private logs: WorkflowLogEntry[] = []
  private maxLogSize = 10000 // Keep last 10k entries in memory
  private metricsCache: WorkflowMetrics | null = null
  private metricsCacheExpiry = 0

  /**
   * Log a workflow event with structured data
   */
  log(
    level: LogLevel,
    workflowStep: WorkflowStep,
    entityType: EntityType,
    entityId: string,
    message: string,
    context?: TenantContext,
    metadata?: Record<string, any>,
    performance?: { duration?: number; startTime?: number; endTime?: number }
  ): void {
    const entry: WorkflowLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      companyId: context?.companyId || 'unknown',
      userId: context?.userId,
      workflowStep,
      entityType,
      entityId,
      message,
      metadata,
      performance,
      correlationId: this.generateCorrelationId(),
    }

    // Add to in-memory storage
    this.logs.push(entry)

    // Maintain max size
    if (this.logs.length > this.maxLogSize) {
      this.logs = this.logs.slice(-this.maxLogSize)
    }

    // Console output with structured format
    this.outputToConsole(entry)

    // Clear metrics cache
    this.metricsCache = null
  }

  /**
   * Log workflow step completion
   */
  logWorkflowStep(
    workflowStep: WorkflowStep,
    entityType: EntityType,
    entityId: string,
    context: TenantContext,
    metadata?: Record<string, any>,
    performance?: { duration?: number }
  ): void {
    const message = `🔄 Workflow: ${workflowStep} completed for ${entityType}:${entityId}`
    
    this.log('info', workflowStep, entityType, entityId, message, context, {
      ...metadata,
      workflowEvent: true,
    }, performance)
  }

  /**
   * Log workflow error
   */
  logWorkflowError(
    workflowStep: WorkflowStep,
    entityType: EntityType,
    entityId: string,
    error: Error | string,
    context: TenantContext,
    metadata?: Record<string, any>
  ): void {
    const errorMessage = error instanceof Error ? error.message : error
    const message = `❌ Workflow Error: ${workflowStep} failed for ${entityType}:${entityId} - ${errorMessage}`
    
    this.log('error', workflowStep, entityType, entityId, message, context, {
      ...metadata,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : { message: error },
      workflowError: true,
    })
  }

  /**
   * Log performance metrics for workflow steps
   */
  logPerformance(
    workflowStep: WorkflowStep,
    entityType: EntityType,
    entityId: string,
    startTime: number,
    context: TenantContext,
    metadata?: Record<string, any>
  ): void {
    const endTime = Date.now()
    const duration = endTime - startTime
    const message = `⚡ Performance: ${workflowStep} for ${entityType}:${entityId} took ${duration}ms`
    
    this.log('debug', workflowStep, entityType, entityId, message, context, {
      ...metadata,
      performanceLog: true,
    }, {
      duration,
      startTime,
      endTime,
    })
  }

  /**
   * Get workflow logs for a specific company
   */
  getLogsForCompany(companyId: string, limit = 100): WorkflowLogEntry[] {
    return this.logs
      .filter(log => log.companyId === companyId)
      .slice(-limit)
      .reverse() // Most recent first
  }

  /**
   * Get workflow logs for a specific entity
   */
  getLogsForEntity(
    entityType: EntityType,
    entityId: string,
    companyId: string,
    limit = 50
  ): WorkflowLogEntry[] {
    return this.logs
      .filter(log => 
        log.companyId === companyId &&
        log.entityType === entityType &&
        log.entityId === entityId
      )
      .slice(-limit)
      .reverse()
  }

  /**
   * Get workflow metrics
   */
  getMetrics(companyId?: string): WorkflowMetrics {
    const now = Date.now()
    
    // Return cached metrics if still valid (5 minutes)
    if (this.metricsCache && now < this.metricsCacheExpiry) {
      return this.metricsCache
    }

    const relevantLogs = companyId 
      ? this.logs.filter(log => log.companyId === companyId)
      : this.logs

    const metrics: WorkflowMetrics = {
      totalEvents: relevantLogs.length,
      eventsByLevel: this.groupByField(relevantLogs, 'level') as Record<LogLevel, number>,
      eventsByWorkflowStep: this.groupByField(relevantLogs, 'workflowStep') as Record<WorkflowStep, number>,
      eventsByEntityType: this.groupByField(relevantLogs, 'entityType') as Record<EntityType, number>,
      averageProcessingTime: this.calculateAverageProcessingTime(relevantLogs),
      errorRate: this.calculateErrorRate(relevantLogs),
      lastUpdated: new Date().toISOString(),
    }

    // Cache for 5 minutes
    this.metricsCache = metrics
    this.metricsCacheExpiry = now + (5 * 60 * 1000)

    return metrics
  }

  /**
   * Clear logs for a specific company (for testing/cleanup)
   */
  clearLogsForCompany(companyId: string): void {
    this.logs = this.logs.filter(log => log.companyId !== companyId)
    this.metricsCache = null
  }

  /**
   * Clear all logs (for testing/cleanup)
   */
  clearAllLogs(): void {
    this.logs = []
    this.metricsCache = null
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private outputToConsole(entry: WorkflowLogEntry): void {
    const emoji = this.getLevelEmoji(entry.level)
    const prefix = `${emoji} [${entry.level.toUpperCase()}] [${entry.companyId}]`
    const suffix = entry.performance?.duration ? ` (${entry.performance.duration}ms)` : ''
    
    const logMessage = `${prefix} ${entry.message}${suffix}`
    
    switch (entry.level) {
      case 'error':
      case 'critical':
        console.error(logMessage, entry.metadata)
        break
      case 'warn':
        console.warn(logMessage, entry.metadata)
        break
      case 'debug':
        if (process.env.NODE_ENV === 'development') {
          console.debug(logMessage, entry.metadata)
        }
        break
      default:
        console.log(logMessage, entry.metadata)
    }
  }

  private getLevelEmoji(level: LogLevel): string {
    const emojis = {
      debug: '🔍',
      info: '📋',
      warn: '⚠️',
      error: '❌',
      critical: '🚨',
    }
    return emojis[level] || '📋'
  }

  private generateCorrelationId(): string {
    return `wf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private groupByField(logs: WorkflowLogEntry[], field: keyof WorkflowLogEntry): Record<string, number> {
    return logs.reduce((acc, log) => {
      const key = String(log[field])
      acc[key] = (acc[key] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  private calculateAverageProcessingTime(logs: WorkflowLogEntry[]): number {
    const logsWithDuration = logs.filter(log => log.performance?.duration)
    if (logsWithDuration.length === 0) return 0
    
    const totalDuration = logsWithDuration.reduce((sum, log) => sum + (log.performance?.duration || 0), 0)
    return Math.round(totalDuration / logsWithDuration.length)
  }

  private calculateErrorRate(logs: WorkflowLogEntry[]): number {
    if (logs.length === 0) return 0
    const errorLogs = logs.filter(log => log.level === 'error' || log.level === 'critical')
    return Math.round((errorLogs.length / logs.length) * 100 * 100) / 100 // Round to 2 decimal places
  }
}

// ============================================================================
// SINGLETON INSTANCE AND HELPER FUNCTIONS
// ============================================================================

export const workflowLogger = new WorkflowLoggerService()

/**
 * Convenience function for logging workflow steps
 */
export function logWorkflowStep(
  step: WorkflowStep,
  entityType: EntityType,
  entityId: string,
  context: TenantContext,
  metadata?: Record<string, any>
): void {
  workflowLogger.logWorkflowStep(step, entityType, entityId, context, metadata)
}

/**
 * Convenience function for logging workflow errors
 */
export function logWorkflowError(
  step: WorkflowStep,
  entityType: EntityType,
  entityId: string,
  error: Error | string,
  context: TenantContext,
  metadata?: Record<string, any>
): void {
  workflowLogger.logWorkflowError(step, entityType, entityId, error, context, metadata)
}

/**
 * Performance timing wrapper for workflow operations
 */
export async function withWorkflowTiming<T>(
  step: WorkflowStep,
  entityType: EntityType,
  entityId: string,
  context: TenantContext,
  operation: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const startTime = Date.now()
  
  try {
    const result = await operation()
    workflowLogger.logPerformance(step, entityType, entityId, startTime, context, metadata)
    return result
  } catch (error) {
    workflowLogger.logWorkflowError(step, entityType, entityId, error as Error, context, metadata)
    throw error
  }
}
