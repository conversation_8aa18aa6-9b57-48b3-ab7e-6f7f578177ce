/**
 * Operational Financial Integration Service
 * 
 * Provides real-time financial impact calculations for operational views.
 * Integrates with existing shipping and inventory modules without breaking changes.
 * 
 * @version 1.0.0 - Task 2.1 Operational Integration Panel
 */

import { db } from "@/lib/db"
import { products, stockLots, shipmentItems, arInvoices, shipments } from "@/lib/schema-postgres"
import { eq, and, sum, sql } from "drizzle-orm"

export interface FinancialImpact {
  revenue: number
  cogs: number
  grossProfit: number
  profitMargin: number
  currency: string
}

export interface InventoryFinancialImpact {
  currentValue: number
  valueChange: number
  averageCostPerUnit: number
  totalUnits: number
  currency: string
}

export interface ShipmentFinancialImpact extends FinancialImpact {
  invoiceGenerated: boolean
  invoiceId?: string
  containerUtilization?: number
  profitPerUnit: number
}

export interface TenantContext {
  companyId: string
  userId: string
}

export class OperationalFinancialIntegration {
  constructor(private context: TenantContext) { }

  /**
   * Calculate financial impact for a shipment
   * Used in shipping module to show real-time financial effects
   */
  async getShipmentFinancialImpact(shipmentId: string): Promise<ShipmentFinancialImpact> {
    // Get shipment with items and products
    const shipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, shipmentId),
        eq(shipments.company_id, this.context.companyId)
      ),
      with: {
        items: {
          with: {
            product: true
          }
        }
      }
    })

    if (!shipment) {
      throw new Error(`Shipment ${shipmentId} not found`)
    }

    // Calculate revenue (selling prices)
    const revenue = shipment.items.reduce((sum, item) => {
      const quantity = parseFloat(item.quantity || '0')
      const unitPrice = parseFloat(item.unit_price || '0')
      return sum + (quantity * unitPrice)
    }, 0)

    // Calculate COGS (cost prices) - using our fixed COGS logic
    const cogs = shipment.items.reduce((sum, item) => {
      const quantity = parseFloat(item.quantity || '0')
      const costPrice = item.product?.cost_price && parseFloat(item.product.cost_price) > 0
        ? parseFloat(item.product.cost_price)
        : item.product?.price && parseFloat(item.product.price) > 0
          ? parseFloat(item.product.price) * 0.65 // 35% margin fallback
          : 20 // Default cost price
      return sum + (quantity * costPrice)
    }, 0)

    const grossProfit = revenue - cogs
    const profitMargin = revenue > 0 ? (grossProfit / revenue) * 100 : 0

    // Check if AR invoice exists
    const allInvoices = await db.query.arInvoices.findMany({
      where: and(
        eq(arInvoices.company_id, this.context.companyId),
        eq(arInvoices.customer_id, shipment.customer_id)
      )
    })

    const invoice = allInvoices.find(inv =>
      inv.notes?.includes(shipmentId) || inv.notes?.includes(shipment.shipment_number || '')
    )

    // Calculate profit per unit
    const totalUnits = shipment.items.reduce((sum, item) => sum + parseFloat(item.quantity || '0'), 0)
    const profitPerUnit = totalUnits > 0 ? grossProfit / totalUnits : 0

    return {
      revenue,
      cogs,
      grossProfit,
      profitMargin,
      currency: 'USD', // Default currency
      invoiceGenerated: !!invoice,
      invoiceId: invoice?.id,
      profitPerUnit
    }
  }

  /**
   * Calculate financial impact for inventory changes
   * Used in inventory module to show value changes
   */
  async getInventoryFinancialImpact(productId: string, quantityChange: number): Promise<InventoryFinancialImpact> {
    // Get product with cost information
    const product = await db.query.products.findFirst({
      where: and(
        eq(products.id, productId),
        eq(products.company_id, this.context.companyId)
      )
    })

    if (!product) {
      throw new Error(`Product ${productId} not found`)
    }

    // Get current stock levels
    const stockLotData = await db.query.stockLots.findMany({
      where: and(
        eq(stockLots.product_id, productId),
        eq(stockLots.company_id, this.context.companyId)
      )
    })

    const totalUnits = stockLotData.reduce((sum, lot) => sum + parseFloat(lot.qty || '0'), 0)

    // Calculate cost price
    const costPrice = product.cost_price && parseFloat(product.cost_price) > 0
      ? parseFloat(product.cost_price)
      : product.price && parseFloat(product.price) > 0
        ? parseFloat(product.price) * 0.65 // 35% margin fallback
        : 20 // Default cost price

    const currentValue = totalUnits * costPrice
    const valueChange = quantityChange * costPrice

    return {
      currentValue,
      valueChange,
      averageCostPerUnit: costPrice,
      totalUnits,
      currency: 'USD'
    }
  }

  /**
   * Get financial summary for multiple products (inventory overview)
   */
  async getInventoryPortfolioImpact(productIds: string[]): Promise<{
    totalValue: number
    totalUnits: number
    averageCostPerUnit: number
    currency: string
  }> {
    if (productIds.length === 0) {
      return {
        totalValue: 0,
        totalUnits: 0,
        averageCostPerUnit: 0,
        currency: 'USD'
      }
    }

    let totalValue = 0
    let totalUnits = 0
    let totalCostBasis = 0

    for (const productId of productIds) {
      try {
        const impact = await this.getInventoryFinancialImpact(productId, 0)
        totalValue += impact.currentValue
        totalUnits += impact.totalUnits
        totalCostBasis += impact.totalUnits * impact.averageCostPerUnit
      } catch (error) {
        console.warn(`Failed to get financial impact for product ${productId}:`, error)
        // Continue with other products
      }
    }

    const averageCostPerUnit = totalUnits > 0 ? totalCostBasis / totalUnits : 0

    return {
      totalValue,
      totalUnits,
      averageCostPerUnit,
      currency: 'USD'
    }
  }

  /**
   * Calculate container utilization financial impact
   * Export-specific metric for container shipping businesses
   */
  async getContainerFinancialImpact(shipmentId: string): Promise<{
    revenuePerContainer: number
    cogsPerContainer: number
    profitPerContainer: number
    utilizationRate: number
    currency: string
  }> {
    const impact = await this.getShipmentFinancialImpact(shipmentId)

    // Assume standard container capacity (this could be configurable)
    const standardContainerCapacity = 100 // units
    const shipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, shipmentId),
        eq(shipments.company_id, this.context.companyId)
      ),
      with: { items: true }
    })

    const totalUnits = shipment?.items.reduce((sum, item) => sum + parseFloat(item.quantity || '0'), 0) || 0
    const utilizationRate = totalUnits > 0 ? Math.min((totalUnits / standardContainerCapacity) * 100, 100) : 0

    return {
      revenuePerContainer: impact.revenue,
      cogsPerContainer: impact.cogs,
      profitPerContainer: impact.grossProfit,
      utilizationRate,
      currency: impact.currency
    }
  }
}
