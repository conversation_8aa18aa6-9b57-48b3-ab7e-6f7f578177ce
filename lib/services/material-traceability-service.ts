/**
 * Manufacturing ERP - Material Traceability Service
 * Professional service for complete supply chain traceability
 * Links finished goods to consumed raw materials for regulatory compliance
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0
 */

import { db, uid } from "@/lib/db"
import {
  workOrders,
  stockLots,
  materialConsumption,
  rawMaterialLots,
  rawMaterials,
  products,
  suppliers,
} from "@/lib/schema-postgres"
import { shipments, shipmentItems } from "@/lib/schema-shipping"
import {
  productMaterialTraceability,
  shipmentMaterialTraceability,
} from "@/lib/schema-material-traceability"
import { eq, and, desc } from "drizzle-orm"
import { z } from "zod"

// ✅ PROFESSIONAL: Type definitions
export interface TraceabilityContext {
  companyId: string
  userId: string
  source: string
}

export interface ProductTraceabilityRecord {
  id: string
  workOrderId: string
  workOrderNumber: string
  stockLotId: string
  productId: string
  productName: string
  productSku: string
  rawMaterialId: string
  rawMaterialName: string
  rawMaterialSku: string
  rawMaterialLotId: string
  rawMaterialLotNumber?: string
  supplierId?: string
  supplierName?: string
  consumedQuantity: number
  finishedQuantity: number
  consumptionRatio: number
  productionDate: string
  productionBatch?: string
  rawMaterialQualityStatus: string
  finishedGoodsQualityStatus?: string
}

export interface ShipmentTraceabilityRecord {
  id: string
  shipmentId: string
  shipmentNumber: string
  shipmentItemId: string
  productId: string
  productName: string
  productSku: string
  stockLotId: string
  rawMaterials: Array<{
    materialId: string
    materialName: string
    materialSku: string
    lotId: string
    lotNumber?: string
    supplierId?: string
    supplierName: string
    quantity: number
    qualityStatus: string
  }>
  shippedQuantity: number
  workOrderId: string
  workOrderNumber: string
  productionDate: string
  shipDate?: string
  trackingNumber?: string
}

// ✅ PROFESSIONAL: Validation schemas
const traceabilityContextSchema = z.object({
  companyId: z.string().min(1, "Company ID is required"),
  userId: z.string().min(1, "User ID is required"),
  source: z.string().min(1, "Source is required"),
})

export class MaterialTraceabilityService {
  private context: TraceabilityContext

  constructor(context: TraceabilityContext) {
    this.context = traceabilityContextSchema.parse(context)
  }

  /**
   * Create product material traceability records when work order is completed
   */
  async createProductTraceability(
    workOrderId: string,
    stockLotId: string
  ): Promise<ProductTraceabilityRecord[]> {
    try {
      console.log(`🔍 Creating product traceability for work order: ${workOrderId}`)

      // Get work order details
      const workOrder = await db.query.workOrders.findFirst({
        where: and(
          eq(workOrders.id, workOrderId),
          eq(workOrders.company_id, this.context.companyId)
        ),
        with: {
          product: true,
        }
      })

      if (!workOrder) {
        throw new Error(`Work order ${workOrderId} not found`)
      }

      // Get stock lot details
      const stockLot = await db.query.stockLots.findFirst({
        where: and(
          eq(stockLots.id, stockLotId),
          eq(stockLots.company_id, this.context.companyId)
        )
      })

      if (!stockLot) {
        throw new Error(`Stock lot ${stockLotId} not found`)
      }

      // Get all material consumption records for this work order
      const consumptionRecords = await db.query.materialConsumption.findMany({
        where: and(
          eq(materialConsumption.work_order_id, workOrderId),
          eq(materialConsumption.company_id, this.context.companyId)
        ),
        with: {
          rawMaterialLot: {
            with: {
              rawMaterial: true,
              supplier: true,
            }
          }
        }
      })

      const traceabilityRecords: ProductTraceabilityRecord[] = []
      const finishedQuantity = parseFloat(stockLot.qty || "0")

      // Create traceability record for each consumed material
      for (const consumption of consumptionRecords) {
        if (!consumption.rawMaterialLot?.rawMaterial) continue

        const consumedQty = parseFloat(consumption.qty_consumed || "0")
        const consumptionRatio = finishedQuantity > 0 ? consumedQty / finishedQuantity : 0

        const traceabilityId = uid()

        // Insert product material traceability record
        await db.insert(productMaterialTraceability).values({
          id: traceabilityId,
          company_id: this.context.companyId,
          work_order_id: workOrderId,
          stock_lot_id: stockLotId,
          product_id: workOrder.product_id,
          material_consumption_id: consumption.id,
          raw_material_lot_id: consumption.raw_material_lot_id,
          raw_material_id: consumption.rawMaterialLot.raw_material_id,
          consumed_quantity: consumption.qty_consumed,
          finished_quantity: stockLot.qty,
          consumption_ratio: consumptionRatio.toString(),
          production_date: workOrder.completed_date || new Date().toISOString().split('T')[0],
          production_batch: stockLot.batch_number,
          raw_material_quality_status: consumption.rawMaterialLot.quality_status || "unknown",
          finished_goods_quality_status: stockLot.quality_status,
          created_by: this.context.userId,
        })

        const traceabilityRecord: ProductTraceabilityRecord = {
          id: traceabilityId,
          workOrderId,
          workOrderNumber: workOrder.number,
          stockLotId,
          productId: workOrder.product_id,
          productName: workOrder.product?.name || "Unknown Product",
          productSku: workOrder.product?.sku || "Unknown SKU",
          rawMaterialId: consumption.rawMaterialLot.raw_material_id,
          rawMaterialName: consumption.rawMaterialLot.rawMaterial.name,
          rawMaterialSku: consumption.rawMaterialLot.rawMaterial.sku,
          rawMaterialLotId: consumption.raw_material_lot_id,
          rawMaterialLotNumber: consumption.rawMaterialLot.lot_number || undefined,
          supplierId: consumption.rawMaterialLot.supplier_id || undefined,
          supplierName: consumption.rawMaterialLot.supplier?.name || undefined,
          consumedQuantity: consumedQty,
          finishedQuantity,
          consumptionRatio,
          productionDate: workOrder.completed_date || new Date().toISOString().split('T')[0],
          productionBatch: stockLot.batch_number || undefined,
          rawMaterialQualityStatus: consumption.rawMaterialLot.quality_status || "unknown",
          finishedGoodsQualityStatus: stockLot.quality_status || undefined,
        }

        traceabilityRecords.push(traceabilityRecord)
      }

      console.log(`✅ Created ${traceabilityRecords.length} product traceability records`)
      return traceabilityRecords

    } catch (error) {
      console.error("❌ Failed to create product traceability:", error)
      throw error
    }
  }

  /**
   * Create shipment material traceability records when items are shipped
   */
  async createShipmentTraceability(
    shipmentId: string,
    shipmentItemId: string
  ): Promise<ShipmentTraceabilityRecord | null> {
    try {
      console.log(`🚢 Creating shipment traceability for item: ${shipmentItemId}`)

      // Get shipment and item details
      const shipmentItem = await db.query.shipmentItems.findFirst({
        where: and(
          eq(shipmentItems.id, shipmentItemId),
          eq(shipmentItems.company_id, this.context.companyId)
        ),
        with: {
          shipment: true,
          product: true,
          stockLot: true,
        }
      })

      if (!shipmentItem) {
        throw new Error(`Shipment item ${shipmentItemId} not found`)
      }

      if (!shipmentItem.stockLot) {
        console.warn(`⚠️ No stock lot linked to shipment item ${shipmentItemId}`)
        return null
      }

      // Get product traceability records for this stock lot
      const productTraceabilityRecords = await db.query.productMaterialTraceability.findMany({
        where: and(
          eq(productMaterialTraceability.stock_lot_id, shipmentItem.stock_lot_id!),
          eq(productMaterialTraceability.company_id, this.context.companyId)
        ),
        with: {
          rawMaterial: true,
          rawMaterialLot: {
            with: {
              supplier: true,
            }
          },
          workOrder: true,
        }
      })

      if (productTraceabilityRecords.length === 0) {
        console.warn(`⚠️ No product traceability found for stock lot ${shipmentItem.stock_lot_id}`)
        return null
      }

      const shippedQuantity = parseFloat(shipmentItem.quantity || "0")
      const rawMaterials = []

      // Create shipment traceability records for each raw material
      for (const productTrace of productTraceabilityRecords) {
        if (!productTrace.rawMaterial || !productTrace.rawMaterialLot) continue

        // Calculate proportional raw material quantity for shipped amount
        const consumptionRatio = parseFloat(productTrace.consumption_ratio || "0")
        const rawMaterialQuantity = shippedQuantity * consumptionRatio

        const shipmentTraceabilityId = uid()

        // Insert shipment material traceability record
        await db.insert(shipmentMaterialTraceability).values({
          id: shipmentTraceabilityId,
          company_id: this.context.companyId,
          shipment_id: shipmentId,
          shipment_item_id: shipmentItemId,
          product_id: shipmentItem.product_id,
          stock_lot_id: shipmentItem.stock_lot_id!,
          product_material_traceability_id: productTrace.id,
          raw_material_id: productTrace.raw_material_id,
          raw_material_name: productTrace.rawMaterial.name,
          raw_material_sku: productTrace.rawMaterial.sku,
          raw_material_lot_id: productTrace.raw_material_lot_id,
          raw_material_lot_number: productTrace.rawMaterialLot.lot_number,
          supplier_id: productTrace.rawMaterialLot.supplier_id,
          supplier_name: productTrace.rawMaterialLot.supplier?.name,
          shipped_quantity: shipmentItem.quantity,
          raw_material_quantity: rawMaterialQuantity.toString(),
          work_order_id: productTrace.work_order_id,
          work_order_number: productTrace.workOrder?.number || "Unknown",
          production_date: productTrace.production_date,
          production_batch: productTrace.production_batch,
          raw_material_quality_status: productTrace.raw_material_quality_status,
          finished_goods_quality_status: productTrace.finished_goods_quality_status,
          ship_date: shipmentItem.shipment?.ship_date,
          tracking_number: shipmentItem.shipment?.tracking_number,
          created_by: this.context.userId,
        })

        rawMaterials.push({
          materialId: productTrace.raw_material_id,
          materialName: productTrace.rawMaterial.name,
          materialSku: productTrace.rawMaterial.sku,
          lotId: productTrace.raw_material_lot_id,
          lotNumber: productTrace.rawMaterialLot.lot_number || undefined,
          supplierId: productTrace.rawMaterialLot.supplier_id || undefined,
          supplierName: productTrace.rawMaterialLot.supplier?.name || "Unknown Supplier",
          quantity: rawMaterialQuantity,
          qualityStatus: productTrace.raw_material_quality_status,
        })
      }

      const traceabilityRecord: ShipmentTraceabilityRecord = {
        id: shipmentItemId,
        shipmentId,
        shipmentNumber: shipmentItem.shipment?.shipment_number || "Unknown",
        shipmentItemId,
        productId: shipmentItem.product_id,
        productName: shipmentItem.product?.name || "Unknown Product",
        productSku: shipmentItem.product?.sku || "Unknown SKU",
        stockLotId: shipmentItem.stock_lot_id!,
        rawMaterials,
        shippedQuantity,
        workOrderId: productTraceabilityRecords[0].work_order_id,
        workOrderNumber: productTraceabilityRecords[0].workOrder?.number || "Unknown",
        productionDate: productTraceabilityRecords[0].production_date,
        shipDate: shipmentItem.shipment?.ship_date || undefined,
        trackingNumber: shipmentItem.shipment?.tracking_number || undefined,
      }

      console.log(`✅ Created shipment traceability with ${rawMaterials.length} raw materials`)
      return traceabilityRecord

    } catch (error) {
      console.error("❌ Failed to create shipment traceability:", error)
      throw error
    }
  }

  /**
   * Query complete traceability for a shipment
   */
  async getShipmentTraceability(shipmentId: string): Promise<ShipmentTraceabilityRecord[]> {
    try {
      const traceabilityRecords = await db.query.shipmentMaterialTraceability.findMany({
        where: and(
          eq(shipmentMaterialTraceability.shipment_id, shipmentId),
          eq(shipmentMaterialTraceability.company_id, this.context.companyId)
        ),
        with: {
          shipment: true,
          shipmentItem: true,
          product: true,
          rawMaterial: true,
          rawMaterialLot: true,
        }
      })

      // Group by shipment item
      const groupedRecords = new Map<string, ShipmentTraceabilityRecord>()

      for (const record of traceabilityRecords) {
        if (!groupedRecords.has(record.shipment_item_id)) {
          groupedRecords.set(record.shipment_item_id, {
            id: record.shipment_item_id,
            shipmentId: record.shipment_id,
            shipmentNumber: record.shipment?.shipment_number || "Unknown",
            shipmentItemId: record.shipment_item_id,
            productId: record.product_id,
            productName: record.product?.name || record.raw_material_name,
            productSku: record.product?.sku || record.raw_material_sku,
            stockLotId: record.stock_lot_id,
            rawMaterials: [],
            shippedQuantity: parseFloat(record.shipped_quantity || "0"),
            workOrderId: record.work_order_id,
            workOrderNumber: record.work_order_number,
            productionDate: record.production_date,
            shipDate: record.ship_date || undefined,
            trackingNumber: record.tracking_number || undefined,
          })
        }

        const shipmentRecord = groupedRecords.get(record.shipment_item_id)!
        shipmentRecord.rawMaterials.push({
          materialId: record.raw_material_id,
          materialName: record.raw_material_name,
          materialSku: record.raw_material_sku,
          lotId: record.raw_material_lot_id,
          lotNumber: record.raw_material_lot_number || undefined,
          supplierId: record.supplier_id || undefined,
          supplierName: record.supplier_name || "Unknown Supplier",
          quantity: parseFloat(record.raw_material_quantity || "0"),
          qualityStatus: record.raw_material_quality_status,
        })
      }

      return Array.from(groupedRecords.values())

    } catch (error) {
      console.error("❌ Failed to get shipment traceability:", error)
      throw error
    }
  }

  /**
   * Query raw material traceability - find where a raw material lot was used
   */
  async getRawMaterialTraceability(rawMaterialLotId: string): Promise<{
    productTraceability: ProductTraceabilityRecord[]
    shipmentTraceability: ShipmentTraceabilityRecord[]
  }> {
    try {
      // Get product traceability
      const productRecords = await db.query.productMaterialTraceability.findMany({
        where: and(
          eq(productMaterialTraceability.raw_material_lot_id, rawMaterialLotId),
          eq(productMaterialTraceability.company_id, this.context.companyId)
        ),
        with: {
          workOrder: true,
          product: true,
          rawMaterial: true,
          rawMaterialLot: {
            with: {
              supplier: true,
            }
          }
        }
      })

      // Get shipment traceability
      const shipmentRecords = await db.query.shipmentMaterialTraceability.findMany({
        where: and(
          eq(shipmentMaterialTraceability.raw_material_lot_id, rawMaterialLotId),
          eq(shipmentMaterialTraceability.company_id, this.context.companyId)
        ),
        with: {
          shipment: true,
          product: true,
        }
      })

      const productTraceability: ProductTraceabilityRecord[] = productRecords.map(record => ({
        id: record.id,
        workOrderId: record.work_order_id,
        workOrderNumber: record.workOrder?.number || "Unknown",
        stockLotId: record.stock_lot_id,
        productId: record.product_id,
        productName: record.product?.name || "Unknown Product",
        productSku: record.product?.sku || "Unknown SKU",
        rawMaterialId: record.raw_material_id,
        rawMaterialName: record.rawMaterial?.name || "Unknown Material",
        rawMaterialSku: record.rawMaterial?.sku || "Unknown SKU",
        rawMaterialLotId: record.raw_material_lot_id,
        rawMaterialLotNumber: record.rawMaterialLot?.lot_number || undefined,
        supplierId: record.rawMaterialLot?.supplier_id || undefined,
        supplierName: record.rawMaterialLot?.supplier?.name || undefined,
        consumedQuantity: parseFloat(record.consumed_quantity || "0"),
        finishedQuantity: parseFloat(record.finished_quantity || "0"),
        consumptionRatio: parseFloat(record.consumption_ratio || "0"),
        productionDate: record.production_date,
        productionBatch: record.production_batch || undefined,
        rawMaterialQualityStatus: record.raw_material_quality_status,
        finishedGoodsQualityStatus: record.finished_goods_quality_status || undefined,
      }))

      // Group shipment records by shipment item
      const groupedShipmentRecords = new Map<string, ShipmentTraceabilityRecord>()

      for (const record of shipmentRecords) {
        if (!groupedShipmentRecords.has(record.shipment_item_id)) {
          groupedShipmentRecords.set(record.shipment_item_id, {
            id: record.shipment_item_id,
            shipmentId: record.shipment_id,
            shipmentNumber: record.shipment?.shipment_number || "Unknown",
            shipmentItemId: record.shipment_item_id,
            productId: record.product_id,
            productName: record.product?.name || "Unknown Product",
            productSku: record.product?.sku || "Unknown SKU",
            stockLotId: record.stock_lot_id,
            rawMaterials: [],
            shippedQuantity: parseFloat(record.shipped_quantity || "0"),
            workOrderId: record.work_order_id,
            workOrderNumber: record.work_order_number,
            productionDate: record.production_date,
            shipDate: record.ship_date || undefined,
            trackingNumber: record.tracking_number || undefined,
          })
        }

        const shipmentRecord = groupedShipmentRecords.get(record.shipment_item_id)!
        shipmentRecord.rawMaterials.push({
          materialId: record.raw_material_id,
          materialName: record.raw_material_name,
          materialSku: record.raw_material_sku,
          lotId: record.raw_material_lot_id,
          lotNumber: record.raw_material_lot_number || undefined,
          supplierId: record.supplier_id || undefined,
          supplierName: record.supplier_name || "Unknown Supplier",
          quantity: parseFloat(record.raw_material_quantity || "0"),
          qualityStatus: record.raw_material_quality_status,
        })
      }

      return {
        productTraceability,
        shipmentTraceability: Array.from(groupedShipmentRecords.values()),
      }

    } catch (error) {
      console.error("❌ Failed to get raw material traceability:", error)
      throw error
    }
  }
}
