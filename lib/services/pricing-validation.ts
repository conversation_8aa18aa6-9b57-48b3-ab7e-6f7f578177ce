/**
 * Manufacturing ERP - Product Pricing Validation Service
 * 
 * Comprehensive business logic validation for product pricing including
 * margin calculations, currency validation, and pricing consistency checks.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Product Pricing Enhancement
 */

export interface PricingData {
  base_price?: string
  cost_price?: string
  margin_percentage?: string
  currency?: string
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  suggestions: string[]
}

export class PricingValidationService {
  
  /**
   * ✅ COMPREHENSIVE PRICING VALIDATION
   * 
   * Validates pricing data with business rules, consistency checks,
   * and provides suggestions for optimization.
   */
  static validatePricing(data: PricingData): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    }

    // ✅ BASIC VALIDATION
    this.validateBasicPricing(data, result)
    
    // ✅ BUSINESS RULES VALIDATION
    this.validateBusinessRules(data, result)
    
    // ✅ MARGIN CALCULATION VALIDATION
    this.validateMarginCalculation(data, result)
    
    // ✅ CURRENCY VALIDATION
    this.validateCurrency(data, result)
    
    // ✅ PRICING OPTIMIZATION SUGGESTIONS
    this.generateOptimizationSuggestions(data, result)

    result.isValid = result.errors.length === 0
    return result
  }

  /**
   * ✅ BASIC PRICING VALIDATION
   */
  private static validateBasicPricing(data: PricingData, result: ValidationResult) {
    // Validate base price
    if (data.base_price) {
      const basePrice = parseFloat(data.base_price)
      if (isNaN(basePrice) || basePrice < 0) {
        result.errors.push("Base price must be a valid positive number")
      } else if (basePrice > 999999.99) {
        result.errors.push("Base price cannot exceed $999,999.99")
      } else if (basePrice < 0.01) {
        result.warnings.push("Base price is very low (less than $0.01)")
      }
    }

    // Validate cost price
    if (data.cost_price) {
      const costPrice = parseFloat(data.cost_price)
      if (isNaN(costPrice) || costPrice < 0) {
        result.errors.push("Cost price must be a valid positive number")
      } else if (costPrice > 999999.99) {
        result.errors.push("Cost price cannot exceed $999,999.99")
      }
    }

    // Validate margin percentage
    if (data.margin_percentage) {
      const margin = parseFloat(data.margin_percentage)
      if (isNaN(margin)) {
        result.errors.push("Margin percentage must be a valid number")
      } else if (margin < -100) {
        result.errors.push("Margin percentage cannot be less than -100%")
      } else if (margin > 1000) {
        result.errors.push("Margin percentage cannot exceed 1000%")
      }
    }
  }

  /**
   * ✅ BUSINESS RULES VALIDATION
   */
  private static validateBusinessRules(data: PricingData, result: ValidationResult) {
    const basePrice = data.base_price ? parseFloat(data.base_price) : null
    const costPrice = data.cost_price ? parseFloat(data.cost_price) : null

    // Rule: Cost price should not exceed base price
    if (basePrice && costPrice && costPrice > basePrice) {
      result.errors.push("Cost price ($" + costPrice.toFixed(2) + ") cannot exceed base price ($" + basePrice.toFixed(2) + ")")
    }

    // Rule: Warn about very high margins
    if (basePrice && costPrice) {
      const margin = ((basePrice - costPrice) / costPrice) * 100
      if (margin > 200) {
        result.warnings.push(`Very high margin (${margin.toFixed(1)}%) - consider market competitiveness`)
      } else if (margin < 10) {
        result.warnings.push(`Low margin (${margin.toFixed(1)}%) - ensure profitability`)
      }
    }

    // Rule: Warn about negative margins
    if (basePrice && costPrice && basePrice < costPrice) {
      result.warnings.push("Negative margin detected - selling below cost price")
    }
  }

  /**
   * ✅ MARGIN CALCULATION VALIDATION
   */
  private static validateMarginCalculation(data: PricingData, result: ValidationResult) {
    const basePrice = data.base_price ? parseFloat(data.base_price) : null
    const costPrice = data.cost_price ? parseFloat(data.cost_price) : null
    const marginPercentage = data.margin_percentage ? parseFloat(data.margin_percentage) : null

    if (basePrice && costPrice && marginPercentage) {
      // Calculate expected margin: ((base_price - cost_price) / cost_price) * 100
      const calculatedMargin = ((basePrice - costPrice) / costPrice) * 100
      const marginDifference = Math.abs(calculatedMargin - marginPercentage)

      if (marginDifference > 1) {
        result.errors.push(
          `Margin percentage (${marginPercentage.toFixed(1)}%) doesn't match calculated margin (${calculatedMargin.toFixed(1)}%)`
        )
        result.suggestions.push(
          `Suggested margin percentage: ${calculatedMargin.toFixed(1)}%`
        )
      }
    }
  }

  /**
   * ✅ CURRENCY VALIDATION
   */
  private static validateCurrency(data: PricingData, result: ValidationResult) {
    const supportedCurrencies = ["USD", "EUR", "CNY", "GBP", "JPY", "CAD", "AUD"]
    
    if (data.currency && !supportedCurrencies.includes(data.currency)) {
      result.errors.push(`Unsupported currency: ${data.currency}. Supported currencies: ${supportedCurrencies.join(", ")}`)
    }

    // Warn about currency-specific considerations
    if (data.currency === "JPY" && data.base_price) {
      const price = parseFloat(data.base_price)
      if (price < 1) {
        result.warnings.push("JPY prices are typically whole numbers (no decimals)")
      }
    }
  }

  /**
   * ✅ PRICING OPTIMIZATION SUGGESTIONS
   */
  private static generateOptimizationSuggestions(data: PricingData, result: ValidationResult) {
    const basePrice = data.base_price ? parseFloat(data.base_price) : null
    const costPrice = data.cost_price ? parseFloat(data.cost_price) : null

    // Suggest margin percentage if missing
    if (basePrice && costPrice && !data.margin_percentage) {
      const calculatedMargin = ((basePrice - costPrice) / costPrice) * 100
      result.suggestions.push(`Consider setting margin percentage: ${calculatedMargin.toFixed(1)}%`)
    }

    // Suggest cost price if missing
    if (basePrice && data.margin_percentage && !costPrice) {
      const margin = parseFloat(data.margin_percentage)
      const suggestedCostPrice = basePrice / (1 + margin / 100)
      result.suggestions.push(`Suggested cost price based on ${margin}% margin: $${suggestedCostPrice.toFixed(2)}`)
    }

    // Suggest base price if missing
    if (costPrice && data.margin_percentage && !basePrice) {
      const margin = parseFloat(data.margin_percentage)
      const suggestedBasePrice = costPrice * (1 + margin / 100)
      result.suggestions.push(`Suggested base price based on ${margin}% margin: $${suggestedBasePrice.toFixed(2)}`)
    }

    // Suggest psychological pricing
    if (basePrice) {
      const roundedPrice = Math.ceil(basePrice)
      const psychologicalPrice = roundedPrice - 0.01
      if (Math.abs(basePrice - psychologicalPrice) > 0.1 && psychologicalPrice > basePrice) {
        result.suggestions.push(`Consider psychological pricing: $${psychologicalPrice.toFixed(2)}`)
      }
    }
  }

  /**
   * ✅ CALCULATE MARGIN FROM PRICES
   */
  static calculateMargin(basePrice: number, costPrice: number): number {
    if (costPrice === 0) return 0
    return ((basePrice - costPrice) / costPrice) * 100
  }

  /**
   * ✅ CALCULATE BASE PRICE FROM COST AND MARGIN
   */
  static calculateBasePriceFromMargin(costPrice: number, marginPercentage: number): number {
    return costPrice * (1 + marginPercentage / 100)
  }

  /**
   * ✅ CALCULATE COST PRICE FROM BASE AND MARGIN
   */
  static calculateCostPriceFromMargin(basePrice: number, marginPercentage: number): number {
    return basePrice / (1 + marginPercentage / 100)
  }

  /**
   * ✅ FORMAT PRICE FOR DISPLAY
   */
  static formatPrice(price: number, currency: string = "USD"): string {
    const currencySymbols: { [key: string]: string } = {
      USD: "$",
      EUR: "€",
      CNY: "¥",
      GBP: "£",
      JPY: "¥",
      CAD: "C$",
      AUD: "A$"
    }

    const symbol = currencySymbols[currency] || currency
    const decimals = currency === "JPY" ? 0 : 2
    
    return `${symbol}${price.toFixed(decimals)}`
  }
}
