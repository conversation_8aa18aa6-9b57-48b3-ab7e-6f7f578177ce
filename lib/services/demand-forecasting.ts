/**
 * Manufacturing ERP - Demand Forecasting Service
 * 
 * Professional service for demand forecasting based on sales contract pipeline analysis
 * Implements export-focused forecasting with container optimization and BOM integration
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { db, uid } from "@/lib/db"
import {
  demandForecasts,
  forecastParameters,
  salesContracts,
  salesContractItems,
  products,
  billOfMaterials,
  rawMaterials,
  companies,
  procurementPlans
} from "@/lib/schema-postgres"
import { eq, and, asc, desc as descOrder, sql, gte, lte, isNull, or } from "drizzle-orm"
import { z } from "zod"

// ✅ PROFESSIONAL: Type definitions for enterprise-grade forecasting
export interface DemandForecastContext {
  companyId: string
  userId: string
  productId?: string
  forecastPeriod: string // "2025-Q1", "2025-02", "2025-W12"
  forecastMethod: "pipeline" | "historical" | "manual" | "hybrid"
}

export interface DemandForecast {
  id: string
  companyId: string
  productId: string
  productName: string
  productSku: string
  forecastPeriod: string
  forecastedDemand: number
  confidenceLevel: "low" | "medium" | "high"
  forecastMethod: "pipeline" | "historical" | "manual" | "hybrid"
  baseDataSource: any // JSON object with source data details
  seasonalityApplied: boolean
  trendFactorApplied: number
  approvalStatus: "draft" | "pending" | "approved" | "rejected"
  notes?: string
  // ✅ REDESIGNED: Simple supplier selection
  supplierPreferences?: string // JSON string containing supplier preferences
  createdBy: string
  approvedBy?: string
  createdAt: Date
  updatedAt: Date
}

export interface MaterialRequirement {
  rawMaterialId: string
  materialName: string
  materialSku: string
  requiredQty: number
  unit: string
  wasteFactorQty: number
  totalRequiredQty: number
  targetDate: string
  priority: "low" | "normal" | "high" | "urgent"
}

export interface PipelineAnalysis {
  totalPipelineValue: number
  contractCount: number
  averageContractValue: number
  conversionProbability: number
  seasonalityFactor: number
  trendFactor: number
  confidenceScore: number
}

// ✅ PROFESSIONAL: Zod validation schemas
export const createDemandForecastSchema = z.object({
  productId: z.string().min(1, "Product ID is required"),
  forecastPeriod: z.string().min(1, "Forecast period is required"),
  forecastedDemand: z.number().min(0, "Forecasted demand must be non-negative"),
  forecastMethod: z.enum(["pipeline", "historical", "manual", "hybrid"]).default("pipeline"),
  confidenceLevel: z.enum(["low", "medium", "high"]).default("medium"),
  notes: z.string().optional(),
  // ✅ REDESIGNED: Simple supplier selection (replaces complex preferences)
  preferredSupplierId: z.string().optional(),
})

export const updateDemandForecastSchema = z.object({
  forecastedDemand: z.number().min(0).optional(),
  confidenceLevel: z.enum(["low", "medium", "high"]).optional(),
  notes: z.string().optional(),
  approvalStatus: z.enum(["draft", "pending", "approved", "rejected"]).optional(),
})

/**
 * ✅ PROFESSIONAL: Demand Forecasting Service
 * Enterprise-grade demand forecasting with sales pipeline analysis
 */
export class DemandForecastingService {

  /**
   * Create manual demand forecast with user-specified demand
   */
  async createManualForecast(
    context: DemandForecastContext,
    forecastedDemand: number,
    confidenceLevel: "low" | "medium" | "high" = "medium",
    notes?: string,
    preferredSupplierId?: string
  ): Promise<DemandForecast> {
    try {
      // Create forecast record with user-specified demand
      const forecastData = {
        id: uid(),
        company_id: context.companyId,
        product_id: context.productId!,
        forecast_period: context.forecastPeriod,
        forecasted_demand: forecastedDemand.toString(),
        confidence_level: confidenceLevel,
        forecast_method: context.forecastMethod,
        base_data_source: JSON.stringify({
          sourceType: "manual",
          userInput: forecastedDemand,
          method: context.forecastMethod,
        }),
        seasonality_applied: "false",
        trend_factor_applied: "1.0",
        // ✅ REDESIGNED: Store simple supplier selection
        supplier_preferences: preferredSupplierId ? JSON.stringify({ preferredSupplierId }) : null,
        created_by: context.userId,
        approval_status: "draft",
        notes: notes || `Manual forecast created by user.`,
      }

      await db.insert(demandForecasts).values(forecastData)

      // Return formatted forecast
      return await this.getDemandForecastById(context.companyId, forecastData.id)
    } catch (error) {
      console.error("Error creating manual forecast:", error)
      throw new Error(`Failed to create manual forecast: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate demand forecast based on sales contract pipeline
   */
  async generatePipelineForecast(
    context: DemandForecastContext,
    preferredSupplierId?: string
  ): Promise<DemandForecast> {
    try {
      // 1. Analyze sales contract pipeline for the product
      const pipelineAnalysis = await this.analyzeSalesPipeline(
        context.companyId,
        context.productId!,
        context.forecastPeriod
      )

      // 2. Get or create forecast parameters for the product
      const parameters = await this.getForecastParameters(
        context.companyId,
        context.productId!
      )

      // 3. Calculate forecasted demand
      const baselineDemand = this.calculateBaselineDemand(pipelineAnalysis)
      const adjustedDemand = this.applyForecastingFactors(
        baselineDemand,
        parameters,
        pipelineAnalysis
      )

      // 4. Determine confidence level
      const confidenceLevel = this.calculateConfidenceLevel(pipelineAnalysis)

      // 5. Create forecast record
      const forecastData = {
        id: uid(),
        company_id: context.companyId,
        product_id: context.productId!,
        forecast_period: context.forecastPeriod,
        forecasted_demand: adjustedDemand.toString(),
        confidence_level: confidenceLevel,
        forecast_method: context.forecastMethod,
        base_data_source: JSON.stringify(pipelineAnalysis),
        seasonality_applied: parameters.seasonalityFactor !== 1.0 ? "true" : "false",
        trend_factor_applied: parameters.trendFactor.toString(),
        // ✅ REDESIGNED: Store simple supplier selection
        supplier_preferences: preferredSupplierId ? JSON.stringify({ preferredSupplierId }) : null,
        created_by: context.userId,
        approval_status: "draft",
        notes: `Auto-generated from pipeline analysis. ${pipelineAnalysis.contractCount} contracts analyzed.`,
      }

      await db.insert(demandForecasts).values(forecastData)

      // 6. Return formatted forecast
      return await this.getDemandForecastById(context.companyId, forecastData.id)
    } catch (error) {
      console.error("Error generating pipeline forecast:", error)
      throw new Error(`Failed to generate demand forecast: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Calculate material requirements from demand forecast
   */
  async explodeForecastToBOM(
    companyId: string,
    forecastId: string
  ): Promise<MaterialRequirement[]> {
    try {
      // 1. Get forecast details
      const forecast = await this.getDemandForecastById(companyId, forecastId)
      if (!forecast) {
        throw new Error("Forecast not found")
      }

      // 2. Get BOM for the product
      const bomItems = await db.query.billOfMaterials.findMany({
        where: and(
          eq(billOfMaterials.company_id, companyId),
          eq(billOfMaterials.product_id, forecast.productId),
          eq(billOfMaterials.status, "active")
        ),
        with: {
          rawMaterial: true,
        },
      })

      if (bomItems.length === 0) {
        throw new Error(`No BOM found for product ${forecast.productSku}`)
      }

      // 3. Calculate material requirements
      const requirements: MaterialRequirement[] = []
      const forecastedQty = forecast.forecastedDemand

      for (const bomItem of bomItems) {
        const qtyRequired = parseFloat(bomItem.qty_required) * forecastedQty
        const wasteFactor = parseFloat(bomItem.waste_factor || "0.05") // 5% default
        const wasteQty = qtyRequired * wasteFactor
        const totalQty = qtyRequired + wasteQty

        requirements.push({
          rawMaterialId: bomItem.raw_material_id,
          materialName: bomItem.rawMaterial.name,
          materialSku: bomItem.rawMaterial.sku,
          requiredQty: qtyRequired,
          unit: bomItem.unit,
          wasteFactorQty: wasteQty,
          totalRequiredQty: totalQty,
          targetDate: this.calculateTargetDate(forecast.forecastPeriod),
          priority: this.determinePriority(forecast.confidenceLevel),
        })
      }

      return requirements
    } catch (error) {
      console.error("Error exploding forecast to BOM:", error)
      throw new Error(`Failed to calculate material requirements: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get demand forecast by ID with product details
   */
  async getDemandForecastById(
    companyId: string,
    forecastId: string
  ): Promise<DemandForecast> {
    const forecast = await db.query.demandForecasts.findFirst({
      where: and(
        eq(demandForecasts.company_id, companyId),
        eq(demandForecasts.id, forecastId)
      ),
      with: {
        product: true,
      },
    })

    if (!forecast) {
      throw new Error("Demand forecast not found")
    }

    return {
      id: forecast.id,
      companyId: forecast.company_id,
      productId: forecast.product_id,
      productName: forecast.product.name,
      productSku: forecast.product.sku,
      forecastPeriod: forecast.forecast_period,
      forecastedDemand: parseFloat(forecast.forecasted_demand),
      confidenceLevel: forecast.confidence_level as "low" | "medium" | "high",
      forecastMethod: forecast.forecast_method as "pipeline" | "historical" | "manual" | "hybrid",
      baseDataSource: forecast.base_data_source ? JSON.parse(forecast.base_data_source) : null,
      seasonalityApplied: forecast.seasonality_applied === "true",
      trendFactorApplied: parseFloat(forecast.trend_factor_applied || "1.0"),
      approvalStatus: forecast.approval_status as "draft" | "pending" | "approved" | "rejected",
      notes: forecast.notes || undefined,
      // ✅ SUPPLIER INTEGRATION: Include supplier preferences
      supplierPreferences: forecast.supplier_preferences || undefined,
      createdBy: forecast.created_by,
      approvedBy: forecast.approved_by || undefined,
      createdAt: forecast.created_at!,
      updatedAt: forecast.updated_at!,
    }
  }

  /**
   * List demand forecasts with filtering
   */
  async listDemandForecasts(
    companyId: string,
    filters?: {
      productId?: string
      forecastPeriod?: string
      approvalStatus?: string
      forecastMethod?: string
    }
  ): Promise<DemandForecast[]> {
    const conditions = [eq(demandForecasts.company_id, companyId)]

    if (filters?.productId) {
      conditions.push(eq(demandForecasts.product_id, filters.productId))
    }
    if (filters?.forecastPeriod) {
      conditions.push(eq(demandForecasts.forecast_period, filters.forecastPeriod))
    }
    if (filters?.approvalStatus) {
      conditions.push(eq(demandForecasts.approval_status, filters.approvalStatus))
    }
    if (filters?.forecastMethod) {
      conditions.push(eq(demandForecasts.forecast_method, filters.forecastMethod))
    }

    const forecasts = await db.query.demandForecasts.findMany({
      where: and(...conditions),
      with: {
        product: true,
      },
      orderBy: [descOrder(demandForecasts.created_at)],
    })

    return forecasts.map(forecast => ({
      id: forecast.id,
      companyId: forecast.company_id,
      productId: forecast.product_id,
      productName: forecast.product.name,
      productSku: forecast.product.sku,
      forecastPeriod: forecast.forecast_period,
      forecastedDemand: parseFloat(forecast.forecasted_demand),
      confidenceLevel: forecast.confidence_level as "low" | "medium" | "high",
      forecastMethod: forecast.forecast_method as "pipeline" | "historical" | "manual" | "hybrid",
      baseDataSource: forecast.base_data_source ? JSON.parse(forecast.base_data_source) : null,
      seasonalityApplied: forecast.seasonality_applied === "true",
      trendFactorApplied: parseFloat(forecast.trend_factor_applied || "1.0"),
      approvalStatus: forecast.approval_status as "draft" | "pending" | "approved" | "rejected",
      notes: forecast.notes || undefined,
      // ✅ SUPPLIER INTEGRATION: Include supplier preferences
      supplierPreferences: forecast.supplier_preferences || undefined,
      createdBy: forecast.created_by,
      approvedBy: forecast.approved_by || undefined,
      createdAt: forecast.created_at!,
      updatedAt: forecast.updated_at!,
    }))
  }

  /**
   * Update demand forecast
   */
  async updateDemandForecast(
    companyId: string,
    forecastId: string,
    updates: {
      forecastedDemand?: number
      confidenceLevel?: "low" | "medium" | "high"
      notes?: string
      approvalStatus?: "draft" | "pending" | "approved" | "rejected"
    }
  ): Promise<DemandForecast> {
    try {
      // Verify forecast exists and belongs to company
      const existingForecast = await this.getDemandForecastById(companyId, forecastId)

      // Prepare update data
      const updateData: any = {
        updated_at: new Date(),
      }

      if (updates.forecastedDemand !== undefined) {
        updateData.forecasted_demand = updates.forecastedDemand.toString()
      }
      if (updates.confidenceLevel) {
        updateData.confidence_level = updates.confidenceLevel
      }
      if (updates.notes !== undefined) {
        updateData.notes = updates.notes
      }
      if (updates.approvalStatus) {
        updateData.approval_status = updates.approvalStatus
        if (updates.approvalStatus === "approved") {
          updateData.approved_at = new Date()
          // TODO: Set approved_by when we have user context
        }
      }

      // Update the forecast
      await db.update(demandForecasts)
        .set(updateData)
        .where(
          and(
            eq(demandForecasts.company_id, companyId),
            eq(demandForecasts.id, forecastId)
          )
        )

      // Return updated forecast
      return await this.getDemandForecastById(companyId, forecastId)
    } catch (error) {
      console.error("Error updating demand forecast:", error)
      throw new Error(`Failed to update demand forecast: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Delete demand forecast with cascade deletion of related data
   */
  async deleteDemandForecast(
    companyId: string,
    forecastId: string
  ): Promise<void> {
    try {
      // Verify forecast exists and belongs to company
      const forecast = await db.query.demandForecasts.findFirst({
        where: and(
          eq(demandForecasts.company_id, companyId),
          eq(demandForecasts.id, forecastId)
        ),
      })

      if (!forecast) {
        throw new Error("Demand forecast not found")
      }

      // ✅ CRITICAL FIX: Delete related procurement plans first (cascade deletion)
      await db.delete(procurementPlans).where(
        eq(procurementPlans.demand_forecast_id, forecastId)
      )

      // Delete the forecast
      await db.delete(demandForecasts).where(
        and(
          eq(demandForecasts.company_id, companyId),
          eq(demandForecasts.id, forecastId)
        )
      )

      console.log(`✅ Successfully deleted forecast ${forecastId} and related procurement plans`)
    } catch (error) {
      console.error("Error deleting demand forecast:", error)
      throw new Error(`Failed to delete demand forecast: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Analyze sales contract pipeline for demand signals
   */
  private async analyzeSalesPipeline(
    companyId: string,
    productId: string,
    forecastPeriod: string
  ): Promise<PipelineAnalysis> {
    try {
      // Get date range for the forecast period
      const { startDate, endDate } = this.parseForecastPeriod(forecastPeriod)

      // Query sales contracts in pipeline (approved and pending)
      const pipelineContracts = await db.query.salesContracts.findMany({
        where: and(
          eq(salesContracts.company_id, companyId),
          or(
            eq(salesContracts.status, "approved"),
            eq(salesContracts.status, "pending")
          ),
          gte(salesContracts.date, startDate),
          lte(salesContracts.date, endDate)
        ),
        with: {
          items: {
            where: eq(salesContractItems.product_id, productId),
          },
        },
      })

      // Calculate pipeline metrics
      let totalQuantity = 0
      let totalValue = 0
      let contractCount = 0

      for (const contract of pipelineContracts) {
        if (contract.items.length > 0) {
          contractCount++
          for (const item of contract.items) {
            const qty = parseFloat(item.qty)
            const price = parseFloat(item.price)
            totalQuantity += qty
            totalValue += qty * price
          }
        }
      }

      // Calculate conversion probability based on contract status
      const approvedContracts = pipelineContracts.filter(c => c.status === "approved").length
      const conversionProbability = contractCount > 0 ? approvedContracts / contractCount : 0.5

      // Calculate confidence score based on data quality
      const confidenceScore = this.calculatePipelineConfidence(
        contractCount,
        totalValue,
        conversionProbability
      )

      return {
        totalPipelineValue: totalValue,
        contractCount,
        averageContractValue: contractCount > 0 ? totalValue / contractCount : 0,
        conversionProbability,
        seasonalityFactor: 1.0, // TODO: Implement seasonal analysis
        trendFactor: 1.0, // TODO: Implement trend analysis
        confidenceScore,
      }
    } catch (error) {
      console.error("Error analyzing sales pipeline:", error)
      throw new Error("Failed to analyze sales pipeline")
    }
  }

  /**
   * Get or create forecast parameters for a product
   */
  private async getForecastParameters(
    companyId: string,
    productId: string
  ): Promise<{
    seasonalityFactor: number
    trendFactor: number
    leadTimeBufferDays: number
    safetyStockPercentage: number
    containerOptimizationEnabled: boolean
  }> {
    let parameters = await db.query.forecastParameters.findFirst({
      where: and(
        eq(forecastParameters.company_id, companyId),
        eq(forecastParameters.product_id, productId)
      ),
    })

    // Create default parameters if none exist
    if (!parameters) {
      const defaultParams = {
        id: uid(),
        company_id: companyId,
        product_id: productId,
        seasonality_factor: "1.0",
        trend_factor: "1.0",
        lead_time_buffer_days: "14",
        safety_stock_percentage: "0.15",
        container_optimization_enabled: "true",
        preferred_container_size: "40ft",
        minimum_order_efficiency: "0.8",
        historical_periods_to_analyze: "12",
        outlier_detection_enabled: "true",
      }

      await db.insert(forecastParameters).values(defaultParams)
      parameters = defaultParams
    }

    return {
      seasonalityFactor: parseFloat(parameters.seasonality_factor || "1.0"),
      trendFactor: parseFloat(parameters.trend_factor || "1.0"),
      leadTimeBufferDays: parseInt(parameters.lead_time_buffer_days || "14"),
      safetyStockPercentage: parseFloat(parameters.safety_stock_percentage || "0.15"),
      containerOptimizationEnabled: parameters.container_optimization_enabled === "true",
    }
  }

  /**
   * Calculate baseline demand from pipeline analysis
   */
  private calculateBaselineDemand(analysis: PipelineAnalysis): number {
    // Apply conversion probability to pipeline quantity
    // This is a simplified calculation - in practice, you'd have more sophisticated models
    const baselineDemand = analysis.contractCount * analysis.conversionProbability
    return Math.max(0, baselineDemand)
  }

  /**
   * Apply forecasting factors (seasonality, trend, etc.)
   */
  private applyForecastingFactors(
    baselineDemand: number,
    parameters: any,
    analysis: PipelineAnalysis
  ): number {
    let adjustedDemand = baselineDemand

    // Apply seasonality factor
    adjustedDemand *= parameters.seasonalityFactor

    // Apply trend factor
    adjustedDemand *= parameters.trendFactor

    // Apply safety stock buffer
    adjustedDemand *= (1 + parameters.safetyStockPercentage)

    return Math.round(adjustedDemand)
  }

  /**
   * Calculate confidence level based on pipeline analysis
   */
  private calculateConfidenceLevel(analysis: PipelineAnalysis): "low" | "medium" | "high" {
    if (analysis.confidenceScore >= 0.8 && analysis.contractCount >= 5) {
      return "high"
    } else if (analysis.confidenceScore >= 0.6 && analysis.contractCount >= 2) {
      return "medium"
    } else {
      return "low"
    }
  }

  /**
   * Parse forecast period into date range
   */
  private parseForecastPeriod(period: string): { startDate: string; endDate: string } {
    const now = new Date()
    const currentYear = now.getFullYear()

    if (period.includes("Q")) {
      // Quarterly format: "2025-Q1"
      const [year, quarter] = period.split("-Q")
      const q = parseInt(quarter)
      const startMonth = (q - 1) * 3 + 1
      const endMonth = q * 3

      return {
        startDate: `${year}-${startMonth.toString().padStart(2, '0')}-01`,
        endDate: `${year}-${endMonth.toString().padStart(2, '0')}-${this.getLastDayOfMonth(parseInt(year), endMonth)}`,
      }
    } else if (period.includes("W")) {
      // Weekly format: "2025-W12"
      const [year, week] = period.split("-W")
      const weekNum = parseInt(week)
      const startDate = this.getDateFromWeek(parseInt(year), weekNum)
      const endDate = new Date(startDate)
      endDate.setDate(endDate.getDate() + 6)

      return {
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
      }
    } else {
      // Monthly format: "2025-02"
      const [year, month] = period.split("-")
      const lastDay = this.getLastDayOfMonth(parseInt(year), parseInt(month))

      return {
        startDate: `${year}-${month}-01`,
        endDate: `${year}-${month}-${lastDay}`,
      }
    }
  }

  /**
   * Calculate pipeline confidence score
   */
  private calculatePipelineConfidence(
    contractCount: number,
    totalValue: number,
    conversionProbability: number
  ): number {
    let confidence = 0

    // Base confidence from contract count
    if (contractCount >= 10) confidence += 0.4
    else if (contractCount >= 5) confidence += 0.3
    else if (contractCount >= 2) confidence += 0.2
    else confidence += 0.1

    // Confidence from total value (indicates serious prospects)
    if (totalValue >= 100000) confidence += 0.3
    else if (totalValue >= 50000) confidence += 0.2
    else if (totalValue >= 10000) confidence += 0.1

    // Confidence from conversion probability
    confidence += conversionProbability * 0.3

    return Math.min(1.0, confidence)
  }

  /**
   * Calculate target date for material requirements
   */
  private calculateTargetDate(forecastPeriod: string): string {
    const { startDate } = this.parseForecastPeriod(forecastPeriod)
    const targetDate = new Date(startDate)
    targetDate.setDate(targetDate.getDate() - 30) // 30 days before period start
    return targetDate.toISOString().split('T')[0]
  }

  /**
   * Determine priority based on confidence level and lead time urgency
   */
  private determinePriority(confidenceLevel: "low" | "medium" | "high"): "low" | "normal" | "high" | "urgent" {
    // Calculate days until target date
    const targetDate = new Date()
    targetDate.setDate(targetDate.getDate() + 30) // 30 days ahead
    const daysUntilNeeded = Math.ceil((targetDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))

    // Standard lead time assumption (30 days for default supplier)
    const standardLeadTime = 30

    // Determine urgency based on lead time vs. available time
    let urgencyLevel: "low" | "normal" | "high" | "urgent"
    if (daysUntilNeeded <= standardLeadTime * 0.8) {
      urgencyLevel = "urgent"
    } else if (daysUntilNeeded <= standardLeadTime) {
      urgencyLevel = "high"
    } else if (daysUntilNeeded <= standardLeadTime * 1.5) {
      urgencyLevel = "normal"
    } else {
      urgencyLevel = "low"
    }

    // Adjust based on confidence level
    switch (confidenceLevel) {
      case "high":
        // High confidence - increase urgency by one level
        if (urgencyLevel === "low") return "normal"
        if (urgencyLevel === "normal") return "high"
        if (urgencyLevel === "high") return "urgent"
        return "urgent"
      case "medium":
        return urgencyLevel
      case "low":
        // Low confidence - decrease urgency by one level
        if (urgencyLevel === "urgent") return "high"
        if (urgencyLevel === "high") return "normal"
        if (urgencyLevel === "normal") return "low"
        return "low"
      default:
        return urgencyLevel
    }
  }

  /**
   * Get last day of month
   */
  private getLastDayOfMonth(year: number, month: number): string {
    const lastDay = new Date(year, month, 0).getDate()
    return lastDay.toString().padStart(2, '0')
  }

  /**
   * Get date from ISO week number
   */
  private getDateFromWeek(year: number, week: number): Date {
    const jan1 = new Date(year, 0, 1)
    const daysToAdd = (week - 1) * 7 - jan1.getDay() + 1
    return new Date(year, 0, 1 + daysToAdd)
  }
}
