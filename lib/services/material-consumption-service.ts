/**
 * Manufacturing ERP - Material Consumption Service
 * 
 * Professional service for automatic material consumption during work order completion
 * Implements FIFO logic, cost allocation, and enterprise-grade validation
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0
 */

import { db, uid } from "@/lib/db"
import {
  materialConsumption,
  rawMaterialLots,
  rawMaterials,
  billOfMaterials,
  workOrders,
  stockLots
} from "@/lib/schema-postgres"
import { eq, and, asc, desc, sql, lt, gt } from "drizzle-orm"
import { z } from "zod"
import { inventoryEventEmitter, InventoryEventType } from "@/lib/events/inventory-events"
import "@/lib/events/inventory-event-init" // Ensure event system is initialized

// ✅ PROFESSIONAL: Type definitions for enterprise-grade service
export interface MaterialConsumptionContext {
  companyId: string
  userId: string
  workOrderId: string
  completedQty: number
  consumptionDate?: string
}

export interface MaterialRequirement {
  rawMaterialId: string
  materialName: string
  materialSku: string
  requiredQty: number
  unit: string
  wasteFactorQty: number
  totalRequiredQty: number
}

export interface MaterialAllocation {
  lotId: string
  lotNumber: string
  availableQty: number
  allocatedQty: number
  unitCost: number
  totalCost: number
  supplier: string
}

export interface ConsumptionResult {
  success: boolean
  workOrderId: string
  totalMaterialCost: number
  consumedMaterials: Array<{
    materialId: string
    materialName: string
    totalQtyConsumed: number
    totalCost: number
    allocations: MaterialAllocation[]
  }>
  shortages: Array<{
    materialId: string
    materialName: string
    requiredQty: number
    availableQty: number
    shortageQty: number
  }>
  warnings: string[]
  errors: string[]
}

// ✅ PROFESSIONAL: Validation schemas
const consumptionContextSchema = z.object({
  companyId: z.string().min(1, "Company ID is required"),
  userId: z.string().min(1, "User ID is required"),
  workOrderId: z.string().min(1, "Work Order ID is required"),
  completedQty: z.number().positive("Completed quantity must be positive"),
  consumptionDate: z.string().optional(),
})

export class MaterialConsumptionService {
  private context: MaterialConsumptionContext

  constructor(context: MaterialConsumptionContext) {
    // ✅ PROFESSIONAL: Validate input context
    const validated = consumptionContextSchema.parse(context)
    this.context = {
      ...validated,
      consumptionDate: context.consumptionDate || new Date().toISOString().split('T')[0]
    }
  }

  /**
   * Main method: Process material consumption for work order completion
   * Implements enterprise-grade workflow with validation and error handling
   */
  async processWorkOrderMaterialConsumption(): Promise<ConsumptionResult> {
    try {
      console.log(`🏭 Processing material consumption for Work Order: ${this.context.workOrderId}`)

      // Step 1: Validate work order and get product information
      const workOrder = await this.validateWorkOrder()

      // Step 2: Calculate material requirements based on BOM
      const materialRequirements = await this.calculateMaterialRequirements(
        workOrder.product_id,
        this.context.completedQty
      )

      if (materialRequirements.length === 0) {
        return {
          success: true,
          workOrderId: this.context.workOrderId,
          totalMaterialCost: 0,
          consumedMaterials: [],
          shortages: [],
          warnings: ["No BOM defined for this product - no materials consumed"],
          errors: []
        }
      }

      // Step 3: Check material availability and allocate lots (FIFO)
      const allocationResult = await this.allocateMaterialLots(materialRequirements)

      // Step 4: Validate no critical shortages
      if (allocationResult.shortages.length > 0) {
        const criticalShortages = allocationResult.shortages.filter(s => s.shortageQty > 0)
        if (criticalShortages.length > 0) {
          return {
            success: false,
            workOrderId: this.context.workOrderId,
            totalMaterialCost: 0,
            consumedMaterials: [],
            shortages: criticalShortages,
            warnings: [],
            errors: ["Critical material shortages prevent work order completion"]
          }
        }
      }

      // Step 5: Execute material consumption in database transaction
      const consumptionResult = await this.executeMaterialConsumption(allocationResult.allocations)

      // Step 6: Update work order with material costs
      await this.updateWorkOrderMaterialCosts(consumptionResult.totalMaterialCost)

      console.log(`✅ Material consumption completed. Total cost: $${consumptionResult.totalMaterialCost}`)

      return {
        success: true,
        workOrderId: this.context.workOrderId,
        totalMaterialCost: consumptionResult.totalMaterialCost,
        consumedMaterials: consumptionResult.consumedMaterials,
        shortages: allocationResult.shortages,
        warnings: allocationResult.warnings,
        errors: []
      }

    } catch (error) {
      console.error("❌ Material consumption failed:", error)
      return {
        success: false,
        workOrderId: this.context.workOrderId,
        totalMaterialCost: 0,
        consumedMaterials: [],
        shortages: [],
        warnings: [],
        errors: [error instanceof Error ? error.message : "Unknown error occurred"]
      }
    }
  }

  /**
   * Validate work order exists and belongs to company
   */
  private async validateWorkOrder() {
    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, this.context.workOrderId),
        eq(workOrders.company_id, this.context.companyId)
      ),
      with: {
        product: true,
        salesContract: {
          with: {
            customer: true
          }
        }
      }
    })

    if (!workOrder) {
      throw new Error(`Work order ${this.context.workOrderId} not found or access denied`)
    }

    if (workOrder.status === "completed") {
      throw new Error("Work order is already completed")
    }

    return workOrder
  }

  /**
   * Calculate material requirements based on BOM and completed quantity
   * Includes waste factor calculations
   */
  private async calculateMaterialRequirements(
    productId: string,
    completedQty: number
  ): Promise<MaterialRequirement[]> {

    // Get BOM for the product
    const bomItems = await db.query.billOfMaterials.findMany({
      where: and(
        eq(billOfMaterials.product_id, productId),
        eq(billOfMaterials.company_id, this.context.companyId),
        eq(billOfMaterials.status, "active")
      ),
      with: {
        rawMaterial: true
      }
    })

    return bomItems.map(bomItem => {
      const qtyRequired = parseFloat(bomItem.qty_required) * completedQty
      const wasteFactor = parseFloat(bomItem.waste_factor || "0.05") // Default 5%
      const wasteFactorQty = qtyRequired * wasteFactor
      const totalRequiredQty = qtyRequired + wasteFactorQty

      return {
        rawMaterialId: bomItem.raw_material_id,
        materialName: bomItem.rawMaterial?.name || "Unknown Material",
        materialSku: bomItem.rawMaterial?.sku || "Unknown SKU",
        requiredQty: qtyRequired,
        unit: bomItem.unit,
        wasteFactorQty,
        totalRequiredQty
      }
    })
  }

  /**
   * Allocate material lots using FIFO method
   * Returns allocation plan with shortage information
   */
  private async allocateMaterialLots(requirements: MaterialRequirement[]) {
    const allocations: Array<{
      materialId: string
      materialName: string
      totalQtyConsumed: number
      totalCost: number
      allocations: MaterialAllocation[]
    }> = []

    const shortages: Array<{
      materialId: string
      materialName: string
      requiredQty: number
      availableQty: number
      shortageQty: number
    }> = []

    const warnings: string[] = []

    for (const requirement of requirements) {
      // ✅ ENHANCED: Get available lots with intelligent FIFO selection
      const availableLots = await this.getOptimalLotsForConsumption(
        requirement.rawMaterialId,
        requirement.totalRequiredQty
      )

      let remainingQty = requirement.totalRequiredQty
      const materialAllocations: MaterialAllocation[] = []
      let totalCost = 0

      // Allocate from available lots using FIFO
      for (const lot of availableLots) {
        if (remainingQty <= 0) break

        const availableQty = parseFloat(lot.qty)
        const allocatedQty = Math.min(remainingQty, availableQty)
        const unitCost = parseFloat(lot.unit_cost || "0")
        const allocationCost = allocatedQty * unitCost

        materialAllocations.push({
          lotId: lot.id,
          lotNumber: lot.lot_number || `LOT-${lot.id.slice(-8)}`,
          availableQty,
          allocatedQty,
          unitCost,
          totalCost: allocationCost,
          supplier: lot.supplier?.name || "Unknown Supplier"
        })

        totalCost += allocationCost
        remainingQty -= allocatedQty
      }

      // Check for shortages
      if (remainingQty > 0) {
        const availableQty = requirement.totalRequiredQty - remainingQty
        shortages.push({
          materialId: requirement.rawMaterialId,
          materialName: requirement.materialName,
          requiredQty: requirement.totalRequiredQty,
          availableQty,
          shortageQty: remainingQty
        })
      }

      allocations.push({
        materialId: requirement.rawMaterialId,
        materialName: requirement.materialName,
        totalQtyConsumed: requirement.totalRequiredQty - remainingQty,
        totalCost,
        allocations: materialAllocations
      })
    }

    return { allocations, shortages, warnings }
  }

  /**
   * Execute material consumption in database transaction
   * Updates lot quantities and creates consumption records
   */
  private async executeMaterialConsumption(allocations: Array<{
    materialId: string
    materialName: string
    totalQtyConsumed: number
    totalCost: number
    allocations: MaterialAllocation[]
  }>) {
    let totalMaterialCost = 0
    const consumedMaterials = []

    // Execute in database transaction for data consistency
    await db.transaction(async (tx) => {
      for (const materialAllocation of allocations) {
        for (const allocation of materialAllocation.allocations) {
          // Create consumption record
          const consumptionId = uid("mc")
          await tx.insert(materialConsumption).values({
            id: consumptionId,
            company_id: this.context.companyId,
            work_order_id: this.context.workOrderId,
            raw_material_lot_id: allocation.lotId,
            qty_consumed: allocation.allocatedQty.toString(),
            unit_cost: allocation.unitCost.toString(),
            total_cost: allocation.totalCost.toString(),
            consumed_date: this.context.consumptionDate!,
            consumed_by: this.context.userId,
            notes: `Auto-consumed during work order completion - Qty: ${this.context.completedQty}`
          })

          // Update lot quantity
          const newQty = allocation.availableQty - allocation.allocatedQty
          await tx.update(rawMaterialLots)
            .set({
              qty: newQty.toString(),
              status: newQty <= 0 ? "consumed" : "available",
              updated_at: new Date()
            })
            .where(eq(rawMaterialLots.id, allocation.lotId))

          // ✅ PROFESSIONAL: Emit material consumption event
          await this.emitMaterialConsumptionEvent(
            consumptionId,
            allocation,
            materialAllocation
          )
        }

        totalMaterialCost += materialAllocation.totalCost
        consumedMaterials.push(materialAllocation)
      }
    })

    return { totalMaterialCost, consumedMaterials }
  }

  /**
   * Emit material consumption event for real-time updates
   */
  private async emitMaterialConsumptionEvent(
    consumptionId: string,
    allocation: MaterialAllocation,
    materialAllocation: any
  ) {
    try {
      // Get work order details for event
      const workOrder = await db.query.workOrders.findFirst({
        where: eq(workOrders.id, this.context.workOrderId),
        with: {
          product: true
        }
      })

      if (!workOrder) return

      await inventoryEventEmitter.emitMaterialConsumptionEvent(
        InventoryEventType.MATERIAL_CONSUMED,
        {
          consumptionId,
          workOrderId: this.context.workOrderId,
          workOrderNumber: workOrder.number,
          productId: workOrder.product_id,
          productName: workOrder.product?.name || "Unknown Product",
          lotId: allocation.lotId,
          materialId: materialAllocation.materialId,
          materialName: materialAllocation.materialName,
          materialSku: "", // Would need to fetch from material data
          quantityConsumed: allocation.allocatedQty,
          unitCost: allocation.unitCost,
          totalCost: allocation.totalCost,
          remainingQuantity: allocation.availableQty - allocation.allocatedQty,
          consumedDate: this.context.consumptionDate!,
          notes: `Auto-consumed during work order completion - Qty: ${this.context.completedQty}`,
        },
        {
          companyId: this.context.companyId,
          userId: this.context.userId,
          source: "MaterialConsumptionService.executeMaterialConsumption",
        }
      )
    } catch (error) {
      console.error("❌ Failed to emit material consumption event:", error)
      // Don't throw - event emission shouldn't block the main workflow
    }
  }

  /**
   * Update work order with material costs
   * Integrates material costs into work order for accurate costing
   */
  private async updateWorkOrderMaterialCosts(materialCost: number) {
    // Get current work order costs
    const workOrder = await db.query.workOrders.findFirst({
      where: eq(workOrders.id, this.context.workOrderId)
    })

    if (!workOrder) return

    const currentMaterialCost = parseFloat(workOrder.material_cost || "0")
    const newMaterialCost = currentMaterialCost + materialCost

    // Update work order with new material cost
    await db.update(workOrders)
      .set({
        material_cost: newMaterialCost.toString(),
        updated_at: new Date()
      })
      .where(eq(workOrders.id, this.context.workOrderId))
  }

  /**
   * ✅ ENHANCED: Get optimal lots for consumption using intelligent FIFO algorithm
   * Considers expiry dates, quality status, and cost optimization
   */
  private async getOptimalLotsForConsumption(
    materialId: string,
    requiredQty: number
  ): Promise<Array<{
    id: string
    lot_number: string | null
    qty: string
    unit_cost: string
    total_cost: string
    currency: string
    received_date: string | null
    expiry_date: string | null
    quality_status: string
    status: string
    location: string
    supplier: any
  }>> {
    // Get all potentially available lots
    const candidateLots = await db.query.rawMaterialLots.findMany({
      where: and(
        eq(rawMaterialLots.raw_material_id, materialId),
        eq(rawMaterialLots.company_id, this.context.companyId),
        eq(rawMaterialLots.status, "available"),
        eq(rawMaterialLots.quality_status, "approved"),
        gt(rawMaterialLots.qty, "0")
      ),
      with: {
        supplier: true
      }
    })

    if (candidateLots.length === 0) {
      return []
    }

    // ✅ ENHANCED: Intelligent lot sorting algorithm
    const sortedLots = candidateLots.sort((a, b) => {
      // Priority 1: Expiry date (use expiring lots first)
      const aExpiry = a.expiry_date ? new Date(a.expiry_date) : null
      const bExpiry = b.expiry_date ? new Date(b.expiry_date) : null

      if (aExpiry && bExpiry) {
        const expiryDiff = aExpiry.getTime() - bExpiry.getTime()
        if (Math.abs(expiryDiff) > 7 * 24 * 60 * 60 * 1000) { // More than 7 days difference
          return expiryDiff // Use expiring lots first
        }
      } else if (aExpiry && !bExpiry) {
        return -1 // Lots with expiry dates first
      } else if (!aExpiry && bExpiry) {
        return 1 // Lots with expiry dates first
      }

      // Priority 2: FIFO - Received date (oldest first)
      const aReceived = a.received_date ? new Date(a.received_date) : new Date(a.created_at || 0)
      const bReceived = b.received_date ? new Date(b.received_date) : new Date(b.created_at || 0)
      const receivedDiff = aReceived.getTime() - bReceived.getTime()

      if (Math.abs(receivedDiff) > 3 * 24 * 60 * 60 * 1000) { // More than 3 days difference
        return receivedDiff // FIFO principle
      }

      // Priority 3: Cost optimization (use higher cost lots first to maintain average cost)
      const aCost = parseFloat(a.unit_cost || "0")
      const bCost = parseFloat(b.unit_cost || "0")
      const costDiff = bCost - aCost // Higher cost first

      if (Math.abs(costDiff) > 0.10) { // More than $0.10 difference
        return costDiff
      }

      // Priority 4: Lot size (use smaller lots first to minimize waste)
      const aQty = parseFloat(a.qty || "0")
      const bQty = parseFloat(b.qty || "0")

      return aQty - bQty // Smaller lots first
    })

    // ✅ ENHANCED: Select optimal combination of lots
    const selectedLots = []
    let remainingQty = requiredQty

    for (const lot of sortedLots) {
      if (remainingQty <= 0) break

      const lotQty = parseFloat(lot.qty || "0")

      // Check if lot is still valid (not expired)
      if (lot.expiry_date) {
        const expiryDate = new Date(lot.expiry_date)
        const today = new Date()
        if (expiryDate < today) {
          console.warn(`⚠️ Skipping expired lot ${lot.lot_number || lot.id}`)
          continue
        }
      }

      selectedLots.push(lot)
      remainingQty -= Math.min(lotQty, remainingQty)
    }

    console.log(`📊 FIFO Algorithm selected ${selectedLots.length} lots for material ${materialId}`)
    console.log(`   - Required: ${requiredQty}, Available: ${selectedLots.reduce((sum, lot) => sum + parseFloat(lot.qty || "0"), 0)}`)

    return selectedLots
  }

  /**
   * Static method: Check material availability for work order
   * Used for pre-production validation
   */
  static async checkMaterialAvailability(
    companyId: string,
    productId: string,
    plannedQty: number
  ): Promise<{
    available: boolean
    shortages: Array<{
      materialId: string
      materialName: string
      requiredQty: number
      availableQty: number
      shortageQty: number
    }>
  }> {
    try {
      // Get BOM for the product
      const bomItems = await db.query.billOfMaterials.findMany({
        where: and(
          eq(billOfMaterials.product_id, productId),
          eq(billOfMaterials.company_id, companyId),
          eq(billOfMaterials.status, "active")
        ),
        with: {
          rawMaterial: true
        }
      })

      const shortages = []

      for (const bomItem of bomItems) {
        const qtyRequired = parseFloat(bomItem.qty_required) * plannedQty
        const wasteFactor = parseFloat(bomItem.waste_factor || "0.05")
        const totalRequired = qtyRequired * (1 + wasteFactor)

        // Get available quantity for this material
        const availableLots = await db.query.rawMaterialLots.findMany({
          where: and(
            eq(rawMaterialLots.raw_material_id, bomItem.raw_material_id),
            eq(rawMaterialLots.company_id, companyId),
            eq(rawMaterialLots.status, "available"),
            eq(rawMaterialLots.quality_status, "approved"),
            gt(rawMaterialLots.qty, "0")
          )
        })

        const availableQty = availableLots.reduce((sum, lot) => {
          return sum + parseFloat(lot.qty)
        }, 0)

        if (availableQty < totalRequired) {
          shortages.push({
            materialId: bomItem.raw_material_id,
            materialName: bomItem.rawMaterial?.name || "Unknown Material",
            requiredQty: totalRequired,
            availableQty,
            shortageQty: totalRequired - availableQty
          })
        }
      }

      return {
        available: shortages.length === 0,
        shortages
      }
    } catch (error) {
      console.error("Error checking material availability:", error)
      return {
        available: false,
        shortages: []
      }
    }
  }
}
