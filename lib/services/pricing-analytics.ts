// ============================================================================
// MANUFACTURING ERP - PRICING ANALYTICS SERVICE
// ============================================================================
//
// Professional pricing analytics service leveraging Phase 2 pricing context
// enhancements for advanced margin analysis, cost tracking, and pricing
// optimization across all ERP modules.
//
// FEATURES:
// ✅ Contract Margin Analysis - Detailed profitability insights
// ✅ Pricing Method Analytics - Performance by pricing strategy
// ✅ Cost Tracking Reports - Comprehensive cost analysis
// ✅ Pricing Optimization - Data-driven pricing recommendations
// ✅ Multi-Currency Support - Global pricing analytics
//
// <AUTHOR> ERP Developer
// @version 1.0.0 - Phase 2 Enhanced Reporting Services
// @date 2024-01-XX
// ============================================================================

import { db } from "@/lib/db"
import { 
  salesContracts, 
  salesContractItems, 
  purchaseContracts, 
  purchaseContractItems,
  products,
  stockTxns
} from "@/lib/schema-postgres"
import { eq, and, desc, sql, gte, lte, isNotNull } from "drizzle-orm"

export interface PricingAnalyticsContext {
  companyId: string
  userId: string
}

export interface MarginAnalysisResult {
  contractId: string
  contractNumber: string
  totalRevenue: number
  totalCost: number
  totalMargin: number
  marginPercentage: number
  currency: string
  itemCount: number
  averageMargin: number
  pricingMethods: Record<string, number>
}

export interface PricingMethodPerformance {
  method: string
  contractCount: number
  totalRevenue: number
  averageMargin: number
  marginRange: { min: number; max: number }
  currency: string
}

export interface CostTrackingReport {
  productId: string
  productName: string
  productSku: string
  currentCostPrice: number | null
  currentBasePrice: number | null
  averageContractPrice: number
  lastTransactionCost: number | null
  costVariance: number
  priceVariance: number
  currency: string
}

export class PricingAnalyticsService {
  constructor(private context: PricingAnalyticsContext) {}

  /**
   * ✅ ENHANCED: Analyze contract margins using pricing context
   */
  async getContractMarginAnalysis(
    dateFrom?: string,
    dateTo?: string,
    currency?: string
  ): Promise<MarginAnalysisResult[]> {
    const whereConditions = [eq(salesContracts.company_id, this.context.companyId)]
    
    if (dateFrom) {
      whereConditions.push(gte(salesContracts.date, dateFrom))
    }
    if (dateTo) {
      whereConditions.push(lte(salesContracts.date, dateTo))
    }
    if (currency) {
      whereConditions.push(eq(salesContracts.currency, currency))
    }

    const contracts = await db.query.salesContracts.findMany({
      where: and(...whereConditions),
      with: {
        items: {
          with: {
            product: true,
          },
        },
      },
      orderBy: [desc(salesContracts.date)],
    })

    return contracts.map(contract => {
      let totalRevenue = 0
      let totalCost = 0
      const pricingMethods: Record<string, number> = {}
      let validMarginItems = 0

      contract.items.forEach(item => {
        const price = parseFloat(item.price || '0')
        const qty = parseFloat(item.qty || '0')
        const itemRevenue = price * qty

        totalRevenue += itemRevenue

        // Calculate cost using pricing context
        let itemCost = 0
        if (item.cost_basis && parseFloat(item.cost_basis) > 0) {
          itemCost = parseFloat(item.cost_basis) * qty
        } else if (item.product.cost_price && parseFloat(item.product.cost_price) > 0) {
          itemCost = parseFloat(item.product.cost_price) * qty
        } else if (item.product.base_price && parseFloat(item.product.base_price) > 0) {
          itemCost = parseFloat(item.product.base_price) * qty * 0.7 // Assume 70% cost ratio
        }

        totalCost += itemCost

        // Track pricing methods
        const method = item.pricing_method || 'unknown'
        pricingMethods[method] = (pricingMethods[method] || 0) + itemRevenue

        if (item.margin_percentage) {
          validMarginItems++
        }
      })

      const totalMargin = totalRevenue - totalCost
      const marginPercentage = totalRevenue > 0 ? (totalMargin / totalRevenue) * 100 : 0
      const averageMargin = validMarginItems > 0 
        ? contract.items.reduce((sum, item) => {
            return sum + (parseFloat(item.margin_percentage || '0'))
          }, 0) / validMarginItems
        : marginPercentage

      return {
        contractId: contract.id,
        contractNumber: contract.number,
        totalRevenue,
        totalCost,
        totalMargin,
        marginPercentage,
        currency: contract.currency || 'USD',
        itemCount: contract.items.length,
        averageMargin,
        pricingMethods,
      }
    })
  }

  /**
   * ✅ ENHANCED: Analyze pricing method performance
   */
  async getPricingMethodPerformance(
    dateFrom?: string,
    dateTo?: string
  ): Promise<PricingMethodPerformance[]> {
    const whereConditions = [eq(salesContracts.company_id, this.context.companyId)]
    
    if (dateFrom) {
      whereConditions.push(gte(salesContracts.date, dateFrom))
    }
    if (dateTo) {
      whereConditions.push(lte(salesContracts.date, dateTo))
    }

    const contracts = await db.query.salesContracts.findMany({
      where: and(...whereConditions),
      with: {
        items: {
          where: isNotNull(salesContractItems.pricing_method),
        },
      },
    })

    const methodStats: Record<string, {
      contractIds: Set<string>
      totalRevenue: number
      margins: number[]
      currency: string
    }> = {}

    contracts.forEach(contract => {
      contract.items.forEach(item => {
        const method = item.pricing_method || 'unknown'
        const price = parseFloat(item.price || '0')
        const qty = parseFloat(item.qty || '0')
        const revenue = price * qty
        const margin = parseFloat(item.margin_percentage || '0')

        if (!methodStats[method]) {
          methodStats[method] = {
            contractIds: new Set(),
            totalRevenue: 0,
            margins: [],
            currency: contract.currency || 'USD',
          }
        }

        methodStats[method].contractIds.add(contract.id)
        methodStats[method].totalRevenue += revenue
        if (margin > 0) {
          methodStats[method].margins.push(margin)
        }
      })
    })

    return Object.entries(methodStats).map(([method, stats]) => ({
      method,
      contractCount: stats.contractIds.size,
      totalRevenue: stats.totalRevenue,
      averageMargin: stats.margins.length > 0 
        ? stats.margins.reduce((sum, margin) => sum + margin, 0) / stats.margins.length
        : 0,
      marginRange: stats.margins.length > 0 
        ? { 
            min: Math.min(...stats.margins), 
            max: Math.max(...stats.margins) 
          }
        : { min: 0, max: 0 },
      currency: stats.currency,
    }))
  }

  /**
   * ✅ ENHANCED: Generate cost tracking report
   */
  async getCostTrackingReport(): Promise<CostTrackingReport[]> {
    const products = await db.query.products.findMany({
      where: eq(products.company_id, this.context.companyId),
      with: {
        salesContractItems: {
          orderBy: [desc(salesContractItems.created_at)],
          limit: 5, // Recent contract prices
        },
        stockTxns: {
          where: isNotNull(stockTxns.unit_cost),
          orderBy: [desc(stockTxns.created_at)],
          limit: 1, // Most recent transaction cost
        },
      },
    })

    return products.map(product => {
      const currentCostPrice = product.cost_price ? parseFloat(product.cost_price) : null
      const currentBasePrice = product.base_price ? parseFloat(product.base_price) : null
      
      // Calculate average contract price
      const contractPrices = product.salesContractItems
        .map(item => parseFloat(item.price || '0'))
        .filter(price => price > 0)
      const averageContractPrice = contractPrices.length > 0
        ? contractPrices.reduce((sum, price) => sum + price, 0) / contractPrices.length
        : 0

      // Get last transaction cost
      const lastTransactionCost = product.stockTxns.length > 0 && product.stockTxns[0].unit_cost
        ? parseFloat(product.stockTxns[0].unit_cost)
        : null

      // Calculate variances
      const costVariance = currentCostPrice && lastTransactionCost
        ? ((lastTransactionCost - currentCostPrice) / currentCostPrice) * 100
        : 0

      const priceVariance = currentBasePrice && averageContractPrice > 0
        ? ((averageContractPrice - currentBasePrice) / currentBasePrice) * 100
        : 0

      return {
        productId: product.id,
        productName: product.name,
        productSku: product.sku,
        currentCostPrice,
        currentBasePrice,
        averageContractPrice,
        lastTransactionCost,
        costVariance,
        priceVariance,
        currency: product.currency || 'USD',
      }
    })
  }

  /**
   * ✅ ENHANCED: Get pricing optimization recommendations
   */
  async getPricingOptimizationRecommendations(): Promise<{
    underperformingProducts: Array<{
      productId: string
      productName: string
      currentMargin: number
      recommendedPrice: number
      potentialIncrease: number
    }>
    overperformingMethods: Array<{
      method: string
      averageMargin: number
      recommendation: string
    }>
  }> {
    const marginAnalysis = await this.getContractMarginAnalysis()
    const methodPerformance = await this.getPricingMethodPerformance()

    // Identify underperforming products (margin < 20%)
    const underperformingProducts = marginAnalysis
      .filter(contract => contract.marginPercentage < 20)
      .flatMap(contract => 
        // This would need more detailed product-level analysis
        // For now, return empty array as placeholder
        []
      )

    // Identify high-performing pricing methods (margin > 30%)
    const overperformingMethods = methodPerformance
      .filter(method => method.averageMargin > 30)
      .map(method => ({
        method: method.method,
        averageMargin: method.averageMargin,
        recommendation: `Consider applying ${method.method} strategy to more products. Current average margin: ${method.averageMargin.toFixed(1)}%`,
      }))

    return {
      underperformingProducts,
      overperformingMethods,
    }
  }
}
