/**
 * Inventory Transactions Engine Service
 * 
 * Comprehensive transaction management for Manufacturing ERP
 * Handles inbound, outbound, transfer, and adjustment transactions
 * Following established patterns with multi-tenant security and enterprise-grade validation
 */

import { db, uid } from "@/lib/db"
import { stockLots, stockTxns, products } from "@/lib/schema-postgres"
import { eq, and, gt, asc, desc, sql } from "drizzle-orm"
import {
  inboundTransactionSchema,
  outboundTransactionSchema,
  transferTransactionSchema,
  adjustmentTransactionSchema,
  comprehensiveTransactionSchema
} from "@/lib/validations"
import { z } from "zod"
import { LEGACY_LOCATION_MAPPING, LocationManager } from "@/lib/location-config"

export interface TransactionContext {
  companyId: string
  userId: string
  userAgent?: string
  ipAddress?: string
}

export interface TransactionResult {
  success: boolean
  transactionId: string
  message: string
  affectedLots?: string[]
}

export interface StockMovement {
  lotId: string
  productId: string
  fromQty: number
  toQty: number
  movedQty: number
}

export class InventoryTransactionService {
  constructor(private context: TransactionContext) { }

  /**
   * Normalize location ID to professional format
   * Handles both legacy and new location IDs for seamless migration
   */
  private normalizeLocationId(locationId: string): string {
    // If it's already a professional location ID, return as-is
    if (LocationManager.getLocationById(locationId)) {
      return locationId
    }

    // Check if it's a legacy location that needs mapping
    const professionalId = LEGACY_LOCATION_MAPPING[locationId]
    if (professionalId) {
      return professionalId
    }

    // Return original if no mapping found (could be a new custom location)
    return locationId
  }

  /**
   * Process Inbound Transaction
   * Creates new stock lot and records inbound transaction
   */
  async processInbound(
    data: z.infer<typeof inboundTransactionSchema>
  ): Promise<TransactionResult> {
    try {
      // Validate input
      const validated = inboundTransactionSchema.parse(data)

      // Normalize location to professional format
      const normalizedLocation = this.normalizeLocationId(validated.to_location)

      // Verify product exists and belongs to company
      const product = await db.query.products.findFirst({
        where: and(
          eq(products.id, validated.product_id),
          eq(products.company_id, this.context.companyId)
        )
      })

      if (!product) {
        throw new Error("Product not found or access denied")
      }

      const lotId = uid("lot")
      const txnId = uid("txn")

      // Determine quality status based on product requirements
      const qualityStatus = product.inspection_required === "true" ? "pending" : "approved"

      await db.transaction(async (tx) => {
        // Create stock lot
        await tx.insert(stockLots).values({
          id: lotId,
          company_id: this.context.companyId,
          product_id: validated.product_id,
          qty: validated.qty.toString(),
          location: normalizedLocation,
          quality_status: qualityStatus,
          quality_notes: product.inspection_required === "true"
            ? "Inspection required before use"
            : "Auto-approved - no inspection required",
          created_at: new Date(),
        })

        // ✅ PHASE 1 ENHANCEMENT: Calculate cost values for accurate inventory valuation
        let unitCost: string | undefined
        let totalValue: string | undefined

        if (validated.unit_cost) {
          unitCost = validated.unit_cost.toString()
          totalValue = (validated.unit_cost * validated.qty).toString()
        } else {
          // Try to get cost from product pricing hierarchy
          const costPrice = product.cost_price && parseFloat(product.cost_price) > 0
            ? parseFloat(product.cost_price)
            : product.base_price && parseFloat(product.base_price) > 0
              ? parseFloat(product.base_price)
              : product.price && parseFloat(product.price) > 0
                ? parseFloat(product.price)
                : undefined

          if (costPrice) {
            unitCost = costPrice.toString()
            totalValue = (costPrice * validated.qty).toString()
          }
        }

        // Create transaction record
        await tx.insert(stockTxns).values({
          id: txnId,
          company_id: this.context.companyId,
          type: "inbound", // Legacy compatibility
          transaction_type: "inbound",
          product_id: validated.product_id,
          qty: validated.qty.toString(),
          to_location: normalizedLocation,
          location: normalizedLocation, // Backward compatibility
          reason_code: validated.reason_code || "receipt",
          approval_status: "approved",
          reference: validated.reference,
          notes: validated.notes,
          workflow_trigger: "manual",
          created_by: this.context.userId,
          created_at: new Date(),
          // ✅ PHASE 1 ENHANCEMENT: Cost tracking fields
          unit_cost: unitCost,
          total_value: totalValue,
          cost_method: validated.cost_method || "standard",
          cost_currency: validated.cost_currency || "USD",
        })
      })

      return {
        success: true,
        transactionId: txnId,
        message: `Successfully received ${validated.qty} units of ${product.name}`,
        affectedLots: [lotId]
      }
    } catch (error) {
      console.error("Inbound transaction error:", error)
      throw new Error(`Inbound transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Process Outbound Transaction
   * Reduces stock from existing lots using FIFO method
   */
  async processOutbound(
    data: z.infer<typeof outboundTransactionSchema>
  ): Promise<TransactionResult> {
    try {
      // Validate input
      const validated = outboundTransactionSchema.parse(data)

      // Verify product exists and belongs to company
      const product = await db.query.products.findFirst({
        where: and(
          eq(products.id, validated.product_id),
          eq(products.company_id, this.context.companyId)
        )
      })

      if (!product) {
        throw new Error("Product not found or access denied")
      }

      // Get available stock lots (FIFO order, approved quality only)
      const availableLots = await db.query.stockLots.findMany({
        where: and(
          eq(stockLots.company_id, this.context.companyId),
          eq(stockLots.product_id, validated.product_id),
          eq(stockLots.location, validated.from_location),
          eq(stockLots.quality_status, "approved"),
          gt(stockLots.qty, "0")
        ),
        orderBy: [asc(stockLots.created_at)]
      })

      // Check available quantity
      const totalAvailable = availableLots.reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
      if (totalAvailable < validated.qty) {
        throw new Error(`Insufficient stock. Available: ${totalAvailable}, Required: ${validated.qty}`)
      }

      const txnId = uid("txn")
      const affectedLots: string[] = []
      const movements: StockMovement[] = []

      // Process outbound using FIFO
      let remaining = validated.qty

      await db.transaction(async (tx) => {
        for (const lot of availableLots) {
          if (remaining <= 0) break

          const lotQty = parseFloat(lot.qty)
          const takeQty = Math.min(remaining, lotQty)
          const newQty = lotQty - takeQty

          // Update lot quantity
          await tx
            .update(stockLots)
            .set({
              qty: newQty.toString(),
              updated_at: new Date()
            })
            .where(eq(stockLots.id, lot.id))

          affectedLots.push(lot.id)
          movements.push({
            lotId: lot.id,
            productId: validated.product_id,
            fromQty: lotQty,
            toQty: newQty,
            movedQty: takeQty
          })

          remaining -= takeQty
        }

        // ✅ PHASE 1 ENHANCEMENT: Calculate cost values for outbound transactions
        let unitCost: string | undefined
        let totalValue: string | undefined

        if (validated.unit_cost) {
          unitCost = validated.unit_cost.toString()
          totalValue = (validated.unit_cost * validated.qty).toString()
        } else {
          // For outbound, try to use average cost from affected lots or product pricing
          const avgCostFromLots = affectedLots.length > 0
            ? affectedLots.reduce((sum, lot) => sum + (parseFloat(lot.qty) || 0), 0) > 0
              ? affectedLots.reduce((sum, lot) => {
                const lotQty = parseFloat(lot.qty) || 0
                const lotCost = product.cost_price && parseFloat(product.cost_price) > 0
                  ? parseFloat(product.cost_price) : 0
                return sum + (lotQty * lotCost)
              }, 0) / affectedLots.reduce((sum, lot) => sum + (parseFloat(lot.qty) || 0), 0)
              : undefined
            : undefined

          const costPrice = avgCostFromLots ||
            (product.cost_price && parseFloat(product.cost_price) > 0
              ? parseFloat(product.cost_price)
              : product.base_price && parseFloat(product.base_price) > 0
                ? parseFloat(product.base_price)
                : product.price && parseFloat(product.price) > 0
                  ? parseFloat(product.price)
                  : undefined)

          if (costPrice) {
            unitCost = costPrice.toString()
            totalValue = (costPrice * validated.qty).toString()
          }
        }

        // Create transaction record
        await tx.insert(stockTxns).values({
          id: txnId,
          company_id: this.context.companyId,
          type: "outbound", // Legacy compatibility
          transaction_type: "outbound",
          product_id: validated.product_id,
          qty: validated.qty.toString(),
          from_location: validated.from_location,
          location: validated.from_location, // Backward compatibility
          reason_code: validated.reason_code || "shipment",
          approval_status: "approved",
          reference: validated.reference,
          notes: validated.notes,
          workflow_trigger: "manual",
          created_by: this.context.userId,
          created_at: new Date(),
          // ✅ PHASE 1 ENHANCEMENT: Cost tracking fields
          unit_cost: unitCost,
          total_value: totalValue,
          cost_method: validated.cost_method || "standard",
          cost_currency: validated.cost_currency || "USD",
        })
      })

      return {
        success: true,
        transactionId: txnId,
        message: `Successfully shipped ${validated.qty} units of ${product.name}`,
        affectedLots
      }
    } catch (error) {
      console.error("Outbound transaction error:", error)
      throw new Error(`Outbound transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Process Transfer Transaction
   * Moves stock between locations with optional approval workflow
   */
  async processTransfer(
    data: z.infer<typeof transferTransactionSchema>
  ): Promise<TransactionResult> {
    try {
      // Validate input
      const validated = transferTransactionSchema.parse(data)

      // Normalize locations to professional format
      const normalizedFromLocation = this.normalizeLocationId(validated.from_location)
      const normalizedToLocation = this.normalizeLocationId(validated.to_location)

      // Verify product exists and belongs to company
      const product = await db.query.products.findFirst({
        where: and(
          eq(products.id, validated.product_id),
          eq(products.company_id, this.context.companyId)
        )
      })

      if (!product) {
        throw new Error("Product not found or access denied")
      }

      // Get available stock lots from source location
      const availableLots = await db.query.stockLots.findMany({
        where: and(
          eq(stockLots.company_id, this.context.companyId),
          eq(stockLots.product_id, validated.product_id),
          eq(stockLots.location, normalizedFromLocation),
          eq(stockLots.quality_status, "approved"),
          gt(stockLots.qty, "0")
        ),
        orderBy: [asc(stockLots.created_at)]
      })

      // Check available quantity
      const totalAvailable = availableLots.reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
      if (totalAvailable < validated.qty) {
        throw new Error(`Insufficient stock in source location. Available: ${totalAvailable}, Required: ${validated.qty}`)
      }

      const txnId = uid("txn")
      const newLotId = uid("lot")
      const affectedLots: string[] = []

      await db.transaction(async (tx) => {
        // Reduce quantity from source lots (FIFO)
        let remaining = validated.qty
        for (const lot of availableLots) {
          if (remaining <= 0) break

          const lotQty = parseFloat(lot.qty)
          const takeQty = Math.min(remaining, lotQty)
          const newQty = lotQty - takeQty

          await tx
            .update(stockLots)
            .set({
              qty: newQty.toString(),
              updated_at: new Date()
            })
            .where(eq(stockLots.id, lot.id))

          affectedLots.push(lot.id)
          remaining -= takeQty
        }

        // Create new lot in destination location
        await tx.insert(stockLots).values({
          id: newLotId,
          company_id: this.context.companyId,
          product_id: validated.product_id,
          qty: validated.qty.toString(),
          location: normalizedToLocation,
          quality_status: "approved", // Transfers maintain quality status
          quality_notes: "Transferred from approved stock",
          created_at: new Date(),
        })

        affectedLots.push(newLotId)

        // Create transaction record
        await tx.insert(stockTxns).values({
          id: txnId,
          company_id: this.context.companyId,
          type: "outbound", // Legacy compatibility (transfer treated as outbound)
          transaction_type: "transfer",
          product_id: validated.product_id,
          qty: validated.qty.toString(),
          from_location: normalizedFromLocation,
          to_location: normalizedToLocation,
          location: normalizedFromLocation, // Backward compatibility
          reason_code: validated.reason_code || "transfer",
          approval_status: validated.approval_status || "approved",
          reference: `Transfer to ${validated.to_location}`,
          notes: validated.notes,
          workflow_trigger: "manual",
          created_by: this.context.userId,
          created_at: new Date(),
        })
      })

      return {
        success: true,
        transactionId: txnId,
        message: `Successfully transferred ${validated.qty} units of ${product.name} from ${normalizedFromLocation} to ${normalizedToLocation}`,
        affectedLots
      }
    } catch (error) {
      console.error("Transfer transaction error:", error)
      throw new Error(`Transfer transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Process Adjustment Transaction
   * Adjusts stock quantities for cycle counts, damage, obsolescence, etc.
   */
  async processAdjustment(
    data: z.infer<typeof adjustmentTransactionSchema>
  ): Promise<TransactionResult> {
    try {
      // Validate input
      const validated = adjustmentTransactionSchema.parse(data)

      // Verify product exists and belongs to company
      const product = await db.query.products.findFirst({
        where: and(
          eq(products.id, validated.product_id),
          eq(products.company_id, this.context.companyId)
        )
      })

      if (!product) {
        throw new Error("Product not found or access denied")
      }

      const txnId = uid("txn")
      const affectedLots: string[] = []

      if (validated.qty > 0) {
        // Positive adjustment - add stock (create new lot)
        const lotId = uid("lot")

        await db.transaction(async (tx) => {
          // Create new stock lot for positive adjustment
          await tx.insert(stockLots).values({
            id: lotId,
            company_id: this.context.companyId,
            product_id: validated.product_id,
            qty: validated.qty.toString(),
            location: validated.location,
            quality_status: "approved", // Adjustments are typically pre-approved
            quality_notes: `Adjustment: ${validated.reason_code}`,
            created_at: new Date(),
          })

          affectedLots.push(lotId)

          // Create transaction record
          await tx.insert(stockTxns).values({
            id: txnId,
            company_id: this.context.companyId,
            type: "inbound", // Legacy compatibility
            transaction_type: "adjustment",
            product_id: validated.product_id,
            qty: validated.qty.toString(),
            location: validated.location,
            reason_code: validated.reason_code,
            approval_status: validated.approval_status || "approved",
            reference: `Adjustment: ${validated.reason_code}`,
            notes: validated.notes,
            workflow_trigger: "manual",
            created_by: this.context.userId,
            created_at: new Date(),
          })
        })

        return {
          success: true,
          transactionId: txnId,
          message: `Successfully added ${validated.qty} units of ${product.name} via adjustment`,
          affectedLots
        }
      } else {
        // Negative adjustment - reduce stock (FIFO from existing lots)
        const adjustmentQty = Math.abs(validated.qty)

        const availableLots = await db.query.stockLots.findMany({
          where: and(
            eq(stockLots.company_id, this.context.companyId),
            eq(stockLots.product_id, validated.product_id),
            eq(stockLots.location, validated.location),
            gt(stockLots.qty, "0")
          ),
          orderBy: [asc(stockLots.created_at)]
        })

        // Check available quantity
        const totalAvailable = availableLots.reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
        if (totalAvailable < adjustmentQty) {
          throw new Error(`Insufficient stock for adjustment. Available: ${totalAvailable}, Required: ${adjustmentQty}`)
        }

        await db.transaction(async (tx) => {
          // Reduce quantity from existing lots (FIFO)
          let remaining = adjustmentQty
          for (const lot of availableLots) {
            if (remaining <= 0) break

            const lotQty = parseFloat(lot.qty)
            const takeQty = Math.min(remaining, lotQty)
            const newQty = lotQty - takeQty

            await tx
              .update(stockLots)
              .set({
                qty: newQty.toString(),
                updated_at: new Date()
              })
              .where(eq(stockLots.id, lot.id))

            affectedLots.push(lot.id)
            remaining -= takeQty
          }

          // Create transaction record
          await tx.insert(stockTxns).values({
            id: txnId,
            company_id: this.context.companyId,
            type: "outbound", // Legacy compatibility
            transaction_type: "adjustment",
            product_id: validated.product_id,
            qty: adjustmentQty.toString(),
            location: validated.location,
            reason_code: validated.reason_code,
            approval_status: validated.approval_status || "approved",
            reference: `Adjustment: ${validated.reason_code}`,
            notes: validated.notes,
            workflow_trigger: "manual",
            created_by: this.context.userId,
            created_at: new Date(),
          })
        })

        return {
          success: true,
          transactionId: txnId,
          message: `Successfully reduced ${adjustmentQty} units of ${product.name} via adjustment`,
          affectedLots
        }
      }
    } catch (error) {
      console.error("Adjustment transaction error:", error)
      throw new Error(`Adjustment transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get transaction history with filtering
   */
  async getTransactionHistory(filters?: {
    productId?: string
    transactionType?: string
    location?: string
    dateFrom?: Date
    dateTo?: Date
    limit?: number
  }): Promise<any[]> {
    try {
      const whereConditions = [eq(stockTxns.company_id, this.context.companyId)]

      if (filters?.productId) {
        whereConditions.push(eq(stockTxns.product_id, filters.productId))
      }
      if (filters?.transactionType) {
        whereConditions.push(eq(stockTxns.transaction_type, filters.transactionType))
      }
      if (filters?.location) {
        whereConditions.push(eq(stockTxns.location, filters.location))
      }

      const transactions = await db.query.stockTxns.findMany({
        where: and(...whereConditions),
        orderBy: [desc(stockTxns.created_at)],
        limit: filters?.limit || 100,
        with: {
          product: true,
        }
      })

      return transactions
    } catch (error) {
      console.error("Transaction history error:", error)
      throw new Error(`Failed to retrieve transaction history: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get current stock levels by location
   */
  async getStockLevels(productId?: string, location?: string): Promise<any[]> {
    try {
      const whereConditions = [
        eq(stockLots.company_id, this.context.companyId),
        gt(stockLots.qty, "0")
      ]

      if (productId) {
        whereConditions.push(eq(stockLots.product_id, productId))
      }
      if (location) {
        whereConditions.push(eq(stockLots.location, location))
      }

      const stockLevels = await db.query.stockLots.findMany({
        where: and(...whereConditions),
        orderBy: [asc(stockLots.created_at)],
        with: {
          product: true,
        }
      })

      return stockLevels
    } catch (error) {
      console.error("Stock levels error:", error)
      throw new Error(`Failed to retrieve stock levels: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}
