/**
 * Manufacturing ERP - Shipping Financial Integration Service
 * Professional shipment-to-invoice workflow automation following ERP standards
 * Zero-breaking-changes: Additive service that extends existing financial integration
 */

import { db, uid } from "@/lib/db"
import {
  arInvoices,
  shipments,
  shipmentItems,
  stockTxns
} from "@/lib/schema-postgres"
import { eq, and, desc } from "drizzle-orm"
import { TenantContext } from "@/lib/tenant-utils"
import { BusinessLogicError, ErrorCode } from "@/lib/errors"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface CreateARInvoiceFromShipmentData {
  shipmentId: string
  invoiceNumber?: string
  dueDate?: string
  paymentTerms?: string
  notes?: string
  autoGenerate?: boolean // For automatic generation on shipment status change
}

export interface ShipmentFinancialSummary {
  shipmentId: string
  shipmentNumber: string
  totalRevenue: number
  totalCOGS: number
  grossProfit: number
  profitMargin: number
  invoiceGenerated: boolean
  invoiceId?: string
}

// ============================================================================
// SHIPPING FINANCIAL INTEGRATION SERVICE
// ============================================================================

export class ShippingFinancialIntegration {
  private context: TenantContext

  constructor(context: TenantContext) {
    this.context = context
  }

  /**
   * Generate AR Invoice from Shipped Order
   * Automatically creates AR invoice when shipment status changes to 'shipped'
   */
  async generateARInvoiceFromShipment(
    data: CreateARInvoiceFromShipmentData
  ): Promise<any> {
    console.log(`🔄 Generating AR invoice from shipment: ${data.shipmentId}`)

    // Validate shipment exists and belongs to company
    const shipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, data.shipmentId),
        eq(shipments.company_id, this.context.companyId)
      ),
      with: {
        customer: true,
        items: {
          with: {
            product: true,
            stockLot: true
          }
        }
      }
    })

    if (!shipment) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        `Shipment ${data.shipmentId} not found`
      )
    }

    // ERP Business Rule: Only shipped orders can be invoiced
    const invoiceableStatuses = ["shipped", "delivered"]
    if (!invoiceableStatuses.includes(shipment.status)) {
      throw new BusinessLogicError(
        ErrorCode.INVALID_STATE_TRANSITION,
        `Shipment must be shipped/delivered to generate invoice. Current status: ${shipment.status}`
      )
    }

    // Check if invoice already exists for this shipment (using notes field temporarily)
    const allInvoices = await db.query.arInvoices.findMany({
      where: and(
        eq(arInvoices.company_id, this.context.companyId),
        eq(arInvoices.customer_id, shipment.customer_id)
      )
    })

    // Calculate expected revenue for comparison
    const expectedRevenue = shipment.items.reduce((sum, item) => {
      const quantity = parseFloat(item.quantity || '0')
      const unitPrice = parseFloat(item.unit_price || '0')
      return sum + (quantity * unitPrice)
    }, 0)

    // Check for existing invoice by multiple criteria
    const existingInvoice = allInvoices.find(inv => {
      const invoiceAmount = parseFloat(inv.amount)
      const amountMatches = Math.abs(invoiceAmount - expectedRevenue) < 0.01
      const notesMatch = inv.notes?.includes(shipment.id) || inv.notes?.includes(shipment.shipment_number || '')

      return amountMatches && notesMatch
    })

    if (existingInvoice) {
      console.log(`✅ Invoice already exists for shipment: ${existingInvoice.number}`)
      return existingInvoice
    }

    // Calculate total revenue from shipment items
    const totalRevenue = shipment.items.reduce((sum, item) => {
      const quantity = parseFloat(item.quantity || '0')
      const unitPrice = parseFloat(item.unit_price || '0')
      return sum + (quantity * unitPrice)
    }, 0)

    if (totalRevenue <= 0) {
      throw new BusinessLogicError(
        ErrorCode.INVALID_BUSINESS_LOGIC,
        `Shipment has no revenue items to invoice`
      )
    }

    // Generate invoice number if not provided
    const invoiceNumber = data.invoiceNumber || await this.generateInvoiceNumber("AR")

    // Calculate due date (default 30 days)
    const dueDate = data.dueDate || this.calculateDueDate(data.paymentTerms || "Net 30")

    const invoiceId = uid("ari")

    // Create AR invoice with shipment reference (using notes field temporarily)
    const newInvoice = {
      id: invoiceId,
      company_id: this.context.companyId,
      number: invoiceNumber,
      customer_id: shipment.customer_id,
      date: new Date().toISOString().split('T')[0],
      due_date: dueDate,
      amount: totalRevenue.toString(),
      received: "0",
      currency: "USD", // Default currency
      status: data.autoGenerate ? "pending" : "draft", // Auto-generated invoices are pending
      payment_terms: data.paymentTerms || "Net 30",
      notes: data.notes || `Generated from shipment ${shipment.shipment_number} (ID: ${shipment.id})`,
    }

    await db.insert(arInvoices).values(newInvoice)

    console.log(`✅ Created AR invoice: ${invoiceNumber} for $${totalRevenue}`)

    // Return created invoice with relationships
    const createdInvoice = await db.query.arInvoices.findFirst({
      where: and(
        eq(arInvoices.id, invoiceId),
        eq(arInvoices.company_id, this.context.companyId)
      ),
      with: {
        customer: true
      }
    })

    return createdInvoice
  }

  /**
   * Get financial summary for a shipment
   */
  async getShipmentFinancialSummary(shipmentId: string): Promise<ShipmentFinancialSummary> {
    const shipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, shipmentId),
        eq(shipments.company_id, this.context.companyId)
      ),
      with: {
        items: {
          with: {
            product: true
          }
        }
      }
    })

    if (!shipment) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        `Shipment ${shipmentId} not found`
      )
    }

    // Calculate revenue (selling prices)
    const totalRevenue = shipment.items.reduce((sum, item) => {
      const quantity = parseFloat(item.quantity || '0')
      const unitPrice = parseFloat(item.unit_price || '0')
      return sum + (quantity * unitPrice)
    }, 0)

    // Calculate COGS (cost prices) - ✅ FIXED: Use proper cost price hierarchy
    const totalCOGS = shipment.items.reduce((sum, item) => {
      const quantity = parseFloat(item.quantity || '0')
      // ✅ COGS FIX: Use cost_price field, fallback to price field, then default
      const costPrice = item.product?.cost_price && parseFloat(item.product.cost_price) > 0
        ? parseFloat(item.product.cost_price)
        : item.product?.price && parseFloat(item.product.price) > 0
          ? parseFloat(item.product.price)
          : 20 // Default cost price fallback
      return sum + (quantity * costPrice)
    }, 0)

    const grossProfit = totalRevenue - totalCOGS
    const profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0

    // Check if invoice exists (using notes field temporarily)
    const allInvoices = await db.query.arInvoices.findMany({
      where: eq(arInvoices.company_id, this.context.companyId)
    })

    const invoice = allInvoices.find(inv =>
      inv.notes?.includes(shipmentId) || inv.notes?.includes(shipment.shipment_number || '')
    )

    return {
      shipmentId,
      shipmentNumber: shipment.shipment_number || 'N/A',
      totalRevenue: Math.round(totalRevenue * 100) / 100,
      totalCOGS: Math.round(totalCOGS * 100) / 100,
      grossProfit: Math.round(grossProfit * 100) / 100,
      profitMargin: Math.round(profitMargin * 100) / 100,
      invoiceGenerated: !!invoice,
      invoiceId: invoice?.id
    }
  }

  /**
   * Generate invoice number following ERP standards
   */
  private async generateInvoiceNumber(type: "AR" | "AP"): Promise<string> {
    const prefix = type === "AR" ? "INV" : "BILL"
    const year = new Date().getFullYear()
    const month = String(new Date().getMonth() + 1).padStart(2, '0')

    // Get next sequence number
    const lastInvoice = await db.query.arInvoices.findFirst({
      where: eq(arInvoices.company_id, this.context.companyId),
      orderBy: [desc(arInvoices.created_at)]
    })

    let sequence = 1
    if (lastInvoice?.number) {
      const match = lastInvoice.number.match(/(\d+)$/)
      if (match) {
        sequence = parseInt(match[1]) + 1
      }
    }

    return `${prefix}-${year}${month}-${String(sequence).padStart(3, '0')}`
  }

  /**
   * Calculate due date based on payment terms
   */
  private calculateDueDate(paymentTerms: string): string {
    const today = new Date()
    let daysToAdd = 30 // Default

    if (paymentTerms.includes("Net 15")) daysToAdd = 15
    else if (paymentTerms.includes("Net 30")) daysToAdd = 30
    else if (paymentTerms.includes("Net 60")) daysToAdd = 60
    else if (paymentTerms.includes("Net 90")) daysToAdd = 90

    const dueDate = new Date(today)
    dueDate.setDate(today.getDate() + daysToAdd)

    return dueDate.toISOString().split('T')[0]
  }
}
