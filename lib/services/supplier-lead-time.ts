/**
 * Manufacturing ERP - Supplier Lead Time Management Service
 * 
 * Professional service for supplier lead time tracking, performance monitoring,
 * and real-time updates for procurement planning optimization
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { db, uid } from "@/lib/db"
import {
  supplierLeadTimes,
  suppliers,
  rawMaterials,
  purchaseContracts,
  purchaseContractItems,
  companies
} from "@/lib/schema-postgres"
import { eq, and, asc, desc as descOrder, sql, gte, lte, isNull, or, avg, count } from "drizzle-orm"
import { z } from "zod"

// ✅ PROFESSIONAL: Type definitions for enterprise-grade lead time management
export interface SupplierLeadTimeContext {
  companyId: string
  userId: string
  supplierId: string
  rawMaterialId?: string
}

export interface SupplierLeadTime {
  id: string
  companyId: string
  supplierId: string
  supplierName: string
  rawMaterialId?: string
  materialName?: string
  materialSku?: string
  leadTimeDays: number
  minimumOrderQty: number
  maximumOrderQty?: number
  unitCost?: number
  currency: string
  reliability: "excellent" | "good" | "fair" | "poor"
  lastUpdated: Date
  performanceMetrics: LeadTimePerformanceMetrics
  notes?: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export interface LeadTimePerformanceMetrics {
  averageActualLeadTime: number
  onTimeDeliveryRate: number
  totalOrders: number
  lastOrderDate?: string
  priceConsistency: number
  qualityScore: number
  overallScore: number
}

export interface LeadTimeUpdate {
  actualLeadTimeDays: number
  orderDate: string
  deliveryDate: string
  orderQty: number
  unitCost?: number
  qualityRating?: number
  notes?: string
}

export interface SupplierPerformanceReport {
  supplierId: string
  supplierName: string
  totalMaterials: number
  averageLeadTime: number
  onTimeDeliveryRate: number
  priceStability: number
  qualityScore: number
  overallRating: "excellent" | "good" | "fair" | "poor"
  recommendations: string[]
  riskFactors: string[]
}

// ✅ PROFESSIONAL: Zod validation schemas
export const createSupplierLeadTimeSchema = z.object({
  supplierId: z.string().min(1, "Supplier ID is required"),
  rawMaterialId: z.string().optional(),
  leadTimeDays: z.number().min(1, "Lead time must be at least 1 day"),
  minimumOrderQty: z.number().min(0, "Minimum order quantity cannot be negative"),
  maximumOrderQty: z.number().optional(),
  unitCost: z.number().optional(),
  currency: z.string().default("USD"),
  notes: z.string().optional(),
})

export const updateSupplierLeadTimeSchema = z.object({
  leadTimeDays: z.number().min(1).optional(),
  minimumOrderQty: z.number().min(0).optional(),
  maximumOrderQty: z.number().optional(),
  unitCost: z.number().optional(),
  currency: z.string().optional(),
  notes: z.string().optional(),
})

export const leadTimeUpdateSchema = z.object({
  actualLeadTimeDays: z.number().min(1, "Actual lead time must be at least 1 day"),
  orderDate: z.string().min(1, "Order date is required"),
  deliveryDate: z.string().min(1, "Delivery date is required"),
  orderQty: z.number().min(0.01, "Order quantity must be positive"),
  unitCost: z.number().optional(),
  qualityRating: z.number().min(1).max(5).optional(),
  notes: z.string().optional(),
})

/**
 * ✅ PROFESSIONAL: Supplier Lead Time Management Service
 * Enterprise-grade lead time tracking with performance analytics
 */
export class SupplierLeadTimeService {

  /**
   * Create or update supplier lead time record
   */
  async createSupplierLeadTime(
    companyId: string,
    data: z.infer<typeof createSupplierLeadTimeSchema>,
    userId: string
  ): Promise<SupplierLeadTime> {
    try {
      // Validate input data
      const validatedData = createSupplierLeadTimeSchema.parse(data)

      // Check if record already exists
      const existingRecord = await db.query.supplierLeadTimes.findFirst({
        where: and(
          eq(supplierLeadTimes.company_id, companyId),
          eq(supplierLeadTimes.supplier_id, validatedData.supplierId),
          validatedData.rawMaterialId
            ? eq(supplierLeadTimes.raw_material_id, validatedData.rawMaterialId)
            : isNull(supplierLeadTimes.raw_material_id)
        ),
      })

      if (existingRecord) {
        throw new Error("Lead time record already exists for this supplier-material combination")
      }

      // Create new lead time record
      const leadTimeData = {
        id: uid(),
        company_id: companyId,
        supplier_id: validatedData.supplierId,
        raw_material_id: validatedData.rawMaterialId,
        lead_time_days: validatedData.leadTimeDays.toString(),
        minimum_order_qty: validatedData.minimumOrderQty.toString(),
        maximum_order_qty: validatedData.maximumOrderQty?.toString(),
        unit_cost: validatedData.unitCost?.toString(),
        currency: validatedData.currency,
        reliability: "good", // Default reliability
        performance_metrics: JSON.stringify({
          averageActualLeadTime: validatedData.leadTimeDays,
          onTimeDeliveryRate: 1.0,
          totalOrders: 0,
          priceConsistency: 1.0,
          qualityScore: 4.0,
          overallScore: 0.8,
        }),
        notes: validatedData.notes,
        created_by: userId,
      }

      await db.insert(supplierLeadTimes).values(leadTimeData)

      // Return formatted lead time record
      return await this.getSupplierLeadTimeById(companyId, leadTimeData.id)
    } catch (error) {
      console.error("Error creating supplier lead time:", error)
      throw new Error(`Failed to create supplier lead time: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Update lead time performance based on actual delivery
   */
  async updateLeadTimePerformance(
    companyId: string,
    leadTimeId: string,
    updateData: z.infer<typeof leadTimeUpdateSchema>,
    userId: string
  ): Promise<SupplierLeadTime> {
    try {
      // Validate input data
      const validatedData = leadTimeUpdateSchema.parse(updateData)

      // Get existing lead time record
      const existingRecord = await this.getSupplierLeadTimeById(companyId, leadTimeId)
      if (!existingRecord) {
        throw new Error("Lead time record not found")
      }

      // Calculate performance metrics
      const currentMetrics = existingRecord.performanceMetrics
      const newTotalOrders = currentMetrics.totalOrders + 1

      // Update average actual lead time
      const newAverageLeadTime = (
        (currentMetrics.averageActualLeadTime * currentMetrics.totalOrders) +
        validatedData.actualLeadTimeDays
      ) / newTotalOrders

      // Calculate on-time delivery (within 10% of promised lead time)
      const isOnTime = validatedData.actualLeadTimeDays <= existingRecord.leadTimeDays * 1.1
      const newOnTimeRate = (
        (currentMetrics.onTimeDeliveryRate * currentMetrics.totalOrders) +
        (isOnTime ? 1 : 0)
      ) / newTotalOrders

      // Update quality score if provided
      const newQualityScore = validatedData.qualityRating
        ? ((currentMetrics.qualityScore * currentMetrics.totalOrders) + validatedData.qualityRating) / newTotalOrders
        : currentMetrics.qualityScore

      // Update price consistency if cost provided
      let newPriceConsistency = currentMetrics.priceConsistency
      if (validatedData.unitCost && existingRecord.unitCost) {
        const priceVariation = Math.abs(validatedData.unitCost - existingRecord.unitCost) / existingRecord.unitCost
        newPriceConsistency = Math.max(0, 1 - priceVariation)
      }

      // Calculate overall score
      const newOverallScore = (
        (newOnTimeRate * 0.4) +
        (newQualityScore / 5 * 0.3) +
        (newPriceConsistency * 0.2) +
        (Math.max(0, 1 - (newAverageLeadTime / existingRecord.leadTimeDays - 1)) * 0.1)
      )

      // Determine reliability rating
      let reliability: "excellent" | "good" | "fair" | "poor"
      if (newOverallScore >= 0.9) reliability = "excellent"
      else if (newOverallScore >= 0.7) reliability = "good"
      else if (newOverallScore >= 0.5) reliability = "fair"
      else reliability = "poor"

      // Update performance metrics
      const updatedMetrics: LeadTimePerformanceMetrics = {
        averageActualLeadTime: newAverageLeadTime,
        onTimeDeliveryRate: newOnTimeRate,
        totalOrders: newTotalOrders,
        lastOrderDate: validatedData.deliveryDate,
        priceConsistency: newPriceConsistency,
        qualityScore: newQualityScore,
        overallScore: newOverallScore,
      }

      // Update database record
      await db.update(supplierLeadTimes)
        .set({
          reliability,
          performance_metrics: JSON.stringify(updatedMetrics),
          unit_cost: validatedData.unitCost?.toString() || existingRecord.unitCost?.toString(),
          updated_at: new Date(),
        })
        .where(and(
          eq(supplierLeadTimes.id, leadTimeId),
          eq(supplierLeadTimes.company_id, companyId)
        ))

      // Return updated record
      return await this.getSupplierLeadTimeById(companyId, leadTimeId)
    } catch (error) {
      console.error("Error updating lead time performance:", error)
      throw new Error(`Failed to update lead time performance: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get supplier lead time by ID with full details
   */
  async getSupplierLeadTimeById(
    companyId: string,
    leadTimeId: string
  ): Promise<SupplierLeadTime> {
    try {
      const leadTime = await db.query.supplierLeadTimes.findFirst({
        where: and(
          eq(supplierLeadTimes.id, leadTimeId),
          eq(supplierLeadTimes.company_id, companyId)
        ),
        with: {
          supplier: true,
          rawMaterial: true,
        },
      })

      if (!leadTime) {
        throw new Error("Supplier lead time not found")
      }

      const performanceMetrics = leadTime.performance_metrics
        ? JSON.parse(leadTime.performance_metrics)
        : {
          averageActualLeadTime: parseFloat(leadTime.lead_time_days),
          onTimeDeliveryRate: 1.0,
          totalOrders: 0,
          priceConsistency: 1.0,
          qualityScore: 4.0,
          overallScore: 0.8,
        }

      return {
        id: leadTime.id,
        companyId: leadTime.company_id,
        supplierId: leadTime.supplier_id,
        supplierName: leadTime.supplier.name,
        rawMaterialId: leadTime.raw_material_id || undefined,
        materialName: leadTime.rawMaterial?.name || undefined,
        materialSku: leadTime.rawMaterial?.sku || undefined,
        leadTimeDays: parseInt(leadTime.lead_time_days),
        minimumOrderQty: parseFloat(leadTime.minimum_order_qty || "0"),
        maximumOrderQty: leadTime.maximum_order_qty ? parseFloat(leadTime.maximum_order_qty) : undefined,
        unitCost: leadTime.unit_cost ? parseFloat(leadTime.unit_cost) : undefined,
        currency: leadTime.currency || "USD",
        reliability: leadTime.reliability as "excellent" | "good" | "fair" | "poor",
        lastUpdated: leadTime.updated_at!,
        performanceMetrics,
        notes: leadTime.notes || undefined,
        createdBy: leadTime.created_by,
        createdAt: leadTime.created_at!,
        updatedAt: leadTime.updated_at!,
      }
    } catch (error) {
      console.error("Error getting supplier lead time:", error)
      throw new Error("Failed to get supplier lead time")
    }
  }

  /**
   * List supplier lead times with filtering
   */
  async listSupplierLeadTimes(
    companyId: string,
    filters?: {
      supplierId?: string
      rawMaterialId?: string
      reliability?: string
      minLeadTime?: number
      maxLeadTime?: number
    }
  ): Promise<SupplierLeadTime[]> {
    try {
      const conditions = [eq(supplierLeadTimes.company_id, companyId)]

      if (filters?.supplierId) {
        conditions.push(eq(supplierLeadTimes.supplier_id, filters.supplierId))
      }
      if (filters?.rawMaterialId) {
        conditions.push(eq(supplierLeadTimes.raw_material_id, filters.rawMaterialId))
      }
      if (filters?.reliability) {
        conditions.push(eq(supplierLeadTimes.reliability, filters.reliability))
      }
      if (filters?.minLeadTime) {
        conditions.push(gte(supplierLeadTimes.lead_time_days, filters.minLeadTime.toString()))
      }
      if (filters?.maxLeadTime) {
        conditions.push(lte(supplierLeadTimes.lead_time_days, filters.maxLeadTime.toString()))
      }

      const leadTimes = await db.query.supplierLeadTimes.findMany({
        where: and(...conditions),
        with: {
          supplier: true,
          rawMaterial: true,
        },
        orderBy: [asc(supplierLeadTimes.lead_time_days), descOrder(supplierLeadTimes.reliability)],
      })

      return leadTimes.map(leadTime => {
        const performanceMetrics = leadTime.performance_metrics
          ? JSON.parse(leadTime.performance_metrics)
          : {
            averageActualLeadTime: parseFloat(leadTime.lead_time_days),
            onTimeDeliveryRate: 1.0,
            totalOrders: 0,
            priceConsistency: 1.0,
            qualityScore: 4.0,
            overallScore: 0.8,
          }

        return {
          id: leadTime.id,
          companyId: leadTime.company_id,
          supplierId: leadTime.supplier_id,
          supplierName: leadTime.supplier.name,
          rawMaterialId: leadTime.raw_material_id || undefined,
          materialName: leadTime.rawMaterial?.name || undefined,
          materialSku: leadTime.rawMaterial?.sku || undefined,
          leadTimeDays: parseInt(leadTime.lead_time_days),
          minimumOrderQty: parseFloat(leadTime.minimum_order_qty || "0"),
          maximumOrderQty: leadTime.maximum_order_qty ? parseFloat(leadTime.maximum_order_qty) : undefined,
          unitCost: leadTime.unit_cost ? parseFloat(leadTime.unit_cost) : undefined,
          currency: leadTime.currency || "USD",
          reliability: leadTime.reliability as "excellent" | "good" | "fair" | "poor",
          lastUpdated: leadTime.updated_at!,
          performanceMetrics,
          notes: leadTime.notes || undefined,
          createdBy: leadTime.created_by,
          createdAt: leadTime.created_at!,
          updatedAt: leadTime.updated_at!,
        }
      })
    } catch (error) {
      console.error("Error listing supplier lead times:", error)
      throw new Error("Failed to list supplier lead times")
    }
  }

  /**
   * Generate supplier performance report
   */
  async generateSupplierPerformanceReport(
    companyId: string,
    supplierId?: string
  ): Promise<SupplierPerformanceReport[]> {
    try {
      const conditions = [eq(supplierLeadTimes.company_id, companyId)]
      if (supplierId) {
        conditions.push(eq(supplierLeadTimes.supplier_id, supplierId))
      }

      const leadTimes = await db.query.supplierLeadTimes.findMany({
        where: and(...conditions),
        with: {
          supplier: true,
        },
      })

      // Group by supplier
      const supplierGroups = new Map<string, typeof leadTimes>()
      for (const leadTime of leadTimes) {
        const supplierId = leadTime.supplier_id
        if (!supplierGroups.has(supplierId)) {
          supplierGroups.set(supplierId, [])
        }
        supplierGroups.get(supplierId)!.push(leadTime)
      }

      const reports: SupplierPerformanceReport[] = []

      for (const [supplierId, supplierLeadTimes] of supplierGroups) {
        const supplier = supplierLeadTimes[0].supplier

        // Calculate aggregate metrics
        let totalLeadTime = 0
        let totalOnTimeRate = 0
        let totalPriceStability = 0
        let totalQualityScore = 0
        let totalOrders = 0

        for (const leadTime of supplierLeadTimes) {
          const metrics = leadTime.performance_metrics
            ? JSON.parse(leadTime.performance_metrics)
            : {
              averageActualLeadTime: parseFloat(leadTime.lead_time_days),
              onTimeDeliveryRate: 1.0,
              totalOrders: 0,
              priceConsistency: 1.0,
              qualityScore: 4.0,
              overallScore: 0.8,
            }

          totalLeadTime += metrics.averageActualLeadTime * metrics.totalOrders
          totalOnTimeRate += metrics.onTimeDeliveryRate * metrics.totalOrders
          totalPriceStability += metrics.priceConsistency * metrics.totalOrders
          totalQualityScore += metrics.qualityScore * metrics.totalOrders
          totalOrders += metrics.totalOrders
        }

        // Calculate weighted averages
        const averageLeadTime = totalOrders > 0 ? totalLeadTime / totalOrders : 0
        const onTimeDeliveryRate = totalOrders > 0 ? totalOnTimeRate / totalOrders : 1.0
        const priceStability = totalOrders > 0 ? totalPriceStability / totalOrders : 1.0
        const qualityScore = totalOrders > 0 ? totalQualityScore / totalOrders : 4.0

        // Calculate overall rating
        const overallScore = (
          (onTimeDeliveryRate * 0.4) +
          (qualityScore / 5 * 0.3) +
          (priceStability * 0.2) +
          (Math.max(0, 1 - (averageLeadTime / 30 - 1)) * 0.1) // Assume 30 days is baseline
        )

        let overallRating: "excellent" | "good" | "fair" | "poor"
        if (overallScore >= 0.9) overallRating = "excellent"
        else if (overallScore >= 0.7) overallRating = "good"
        else if (overallScore >= 0.5) overallRating = "fair"
        else overallRating = "poor"

        // Generate recommendations and risk factors
        const recommendations: string[] = []
        const riskFactors: string[] = []

        if (onTimeDeliveryRate < 0.8) {
          riskFactors.push("Poor on-time delivery performance")
          recommendations.push("Implement delivery performance monitoring")
        }
        if (qualityScore < 3.5) {
          riskFactors.push("Quality issues reported")
          recommendations.push("Conduct quality audit and improvement plan")
        }
        if (priceStability < 0.7) {
          riskFactors.push("Price volatility concerns")
          recommendations.push("Negotiate fixed-price contracts")
        }
        if (averageLeadTime > 45) {
          riskFactors.push("Long lead times impact planning")
          recommendations.push("Explore alternative suppliers or buffer stock")
        }

        if (overallRating === "excellent") {
          recommendations.push("Consider strategic partnership opportunities")
        }
        if (supplierLeadTimes.length === 1) {
          recommendations.push("Expand material sourcing to reduce dependency")
        }

        reports.push({
          supplierId,
          supplierName: supplier.name,
          totalMaterials: supplierLeadTimes.length,
          averageLeadTime,
          onTimeDeliveryRate,
          priceStability,
          qualityScore,
          overallRating,
          recommendations,
          riskFactors,
        })
      }

      // Sort by overall rating and performance
      const ratingOrder = { excellent: 4, good: 3, fair: 2, poor: 1 }
      return reports.sort((a, b) => {
        const ratingDiff = ratingOrder[b.overallRating] - ratingOrder[a.overallRating]
        if (ratingDiff !== 0) return ratingDiff
        return b.onTimeDeliveryRate - a.onTimeDeliveryRate
      })
    } catch (error) {
      console.error("Error generating supplier performance report:", error)
      throw new Error("Failed to generate supplier performance report")
    }
  }

  /**
   * Get best suppliers for a specific material
   */
  async getBestSuppliersForMaterial(
    companyId: string,
    rawMaterialId: string,
    requiredQty?: number
  ): Promise<SupplierLeadTime[]> {
    try {
      const leadTimes = await this.listSupplierLeadTimes(companyId, {
        rawMaterialId,
      })

      // Filter by quantity constraints if provided
      let filteredLeadTimes = leadTimes
      if (requiredQty) {
        filteredLeadTimes = leadTimes.filter(lt => {
          const meetsMinimum = requiredQty >= lt.minimumOrderQty
          const meetsMaximum = !lt.maximumOrderQty || requiredQty <= lt.maximumOrderQty
          return meetsMinimum && meetsMaximum
        })
      }

      // Sort by performance score and reliability
      const reliabilityOrder = { excellent: 4, good: 3, fair: 2, poor: 1 }
      return filteredLeadTimes.sort((a, b) => {
        const reliabilityDiff = reliabilityOrder[b.reliability] - reliabilityOrder[a.reliability]
        if (reliabilityDiff !== 0) return reliabilityDiff

        const scoreDiff = b.performanceMetrics.overallScore - a.performanceMetrics.overallScore
        if (scoreDiff !== 0) return scoreDiff

        return a.leadTimeDays - b.leadTimeDays // Prefer shorter lead times
      })
    } catch (error) {
      console.error("Error getting best suppliers for material:", error)
      throw new Error("Failed to get best suppliers for material")
    }
  }

  /**
   * Update supplier lead time record
   */
  async updateSupplierLeadTime(
    companyId: string,
    leadTimeId: string,
    updateData: z.infer<typeof updateSupplierLeadTimeSchema>,
    userId: string
  ): Promise<SupplierLeadTime> {
    try {
      // Validate input data
      const validatedData = updateSupplierLeadTimeSchema.parse(updateData)

      // Check if record exists
      const existingRecord = await this.getSupplierLeadTimeById(companyId, leadTimeId)
      if (!existingRecord) {
        throw new Error("Lead time record not found")
      }

      // Update database record
      const updateFields: any = {
        updated_at: new Date(),
      }

      if (validatedData.leadTimeDays !== undefined) {
        updateFields.lead_time_days = validatedData.leadTimeDays.toString()
      }
      if (validatedData.minimumOrderQty !== undefined) {
        updateFields.minimum_order_qty = validatedData.minimumOrderQty.toString()
      }
      if (validatedData.maximumOrderQty !== undefined) {
        updateFields.maximum_order_qty = validatedData.maximumOrderQty.toString()
      }
      if (validatedData.unitCost !== undefined) {
        updateFields.unit_cost = validatedData.unitCost.toString()
      }
      if (validatedData.currency !== undefined) {
        updateFields.currency = validatedData.currency
      }
      if (validatedData.notes !== undefined) {
        updateFields.notes = validatedData.notes
      }

      await db.update(supplierLeadTimes)
        .set(updateFields)
        .where(and(
          eq(supplierLeadTimes.id, leadTimeId),
          eq(supplierLeadTimes.company_id, companyId)
        ))

      // Return updated record
      return await this.getSupplierLeadTimeById(companyId, leadTimeId)
    } catch (error) {
      console.error("Error updating supplier lead time:", error)
      throw new Error(`Failed to update supplier lead time: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}
