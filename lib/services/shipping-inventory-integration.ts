/**
 * Manufacturing ERP - Shipping Inventory Integration Service
 * 
 * Professional inventory integration for shipping workflow with outbound
 * transactions, stock lot updates, and location utilization management.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 2 Inventory Integration
 */

import { db } from "@/lib/db"
import {
  shipmentItems,
  stockLots,
  stockTxns,
  shipments
} from "@/lib/schema-postgres"
import { eq, and, sql } from "drizzle-orm"
import { LocationManager } from "@/lib/location-config"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ShipmentItemWithDetails {
  id: string
  shipment_id: string
  product_id: string
  quantity: number
  unit_price: number
  stock_lot_id: string | null
  product: {
    id: string
    name: string
    sku: string
    unit: string
  }
  stock_lot?: {
    id: string
    location: string
    qty: number
    status: string
  }
}

interface InventoryMovement {
  product_id: string
  stock_lot_id: string
  quantity: number
  from_location: string
  to_location: string
  movement_type: 'outbound_shipment'
  reference_id: string
  reference_type: 'shipment'
}

interface LocationUtilizationUpdate {
  location_id: string
  capacity_change: number
  utilization_change: number
  movement_type: 'outbound'
}

// ============================================================================
// SHIPPING INVENTORY INTEGRATION SERVICE
// ============================================================================

export class ShippingInventoryIntegration {

  /**
   * Process inventory movements when shipment is shipped
   */
  async processShipmentInventoryMovements(
    shipmentId: string,
    companyId: string,
    userId?: string
  ): Promise<void> {
    try {
      console.log(`Processing inventory movements for shipment ${shipmentId}`)

      // Get shipment items with product and stock lot details
      const shipmentItemsData = await this.getShipmentItemsWithDetails(shipmentId, companyId)

      if (shipmentItemsData.length === 0) {
        console.log(`No items found for shipment ${shipmentId}`)
        return
      }

      // Process each item
      for (const item of shipmentItemsData) {
        await this.processItemInventoryMovement(item, companyId, userId)
      }

      console.log(`Successfully processed ${shipmentItemsData.length} items for shipment ${shipmentId}`)

    } catch (error) {
      console.error(`Error processing inventory movements for shipment ${shipmentId}:`, error)
      throw error
    }
  }

  /**
   * Reverse inventory movements if shipment is cancelled
   */
  async reverseShipmentInventoryMovements(
    shipmentId: string,
    companyId: string,
    userId?: string
  ): Promise<void> {
    try {
      console.log(`Reversing inventory movements for shipment ${shipmentId}`)

      // Get all outbound transactions for this shipment
      const transactions = await db.query.stockTxns.findMany({
        where: and(
          eq(stockTxns.company_id, companyId),
          eq(stockTxns.reference_id, shipmentId),
          eq(stockTxns.reason_code, 'shipment'),
          eq(stockTxns.transaction_type, 'outbound')
        )
      })

      // Reverse each transaction
      for (const transaction of transactions) {
        await this.reverseStockTransaction(transaction, companyId, userId)
      }

      console.log(`Successfully reversed ${transactions.length} transactions for shipment ${shipmentId}`)

    } catch (error) {
      console.error(`Error reversing inventory movements for shipment ${shipmentId}:`, error)
      throw error
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private async getShipmentItemsWithDetails(
    shipmentId: string,
    companyId: string
  ): Promise<ShipmentItemWithDetails[]> {
    const items = await db.query.shipmentItems.findMany({
      where: and(
        eq(shipmentItems.shipment_id, shipmentId),
        eq(shipmentItems.company_id, companyId)
      ),
      with: {
        product: true,
        stockLot: true
      }
    })

    return items.map(item => ({
      id: item.id,
      shipment_id: item.shipment_id,
      product_id: item.product_id,
      quantity: parseFloat(item.quantity),
      unit_price: parseFloat(item.unit_price || '0'),
      stock_lot_id: item.stock_lot_id,
      product: {
        id: item.product.id,
        name: item.product.name,
        sku: item.product.sku,
        unit: item.product.unit
      },
      stock_lot: item.stockLot ? {
        id: item.stockLot.id,
        location: item.stockLot.location,
        qty: parseFloat(item.stockLot.qty),
        status: item.stockLot.status
      } : undefined
    }))
  }

  private async processItemInventoryMovement(
    item: ShipmentItemWithDetails,
    companyId: string,
    userId?: string
  ): Promise<void> {
    try {
      // 1. Create outbound stock transaction
      await this.createOutboundTransaction(item, companyId, userId)

      // 2. Update stock lot quantities
      if (item.stock_lot_id) {
        await this.updateStockLotQuantity(item.stock_lot_id, item.quantity, companyId)
      }

      // 3. Update location utilization
      if (item.stock_lot?.location) {
        await this.updateLocationUtilization(
          item.stock_lot.location,
          item.quantity,
          'outbound',
          companyId
        )
      }

      // 4. Create audit trail entry
      await this.createInventoryAuditEntry(item, companyId, userId)

    } catch (error) {
      console.error(`Error processing inventory movement for item ${item.id}:`, error)
      throw error
    }
  }

  private async createOutboundTransaction(
    item: ShipmentItemWithDetails,
    companyId: string,
    userId?: string
  ): Promise<void> {
    const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // ✅ COGS FIX: Use product cost_price instead of selling unit_price for accurate COGS
    const costPrice = item.product.cost_price && parseFloat(item.product.cost_price) > 0
      ? parseFloat(item.product.cost_price)
      : item.product.price && parseFloat(item.product.price) > 0
        ? parseFloat(item.product.price)
        : parseFloat(item.unit_price) // Fallback to unit_price if no cost_price available

    const totalCOGS = item.quantity * costPrice

    await db.insert(stockTxns).values({
      id: transactionId,
      company_id: companyId,
      product_id: item.product_id,
      type: 'outbound', // Legacy compatibility field
      transaction_type: 'outbound',
      qty: item.quantity.toString(),
      unit_cost: costPrice.toString(), // ✅ FIXED: Use cost price for COGS
      total_value: totalCOGS.toString(), // ✅ FIXED: Use cost-based total for COGS
      location: item.stock_lot?.location || 'unknown',
      reason_code: 'shipment',
      reference_id: item.shipment_id,
      notes: `Outbound shipment - ${item.product.name} (${item.product.sku}) - COGS: $${costPrice}/unit`,
      created_by: userId || 'system'
    })

    console.log(`✅ Created COGS transaction ${transactionId}: ${item.quantity} × $${costPrice} = $${totalCOGS} for ${item.product.name}`)
  }

  private async updateStockLotQuantity(
    stockLotId: string,
    shippedQuantity: number,
    companyId: string
  ): Promise<void> {
    // Get current stock lot
    const stockLot = await db.query.stockLots.findFirst({
      where: and(
        eq(stockLots.id, stockLotId),
        eq(stockLots.company_id, companyId)
      )
    })

    if (!stockLot) {
      throw new Error(`Stock lot ${stockLotId} not found`)
    }

    const currentQty = parseFloat(stockLot.qty)
    const newQty = Math.max(0, currentQty - shippedQuantity)

    // Update stock lot quantity
    await db.update(stockLots)
      .set({
        qty: newQty.toString(),
        status: newQty === 0 ? 'depleted' : stockLot.status,
        updated_at: new Date()
      })
      .where(and(
        eq(stockLots.id, stockLotId),
        eq(stockLots.company_id, companyId)
      ))

    console.log(`Updated stock lot ${stockLotId}: ${currentQty} → ${newQty}`)
  }

  private async updateLocationUtilization(
    locationId: string,
    quantity: number,
    movementType: 'outbound',
    companyId: string
  ): Promise<void> {
    try {
      // Get location configuration
      const locationConfig = LocationManager.getLocationById(locationId)
      if (!locationConfig) {
        console.warn(`Location ${locationId} not found in configuration`)
        return
      }

      // Calculate utilization change (negative for outbound)
      const utilizationChange = movementType === 'outbound' ? -quantity : quantity

      // In a real system, this would update a location_utilization table
      // For now, we'll log the change
      console.log(`Location ${locationId} utilization change: ${utilizationChange} units`)

      // TODO: Implement actual location utilization table updates
      // await db.update(locationUtilization)
      //   .set({
      //     current_stock: sql`current_stock + ${utilizationChange}`,
      //     updated_at: new Date()
      //   })
      //   .where(and(
      //     eq(locationUtilization.location_id, locationId),
      //     eq(locationUtilization.company_id, companyId)
      //   ))

    } catch (error) {
      console.error(`Error updating location utilization for ${locationId}:`, error)
      // Don't throw - this is not critical for shipment processing
    }
  }

  private async createInventoryAuditEntry(
    item: ShipmentItemWithDetails,
    companyId: string,
    userId?: string
  ): Promise<void> {
    try {
      // In a real system, this would create an audit log entry
      const auditEntry = {
        timestamp: new Date().toISOString(),
        company_id: companyId,
        user_id: userId || 'system',
        action: 'inventory_outbound_shipment',
        entity_type: 'stock_lot',
        entity_id: item.stock_lot_id,
        changes: {
          product_id: item.product_id,
          product_name: item.product.name,
          quantity_shipped: item.quantity,
          unit: item.product.unit,
          shipment_id: item.shipment_id,
          location: item.stock_lot?.location
        }
      }

      console.log('Audit Entry:', JSON.stringify(auditEntry, null, 2))

      // TODO: Implement actual audit log table
      // await db.insert(auditLog).values(auditEntry)

    } catch (error) {
      console.error('Error creating audit entry:', error)
      // Don't throw - this is not critical for shipment processing
    }
  }

  private async reverseStockTransaction(
    transaction: any,
    companyId: string,
    userId?: string
  ): Promise<void> {
    try {
      // Create reversal transaction
      const reversalId = `rev_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      await db.insert(stockTxns).values({
        id: reversalId,
        company_id: companyId,
        product_id: transaction.product_id,
        type: 'inbound', // Legacy compatibility field
        transaction_type: 'inbound',
        qty: transaction.quantity,
        unit_cost: transaction.unit_cost,
        total_value: transaction.total_cost,
        location: transaction.location,
        reason_code: 'adjustment',
        reference_id: transaction.reference_id,
        notes: `Reversal of transaction ${transaction.id} - Shipment cancelled`,
        created_by: userId || 'system'
      })

      // Restore stock lot quantity
      if (transaction.stock_lot_id) {
        const quantity = parseFloat(transaction.quantity)

        await db.update(stockLots)
          .set({
            qty: sql`CAST(qty AS DECIMAL) + ${quantity}`,
            status: 'available', // Restore to available status
            updated_at: new Date()
          })
          .where(and(
            eq(stockLots.id, transaction.stock_lot_id),
            eq(stockLots.company_id, companyId)
          ))
      }

      console.log(`Created reversal transaction ${reversalId} for original transaction ${transaction.id}`)

    } catch (error) {
      console.error(`Error reversing transaction ${transaction.id}:`, error)
      throw error
    }
  }
}
