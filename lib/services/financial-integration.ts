/**
 * Manufacturing ERP - Financial Integration Service
 * Professional contract-to-invoice workflow automation following ERP standards
 */

import { db, uid } from "@/lib/db"
import {
  arInvoices,
  apInvoices
} from "@/lib/schema-postgres"
import { eq, and, desc } from "drizzle-orm"
import { TenantContext } from "@/lib/tenant-utils"
import { BusinessLogicError, ErrorCode } from "@/lib/errors"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface CreateARInvoiceFromContractData {
  contractId: string
  invoiceNumber?: string
  dueDate?: string
  paymentTerms?: string
  notes?: string
  partialAmount?: number // For partial invoicing
}

export interface CreateAPInvoiceFromContractData {
  contractId: string
  invoiceNumber?: string
  dueDate?: string
  paymentTerms?: string
  notes?: string
  partialAmount?: number // For partial invoicing
}

export interface PaymentData {
  invoiceId: string
  amount: number
  paymentDate?: string
  notes?: string
}

export interface FinancialSummary {
  totalAR: number
  totalAP: number
  outstandingAR: number
  outstandingAP: number
  overdueAR: number
  overdueAP: number
}

export interface ManufacturingFinancialKPIs {
  // Core Financial KPIs (Accrual Basis)
  totalRevenue: { mtd: number; ytd: number }
  totalExpenses: { mtd: number; ytd: number }
  profitLoss: { mtd: number; ytd: number }

  // ✅ ENHANCED: Cash Flow KPIs (Cash Basis)
  cashReceived: { mtd: number; ytd: number }
  pendingReceivables: { count: number; value: number }
  netCashFlow: number
  cashFlowTrend: 'positive' | 'negative' | 'stable'

  // ✅ ENHANCED: Collection Performance
  averageCollectionDays: number
  collectionRate: number // Cash received / total revenue
  overdueInvoices: { count: number; value: number }

  // AR/AP Intelligence
  overdueAR: { count: number; value: number }
  overdueAP: { count: number; value: number }

  // Manufacturing-Specific KPIs
  contractProfitability: number
  manufacturingMargin: number

  // Business Performance
  totalContracts: { active: number; completed: number }
  customerPaymentHealth: 'excellent' | 'good' | 'warning' | 'critical'
  supplierPaymentPerformance: 'excellent' | 'good' | 'warning' | 'critical'
}

// ============================================================================
// FINANCIAL INTEGRATION SERVICE
// ============================================================================

export class FinancialIntegrationService {
  constructor(private context: TenantContext) { }

  /**
   * Generate AR Invoice from Sales Contract
   * Professional ERP workflow automation
   */
  async generateARInvoiceFromContract(
    data: CreateARInvoiceFromContractData
  ): Promise<any> {
    // Validate sales contract exists and belongs to company
    const contract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, data.contractId),
        eq(salesContracts.company_id, this.context.companyId)
      ),
      with: {
        customer: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    })

    if (!contract) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        `Sales contract ${data.contractId} not found`
      )
    }

    // ERP Business Rule: Only approved/active contracts can be invoiced
    const invoiceableStatuses = ["approved", "active", "in_production", "shipped"]
    if (!invoiceableStatuses.includes(contract.status)) {
      throw new BusinessLogicError(
        ErrorCode.INVALID_STATE_TRANSITION,
        `Contract must be approved/active to generate invoice. Current status: ${contract.status}`
      )
    }

    // Calculate total amount (partial or full)
    const contractTotal = contract.items.reduce((sum, item) => {
      return sum + (parseFloat(item.price) * parseInt(item.qty))
    }, 0)

    const invoiceAmount = data.partialAmount || contractTotal

    // Generate invoice number if not provided
    const invoiceNumber = data.invoiceNumber || await this.generateInvoiceNumber("AR")

    // Calculate due date (default 30 days)
    const dueDate = data.dueDate || this.calculateDueDate(data.paymentTerms || "Net 30")

    const invoiceId = uid("ari")

    // Create AR invoice
    const newInvoice = {
      id: invoiceId,
      company_id: this.context.companyId,
      number: invoiceNumber,
      customer_id: contract.customer_id,
      sales_contract_id: contract.id,
      contract_reference: contract.number,
      date: new Date().toISOString().split('T')[0],
      due_date: dueDate,
      amount: invoiceAmount.toString(),
      received: "0",
      currency: contract.currency || "USD",
      status: "draft",
      payment_terms: data.paymentTerms || "Net 30",
      notes: data.notes || `Generated from sales contract ${contract.number}`,
    }

    await db.insert(arInvoices).values(newInvoice)

    // Return created invoice with relationships
    const createdInvoice = await db.query.arInvoices.findFirst({
      where: and(
        eq(arInvoices.id, invoiceId),
        eq(arInvoices.company_id, this.context.companyId)
      ),
      with: {
        customer: true,
        salesContract: true,
      },
    })

    return createdInvoice
  }

  /**
   * Generate AP Invoice from Purchase Contract
   */
  async generateAPInvoiceFromContract(
    data: CreateAPInvoiceFromContractData
  ): Promise<any> {
    // Validate purchase contract exists and belongs to company
    const contract = await db.query.purchaseContracts.findFirst({
      where: and(
        eq(purchaseContracts.id, data.contractId),
        eq(purchaseContracts.company_id, this.context.companyId)
      ),
      with: {
        supplier: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    })

    if (!contract) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        `Purchase contract ${data.contractId} not found`
      )
    }

    // Calculate total amount
    const contractTotal = contract.items.reduce((sum, item) => {
      return sum + (parseFloat(item.price) * parseInt(item.qty))
    }, 0)

    const invoiceAmount = data.partialAmount || contractTotal
    const invoiceNumber = data.invoiceNumber || await this.generateInvoiceNumber("AP")
    const dueDate = data.dueDate || this.calculateDueDate(data.paymentTerms || "Net 30")

    const invoiceId = uid("api")

    const newInvoice = {
      id: invoiceId,
      company_id: this.context.companyId,
      number: invoiceNumber,
      supplier_id: contract.supplier_id,
      purchase_contract_id: contract.id,
      contract_reference: contract.number,
      date: new Date().toISOString().split('T')[0],
      due_date: dueDate,
      amount: invoiceAmount.toString(),
      paid: "0",
      currency: contract.currency || "USD",
      status: "draft",
      payment_terms: data.paymentTerms || "Net 30",
      notes: data.notes || `Generated from purchase contract ${contract.number}`,
    }

    await db.insert(apInvoices).values(newInvoice)

    return await db.query.apInvoices.findFirst({
      where: and(
        eq(apInvoices.id, invoiceId),
        eq(apInvoices.company_id, this.context.companyId)
      ),
      with: {
        supplier: true,
        purchaseContract: true,
      },
    })
  }

  /**
   * Record payment for AR invoice
   */
  async recordARPayment(data: PaymentData): Promise<any> {
    const invoice = await db.query.arInvoices.findFirst({
      where: and(
        eq(arInvoices.id, data.invoiceId),
        eq(arInvoices.company_id, this.context.companyId)
      ),
    })

    if (!invoice) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        `AR Invoice ${data.invoiceId} not found`
      )
    }

    const currentReceived = parseFloat(invoice.received || "0")
    const newReceived = currentReceived + data.amount
    const totalAmount = parseFloat(invoice.amount)

    // Determine new status
    let newStatus = invoice.status
    if (newReceived >= totalAmount) {
      newStatus = "paid"
    } else if (newReceived > 0) {
      newStatus = "partial"
    }

    await db.update(arInvoices)
      .set({
        received: newReceived.toString(),
        status: newStatus,
        updated_at: new Date(),
      })
      .where(and(
        eq(arInvoices.id, data.invoiceId),
        eq(arInvoices.company_id, this.context.companyId)
      ))

    return { success: true, newReceived, newStatus }
  }

  /**
   * Record payment for AP invoice
   */
  async recordAPPayment(data: PaymentData): Promise<any> {
    const invoice = await db.query.apInvoices.findFirst({
      where: and(
        eq(apInvoices.id, data.invoiceId),
        eq(apInvoices.company_id, this.context.companyId)
      ),
    })

    if (!invoice) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        `AP Invoice ${data.invoiceId} not found`
      )
    }

    const currentPaid = parseFloat(invoice.paid || "0")
    const newPaid = currentPaid + data.amount
    const totalAmount = parseFloat(invoice.amount)

    // Determine new status
    let newStatus = invoice.status
    if (newPaid >= totalAmount) {
      newStatus = "paid"
    } else if (newPaid > 0) {
      newStatus = "partial"
    }

    await db.update(apInvoices)
      .set({
        paid: newPaid.toString(),
        status: newStatus,
        updated_at: new Date(),
      })
      .where(and(
        eq(apInvoices.id, data.invoiceId),
        eq(apInvoices.company_id, this.context.companyId)
      ))

    return { success: true, newPaid, newStatus }
  }

  /**
   * Get AP payment history (placeholder - would integrate with payment tracking system)
   */
  async getAPPaymentHistory(invoiceId: string): Promise<any[]> {
    // In a real implementation, this would fetch from a payments table
    // For now, return basic payment info from the invoice
    const invoice = await db.query.apInvoices.findFirst({
      where: and(
        eq(apInvoices.id, invoiceId),
        eq(apInvoices.company_id, this.context.companyId)
      ),
    })

    if (!invoice) {
      return []
    }

    const paidAmount = parseFloat(invoice.paid || '0')
    if (paidAmount > 0) {
      return [{
        id: `payment_${invoice.id}`,
        amount: paidAmount,
        date: invoice.updated_at || invoice.created_at,
        method: 'Manual Entry',
        notes: 'Payment recorded in system',
      }]
    }

    return []
  }

  /**
   * Get invoice details with payment information
   */
  async getInvoiceDetails(invoiceId: string): Promise<any> {
    return await db.query.arInvoices.findFirst({
      where: and(
        eq(arInvoices.id, invoiceId),
        eq(arInvoices.company_id, this.context.companyId)
      ),
      with: {
        customer: true,
        salesContract: true,
      },
    })
  }

  /**
   * Preview invoice generation from contract
   */
  async previewInvoiceFromContract(
    contractId: string,
    options: { partialAmount?: number; paymentTerms?: string } = {}
  ): Promise<any> {
    const contract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, contractId),
        eq(salesContracts.company_id, this.context.companyId)
      ),
      with: {
        customer: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    })

    if (!contract) {
      return {
        status: "error",
        reason: "Contract not found",
      }
    }

    const invoiceableStatuses = ["approved", "active", "in_production", "shipped"]
    if (!invoiceableStatuses.includes(contract.status)) {
      return {
        status: "error",
        reason: `Contract status '${contract.status}' is not invoiceable`,
      }
    }

    const contractTotal = contract.items.reduce((sum, item) => {
      return sum + (parseFloat(item.price) * parseInt(item.qty))
    }, 0)

    const invoiceAmount = options.partialAmount || contractTotal
    const dueDate = this.calculateDueDate(options.paymentTerms || "Net 30")

    return {
      status: "ready",
      contractNumber: contract.number,
      customerName: contract.customer.name,
      contractTotal,
      invoiceAmount,
      currency: contract.currency || "USD",
      dueDate,
      paymentTerms: options.paymentTerms || "Net 30",
      itemCount: contract.items.length,
    }
  }

  /**
   * Get comprehensive manufacturing financial KPIs
   * Professional ERP business intelligence dashboard
   */
  async getManufacturingFinancialKPIs(): Promise<ManufacturingFinancialKPIs> {
    const today = new Date()
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
    const startOfYear = new Date(today.getFullYear(), 0, 1)
    const todayStr = today.toISOString().split('T')[0]
    const monthStr = startOfMonth.toISOString().split('T')[0]
    const yearStr = startOfYear.toISOString().split('T')[0]

    // Fetch financial data first
    const [arData, apData] = await Promise.all([
      db.query.arInvoices.findMany({
        where: eq(arInvoices.company_id, this.context.companyId),
        with: { customer: true, salesContract: true },
      }),
      db.query.apInvoices.findMany({
        where: eq(apInvoices.company_id, this.context.companyId),
        with: { supplier: true, purchaseContract: true },
      }),
    ])

    // Fetch contract data separately to avoid circular dependency
    const [salesContractsData, purchaseContractsData] = await Promise.all([
      db.query.salesContracts.findMany({
        where: (salesContracts, { eq }) => eq(salesContracts.company_id, this.context.companyId),
        with: { customer: true, items: true },
      }),
      db.query.purchaseContracts.findMany({
        where: (purchaseContracts, { eq }) => eq(purchaseContracts.company_id, this.context.companyId),
        with: { supplier: true, items: true },
      }),
    ])

    // Calculate Revenue (MTD/YTD) from AR invoices
    const totalRevenue = {
      mtd: arData
        .filter(inv => inv.date >= monthStr)
        .reduce((sum, inv) => sum + parseFloat(inv.amount), 0),
      ytd: arData
        .filter(inv => inv.date >= yearStr)
        .reduce((sum, inv) => sum + parseFloat(inv.amount), 0),
    }

    // Calculate Expenses (MTD/YTD) from AP invoices
    const totalExpenses = {
      mtd: apData
        .filter(inv => inv.date >= monthStr)
        .reduce((sum, inv) => sum + parseFloat(inv.amount), 0),
      ytd: apData
        .filter(inv => inv.date >= yearStr)
        .reduce((sum, inv) => sum + parseFloat(inv.amount), 0),
    }

    // Calculate Profit/Loss
    const profitLoss = {
      mtd: totalRevenue.mtd - totalExpenses.mtd,
      ytd: totalRevenue.ytd - totalExpenses.ytd,
    }

    // Calculate Overdue AR/AP with intelligence
    const overdueAR = arData.reduce(
      (acc, inv) => {
        if (inv.due_date && inv.due_date < todayStr && inv.status !== 'paid') {
          const outstanding = parseFloat(inv.amount) - parseFloat(inv.received || '0')
          if (outstanding > 0) {
            acc.count++
            acc.value += outstanding
          }
        }
        return acc
      },
      { count: 0, value: 0 }
    )

    const overdueAP = apData.reduce(
      (acc, inv) => {
        if (inv.due_date && inv.due_date < todayStr && inv.status !== 'paid') {
          const outstanding = parseFloat(inv.amount) - parseFloat(inv.paid || '0')
          if (outstanding > 0) {
            acc.count++
            acc.value += outstanding
          }
        }
        return acc
      },
      { count: 0, value: 0 }
    )

    // Calculate Manufacturing-Specific KPIs
    const contractProfitability = this.calculateContractProfitability(salesContractsData, purchaseContractsData)
    const averageCollectionDays = this.calculateAverageCollectionDays(arData)
    const manufacturingMargin = totalRevenue.ytd > 0 ? (profitLoss.ytd / totalRevenue.ytd) * 100 : 0

    // Calculate Cash Flow Intelligence
    const totalARReceived = arData.reduce((sum, inv) => sum + parseFloat(inv.received || '0'), 0)
    const totalAPPaid = apData.reduce((sum, inv) => sum + parseFloat(inv.paid || '0'), 0)
    const netCashFlow = totalARReceived - totalAPPaid

    const cashFlowTrend = this.determineCashFlowTrend(arData, apData, monthStr)

    // ✅ ENHANCED: Calculate Cash Flow KPIs
    const cashReceived = {
      mtd: arData.filter(inv => inv.date && inv.date >= monthStr)
        .reduce((sum, inv) => sum + parseFloat(inv.received || '0'), 0),
      ytd: totalARReceived
    }

    const pendingReceivables = {
      count: arData.filter(inv => parseFloat(inv.received || '0') < parseFloat(inv.amount || '0')).length,
      value: arData.reduce((sum, inv) => {
        const amount = parseFloat(inv.amount || '0')
        const received = parseFloat(inv.received || '0')
        return sum + Math.max(0, amount - received)
      }, 0)
    }

    const collectionRate = totalRevenue.ytd > 0 ? (totalARReceived / totalRevenue.ytd) * 100 : 0

    const overdueInvoices = {
      count: arData.filter(inv => {
        if (!inv.due_date) return false
        const dueDate = new Date(inv.due_date)
        return dueDate < today && parseFloat(inv.received || '0') < parseFloat(inv.amount || '0')
      }).length,
      value: arData.filter(inv => {
        if (!inv.due_date) return false
        const dueDate = new Date(inv.due_date)
        return dueDate < today && parseFloat(inv.received || '0') < parseFloat(inv.amount || '0')
      }).reduce((sum, inv) => {
        const amount = parseFloat(inv.amount || '0')
        const received = parseFloat(inv.received || '0')
        return sum + Math.max(0, amount - received)
      }, 0)
    }

    // Calculate Business Performance Metrics
    const totalContracts = {
      active: salesContractsData.filter(c => ['approved', 'active', 'in_production'].includes(c.status)).length +
        purchaseContractsData.filter(c => ['approved', 'active', 'in_production'].includes(c.status)).length,
      completed: salesContractsData.filter(c => c.status === 'completed').length +
        purchaseContractsData.filter(c => c.status === 'completed').length,
    }

    const customerPaymentHealth = this.assessCustomerPaymentHealth(overdueAR, totalRevenue.ytd)
    const supplierPaymentPerformance = this.assessSupplierPaymentPerformance(overdueAP, totalExpenses.ytd)

    return {
      // Core Financial KPIs (Accrual Basis)
      totalRevenue,
      totalExpenses,
      profitLoss,

      // ✅ ENHANCED: Cash Flow KPIs (Cash Basis)
      cashReceived,
      pendingReceivables,
      netCashFlow,
      cashFlowTrend,

      // ✅ ENHANCED: Collection Performance
      averageCollectionDays,
      collectionRate,
      overdueInvoices,

      // AR/AP Intelligence
      overdueAR,
      overdueAP,

      // Manufacturing-Specific KPIs
      contractProfitability,
      manufacturingMargin,

      // Business Performance
      totalContracts,
      customerPaymentHealth,
      supplierPaymentPerformance,
    }
  }

  /**
   * Get financial summary for dashboard (legacy support)
   */
  async getFinancialSummary(): Promise<FinancialSummary> {
    const [arData, apData] = await Promise.all([
      db.query.arInvoices.findMany({
        where: eq(arInvoices.company_id, this.context.companyId),
      }),
      db.query.apInvoices.findMany({
        where: eq(apInvoices.company_id, this.context.companyId),
      }),
    ])

    const today = new Date().toISOString().split('T')[0]

    const summary: FinancialSummary = {
      totalAR: arData.reduce((sum, inv) => sum + parseFloat(inv.amount), 0),
      totalAP: apData.reduce((sum, inv) => sum + parseFloat(inv.amount), 0),
      outstandingAR: arData.reduce((sum, inv) => {
        const outstanding = parseFloat(inv.amount) - parseFloat(inv.received || "0")
        return sum + Math.max(0, outstanding)
      }, 0),
      outstandingAP: apData.reduce((sum, inv) => {
        const outstanding = parseFloat(inv.amount) - parseFloat(inv.paid || "0")
        return sum + Math.max(0, outstanding)
      }, 0),
      overdueAR: arData.reduce((sum, inv) => {
        if (inv.due_date && inv.due_date < today && inv.status !== "paid") {
          const outstanding = parseFloat(inv.amount) - parseFloat(inv.received || "0")
          return sum + Math.max(0, outstanding)
        }
        return sum
      }, 0),
      overdueAP: apData.reduce((sum, inv) => {
        if (inv.due_date && inv.due_date < today && inv.status !== "paid") {
          const outstanding = parseFloat(inv.amount) - parseFloat(inv.paid || "0")
          return sum + Math.max(0, outstanding)
        }
        return sum
      }, 0),
    }

    return summary
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private async generateInvoiceNumber(type: "AR" | "AP"): Promise<string> {
    const prefix = type === "AR" ? "INV" : "BILL"
    const year = new Date().getFullYear()
    const month = String(new Date().getMonth() + 1).padStart(2, '0')

    // Get count of invoices this month for sequence
    const startOfMonth = `${year}-${month}-01`
    const endOfMonth = `${year}-${month}-31`

    const table = type === "AR" ? arInvoices : apInvoices
    const count = await db.select().from(table).where(
      and(
        eq(table.company_id, this.context.companyId),
        // Add date range filter here if needed
      )
    )

    const sequence = String(count.length + 1).padStart(4, '0')
    return `${prefix}-${year}${month}-${sequence}`
  }

  private calculateDueDate(paymentTerms: string): string {
    const today = new Date()
    let daysToAdd = 30 // Default

    // Parse payment terms
    const match = paymentTerms.match(/Net (\d+)/)
    if (match) {
      daysToAdd = parseInt(match[1])
    }

    const dueDate = new Date(today.getTime() + daysToAdd * 24 * 60 * 60 * 1000)
    return dueDate.toISOString().split('T')[0]
  }

  // ============================================================================
  // MANUFACTURING ERP KPI CALCULATION METHODS
  // ============================================================================

  private calculateContractProfitability(salesContracts: any[], purchaseContracts: any[]): number {
    // ✅ FIXED: Include approved/active contracts for meaningful profitability metrics
    const activeStatuses = ['approved', 'active', 'in_production', 'completed']

    const totalSalesValue = salesContracts
      .filter(c => activeStatuses.includes(c.status))
      .reduce((sum, contract) => {
        return sum + contract.items.reduce((itemSum: number, item: any) => {
          return itemSum + (parseFloat(item.price) * parseInt(item.qty))
        }, 0)
      }, 0)

    const totalPurchaseValue = purchaseContracts
      .filter(c => activeStatuses.includes(c.status))
      .reduce((sum, contract) => {
        return sum + contract.items.reduce((itemSum: number, item: any) => {
          return itemSum + (parseFloat(item.price) * parseInt(item.qty))
        }, 0)
      }, 0)

    return totalSalesValue > 0 ? ((totalSalesValue - totalPurchaseValue) / totalSalesValue) * 100 : 0
  }

  private calculateAverageCollectionDays(arData: any[]): number {
    const paidInvoices = arData.filter(inv => inv.status === 'paid' && inv.date && inv.updated_at)

    if (paidInvoices.length === 0) return 0

    const totalDays = paidInvoices.reduce((sum, inv) => {
      const invoiceDate = new Date(inv.date)
      const paidDate = new Date(inv.updated_at)
      const daysDiff = Math.floor((paidDate.getTime() - invoiceDate.getTime()) / (1000 * 60 * 60 * 24))
      return sum + Math.max(0, daysDiff)
    }, 0)

    return Math.round(totalDays / paidInvoices.length)
  }

  private determineCashFlowTrend(arData: any[], apData: any[], monthStr: string): 'positive' | 'negative' | 'stable' {
    const thisMonthAR = arData
      .filter(inv => inv.date >= monthStr)
      .reduce((sum, inv) => sum + parseFloat(inv.received || '0'), 0)

    const thisMonthAP = apData
      .filter(inv => inv.date >= monthStr)
      .reduce((sum, inv) => sum + parseFloat(inv.paid || '0'), 0)

    const netFlow = thisMonthAR - thisMonthAP

    if (netFlow > 1000) return 'positive'
    if (netFlow < -1000) return 'negative'
    return 'stable'
  }

  private assessCustomerPaymentHealth(overdueAR: { count: number; value: number }, totalRevenue: number): 'excellent' | 'good' | 'warning' | 'critical' {
    if (totalRevenue === 0) return 'excellent'

    const overdueRatio = overdueAR.value / totalRevenue

    if (overdueRatio < 0.05) return 'excellent'  // Less than 5% overdue
    if (overdueRatio < 0.15) return 'good'       // Less than 15% overdue
    if (overdueRatio < 0.30) return 'warning'    // Less than 30% overdue
    return 'critical'                            // More than 30% overdue
  }

  private assessSupplierPaymentPerformance(overdueAP: { count: number; value: number }, totalExpenses: number): 'excellent' | 'good' | 'warning' | 'critical' {
    if (totalExpenses === 0) return 'excellent'

    const overdueRatio = overdueAP.value / totalExpenses

    if (overdueRatio < 0.05) return 'excellent'  // Less than 5% overdue
    if (overdueRatio < 0.15) return 'good'       // Less than 15% overdue
    if (overdueRatio < 0.30) return 'warning'    // Less than 30% overdue
    return 'critical'                            // More than 30% overdue
  }
}

export const createFinancialService = (context: TenantContext) =>
  new FinancialIntegrationService(context)
