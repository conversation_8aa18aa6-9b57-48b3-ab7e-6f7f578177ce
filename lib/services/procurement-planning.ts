/**
 * Manufacturing ERP - Procurement Planning Service
 * 
 * Professional service for automated procurement planning with supplier selection,
 * container optimization, and purchase recommendations for export manufacturing
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { db, uid } from "@/lib/db"
import {
  procurementPlans,
  supplierLeadTimes,
  suppliers,
  rawMaterials,
  demandForecasts,
  purchaseContracts,
  purchaseContractItems,
  companies
} from "@/lib/schema-postgres"
import { eq, and, asc, desc as descOrder, sql, gte, lte, isNull, or, lt } from "drizzle-orm"
import { z } from "zod"
import { DemandForecastingService } from "./demand-forecasting"

// ✅ PROFESSIONAL: Type definitions for enterprise-grade procurement planning
export interface ProcurementPlanContext {
  companyId: string
  userId: string
  demandForecastId?: string
  targetDate?: string
  priority: "low" | "normal" | "high" | "urgent"
}

export interface ProcurementPlan {
  id: string
  companyId: string
  rawMaterialId: string
  materialName: string
  materialSku: string
  demandForecastId?: string
  plannedQty: number
  targetDate: string
  supplierId?: string
  supplierName?: string
  estimatedCost: number
  estimatedLeadTime: number
  containerOptimization: ContainerOptimization
  priority: "low" | "normal" | "high" | "urgent"
  status: "draft" | "pending" | "approved" | "ordered" | "received"
  notes?: string
  createdBy: string
  approvedBy?: string
  createdAt: Date
  updatedAt: Date
}

export interface SupplierRecommendation {
  supplierId: string
  supplierName: string
  leadTimeDays: number
  estimatedCost: number
  minimumOrderQty: number
  maximumOrderQty?: number
  reliabilityScore: number
  costEfficiencyScore: number
  overallScore: number
  reasons: string[]
}

export interface ContainerOptimization {
  containerType: "20ft" | "40ft" | "40ft-hc"
  maxWeight: number // kg
  maxVolume: number // m³
  estimatedWeight: number
  estimatedVolume: number
  utilizationRate: number
  costPerUnit: number
  recommendedQty: number
  multipleContainers?: boolean
}

export interface PurchaseRecommendation {
  rawMaterialId: string
  materialName: string
  recommendedQty: number
  recommendedSupplier: SupplierRecommendation
  targetDate: string
  estimatedTotalCost: number
  containerOptimization: ContainerOptimization
  urgencyLevel: "low" | "normal" | "high" | "critical"
  businessJustification: string[]
}

// ✅ PROFESSIONAL: Zod validation schemas
export const createProcurementPlanSchema = z.object({
  rawMaterialId: z.string().min(1, "Raw material ID is required"),
  demandForecastId: z.string().optional(),
  plannedQty: z.number().min(0.01, "Planned quantity must be positive"),
  targetDate: z.string().min(1, "Target date is required"),
  supplierId: z.string().optional(),
  priority: z.enum(["low", "normal", "high", "urgent"]).default("normal"),
  notes: z.string().optional(),
})

export const updateProcurementPlanSchema = z.object({
  plannedQty: z.number().min(0.01).optional(),
  targetDate: z.string().optional(),
  supplierId: z.string().optional(),
  priority: z.enum(["low", "normal", "high", "urgent"]).optional(),
  status: z.enum(["draft", "pending", "approved", "ordered", "received"]).optional(),
  notes: z.string().optional(),
})

/**
 * ✅ PROFESSIONAL: Procurement Planning Service
 * Enterprise-grade procurement planning with supplier optimization and container efficiency
 */
export class ProcurementPlanningService {

  /**
   * Generate procurement plans from demand forecasts
   */
  async generateProcurementPlansFromForecast(
    companyId: string,
    demandForecastId: string,
    userId: string,
    options?: {
      containerOptimization?: boolean
      preferredSuppliers?: string[]
      maxLeadTime?: number
    }
  ): Promise<ProcurementPlan[]> {
    try {
      // 0. Check if procurement plans already exist for this forecast
      const existingPlans = await db.query.procurementPlans.findMany({
        where: and(
          eq(procurementPlans.company_id, companyId),
          eq(procurementPlans.demand_forecast_id, demandForecastId)
        )
      })

      if (existingPlans.length > 0) {
        console.log(`Procurement plans already exist for forecast ${demandForecastId}, skipping generation`)
        return existingPlans.map(plan => ({
          id: plan.id,
          companyId: plan.company_id,
          rawMaterialId: plan.raw_material_id,
          materialName: "Material", // Will be populated by caller if needed
          materialSku: "SKU",
          demandForecastId: plan.demand_forecast_id || undefined,
          plannedQty: parseFloat(plan.planned_qty),
          targetDate: plan.target_date,
          supplierId: plan.supplier_id || undefined,
          supplierName: undefined,
          estimatedCost: parseFloat(plan.estimated_cost || "0"),
          estimatedLeadTime: parseFloat(plan.estimated_lead_time || "30"),
          priority: plan.priority as "low" | "normal" | "high" | "critical",
          status: plan.status as "draft" | "pending" | "approved" | "ordered",
          createdBy: plan.created_by,
          createdAt: plan.created_at!,
          updatedAt: plan.updated_at!,
        }))
      }

      // 1. Get demand forecast and material requirements
      const forecastingService = new DemandForecastingService()
      const materialRequirements = await forecastingService.explodeForecastToBOM(
        companyId,
        demandForecastId
      )

      if (materialRequirements.length === 0) {
        throw new Error("No material requirements found for this forecast")
      }

      // 2. Generate procurement plans for each material
      const generatedPlans: ProcurementPlan[] = []

      for (const requirement of materialRequirements) {
        // Get supplier recommendations
        const supplierRecommendations = await this.getSupplierRecommendations(
          companyId,
          requirement.rawMaterialId,
          requirement.totalRequiredQty,
          requirement.targetDate,
          options
        )

        // Select best supplier (with fallback)
        const bestSupplier = supplierRecommendations[0] || {
          supplierId: "default-supplier",
          supplierName: "Default Supplier",
          leadTimeDays: 30,
          estimatedCost: 0,
          minimumOrderQty: 0,
          maximumOrderQty: undefined,
          reliabilityScore: 0.5,
          costEfficiencyScore: 0.5,
          overallScore: 0.5,
          reasons: ["No supplier data available - using defaults"],
        }

        // Calculate container optimization
        const containerOptimization = await this.optimizeContainerLoad(
          companyId,
          requirement.rawMaterialId,
          requirement.totalRequiredQty,
          bestSupplier?.supplierId
        )

        // Create procurement plan
        const planData = {
          id: uid(),
          company_id: companyId,
          raw_material_id: requirement.rawMaterialId,
          demand_forecast_id: demandForecastId,
          planned_qty: containerOptimization.recommendedQty.toString(),
          target_date: requirement.targetDate,
          supplier_id: bestSupplier.supplierId === "default-supplier" ? null : bestSupplier.supplierId,
          estimated_cost: (bestSupplier.estimatedCost > 0
            ? bestSupplier.estimatedCost * containerOptimization.recommendedQty
            : this.calculateMaterialCost(requirement, containerOptimization.recommendedQty)
          ).toString(),
          estimated_lead_time: bestSupplier?.leadTimeDays.toString() || "30",
          container_optimization: JSON.stringify(containerOptimization),
          priority: requirement.priority,
          status: "draft",
          created_by: userId,
        }

        await db.insert(procurementPlans).values(planData)

        // Format for response
        generatedPlans.push({
          id: planData.id,
          companyId: planData.company_id,
          rawMaterialId: planData.raw_material_id,
          materialName: requirement.materialName,
          materialSku: requirement.materialSku,
          demandForecastId: planData.demand_forecast_id!,
          plannedQty: parseFloat(planData.planned_qty),
          targetDate: planData.target_date,
          supplierId: planData.supplier_id || "default-supplier",
          supplierName: bestSupplier.supplierName || "TBD",
          estimatedCost: parseFloat(planData.estimated_cost),
          estimatedLeadTime: parseFloat(planData.estimated_lead_time),
          containerOptimization,
          priority: planData.priority as "low" | "normal" | "high" | "urgent",
          status: planData.status as "draft" | "pending" | "approved" | "ordered" | "received",
          createdBy: planData.created_by,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
      }

      return generatedPlans
    } catch (error) {
      console.error("Error generating procurement plans:", error)
      throw new Error(`Failed to generate procurement plans: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Calculate material cost from BOM data
   */
  private calculateMaterialCost(requirement: any, quantity: number): number {
    // Try to get cost from material requirement data
    if (requirement.materialCost) {
      return parseFloat(requirement.materialCost) * quantity
    }

    // Default cost estimation based on material type
    const defaultCosts = {
      yarn: 4.5,      // $4.50 per kg
      fabric: 8.5,    // $8.50 per meter
      dyes: 12.0,     // $12.00 per kg
      default: 5.0,   // $5.00 per unit
    }

    const materialName = requirement.materialName?.toLowerCase() || ""
    let unitCost = defaultCosts.default

    if (materialName.includes("yarn")) unitCost = defaultCosts.yarn
    else if (materialName.includes("fabric")) unitCost = defaultCosts.fabric
    else if (materialName.includes("dye")) unitCost = defaultCosts.dyes

    return unitCost * quantity
  }

  /**
   * Get supplier recommendations with scoring
   */
  async getSupplierRecommendations(
    companyId: string,
    rawMaterialId: string,
    requiredQty: number,
    targetDate: string,
    options?: {
      preferredSuppliers?: string[]
      maxLeadTime?: number
    }
  ): Promise<SupplierRecommendation[]> {
    try {
      // Get suppliers with lead time information for this material
      const supplierLeadTimeData = await db.query.supplierLeadTimes.findMany({
        where: and(
          eq(supplierLeadTimes.company_id, companyId),
          or(
            eq(supplierLeadTimes.raw_material_id, rawMaterialId),
            isNull(supplierLeadTimes.raw_material_id) // Generic lead times
          )
        ),
        with: {
          supplier: true,
          rawMaterial: true,
        },
      })

      if (supplierLeadTimeData.length === 0) {
        // No specific lead time data, get all active suppliers
        const allSuppliers = await db.query.suppliers.findMany({
          where: and(
            eq(suppliers.company_id, companyId),
            eq(suppliers.status, "active")
          ),
        })

        return allSuppliers.map(supplier => ({
          supplierId: supplier.id,
          supplierName: supplier.name,
          leadTimeDays: 30, // Default lead time
          estimatedCost: 0, // TODO: Implement cost estimation
          minimumOrderQty: 0,
          maximumOrderQty: undefined,
          reliabilityScore: 0.5,
          costEfficiencyScore: 0.5,
          overallScore: 0.5,
          reasons: ["No historical data available"],
        }))
      }

      // Calculate recommendations with scoring
      const recommendations: SupplierRecommendation[] = []

      for (const leadTimeData of supplierLeadTimeData) {
        const leadTime = parseInt(leadTimeData.lead_time_days)
        const minOrderQty = parseFloat(leadTimeData.minimum_order_qty || "0")
        const maxOrderQty = leadTimeData.maximum_order_qty ? parseFloat(leadTimeData.maximum_order_qty) : undefined

        // Skip if quantity constraints not met
        if (requiredQty < minOrderQty) continue
        if (maxOrderQty && requiredQty > maxOrderQty) continue

        // Skip if lead time exceeds maximum
        if (options?.maxLeadTime && leadTime > options.maxLeadTime) continue

        // Calculate scores
        const reliabilityScore = await this.calculateReliabilityScore(
          companyId,
          leadTimeData.supplier_id
        )
        const costEfficiencyScore = await this.calculateCostEfficiencyScore(
          companyId,
          leadTimeData.supplier_id,
          rawMaterialId
        )

        // Overall score with weights
        const leadTimeScore = Math.max(0, 1 - (leadTime / 60)) // Penalize long lead times
        const overallScore = (
          reliabilityScore * 0.4 +
          costEfficiencyScore * 0.3 +
          leadTimeScore * 0.3
        )

        // Bonus for preferred suppliers
        const isPreferred = options?.preferredSuppliers?.includes(leadTimeData.supplier_id)
        const finalScore = isPreferred ? overallScore * 1.1 : overallScore

        const reasons = []
        if (isPreferred) reasons.push("Preferred supplier")
        if (leadTime <= 14) reasons.push("Fast delivery")
        if (reliabilityScore > 0.8) reasons.push("High reliability")
        if (costEfficiencyScore > 0.8) reasons.push("Cost efficient")

        recommendations.push({
          supplierId: leadTimeData.supplier_id,
          supplierName: leadTimeData.supplier.name,
          leadTimeDays: leadTime,
          estimatedCost: 0, // TODO: Implement cost estimation
          minimumOrderQty: minOrderQty,
          maximumOrderQty: maxOrderQty,
          reliabilityScore,
          costEfficiencyScore,
          overallScore: finalScore,
          reasons,
        })
      }

      // Sort by overall score (descending)
      return recommendations.sort((a, b) => b.overallScore - a.overallScore)
    } catch (error) {
      console.error("Error getting supplier recommendations:", error)
      throw new Error("Failed to get supplier recommendations")
    }
  }

  /**
   * Optimize container load for export efficiency
   */
  async optimizeContainerLoad(
    companyId: string,
    rawMaterialId: string,
    requiredQty: number,
    supplierId?: string
  ): Promise<ContainerOptimization> {
    try {
      // Get material specifications for weight/volume calculations
      const material = await db.query.rawMaterials.findFirst({
        where: and(
          eq(rawMaterials.id, rawMaterialId),
          eq(rawMaterials.company_id, companyId)
        ),
      })

      if (!material) {
        throw new Error("Raw material not found")
      }

      // Parse specifications for weight/volume data
      const specs = material.specifications ? JSON.parse(material.specifications) : {}
      const unitWeight = parseFloat(specs.unit_weight || "1") // kg per unit
      const unitVolume = parseFloat(specs.unit_volume || "0.001") // m³ per unit

      // Container specifications
      const containerSpecs = {
        "20ft": { maxWeight: 28000, maxVolume: 33.2, costMultiplier: 1.0 },
        "40ft": { maxWeight: 26500, maxVolume: 67.7, costMultiplier: 1.6 },
        "40ft-hc": { maxWeight: 26500, maxVolume: 76.4, costMultiplier: 1.7 },
      }

      // Calculate total weight and volume for required quantity
      const totalWeight = requiredQty * unitWeight
      const totalVolume = requiredQty * unitVolume

      // Find optimal container type
      let bestContainer: ContainerOptimization | null = null
      let bestEfficiency = 0

      for (const [containerType, specs] of Object.entries(containerSpecs)) {
        // Check if material fits in container
        if (totalWeight <= specs.maxWeight && totalVolume <= specs.maxVolume) {
          const weightUtilization = totalWeight / specs.maxWeight
          const volumeUtilization = totalVolume / specs.maxVolume
          const utilizationRate = Math.max(weightUtilization, volumeUtilization)

          // Calculate efficiency (higher utilization = better efficiency)
          const efficiency = utilizationRate / specs.costMultiplier

          if (efficiency > bestEfficiency) {
            bestEfficiency = efficiency
            bestContainer = {
              containerType: containerType as "20ft" | "40ft" | "40ft-hc",
              maxWeight: specs.maxWeight,
              maxVolume: specs.maxVolume,
              estimatedWeight: totalWeight,
              estimatedVolume: totalVolume,
              utilizationRate,
              costPerUnit: specs.costMultiplier * 1000, // Base cost estimate
              recommendedQty: requiredQty,
              multipleContainers: false,
            }
          }
        }
      }

      // If no single container fits, calculate multiple containers
      if (!bestContainer) {
        const containerType = "40ft-hc" // Use largest container for multiple
        const specs = containerSpecs[containerType]

        const containersNeeded = Math.ceil(Math.max(
          totalWeight / specs.maxWeight,
          totalVolume / specs.maxVolume
        ))

        const avgUtilization = Math.max(
          totalWeight / (specs.maxWeight * containersNeeded),
          totalVolume / (specs.maxVolume * containersNeeded)
        )

        bestContainer = {
          containerType,
          maxWeight: specs.maxWeight * containersNeeded,
          maxVolume: specs.maxVolume * containersNeeded,
          estimatedWeight: totalWeight,
          estimatedVolume: totalVolume,
          utilizationRate: avgUtilization,
          costPerUnit: specs.costMultiplier * 1000 * containersNeeded,
          recommendedQty: requiredQty,
          multipleContainers: containersNeeded > 1,
        }
      }

      // Adjust quantity for better container utilization if efficiency is low
      if (bestContainer.utilizationRate < 0.7) {
        const optimalQty = this.calculateOptimalQuantity(
          requiredQty,
          unitWeight,
          unitVolume,
          bestContainer
        )

        if (optimalQty > requiredQty) {
          bestContainer.recommendedQty = optimalQty
          bestContainer.estimatedWeight = optimalQty * unitWeight
          bestContainer.estimatedVolume = optimalQty * unitVolume
          bestContainer.utilizationRate = Math.max(
            bestContainer.estimatedWeight / bestContainer.maxWeight,
            bestContainer.estimatedVolume / bestContainer.maxVolume
          )
        }
      }

      return bestContainer
    } catch (error) {
      console.error("Error optimizing container load:", error)
      // Return default optimization
      return {
        containerType: "40ft",
        maxWeight: 26500,
        maxVolume: 67.7,
        estimatedWeight: requiredQty * 1, // Default 1kg per unit
        estimatedVolume: requiredQty * 0.001, // Default 0.001m³ per unit
        utilizationRate: 0.5,
        costPerUnit: 1600,
        recommendedQty: requiredQty,
        multipleContainers: false,
      }
    }
  }

  /**
   * Generate purchase recommendations based on current inventory and demand
   */
  async generatePurchaseRecommendations(
    companyId: string,
    options?: {
      daysAhead?: number
      includeBufferStock?: boolean
      containerOptimization?: boolean
    }
  ): Promise<PurchaseRecommendation[]> {
    try {
      const daysAhead = options?.daysAhead || 90
      const targetDate = new Date()
      targetDate.setDate(targetDate.getDate() + daysAhead)

      // Get all procurement plans that need attention
      const upcomingPlans = await db.query.procurementPlans.findMany({
        where: and(
          eq(procurementPlans.company_id, companyId),
          lte(procurementPlans.target_date, targetDate.toISOString().split('T')[0]),
          or(
            eq(procurementPlans.status, "draft"),
            eq(procurementPlans.status, "pending")
          )
        ),
        with: {
          rawMaterial: true,
          supplier: true,
          demandForecast: true,
        },
        orderBy: [asc(procurementPlans.target_date), descOrder(procurementPlans.priority)],
      })

      const recommendations: PurchaseRecommendation[] = []

      for (const plan of upcomingPlans) {
        // Get supplier recommendations
        const supplierRecommendations = await this.getSupplierRecommendations(
          companyId,
          plan.raw_material_id,
          parseFloat(plan.planned_qty),
          plan.target_date
        )

        if (supplierRecommendations.length === 0) continue

        const bestSupplier = supplierRecommendations[0]

        // Get container optimization
        const containerOptimization = options?.containerOptimization
          ? await this.optimizeContainerLoad(
            companyId,
            plan.raw_material_id,
            parseFloat(plan.planned_qty),
            bestSupplier.supplierId
          )
          : {
            containerType: "40ft" as const,
            maxWeight: 26500,
            maxVolume: 67.7,
            estimatedWeight: parseFloat(plan.planned_qty),
            estimatedVolume: parseFloat(plan.planned_qty) * 0.001,
            utilizationRate: 0.5,
            costPerUnit: 1600,
            recommendedQty: parseFloat(plan.planned_qty),
            multipleContainers: false,
          }

        // Calculate urgency level
        const daysUntilNeeded = Math.ceil(
          (new Date(plan.target_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
        )

        // ✅ UNIFIED PRIORITY SYSTEM: Use same logic as procurement plans
        let urgencyLevel: "low" | "normal" | "high" | "critical"
        if (daysUntilNeeded <= bestSupplier.leadTimeDays * 0.8) {
          urgencyLevel = "critical"
        } else if (daysUntilNeeded <= bestSupplier.leadTimeDays) {
          urgencyLevel = "urgent" as any // Map urgent to critical for recommendations
        } else if (daysUntilNeeded <= bestSupplier.leadTimeDays * 1.5) {
          urgencyLevel = "high"
        } else {
          urgencyLevel = "normal"
        }

        // Convert urgent to critical for recommendations display
        if (urgencyLevel === "urgent" as any) {
          urgencyLevel = "critical"
        }

        // Generate business justification
        const businessJustification = []
        if (plan.demandForecast) {
          businessJustification.push(`Required for demand forecast ${plan.demandForecast.id}`)
        }
        if (urgencyLevel === "critical") {
          businessJustification.push("URGENT: Lead time exceeds available time")
        }
        if (containerOptimization.utilizationRate > 0.8) {
          businessJustification.push("Excellent container utilization")
        }
        if (bestSupplier.overallScore > 0.8) {
          businessJustification.push("High-quality supplier recommended")
        }

        recommendations.push({
          rawMaterialId: plan.raw_material_id,
          materialName: plan.rawMaterial.name,
          recommendedQty: containerOptimization.recommendedQty,
          recommendedSupplier: bestSupplier,
          targetDate: plan.target_date,
          estimatedTotalCost: bestSupplier.estimatedCost * containerOptimization.recommendedQty,
          containerOptimization,
          urgencyLevel,
          businessJustification,
        })
      }

      // Sort by urgency and target date
      const urgencyOrder = { critical: 4, high: 3, normal: 2, low: 1 }
      return recommendations.sort((a, b) => {
        const urgencyDiff = urgencyOrder[b.urgencyLevel] - urgencyOrder[a.urgencyLevel]
        if (urgencyDiff !== 0) return urgencyDiff
        return new Date(a.targetDate).getTime() - new Date(b.targetDate).getTime()
      })
    } catch (error) {
      console.error("Error generating purchase recommendations:", error)
      throw new Error("Failed to generate purchase recommendations")
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Calculate supplier reliability score based on historical performance
   */
  private async calculateReliabilityScore(
    companyId: string,
    supplierId: string
  ): Promise<number> {
    try {
      // Get recent purchase contracts with this supplier
      const recentContracts = await db.query.purchaseContracts.findMany({
        where: and(
          eq(purchaseContracts.company_id, companyId),
          eq(purchaseContracts.supplier_id, supplierId)
        ),
        orderBy: [descOrder(purchaseContracts.created_at)],
        limit: 10, // Last 10 contracts
      })

      if (recentContracts.length === 0) {
        return 0.5 // Default score for new suppliers
      }

      // Calculate reliability based on contract completion rates
      // TODO: Implement actual delivery tracking
      // For now, return a score based on contract status
      const completedContracts = recentContracts.filter(c => c.status === "completed").length
      const reliabilityScore = completedContracts / recentContracts.length

      return Math.max(0.1, Math.min(1.0, reliabilityScore))
    } catch (error) {
      console.error("Error calculating reliability score:", error)
      return 0.5 // Default score on error
    }
  }

  /**
   * Calculate supplier cost efficiency score
   */
  private async calculateCostEfficiencyScore(
    companyId: string,
    supplierId: string,
    rawMaterialId: string
  ): Promise<number> {
    try {
      // Get recent purchase contract items for this material and supplier
      const recentItems = await db.query.purchaseContractItems.findMany({
        where: and(
          eq(purchaseContractItems.product_id, rawMaterialId)
        ),
        with: {
          contract: {
            where: and(
              eq(purchaseContracts.company_id, companyId),
              eq(purchaseContracts.supplier_id, supplierId)
            ),
          },
        },
        orderBy: [descOrder(purchaseContractItems.created_at)],
        limit: 5, // Last 5 purchases
      })

      if (recentItems.length === 0) {
        return 0.5 // Default score for new supplier-material combinations
      }

      // Calculate average price and compare with market
      const avgPrice = recentItems.reduce((sum, item) =>
        sum + parseFloat(item.price), 0
      ) / recentItems.length

      // TODO: Implement market price comparison
      // For now, return a score based on price consistency
      const priceVariance = recentItems.reduce((variance, item) => {
        const diff = parseFloat(item.price) - avgPrice
        return variance + (diff * diff)
      }, 0) / recentItems.length

      // Lower variance = higher score (more consistent pricing)
      const consistencyScore = Math.max(0.1, 1 - (priceVariance / (avgPrice * avgPrice)))

      return Math.max(0.1, Math.min(1.0, consistencyScore))
    } catch (error) {
      console.error("Error calculating cost efficiency score:", error)
      return 0.5 // Default score on error
    }
  }

  /**
   * Calculate optimal quantity for better container utilization
   */
  private calculateOptimalQuantity(
    requiredQty: number,
    unitWeight: number,
    unitVolume: number,
    containerOptimization: ContainerOptimization
  ): number {
    // Calculate quantity that would achieve 80% utilization
    const targetUtilization = 0.8

    const qtyForWeightUtilization = (containerOptimization.maxWeight * targetUtilization) / unitWeight
    const qtyForVolumeUtilization = (containerOptimization.maxVolume * targetUtilization) / unitVolume

    // Use the more constraining factor
    const optimalQty = Math.min(qtyForWeightUtilization, qtyForVolumeUtilization)

    // Only recommend higher quantity if it's reasonable (within 50% of required)
    if (optimalQty <= requiredQty * 1.5) {
      return Math.ceil(optimalQty)
    }

    return requiredQty
  }

  /**
   * Get procurement plan by ID with full details
   */
  async getProcurementPlanById(
    companyId: string,
    planId: string
  ): Promise<ProcurementPlan | null> {
    try {
      const plan = await db.query.procurementPlans.findFirst({
        where: and(
          eq(procurementPlans.id, planId),
          eq(procurementPlans.company_id, companyId)
        ),
        with: {
          rawMaterial: true,
          supplier: true,
          demandForecast: true,
        },
      })

      if (!plan) {
        return null
      }

      return {
        id: plan.id,
        companyId: plan.company_id,
        rawMaterialId: plan.raw_material_id,
        materialName: plan.rawMaterial.name,
        materialSku: plan.rawMaterial.sku,
        demandForecastId: plan.demand_forecast_id || undefined,
        plannedQty: parseFloat(plan.planned_qty),
        targetDate: plan.target_date,
        supplierId: plan.supplier_id || undefined,
        supplierName: plan.supplier?.name || undefined,
        estimatedCost: parseFloat(plan.estimated_cost || "0"),
        estimatedLeadTime: parseFloat(plan.estimated_lead_time || "30"),
        containerOptimization: plan.container_optimization
          ? JSON.parse(plan.container_optimization)
          : {
            containerType: "40ft" as const,
            maxWeight: 26500,
            maxVolume: 67.7,
            estimatedWeight: parseFloat(plan.planned_qty),
            estimatedVolume: parseFloat(plan.planned_qty) * 0.001,
            utilizationRate: 0.5,
            costPerUnit: 1600,
            recommendedQty: parseFloat(plan.planned_qty),
            multipleContainers: false,
          },
        priority: plan.priority as "low" | "normal" | "high" | "urgent",
        status: plan.status as "draft" | "pending" | "approved" | "ordered" | "received",
        notes: plan.notes || undefined,
        createdBy: plan.created_by,
        approvedBy: plan.approved_by || undefined,
        createdAt: plan.created_at!,
        updatedAt: plan.updated_at!,
      }
    } catch (error) {
      console.error("Error getting procurement plan:", error)
      throw new Error("Failed to get procurement plan")
    }
  }

  /**
   * List procurement plans with filtering
   */
  async listProcurementPlans(
    companyId: string,
    filters?: {
      status?: string
      priority?: string
      supplierId?: string
      rawMaterialId?: string
      demandForecastId?: string
    }
  ): Promise<ProcurementPlan[]> {
    try {
      const conditions = [eq(procurementPlans.company_id, companyId)]

      if (filters?.status) {
        conditions.push(eq(procurementPlans.status, filters.status))
      }
      if (filters?.priority) {
        conditions.push(eq(procurementPlans.priority, filters.priority))
      }
      if (filters?.supplierId) {
        conditions.push(eq(procurementPlans.supplier_id, filters.supplierId))
      }
      if (filters?.rawMaterialId) {
        conditions.push(eq(procurementPlans.raw_material_id, filters.rawMaterialId))
      }
      if (filters?.demandForecastId) {
        conditions.push(eq(procurementPlans.demand_forecast_id, filters.demandForecastId))
      }

      const plans = await db.query.procurementPlans.findMany({
        where: and(...conditions),
        with: {
          rawMaterial: true,
          supplier: true,
          demandForecast: true,
        },
        orderBy: [asc(procurementPlans.target_date), descOrder(procurementPlans.priority)],
      })

      return plans.map(plan => ({
        id: plan.id,
        companyId: plan.company_id,
        rawMaterialId: plan.raw_material_id,
        materialName: plan.rawMaterial.name,
        materialSku: plan.rawMaterial.sku,
        demandForecastId: plan.demand_forecast_id || undefined,
        plannedQty: parseFloat(plan.planned_qty),
        targetDate: plan.target_date,
        supplierId: plan.supplier_id || undefined,
        supplierName: plan.supplier?.name || undefined,
        estimatedCost: parseFloat(plan.estimated_cost || "0"),
        estimatedLeadTime: parseFloat(plan.estimated_lead_time || "30"),
        containerOptimization: plan.container_optimization
          ? JSON.parse(plan.container_optimization)
          : {
            containerType: "40ft" as const,
            maxWeight: 26500,
            maxVolume: 67.7,
            estimatedWeight: parseFloat(plan.planned_qty),
            estimatedVolume: parseFloat(plan.planned_qty) * 0.001,
            utilizationRate: 0.5,
            costPerUnit: 1600,
            recommendedQty: parseFloat(plan.planned_qty),
            multipleContainers: false,
          },
        priority: plan.priority as "low" | "normal" | "high" | "urgent",
        status: plan.status as "draft" | "pending" | "approved" | "ordered" | "received",
        notes: plan.notes || undefined,
        createdBy: plan.created_by,
        approvedBy: plan.approved_by || undefined,
        createdAt: plan.created_at!,
        updatedAt: plan.updated_at!,
      }))
    } catch (error) {
      console.error("Error listing procurement plans:", error)
      throw new Error("Failed to list procurement plans")
    }
  }

  /**
   * Update procurement plan status and other fields
   */
  async updateProcurementPlan(
    companyId: string,
    planId: string,
    updates: {
      plannedQty?: number
      targetDate?: string
      supplierId?: string
      priority?: "low" | "normal" | "high" | "urgent"
      status?: "draft" | "pending" | "approved" | "ordered" | "received"
      notes?: string
      approvedBy?: string
    }
  ): Promise<ProcurementPlan> {
    try {
      // Prepare update data
      const updateData: any = {
        updated_at: new Date(),
      }

      if (updates.plannedQty !== undefined) {
        updateData.planned_qty = updates.plannedQty.toString()
      }
      if (updates.targetDate !== undefined) {
        updateData.target_date = updates.targetDate
      }
      if (updates.supplierId !== undefined) {
        updateData.supplier_id = updates.supplierId
      }
      if (updates.priority !== undefined) {
        updateData.priority = updates.priority
      }
      if (updates.status !== undefined) {
        updateData.status = updates.status
        if (updates.status === "approved" && updates.approvedBy) {
          updateData.approved_by = updates.approvedBy
          updateData.approved_at = new Date()
        }
      }
      if (updates.notes !== undefined) {
        updateData.notes = updates.notes
      }

      // Update the procurement plan
      const [updatedPlan] = await db
        .update(procurementPlans)
        .set(updateData)
        .where(and(
          eq(procurementPlans.id, planId),
          eq(procurementPlans.company_id, companyId)
        ))
        .returning()

      if (!updatedPlan) {
        throw new Error("Procurement plan not found or update failed")
      }

      // Return the updated plan with relationships
      return await this.getProcurementPlanById(companyId, planId)
    } catch (error) {
      console.error("Error updating procurement plan:", error)
      throw new Error("Failed to update procurement plan")
    }
  }

  /**
   * Bulk approve procurement plans
   */
  async bulkApproveProcurementPlans(
    companyId: string,
    planIds: string[],
    approvedBy: string,
    approvalNotes?: string
  ): Promise<{
    approved: ProcurementPlan[]
    failed: { planId: string; reason: string }[]
  }> {
    try {
      const approved: ProcurementPlan[] = []
      const failed: { planId: string; reason: string }[] = []

      for (const planId of planIds) {
        try {
          // Check if plan exists and can be approved
          const existingPlan = await this.getProcurementPlanById(companyId, planId)

          if (!existingPlan) {
            failed.push({ planId, reason: "Plan not found" })
            continue
          }

          // Business rule: Only draft and pending plans can be approved
          if (existingPlan.status !== "draft" && existingPlan.status !== "pending") {
            failed.push({
              planId,
              reason: `Cannot approve plan with status '${existingPlan.status}'. Only draft and pending plans can be approved.`
            })
            continue
          }

          // Update plan to approved status
          const updatedPlan = await this.updateProcurementPlan(
            companyId,
            planId,
            {
              status: "approved",
              approvedBy,
              notes: approvalNotes
            }
          )

          approved.push(updatedPlan)
        } catch (error) {
          failed.push({
            planId,
            reason: error instanceof Error ? error.message : "Unknown error"
          })
        }
      }

      return { approved, failed }
    } catch (error) {
      console.error("Error bulk approving procurement plans:", error)
      throw new Error("Failed to bulk approve procurement plans")
    }
  }
}
