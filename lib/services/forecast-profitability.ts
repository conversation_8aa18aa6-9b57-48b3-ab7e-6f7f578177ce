/**
 * Manufacturing ERP - Forecast Profitability Service
 * 
 * Professional service for calculating profit margins and profitability metrics
 * for demand forecasts based on BOM costs and product selling prices.
 * Extends the existing BOM profit margin system to forecasting and MRP modules.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Profit Margin Integration
 */

import { db } from "@/lib/db"
import {
  demandForecasts,
  products,
  billOfMaterials,
  rawMaterials,
} from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"

// ✅ PROFESSIONAL: Type definitions for profit margin analysis
export interface ForecastProfitability {
  forecastId: string
  productId: string
  productName: string
  productSku: string
  forecastedDemand: number
  
  // Financial metrics
  sellingPrice: number
  materialCost: number
  totalRevenue: number
  totalMaterialCost: number
  totalProfit: number
  marginPercentage: number
  profitabilityStatus: 'excellent' | 'good' | 'fair' | 'poor'
  
  // BOM breakdown
  bomItemCount: number
  hasCompleteBOM: boolean
  
  // Currency and formatting
  currency: string
}

export interface ProfitabilityOverview {
  totalForecasts: number
  totalRevenue: number
  totalMaterialCost: number
  totalProfit: number
  averageMargin: number
  profitabilityDistribution: {
    excellent: number
    good: number
    fair: number
    poor: number
  }
  topProfitableForecasts: ForecastProfitability[]
  lowProfitableForecasts: ForecastProfitability[]
}

/**
 * ✅ PROFESSIONAL: Forecast Profitability Service
 * Calculates profit margins for demand forecasts using BOM cost data
 */
export class ForecastProfitabilityService {
  
  /**
   * Calculate profitability for a single forecast
   */
  async calculateForecastProfitability(
    companyId: string,
    forecastId: string
  ): Promise<ForecastProfitability | null> {
    try {
      // 1. Get forecast with product details
      const forecast = await db.query.demandForecasts.findFirst({
        where: and(
          eq(demandForecasts.id, forecastId),
          eq(demandForecasts.company_id, companyId)
        ),
        with: {
          product: true,
        },
      })

      if (!forecast || !forecast.product) {
        return null
      }

      // 2. Get BOM items for cost calculation
      const bomItems = await db.query.billOfMaterials.findMany({
        where: and(
          eq(billOfMaterials.company_id, companyId),
          eq(billOfMaterials.product_id, forecast.product_id),
          eq(billOfMaterials.status, "active")
        ),
        with: {
          rawMaterial: true,
        },
      })

      // 3. Calculate material cost (same logic as BOM module)
      const materialCost = bomItems.reduce((sum, item) => {
        if (item.rawMaterial?.standard_cost) {
          const qty = parseFloat(item.qty_required || "0")
          const wasteFactor = parseFloat(item.waste_factor || "0.05")
          const totalQty = qty * (1 + wasteFactor)
          const cost = parseFloat(item.rawMaterial.standard_cost || "0")
          return sum + (totalQty * cost)
        }
        return sum
      }, 0)

      // 4. Get selling price (same logic as BOM module)
      const sellingPrice = forecast.product.base_price 
        ? parseFloat(forecast.product.base_price) 
        : 0

      // 5. Calculate profitability metrics
      const forecastedDemand = parseFloat(forecast.forecasted_demand)
      const totalRevenue = sellingPrice * forecastedDemand
      const totalMaterialCost = materialCost * forecastedDemand
      const totalProfit = totalRevenue - totalMaterialCost
      const marginPercentage = sellingPrice > 0 ? (totalProfit / totalRevenue) * 100 : 0

      // 6. Determine profitability status (same logic as BOM module)
      const profitabilityStatus = marginPercentage >= 30 ? 'excellent' :
        marginPercentage >= 20 ? 'good' :
          marginPercentage >= 10 ? 'fair' : 'poor'

      return {
        forecastId: forecast.id,
        productId: forecast.product_id,
        productName: forecast.product.name,
        productSku: forecast.product.sku,
        forecastedDemand,
        sellingPrice,
        materialCost,
        totalRevenue,
        totalMaterialCost,
        totalProfit,
        marginPercentage,
        profitabilityStatus,
        bomItemCount: bomItems.length,
        hasCompleteBOM: bomItems.length > 0 && bomItems.every(item => item.rawMaterial?.standard_cost),
        currency: forecast.product.currency || 'USD',
      }
    } catch (error) {
      console.error("Error calculating forecast profitability:", error)
      return null
    }
  }

  /**
   * Calculate profitability for multiple forecasts
   */
  async calculateMultipleForecastsProfitability(
    companyId: string,
    forecastIds: string[]
  ): Promise<ForecastProfitability[]> {
    const results: ForecastProfitability[] = []
    
    for (const forecastId of forecastIds) {
      const profitability = await this.calculateForecastProfitability(companyId, forecastId)
      if (profitability) {
        results.push(profitability)
      }
    }
    
    return results
  }

  /**
   * Get profitability overview for all active forecasts
   */
  async getProfitabilityOverview(companyId: string): Promise<ProfitabilityOverview> {
    try {
      // 1. Get all active forecasts
      const forecasts = await db.query.demandForecasts.findMany({
        where: eq(demandForecasts.company_id, companyId),
        with: {
          product: true,
        },
      })

      // 2. Calculate profitability for each forecast
      const profitabilityData: ForecastProfitability[] = []
      for (const forecast of forecasts) {
        const profitability = await this.calculateForecastProfitability(companyId, forecast.id)
        if (profitability) {
          profitabilityData.push(profitability)
        }
      }

      // 3. Calculate overview metrics
      const totalRevenue = profitabilityData.reduce((sum, item) => sum + item.totalRevenue, 0)
      const totalMaterialCost = profitabilityData.reduce((sum, item) => sum + item.totalMaterialCost, 0)
      const totalProfit = totalRevenue - totalMaterialCost
      const averageMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0

      // 4. Calculate profitability distribution
      const distribution = {
        excellent: profitabilityData.filter(item => item.profitabilityStatus === 'excellent').length,
        good: profitabilityData.filter(item => item.profitabilityStatus === 'good').length,
        fair: profitabilityData.filter(item => item.profitabilityStatus === 'fair').length,
        poor: profitabilityData.filter(item => item.profitabilityStatus === 'poor').length,
      }

      // 5. Get top and low profitable forecasts
      const sortedByMargin = [...profitabilityData].sort((a, b) => b.marginPercentage - a.marginPercentage)
      const topProfitableForecasts = sortedByMargin.slice(0, 5)
      const lowProfitableForecasts = sortedByMargin.slice(-5).reverse()

      return {
        totalForecasts: profitabilityData.length,
        totalRevenue,
        totalMaterialCost,
        totalProfit,
        averageMargin,
        profitabilityDistribution: distribution,
        topProfitableForecasts,
        lowProfitableForecasts,
      }
    } catch (error) {
      console.error("Error calculating profitability overview:", error)
      return {
        totalForecasts: 0,
        totalRevenue: 0,
        totalMaterialCost: 0,
        totalProfit: 0,
        averageMargin: 0,
        profitabilityDistribution: { excellent: 0, good: 0, fair: 0, poor: 0 },
        topProfitableForecasts: [],
        lowProfitableForecasts: [],
      }
    }
  }

  /**
   * Get profitability status badge configuration (consistent with BOM module)
   */
  static getProfitabilityBadgeConfig(status: 'excellent' | 'good' | 'fair' | 'poor') {
    switch (status) {
      case 'excellent':
        return {
          variant: 'default' as const,
          className: 'bg-green-600 text-white hover:bg-green-700',
          label: 'Excellent',
          description: '30%+ margin'
        }
      case 'good':
        return {
          variant: 'secondary' as const,
          className: 'bg-blue-600 text-white hover:bg-blue-700',
          label: 'Good',
          description: '20-30% margin'
        }
      case 'fair':
        return {
          variant: 'outline' as const,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-300 hover:bg-yellow-200',
          label: 'Fair',
          description: '10-20% margin'
        }
      case 'poor':
        return {
          variant: 'destructive' as const,
          className: 'bg-red-600 text-white hover:bg-red-700',
          label: 'Poor',
          description: '<10% margin'
        }
    }
  }
}
