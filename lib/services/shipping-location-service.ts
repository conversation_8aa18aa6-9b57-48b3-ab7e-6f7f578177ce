/**
 * Manufacturing ERP - Shipping Location Service
 * 
 * Professional location-aware shipping service with capacity validation,
 * pickup optimization, and staging area management.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1 Location Integration
 */

import { db } from "@/lib/db"
import { shipments, stockLots } from "@/lib/schema-postgres"
import { eq, and, sql } from "drizzle-orm"
import { LocationManager, getLocationForUI } from "@/lib/location-config"
import { 
  validateShippingCapacity, 
  getShippingStagingUtilization,
  CapacityValidationResult 
} from "@/lib/location-utilization"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface ShippingLocationOption {
  locationId: string
  locationName: string
  locationType: string
  icon: string
  capacity: number
  availableCapacity: number
  utilizationPercentage: number
  shippingMethods: string[]
  attributes: {
    dockType?: string
    maxVehicleSize?: string
    temperatureControlled: boolean
    securityLevel: string
  }
  estimatedProcessingTime: number // in hours
  costFactor: number // multiplier for shipping costs
}

export interface ProductAllocation {
  productId: string
  quantity: number
  currentLocationId: string
  weight?: number
  volume?: number
}

export interface LocationOptimizationResult {
  recommendedLocationId: string
  locationName: string
  optimizationScore: number
  reasons: string[]
  alternativeLocations: ShippingLocationOption[]
  estimatedSavings: {
    time: number // hours
    cost: number // percentage
    efficiency: number // percentage
  }
}

export interface StagingReservation {
  reservationId: string
  shipmentId: string
  locationId: string
  reservedCapacity: number
  reservedUntil: Date
  status: 'active' | 'expired' | 'released'
}

// ============================================================================
// SHIPPING LOCATION SERVICE CLASS
// ============================================================================

export class ShippingLocationService {
  
  /**
   * Get available shipping locations by method
   */
  async getAvailableShippingLocations(
    shippingMethod: string,
    companyId: string
  ): Promise<ShippingLocationOption[]> {
    try {
      // Get all shipping-related locations
      const stagingLocations = LocationManager.getLocationsByType('shipping_staging')
      const dockLocations = LocationManager.getLocationsByType('shipping_dock')
      const allLocations = [...stagingLocations, ...dockLocations]

      const locationOptions: ShippingLocationOption[] = []

      for (const location of allLocations) {
        // Check if location supports the shipping method
        const supportsMethod = !location.attributes.shippingMethods || 
          location.attributes.shippingMethods.includes(shippingMethod)

        if (!supportsMethod) continue

        // Get current utilization
        const utilization = await this.getLocationUtilization(location.id, companyId)

        locationOptions.push({
          locationId: location.id,
          locationName: location.displayName,
          locationType: location.type,
          icon: location.icon,
          capacity: location.capacity,
          availableCapacity: utilization.availableCapacity,
          utilizationPercentage: utilization.utilizationPercentage,
          shippingMethods: location.attributes.shippingMethods || [],
          attributes: {
            dockType: location.attributes.dockType,
            maxVehicleSize: location.attributes.maxVehicleSize,
            temperatureControlled: location.attributes.temperatureControlled || false,
            securityLevel: location.attributes.securityLevel
          },
          estimatedProcessingTime: this.calculateProcessingTime(location, shippingMethod),
          costFactor: this.calculateCostFactor(location, shippingMethod)
        })
      }

      // Sort by optimization score (availability + efficiency)
      return locationOptions.sort((a, b) => {
        const scoreA = (a.availableCapacity / a.capacity) * (1 / a.costFactor)
        const scoreB = (b.availableCapacity / b.capacity) * (1 / b.costFactor)
        return scoreB - scoreA
      })

    } catch (error) {
      console.error('Error getting available shipping locations:', error)
      return []
    }
  }

  /**
   * Optimize pickup location based on inventory distribution
   */
  async optimizePickupLocation(
    productAllocations: ProductAllocation[],
    shippingMethod: string,
    companyId: string
  ): Promise<LocationOptimizationResult> {
    try {
      const availableLocations = await this.getAvailableShippingLocations(shippingMethod, companyId)
      
      if (availableLocations.length === 0) {
        throw new Error('No available shipping locations for the specified method')
      }

      // Calculate optimization scores for each location
      const locationScores = await Promise.all(
        availableLocations.map(async (location) => {
          const score = await this.calculateOptimizationScore(
            location,
            productAllocations,
            companyId
          )
          return { location, score }
        })
      )

      // Sort by score (highest first)
      locationScores.sort((a, b) => b.score.totalScore - a.score.totalScore)
      const bestLocation = locationScores[0]

      return {
        recommendedLocationId: bestLocation.location.locationId,
        locationName: bestLocation.location.locationName,
        optimizationScore: bestLocation.score.totalScore,
        reasons: bestLocation.score.reasons,
        alternativeLocations: locationScores.slice(1, 4).map(ls => ls.location),
        estimatedSavings: bestLocation.score.estimatedSavings
      }

    } catch (error) {
      console.error('Error optimizing pickup location:', error)
      throw error
    }
  }

  /**
   * Validate shipping capacity for staging areas
   */
  async validateShippingCapacity(
    locationId: string,
    shipmentVolume: number,
    companyId: string
  ): Promise<CapacityValidationResult> {
    return validateShippingCapacity(locationId, shipmentVolume, companyId)
  }

  /**
   * Reserve staging area capacity for shipment
   */
  async reserveStagingCapacity(
    shipmentId: string,
    stagingLocationId: string,
    requiredCapacity: number,
    companyId: string
  ): Promise<StagingReservation> {
    try {
      // Validate capacity is available
      const validation = await this.validateShippingCapacity(
        stagingLocationId,
        requiredCapacity,
        companyId
      )

      if (!validation.canAccommodate) {
        throw new Error(`Insufficient capacity at ${stagingLocationId}. ${validation.message}`)
      }

      // Create reservation (in a real system, this would be stored in database)
      const reservation: StagingReservation = {
        reservationId: `res_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        shipmentId,
        locationId: stagingLocationId,
        reservedCapacity: requiredCapacity,
        reservedUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        status: 'active'
      }

      // TODO: Store reservation in database table
      // await db.insert(stagingReservations).values(reservation)

      return reservation

    } catch (error) {
      console.error('Error reserving staging capacity:', error)
      throw error
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private async getLocationUtilization(locationId: string, companyId: string) {
    // Get current stock in location
    const stockResult = await db
      .select({
        totalStock: sql<number>`COALESCE(SUM(CAST(${stockLots.qty} AS DECIMAL)), 0)`
      })
      .from(stockLots)
      .where(and(
        eq(stockLots.company_id, companyId),
        eq(stockLots.location, locationId),
        eq(stockLots.status, 'available')
      ))

    const currentStock = stockResult[0]?.totalStock || 0
    const locationConfig = getLocationForUI(locationId)
    const capacity = locationConfig?.capacity || 1000

    return {
      currentStock,
      capacity,
      availableCapacity: Math.max(0, capacity - currentStock),
      utilizationPercentage: (currentStock / capacity) * 100
    }
  }

  private calculateProcessingTime(location: any, shippingMethod: string): number {
    // Base processing time by location type
    let baseTime = location.type === 'shipping_dock' ? 2 : 4 // hours

    // Adjust by shipping method
    const methodMultipliers = {
      'express': 0.5,
      'air_freight': 0.7,
      'truck': 1.0,
      'sea_freight': 1.5
    }

    return baseTime * (methodMultipliers[shippingMethod as keyof typeof methodMultipliers] || 1.0)
  }

  private calculateCostFactor(location: any, shippingMethod: string): number {
    // Base cost factor by location efficiency
    let baseFactor = location.attributes.automatedHandling ? 0.8 : 1.0

    // Adjust by security level (higher security = higher cost)
    const securityMultipliers = {
      'low': 0.9,
      'medium': 1.0,
      'high': 1.1,
      'restricted': 1.3
    }

    return baseFactor * (securityMultipliers[location.attributes.securityLevel as keyof typeof securityMultipliers] || 1.0)
  }

  private async calculateOptimizationScore(
    location: ShippingLocationOption,
    productAllocations: ProductAllocation[],
    companyId: string
  ) {
    // Calculate distance score (proximity to inventory)
    const proximityScore = await this.calculateProximityScore(location, productAllocations, companyId)
    
    // Calculate capacity score
    const capacityScore = location.availableCapacity / location.capacity * 100
    
    // Calculate efficiency score
    const efficiencyScore = (1 / location.costFactor) * 100
    
    // Calculate processing time score
    const timeScore = Math.max(0, 100 - location.estimatedProcessingTime * 10)

    // Weighted total score
    const totalScore = (
      proximityScore * 0.3 +
      capacityScore * 0.3 +
      efficiencyScore * 0.2 +
      timeScore * 0.2
    )

    const reasons = []
    if (proximityScore > 80) reasons.push('Close to inventory sources')
    if (capacityScore > 70) reasons.push('High available capacity')
    if (efficiencyScore > 80) reasons.push('Cost-efficient operations')
    if (timeScore > 70) reasons.push('Fast processing time')

    return {
      totalScore,
      proximityScore,
      capacityScore,
      efficiencyScore,
      timeScore,
      reasons,
      estimatedSavings: {
        time: Math.max(0, 8 - location.estimatedProcessingTime),
        cost: Math.max(0, (2 - location.costFactor) * 50),
        efficiency: Math.min(100, totalScore - 50)
      }
    }
  }

  private async calculateProximityScore(
    location: ShippingLocationOption,
    productAllocations: ProductAllocation[],
    companyId: string
  ): Promise<number> {
    // Simplified proximity calculation
    // In a real system, this would consider physical distance, flow connections, etc.
    
    let totalScore = 0
    let totalWeight = 0

    for (const allocation of productAllocations) {
      const weight = allocation.quantity
      
      // Check if current location is in the same flow as shipping location
      const currentLocationConfig = getLocationForUI(allocation.currentLocationId)
      const shippingLocationConfig = getLocationForUI(location.locationId)
      
      let proximityScore = 50 // base score
      
      if (currentLocationConfig && shippingLocationConfig) {
        // Same building = high proximity
        if (currentLocationConfig.hierarchy.building === shippingLocationConfig.hierarchy.building) {
          proximityScore = 90
        }
        // Same plant = medium proximity  
        else if (currentLocationConfig.hierarchy.plant === shippingLocationConfig.hierarchy.plant) {
          proximityScore = 70
        }
        // Check flow connections
        else if (currentLocationConfig.flowConnections.includes(location.locationId)) {
          proximityScore = 80
        }
      }
      
      totalScore += proximityScore * weight
      totalWeight += weight
    }

    return totalWeight > 0 ? totalScore / totalWeight : 50
  }
}
