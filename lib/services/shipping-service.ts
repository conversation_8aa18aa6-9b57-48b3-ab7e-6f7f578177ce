/**
 * Manufacturing ERP - Shipping Service
 * Professional shipping management with complete workflow integration
 */

import { db } from "@/lib/db"
import {
  shipments,
  shipmentItems,
  shippingDocuments,
  shippingTracking,
  stockLots,
  stockTxns,
  salesContracts,
  customers,
  products,
  arInvoices
} from "@/lib/schema-postgres"
import { eq, and, desc, sql } from "drizzle-orm"
import { generateId } from "@/lib/utils"
import { ShippingInventoryIntegration } from "./shipping-inventory-integration"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface CreateShipmentData {
  sales_contract_id?: string
  customer_id: string
  shipping_method: string
  carrier?: string
  service_type?: string
  pickup_address?: any
  delivery_address?: any
  ship_date?: string
  estimated_delivery?: string
  shipping_cost?: string
  insurance_cost?: string
  notes?: string
  special_instructions?: string
  items: CreateShipmentItemData[]
}

export interface CreateShipmentItemData {
  product_id: string
  stock_lot_id?: string
  quantity: string
  unit_price?: string
  batch_number?: string
  lot_number?: string
}

export interface ShipmentStatusUpdate {
  status: string
  location?: string
  description: string
  estimated_delivery?: string
  notes?: string
}

export interface InventoryAllocation {
  product_id: string
  quantity: string
  preferred_location?: string
}

// ============================================================================
// SHIPPING SERVICE CLASS
// ============================================================================

export class ShippingService {

  /**
   * Create a new shipment
   */
  async createShipment(data: CreateShipmentData, companyId: string, userId?: string): Promise<any> {
    const shipmentId = generateId()

    // Generate shipment number
    const shipmentNumber = await this.generateShipmentNumber(companyId)

    // Calculate totals
    const totalValue = this.calculateTotalValue(data.items)
    const totalWeight = this.calculateTotalWeight(data.items)

    // Create shipment record
    const [shipment] = await db.insert(shipments).values({
      id: shipmentId,
      company_id: companyId,
      shipment_number: shipmentNumber,
      sales_contract_id: data.sales_contract_id,
      customer_id: data.customer_id,
      shipping_method: data.shipping_method,
      carrier: data.carrier,
      service_type: data.service_type,
      pickup_address: data.pickup_address,
      delivery_address: data.delivery_address,
      ship_date: data.ship_date,
      estimated_delivery: data.estimated_delivery,
      shipping_cost: data.shipping_cost,
      insurance_cost: data.insurance_cost,
      total_value: totalValue,
      total_weight: totalWeight,
      notes: data.notes,
      special_instructions: data.special_instructions,
      created_by: userId,
      status: "preparing"
    }).returning()

    // Create shipment items
    for (const itemData of data.items) {
      await this.addShipmentItem(shipmentId, itemData, companyId)
    }

    // ✅ ENHANCEMENT: Auto-allocate inventory for new shipments
    try {
      const allocations = data.items.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity,
        preferred_location: item.preferred_location
      }))

      await this.allocateInventory(shipmentId, allocations, companyId)
      console.log(`✅ Auto-allocated inventory for shipment ${shipmentNumber}`)
    } catch (error) {
      console.log(`⚠️ Auto-allocation failed for shipment ${shipmentNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      // Continue without failing the shipment creation
      // This allows manual allocation later
    }

    // Create initial tracking entry
    await this.addTrackingEntry(shipmentId, {
      status: "preparing",
      description: "Shipment created and preparing for dispatch",
      location: "Warehouse"
    }, companyId, userId)

    return shipment
  }

  /**
   * Create shipment from sales contract
   */
  async createShipmentFromContract(contractId: string, companyId: string, userId?: string): Promise<any> {
    // Get contract with items and customer
    const contract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, contractId),
        eq(salesContracts.company_id, companyId)
      ),
      with: {
        customer: true,
        items: {
          with: {
            product: true
          }
        }
      }
    })

    if (!contract) {
      throw new Error("Sales contract not found")
    }

    // ERP Best Practice: Allow approved, active, and in_production contracts to be shipped
    const shippableStatuses = ["approved", "active", "in_production"]
    if (!shippableStatuses.includes(contract.status)) {
      throw new Error(`Only contracts with status ${shippableStatuses.join(", ")} can be shipped`)
    }

    // Convert contract items to shipment items
    const shipmentItems: CreateShipmentItemData[] = contract.items.map(item => ({
      product_id: item.product_id,
      quantity: item.qty,
      unit_price: item.price,
    }))

    // Create shipment data
    const shipmentData: CreateShipmentData = {
      sales_contract_id: contractId,
      customer_id: contract.customer_id,
      shipping_method: "sea_freight", // Default, can be updated
      delivery_address: {
        name: contract.customer.name,
        address: contract.customer.address,
        contact: contract.customer.contact_name,
        phone: contract.customer.contact_phone,
        email: contract.customer.contact_email
      },
      items: shipmentItems
    }

    return this.createShipment(shipmentData, companyId, userId)
  }

  /**
   * Add item to shipment
   */
  async addShipmentItem(shipmentId: string, itemData: CreateShipmentItemData, companyId: string): Promise<any> {
    const itemId = generateId()

    // Calculate total price
    const totalPrice = itemData.unit_price ?
      (parseFloat(itemData.quantity) * parseFloat(itemData.unit_price)).toString() :
      undefined

    const [item] = await db.insert(shipmentItems).values({
      id: itemId,
      company_id: companyId,
      shipment_id: shipmentId,
      product_id: itemData.product_id,
      stock_lot_id: itemData.stock_lot_id,
      quantity: itemData.quantity,
      unit_price: itemData.unit_price,
      total_price: totalPrice,
      batch_number: itemData.batch_number,
      lot_number: itemData.lot_number,
      status: "pending"
    }).returning()

    return item
  }

  /**
   * Allocate inventory to shipment
   */
  async allocateInventory(shipmentId: string, allocations: InventoryAllocation[], companyId: string): Promise<void> {
    for (const allocation of allocations) {
      // Find available stock lots for the product
      const availableStock = await db.query.stockLots.findMany({
        where: and(
          eq(stockLots.company_id, companyId),
          eq(stockLots.product_id, allocation.product_id),
          eq(stockLots.status, "available"),
          eq(stockLots.quality_status, "approved")
        ),
        orderBy: [stockLots.created_at]
      })

      if (availableStock.length === 0) {
        throw new Error(`No available inventory for product ${allocation.product_id}`)
      }

      let remainingQty = parseFloat(allocation.quantity)

      for (const stockLot of availableStock) {
        if (remainingQty <= 0) break

        const availableQty = parseFloat(stockLot.qty)
        const allocateQty = Math.min(remainingQty, availableQty)

        // Reserve the stock lot for this shipment
        await db.update(stockLots)
          .set({
            status: "reserved",
            // Add shipment reference if we add this field later
          })
          .where(eq(stockLots.id, stockLot.id))

        // Update shipment item with stock lot reference
        await db.update(shipmentItems)
          .set({ stock_lot_id: stockLot.id })
          .where(and(
            eq(shipmentItems.shipment_id, shipmentId),
            eq(shipmentItems.product_id, allocation.product_id)
          ))

        remainingQty -= allocateQty
      }

      if (remainingQty > 0) {
        throw new Error(`Insufficient inventory for product ${allocation.product_id}. Need ${remainingQty} more units.`)
      }
    }
  }

  /**
   * Update shipment status
   */
  async updateShipmentStatus(shipmentId: string, statusUpdate: ShipmentStatusUpdate, companyId: string, userId?: string): Promise<void> {
    // Update shipment status
    await db.update(shipments)
      .set({
        status: statusUpdate.status,
        estimated_delivery: statusUpdate.estimated_delivery,
        updated_by: userId
      })
      .where(and(
        eq(shipments.id, shipmentId),
        eq(shipments.company_id, companyId)
      ))

    // Add tracking entry
    await this.addTrackingEntry(shipmentId, statusUpdate, companyId, userId)

    // Handle status-specific logic
    if (statusUpdate.status === "shipped") {
      await this.handleShipmentShipped(shipmentId, companyId, userId)
    } else if (statusUpdate.status === "delivered") {
      await this.handleShipmentDelivered(shipmentId, companyId, userId)
    }
  }

  /**
   * Add tracking entry
   */
  async addTrackingEntry(shipmentId: string, statusUpdate: ShipmentStatusUpdate, companyId: string, userId?: string): Promise<void> {
    const trackingId = generateId()

    await db.insert(shippingTracking).values({
      id: trackingId,
      company_id: companyId,
      shipment_id: shipmentId,
      status: statusUpdate.status,
      location: statusUpdate.location,
      description: statusUpdate.description,
      timestamp: new Date(),
      estimated_delivery: statusUpdate.estimated_delivery,
      notes: statusUpdate.notes,
      created_by: userId
    })
  }

  /**
   * Get available inventory for product
   */
  async getAvailableInventory(productId: string, companyId: string): Promise<any[]> {
    return db.query.stockLots.findMany({
      where: and(
        eq(stockLots.company_id, companyId),
        eq(stockLots.product_id, productId),
        eq(stockLots.status, "available"),
        eq(stockLots.quality_status, "approved")
      ),
      with: {
        product: true
      },
      orderBy: [stockLots.created_at]
    })
  }

  /**
   * Generate shipment number
   */
  private async generateShipmentNumber(companyId: string): Promise<string> {
    const year = new Date().getFullYear()
    const month = String(new Date().getMonth() + 1).padStart(2, '0')

    // Get the count of shipments this month
    const count = await db
      .select({ count: sql<number>`count(*)` })
      .from(shipments)
      .where(and(
        eq(shipments.company_id, companyId),
        sql`EXTRACT(YEAR FROM created_at) = ${year}`,
        sql`EXTRACT(MONTH FROM created_at) = ${new Date().getMonth() + 1}`
      ))

    const sequence = String((count[0]?.count || 0) + 1).padStart(3, '0')
    return `SH-${year}${month}-${sequence}`
  }

  /**
   * Calculate total value of shipment items
   */
  private calculateTotalValue(items: CreateShipmentItemData[]): string {
    const total = items.reduce((sum, item) => {
      if (item.unit_price) {
        return sum + (parseFloat(item.quantity) * parseFloat(item.unit_price))
      }
      return sum
    }, 0)

    return total.toString()
  }

  /**
   * Calculate total weight (placeholder - would need product weight data)
   */
  private calculateTotalWeight(items: CreateShipmentItemData[]): string {
    // Placeholder calculation - in real implementation, would get product weights
    const totalUnits = items.reduce((sum, item) => sum + parseFloat(item.quantity), 0)
    return (totalUnits * 1.5).toString() // Assume 1.5kg per unit average
  }

  /**
   * Handle shipment shipped status
   * ✅ PHASE 2 ENHANCEMENT: Complete inventory integration
   */
  async handleShipmentShipped(shipmentId: string, companyId: string, userId?: string): Promise<void> {
    try {
      console.log(`Processing shipment shipped: ${shipmentId}`)

      // 1. Update actual ship date
      await db.update(shipments)
        .set({
          ship_date: new Date().toISOString().split('T')[0],
          updated_at: new Date()
        })
        .where(and(
          eq(shipments.id, shipmentId),
          eq(shipments.company_id, companyId)
        ))

      // 2. ✅ NEW: Process comprehensive inventory movements
      const inventoryIntegration = new ShippingInventoryIntegration()
      await inventoryIntegration.processShipmentInventoryMovements(
        shipmentId,
        companyId,
        userId
      )

      // 3. ✅ ENHANCED: Update stock lot status (keeping existing logic for compatibility)
      const shipmentItemsList = await db.query.shipmentItems.findMany({
        where: and(
          eq(shipmentItems.shipment_id, shipmentId),
          eq(shipmentItems.company_id, companyId)
        )
      })

      for (const item of shipmentItemsList) {
        if (item.stock_lot_id) {
          await db.update(stockLots)
            .set({
              status: "shipped",
              updated_at: new Date()
            })
            .where(eq(stockLots.id, item.stock_lot_id))
        }
      }

      // 4. ✅ NEW: Update shipping location utilization
      await this.updateShippingLocationUtilization(shipmentId, companyId)

      // 5. ✅ NEW: Trigger downstream workflows
      await this.triggerShippingNotifications(shipmentId, companyId, userId)

      // 6. ✅ AUTOMATIC INVOICE GENERATION: Generate AR Invoice when shipped
      await this.generateARInvoiceForShipment(shipmentId, companyId, userId)

      console.log(`Successfully processed shipment shipped: ${shipmentId}`)

    } catch (error) {
      console.error(`Error processing shipment shipped ${shipmentId}:`, error)
      throw error
    }
  }

  /**
   * Handle shipment delivered status
   * ✅ PHASE 2 ENHANCEMENT: Enhanced delivery processing
   */
  private async handleShipmentDelivered(shipmentId: string, companyId: string, userId?: string): Promise<void> {
    // Update actual delivery date
    await db.update(shipments)
      .set({
        actual_delivery: new Date().toISOString().split('T')[0],
        updated_at: new Date()
      })
      .where(and(
        eq(shipments.id, shipmentId),
        eq(shipments.company_id, companyId)
      ))

    // Update sales contract shipping status if linked
    const shipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, shipmentId),
        eq(shipments.company_id, companyId)
      )
    })

    if (shipment?.sales_contract_id) {
      await db.update(salesContracts)
        .set({
          // Add shipping_status field to sales_contracts if needed
          status: "completed",
          updated_at: new Date()
        })
        .where(eq(salesContracts.id, shipment.sales_contract_id))
    }

    // ✅ NEW: Release staging area capacity
    await this.releaseStagingCapacity(shipmentId, companyId)

    // ✅ NEW: Generate delivery confirmation documents
    await this.generateDeliveryDocuments(shipmentId, companyId, userId)

    // ✅ NEW: Trigger delivery notifications
    await this.triggerDeliveryNotifications(shipmentId, companyId, userId)
  }

  // ============================================================================
  // ✅ PHASE 2 ENHANCEMENT: New Workflow Helper Methods
  // ============================================================================

  /**
   * Update shipping location utilization when shipment is shipped
   */
  private async updateShippingLocationUtilization(shipmentId: string, companyId: string): Promise<void> {
    try {
      // Get shipment with location information
      const shipment = await db.query.shipments.findFirst({
        where: and(
          eq(shipments.id, shipmentId),
          eq(shipments.company_id, companyId)
        )
      })

      if (shipment?.pickup_location_id || shipment?.staging_location_id) {
        // In a real system, this would update location utilization tables
        console.log(`Updating location utilization for shipment ${shipmentId}`)
        console.log(`Pickup location: ${shipment.pickup_location_id}`)
        console.log(`Staging location: ${shipment.staging_location_id}`)

        // TODO: Implement actual location utilization updates
        // This would integrate with the location utilization service
      }
    } catch (error) {
      console.error(`Error updating shipping location utilization for ${shipmentId}:`, error)
      // Don't throw - this is not critical for shipment processing
    }
  }

  /**
   * Trigger shipping notifications and downstream workflows
   */
  private async triggerShippingNotifications(shipmentId: string, companyId: string, userId?: string): Promise<void> {
    try {
      console.log(`Triggering shipping notifications for shipment ${shipmentId}`)

      // TODO: Implement notification system
      // - Email notifications to customer
      // - Internal notifications to sales team
      // - Integration with external tracking systems
      // - Update CRM systems

    } catch (error) {
      console.error(`Error triggering shipping notifications for ${shipmentId}:`, error)
      // Don't throw - this is not critical for shipment processing
    }
  }

  /**
   * Release staging area capacity when shipment is delivered
   */
  private async releaseStagingCapacity(shipmentId: string, companyId: string): Promise<void> {
    try {
      console.log(`Releasing staging capacity for shipment ${shipmentId}`)

      // TODO: Implement staging capacity release
      // This would integrate with the shipping location service
      // to free up reserved capacity in staging areas

    } catch (error) {
      console.error(`Error releasing staging capacity for ${shipmentId}:`, error)
      // Don't throw - this is not critical for shipment processing
    }
  }

  /**
   * Generate delivery confirmation documents
   */
  private async generateDeliveryDocuments(shipmentId: string, companyId: string, userId?: string): Promise<void> {
    try {
      console.log(`Generating delivery documents for shipment ${shipmentId}`)

      // TODO: Implement document generation
      // - Delivery confirmation receipt
      // - Proof of delivery (POD)
      // - Customer satisfaction survey
      // - Invoice finalization

    } catch (error) {
      console.error(`Error generating delivery documents for ${shipmentId}:`, error)
      // Don't throw - this is not critical for shipment processing
    }
  }

  /**
   * Trigger delivery notifications and completion workflows
   */
  private async triggerDeliveryNotifications(shipmentId: string, companyId: string, userId?: string): Promise<void> {
    try {
      console.log(`Triggering delivery notifications for shipment ${shipmentId}`)

      // TODO: Implement delivery notification system
      // - Delivery confirmation emails
      // - Customer feedback requests
      // - Internal completion notifications
      // - Performance metrics updates

    } catch (error) {
      console.error(`Error triggering delivery notifications for ${shipmentId}:`, error)
      // Don't throw - this is not critical for shipment processing
    }
  }

  /**
   * ✅ AUTOMATIC INVOICE GENERATION: Generate AR Invoice when shipment is shipped
   * Integrates with existing finance module with zero breaking changes
   */
  private async generateARInvoiceForShipment(shipmentId: string, companyId: string, userId?: string): Promise<void> {
    try {
      console.log(`🧾 Auto-generating AR invoice for shipped shipment: ${shipmentId}`)

      // Check if invoice already exists for this shipment
      const existingInvoices = await db.query.arInvoices.findMany({
        where: eq(arInvoices.company_id, companyId)
      })

      const existingInvoice = existingInvoices.find(inv =>
        inv.notes?.includes(shipmentId)
      )

      if (existingInvoice) {
        console.log(`📋 Invoice already exists for shipment ${shipmentId}: ${existingInvoice.number}`)
        return
      }

      // Use the existing ShippingFinancialIntegration service
      const { ShippingFinancialIntegration } = await import('./shipping-financial-integration')
      const financialIntegration = new ShippingFinancialIntegration({
        companyId,
        userId: userId || 'system'
      })

      // Generate AR invoice automatically
      const invoice = await financialIntegration.generateARInvoiceFromShipment({
        shipmentId,
        autoGenerate: true,
        paymentTerms: "Net 30",
        notes: `Auto-generated from shipment ${shipmentId}`
      })

      console.log(`✅ Successfully generated AR invoice ${invoice.number} for shipment ${shipmentId}`)

    } catch (error) {
      console.error(`❌ Error generating AR invoice for shipment ${shipmentId}:`, error)
      // Don't throw - invoice generation failure shouldn't break shipment processing
      // This maintains zero breaking changes principle
    }
  }
}
