/**
 * Manufacturing ERP - Database-Driven Location Service
 * 
 * Professional location management service that fetches locations from the database
 * instead of hardcoded configuration. Provides compatibility with the existing
 * LocationManager interface while using real database data.
 * 
 * <AUTHOR> ERP System
 * @version 1.0.0 - Database Integration
 */

import { db } from "@/lib/db"
import { locations } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { LocationConfig, LocationType, LocationTypeConfig } from "@/lib/location-config"

/**
 * Database-aware location service
 * Provides the same interface as LocationManager but uses database data
 */
export class DatabaseLocationService {
  
  /**
   * Convert database location to LocationConfig format
   */
  private static convertDbLocationToConfig(dbLocation: any): LocationConfig {
    const typeConfig = LocationTypeConfig[dbLocation.type as LocationType] || {
      icon: '📍',
      color: 'bg-gray-500',
      description: 'Location'
    }

    return {
      id: dbLocation.id,
      name: dbLocation.name,
      displayName: dbLocation.name,
      description: dbLocation.description || typeConfig.description,
      type: dbLocation.type as LocationType,
      capacity: dbLocation.capacity || 0,
      icon: typeConfig.icon,
      color: typeConfig.color,
      attributes: {
        temperatureControlled: dbLocation.is_temperature_controlled || false,
        securityLevel: dbLocation.security_level || 'medium',
        qualityControlRequired: dbLocation.requires_quality_check || false,
        automatedHandling: dbLocation.automated_handling || false,
      },
      hierarchy: {
        plant: 'Main Plant', // Default values for now
        building: 'Building A',
        zone: dbLocation.zone || 'Zone 1',
        level: dbLocation.floor_level || 1,
      },
      flowConnections: [], // Will be populated based on relationships
      isActive: dbLocation.is_active !== false,
      createdAt: new Date(dbLocation.created_at),
      updatedAt: new Date(dbLocation.updated_at),
    }
  }

  /**
   * Get all locations for a company
   */
  static async getAllLocations(companyId: string): Promise<LocationConfig[]> {
    try {
      const dbLocations = await db.query.locations.findMany({
        where: and(
          eq(locations.company_id, companyId),
          eq(locations.is_active, true)
        ),
        orderBy: [locations.name],
      })

      return dbLocations.map(this.convertDbLocationToConfig)
    } catch (error) {
      console.error('Error fetching locations:', error)
      return []
    }
  }

  /**
   * Get locations by type for a company
   */
  static async getLocationsByType(type: LocationType, companyId: string): Promise<LocationConfig[]> {
    try {
      const dbLocations = await db.query.locations.findMany({
        where: and(
          eq(locations.company_id, companyId),
          eq(locations.type, type),
          eq(locations.is_active, true)
        ),
        orderBy: [locations.name],
      })

      return dbLocations.map(this.convertDbLocationToConfig)
    } catch (error) {
      console.error(`Error fetching locations by type ${type}:`, error)
      return []
    }
  }

  /**
   * Get location by ID for a company
   */
  static async getLocationById(locationId: string, companyId: string): Promise<LocationConfig | null> {
    try {
      const dbLocation = await db.query.locations.findFirst({
        where: and(
          eq(locations.id, locationId),
          eq(locations.company_id, companyId)
        ),
      })

      return dbLocation ? this.convertDbLocationToConfig(dbLocation) : null
    } catch (error) {
      console.error(`Error fetching location ${locationId}:`, error)
      return null
    }
  }

  /**
   * Get locations for dropdown components
   */
  static async getLocationsForDropdown(companyId: string): Promise<Array<{ value: string, label: string, description: string }>> {
    const allLocations = await this.getAllLocations(companyId)
    return allLocations.map(location => ({
      value: location.id,
      label: `${location.icon} ${location.displayName}`,
      description: location.description
    }))
  }

  /**
   * Get location for UI display (compatible with existing getLocationForUI function)
   */
  static async getLocationForUI(locationId: string | null | undefined, companyId: string): Promise<{
    id: string
    displayName: string
    icon: string
    color: string
  } | null> {
    if (!locationId) return null

    const location = await this.getLocationById(locationId, companyId)
    if (location) {
      return {
        id: location.id,
        displayName: location.displayName,
        icon: location.icon,
        color: location.color
      }
    }

    // Fallback: Return a default representation
    return {
      id: locationId,
      displayName: locationId,
      icon: '📍',
      color: 'bg-gray-500'
    }
  }
}

/**
 * Helper function to get locations for dropdown (async version)
 * This replaces the synchronous getLocationsForDropdown function
 */
export async function getLocationsForDropdownAsync(companyId: string): Promise<Array<{ value: string, label: string, description: string }>> {
  return DatabaseLocationService.getLocationsForDropdown(companyId)
}

/**
 * Helper function to get location for UI display (async version)
 * This replaces the synchronous getLocationForUI function
 */
export async function getLocationForUIAsync(locationId: string | null | undefined, companyId: string): Promise<{
  id: string
  displayName: string
  icon: string
  color: string
} | null> {
  return DatabaseLocationService.getLocationForUI(locationId, companyId)
}
