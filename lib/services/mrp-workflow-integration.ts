/**
 * Manufacturing ERP - MRP Workflow Integration Service
 * 
 * Professional service for integrating MRP system with existing ERP workflows.
 * Provides seamless data flow between demand forecasting, procurement planning,
 * work orders, inventory management, and sales contracts.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Integration
 */

import { db, uid } from "@/lib/db"
import {
  demandForecasts,
  procurementPlans,
  supplierLeadTimes,
  salesContracts,
  salesContractItems,
  workOrders,
  workOperations,
  stockLots,
  stockTxns,
  rawMaterials,
  products,
  suppliers,
  companies
} from "@/lib/schema-postgres"
import { eq, and, asc, descOrder, sql, gte, lte, isNull, or } from "drizzle-orm"
import { z } from "zod"
import { DemandForecastingService } from "./demand-forecasting"
import { ProcurementPlanningService } from "./procurement-planning"
import { SupplierLeadTimeService } from "./supplier-lead-time"
import { workOrderGenerationService } from "./work-order-generation"

// ✅ PROFESSIONAL: Type definitions for workflow integration
export interface MRPWorkflowTrigger {
  eventType: 'contract_approved' | 'forecast_approved' | 'procurement_approved' | 'work_order_completed' | 'quality_approved'
  entityId: string
  entityType: string
  companyId: string
  triggeredBy: string
  data?: any
}

export interface WorkflowIntegrationResult {
  success: boolean
  triggeredActions: string[]
  generatedEntities: Array<{
    type: string
    id: string
    name: string
  }>
  warnings: string[]
  errors: string[]
}

export interface ContractToForecastMapping {
  contractId: string
  contractNumber: string
  customerId: string
  items: Array<{
    productId: string
    productName: string
    quantity: number
    deliveryDate: string
  }>
  forecastPeriod: string
  confidenceLevel: 'high' | 'medium' | 'low'
}

export interface ForecastToProcurementMapping {
  forecastId: string
  productId: string
  forecastedDemand: number
  materialRequirements: Array<{
    materialId: string
    materialName: string
    requiredQuantity: number
    targetDate: string
    recommendedSupplier?: {
      id: string
      name: string
      leadTime: number
      cost: number
    }
  }>
}

// ✅ PROFESSIONAL: Zod validation schemas
export const workflowTriggerSchema = z.object({
  eventType: z.enum(['contract_approved', 'forecast_approved', 'procurement_approved', 'work_order_completed', 'quality_approved']),
  entityId: z.string().min(1),
  entityType: z.string().min(1),
  companyId: z.string().min(1),
  triggeredBy: z.string().min(1),
  data: z.any().optional(),
})

/**
 * ✅ PROFESSIONAL: MRP Workflow Integration Service
 * Orchestrates data flow between MRP system and existing ERP workflows
 */
export class MRPWorkflowIntegrationService {
  private demandService: DemandForecastingService
  private procurementService: ProcurementPlanningService
  private leadTimeService: SupplierLeadTimeService

  constructor() {
    this.demandService = new DemandForecastingService()
    this.procurementService = new ProcurementPlanningService()
    this.leadTimeService = new SupplierLeadTimeService()
  }

  /**
   * Main workflow trigger handler
   */
  async handleWorkflowTrigger(trigger: MRPWorkflowTrigger): Promise<WorkflowIntegrationResult> {
    try {
      // Validate trigger data
      const validatedTrigger = workflowTriggerSchema.parse(trigger)

      switch (validatedTrigger.eventType) {
        case 'contract_approved':
          return await this.handleContractApproved(validatedTrigger)
        case 'forecast_approved':
          return await this.handleForecastApproved(validatedTrigger)
        case 'procurement_approved':
          return await this.handleProcurementApproved(validatedTrigger)
        case 'work_order_completed':
          return await this.handleWorkOrderCompleted(validatedTrigger)
        case 'quality_approved':
          return await this.handleQualityApproved(validatedTrigger)
        default:
          throw new Error(`Unsupported workflow event type: ${validatedTrigger.eventType}`)
      }
    } catch (error) {
      console.error("Error handling workflow trigger:", error)
      return {
        success: false,
        triggeredActions: [],
        generatedEntities: [],
        warnings: [],
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }

  /**
   * Handle sales contract approval - generate demand forecasts and work orders
   */
  private async handleContractApproved(trigger: MRPWorkflowTrigger): Promise<WorkflowIntegrationResult> {
    const result: WorkflowIntegrationResult = {
      success: true,
      triggeredActions: [],
      generatedEntities: [],
      warnings: [],
      errors: []
    }

    try {
      // 1. Get contract details
      const contract = await db.query.salesContracts.findFirst({
        where: and(
          eq(salesContracts.id, trigger.entityId),
          eq(salesContracts.company_id, trigger.companyId)
        ),
        with: {
          items: {
            with: {
              product: true
            }
          },
          customer: true
        }
      })

      if (!contract) {
        result.errors.push("Sales contract not found")
        result.success = false
        return result
      }

      // 2. Generate demand forecasts for contract items
      const forecastMapping = await this.createContractToForecastMapping(contract)

      for (const item of forecastMapping.items) {
        try {
          const forecastId = await this.demandService.createDemandForecast(
            trigger.companyId,
            {
              productId: item.productId,
              forecastPeriod: forecastMapping.forecastPeriod,
              forecastedDemand: item.quantity.toString(),
              confidenceLevel: forecastMapping.confidenceLevel,
              forecastMethod: 'pipeline',
              baseDataSource: JSON.stringify({
                sourceType: 'sales_contract',
                contractId: contract.id,
                contractNumber: contract.number,
                customerId: contract.customer_id,
                deliveryDate: item.deliveryDate
              }),
              notes: `Auto-generated from approved sales contract ${contract.number}`,
              approvalStatus: 'approved' // Auto-approve contract-based forecasts
            },
            trigger.triggeredBy
          )

          result.generatedEntities.push({
            type: 'demand_forecast',
            id: forecastId,
            name: `Forecast for ${item.productName} (${forecastMapping.forecastPeriod})`
          })

          result.triggeredActions.push(`Generated demand forecast for ${item.productName}`)
        } catch (error) {
          result.warnings.push(`Failed to create forecast for ${item.productName}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }

      // 3. Generate work orders using existing service
      try {
        const workOrderResults = await workOrderGenerationService.generateWorkOrdersForContract(
          trigger.companyId,
          trigger.entityId,
          {
            priority: 'normal',
            autoCreateQualityInspections: true,
            notes: `Generated from MRP workflow integration`
          }
        )

        for (const workOrder of workOrderResults) {
          result.generatedEntities.push({
            type: 'work_order',
            id: workOrder.id,
            name: `Work Order ${workOrder.number}`
          })
        }

        result.triggeredActions.push(`Generated ${workOrderResults.length} work orders`)
      } catch (error) {
        result.warnings.push(`Failed to generate work orders: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }

      // 4. Generate procurement plans for required materials
      try {
        const procurementResults = await this.generateProcurementFromContract(contract, trigger.triggeredBy)

        for (const procurementPlan of procurementResults) {
          result.generatedEntities.push({
            type: 'procurement_plan',
            id: procurementPlan.id,
            name: `Procurement for ${procurementPlan.materialName}`
          })
        }

        result.triggeredActions.push(`Generated ${procurementResults.length} procurement plans`)
      } catch (error) {
        result.warnings.push(`Failed to generate procurement plans: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }

      return result
    } catch (error) {
      console.error("Error in handleContractApproved:", error)
      result.success = false
      result.errors.push(error instanceof Error ? error.message : 'Unknown error')
      return result
    }
  }

  /**
   * Handle demand forecast approval - generate procurement plans
   */
  private async handleForecastApproved(trigger: MRPWorkflowTrigger): Promise<WorkflowIntegrationResult> {
    const result: WorkflowIntegrationResult = {
      success: true,
      triggeredActions: [],
      generatedEntities: [],
      warnings: [],
      errors: []
    }

    try {
      // Get forecast details
      const forecast = await db.query.demandForecasts.findFirst({
        where: and(
          eq(demandForecasts.id, trigger.entityId),
          eq(demandForecasts.company_id, trigger.companyId)
        ),
        with: {
          product: {
            with: {
              bomItems: {
                with: {
                  rawMaterial: true
                }
              }
            }
          }
        }
      })

      if (!forecast) {
        result.errors.push("Demand forecast not found")
        result.success = false
        return result
      }

      // Generate procurement plans for BOM materials
      const procurementMapping = await this.createForecastToProcurementMapping(forecast)

      for (const materialReq of procurementMapping.materialRequirements) {
        try {
          const procurementId = await this.procurementService.createProcurementPlan(
            trigger.companyId,
            {
              rawMaterialId: materialReq.materialId,
              demandForecastId: forecast.id,
              plannedQty: materialReq.requiredQuantity.toString(),
              targetDate: materialReq.targetDate,
              supplierId: materialReq.recommendedSupplier?.id,
              estimatedCost: materialReq.recommendedSupplier?.cost.toString() || '0',
              priority: 'normal',
              status: 'draft',
              notes: `Auto-generated from approved forecast ${forecast.id}`
            },
            trigger.triggeredBy
          )

          result.generatedEntities.push({
            type: 'procurement_plan',
            id: procurementId,
            name: `Procurement for ${materialReq.materialName}`
          })

          result.triggeredActions.push(`Generated procurement plan for ${materialReq.materialName}`)
        } catch (error) {
          result.warnings.push(`Failed to create procurement plan for ${materialReq.materialName}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }

      return result
    } catch (error) {
      console.error("Error in handleForecastApproved:", error)
      result.success = false
      result.errors.push(error instanceof Error ? error.message : 'Unknown error')
      return result
    }
  }

  /**
   * Handle procurement plan approval - update supplier lead times and trigger purchase orders
   */
  private async handleProcurementApproved(trigger: MRPWorkflowTrigger): Promise<WorkflowIntegrationResult> {
    const result: WorkflowIntegrationResult = {
      success: true,
      triggeredActions: ['Procurement plan approved - ready for purchase order generation'],
      generatedEntities: [],
      warnings: [],
      errors: []
    }

    // TODO: Implement purchase order generation integration
    // This would integrate with the existing purchase contract system

    return result
  }

  /**
   * Handle work order completion - update inventory and trigger quality inspections
   */
  private async handleWorkOrderCompleted(trigger: MRPWorkflowTrigger): Promise<WorkflowIntegrationResult> {
    const result: WorkflowIntegrationResult = {
      success: true,
      triggeredActions: ['Work order completed - inventory updated'],
      generatedEntities: [],
      warnings: [],
      errors: []
    }

    // TODO: Implement inventory update integration
    // This would integrate with the existing inventory management system

    return result
  }

  /**
   * Handle quality approval - clear for shipping
   */
  private async handleQualityApproved(trigger: MRPWorkflowTrigger): Promise<WorkflowIntegrationResult> {
    const result: WorkflowIntegrationResult = {
      success: true,
      triggeredActions: ['Quality approved - cleared for shipping'],
      generatedEntities: [],
      warnings: [],
      errors: []
    }

    // TODO: Implement shipping integration
    // This would integrate with the existing shipping management system

    return result
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Create mapping from sales contract to demand forecast
   */
  private async createContractToForecastMapping(contract: any): Promise<ContractToForecastMapping> {
    // Determine forecast period based on delivery date
    const deliveryDate = new Date(contract.delivery_date || Date.now())
    const currentDate = new Date()
    const monthsDiff = (deliveryDate.getFullYear() - currentDate.getFullYear()) * 12 +
      (deliveryDate.getMonth() - currentDate.getMonth())

    let forecastPeriod: string
    let confidenceLevel: 'high' | 'medium' | 'low'

    if (monthsDiff <= 1) {
      forecastPeriod = `${deliveryDate.getFullYear()}-${String(deliveryDate.getMonth() + 1).padStart(2, '0')}`
      confidenceLevel = 'high' // Short-term contracts have high confidence
    } else if (monthsDiff <= 3) {
      const quarter = Math.ceil((deliveryDate.getMonth() + 1) / 3)
      forecastPeriod = `${deliveryDate.getFullYear()}-Q${quarter}`
      confidenceLevel = 'high'
    } else {
      const quarter = Math.ceil((deliveryDate.getMonth() + 1) / 3)
      forecastPeriod = `${deliveryDate.getFullYear()}-Q${quarter}`
      confidenceLevel = 'medium' // Longer-term contracts have medium confidence
    }

    return {
      contractId: contract.id,
      contractNumber: contract.number,
      customerId: contract.customer_id,
      items: contract.items.map((item: any) => ({
        productId: item.product_id,
        productName: item.product?.name || 'Unknown Product',
        quantity: parseInt(item.qty),
        deliveryDate: contract.delivery_date || new Date().toISOString().split('T')[0]
      })),
      forecastPeriod,
      confidenceLevel
    }
  }

  /**
   * Create mapping from demand forecast to procurement requirements
   */
  private async createForecastToProcurementMapping(forecast: any): Promise<ForecastToProcurementMapping> {
    const materialRequirements = []
    const forecastedDemand = parseInt(forecast.forecasted_demand)

    // Calculate material requirements from BOM
    if (forecast.product?.bomItems) {
      for (const bomItem of forecast.product.bomItems) {
        const requiredQuantity = parseFloat(bomItem.qty_required) * forecastedDemand

        // Get recommended supplier for this material
        const recommendedSupplier = await this.getRecommendedSupplier(
          forecast.company_id,
          bomItem.raw_material_id
        )

        // Calculate target date (forecast period minus lead time)
        const targetDate = this.calculateTargetDate(
          forecast.forecast_period,
          recommendedSupplier?.leadTime || 30
        )

        materialRequirements.push({
          materialId: bomItem.raw_material_id,
          materialName: bomItem.rawMaterial?.name || 'Unknown Material',
          requiredQuantity,
          targetDate,
          recommendedSupplier
        })
      }
    }

    return {
      forecastId: forecast.id,
      productId: forecast.product_id,
      forecastedDemand,
      materialRequirements
    }
  }

  /**
   * Get recommended supplier for a material
   */
  private async getRecommendedSupplier(companyId: string, materialId: string): Promise<{
    id: string
    name: string
    leadTime: number
    cost: number
  } | null> {
    try {
      const supplierLeadTime = await db.query.supplierLeadTimes.findFirst({
        where: and(
          eq(supplierLeadTimes.company_id, companyId),
          eq(supplierLeadTimes.raw_material_id, materialId),
          eq(supplierLeadTimes.reliability, 'excellent')
        ),
        with: {
          supplier: true
        },
        orderBy: [asc(supplierLeadTimes.lead_time_days)]
      })

      if (supplierLeadTime) {
        return {
          id: supplierLeadTime.supplier_id,
          name: supplierLeadTime.supplier?.name || 'Unknown Supplier',
          leadTime: parseInt(supplierLeadTime.lead_time_days),
          cost: parseFloat(supplierLeadTime.unit_cost || '0')
        }
      }

      // Fallback to any supplier for this material
      const fallbackSupplier = await db.query.supplierLeadTimes.findFirst({
        where: and(
          eq(supplierLeadTimes.company_id, companyId),
          eq(supplierLeadTimes.raw_material_id, materialId)
        ),
        with: {
          supplier: true
        },
        orderBy: [asc(supplierLeadTimes.lead_time_days)]
      })

      if (fallbackSupplier) {
        return {
          id: fallbackSupplier.supplier_id,
          name: fallbackSupplier.supplier?.name || 'Unknown Supplier',
          leadTime: parseInt(fallbackSupplier.lead_time_days),
          cost: parseFloat(fallbackSupplier.unit_cost || '0')
        }
      }

      return null
    } catch (error) {
      console.error("Error getting recommended supplier:", error)
      return null
    }
  }

  /**
   * Calculate target date for procurement based on forecast period and lead time
   */
  private calculateTargetDate(forecastPeriod: string, leadTimeDays: number): string {
    try {
      let targetDate: Date

      if (forecastPeriod.includes('Q')) {
        // Quarterly forecast (e.g., "2025-Q1")
        const [year, quarter] = forecastPeriod.split('-Q')
        const quarterStartMonth = (parseInt(quarter) - 1) * 3
        targetDate = new Date(parseInt(year), quarterStartMonth, 1)
      } else if (forecastPeriod.includes('-')) {
        // Monthly forecast (e.g., "2025-03")
        const [year, month] = forecastPeriod.split('-')
        targetDate = new Date(parseInt(year), parseInt(month) - 1, 1)
      } else {
        // Default to current date
        targetDate = new Date()
      }

      // Subtract lead time to get procurement target date
      targetDate.setDate(targetDate.getDate() - leadTimeDays)

      return targetDate.toISOString().split('T')[0]
    } catch (error) {
      console.error("Error calculating target date:", error)
      // Fallback to 30 days from now
      const fallbackDate = new Date()
      fallbackDate.setDate(fallbackDate.getDate() + 30)
      return fallbackDate.toISOString().split('T')[0]
    }
  }

  /**
   * Generate procurement plans from contract BOM requirements
   */
  private async generateProcurementFromContract(contract: any, createdBy: string): Promise<Array<{
    id: string
    materialName: string
  }>> {
    const procurementPlans = []

    for (const item of contract.items) {
      if (item.product?.bomItems) {
        for (const bomItem of item.product.bomItems) {
          const requiredQuantity = parseFloat(bomItem.qty_required) * parseInt(item.qty)

          // Get recommended supplier
          const recommendedSupplier = await this.getRecommendedSupplier(
            contract.company_id,
            bomItem.raw_material_id
          )

          // Calculate target date based on contract delivery date
          const contractDeliveryDate = new Date(contract.delivery_date || Date.now())
          const leadTime = recommendedSupplier?.leadTime || 30
          const targetDate = new Date(contractDeliveryDate)
          targetDate.setDate(targetDate.getDate() - leadTime - 7) // 7 days buffer

          try {
            const procurementId = await this.procurementService.createProcurementPlan(
              contract.company_id,
              {
                rawMaterialId: bomItem.raw_material_id,
                demandForecastId: null, // Direct from contract, not forecast
                plannedQty: requiredQuantity.toString(),
                targetDate: targetDate.toISOString().split('T')[0],
                supplierId: recommendedSupplier?.id,
                estimatedCost: (requiredQuantity * (recommendedSupplier?.cost || 0)).toString(),
                priority: 'normal',
                status: 'draft',
                notes: `Auto-generated from contract ${contract.number} for ${item.product?.name}`
              },
              createdBy
            )

            procurementPlans.push({
              id: procurementId,
              materialName: bomItem.rawMaterial?.name || 'Unknown Material'
            })
          } catch (error) {
            console.error(`Error creating procurement plan for ${bomItem.rawMaterial?.name}:`, error)
          }
        }
      }
    }

    return procurementPlans
  }
}

export const mrpWorkflowIntegrationService = new MRPWorkflowIntegrationService()
