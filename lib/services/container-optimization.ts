/**
 * Manufacturing ERP - Container Load Optimization Engine
 * 
 * Professional service for container load optimization with weight/volume constraints,
 * cost optimization, and export shipping efficiency for international trade
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { db, uid } from "@/lib/db"
import {
  rawMaterials,
  products,
  procurementPlans,
  companies
} from "@/lib/schema-postgres"
import { eq, and, asc, desc, sql, gte, lte, isNull, or } from "drizzle-orm"
import { z } from "zod"

// ✅ PROFESSIONAL: Type definitions for enterprise-grade container optimization
export interface ContainerSpecifications {
  "20ft": ContainerSpec
  "40ft": ContainerSpec
  "40ft-hc": ContainerSpec
  "45ft": ContainerSpec
}

export interface ContainerSpec {
  name: string
  maxWeight: number // kg
  maxVolume: number // m³
  maxLength: number // m
  maxWidth: number // m
  maxHeight: number // m
  baseCost: number // USD
  costPerKm: number // USD per km
  availabilityFactor: number // 0-1, availability in market
}

export interface CargoItem {
  id: string
  name: string
  sku: string
  quantity: number
  unitWeight: number // kg per unit
  unitVolume: number // m³ per unit
  unitValue: number // USD per unit
  dimensions?: {
    length: number // m
    width: number // m
    height: number // m
  }
  stackable: boolean
  fragile: boolean
  hazardous: boolean
  temperatureControlled: boolean
  priority: "low" | "normal" | "high" | "urgent"
}

export interface OptimizationConstraints {
  maxContainers?: number
  preferredContainerTypes?: ("20ft" | "40ft" | "40ft-hc" | "45ft")[]
  maxTotalCost?: number
  maxTotalWeight?: number
  maxTotalVolume?: number
  requireTemperatureControl?: boolean
  allowMixedCargo?: boolean
  minimumUtilization?: number // 0-1
  maxLoadingTime?: number // hours
}

export interface ContainerLoadPlan {
  containerId: string
  containerType: "20ft" | "40ft" | "40ft-hc" | "45ft"
  items: LoadedItem[]
  totalWeight: number
  totalVolume: number
  totalValue: number
  weightUtilization: number
  volumeUtilization: number
  overallUtilization: number
  loadingSequence: LoadingInstruction[]
  estimatedCost: number
  estimatedLoadingTime: number
}

export interface LoadedItem {
  cargoItemId: string
  name: string
  sku: string
  loadedQuantity: number
  totalWeight: number
  totalVolume: number
  totalValue: number
  position: {
    x: number
    y: number
    z: number
  }
  stackLevel: number
}

export interface LoadingInstruction {
  sequence: number
  itemId: string
  itemName: string
  quantity: number
  position: string
  specialInstructions?: string[]
}

export interface OptimizationResult {
  containers: ContainerLoadPlan[]
  unloadedItems: CargoItem[]
  summary: {
    totalContainers: number
    totalWeight: number
    totalVolume: number
    totalValue: number
    totalCost: number
    averageUtilization: number
    estimatedShippingTime: number
    co2Emissions: number
  }
  efficiency: {
    weightEfficiency: number
    volumeEfficiency: number
    costEfficiency: number
    overallScore: number
  }
  recommendations: string[]
  warnings: string[]
}

// ✅ PROFESSIONAL: Zod validation schemas
export const optimizeContainerLoadSchema = z.object({
  items: z.array(z.object({
    id: z.string(),
    name: z.string(),
    sku: z.string(),
    quantity: z.number().min(1),
    unitWeight: z.number().min(0.001),
    unitVolume: z.number().min(0.000001),
    unitValue: z.number().min(0),
    stackable: z.boolean().default(true),
    fragile: z.boolean().default(false),
    hazardous: z.boolean().default(false),
    temperatureControlled: z.boolean().default(false),
    priority: z.enum(["low", "normal", "high", "urgent"]).default("normal"),
  })).min(1, "At least one item is required"),
  constraints: z.object({
    maxContainers: z.number().optional(),
    preferredContainerTypes: z.array(z.enum(["20ft", "40ft", "40ft-hc", "45ft"])).optional(),
    maxTotalCost: z.number().optional(),
    requireTemperatureControl: z.boolean().default(false),
    allowMixedCargo: z.boolean().default(true),
    minimumUtilization: z.number().min(0).max(1).default(0.7),
  }).optional(),
})

/**
 * ✅ PROFESSIONAL: Container Load Optimization Engine
 * Enterprise-grade container optimization with 3D bin packing and cost optimization
 */
export class ContainerOptimizationService {

  // Container specifications based on international standards
  private readonly containerSpecs: ContainerSpecifications = {
    "20ft": {
      name: "20ft Standard Container",
      maxWeight: 28080, // kg (including tare weight)
      maxVolume: 33.2, // m³
      maxLength: 5.9,
      maxWidth: 2.35,
      maxHeight: 2.39,
      baseCost: 1200,
      costPerKm: 0.8,
      availabilityFactor: 0.9,
    },
    "40ft": {
      name: "40ft Standard Container",
      maxWeight: 26500, // kg (including tare weight)
      maxVolume: 67.7, // m³
      maxLength: 12.03,
      maxWidth: 2.35,
      maxHeight: 2.39,
      baseCost: 1800,
      costPerKm: 1.2,
      availabilityFactor: 0.95,
    },
    "40ft-hc": {
      name: "40ft High Cube Container",
      maxWeight: 26500, // kg
      maxVolume: 76.4, // m³
      maxLength: 12.03,
      maxWidth: 2.35,
      maxHeight: 2.70,
      baseCost: 2000,
      costPerKm: 1.3,
      availabilityFactor: 0.85,
    },
    "45ft": {
      name: "45ft High Cube Container",
      maxWeight: 26500, // kg
      maxVolume: 86.0, // m³
      maxLength: 13.56,
      maxWidth: 2.35,
      maxHeight: 2.70,
      baseCost: 2200,
      costPerKm: 1.4,
      availabilityFactor: 0.7,
    },
  }

  /**
   * Optimize container loading for maximum efficiency
   */
  async optimizeContainerLoad(
    companyId: string,
    items: CargoItem[],
    constraints?: OptimizationConstraints,
    destination?: {
      country: string
      port: string
      distance: number // km
    }
  ): Promise<OptimizationResult> {
    try {
      // 1. Validate and prepare items
      const validatedItems = this.validateAndPrepareItems(items)

      // 2. Apply constraints and filters
      const applicableContainerTypes = this.getApplicableContainerTypes(constraints)

      // 3. Sort items by optimization priority
      const sortedItems = this.sortItemsByPriority(validatedItems)

      // 4. Run optimization algorithm
      const optimizationResult = await this.runOptimizationAlgorithm(
        sortedItems,
        applicableContainerTypes,
        constraints,
        destination
      )

      // 5. Generate loading instructions
      const containersWithInstructions = this.generateLoadingInstructions(optimizationResult.containers)

      // 6. Calculate efficiency metrics
      const efficiency = this.calculateEfficiencyMetrics(optimizationResult)

      // 7. Generate recommendations and warnings
      const { recommendations, warnings } = this.generateRecommendationsAndWarnings(
        optimizationResult,
        constraints
      )

      return {
        containers: containersWithInstructions,
        unloadedItems: optimizationResult.unloadedItems,
        summary: optimizationResult.summary,
        efficiency,
        recommendations,
        warnings,
      }
    } catch (error) {
      console.error("Error optimizing container load:", error)
      throw new Error(`Failed to optimize container load: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get optimal container type for single item type
   */
  async getOptimalContainerForItem(
    companyId: string,
    item: CargoItem,
    quantity?: number
  ): Promise<{
    containerType: keyof ContainerSpecifications
    maxQuantity: number
    utilization: number
    cost: number
    recommendations: string[]
  }> {
    try {
      const actualQuantity = quantity || item.quantity
      const totalWeight = item.unitWeight * actualQuantity
      const totalVolume = item.unitVolume * actualQuantity

      let bestOption: any = null
      let bestScore = 0

      for (const [containerType, spec] of Object.entries(this.containerSpecs)) {
        // Check if item fits in container
        const maxQtyByWeight = Math.floor(spec.maxWeight / item.unitWeight)
        const maxQtyByVolume = Math.floor(spec.maxVolume / item.unitVolume)
        const maxQty = Math.min(maxQtyByWeight, maxQtyByVolume)

        if (maxQty >= actualQuantity) {
          const weightUtilization = totalWeight / spec.maxWeight
          const volumeUtilization = totalVolume / spec.maxVolume
          const utilization = Math.max(weightUtilization, volumeUtilization)

          // Calculate efficiency score (higher utilization and availability, lower cost)
          const score = (utilization * 0.5) + (spec.availabilityFactor * 0.3) + ((2500 - spec.baseCost) / 2500 * 0.2)

          if (score > bestScore) {
            bestScore = score
            bestOption = {
              containerType: containerType as keyof ContainerSpecifications,
              maxQuantity: maxQty,
              utilization,
              cost: spec.baseCost,
              spec,
            }
          }
        }
      }

      if (!bestOption) {
        throw new Error("No suitable container found for the specified item and quantity")
      }

      // Generate recommendations
      const recommendations = []
      if (bestOption.utilization < 0.7) {
        recommendations.push("Consider increasing quantity to improve container utilization")
      }
      if (bestOption.utilization > 0.95) {
        recommendations.push("Container utilization is very high - consider weight distribution")
      }
      if (bestOption.spec.availabilityFactor < 0.8) {
        recommendations.push("Container type has limited availability - consider alternatives")
      }

      return {
        containerType: bestOption.containerType,
        maxQuantity: bestOption.maxQuantity,
        utilization: bestOption.utilization,
        cost: bestOption.cost,
        recommendations,
      }
    } catch (error) {
      console.error("Error getting optimal container for item:", error)
      throw new Error("Failed to determine optimal container")
    }
  }

  /**
   * Calculate shipping cost estimate
   */
  async calculateShippingCost(
    containers: ContainerLoadPlan[],
    destination: {
      country: string
      port: string
      distance: number
    },
    options?: {
      includeInsurance?: boolean
      includeCustoms?: boolean
      expedited?: boolean
    }
  ): Promise<{
    containerCosts: { containerId: string; baseCost: number; transportCost: number; totalCost: number }[]
    additionalCosts: { type: string; cost: number; description: string }[]
    totalCost: number
    breakdown: {
      containerBaseCost: number
      transportCost: number
      insuranceCost: number
      customsCost: number
      expeditedCost: number
      totalCost: number
    }
  }> {
    try {
      const containerCosts = containers.map(container => {
        const spec = this.containerSpecs[container.containerType]
        const baseCost = spec.baseCost
        const transportCost = spec.costPerKm * destination.distance
        const totalCost = baseCost + transportCost

        return {
          containerId: container.containerId,
          baseCost,
          transportCost,
          totalCost,
        }
      })

      const additionalCosts = []
      let insuranceCost = 0
      let customsCost = 0
      let expeditedCost = 0

      if (options?.includeInsurance) {
        const totalValue = containers.reduce((sum, c) => sum + c.totalValue, 0)
        insuranceCost = totalValue * 0.005 // 0.5% of cargo value
        additionalCosts.push({
          type: "insurance",
          cost: insuranceCost,
          description: "Cargo insurance (0.5% of value)",
        })
      }

      if (options?.includeCustoms) {
        customsCost = containers.length * 150 // $150 per container for customs
        additionalCosts.push({
          type: "customs",
          cost: customsCost,
          description: "Customs clearance fees",
        })
      }

      if (options?.expedited) {
        expeditedCost = containers.length * 300 // $300 per container for expedited
        additionalCosts.push({
          type: "expedited",
          cost: expeditedCost,
          description: "Expedited shipping surcharge",
        })
      }

      const containerBaseCost = containerCosts.reduce((sum, c) => sum + c.baseCost, 0)
      const transportCost = containerCosts.reduce((sum, c) => sum + c.transportCost, 0)
      const totalCost = containerBaseCost + transportCost + insuranceCost + customsCost + expeditedCost

      return {
        containerCosts,
        additionalCosts,
        totalCost,
        breakdown: {
          containerBaseCost,
          transportCost,
          insuranceCost,
          customsCost,
          expeditedCost,
          totalCost,
        },
      }
    } catch (error) {
      console.error("Error calculating shipping cost:", error)
      throw new Error("Failed to calculate shipping cost")
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Validate and prepare items for optimization
   */
  private validateAndPrepareItems(items: CargoItem[]): CargoItem[] {
    return items.map(item => {
      // Validate required fields
      if (!item.id || !item.name || item.quantity <= 0) {
        throw new Error(`Invalid item: ${item.name || 'Unknown'}`)
      }

      // Set default dimensions if not provided
      if (!item.dimensions && item.unitVolume > 0) {
        const cubeRoot = Math.cbrt(item.unitVolume)
        item.dimensions = {
          length: cubeRoot,
          width: cubeRoot,
          height: cubeRoot,
        }
      }

      return item
    })
  }

  /**
   * Get applicable container types based on constraints
   */
  private getApplicableContainerTypes(constraints?: OptimizationConstraints): (keyof ContainerSpecifications)[] {
    if (constraints?.preferredContainerTypes) {
      return constraints.preferredContainerTypes
    }

    // Default to all container types, sorted by efficiency
    return ["40ft-hc", "40ft", "20ft", "45ft"]
  }

  /**
   * Sort items by optimization priority
   */
  private sortItemsByPriority(items: CargoItem[]): CargoItem[] {
    const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 }

    return items.sort((a, b) => {
      // First by priority
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
      if (priorityDiff !== 0) return priorityDiff

      // Then by value density (value per volume)
      const aValueDensity = a.unitValue / a.unitVolume
      const bValueDensity = b.unitValue / b.unitVolume
      if (bValueDensity !== aValueDensity) return bValueDensity - aValueDensity

      // Finally by weight (lighter items first for better stacking)
      return a.unitWeight - b.unitWeight
    })
  }

  /**
   * Run the main optimization algorithm
   */
  private async runOptimizationAlgorithm(
    items: CargoItem[],
    containerTypes: (keyof ContainerSpecifications)[],
    constraints?: OptimizationConstraints,
    destination?: { country: string; port: string; distance: number }
  ): Promise<{
    containers: ContainerLoadPlan[]
    unloadedItems: CargoItem[]
    summary: OptimizationResult['summary']
  }> {
    const containers: ContainerLoadPlan[] = []
    const unloadedItems: CargoItem[] = []
    const remainingItems = [...items]

    let containerCounter = 1

    while (remainingItems.length > 0 && (!constraints?.maxContainers || containers.length < constraints.maxContainers)) {
      // Find best container type for remaining items
      const bestContainer = this.findBestContainerForItems(remainingItems, containerTypes, constraints)

      if (!bestContainer) {
        // No suitable container found, move remaining items to unloaded
        unloadedItems.push(...remainingItems)
        break
      }

      // Create container load plan
      const containerPlan: ContainerLoadPlan = {
        containerId: `CONT-${containerCounter.toString().padStart(3, '0')}`,
        containerType: bestContainer.containerType,
        items: bestContainer.loadedItems,
        totalWeight: bestContainer.totalWeight,
        totalVolume: bestContainer.totalVolume,
        totalValue: bestContainer.totalValue,
        weightUtilization: bestContainer.totalWeight / this.containerSpecs[bestContainer.containerType].maxWeight,
        volumeUtilization: bestContainer.totalVolume / this.containerSpecs[bestContainer.containerType].maxVolume,
        overallUtilization: Math.max(
          bestContainer.totalWeight / this.containerSpecs[bestContainer.containerType].maxWeight,
          bestContainer.totalVolume / this.containerSpecs[bestContainer.containerType].maxVolume
        ),
        loadingSequence: [], // Will be generated later
        estimatedCost: this.containerSpecs[bestContainer.containerType].baseCost,
        estimatedLoadingTime: this.calculateLoadingTime(bestContainer.loadedItems),
      }

      containers.push(containerPlan)

      // Remove loaded items from remaining items
      for (const loadedItem of bestContainer.loadedItems) {
        const itemIndex = remainingItems.findIndex(item => item.id === loadedItem.cargoItemId)
        if (itemIndex !== -1) {
          const remainingQty = remainingItems[itemIndex].quantity - loadedItem.loadedQuantity
          if (remainingQty > 0) {
            remainingItems[itemIndex].quantity = remainingQty
          } else {
            remainingItems.splice(itemIndex, 1)
          }
        }
      }

      containerCounter++
    }

    // Add any remaining items to unloaded
    unloadedItems.push(...remainingItems)

    // Calculate summary
    const summary = {
      totalContainers: containers.length,
      totalWeight: containers.reduce((sum, c) => sum + c.totalWeight, 0),
      totalVolume: containers.reduce((sum, c) => sum + c.totalVolume, 0),
      totalValue: containers.reduce((sum, c) => sum + c.totalValue, 0),
      totalCost: containers.reduce((sum, c) => sum + c.estimatedCost, 0),
      averageUtilization: containers.reduce((sum, c) => sum + c.overallUtilization, 0) / containers.length,
      estimatedShippingTime: destination ? this.calculateShippingTime(destination.distance) : 0,
      co2Emissions: this.calculateCO2Emissions(containers, destination?.distance || 0),
    }

    return { containers, unloadedItems, summary }
  }

  /**
   * Find best container type and loading for given items
   */
  private findBestContainerForItems(
    items: CargoItem[],
    containerTypes: (keyof ContainerSpecifications)[],
    constraints?: OptimizationConstraints
  ): {
    containerType: keyof ContainerSpecifications
    loadedItems: LoadedItem[]
    totalWeight: number
    totalVolume: number
    totalValue: number
  } | null {
    let bestOption: any = null
    let bestScore = 0

    for (const containerType of containerTypes) {
      const spec = this.containerSpecs[containerType]
      const loadResult = this.packItemsIntoContainer(items, spec, constraints)

      if (loadResult.loadedItems.length > 0) {
        // Calculate efficiency score
        const utilization = Math.max(
          loadResult.totalWeight / spec.maxWeight,
          loadResult.totalVolume / spec.maxVolume
        )

        const valueEfficiency = loadResult.totalValue / spec.baseCost
        const score = (utilization * 0.6) + (valueEfficiency * 0.3) + (spec.availabilityFactor * 0.1)

        if (score > bestScore && utilization >= (constraints?.minimumUtilization || 0.7)) {
          bestScore = score
          bestOption = {
            containerType,
            ...loadResult,
          }
        }
      }
    }

    return bestOption
  }

  /**
   * Pack items into a specific container using bin packing algorithm
   */
  private packItemsIntoContainer(
    items: CargoItem[],
    containerSpec: ContainerSpec,
    constraints?: OptimizationConstraints
  ): {
    loadedItems: LoadedItem[]
    totalWeight: number
    totalVolume: number
    totalValue: number
  } {
    const loadedItems: LoadedItem[] = []
    let totalWeight = 0
    let totalVolume = 0
    let totalValue = 0

    // Simple first-fit decreasing algorithm
    // TODO: Implement more sophisticated 3D bin packing algorithm

    for (const item of items) {
      let loadableQty = item.quantity

      // Check weight constraint
      const maxQtyByWeight = Math.floor((containerSpec.maxWeight - totalWeight) / item.unitWeight)
      loadableQty = Math.min(loadableQty, maxQtyByWeight)

      // Check volume constraint
      const maxQtyByVolume = Math.floor((containerSpec.maxVolume - totalVolume) / item.unitVolume)
      loadableQty = Math.min(loadableQty, maxQtyByVolume)

      if (loadableQty > 0) {
        const itemWeight = item.unitWeight * loadableQty
        const itemVolume = item.unitVolume * loadableQty
        const itemValue = item.unitValue * loadableQty

        loadedItems.push({
          cargoItemId: item.id,
          name: item.name,
          sku: item.sku,
          loadedQuantity: loadableQty,
          totalWeight: itemWeight,
          totalVolume: itemVolume,
          totalValue: itemValue,
          position: { x: 0, y: 0, z: 0 }, // TODO: Calculate actual 3D position
          stackLevel: 1, // TODO: Calculate actual stack level
        })

        totalWeight += itemWeight
        totalVolume += itemVolume
        totalValue += itemValue
      }
    }

    return { loadedItems, totalWeight, totalVolume, totalValue }
  }

  /**
   * Generate loading instructions for containers
   */
  private generateLoadingInstructions(containers: ContainerLoadPlan[]): ContainerLoadPlan[] {
    return containers.map(container => {
      const loadingSequence: LoadingInstruction[] = []
      let sequence = 1

      // Sort items by loading priority (heavy items first, fragile items last)
      const sortedItems = container.items.sort((a, b) => {
        // Heavy items first
        const weightDiff = b.totalWeight - a.totalWeight
        if (weightDiff !== 0) return weightDiff

        // Non-fragile items first
        const aItem = container.items.find(item => item.cargoItemId === a.cargoItemId)
        const bItem = container.items.find(item => item.cargoItemId === b.cargoItemId)
        // TODO: Access fragile property from original cargo items

        return 0
      })

      for (const item of sortedItems) {
        const instructions = []

        if (item.totalWeight > 1000) {
          instructions.push("Use mechanical lifting equipment")
        }
        if (item.stackLevel > 1) {
          instructions.push("Stack carefully on lower items")
        }

        loadingSequence.push({
          sequence: sequence++,
          itemId: item.cargoItemId,
          itemName: item.name,
          quantity: item.loadedQuantity,
          position: `Position ${item.position.x},${item.position.y},${item.position.z}`,
          specialInstructions: instructions.length > 0 ? instructions : undefined,
        })
      }

      return {
        ...container,
        loadingSequence,
      }
    })
  }

  /**
   * Calculate efficiency metrics
   */
  private calculateEfficiencyMetrics(result: { containers: ContainerLoadPlan[]; summary: any }): OptimizationResult['efficiency'] {
    const totalCapacityWeight = result.containers.reduce((sum, c) =>
      sum + this.containerSpecs[c.containerType].maxWeight, 0
    )
    const totalCapacityVolume = result.containers.reduce((sum, c) =>
      sum + this.containerSpecs[c.containerType].maxVolume, 0
    )
    const totalCapacityCost = result.containers.reduce((sum, c) =>
      sum + this.containerSpecs[c.containerType].baseCost, 0
    )

    const weightEfficiency = result.summary.totalWeight / totalCapacityWeight
    const volumeEfficiency = result.summary.totalVolume / totalCapacityVolume
    const costEfficiency = result.summary.totalValue / totalCapacityCost
    const overallScore = (weightEfficiency * 0.4) + (volumeEfficiency * 0.4) + (costEfficiency * 0.2)

    return {
      weightEfficiency,
      volumeEfficiency,
      costEfficiency,
      overallScore,
    }
  }

  /**
   * Generate recommendations and warnings
   */
  private generateRecommendationsAndWarnings(
    result: { containers: ContainerLoadPlan[]; unloadedItems: CargoItem[] },
    constraints?: OptimizationConstraints
  ): { recommendations: string[]; warnings: string[] } {
    const recommendations: string[] = []
    const warnings: string[] = []

    // Check utilization
    const avgUtilization = result.containers.reduce((sum, c) => sum + c.overallUtilization, 0) / result.containers.length
    if (avgUtilization < 0.7) {
      recommendations.push("Consider consolidating shipments to improve container utilization")
    }
    if (avgUtilization > 0.95) {
      warnings.push("Very high container utilization - ensure proper weight distribution")
    }

    // Check unloaded items
    if (result.unloadedItems.length > 0) {
      warnings.push(`${result.unloadedItems.length} items could not be loaded`)
      recommendations.push("Consider using additional containers or different container types")
    }

    // Check container mix
    const containerTypes = new Set(result.containers.map(c => c.containerType))
    if (containerTypes.size > 2) {
      recommendations.push("Consider standardizing container types for operational efficiency")
    }

    return { recommendations, warnings }
  }

  /**
   * Calculate estimated loading time
   */
  private calculateLoadingTime(items: LoadedItem[]): number {
    // Base time: 30 minutes per container + 2 minutes per item + weight factor
    const baseTime = 30 // minutes
    const itemTime = items.length * 2 // minutes
    const weightFactor = items.reduce((sum, item) => sum + item.totalWeight, 0) / 1000 * 0.5 // minutes per ton

    return baseTime + itemTime + weightFactor
  }

  /**
   * Calculate shipping time based on distance
   */
  private calculateShippingTime(distance: number): number {
    // Simplified calculation: assume 500 km/day average speed
    return Math.ceil(distance / 500)
  }

  /**
   * Calculate CO2 emissions
   */
  private calculateCO2Emissions(containers: ContainerLoadPlan[], distance: number): number {
    // Simplified calculation: 0.05 kg CO2 per km per container
    return containers.length * distance * 0.05
  }
}
