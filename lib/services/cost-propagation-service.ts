/**
 * Manufacturing ERP - Cost Propagation Service
 * Professional service for automatic cost recalculation across the manufacturing workflow
 * Maintains cost accuracy when material prices or BOM changes occur
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0
 */

import { db } from "@/lib/db"
import {
  rawMaterials,
  rawMaterialLots,
  billOfMaterials,
  workOrders,
  products,
  materialConsumption,
} from "@/lib/schema-postgres"
import { eq, and, sql, avg, sum } from "drizzle-orm"
import { z } from "zod"

// ✅ PROFESSIONAL: Type definitions
export interface CostPropagationContext {
  companyId: string
  userId: string
  triggeredBy: string // What triggered the cost update
  reason: string // Human-readable reason for the update
}

export interface MaterialCostUpdate {
  materialId: string
  materialName: string
  materialSku: string
  previousCost: number
  newCost: number
  costChange: number
  costChangePercent: number
}

export interface ProductCostUpdate {
  productId: string
  productName: string
  productSku: string
  previousCost: number
  newCost: number
  costChange: number
  affectedWorkOrders: number
}

export interface CostPropagationResult {
  success: boolean
  materialUpdates: MaterialCostUpdate[]
  productUpdates: ProductCostUpdate[]
  workOrderUpdates: Array<{
    workOrderId: string
    workOrderNumber: string
    previousCost: number
    newCost: number
    costChange: number
  }>
  totalImpactedRecords: number
  warnings: string[]
  errors: string[]
}

// ✅ PROFESSIONAL: Validation schema
const costPropagationContextSchema = z.object({
  companyId: z.string().min(1, "Company ID is required"),
  userId: z.string().min(1, "User ID is required"),
  triggeredBy: z.string().min(1, "Trigger source is required"),
  reason: z.string().min(1, "Reason is required"),
})

export class CostPropagationService {
  private context: CostPropagationContext

  constructor(context: CostPropagationContext) {
    // ✅ PROFESSIONAL: Validate input context
    this.context = costPropagationContextSchema.parse(context)
  }

  /**
   * Main method: Propagate cost changes when raw material costs are updated
   */
  async propagateRawMaterialCostChanges(materialId: string): Promise<CostPropagationResult> {
    try {
      console.log(`💰 Starting cost propagation for material: ${materialId}`)

      // Step 1: Calculate new average cost for the material
      const materialCostUpdate = await this.calculateMaterialAverageCost(materialId)
      
      if (!materialCostUpdate) {
        return {
          success: true,
          materialUpdates: [],
          productUpdates: [],
          workOrderUpdates: [],
          totalImpactedRecords: 0,
          warnings: ["No cost update needed - material not found or no lots available"],
          errors: []
        }
      }

      // Step 2: Update products that use this material in their BOM
      const productUpdates = await this.updateProductCosts(materialId, materialCostUpdate)

      // Step 3: Update active work orders that use this material
      const workOrderUpdates = await this.updateWorkOrderCosts(materialId, materialCostUpdate)

      const totalImpacted = 1 + productUpdates.length + workOrderUpdates.length

      console.log(`✅ Cost propagation completed. Impacted ${totalImpacted} records`)

      return {
        success: true,
        materialUpdates: [materialCostUpdate],
        productUpdates,
        workOrderUpdates,
        totalImpactedRecords: totalImpacted,
        warnings: [],
        errors: []
      }

    } catch (error) {
      console.error("❌ Cost propagation failed:", error)
      return {
        success: false,
        materialUpdates: [],
        productUpdates: [],
        workOrderUpdates: [],
        totalImpactedRecords: 0,
        warnings: [],
        errors: [error instanceof Error ? error.message : "Unknown error occurred"]
      }
    }
  }

  /**
   * Calculate new average cost for a raw material based on available lots
   */
  private async calculateMaterialAverageCost(materialId: string): Promise<MaterialCostUpdate | null> {
    // Get material details
    const material = await db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, materialId),
        eq(rawMaterials.company_id, this.context.companyId)
      )
    })

    if (!material) {
      console.warn(`⚠️ Material ${materialId} not found`)
      return null
    }

    // Get available lots to calculate weighted average cost
    const availableLots = await db.query.rawMaterialLots.findMany({
      where: and(
        eq(rawMaterialLots.raw_material_id, materialId),
        eq(rawMaterialLots.company_id, this.context.companyId),
        eq(rawMaterialLots.status, "available"),
        eq(rawMaterialLots.quality_status, "approved")
      )
    })

    if (availableLots.length === 0) {
      console.warn(`⚠️ No available lots found for material ${materialId}`)
      return null
    }

    // Calculate weighted average cost
    let totalValue = 0
    let totalQuantity = 0

    availableLots.forEach(lot => {
      const qty = parseFloat(lot.qty || "0")
      const cost = parseFloat(lot.unit_cost || "0")
      totalValue += qty * cost
      totalQuantity += qty
    })

    const newAverageCost = totalQuantity > 0 ? totalValue / totalQuantity : 0
    const previousCost = parseFloat(material.standard_cost || "0")
    const costChange = newAverageCost - previousCost
    const costChangePercent = previousCost > 0 ? (costChange / previousCost) * 100 : 0

    // Update material standard cost
    await db.update(rawMaterials)
      .set({
        standard_cost: newAverageCost.toString(),
        updated_at: new Date(),
      })
      .where(eq(rawMaterials.id, materialId))

    console.log(`📊 Material ${material.name} cost updated: $${previousCost} → $${newAverageCost} (${costChangePercent.toFixed(2)}%)`)

    return {
      materialId,
      materialName: material.name,
      materialSku: material.sku,
      previousCost,
      newCost: newAverageCost,
      costChange,
      costChangePercent,
    }
  }

  /**
   * Update product costs based on BOM and new material costs
   */
  private async updateProductCosts(
    materialId: string, 
    materialCostUpdate: MaterialCostUpdate
  ): Promise<ProductCostUpdate[]> {
    // Find products that use this material in their BOM
    const affectedBOMs = await db.query.billOfMaterials.findMany({
      where: and(
        eq(billOfMaterials.raw_material_id, materialId),
        eq(billOfMaterials.company_id, this.context.companyId),
        eq(billOfMaterials.status, "active")
      ),
      with: {
        product: true,
      }
    })

    const productUpdates: ProductCostUpdate[] = []

    for (const bomItem of affectedBOMs) {
      if (!bomItem.product) continue

      try {
        // Calculate new product cost based on all BOM items
        const newProductCost = await this.calculateProductCost(bomItem.product_id)
        const previousCost = parseFloat(bomItem.product.standard_cost || "0")
        const costChange = newProductCost - previousCost

        // Update product cost
        await db.update(products)
          .set({
            standard_cost: newProductCost.toString(),
            updated_at: new Date(),
          })
          .where(eq(products.id, bomItem.product_id))

        // Count affected work orders
        const affectedWorkOrdersCount = await db.query.workOrders.findMany({
          where: and(
            eq(workOrders.product_id, bomItem.product_id),
            eq(workOrders.company_id, this.context.companyId),
            eq(workOrders.status, "pending") // Only count pending work orders
          )
        })

        productUpdates.push({
          productId: bomItem.product_id,
          productName: bomItem.product.name,
          productSku: bomItem.product.sku,
          previousCost,
          newCost: newProductCost,
          costChange,
          affectedWorkOrders: affectedWorkOrdersCount.length,
        })

        console.log(`📦 Product ${bomItem.product.name} cost updated: $${previousCost} → $${newProductCost}`)

      } catch (error) {
        console.error(`❌ Failed to update product ${bomItem.product_id} cost:`, error)
      }
    }

    return productUpdates
  }

  /**
   * Calculate total product cost based on current BOM and material costs
   */
  private async calculateProductCost(productId: string): Promise<number> {
    const bomItems = await db.query.billOfMaterials.findMany({
      where: and(
        eq(billOfMaterials.product_id, productId),
        eq(billOfMaterials.company_id, this.context.companyId),
        eq(billOfMaterials.status, "active")
      ),
      with: {
        rawMaterial: true,
      }
    })

    let totalCost = 0

    for (const bomItem of bomItems) {
      if (!bomItem.rawMaterial) continue

      const qtyRequired = parseFloat(bomItem.qty_required || "0")
      const wasteFactor = parseFloat(bomItem.waste_factor || "0.05")
      const totalQtyNeeded = qtyRequired * (1 + wasteFactor)
      const materialCost = parseFloat(bomItem.rawMaterial.standard_cost || "0")
      
      totalCost += totalQtyNeeded * materialCost
    }

    return totalCost
  }

  /**
   * Update work order costs when material costs change
   */
  private async updateWorkOrderCosts(
    materialId: string,
    materialCostUpdate: MaterialCostUpdate
  ): Promise<Array<{
    workOrderId: string
    workOrderNumber: string
    previousCost: number
    newCost: number
    costChange: number
  }>> {
    // Find active work orders that use this material
    const affectedWorkOrders = await db.query.workOrders.findMany({
      where: and(
        eq(workOrders.company_id, this.context.companyId),
        eq(workOrders.status, "pending") // Only update pending work orders
      ),
      with: {
        product: {
          with: {
            billOfMaterials: {
              where: eq(billOfMaterials.raw_material_id, materialId)
            }
          }
        }
      }
    })

    const workOrderUpdates = []

    for (const workOrder of affectedWorkOrders) {
      if (!workOrder.product?.billOfMaterials?.length) continue

      try {
        // Recalculate work order material cost
        const newMaterialCost = await this.calculateWorkOrderMaterialCost(workOrder.id)
        const previousCost = parseFloat(workOrder.material_cost || "0")
        const costChange = newMaterialCost - previousCost

        // Update work order cost
        await db.update(workOrders)
          .set({
            material_cost: newMaterialCost.toString(),
            updated_at: new Date(),
          })
          .where(eq(workOrders.id, workOrder.id))

        workOrderUpdates.push({
          workOrderId: workOrder.id,
          workOrderNumber: workOrder.number,
          previousCost,
          newCost: newMaterialCost,
          costChange,
        })

        console.log(`🏭 Work Order ${workOrder.number} cost updated: $${previousCost} → $${newMaterialCost}`)

      } catch (error) {
        console.error(`❌ Failed to update work order ${workOrder.id} cost:`, error)
      }
    }

    return workOrderUpdates
  }

  /**
   * Calculate work order material cost based on BOM and current material costs
   */
  private async calculateWorkOrderMaterialCost(workOrderId: string): Promise<number> {
    const workOrder = await db.query.workOrders.findFirst({
      where: eq(workOrders.id, workOrderId),
      with: {
        product: {
          with: {
            billOfMaterials: {
              with: {
                rawMaterial: true,
              }
            }
          }
        }
      }
    })

    if (!workOrder?.product?.billOfMaterials) return 0

    const workOrderQty = parseFloat(workOrder.qty || "0")
    let totalMaterialCost = 0

    for (const bomItem of workOrder.product.billOfMaterials) {
      if (!bomItem.rawMaterial) continue

      const qtyRequired = parseFloat(bomItem.qty_required || "0") * workOrderQty
      const wasteFactor = parseFloat(bomItem.waste_factor || "0.05")
      const totalQtyNeeded = qtyRequired * (1 + wasteFactor)
      const materialCost = parseFloat(bomItem.rawMaterial.standard_cost || "0")
      
      totalMaterialCost += totalQtyNeeded * materialCost
    }

    return totalMaterialCost
  }
}
