/**
 * Manufacturing ERP - Inventory Event System
 * Professional event-driven architecture for real-time inventory updates
 * Maintains data consistency across all modules with multi-tenant security
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0
 */

import { z } from "zod"

// ✅ PROFESSIONAL: Event type definitions
export enum InventoryEventType {
  // Raw Material Events
  RAW_MATERIAL_LOT_CREATED = "raw_material_lot_created",
  RAW_MATERIAL_LOT_UPDATED = "raw_material_lot_updated",
  RAW_MATERIAL_LOT_CONSUMED = "raw_material_lot_consumed",
  RAW_MATERIAL_LOT_DELETED = "raw_material_lot_deleted",
  
  // Material Consumption Events
  MATERIAL_CONSUMED = "material_consumed",
  MATERIAL_CONSUMPTION_REVERSED = "material_consumption_reversed",
  
  // Stock Events
  STOCK_LOT_CREATED = "stock_lot_created",
  STOCK_LOT_UPDATED = "stock_lot_updated",
  STOCK_TRANSACTION_CREATED = "stock_transaction_created",
  
  // Quality Events
  QUALITY_STATUS_CHANGED = "quality_status_changed",
  
  // Work Order Events
  WORK_ORDER_MATERIAL_ALLOCATED = "work_order_material_allocated",
  WORK_ORDER_COMPLETED = "work_order_completed",
}

// ✅ PROFESSIONAL: Base event interface
export interface BaseInventoryEvent {
  id: string
  type: InventoryEventType
  companyId: string
  userId: string
  timestamp: string
  source: string // API endpoint or service that triggered the event
  metadata?: Record<string, any>
}

// ✅ PROFESSIONAL: Raw Material Lot Events
export interface RawMaterialLotEvent extends BaseInventoryEvent {
  data: {
    lotId: string
    materialId: string
    materialName: string
    materialSku: string
    supplierId?: string
    supplierName?: string
    quantity: number
    unitCost: number
    totalCost: number
    currency: string
    status: "available" | "reserved" | "consumed" | "expired"
    qualityStatus: "pending" | "approved" | "rejected" | "quarantined"
    location: string
    lotNumber?: string
    receivedDate?: string
    expiryDate?: string
  }
}

// ✅ PROFESSIONAL: Material Consumption Events
export interface MaterialConsumptionEvent extends BaseInventoryEvent {
  data: {
    consumptionId: string
    workOrderId: string
    workOrderNumber: string
    productId: string
    productName: string
    lotId: string
    materialId: string
    materialName: string
    materialSku: string
    quantityConsumed: number
    unitCost: number
    totalCost: number
    remainingQuantity: number
    consumedDate: string
    notes?: string
  }
}

// ✅ PROFESSIONAL: Stock Events
export interface StockEvent extends BaseInventoryEvent {
  data: {
    stockLotId?: string
    transactionId?: string
    productId: string
    productName: string
    productSku: string
    quantity: number
    location: string
    transactionType?: "inbound" | "outbound" | "adjustment"
    workOrderId?: string
    qualityStatus?: "pending" | "approved" | "rejected"
    reference?: string
    notes?: string
  }
}

// ✅ PROFESSIONAL: Quality Status Events
export interface QualityStatusEvent extends BaseInventoryEvent {
  data: {
    entityType: "raw_material_lot" | "stock_lot"
    entityId: string
    previousStatus: string
    newStatus: string
    inspectionId?: string
    approvedBy?: string
    approvedDate?: string
    notes?: string
  }
}

// ✅ PROFESSIONAL: Union type for all events
export type InventoryEvent = 
  | RawMaterialLotEvent 
  | MaterialConsumptionEvent 
  | StockEvent 
  | QualityStatusEvent

// ✅ PROFESSIONAL: Event validation schemas
export const baseEventSchema = z.object({
  id: z.string().min(1),
  type: z.nativeEnum(InventoryEventType),
  companyId: z.string().min(1),
  userId: z.string().min(1),
  timestamp: z.string(),
  source: z.string().min(1),
  metadata: z.record(z.any()).optional(),
})

export const rawMaterialLotEventSchema = baseEventSchema.extend({
  data: z.object({
    lotId: z.string().min(1),
    materialId: z.string().min(1),
    materialName: z.string().min(1),
    materialSku: z.string().min(1),
    supplierId: z.string().optional(),
    supplierName: z.string().optional(),
    quantity: z.number(),
    unitCost: z.number(),
    totalCost: z.number(),
    currency: z.string().default("USD"),
    status: z.enum(["available", "reserved", "consumed", "expired"]),
    qualityStatus: z.enum(["pending", "approved", "rejected", "quarantined"]),
    location: z.string().min(1),
    lotNumber: z.string().optional(),
    receivedDate: z.string().optional(),
    expiryDate: z.string().optional(),
  })
})

export const materialConsumptionEventSchema = baseEventSchema.extend({
  data: z.object({
    consumptionId: z.string().min(1),
    workOrderId: z.string().min(1),
    workOrderNumber: z.string().min(1),
    productId: z.string().min(1),
    productName: z.string().min(1),
    lotId: z.string().min(1),
    materialId: z.string().min(1),
    materialName: z.string().min(1),
    materialSku: z.string().min(1),
    quantityConsumed: z.number().positive(),
    unitCost: z.number(),
    totalCost: z.number(),
    remainingQuantity: z.number().min(0),
    consumedDate: z.string(),
    notes: z.string().optional(),
  })
})

// ✅ PROFESSIONAL: Event handler interface
export interface InventoryEventHandler {
  canHandle(event: InventoryEvent): boolean
  handle(event: InventoryEvent): Promise<void>
  priority: number // Lower numbers = higher priority
}

// ✅ PROFESSIONAL: Event emitter class
export class InventoryEventEmitter {
  private handlers: InventoryEventHandler[] = []
  private static instance: InventoryEventEmitter

  static getInstance(): InventoryEventEmitter {
    if (!InventoryEventEmitter.instance) {
      InventoryEventEmitter.instance = new InventoryEventEmitter()
    }
    return InventoryEventEmitter.instance
  }

  /**
   * Register an event handler
   */
  registerHandler(handler: InventoryEventHandler): void {
    this.handlers.push(handler)
    // Sort by priority (lower numbers first)
    this.handlers.sort((a, b) => a.priority - b.priority)
  }

  /**
   * Emit an inventory event to all registered handlers
   */
  async emit(event: InventoryEvent): Promise<void> {
    console.log(`📡 Emitting inventory event: ${event.type} for company ${event.companyId}`)
    
    // Validate event structure
    try {
      baseEventSchema.parse(event)
    } catch (error) {
      console.error("❌ Invalid event structure:", error)
      return
    }

    // Process handlers in priority order
    for (const handler of this.handlers) {
      if (handler.canHandle(event)) {
        try {
          await handler.handle(event)
          console.log(`✅ Event handled by ${handler.constructor.name}`)
        } catch (error) {
          console.error(`❌ Event handler ${handler.constructor.name} failed:`, error)
          // Continue processing other handlers even if one fails
        }
      }
    }
  }

  /**
   * Create and emit a raw material lot event
   */
  async emitRawMaterialLotEvent(
    type: InventoryEventType,
    lotData: RawMaterialLotEvent['data'],
    context: { companyId: string; userId: string; source: string }
  ): Promise<void> {
    const event: RawMaterialLotEvent = {
      id: `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      companyId: context.companyId,
      userId: context.userId,
      timestamp: new Date().toISOString(),
      source: context.source,
      data: lotData,
    }

    await this.emit(event)
  }

  /**
   * Create and emit a material consumption event
   */
  async emitMaterialConsumptionEvent(
    type: InventoryEventType,
    consumptionData: MaterialConsumptionEvent['data'],
    context: { companyId: string; userId: string; source: string }
  ): Promise<void> {
    const event: MaterialConsumptionEvent = {
      id: `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      companyId: context.companyId,
      userId: context.userId,
      timestamp: new Date().toISOString(),
      source: context.source,
      data: consumptionData,
    }

    await this.emit(event)
  }
}

// ✅ PROFESSIONAL: Export singleton instance
export const inventoryEventEmitter = InventoryEventEmitter.getInstance()
