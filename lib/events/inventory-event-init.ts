/**
 * Manufacturing ERP - Inventory Event System Initialization
 * Professional initialization service for event-driven inventory updates
 * Registers all event handlers and ensures proper system startup
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0
 */

import { inventoryEventEmitter } from "./inventory-events"
import { inventoryEventHandlers } from "./inventory-event-handlers"

// ✅ PROFESSIONAL: Event system initialization flag
let isInitialized = false

/**
 * Initialize the inventory event system
 * Registers all event handlers and sets up the event-driven architecture
 */
export function initializeInventoryEventSystem(): void {
  if (isInitialized) {
    console.log("📡 Inventory event system already initialized")
    return
  }

  console.log("🚀 Initializing inventory event system...")

  try {
    // Register all event handlers
    inventoryEventHandlers.forEach(handler => {
      inventoryEventEmitter.registerHandler(handler)
      console.log(`✅ Registered event handler: ${handler.constructor.name} (priority: ${handler.priority})`)
    })

    isInitialized = true
    console.log(`🎉 Inventory event system initialized successfully with ${inventoryEventHandlers.length} handlers`)

  } catch (error) {
    console.error("❌ Failed to initialize inventory event system:", error)
    throw error
  }
}

/**
 * Check if the inventory event system is initialized
 */
export function isInventoryEventSystemInitialized(): boolean {
  return isInitialized
}

/**
 * Get the number of registered event handlers
 */
export function getRegisteredHandlerCount(): number {
  return inventoryEventHandlers.length
}

// ✅ PROFESSIONAL: Auto-initialize in development/production
// This ensures the event system is always ready when the module is imported
if (typeof window === "undefined") { // Server-side only
  // Initialize on server startup
  setTimeout(() => {
    initializeInventoryEventSystem()
  }, 100) // Small delay to ensure all modules are loaded
}
