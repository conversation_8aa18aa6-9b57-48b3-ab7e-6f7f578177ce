/**
 * Manufacturing ERP - Predictive Analytics for Demand Forecasting
 * 
 * Advanced analytics enhancement extending existing MRP capabilities
 * with predictive modeling for improved demand forecasting accuracy.
 * 
 * ✅ ENHANCED: Extends existing MRP system without breaking changes
 * ✅ MAINTAINED: 100% backward compatibility with current forecasting
 * ✅ SECURED: Multi-tenant isolation and data protection
 */

import { TenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { demandForecasts, salesContracts, salesContractItems, products } from "@/lib/schema-postgres"
import { eq, and, gte, lte, desc } from "drizzle-orm"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface PredictiveModel {
  modelType: 'linear_regression' | 'moving_average' | 'exponential_smoothing' | 'seasonal_decomposition'
  accuracy: number
  confidence: number
  lastTrained: string
  parameters: Record<string, any>
}

export interface PredictiveAnalysis {
  productId: string
  forecastPeriod: string
  predictedDemand: number
  confidenceInterval: {
    lower: number
    upper: number
  }
  seasonalityFactor: number
  trendFactor: number
  modelUsed: PredictiveModel
  historicalAccuracy: number
  riskFactors: string[]
}

export interface HistoricalPattern {
  period: string
  actualDemand: number
  seasonalIndex: number
  trendValue: number
  cyclicalComponent: number
}

// ============================================================================
// PREDICTIVE FORECASTING SERVICE
// ============================================================================

export class PredictiveForecastingService {
  
  /**
   * Generate predictive demand forecast using historical data analysis
   */
  async generatePredictiveForecast(
    productId: string,
    forecastPeriod: string,
    context: TenantContext
  ): Promise<PredictiveAnalysis> {
    try {
      // 1. Gather historical data
      const historicalData = await this.getHistoricalDemandData(productId, context)
      
      // 2. Analyze patterns
      const patterns = this.analyzeHistoricalPatterns(historicalData)
      
      // 3. Select best model
      const model = this.selectOptimalModel(patterns)
      
      // 4. Generate prediction
      const prediction = this.generatePrediction(patterns, model, forecastPeriod)
      
      // 5. Calculate confidence intervals
      const confidenceInterval = this.calculateConfidenceInterval(prediction, patterns, model)
      
      // 6. Assess risk factors
      const riskFactors = this.assessRiskFactors(patterns, historicalData)
      
      return {
        productId,
        forecastPeriod,
        predictedDemand: Math.round(prediction),
        confidenceInterval,
        seasonalityFactor: patterns.seasonalityFactor || 1.0,
        trendFactor: patterns.trendFactor || 1.0,
        modelUsed: model,
        historicalAccuracy: this.calculateHistoricalAccuracy(patterns, model),
        riskFactors
      }
    } catch (error) {
      console.error("Predictive forecasting error:", error)
      throw new Error(`Failed to generate predictive forecast: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get historical demand data for analysis
   */
  private async getHistoricalDemandData(productId: string, context: TenantContext) {
    // Get historical forecasts and actual sales data
    const [forecasts, contracts] = await Promise.all([
      db.query.demandForecasts.findMany({
        where: and(
          eq(demandForecasts.product_id, productId),
          eq(demandForecasts.company_id, context.companyId)
        ),
        orderBy: [desc(demandForecasts.created_at)],
        limit: 24 // Last 24 periods for analysis
      }),
      
      db.query.salesContractItems.findMany({
        where: and(
          eq(salesContractItems.product_id, productId),
          eq(salesContractItems.company_id, context.companyId)
        ),
        with: {
          salesContract: true
        },
        orderBy: [desc(salesContractItems.created_at)],
        limit: 50 // Last 50 contracts for trend analysis
      })
    ])

    return { forecasts, contracts }
  }

  /**
   * Analyze historical patterns for predictive modeling
   */
  private analyzeHistoricalPatterns(historicalData: any): any {
    const { forecasts, contracts } = historicalData
    
    // Simple pattern analysis (can be enhanced with more sophisticated algorithms)
    const patterns = {
      seasonalityFactor: this.calculateSeasonality(contracts),
      trendFactor: this.calculateTrend(contracts),
      volatility: this.calculateVolatility(forecasts),
      averageDemand: this.calculateAverageDemand(contracts),
      growthRate: this.calculateGrowthRate(contracts)
    }

    return patterns
  }

  /**
   * Select optimal predictive model based on data characteristics
   */
  private selectOptimalModel(patterns: any): PredictiveModel {
    // Simple model selection logic (can be enhanced with ML model comparison)
    let modelType: PredictiveModel['modelType'] = 'moving_average'
    
    if (patterns.volatility > 0.3) {
      modelType = 'exponential_smoothing'
    } else if (patterns.seasonalityFactor > 1.2 || patterns.seasonalityFactor < 0.8) {
      modelType = 'seasonal_decomposition'
    } else if (Math.abs(patterns.trendFactor - 1.0) > 0.1) {
      modelType = 'linear_regression'
    }

    return {
      modelType,
      accuracy: this.estimateModelAccuracy(modelType, patterns),
      confidence: this.calculateModelConfidence(modelType, patterns),
      lastTrained: new Date().toISOString(),
      parameters: {
        seasonality: patterns.seasonalityFactor,
        trend: patterns.trendFactor,
        volatility: patterns.volatility
      }
    }
  }

  /**
   * Generate prediction using selected model
   */
  private generatePrediction(patterns: any, model: PredictiveModel, forecastPeriod: string): number {
    const baseDemand = patterns.averageDemand || 100
    
    switch (model.modelType) {
      case 'linear_regression':
        return baseDemand * patterns.trendFactor * patterns.seasonalityFactor
      
      case 'exponential_smoothing':
        return baseDemand * (1 + patterns.growthRate) * patterns.seasonalityFactor
      
      case 'seasonal_decomposition':
        return baseDemand * patterns.seasonalityFactor * (1 + patterns.growthRate * 0.5)
      
      case 'moving_average':
      default:
        return baseDemand * patterns.seasonalityFactor
    }
  }

  /**
   * Calculate confidence interval for prediction
   */
  private calculateConfidenceInterval(prediction: number, patterns: any, model: PredictiveModel) {
    const errorMargin = prediction * patterns.volatility * (1 - model.confidence)
    
    return {
      lower: Math.max(0, Math.round(prediction - errorMargin)),
      upper: Math.round(prediction + errorMargin)
    }
  }

  /**
   * Assess risk factors that could affect forecast accuracy
   */
  private assessRiskFactors(patterns: any, historicalData: any): string[] {
    const risks: string[] = []
    
    if (patterns.volatility > 0.4) {
      risks.push("High demand volatility detected")
    }
    
    if (historicalData.forecasts.length < 6) {
      risks.push("Limited historical data available")
    }
    
    if (Math.abs(patterns.trendFactor - 1.0) > 0.3) {
      risks.push("Strong trend detected - extrapolation risk")
    }
    
    if (patterns.seasonalityFactor > 2.0 || patterns.seasonalityFactor < 0.5) {
      risks.push("High seasonality - timing sensitivity")
    }

    return risks
  }

  // ============================================================================
  // HELPER CALCULATION METHODS
  // ============================================================================

  private calculateSeasonality(contracts: any[]): number {
    if (contracts.length < 4) return 1.0
    
    // Simple seasonality calculation based on quarterly patterns
    const quarters = [0, 0, 0, 0]
    let totalDemand = 0
    
    contracts.forEach(contract => {
      const date = new Date(contract.salesContract?.created_at || contract.created_at)
      const quarter = Math.floor(date.getMonth() / 3)
      const quantity = parseFloat(contract.qty || '0')
      quarters[quarter] += quantity
      totalDemand += quantity
    })
    
    const avgQuarterly = totalDemand / 4
    const currentQuarter = Math.floor(new Date().getMonth() / 3)
    
    return avgQuarterly > 0 ? quarters[currentQuarter] / avgQuarterly : 1.0
  }

  private calculateTrend(contracts: any[]): number {
    if (contracts.length < 2) return 1.0
    
    const recent = contracts.slice(0, Math.floor(contracts.length / 2))
    const older = contracts.slice(Math.floor(contracts.length / 2))
    
    const recentAvg = recent.reduce((sum, c) => sum + parseFloat(c.qty || '0'), 0) / recent.length
    const olderAvg = older.reduce((sum, c) => sum + parseFloat(c.qty || '0'), 0) / older.length
    
    return olderAvg > 0 ? recentAvg / olderAvg : 1.0
  }

  private calculateVolatility(forecasts: any[]): number {
    if (forecasts.length < 2) return 0.2 // Default moderate volatility
    
    const demands = forecasts.map(f => parseFloat(f.forecasted_demand || '0'))
    const mean = demands.reduce((sum, d) => sum + d, 0) / demands.length
    const variance = demands.reduce((sum, d) => sum + Math.pow(d - mean, 2), 0) / demands.length
    const stdDev = Math.sqrt(variance)
    
    return mean > 0 ? stdDev / mean : 0.2
  }

  private calculateAverageDemand(contracts: any[]): number {
    if (contracts.length === 0) return 100 // Default baseline
    
    const totalDemand = contracts.reduce((sum, c) => sum + parseFloat(c.qty || '0'), 0)
    return totalDemand / contracts.length
  }

  private calculateGrowthRate(contracts: any[]): number {
    if (contracts.length < 2) return 0
    
    const sortedContracts = contracts.sort((a, b) => 
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    )
    
    const firstHalf = sortedContracts.slice(0, Math.floor(contracts.length / 2))
    const secondHalf = sortedContracts.slice(Math.floor(contracts.length / 2))
    
    const firstAvg = firstHalf.reduce((sum, c) => sum + parseFloat(c.qty || '0'), 0) / firstHalf.length
    const secondAvg = secondHalf.reduce((sum, c) => sum + parseFloat(c.qty || '0'), 0) / secondHalf.length
    
    return firstAvg > 0 ? (secondAvg - firstAvg) / firstAvg : 0
  }

  private estimateModelAccuracy(modelType: PredictiveModel['modelType'], patterns: any): number {
    // Simple accuracy estimation based on model type and data characteristics
    const baseAccuracy = {
      'moving_average': 0.75,
      'linear_regression': 0.80,
      'exponential_smoothing': 0.85,
      'seasonal_decomposition': 0.82
    }[modelType]
    
    // Adjust based on data quality
    const volatilityPenalty = Math.min(patterns.volatility * 0.2, 0.15)
    
    return Math.max(0.6, baseAccuracy - volatilityPenalty)
  }

  private calculateModelConfidence(modelType: PredictiveModel['modelType'], patterns: any): number {
    const baseConfidence = this.estimateModelAccuracy(modelType, patterns)
    
    // Adjust confidence based on data availability and stability
    const stabilityBonus = patterns.volatility < 0.2 ? 0.05 : 0
    
    return Math.min(0.95, baseConfidence + stabilityBonus)
  }

  private calculateHistoricalAccuracy(patterns: any, model: PredictiveModel): number {
    // Simplified historical accuracy calculation
    // In a real implementation, this would compare past predictions to actual outcomes
    return model.accuracy * (1 - patterns.volatility * 0.3)
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const predictiveForecastingService = new PredictiveForecastingService()
