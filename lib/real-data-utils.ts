/**
 * Real Data Detection Utilities
 * 
 * Functions to detect and filter only real, linked data in the Manufacturing ERP
 * Zero breaking changes - only adds filtering capabilities
 */

import { db } from "@/lib/db"
import { stockLots, rawMaterialLots } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { LocationManager, LocationConfig } from "@/lib/location-config"

/**
 * Get locations that actually have inventory data
 */
export async function getUsedLocations(companyId: string): Promise<string[]> {
  try {
    // Get locations from stock lots (finished goods)
    const stockLocations = await db
      .selectDistinct({ location: stockLots.location })
      .from(stockLots)
      .where(eq(stockLots.company_id, companyId))

    // Get locations from raw material lots
    const rawMaterialLocations = await db
      .selectDistinct({ location: rawMaterialLots.location })
      .from(rawMaterialLots)
      .where(eq(rawMaterialLots.company_id, companyId))

    // Combine and deduplicate
    const allLocations = [
      ...stockLocations.map(l => l.location),
      ...rawMaterialLocations.map(l => l.location)
    ].filter(Boolean) // Remove null/undefined

    return [...new Set(allLocations)]
  } catch (error) {
    console.error("Error getting used locations:", error)
    return []
  }
}

/**
 * Get location configurations for only used locations
 */
export async function getRealLocationConfigs(companyId: string): Promise<LocationConfig[]> {
  const usedLocationIds = await getUsedLocations(companyId)
  const allLocations = LocationManager.getAllLocations()
  
  return allLocations.filter(location => 
    usedLocationIds.includes(location.id) || 
    usedLocationIds.includes(location.name)
  )
}

/**
 * Get products that actually have inventory
 */
export async function getProductsWithInventory(companyId: string): Promise<string[]> {
  try {
    // Get products from stock lots
    const stockProducts = await db
      .selectDistinct({ product_id: stockLots.product_id })
      .from(stockLots)
      .where(and(
        eq(stockLots.company_id, companyId),
        // Only products with actual quantity > 0
      ))

    return stockProducts.map(p => p.product_id).filter(Boolean)
  } catch (error) {
    console.error("Error getting products with inventory:", error)
    return []
  }
}

/**
 * Check if a location is actually used
 */
export async function isLocationUsed(locationId: string, companyId: string): Promise<boolean> {
  const usedLocations = await getUsedLocations(companyId)
  return usedLocations.includes(locationId)
}

/**
 * Get real data statistics
 */
export async function getRealDataStats(companyId: string) {
  try {
    const [usedLocations, productsWithInventory] = await Promise.all([
      getUsedLocations(companyId),
      getProductsWithInventory(companyId)
    ])

    // Get total stock lots
    const totalStockLots = await db
      .select({ count: stockLots.id })
      .from(stockLots)
      .where(eq(stockLots.company_id, companyId))

    // Get total raw material lots
    const totalRawMaterialLots = await db
      .select({ count: rawMaterialLots.id })
      .from(rawMaterialLots)
      .where(eq(rawMaterialLots.company_id, companyId))

    return {
      usedLocationsCount: usedLocations.length,
      productsWithInventoryCount: productsWithInventory.length,
      totalStockLotsCount: totalStockLots.length,
      totalRawMaterialLotsCount: totalRawMaterialLots.length,
      usedLocations,
      productsWithInventory
    }
  } catch (error) {
    console.error("Error getting real data stats:", error)
    return {
      usedLocationsCount: 0,
      productsWithInventoryCount: 0,
      totalStockLotsCount: 0,
      totalRawMaterialLotsCount: 0,
      usedLocations: [],
      productsWithInventory: []
    }
  }
}
