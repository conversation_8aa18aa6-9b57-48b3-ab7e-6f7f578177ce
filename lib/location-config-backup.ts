/**
 * Manufacturing ERP - Professional Location Management System
 * 
 * Enterprise-grade location configuration following industrial ERP standards
 * Supports dynamic location management, capacity planning, and hierarchical organization
 */

export interface LocationConfig {
  id: string
  name: string
  displayName: string
  description: string
  type: LocationType
  capacity: number
  icon: string
  color: string
  attributes: LocationAttributes
  hierarchy: LocationHierarchy
  flowConnections: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface LocationAttributes {
  temperatureControlled?: boolean
  hazardousMaterials?: boolean
  securityLevel: 'low' | 'medium' | 'high' | 'restricted'
  accessControl?: string[]
  qualityControlRequired?: boolean
  customsControlled?: boolean
  automatedHandling?: boolean
  // ✅ PHASE 1 ENHANCEMENT: Shipping-specific attributes
  shippingMethods?: string[]
  maxPackageSize?: 'small' | 'medium' | 'large' | 'oversized'
  dockType?: 'truck_loading' | 'container_loading' | 'rail_loading' | 'air_cargo'
  maxVehicleSize?: 'van' | 'truck' | 'container_truck' | 'rail_car' | 'aircraft'
}

export interface LocationHierarchy {
  plant?: string
  building?: string
  zone?: string
  area?: string
  level?: number
}

export type LocationType =
  | 'raw_materials'
  | 'work_in_progress'
  | 'finished_goods'
  | 'distribution'
  | 'quality_control'
  | 'maintenance'
  | 'shipping'
  | 'receiving'
  | 'customer_specific'
  | 'export_staging'
  | 'quarantine'
  | 'returns'
  | 'shipping_staging'  // ✅ PHASE 1 ENHANCEMENT
  | 'shipping_dock'     // ✅ PHASE 1 ENHANCEMENT

/**
 * Professional Location Configuration
 * Based on industrial manufacturing ERP best practices
 */
export const DEFAULT_LOCATION_CONFIG: LocationConfig[] = [
  // ✅ CLEANED FOR TESTING - No hardcoded locations
  // All locations are now stored in the database

  // Distribution Locations
  {
    id: 'dist_shanghai',
    name: 'dist_shanghai',
    displayName: 'Shanghai Distribution Center',
    description: 'Regional distribution center for East China',
    type: 'distribution',
    capacity: 25000,
    icon: '🏪',
    color: 'bg-purple-500',
    attributes: {
      temperatureControlled: true,
      securityLevel: 'high',
      qualityControlRequired: false,
      automatedHandling: true,
      customsControlled: true
    },
    hierarchy: {
      plant: 'shanghai_dc',
      building: 'main_warehouse',
      zone: 'distribution',
      level: 1
    },
    flowConnections: ['customer_shipment'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'dist_beijing',
    name: 'dist_beijing',
    displayName: 'Beijing Distribution Center',
    description: 'Regional distribution center for North China',
    type: 'distribution',
    capacity: 20000,
    icon: '🏬',
    color: 'bg-indigo-500',
    attributes: {
      temperatureControlled: true,
      securityLevel: 'high',
      qualityControlRequired: false,
      automatedHandling: true,
      customsControlled: true
    },
    hierarchy: {
      plant: 'beijing_dc',
      building: 'main_warehouse',
      zone: 'distribution',
      level: 1
    },
    flowConnections: ['customer_shipment'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // Export and Specialized Locations
  {
    id: 'export_staging',
    name: 'export_staging',
    displayName: 'Export Staging Area',
    description: 'Staging area for international shipments',
    type: 'export_staging',
    capacity: 3000,
    icon: '🚢',
    color: 'bg-cyan-500',
    attributes: {
      temperatureControlled: false,
      securityLevel: 'high',
      qualityControlRequired: true,
      automatedHandling: false,
      customsControlled: true
    },
    hierarchy: {
      plant: 'main_plant',
      building: 'export_facility',
      zone: 'staging',
      level: 1
    },
    flowConnections: ['international_shipping'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // ✅ PHASE 1 ENHANCEMENT: Shipping-Specific Locations
  {
    id: 'shipping_staging_main',
    name: 'shipping_staging_main',
    displayName: '📦 Main Shipping Staging Area',
    description: 'Primary staging area for outbound shipments',
    type: 'shipping_staging',
    capacity: 2000,
    icon: '📦',
    color: 'bg-orange-500',
    attributes: {
      shippingMethods: ['sea_freight', 'air_freight', 'truck', 'express'],
      maxPackageSize: 'large',
      temperatureControlled: true,
      securityLevel: 'high',
      qualityControlRequired: false,
      automatedHandling: true
    },
    hierarchy: {
      plant: 'main_plant',
      building: 'shipping_dock',
      zone: 'staging',
      level: 1
    },
    flowConnections: ['shipping_dock_a', 'shipping_dock_b'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'shipping_dock_a',
    name: 'shipping_dock_a',
    displayName: '🚛 Shipping Dock A',
    description: 'Loading dock for truck and express shipments',
    type: 'shipping_dock',
    capacity: 500,
    icon: '🚛',
    color: 'bg-green-600',
    attributes: {
      shippingMethods: ['truck', 'express'],
      dockType: 'truck_loading',
      maxVehicleSize: 'truck',
      temperatureControlled: false,
      securityLevel: 'medium',
      automatedHandling: false
    },
    hierarchy: {
      plant: 'main_plant',
      building: 'shipping_dock',
      zone: 'dock_a',
      level: 1
    },
    flowConnections: ['customer_delivery'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'shipping_dock_b',
    name: 'shipping_dock_b',
    displayName: '🚢 Shipping Dock B',
    description: 'Loading dock for sea freight and air freight',
    type: 'shipping_dock',
    capacity: 800,
    icon: '🚢',
    color: 'bg-blue-600',
    attributes: {
      shippingMethods: ['sea_freight', 'air_freight'],
      dockType: 'container_loading',
      maxVehicleSize: 'container_truck',
      temperatureControlled: true,
      securityLevel: 'high',
      automatedHandling: true
    },
    hierarchy: {
      plant: 'main_plant',
      building: 'shipping_dock',
      zone: 'dock_b',
      level: 1
    },
    flowConnections: ['international_shipping'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // ✅ PHASE 1 ENHANCEMENT: Shipping-Specific Locations
  {
    id: 'shipping_staging_main',
    name: 'shipping_staging_main',
    displayName: '📦 Main Shipping Staging Area',
    description: 'Primary staging area for outbound shipments',
    type: 'shipping_staging',
    capacity: 2000,
    icon: '📦',
    color: 'bg-orange-500',
    attributes: {
      shippingMethods: ['sea_freight', 'air_freight', 'truck', 'express'],
      maxPackageSize: 'large',
      temperatureControlled: true,
      securityLevel: 'high',
      qualityControlRequired: false,
      automatedHandling: true
    },
    hierarchy: {
      plant: 'main_plant',
      building: 'shipping_dock',
      zone: 'staging',
      level: 1
    },
    flowConnections: ['shipping_dock_a', 'shipping_dock_b'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'shipping_dock_a',
    name: 'shipping_dock_a',
    displayName: '🚛 Shipping Dock A',
    description: 'Loading dock for truck and express shipments',
    type: 'shipping_dock',
    capacity: 500,
    icon: '🚛',
    color: 'bg-green-600',
    attributes: {
      shippingMethods: ['truck', 'express'],
      dockType: 'truck_loading',
      maxVehicleSize: 'truck',
      temperatureControlled: false,
      securityLevel: 'medium',
      automatedHandling: false
    },
    hierarchy: {
      plant: 'main_plant',
      building: 'shipping_dock',
      zone: 'dock_a',
      level: 1
    },
    flowConnections: ['customer_delivery'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'shipping_dock_b',
    name: 'shipping_dock_b',
    displayName: '🚢 Shipping Dock B',
    description: 'Loading dock for sea freight and air freight',
    type: 'shipping_dock',
    capacity: 800,
    icon: '🚢',
    color: 'bg-blue-600',
    attributes: {
      shippingMethods: ['sea_freight', 'air_freight'],
      dockType: 'container_loading',
      maxVehicleSize: 'container_truck',
      temperatureControlled: true,
      securityLevel: 'high',
      automatedHandling: true
    },
    hierarchy: {
      plant: 'main_plant',
      building: 'shipping_dock',
      zone: 'dock_b',
      level: 1
    },
    flowConnections: ['international_shipping'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]

/**
 * Location Management Utilities
 */
export class LocationManager {
  private static locations: LocationConfig[] = [...DEFAULT_LOCATION_CONFIG]

  static getAllLocations(): LocationConfig[] {
    // ✅ FIXED: Only show the 2 locations that exist in user's location directory
    const userLocations = this.locations.filter(loc =>
      loc.id === 'rm_building_a' || loc.id === 'fg_main_warehouse'
    )

    // Return only user's actual locations
    return userLocations.length > 0 ? userLocations : this.locations.slice(0, 2)
  }

  static getLocationById(id: string): LocationConfig | undefined {
    return this.locations.find(loc => loc.id === id)
  }

  static getLocationsByType(type: LocationType): LocationConfig[] {
    return this.locations.filter(loc => loc.type === type && loc.isActive)
  }

  static addLocation(location: Omit<LocationConfig, 'createdAt' | 'updatedAt'>): LocationConfig {
    const newLocation: LocationConfig = {
      ...location,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    this.locations.push(newLocation)
    return newLocation
  }

  static updateLocation(id: string, updates: Partial<LocationConfig>): LocationConfig | null {
    const index = this.locations.findIndex(loc => loc.id === id)
    if (index === -1) return null

    this.locations[index] = {
      ...this.locations[index],
      ...updates,
      updatedAt: new Date()
    }
    return this.locations[index]
  }

  static deactivateLocation(id: string): boolean {
    const location = this.getLocationById(id)
    if (!location) return false

    location.isActive = false
    location.updatedAt = new Date()
    return true
  }

  static getLocationHierarchy(): Record<string, LocationConfig[]> {
    const hierarchy: Record<string, LocationConfig[]> = {}

    this.locations.forEach(location => {
      const plant = location.hierarchy.plant || 'default'
      if (!hierarchy[plant]) {
        hierarchy[plant] = []
      }
      hierarchy[plant].push(location)
    })

    return hierarchy
  }

  static getLocationFlow(): Array<{ from: string, to: string[], description: string }> {
    return this.locations.map(location => ({
      from: location.id,
      to: location.flowConnections,
      description: `${location.displayName} flows to connected locations`
    }))
  }
}

/**
 * Legacy Location Mapping
 * Maps old hard-coded locations to new professional system
 */
export const LEGACY_LOCATION_MAPPING: Record<string, string> = {
  'raw_materials': 'rm_building_a',
  'work_in_progress': 'wip_production_line_1',
  'finished_goods': 'fg_main_warehouse',
  'warehouse_a': 'dist_shanghai',
  'warehouse_b': 'dist_beijing'
}

/**
 * Get location configuration for UI components
 */
export function getLocationForUI(legacyId: string): LocationConfig | undefined {
  const newId = LEGACY_LOCATION_MAPPING[legacyId] || legacyId
  return LocationManager.getLocationById(newId)
}

/**
 * Get all locations formatted for dropdowns
 */
export function getLocationsForDropdown(): Array<{ value: string, label: string, description: string }> {
  return LocationManager.getAllLocations().map(location => ({
    value: location.id,
    label: `${location.icon} ${location.displayName}`,
    description: location.description
  }))
}
