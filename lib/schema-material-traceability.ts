/**
 * Manufacturing ERP - Material Traceability Schema
 * Professional supply chain traceability linking finished goods to raw materials
 * Enables complete traceability from raw materials to shipped products
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0
 */

import { pgTable, text, timestamp, index } from "drizzle-orm/pg-core"
import { relations } from "drizzle-orm"
import { 
  companies, 
  workOrders, 
  stockLots, 
  materialConsumption,
  rawMaterialLots,
  rawMaterials,
  products,
} from "./schema-postgres"
import { shipments, shipmentItems } from "./schema-shipping"

// ============================================================================
// MATERIAL TRACEABILITY TABLES
// ============================================================================

/**
 * Product Material Traceability - Links finished goods to consumed raw materials
 * Captures the complete material lineage for each produced item
 */
export const productMaterialTraceability = pgTable("product_material_traceability", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  
  // Production Context
  work_order_id: text("work_order_id").notNull().references(() => workOrders.id),
  stock_lot_id: text("stock_lot_id").notNull().references(() => stockLots.id), // Finished goods lot
  product_id: text("product_id").notNull().references(() => products.id),
  
  // Raw Material Consumption
  material_consumption_id: text("material_consumption_id").notNull().references(() => materialConsumption.id),
  raw_material_lot_id: text("raw_material_lot_id").notNull().references(() => rawMaterialLots.id),
  raw_material_id: text("raw_material_id").notNull().references(() => rawMaterials.id),
  
  // Traceability Details
  consumed_quantity: text("consumed_quantity").notNull(), // Amount of raw material used
  finished_quantity: text("finished_quantity").notNull(), // Amount of finished product produced
  consumption_ratio: text("consumption_ratio").notNull(), // Raw material per finished unit
  
  // Production Context
  production_date: text("production_date").notNull(),
  production_batch: text("production_batch"), // Production batch identifier
  
  // Quality Context
  raw_material_quality_status: text("raw_material_quality_status").notNull(), // Quality status when consumed
  finished_goods_quality_status: text("finished_goods_quality_status"), // Quality status of finished goods
  
  // Audit Trail
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  created_by: text("created_by"),
}, (table) => ({
  // Performance Indexes
  companyIdIdx: index("product_material_traceability_company_id_idx").on(table.company_id),
  workOrderIdIdx: index("product_material_traceability_work_order_id_idx").on(table.work_order_id),
  stockLotIdIdx: index("product_material_traceability_stock_lot_id_idx").on(table.stock_lot_id),
  productIdIdx: index("product_material_traceability_product_id_idx").on(table.product_id),
  rawMaterialIdIdx: index("product_material_traceability_raw_material_id_idx").on(table.raw_material_id),
  rawMaterialLotIdIdx: index("product_material_traceability_raw_material_lot_id_idx").on(table.raw_material_lot_id),
  productionDateIdx: index("product_material_traceability_production_date_idx").on(table.production_date),
}))

/**
 * Shipment Material Traceability - Links shipped items to their raw material origins
 * Enables complete supply chain traceability for shipped products
 */
export const shipmentMaterialTraceability = pgTable("shipment_material_traceability", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  
  // Shipping Context
  shipment_id: text("shipment_id").notNull().references(() => shipments.id),
  shipment_item_id: text("shipment_item_id").notNull().references(() => shipmentItems.id),
  
  // Product Context
  product_id: text("product_id").notNull().references(() => products.id),
  stock_lot_id: text("stock_lot_id").notNull().references(() => stockLots.id),
  
  // Material Traceability Link
  product_material_traceability_id: text("product_material_traceability_id").notNull()
    .references(() => productMaterialTraceability.id),
  
  // Raw Material Details (denormalized for query performance)
  raw_material_id: text("raw_material_id").notNull().references(() => rawMaterials.id),
  raw_material_name: text("raw_material_name").notNull(),
  raw_material_sku: text("raw_material_sku").notNull(),
  raw_material_lot_id: text("raw_material_lot_id").notNull().references(() => rawMaterialLots.id),
  raw_material_lot_number: text("raw_material_lot_number"),
  
  // Supplier Information (denormalized)
  supplier_id: text("supplier_id"),
  supplier_name: text("supplier_name"),
  
  // Quantity Traceability
  shipped_quantity: text("shipped_quantity").notNull(), // Quantity of finished product shipped
  raw_material_quantity: text("raw_material_quantity").notNull(), // Equivalent raw material quantity
  
  // Production Context
  work_order_id: text("work_order_id").notNull().references(() => workOrders.id),
  work_order_number: text("work_order_number").notNull(),
  production_date: text("production_date").notNull(),
  production_batch: text("production_batch"),
  
  // Quality Context
  raw_material_quality_status: text("raw_material_quality_status").notNull(),
  finished_goods_quality_status: text("finished_goods_quality_status"),
  
  // Shipping Context
  ship_date: text("ship_date"),
  tracking_number: text("tracking_number"),
  
  // Audit Trail
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  created_by: text("created_by"),
}, (table) => ({
  // Performance Indexes
  companyIdIdx: index("shipment_material_traceability_company_id_idx").on(table.company_id),
  shipmentIdIdx: index("shipment_material_traceability_shipment_id_idx").on(table.shipment_id),
  shipmentItemIdIdx: index("shipment_material_traceability_shipment_item_id_idx").on(table.shipment_item_id),
  productIdIdx: index("shipment_material_traceability_product_id_idx").on(table.product_id),
  stockLotIdIdx: index("shipment_material_traceability_stock_lot_id_idx").on(table.stock_lot_id),
  rawMaterialIdIdx: index("shipment_material_traceability_raw_material_id_idx").on(table.raw_material_id),
  rawMaterialLotIdIdx: index("shipment_material_traceability_raw_material_lot_id_idx").on(table.raw_material_lot_id),
  workOrderIdIdx: index("shipment_material_traceability_work_order_id_idx").on(table.work_order_id),
  productionDateIdx: index("shipment_material_traceability_production_date_idx").on(table.production_date),
  shipDateIdx: index("shipment_material_traceability_ship_date_idx").on(table.ship_date),
  trackingNumberIdx: index("shipment_material_traceability_tracking_number_idx").on(table.tracking_number),
}))

// ============================================================================
// RELATIONS
// ============================================================================

export const productMaterialTraceabilityRelations = relations(productMaterialTraceability, ({ one }) => ({
  company: one(companies, {
    fields: [productMaterialTraceability.company_id],
    references: [companies.id],
  }),
  workOrder: one(workOrders, {
    fields: [productMaterialTraceability.work_order_id],
    references: [workOrders.id],
  }),
  stockLot: one(stockLots, {
    fields: [productMaterialTraceability.stock_lot_id],
    references: [stockLots.id],
  }),
  product: one(products, {
    fields: [productMaterialTraceability.product_id],
    references: [products.id],
  }),
  materialConsumption: one(materialConsumption, {
    fields: [productMaterialTraceability.material_consumption_id],
    references: [materialConsumption.id],
  }),
  rawMaterialLot: one(rawMaterialLots, {
    fields: [productMaterialTraceability.raw_material_lot_id],
    references: [rawMaterialLots.id],
  }),
  rawMaterial: one(rawMaterials, {
    fields: [productMaterialTraceability.raw_material_id],
    references: [rawMaterials.id],
  }),
}))

export const shipmentMaterialTraceabilityRelations = relations(shipmentMaterialTraceability, ({ one }) => ({
  company: one(companies, {
    fields: [shipmentMaterialTraceability.company_id],
    references: [companies.id],
  }),
  shipment: one(shipments, {
    fields: [shipmentMaterialTraceability.shipment_id],
    references: [shipments.id],
  }),
  shipmentItem: one(shipmentItems, {
    fields: [shipmentMaterialTraceability.shipment_item_id],
    references: [shipmentItems.id],
  }),
  product: one(products, {
    fields: [shipmentMaterialTraceability.product_id],
    references: [products.id],
  }),
  stockLot: one(stockLots, {
    fields: [shipmentMaterialTraceability.stock_lot_id],
    references: [stockLots.id],
  }),
  productMaterialTraceability: one(productMaterialTraceability, {
    fields: [shipmentMaterialTraceability.product_material_traceability_id],
    references: [productMaterialTraceability.id],
  }),
  rawMaterial: one(rawMaterials, {
    fields: [shipmentMaterialTraceability.raw_material_id],
    references: [rawMaterials.id],
  }),
  rawMaterialLot: one(rawMaterialLots, {
    fields: [shipmentMaterialTraceability.raw_material_lot_id],
    references: [rawMaterialLots.id],
  }),
  workOrder: one(workOrders, {
    fields: [shipmentMaterialTraceability.work_order_id],
    references: [workOrders.id],
  }),
}))

// ============================================================================
// EXPORTS
// ============================================================================

export {
  productMaterialTraceability,
  shipmentMaterialTraceability,
}
