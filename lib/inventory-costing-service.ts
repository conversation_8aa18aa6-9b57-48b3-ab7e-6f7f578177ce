/**
 * Manufacturing ERP - Professional Inventory Costing Service
 * 
 * Industrial-standard costing system for manufacturing ERP.
 * Implements multi-tier pricing logic following industry best practices.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Professional Costing System
 */

import { db } from "@/lib/db"
import { products, salesContracts, salesContractItems } from "@/lib/schema-postgres"
import { eq, and, desc, sql } from "drizzle-orm"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface ProductCostBreakdown {
  productId: string
  sku: string
  name: string

  // ✅ COST COMPONENTS
  materialCost: number
  laborCost: number
  overheadCost: number
  standardCost: number

  // ✅ PRICING TIERS
  contractPrice?: number          // From active sales contracts
  lastContractPrice?: number      // From historical contracts
  sellingPrice?: number          // Set selling price
  calculatedPrice: number         // Final calculated price

  // ✅ METADATA
  pricingMethod: 'contract' | 'historical' | 'base_price' | 'cost_plus_margin' | 'legacy_price' | 'standard' | 'selling' | 'fallback'
  confidence: 'high' | 'medium' | 'low'
  lastUpdated: Date
}

export interface LocationCostingRules {
  location: string
  costMultiplier: number          // 1.0 = full cost, 0.5 = 50% cost
  includeLabor: boolean
  includeOverhead: boolean
  description: string
}

// ============================================================================
// PROFESSIONAL COSTING SERVICE
// ============================================================================

export class InventoryCostingService {

  // ✅ LOCATION-BASED COSTING RULES
  private static readonly LOCATION_RULES: LocationCostingRules[] = [
    {
      location: 'Raw Materials - Building A',
      costMultiplier: 0.6,          // Raw materials cost only + handling
      includeLabor: false,
      includeOverhead: false,
      description: 'Raw materials with handling cost'
    },
    {
      location: 'Main Finished Goods Warehouse',
      costMultiplier: 1.0,          // Full cost
      includeLabor: true,
      includeOverhead: true,
      description: 'Finished goods with full manufacturing cost'
    },
    {
      location: 'Work in Progress',
      costMultiplier: 0.8,          // Partial manufacturing cost
      includeLabor: true,
      includeOverhead: false,
      description: 'WIP with material and labor cost'
    }
  ]

  // ✅ INDUSTRY STANDARD MARGINS
  private static readonly DEFAULT_MARGINS = {
    rawMaterials: 0.15,             // 15% margin on raw materials
    finishedGoods: 0.35,            // 35% margin on finished goods
    customProducts: 0.45            // 45% margin on custom/specialty items
  }

  /**
   * ✅ GET PRODUCT COST BREAKDOWN
   * 
   * Calculates comprehensive cost breakdown for a product using
   * industrial-standard multi-tier pricing logic.
   */
  static async getProductCostBreakdown(
    productId: string,
    companyId: string,
    location?: string
  ): Promise<ProductCostBreakdown> {

    // Fetch product data
    const product = await db.query.products.findFirst({
      where: and(
        eq(products.id, productId),
        eq(products.company_id, companyId)
      )
    })

    if (!product) {
      throw new Error(`Product ${productId} not found`)
    }

    // ✅ ENHANCED: Extract cost components using pricing hierarchy
    let basePrice = 0
    if (product.base_price && parseFloat(product.base_price) > 0) {
      basePrice = parseFloat(product.base_price)
    } else if (product.cost_price && parseFloat(product.cost_price) > 0) {
      basePrice = parseFloat(product.cost_price)
    } else if (product.price && parseFloat(product.price) > 0) {
      basePrice = parseFloat(product.price)
    } else {
      basePrice = 20 // Fallback price
    }

    const materialCost = basePrice * 0.4  // Assume 40% material cost
    const laborCost = basePrice * 0.25    // Assume 25% labor cost
    const overheadCost = basePrice * 0.15 // Assume 15% overhead cost
    const standardCost = materialCost + laborCost + overheadCost

    // ✅ STEP 2: CHECK FOR CONTRACT PRICING
    const contractPrice = await this.getContractPrice(productId, companyId)
    const lastContractPrice = await this.getLastContractPrice(productId, companyId)

    // ✅ STEP 3: APPLY LOCATION-BASED COSTING
    const locationRule = this.LOCATION_RULES.find(rule => rule.location === location)
    let adjustedCost = standardCost

    if (locationRule) {
      adjustedCost = materialCost // Start with material cost
      if (locationRule.includeLabor) adjustedCost += laborCost
      if (locationRule.includeOverhead) adjustedCost += overheadCost
      adjustedCost *= locationRule.costMultiplier
    }

    // ✅ STEP 4: DETERMINE FINAL PRICE USING HIERARCHY
    let calculatedPrice: number
    let pricingMethod: ProductCostBreakdown['pricingMethod']
    let confidence: ProductCostBreakdown['confidence']

    if (contractPrice && contractPrice > 0) {
      calculatedPrice = contractPrice
      pricingMethod = 'contract'
      confidence = 'high'
    } else if (lastContractPrice && lastContractPrice > 0) {
      calculatedPrice = lastContractPrice
      pricingMethod = 'historical'
      confidence = 'medium'
    } else if (product.base_price && parseFloat(product.base_price) > 0) {
      // ✅ ENHANCED: Use base_price from new pricing system
      calculatedPrice = parseFloat(product.base_price)
      pricingMethod = 'base_price'
      confidence = 'high'
    } else if (product.cost_price && parseFloat(product.cost_price) > 0) {
      // ✅ ENHANCED: Use cost_price with margin for selling price calculation
      const costPrice = parseFloat(product.cost_price)
      const margin = product.margin_percentage ? parseFloat(product.margin_percentage) / 100 : this.getDefaultMargin(product.category)
      calculatedPrice = costPrice * (1 + margin)
      pricingMethod = 'cost_plus_margin'
      confidence = 'high'
    } else if (product.price && parseFloat(product.price) > 0) {
      // ✅ FALLBACK: Legacy price field for backward compatibility
      calculatedPrice = parseFloat(product.price)
      pricingMethod = 'legacy_price'
      confidence = 'medium'
    } else if (standardCost > 0) {
      // Apply default margin based on product category
      const margin = this.getDefaultMargin(product.category)
      calculatedPrice = adjustedCost * (1 + margin)
      pricingMethod = 'standard'
      confidence = 'low'
    } else {
      calculatedPrice = 20 // Fallback price
      pricingMethod = 'fallback'
      confidence = 'low'
    }

    return {
      productId: product.id,
      sku: product.sku || 'N/A',
      name: product.name || 'Unknown Product',

      materialCost: Math.round(materialCost * 100) / 100,
      laborCost: Math.round(laborCost * 100) / 100,
      overheadCost: Math.round(overheadCost * 100) / 100,
      standardCost: Math.round(adjustedCost * 100) / 100,

      contractPrice,
      lastContractPrice,
      sellingPrice: product.price ? parseFloat(product.price) : undefined,
      calculatedPrice: Math.round(calculatedPrice * 100) / 100,

      pricingMethod,
      confidence,
      lastUpdated: new Date()
    }
  }

  /**
   * ✅ GET CURRENT CONTRACT PRICE
   * 
   * Finds the current active contract price for a product.
   */
  private static async getContractPrice(productId: string, companyId: string): Promise<number | undefined> {
    try {
      const contractItem = await db.query.salesContractItems.findFirst({
        where: eq(salesContractItems.product_id, productId),
        with: {
          contract: {
            where: and(
              eq(salesContracts.company_id, companyId),
              eq(salesContracts.status, 'approved')
            )
          }
        },
        orderBy: [desc(salesContractItems.created_at)]
      })

      return contractItem?.unit_price ? parseFloat(contractItem.unit_price) : undefined
    } catch (error) {
      console.error('Error fetching contract price:', error)
      return undefined
    }
  }

  /**
   * ✅ GET LAST CONTRACT PRICE
   * 
   * Finds the most recent historical contract price for a product.
   */
  private static async getLastContractPrice(productId: string, companyId: string): Promise<number | undefined> {
    try {
      const contractItem = await db.query.salesContractItems.findFirst({
        where: eq(salesContractItems.product_id, productId),
        with: {
          contract: {
            where: eq(salesContracts.company_id, companyId)
          }
        },
        orderBy: [desc(salesContractItems.created_at)]
      })

      return contractItem?.unit_price ? parseFloat(contractItem.unit_price) : undefined
    } catch (error) {
      console.error('Error fetching last contract price:', error)
      return undefined
    }
  }

  /**
   * ✅ GET DEFAULT MARGIN BY CATEGORY
   * 
   * Returns appropriate margin based on product category.
   */
  private static getDefaultMargin(category?: string): number {
    if (!category) return this.DEFAULT_MARGINS.finishedGoods

    const cat = category.toLowerCase()
    if (cat.includes('raw') || cat.includes('material')) {
      return this.DEFAULT_MARGINS.rawMaterials
    } else if (cat.includes('custom') || cat.includes('specialty')) {
      return this.DEFAULT_MARGINS.customProducts
    } else {
      return this.DEFAULT_MARGINS.finishedGoods
    }
  }

  /**
   * ✅ BULK CALCULATE INVENTORY VALUES
   * 
   * Calculates values for multiple inventory lots efficiently.
   */
  static async calculateInventoryValues(
    inventoryLots: Array<{
      product_id: string
      qty: string
      location: string
      product?: any
    }>,
    companyId: string
  ): Promise<Array<{
    lot: any
    costBreakdown: ProductCostBreakdown
    totalValue: number
  }>> {

    const results = []

    for (const lot of inventoryLots) {
      try {
        const costBreakdown = await this.getProductCostBreakdown(
          lot.product_id,
          companyId,
          lot.location
        )

        const qty = parseFloat(lot.qty || '0')
        const totalValue = qty * costBreakdown.calculatedPrice

        results.push({
          lot,
          costBreakdown,
          totalValue: Math.round(totalValue * 100) / 100
        })
      } catch (error) {
        console.error(`Error calculating cost for lot ${lot.product_id}:`, error)
        // Fallback calculation
        const qty = parseFloat(lot.qty || '0')
        results.push({
          lot,
          costBreakdown: {
            productId: lot.product_id,
            sku: lot.product?.sku || 'N/A',
            name: lot.product?.name || 'Unknown',
            materialCost: 0,
            laborCost: 0,
            overheadCost: 0,
            standardCost: 20,
            calculatedPrice: 20,
            pricingMethod: 'fallback' as const,
            confidence: 'low' as const,
            lastUpdated: new Date()
          },
          totalValue: qty * 20
        })
      }
    }

    return results
  }
}
