import { relations } from "drizzle-orm"
import { index, pgTable, text, timestamp, boolean, unique, integer } from "drizzle-orm/pg-core"

// BASIC ENTITIES
export const customers = pgTable("customers", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  name: text("name").notNull(),
  contact_name: text("contact_name"),
  contact_phone: text("contact_phone"),
  contact_email: text("contact_email"),
  address: text("address"),
  tax_id: text("tax_id"),
  bank: text("bank"),
  incoterm: text("incoterm"),
  payment_term: text("payment_term"),
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("customers_company_id_idx").on(table.company_id),
}))

export const suppliers = pgTable("suppliers", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  name: text("name").notNull(),
  contact_name: text("contact_name"),
  contact_phone: text("contact_phone"),
  contact_email: text("contact_email"),
  address: text("address"),
  tax_id: text("tax_id"),
  bank: text("bank"),
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("suppliers_company_id_idx").on(table.company_id),
}))

// COMPANY PROFILE - Auth0 Integration
export const companies = pgTable("companies", {
  id: text("id").primaryKey(),
  auth0_user_id: text("auth0_user_id").notNull().unique(), // Auth0 user ID (sub claim)

  // Basic Company Information
  name: text("name").notNull(),
  legal_name: text("legal_name"), // Full legal company name
  registration_number: text("registration_number"), // Business registration number
  tax_id: text("tax_id"), // Tax identification number
  vat_number: text("vat_number"), // VAT registration number

  // Contact Information
  email: text("email"),
  phone: text("phone"),
  website: text("website"),

  // Address Information
  address_line1: text("address_line1"),
  address_line2: text("address_line2"),
  city: text("city"),
  state_province: text("state_province"),
  postal_code: text("postal_code"),
  country: text("country"),

  // Business Information
  industry: text("industry"),
  business_type: text("business_type"), // "manufacturer", "trader", "service", etc.
  employee_count: text("employee_count"),
  annual_revenue: text("annual_revenue"),

  // Banking Information
  bank_name: text("bank_name"),
  bank_account: text("bank_account"),
  bank_swift: text("bank_swift"),
  bank_address: text("bank_address"),
  bank_iban: text("bank_iban"),

  // Settings and Preferences
  currency: text("currency").default("USD"),
  timezone: text("timezone").default("UTC"),
  date_format: text("date_format").default("YYYY-MM-DD"),
  language: text("language").default("en"),

  // Trade Information
  export_license: text("export_license"),
  customs_code: text("customs_code"),
  preferred_incoterms: text("preferred_incoterms").default("FOB"),
  preferred_payment_terms: text("preferred_payment_terms").default("30 days"),

  // System Fields
  status: text("status").default("active"), // "active", "suspended", "pending"
  onboarding_completed: text("onboarding_completed").default("false"),
  onboarding_step: text("onboarding_step").default("basic_info"),
  subscription_plan: text("subscription_plan").default("free"), // "free", "basic", "premium", "enterprise"
  subscription_status: text("subscription_status").default("active"),
  trial_ends_at: timestamp("trial_ends_at", { withTimezone: true }),

  // Audit Fields
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  last_login_at: timestamp("last_login_at", { withTimezone: true }),
}, (table) => ({
  auth0UserIdIdx: index("companies_auth0_user_id_idx").on(table.auth0_user_id),
  nameIdx: index("companies_name_idx").on(table.name),
}))

export const products = pgTable("products", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  sku: text("sku").notNull(),
  name: text("name").notNull(),
  unit: text("unit").notNull(),
  hs_code: text("hs_code"),
  origin: text("origin"),
  package: text("package"),
  image: text("image"),
  category: text("category"),
  description: text("description"),

  // ✅ PRICING SYSTEM: Enhanced pricing fields (backward compatible)
  price: text("price"), // Legacy field - maintained for backward compatibility
  base_price: text("base_price"), // Standard selling price
  cost_price: text("cost_price"), // Cost basis for margin calculations
  margin_percentage: text("margin_percentage"), // Target margin percentage
  currency: text("currency").default("USD"), // Price currency
  price_updated_at: timestamp("price_updated_at", { withTimezone: true }), // Price change tracking

  inspection_required: text("inspection_required").default("false"),
  quality_tolerance: text("quality_tolerance"),
  quality_notes: text("quality_notes"),
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("products_company_id_idx").on(table.company_id),
  skuCompanyIdx: index("products_sku_company_idx").on(table.sku, table.company_id), // Ensure SKU uniqueness per company
}))

// ✅ PRODUCT PRICING HISTORY TABLE
export const productPricingHistory = pgTable("product_pricing_history", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  product_id: text("product_id").notNull().references(() => products.id),

  // Price change details
  old_price: text("old_price"),
  new_price: text("new_price"),
  old_base_price: text("old_base_price"),
  new_base_price: text("new_base_price"),
  old_cost_price: text("old_cost_price"),
  new_cost_price: text("new_cost_price"),
  old_margin_percentage: text("old_margin_percentage"),
  new_margin_percentage: text("new_margin_percentage"),

  // Change metadata
  change_reason: text("change_reason"), // 'manual', 'contract_update', 'cost_adjustment', 'market_update'
  changed_by: text("changed_by"), // User ID who made the change
  change_notes: text("change_notes"),

  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("pricing_history_company_id_idx").on(table.company_id),
  productIdIdx: index("pricing_history_product_id_idx").on(table.product_id),
  createdAtIdx: index("pricing_history_created_at_idx").on(table.created_at),
}))

export const samples = pgTable("samples", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  code: text("code").notNull(),
  name: text("name").notNull(),
  date: text("date").notNull(),
  status: text("status").default("active"),

  // ✅ BIDIRECTIONAL SAMPLE WORKFLOW FIELDS
  sample_direction: text("sample_direction").notNull().default("outbound"), // "outbound" | "inbound" | "internal"
  sample_purpose: text("sample_purpose"), // "customer_evaluation" | "manufacturing_quote" | "material_testing" | "quality_control"
  sender_type: text("sender_type"), // "customer" | "supplier" | "internal" | null
  receiver_type: text("receiver_type"), // "customer" | "supplier" | "internal" | null

  // ✅ ENHANCED RELATIONSHIP FIELDS (Context-Aware)
  customer_id: text("customer_id").references(() => customers.id), // Customer involved (sender OR receiver)
  product_id: text("product_id").references(() => products.id),
  supplier_id: text("supplier_id").references(() => suppliers.id), // Supplier involved (sender OR receiver)
  // work_order_id: text("work_order_id").references(() => workOrders.id), // TODO: Enable after database migration

  // ✅ APPROVAL WORKFLOW FIELDS
  sample_type: text("sample_type").default("development"), // "development", "production", "quality", "prototype"
  approval_status: text("approval_status").default("pending"), // "pending", "approved", "rejected", "revision_required"
  approved_by: text("approved_by"),
  approved_date: text("approved_date"),
  rejection_reason: text("rejection_reason"),

  // ✅ INBOUND SAMPLE TRACKING FIELDS
  received_date: text("received_date"), // When inbound sample was received
  testing_status: text("testing_status").default("not_started"), // "not_started" | "in_progress" | "completed" | "failed"
  testing_results: text("testing_results"), // QC/analysis results for inbound samples
  quote_requested: boolean("quote_requested").default(false), // Whether customer requested manufacturing quote
  quote_provided: boolean("quote_provided").default(false), // Whether we provided quote

  // ✅ BUSINESS SPECIFICATION FIELDS
  notes: text("notes"),
  quantity: text("quantity"),
  unit: text("unit"),
  specifications: text("specifications"), // JSON field for technical specifications
  quality_requirements: text("quality_requirements"),
  delivery_date: text("delivery_date"),
  priority: text("priority").default("normal"), // "low", "normal", "high", "urgent"

  // ✅ COMMERCIAL FIELDS
  cost: text("cost"),
  currency: text("currency").default("USD"),

  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  // ✅ PERFORMANCE INDEXES FOR ALL RELATIONSHIPS
  companyIdIdx: index("samples_company_id_idx").on(table.company_id),
  customerIdIdx: index("samples_customer_id_idx").on(table.customer_id),
  productIdIdx: index("samples_product_id_idx").on(table.product_id),
  supplierIdIdx: index("samples_supplier_id_idx").on(table.supplier_id),
  // workOrderIdIdx: index("samples_work_order_id_idx").on(table.work_order_id), // TODO: Enable after database migration

  // ✅ WORKFLOW AND CLASSIFICATION INDEXES
  sampleDirectionIdx: index("samples_direction_idx").on(table.sample_direction),
  approvalStatusIdx: index("samples_approval_status_idx").on(table.approval_status),
  testingStatusIdx: index("samples_testing_status_idx").on(table.testing_status),
  sampleTypeIdx: index("samples_sample_type_idx").on(table.sample_type),
  priorityIdx: index("samples_priority_idx").on(table.priority),
}))

export const salesContracts = pgTable("sales_contracts", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  customer_id: text("customer_id").notNull().references(() => customers.id),
  template_id: text("template_id").references(() => contractTemplates.id),
  date: text("date").notNull(),
  currency: text("currency"),
  status: text("status").default("draft"),
  content: text("content"), // Rich text contract content
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("sales_contracts_company_id_idx").on(table.company_id),
}))

export const salesContractItems = pgTable("sales_contract_items", {
  id: text("id").primaryKey(),
  contract_id: text("contract_id").notNull().references(() => salesContracts.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  price: text("price").notNull(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  // ✅ PHASE 2 ENHANCEMENT: Pricing context for advanced analytics
  pricing_method: text("pricing_method"), // "base_price", "cost_plus_margin", "negotiated", "market_rate"
  margin_percentage: text("margin_percentage"), // Applied margin percentage
  cost_basis: text("cost_basis"), // Cost used for margin calculation
  price_currency: text("price_currency").default("USD"), // Currency for this line item
}, (table) => ({
  // ✅ PHASE 2 ENHANCEMENT: Analytics indexes
  pricingMethodIdx: index("sales_contract_items_pricing_method_idx").on(table.pricing_method),
  marginIdx: index("sales_contract_items_margin_idx").on(table.margin_percentage),
}))

export const purchaseContracts = pgTable("purchase_contracts", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  supplier_id: text("supplier_id").notNull().references(() => suppliers.id),
  template_id: text("template_id").references(() => contractTemplates.id),
  date: text("date").notNull(),
  currency: text("currency"),
  status: text("status").default("draft"),
  content: text("content"), // Rich text contract content
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("purchase_contracts_company_id_idx").on(table.company_id),
}))

export const purchaseContractItems = pgTable("purchase_contract_items", {
  id: text("id").primaryKey(),
  contract_id: text("contract_id").notNull().references(() => purchaseContracts.id),
  product_id: text("product_id").notNull(), // ✅ FIXED: Remove foreign key constraint to support both products and raw materials
  qty: text("qty").notNull(),
  price: text("price").notNull(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  // ✅ PHASE 2 ENHANCEMENT: Pricing context for advanced analytics
  pricing_method: text("pricing_method"), // "cost_price", "negotiated", "market_rate", "supplier_quote"
  cost_basis: text("cost_basis"), // Reference cost for comparison
  price_currency: text("price_currency").default("USD"), // Currency for this line item
}, (table) => ({
  // ✅ PHASE 2 ENHANCEMENT: Analytics indexes
  pricingMethodIdx: index("purchase_contract_items_pricing_method_idx").on(table.pricing_method),
}))

// Contract Templates Schema
export const contractTemplates = pgTable("contract_templates", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  name: text("name").notNull(),
  type: text("type").notNull(), // 'sales' | 'purchase'
  content: text("content").notNull(), // Rich text template content

  // Template metadata
  currency: text("currency"),
  payment_terms: text("payment_terms"),
  delivery_terms: text("delivery_terms"),
  language: text("language").default("en"),
  version: text("version").default("1"),
  description: text("description"),
  is_default: text("is_default").default("false"), // "true" | "false"
  is_active: text("is_active").default("true"),
  status: text("status").default("active"), // "active" | "inactive" | "archived"

  // Audit fields
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("contract_templates_company_id_idx").on(table.company_id),
}))

export const workOrders = pgTable("work_orders", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  sales_contract_id: text("sales_contract_id").references(() => salesContracts.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  due_date: text("due_date"),
  status: text("status").default("pending"),
  priority: text("priority").default("normal"), // "low", "normal", "high", "urgent"
  notes: text("notes"), // Production notes and instructions

  // ✅ COST TRACKING: Material and labor costs
  material_cost: text("material_cost").default("0"), // Total material cost consumed
  labor_cost: text("labor_cost").default("0"), // Total labor cost
  overhead_cost: text("overhead_cost").default("0"), // Allocated overhead
  total_cost: text("total_cost").default("0"), // Total work order cost

  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("work_orders_company_id_idx").on(table.company_id),
}))

export const workOperations = pgTable("work_operations", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  work_order_id: text("work_order_id").notNull().references(() => workOrders.id),
  operation_name: text("operation_name").notNull(),
  sequence: text("sequence").notNull(),
  status: text("status").default("pending"), // "pending", "in_progress", "completed", "skipped"
  start_time: timestamp("start_time", { withTimezone: true }),
  end_time: timestamp("end_time", { withTimezone: true }),
  notes: text("notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("work_operations_company_id_idx").on(table.company_id),
  workOrderIdIdx: index("work_operations_work_order_id_idx").on(table.work_order_id),
}))

export const stockLots = pgTable("stock_lots", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  location: text("location").notNull(),
  lot_number: text("lot_number"),
  expiry_date: text("expiry_date"),
  status: text("status").default("available"),

  // ✅ QUALITY CONTROL INTEGRATION FIELDS
  quality_status: text("quality_status").default("pending"), // "pending", "approved", "rejected", "quarantined"
  inspection_id: text("inspection_id").references(() => qualityInspections.id),
  quality_approved_date: text("quality_approved_date"),
  quality_approved_by: text("quality_approved_by"),
  quality_notes: text("quality_notes"),

  // ✅ WORK ORDER INTEGRATION FIELDS
  work_order_id: text("work_order_id").references(() => workOrders.id),

  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("stock_lots_company_id_idx").on(table.company_id),
  qualityStatusIdx: index("stock_lots_quality_status_idx").on(table.quality_status),
  inspectionIdIdx: index("stock_lots_inspection_id_idx").on(table.inspection_id),
  workOrderIdIdx: index("stock_lots_work_order_id_idx").on(table.work_order_id),
}))

export const stockTxns = pgTable("stock_txns", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company

  // ✅ ENHANCED: Comprehensive transaction type system
  type: text("type").notNull(), // "inbound" or "outbound" (legacy compatibility)
  transaction_type: text("transaction_type").notNull().default("manual"), // "inbound", "outbound", "transfer", "adjustment"

  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  reference: text("reference"),

  // ✅ ENHANCED: Multi-location transfer support
  from_location: text("from_location"), // Source location for transfers
  to_location: text("to_location"), // Destination location for transfers
  location: text("location"), // Primary location (backward compatibility)

  // ✅ ENHANCED: Transaction categorization and approval
  reason_code: text("reason_code"), // "receipt", "shipment", "transfer", "cycle_count", "damage", "obsolete", "adjustment"
  approval_status: text("approval_status").notNull().default("approved"), // "pending", "approved", "rejected"
  approved_by: text("approved_by"), // User ID who approved the transaction
  approved_at: timestamp("approved_at", { withTimezone: true }),

  // ✅ WORKFLOW AUTOMATION TRACKING FIELDS
  workflow_trigger: text("workflow_trigger"), // "quality_approved", "work_order_completed", "manual", "contract_approved"
  reference_id: text("reference_id"), // quality_inspection_id, work_order_id, sales_contract_id, etc.
  notes: text("notes"), // Additional transaction notes

  // ✅ ENHANCED: Audit and tracking
  created_by: text("created_by"), // User ID who created the transaction
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),

  // ✅ PHASE 1 ENHANCEMENT: Cost tracking for accurate inventory valuation
  unit_cost: text("unit_cost"), // Cost per unit at transaction time
  total_value: text("total_value"), // Total transaction value (qty * unit_cost)
  cost_method: text("cost_method").default("standard"), // "standard", "fifo", "lifo", "average", "actual"
  cost_currency: text("cost_currency").default("USD"), // Currency for cost values
}, (table) => ({
  companyIdIdx: index("stock_txns_company_id_idx").on(table.company_id),
  transactionTypeIdx: index("stock_txns_transaction_type_idx").on(table.transaction_type),
  approvalStatusIdx: index("stock_txns_approval_status_idx").on(table.approval_status),
  workflowTriggerIdx: index("stock_txns_workflow_trigger_idx").on(table.workflow_trigger),
  referenceIdIdx: index("stock_txns_reference_id_idx").on(table.reference_id),
  fromLocationIdx: index("stock_txns_from_location_idx").on(table.from_location),
  toLocationIdx: index("stock_txns_to_location_idx").on(table.to_location),
  createdByIdx: index("stock_txns_created_by_idx").on(table.created_by),
  // ✅ PHASE 1 ENHANCEMENT: Cost tracking indexes
  unitCostIdx: index("stock_txns_unit_cost_idx").on(table.unit_cost),
  costMethodIdx: index("stock_txns_cost_method_idx").on(table.cost_method),
}))

export const declarations = pgTable("declarations", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  status: text("status").default("draft"),
  // ✅ SIMPLE INTEGRATION: Optional Sales Contract Linking
  sales_contract_id: text("sales_contract_id").references(() => salesContracts.id),
  contract_reference: text("contract_reference"), // Human-readable contract number for display
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("declarations_company_id_idx").on(table.company_id),
  salesContractIdIdx: index("declarations_sales_contract_id_idx").on(table.sales_contract_id),
}))

export const declarationItems = pgTable("declaration_items", {
  id: text("id").primaryKey(),
  declaration_id: text("declaration_id").notNull().references(() => declarations.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  hs_code: text("hs_code"), // Harmonized System code for customs

  // ✅ PHASE 1 ENHANCEMENT: Customs value declaration fields
  unit_value: text("unit_value"), // Declared value per unit for customs
  total_value: text("total_value"), // Total declared value (qty * unit_value)
  value_currency: text("value_currency").default("USD"), // Currency for declared values
  value_method: text("value_method"), // "product_price", "contract_price", "declared", "market_value"

  // Quality Integration Fields
  quality_inspection_id: text("quality_inspection_id").references(() => qualityInspections.id),
  quality_status: text("quality_status").default("pending"), // "pending", "approved", "rejected"
  quality_notes: text("quality_notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  // ✅ PHASE 1 ENHANCEMENT: Value declaration indexes for customs compliance
  totalValueIdx: index("declaration_items_total_value_idx").on(table.total_value),
  valueMethodIdx: index("declaration_items_value_method_idx").on(table.value_method),
  valueCurrencyIdx: index("declaration_items_value_currency_idx").on(table.value_currency),
}))

export const documents = pgTable("documents", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  declaration_id: text("declaration_id").references(() => declarations.id), // Fixed: Use consistent field name
  filename: text("filename").notNull(),
  url: text("url").notNull(),
  filetype: text("filetype"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("documents_company_id_idx").on(table.company_id),
}))

export const arInvoices = pgTable("ar_invoices", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  customer_id: text("customer_id").notNull().references(() => customers.id),
  // ✅ ENHANCED: Contract integration for workflow automation
  sales_contract_id: text("sales_contract_id").references(() => salesContracts.id),
  contract_reference: text("contract_reference"), // Human-readable contract number
  date: text("date").notNull(),
  due_date: text("due_date"), // Payment due date
  amount: text("amount").notNull(),
  received: text("received").default("0"), // Amount received/paid
  currency: text("currency").default("USD"),
  status: text("status").default("draft"), // draft, sent, paid, overdue, cancelled
  // ✅ ENHANCED: Payment tracking
  payment_terms: text("payment_terms"), // Net 30, Net 60, etc.
  notes: text("notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("ar_invoices_company_id_idx").on(table.company_id),
  contractIdIdx: index("ar_invoices_contract_id_idx").on(table.sales_contract_id),
}))

export const apInvoices = pgTable("ap_invoices", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  supplier_id: text("supplier_id").notNull().references(() => suppliers.id),
  // ✅ ENHANCED: Purchase contract integration for workflow automation
  purchase_contract_id: text("purchase_contract_id").references(() => purchaseContracts.id),
  contract_reference: text("contract_reference"), // Human-readable contract number
  date: text("date").notNull(),
  due_date: text("due_date"), // Payment due date
  amount: text("amount").notNull(),
  paid: text("paid").default("0"), // Amount paid
  currency: text("currency").default("USD"),
  status: text("status").default("draft"), // draft, received, approved, paid, overdue
  // ✅ ENHANCED: Payment tracking
  payment_terms: text("payment_terms"), // Net 30, Net 60, etc.
  notes: text("notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("ap_invoices_company_id_idx").on(table.company_id),
  contractIdIdx: index("ap_invoices_contract_id_idx").on(table.purchase_contract_id),
}))

// QUALITY CONTROL TABLES
export const qualityInspections = pgTable("quality_inspections", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  work_order_id: text("work_order_id").references(() => workOrders.id),
  inspection_type: text("inspection_type").notNull(), // "incoming", "in-process", "final", "pre-shipment"
  inspector: text("inspector").notNull(),
  inspection_date: text("inspection_date").notNull(),
  status: text("status").default("pending"), // "pending", "in-progress", "completed", "failed"
  notes: text("notes"),
  // ✅ ATTACHMENT FIELDS: Document and photo storage
  attachments: text("attachments"), // JSON array of document filenames
  photos: text("photos"),          // JSON array of photo filenames
  // ✅ ARCHIVE FIELDS: For compliance and audit trail
  archived: boolean("archived").default(false),
  archived_at: timestamp("archived_at", { withTimezone: true }),
  archived_by: text("archived_by"),
  archive_reason: text("archive_reason"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_inspections_company_id_idx").on(table.company_id),
}))

export const qualityDefects = pgTable("quality_defects", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  inspection_id: text("inspection_id").references(() => qualityInspections.id),
  work_order_id: text("work_order_id").references(() => workOrders.id),
  product_id: text("product_id").references(() => products.id),
  defect_type: text("defect_type").notNull(),
  severity: text("severity").notNull(), // "minor", "major", "critical"
  description: text("description").notNull(),
  quantity_affected: text("quantity_affected"),
  corrective_action: text("corrective_action"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_defects_company_id_idx").on(table.company_id),
}))

export const qualityStandards = pgTable("quality_standards", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  product_id: text("product_id").references(() => products.id),
  standard_name: text("standard_name").notNull(),
  specification: text("specification").notNull(),
  tolerance: text("tolerance"),
  test_method: text("test_method"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_standards_company_id_idx").on(table.company_id),
}))

// ============================================================================
// LOCATIONS TABLE - Professional Location Management
// ============================================================================
export const locations = pgTable("locations", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company

  // Basic Location Information
  name: text("name").notNull(),
  type: text("type").notNull(), // warehouse, raw_materials, finished_goods, etc.
  description: text("description"),

  // Capacity and Operational Data
  capacity: integer("capacity").default(0),
  current_utilization: integer("current_utilization").default(0),

  // Location Attributes
  is_active: boolean("is_active").default(true),
  is_temperature_controlled: boolean("is_temperature_controlled").default(false),
  security_level: text("security_level").default("medium"), // low, medium, high

  // Hierarchy and Organization
  parent_location_id: text("parent_location_id").references(() => locations.id),
  location_code: text("location_code"), // Short code for easy reference

  // Address and Physical Details
  address: text("address"),
  floor_level: integer("floor_level"),
  zone: text("zone"),

  // Operational Settings
  allows_mixed_products: boolean("allows_mixed_products").default(true),
  requires_quality_check: boolean("requires_quality_check").default(false),
  automated_handling: boolean("automated_handling").default(false),

  // Audit Fields
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("locations_company_id_idx").on(table.company_id),
  typeIdx: index("locations_type_idx").on(table.type),
  activeIdx: index("locations_active_idx").on(table.is_active),
  parentIdx: index("locations_parent_idx").on(table.parent_location_id),
  codeIdx: index("locations_code_idx").on(table.company_id, table.location_code),
  uniqueCode: unique("locations_company_code_unique").on(table.company_id, table.location_code),
}))

export const qualityCertificates = pgTable("quality_certificates", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  inspection_id: text("inspection_id").references(() => qualityInspections.id),
  certificate_number: text("certificate_number").notNull(),
  certificate_type: text("certificate_type").notNull(), // "COA", "COC", "test_report", "compliance"
  issued_date: text("issued_date").notNull(),
  valid_until: text("valid_until"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_certificates_company_id_idx").on(table.company_id),
}))

export const inspectionResults = pgTable("inspection_results", {
  id: text("id").primaryKey(),
  inspection_id: text("inspection_id").notNull().references(() => qualityInspections.id),
  standard_id: text("standard_id").references(() => qualityStandards.id),
  measured_value: text("measured_value"),
  result: text("result").notNull(), // "pass", "fail", "conditional"
  notes: text("notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
})

// ✅ RAW MATERIALS MANAGEMENT TABLES
export const rawMaterials = pgTable("raw_materials", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  sku: text("sku").notNull(),
  name: text("name").notNull(),
  category: text("category").notNull(), // "yarn", "fabric", "dyes", "chemicals", "accessories"
  unit: text("unit").notNull(), // "kg", "meters", "liters", "pieces"

  // Supplier Integration
  primary_supplier_id: text("primary_supplier_id").references(() => suppliers.id),

  // Specifications
  composition: text("composition"),
  quality_grade: text("quality_grade"),
  specifications: text("specifications"), // JSON field for detailed specs

  // Cost Management
  standard_cost: text("standard_cost"), // Standard cost per unit
  currency: text("currency").default("USD"),

  // Inventory Control
  reorder_point: text("reorder_point").default("0"),
  max_stock_level: text("max_stock_level").default("0"),
  lead_time_days: text("lead_time_days").default("7"),

  // Quality Control
  inspection_required: text("inspection_required").default("false"),
  quality_tolerance: text("quality_tolerance"),
  quality_notes: text("quality_notes"),

  // Status
  status: text("status").default("active"), // "active", "inactive", "discontinued"
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("raw_materials_company_id_idx").on(table.company_id),
  categoryIdx: index("raw_materials_category_idx").on(table.category),
  supplierIdx: index("raw_materials_supplier_idx").on(table.primary_supplier_id),
}))

export const rawMaterialLots = pgTable("raw_material_lots", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  raw_material_id: text("raw_material_id").notNull().references(() => rawMaterials.id),

  // Lot Information
  lot_number: text("lot_number"),
  supplier_id: text("supplier_id").notNull().references(() => suppliers.id),
  purchase_contract_id: text("purchase_contract_id").references(() => purchaseContracts.id),

  // Quantity and Location
  qty: text("qty").notNull(),
  location: text("location").notNull(),

  // Cost Tracking
  unit_cost: text("unit_cost").notNull(),
  total_cost: text("total_cost").notNull(),
  currency: text("currency").default("USD"),

  // Dates
  received_date: text("received_date"),
  expiry_date: text("expiry_date"),

  // Quality Integration
  quality_status: text("quality_status").default("pending"), // "pending", "approved", "rejected", "quarantined"
  inspection_id: text("inspection_id").references(() => qualityInspections.id),
  quality_approved_date: text("quality_approved_date"),
  quality_approved_by: text("quality_approved_by"),
  quality_notes: text("quality_notes"),

  // Status
  status: text("status").default("available"), // "available", "reserved", "consumed", "expired"
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("raw_material_lots_company_id_idx").on(table.company_id),
  materialIdx: index("raw_material_lots_material_idx").on(table.raw_material_id),
  statusIdx: index("raw_material_lots_status_idx").on(table.status),
}))

export const materialConsumption = pgTable("material_consumption", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  work_order_id: text("work_order_id").notNull().references(() => workOrders.id),
  raw_material_lot_id: text("raw_material_lot_id").notNull().references(() => rawMaterialLots.id),

  // Consumption Details
  qty_consumed: text("qty_consumed").notNull(),
  unit_cost: text("unit_cost").notNull(),
  total_cost: text("total_cost").notNull(),

  // Tracking
  consumed_date: text("consumed_date").notNull(),
  consumed_by: text("consumed_by"), // User who recorded consumption
  notes: text("notes"),

  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("material_consumption_company_id_idx").on(table.company_id),
  workOrderIdx: index("material_consumption_work_order_idx").on(table.work_order_id),
}))

export const billOfMaterials = pgTable("bill_of_materials", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  product_id: text("product_id").notNull().references(() => products.id),
  raw_material_id: text("raw_material_id").notNull().references(() => rawMaterials.id),

  // BOM Details
  qty_required: text("qty_required").notNull(), // Quantity needed per unit of finished product
  unit: text("unit").notNull(),
  waste_factor: text("waste_factor").default("0.05"), // 5% waste factor default

  // Status
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("bill_of_materials_company_id_idx").on(table.company_id),
  productIdx: index("bill_of_materials_product_idx").on(table.product_id),
}))

// RELATIONS (same as SQLite schema)
export const companiesRelations = relations(companies, ({ many }) => ({
  customers: many(customers),
  suppliers: many(suppliers),
  products: many(products),
  samples: many(samples),
  salesContracts: many(salesContracts),
  purchaseContracts: many(purchaseContracts),
  contractTemplates: many(contractTemplates),
  workOrders: many(workOrders),
  stockLots: many(stockLots),
  stockTxns: many(stockTxns),
  declarations: many(declarations),
  arInvoices: many(arInvoices),
  apInvoices: many(apInvoices),
  qualityInspections: many(qualityInspections),
  qualityDefects: many(qualityDefects),
  qualityStandards: many(qualityStandards),
  qualityCertificates: many(qualityCertificates),
  // ✅ RAW MATERIALS RELATIONS
  rawMaterials: many(rawMaterials),
  rawMaterialLots: many(rawMaterialLots),
  materialConsumption: many(materialConsumption),
  billOfMaterials: many(billOfMaterials),
  // ✅ ADVANCED MRP SYSTEM RELATIONS
  demandForecasts: many(demandForecasts),
  forecastParameters: many(forecastParameters),
  procurementPlans: many(procurementPlans),
  supplierLeadTimes: many(supplierLeadTimes),
  // ✅ MULTI-CURRENCY SYSTEM RELATIONS
  currencies: many(currencies),
  exchangeRateHistory: many(exchangeRateHistory),
  currencyConversionCache: many(currencyConversionCache),
  financialTransactions: many(financialTransactions),
  currencyExposure: many(currencyExposure),
  // ✅ EXPORT FINANCIAL ANALYTICS RELATIONS
  exportRevenueAnalytics: many(exportRevenueAnalytics),
  containerCostAllocation: many(containerCostAllocation),
  cashFlowForecasts: many(cashFlowForecasts),
  currencyRiskAssessments: many(currencyRiskAssessments),
  // ✅ LOCATION MANAGEMENT RELATIONS
  locations: many(locations),
}))

// ============================================================================
// MULTI-CURRENCY SYSTEM TABLES
// ============================================================================

export const currencies = pgTable("currencies", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  code: text("code").notNull(), // ISO 4217 currency code
  name: text("name").notNull(),
  symbol: text("symbol").notNull(),
  decimal_places: text("decimal_places").notNull().default("2"),
  is_base_currency: text("is_base_currency").notNull().default("false"),
  is_active: text("is_active").notNull().default("true"),
  exchange_rate: text("exchange_rate").default("1.0"),
  last_rate_update: timestamp("last_rate_update", { withTimezone: true }),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("currencies_company_id_idx").on(table.company_id),
  codeIdx: index("currencies_code_idx").on(table.code),
  baseIdx: index("currencies_base_idx").on(table.company_id, table.is_base_currency),
  activeIdx: index("currencies_active_idx").on(table.company_id, table.is_active),
  uniqueCompanyCode: unique().on(table.company_id, table.code),
}))

export const exchangeRateHistory = pgTable("exchange_rate_history", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  from_currency_code: text("from_currency_code").notNull(),
  to_currency_code: text("to_currency_code").notNull(),
  exchange_rate: text("exchange_rate").notNull(),
  effective_date: text("effective_date").notNull(),
  rate_source: text("rate_source").default("manual"),
  rate_type: text("rate_type").default("spot"),
  notes: text("notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("exchange_rate_history_company_id_idx").on(table.company_id),
  currenciesIdx: index("exchange_rate_history_currencies_idx").on(table.from_currency_code, table.to_currency_code),
  dateIdx: index("exchange_rate_history_date_idx").on(table.effective_date),
  sourceIdx: index("exchange_rate_history_source_idx").on(table.rate_source),
}))

export const currencyConversionCache = pgTable("currency_conversion_cache", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  from_currency_code: text("from_currency_code").notNull(),
  to_currency_code: text("to_currency_code").notNull(),
  exchange_rate: text("exchange_rate").notNull(),
  amount_from: text("amount_from").notNull(),
  amount_to: text("amount_to").notNull(),
  conversion_date: text("conversion_date").notNull(),
  cache_expires_at: timestamp("cache_expires_at", { withTimezone: true }).notNull(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("currency_conversion_cache_company_id_idx").on(table.company_id),
  currenciesIdx: index("currency_conversion_cache_currencies_idx").on(table.from_currency_code, table.to_currency_code),
  expiresIdx: index("currency_conversion_cache_expires_idx").on(table.cache_expires_at),
  uniqueConversion: unique().on(table.company_id, table.from_currency_code, table.to_currency_code, table.amount_from, table.conversion_date),
}))

export const financialTransactions = pgTable("financial_transactions", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  transaction_type: text("transaction_type").notNull(),
  reference_id: text("reference_id"),
  reference_type: text("reference_type"),
  account_type: text("account_type").notNull(),
  amount: text("amount").notNull(),
  currency_code: text("currency_code").notNull().default("USD"),
  base_amount: text("base_amount"),
  base_currency_code: text("base_currency_code"),
  exchange_rate: text("exchange_rate").default("1.0"),
  transaction_date: text("transaction_date").notNull(),
  description: text("description"),
  notes: text("notes"),
  status: text("status").notNull().default("pending"),
  created_by: text("created_by").notNull(),
  approved_by: text("approved_by"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("financial_transactions_company_id_idx").on(table.company_id),
  typeIdx: index("financial_transactions_type_idx").on(table.transaction_type),
  referenceIdx: index("financial_transactions_reference_idx").on(table.reference_id, table.reference_type),
  accountIdx: index("financial_transactions_account_idx").on(table.account_type),
  currencyIdx: index("financial_transactions_currency_idx").on(table.currency_code),
  dateIdx: index("financial_transactions_date_idx").on(table.transaction_date),
  statusIdx: index("financial_transactions_status_idx").on(table.status),
}))

export const currencyExposure = pgTable("currency_exposure", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  currency_code: text("currency_code").notNull(),
  exposure_type: text("exposure_type").notNull(),
  total_amount: text("total_amount").notNull().default("0"),
  base_amount: text("base_amount").notNull().default("0"),
  unrealized_gain_loss: text("unrealized_gain_loss").default("0"),
  risk_level: text("risk_level").default("medium"),
  hedge_ratio: text("hedge_ratio").default("0"),
  calculation_date: text("calculation_date").notNull(),
  next_revaluation_date: text("next_revaluation_date"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("currency_exposure_company_id_idx").on(table.company_id),
  currencyIdx: index("currency_exposure_currency_idx").on(table.currency_code),
  typeIdx: index("currency_exposure_type_idx").on(table.exposure_type),
  riskIdx: index("currency_exposure_risk_idx").on(table.risk_level),
  dateIdx: index("currency_exposure_date_idx").on(table.calculation_date),
  uniqueExposure: unique().on(table.company_id, table.currency_code, table.exposure_type, table.calculation_date),
}))

// ============================================================================
// EXPORT FINANCIAL ANALYTICS TABLES
// ============================================================================

export const exportRevenueAnalytics = pgTable("export_revenue_analytics", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  period_type: text("period_type").notNull(),
  period_start: text("period_start").notNull(),
  period_end: text("period_end").notNull(),
  total_revenue_usd: text("total_revenue_usd").notNull().default("0"),
  total_revenue_local: text("total_revenue_local").notNull().default("0"),
  local_currency_code: text("local_currency_code").notNull().default("USD"),
  export_volume_containers: text("export_volume_containers").default("0"),
  export_volume_weight: text("export_volume_weight").default("0"),
  export_destinations_count: text("export_destinations_count").default("0"),
  top_export_destination: text("top_export_destination"),
  currency_breakdown: text("currency_breakdown"), // JSON string
  exchange_rate_impact: text("exchange_rate_impact").default("0"),
  average_order_value: text("average_order_value").default("0"),
  revenue_per_container: text("revenue_per_container").default("0"),
  profit_margin_percentage: text("profit_margin_percentage").default("0"),
  currency_exposure_risk: text("currency_exposure_risk").default("medium"),
  concentration_risk: text("concentration_risk").default("medium"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("export_revenue_analytics_company_id_idx").on(table.company_id),
  periodIdx: index("export_revenue_analytics_period_idx").on(table.period_type, table.period_start, table.period_end),
  currencyIdx: index("export_revenue_analytics_currency_idx").on(table.local_currency_code),
}))

export const containerCostAllocation = pgTable("container_cost_allocation", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  container_id: text("container_id").notNull(),
  container_type: text("container_type").notNull(),
  container_number: text("container_number"),
  shipment_id: text("shipment_id"),
  export_declaration_id: text("export_declaration_id"),
  sales_contract_id: text("sales_contract_id").references(() => salesContracts.id),
  base_shipping_cost: text("base_shipping_cost").notNull().default("0"),
  fuel_surcharge: text("fuel_surcharge").default("0"),
  port_charges: text("port_charges").default("0"),
  customs_fees: text("customs_fees").default("0"),
  insurance_cost: text("insurance_cost").default("0"),
  handling_fees: text("handling_fees").default("0"),
  documentation_fees: text("documentation_fees").default("0"),
  other_charges: text("other_charges").default("0"),
  cost_currency: text("cost_currency").notNull().default("USD"),
  base_cost_amount: text("base_cost_amount"),
  base_currency_code: text("base_currency_code"),
  exchange_rate: text("exchange_rate").default("1.0"),
  total_container_weight: text("total_container_weight").default("0"),
  total_container_volume: text("total_container_volume").default("0"),
  utilization_percentage: text("utilization_percentage").default("0"),
  allocation_method: text("allocation_method").default("weight"),
  allocated_products: text("allocated_products"), // JSON string
  shipping_date: text("shipping_date").notNull(),
  arrival_date: text("arrival_date"),
  cost_date: text("cost_date").notNull(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("container_cost_allocation_company_id_idx").on(table.company_id),
  containerIdx: index("container_cost_allocation_container_idx").on(table.container_id),
  shipmentIdx: index("container_cost_allocation_shipment_idx").on(table.shipment_id),
  contractIdx: index("container_cost_allocation_contract_idx").on(table.sales_contract_id),
  dateIdx: index("container_cost_allocation_date_idx").on(table.shipping_date),
}))

export const cashFlowForecasts = pgTable("cash_flow_forecasts", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  forecast_date: text("forecast_date").notNull(),
  forecast_type: text("forecast_type").notNull(),
  forecast_horizon_days: text("forecast_horizon_days").notNull(),
  expected_ar_collections: text("expected_ar_collections").default("0"),
  expected_contract_payments: text("expected_contract_payments").default("0"),
  expected_export_receipts: text("expected_export_receipts").default("0"),
  other_inflows: text("other_inflows").default("0"),
  total_inflows: text("total_inflows").default("0"),
  expected_ap_payments: text("expected_ap_payments").default("0"),
  expected_payroll: text("expected_payroll").default("0"),
  expected_operating_expenses: text("expected_operating_expenses").default("0"),
  expected_shipping_costs: text("expected_shipping_costs").default("0"),
  expected_raw_material_costs: text("expected_raw_material_costs").default("0"),
  other_outflows: text("other_outflows").default("0"),
  total_outflows: text("total_outflows").default("0"),
  net_cash_flow: text("net_cash_flow").default("0"),
  cumulative_cash_flow: text("cumulative_cash_flow").default("0"),
  currency_risk_adjustment: text("currency_risk_adjustment").default("0"),
  exchange_rate_sensitivity: text("exchange_rate_sensitivity"), // JSON string
  confidence_level: text("confidence_level").default("medium"),
  risk_factors: text("risk_factors"), // JSON string
  best_case_scenario: text("best_case_scenario").default("0"),
  worst_case_scenario: text("worst_case_scenario").default("0"),
  most_likely_scenario: text("most_likely_scenario").default("0"),
  created_by: text("created_by").notNull(),
  forecast_method: text("forecast_method").default("historical"),
  last_updated_at: timestamp("last_updated_at", { withTimezone: true }).defaultNow(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("cash_flow_forecasts_company_id_idx").on(table.company_id),
  dateIdx: index("cash_flow_forecasts_date_idx").on(table.forecast_date),
  typeIdx: index("cash_flow_forecasts_type_idx").on(table.forecast_type),
  horizonIdx: index("cash_flow_forecasts_horizon_idx").on(table.forecast_horizon_days),
}))

export const currencyRiskAssessments = pgTable("currency_risk_assessments", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  assessment_date: text("assessment_date").notNull(),
  assessment_type: text("assessment_type").notNull(),
  base_currency_code: text("base_currency_code").notNull(),
  currency_exposures: text("currency_exposures").notNull(), // JSON string
  total_exposure_base_currency: text("total_exposure_base_currency").default("0"),
  value_at_risk_1day: text("value_at_risk_1day").default("0"),
  value_at_risk_1week: text("value_at_risk_1week").default("0"),
  value_at_risk_1month: text("value_at_risk_1month").default("0"),
  overall_risk_level: text("overall_risk_level").default("medium"),
  highest_risk_currency: text("highest_risk_currency"),
  risk_concentration_ratio: text("risk_concentration_ratio").default("0"),
  hedged_exposure_percentage: text("hedged_exposure_percentage").default("0"),
  unhedged_exposure: text("unhedged_exposure").default("0"),
  hedging_effectiveness: text("hedging_effectiveness").default("0"),
  risk_mitigation_recommendations: text("risk_mitigation_recommendations"), // JSON string
  hedging_recommendations: text("hedging_recommendations"), // JSON string
  created_by: text("created_by").notNull(),
  assessment_method: text("assessment_method").default("historical"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("currency_risk_assessments_company_id_idx").on(table.company_id),
  dateIdx: index("currency_risk_assessments_date_idx").on(table.assessment_date),
  riskIdx: index("currency_risk_assessments_risk_idx").on(table.overall_risk_level),
  currencyIdx: index("currency_risk_assessments_currency_idx").on(table.base_currency_code),
}))

export const customersRelations = relations(customers, ({ one, many }) => ({
  company: one(companies, {
    fields: [customers.company_id],
    references: [companies.id],
  }),
  // ✅ ENHANCED: Sample relationship integration
  samples: many(samples),
  salesContracts: many(salesContracts),
  arInvoices: many(arInvoices),
}))

export const suppliersRelations = relations(suppliers, ({ one, many }) => ({
  company: one(companies, {
    fields: [suppliers.company_id],
    references: [companies.id],
  }),
  // ✅ ENHANCED: Sample relationship integration
  samples: many(samples),
  purchaseContracts: many(purchaseContracts),
  apInvoices: many(apInvoices),
  // ✅ RAW MATERIALS RELATIONS
  rawMaterials: many(rawMaterials),
  rawMaterialLots: many(rawMaterialLots),
  // ✅ ADVANCED MRP SYSTEM RELATIONS
  procurementPlans: many(procurementPlans),
  supplierLeadTimes: many(supplierLeadTimes),
}))

export const productsRelations = relations(products, ({ one, many }) => ({
  company: one(companies, {
    fields: [products.company_id],
    references: [companies.id],
  }),
  // ✅ ENHANCED: Sample relationship integration
  samples: many(samples),
  salesContractItems: many(salesContractItems),
  purchaseContractItems: many(purchaseContractItems),
  workOrders: many(workOrders),
  stockLots: many(stockLots),
  stockTxns: many(stockTxns),
  declarationItems: many(declarationItems),
  qualityDefects: many(qualityDefects),
  qualityStandards: many(qualityStandards),
  // ✅ RAW MATERIALS RELATIONS
  billOfMaterials: many(billOfMaterials),
  // ✅ ADVANCED MRP SYSTEM RELATIONS
  demandForecasts: many(demandForecasts),
  forecastParameters: many(forecastParameters),
}))

export const samplesRelations = relations(samples, ({ one, many }) => ({
  // ✅ CORE ENTITY RELATIONS
  company: one(companies, {
    fields: [samples.company_id],
    references: [companies.id],
  }),
  customer: one(customers, {
    fields: [samples.customer_id],
    references: [customers.id],
  }),
  product: one(products, {
    fields: [samples.product_id],
    references: [products.id],
  }),
  supplier: one(suppliers, {
    fields: [samples.supplier_id],
    references: [suppliers.id],
  }),
  // workOrder: one(workOrders, {
  //   fields: [samples.work_order_id],
  //   references: [workOrders.id],
  // }), // TODO: Enable after database migration

  // ✅ MANUFACTURING WORKFLOW RELATIONS (Future Phase 2 Integration)
  // workOrders: many(workOrders),           // Sample → Work Order generation
  // salesContracts: many(salesContracts),   // Sample → Sales Contract creation

  // ✅ QUALITY CONTROL RELATIONS (Future Phase 3 Integration)
  // qualityInspections: many(qualityInspections), // Sample quality validation
  // qualityStandards: many(qualityStandards),     // Sample quality requirements

  // ✅ INVENTORY RELATIONS (Future Integration)
  // stockLots: many(stockLots),             // Sample inventory tracking
  // stockTxns: many(stockTxns),             // Sample stock movements

  // ✅ EXPORT & COMPLIANCE RELATIONS (Future Integration)
  // declarations: many(declarations),        // Sample-based export docs

  // ✅ FINANCIAL RELATIONS (Future Integration)
  // arInvoices: many(arInvoices),           // Sample billing to customers
}))

export const salesContractsRelations = relations(salesContracts, ({ one, many }) => ({
  company: one(companies, {
    fields: [salesContracts.company_id],
    references: [companies.id],
  }),
  customer: one(customers, {
    fields: [salesContracts.customer_id],
    references: [customers.id],
  }),
  template: one(contractTemplates, {
    fields: [salesContracts.template_id],
    references: [contractTemplates.id],
  }),
  items: many(salesContractItems),
  workOrders: many(workOrders),
  // ✅ SIMPLE INTEGRATION: Export Declarations Relationship
  exportDeclarations: many(declarations),
  // ✅ ENHANCED: Financial integration
  arInvoices: many(arInvoices),
}))

export const salesContractItemsRelations = relations(salesContractItems, ({ one }) => ({
  contract: one(salesContracts, {
    fields: [salesContractItems.contract_id],
    references: [salesContracts.id],
  }),
  product: one(products, {
    fields: [salesContractItems.product_id],
    references: [products.id],
  }),
}))

export const purchaseContractsRelations = relations(purchaseContracts, ({ one, many }) => ({
  company: one(companies, {
    fields: [purchaseContracts.company_id],
    references: [companies.id],
  }),
  supplier: one(suppliers, {
    fields: [purchaseContracts.supplier_id],
    references: [suppliers.id],
  }),
  template: one(contractTemplates, {
    fields: [purchaseContracts.template_id],
    references: [contractTemplates.id],
  }),
  items: many(purchaseContractItems),
  // ✅ ENHANCED: Financial integration
  apInvoices: many(apInvoices),
}))

export const purchaseContractItemsRelations = relations(purchaseContractItems, ({ one }) => ({
  contract: one(purchaseContracts, {
    fields: [purchaseContractItems.contract_id],
    references: [purchaseContracts.id],
  }),
  // ✅ FIXED: Removed product relation since product_id can reference both products and raw materials
  // Application logic will handle the relationship based on the ID prefix (prod_ or rm_)
}))

export const contractTemplatesRelations = relations(contractTemplates, ({ one, many }) => ({
  company: one(companies, {
    fields: [contractTemplates.company_id],
    references: [companies.id],
  }),
  salesContracts: many(salesContracts),
  purchaseContracts: many(purchaseContracts),
}))

export const arInvoicesRelations = relations(arInvoices, ({ one }) => ({
  company: one(companies, {
    fields: [arInvoices.company_id],
    references: [companies.id],
  }),
  customer: one(customers, {
    fields: [arInvoices.customer_id],
    references: [customers.id],
  }),
  // ✅ ENHANCED: Sales contract relationship for workflow integration
  salesContract: one(salesContracts, {
    fields: [arInvoices.sales_contract_id],
    references: [salesContracts.id],
  }),
}))

export const apInvoicesRelations = relations(apInvoices, ({ one }) => ({
  company: one(companies, {
    fields: [apInvoices.company_id],
    references: [companies.id],
  }),
  supplier: one(suppliers, {
    fields: [apInvoices.supplier_id],
    references: [suppliers.id],
  }),
  // ✅ ENHANCED: Purchase contract relationship for workflow integration
  purchaseContract: one(purchaseContracts, {
    fields: [apInvoices.purchase_contract_id],
    references: [purchaseContracts.id],
  }),
}))

// ✅ WORK ORDERS RELATIONS
export const workOrdersRelations = relations(workOrders, ({ one, many }) => ({
  company: one(companies, {
    fields: [workOrders.company_id],
    references: [companies.id],
  }),
  salesContract: one(salesContracts, {
    fields: [workOrders.sales_contract_id],
    references: [salesContracts.id],
  }),
  product: one(products, {
    fields: [workOrders.product_id],
    references: [products.id],
  }),
  // ✅ REVERSE RELATIONSHIP: Work Order ← Sample
  samples: many(samples),
  operations: many(workOperations),
  qualityInspections: many(qualityInspections),
  // ✅ NEW: Work Order → Stock lots relationship
  stockLots: many(stockLots),
  // ✅ RAW MATERIALS RELATIONS
  materialConsumptions: many(materialConsumption),
}))

export const workOperationsRelations = relations(workOperations, ({ one }) => ({
  company: one(companies, {
    fields: [workOperations.company_id],
    references: [companies.id],
  }),
  workOrder: one(workOrders, {
    fields: [workOperations.work_order_id],
    references: [workOrders.id],
  }),
}))

// ✅ QUALITY INSPECTIONS RELATIONS
export const qualityInspectionsRelations = relations(qualityInspections, ({ one, many }) => ({
  company: one(companies, {
    fields: [qualityInspections.company_id],
    references: [companies.id],
  }),
  workOrder: one(workOrders, {
    fields: [qualityInspections.work_order_id],
    references: [workOrders.id],
  }),
  // ✅ NEW: Quality inspection → Stock lots relationship
  stockLots: many(stockLots),
}))

// ✅ INVENTORY RELATIONS
export const stockLotsRelations = relations(stockLots, ({ one }) => ({
  company: one(companies, {
    fields: [stockLots.company_id],
    references: [companies.id],
  }),
  product: one(products, {
    fields: [stockLots.product_id],
    references: [products.id],
  }),
  // ✅ NEW: Quality integration relationships
  inspection: one(qualityInspections, {
    fields: [stockLots.inspection_id],
    references: [qualityInspections.id],
  }),
  workOrder: one(workOrders, {
    fields: [stockLots.work_order_id],
    references: [workOrders.id],
  }),
}))

export const stockTxnsRelations = relations(stockTxns, ({ one }) => ({
  company: one(companies, {
    fields: [stockTxns.company_id],
    references: [companies.id],
  }),
  product: one(products, {
    fields: [stockTxns.product_id],
    references: [products.id],
  }),
}))

// ============================================================================
// EXPORT TRADE MODULE RELATIONS
// ============================================================================

export const declarationsRelations = relations(declarations, ({ one, many }) => ({
  company: one(companies, {
    fields: [declarations.company_id],
    references: [companies.id],
  }),
  // ✅ SIMPLE INTEGRATION: Optional Sales Contract Relationship
  salesContract: one(salesContracts, {
    fields: [declarations.sales_contract_id],
    references: [salesContracts.id],
  }),
  items: many(declarationItems),
  documents: many(documents),
}))

export const declarationItemsRelations = relations(declarationItems, ({ one }) => ({
  declaration: one(declarations, {
    fields: [declarationItems.declaration_id],
    references: [declarations.id],
  }),
  product: one(products, {
    fields: [declarationItems.product_id],
    references: [products.id],
  }),
  qualityInspection: one(qualityInspections, {
    fields: [declarationItems.quality_inspection_id],
    references: [qualityInspections.id],
  }),
}))

export const documentsRelations = relations(documents, ({ one }) => ({
  company: one(companies, {
    fields: [documents.company_id],
    references: [companies.id],
  }),
  declaration: one(declarations, {
    fields: [documents.declaration_id], // Fixed: Use correct field name
    references: [declarations.id],
  }),
}))

// ✅ RAW MATERIALS RELATIONS
export const rawMaterialsRelations = relations(rawMaterials, ({ one, many }) => ({
  company: one(companies, {
    fields: [rawMaterials.company_id],
    references: [companies.id],
  }),
  primarySupplier: one(suppliers, {
    fields: [rawMaterials.primary_supplier_id],
    references: [suppliers.id],
  }),
  lots: many(rawMaterialLots),
  bomItems: many(billOfMaterials),
  // ✅ ADVANCED MRP SYSTEM RELATIONS
  procurementPlans: many(procurementPlans),
  supplierLeadTimes: many(supplierLeadTimes),
}))

export const rawMaterialLotsRelations = relations(rawMaterialLots, ({ one, many }) => ({
  company: one(companies, {
    fields: [rawMaterialLots.company_id],
    references: [companies.id],
  }),
  rawMaterial: one(rawMaterials, {
    fields: [rawMaterialLots.raw_material_id],
    references: [rawMaterials.id],
  }),
  supplier: one(suppliers, {
    fields: [rawMaterialLots.supplier_id],
    references: [suppliers.id],
  }),
  purchaseContract: one(purchaseContracts, {
    fields: [rawMaterialLots.purchase_contract_id],
    references: [purchaseContracts.id],
  }),
  inspection: one(qualityInspections, {
    fields: [rawMaterialLots.inspection_id],
    references: [qualityInspections.id],
  }),
  consumptions: many(materialConsumption),
}))

export const materialConsumptionRelations = relations(materialConsumption, ({ one }) => ({
  company: one(companies, {
    fields: [materialConsumption.company_id],
    references: [companies.id],
  }),
  workOrder: one(workOrders, {
    fields: [materialConsumption.work_order_id],
    references: [workOrders.id],
  }),
  rawMaterialLot: one(rawMaterialLots, {
    fields: [materialConsumption.raw_material_lot_id],
    references: [rawMaterialLots.id],
  }),
}))

export const billOfMaterialsRelations = relations(billOfMaterials, ({ one }) => ({
  company: one(companies, {
    fields: [billOfMaterials.company_id],
    references: [companies.id],
  }),
  product: one(products, {
    fields: [billOfMaterials.product_id],
    references: [products.id],
  }),
  rawMaterial: one(rawMaterials, {
    fields: [billOfMaterials.raw_material_id],
    references: [rawMaterials.id],
  }),
}))

// ============================================================================
// ADVANCED MRP SYSTEM TABLES - PHASE 1A IMPLEMENTATION
// ============================================================================

// DEMAND FORECASTING TABLES
export const demandForecasts = pgTable("demand_forecasts", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  product_id: text("product_id").notNull().references(() => products.id),

  // Forecast Details
  forecast_period: text("forecast_period").notNull(), // "2025-Q1", "2025-02", "2025-W12"
  forecasted_demand: text("forecasted_demand").notNull(), // Quantity forecasted
  confidence_level: text("confidence_level").default("medium"), // "low", "medium", "high"
  forecast_method: text("forecast_method").default("pipeline"), // "pipeline", "historical", "manual", "hybrid"

  // Business Context
  base_data_source: text("base_data_source"), // JSON: contracts, historical data used
  seasonality_applied: text("seasonality_applied").default("false"),
  trend_factor_applied: text("trend_factor_applied").default("1.0"),

  // ✅ SUPPLIER INTEGRATION: Optional supplier preferences (JSON)
  supplier_preferences: text("supplier_preferences"), // JSON: { preferredSuppliers: [], excludedSuppliers: [], maxLeadTime: number }

  // Audit and Tracking
  created_by: text("created_by").notNull(), // User who created forecast
  approved_by: text("approved_by"), // User who approved forecast
  approval_status: text("approval_status").default("draft"), // "draft", "pending", "approved", "rejected"
  notes: text("notes"), // Business notes and assumptions

  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("demand_forecasts_company_id_idx").on(table.company_id),
  productIdIdx: index("demand_forecasts_product_id_idx").on(table.product_id),
  forecastPeriodIdx: index("demand_forecasts_period_idx").on(table.forecast_period),
  approvalStatusIdx: index("demand_forecasts_approval_status_idx").on(table.approval_status),
}))

export const forecastParameters = pgTable("forecast_parameters", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  product_id: text("product_id").notNull().references(() => products.id),

  // Forecasting Parameters
  seasonality_factor: text("seasonality_factor").default("1.0"), // Seasonal adjustment multiplier
  trend_factor: text("trend_factor").default("1.0"), // Growth/decline trend multiplier
  lead_time_buffer_days: text("lead_time_buffer_days").default("14"), // Safety buffer for lead times
  safety_stock_percentage: text("safety_stock_percentage").default("0.15"), // 15% safety stock default

  // Container Shipping Optimization
  container_optimization_enabled: text("container_optimization_enabled").default("true"),
  preferred_container_size: text("preferred_container_size").default("40ft"), // "20ft", "40ft", "40ft_hc"
  minimum_order_efficiency: text("minimum_order_efficiency").default("0.8"), // 80% container utilization minimum

  // Historical Analysis Settings
  historical_periods_to_analyze: text("historical_periods_to_analyze").default("12"), // Months of history
  outlier_detection_enabled: text("outlier_detection_enabled").default("true"),

  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("forecast_parameters_company_id_idx").on(table.company_id),
  productIdIdx: index("forecast_parameters_product_id_idx").on(table.product_id),
  // Unique constraint: one parameter set per product per company
  uniqueProductParams: index("forecast_parameters_unique_product").on(table.company_id, table.product_id),
}))

// PROCUREMENT PLANNING TABLES
export const procurementPlans = pgTable("procurement_plans", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  raw_material_id: text("raw_material_id").notNull().references(() => rawMaterials.id),
  demand_forecast_id: text("demand_forecast_id").references(() => demandForecasts.id),
  planned_qty: text("planned_qty").notNull(),
  target_date: text("target_date").notNull(),
  supplier_id: text("supplier_id").references(() => suppliers.id),
  estimated_cost: text("estimated_cost").default("0"),
  estimated_lead_time: text("estimated_lead_time").default("30"),
  container_optimization: text("container_optimization"),
  priority: text("priority").notNull().default("normal"),
  status: text("status").notNull().default("draft"),
  notes: text("notes"),
  created_by: text("created_by").notNull(),
  approved_by: text("approved_by"),
  approved_at: timestamp("approved_at", { withTimezone: true }),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("procurement_plans_company_id_idx").on(table.company_id),
  rawMaterialIdIdx: index("procurement_plans_raw_material_id_idx").on(table.raw_material_id),
  statusIdx: index("procurement_plans_status_idx").on(table.status),
  targetDateIdx: index("procurement_plans_target_date_idx").on(table.target_date),
  priorityIdx: index("procurement_plans_priority_idx").on(table.priority),
}))

export const supplierLeadTimes = pgTable("supplier_lead_times", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  supplier_id: text("supplier_id").notNull().references(() => suppliers.id),
  raw_material_id: text("raw_material_id").references(() => rawMaterials.id),
  lead_time_days: text("lead_time_days").notNull(),
  minimum_order_qty: text("minimum_order_qty").default("0"),
  maximum_order_qty: text("maximum_order_qty"),
  unit_cost: text("unit_cost"),
  currency: text("currency").default("USD"),
  reliability: text("reliability").notNull().default("good"),
  performance_metrics: text("performance_metrics"),
  notes: text("notes"),
  created_by: text("created_by").notNull(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("supplier_lead_times_company_id_idx").on(table.company_id),
  supplierIdIdx: index("supplier_lead_times_supplier_id_idx").on(table.supplier_id),
  rawMaterialIdIdx: index("supplier_lead_times_raw_material_id_idx").on(table.raw_material_id),
  reliabilityIdx: index("supplier_lead_times_reliability_idx").on(table.reliability),
  leadTimeIdx: index("supplier_lead_times_lead_time_idx").on(table.lead_time_days),
}))

// ============================================================================
// SHIPPING MODULE EXPORTS
// ============================================================================

// Import and export shipping module
export {
  shipments, shipmentsRelations,
  shipmentItems, shipmentItemsRelations,
  shippingDocuments, shippingDocumentsRelations,
  shippingTracking, shippingTrackingRelations,
} from "./schema-shipping"

// ============================================================================
// ADVANCED MRP SYSTEM RELATIONS - PHASE 1A IMPLEMENTATION
// ============================================================================

export const demandForecastsRelations = relations(demandForecasts, ({ one, many }) => ({
  company: one(companies, {
    fields: [demandForecasts.company_id],
    references: [companies.id],
  }),
  product: one(products, {
    fields: [demandForecasts.product_id],
    references: [products.id],
  }),
  procurementPlans: many(procurementPlans),
}))

export const forecastParametersRelations = relations(forecastParameters, ({ one }) => ({
  company: one(companies, {
    fields: [forecastParameters.company_id],
    references: [companies.id],
  }),
  product: one(products, {
    fields: [forecastParameters.product_id],
    references: [products.id],
  }),
}))

export const procurementPlansRelations = relations(procurementPlans, ({ one }) => ({
  company: one(companies, {
    fields: [procurementPlans.company_id],
    references: [companies.id],
  }),
  rawMaterial: one(rawMaterials, {
    fields: [procurementPlans.raw_material_id],
    references: [rawMaterials.id],
  }),
  demandForecast: one(demandForecasts, {
    fields: [procurementPlans.demand_forecast_id],
    references: [demandForecasts.id],
  }),
  supplier: one(suppliers, {
    fields: [procurementPlans.supplier_id],
    references: [suppliers.id],
  }),
}))

export const supplierLeadTimesRelations = relations(supplierLeadTimes, ({ one }) => ({
  company: one(companies, {
    fields: [supplierLeadTimes.company_id],
    references: [companies.id],
  }),
  supplier: one(suppliers, {
    fields: [supplierLeadTimes.supplier_id],
    references: [suppliers.id],
  }),
  rawMaterial: one(rawMaterials, {
    fields: [supplierLeadTimes.raw_material_id],
    references: [rawMaterials.id],
  }),
}))

// ============================================================================
// MULTI-CURRENCY SYSTEM RELATIONS
// ============================================================================

export const currenciesRelations = relations(currencies, ({ one }) => ({
  company: one(companies, {
    fields: [currencies.company_id],
    references: [companies.id],
  }),
}))

export const exchangeRateHistoryRelations = relations(exchangeRateHistory, ({ one }) => ({
  company: one(companies, {
    fields: [exchangeRateHistory.company_id],
    references: [companies.id],
  }),
}))

export const currencyConversionCacheRelations = relations(currencyConversionCache, ({ one }) => ({
  company: one(companies, {
    fields: [currencyConversionCache.company_id],
    references: [companies.id],
  }),
}))

export const financialTransactionsRelations = relations(financialTransactions, ({ one }) => ({
  company: one(companies, {
    fields: [financialTransactions.company_id],
    references: [companies.id],
  }),
}))

export const currencyExposureRelations = relations(currencyExposure, ({ one }) => ({
  company: one(companies, {
    fields: [currencyExposure.company_id],
    references: [companies.id],
  }),
}))

// ============================================================================
// EXPORT FINANCIAL ANALYTICS RELATIONS
// ============================================================================

export const exportRevenueAnalyticsRelations = relations(exportRevenueAnalytics, ({ one }) => ({
  company: one(companies, {
    fields: [exportRevenueAnalytics.company_id],
    references: [companies.id],
  }),
}))

export const containerCostAllocationRelations = relations(containerCostAllocation, ({ one }) => ({
  company: one(companies, {
    fields: [containerCostAllocation.company_id],
    references: [companies.id],
  }),
  salesContract: one(salesContracts, {
    fields: [containerCostAllocation.sales_contract_id],
    references: [salesContracts.id],
  }),
}))

export const cashFlowForecastsRelations = relations(cashFlowForecasts, ({ one }) => ({
  company: one(companies, {
    fields: [cashFlowForecasts.company_id],
    references: [companies.id],
  }),
}))

export const currencyRiskAssessmentsRelations = relations(currencyRiskAssessments, ({ one }) => ({
  company: one(companies, {
    fields: [currencyRiskAssessments.company_id],
    references: [companies.id],
  }),
}))

// ============================================================================
// LOCATIONS RELATIONS
// ============================================================================
export const locationsRelations = relations(locations, ({ one, many }) => ({
  company: one(companies, {
    fields: [locations.company_id],
    references: [companies.id],
  }),
  parentLocation: one(locations, {
    fields: [locations.parent_location_id],
    references: [locations.id],
    relationName: "parentChild"
  }),
  childLocations: many(locations, {
    relationName: "parentChild"
  }),
  // Future: Add relations to stock_lots, shipments, etc. when needed
}))
