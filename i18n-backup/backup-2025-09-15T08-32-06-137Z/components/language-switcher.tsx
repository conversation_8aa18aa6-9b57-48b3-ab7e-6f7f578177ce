"use client"

import { useI18n } from "./i18n-provider"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Globe } from "lucide-react"

export function LanguageSwitcher() {
  const { locale, setLocale } = useI18n()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          aria-label="Change language"
          className="flex items-center gap-2 px-3 py-2 h-auto rounded-full hover:bg-slate-100 transition-colors text-slate-600 hover:text-slate-900"
        >
          <Globe className="h-4 w-4" />
          <span className="text-sm font-medium">
            {locale === "zh" ? "中文" : "EN"}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[120px]">
        <DropdownMenuItem
          onClick={() => setLocale("en")}
          className="flex items-center justify-between"
        >
          <span>English</span>
          {locale === "en" && <span className="text-blue-600">✓</span>}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setLocale("zh")}
          className="flex items-center justify-between"
        >
          <span>中文</span>
          {locale === "zh" && <span className="text-blue-600">✓</span>}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
