#!/usr/bin/env node

/**
 * Manufacturing ERP i18n Restore Script
 * Generated on: 2025-09-15T08:02:56.784Z
 * 
 * This script restores the i18n system to its backed-up state.
 * Use this if you need to rollback changes and return to the working system.
 */

const fs = require('fs');
const path = require('path');

const BACKUP_PATH = 'i18n-backup/backup-2025-09-15T08-02-56-773Z';
const FILES_TO_RESTORE = [
  "components/i18n-provider.tsx",
  "components/language-switcher.tsx",
  "package.json",
  "package-lock.json"
];

console.log('🔄 Restoring Manufacturing ERP i18n system...');
console.log('Backup source:', BACKUP_PATH);

let restored = 0;
let failed = 0;

FILES_TO_RESTORE.forEach(file => {
  try {
    const backupFile = path.join(BACKUP_PATH, file);
    
    if (!fs.existsSync(backupFile)) {
      console.warn(`⚠️  Backup file not found: ${backupFile}`);
      failed++;
      return;
    }
    
    // Create target directory if needed
    const targetDir = path.dirname(file);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }
    
    // Restore file
    fs.copyFileSync(backupFile, file);
    console.log(`✅ Restored: ${file}`);
    restored++;
    
  } catch (error) {
    console.error(`❌ Failed to restore ${file}:`, error.message);
    failed++;
  }
});

console.log(`\n📊 Restore Summary:`);
console.log(`   Restored: ${restored} files`);
console.log(`   Failed: ${failed} files`);

if (failed === 0) {
  console.log('\n✅ i18n system successfully restored to backed-up state!');
  console.log('🔄 Please restart your development server: npm run dev');
} else {
  console.log('\n⚠️  Some files failed to restore. Please check manually.');
}
