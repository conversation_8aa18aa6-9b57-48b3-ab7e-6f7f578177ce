"use client"

import type React from "react"

import { createContext, useContext, useEffect, useMemo, useState } from "react"

export type Locale = "en" | "zh"

type Dict = Record<string, string>

const en: Dict = {
  // App + Nav
  "app.name": "FC-CHINA",
  "nav.group.overview": "Overview",
  "nav.group.master-data": "Master Data",
  "nav.group.sales-purchasing": "Sales & Purchasing", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.sales-process": "Sales Process", // ✅ NEW: Optimized grouping
  "nav.group.production": "Production", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.production-planning": "Production Planning", // ✅ NEW: Optimized grouping
  "nav.group.inventory-logistics": "Inventory & Logistics", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.quality-inventory": "Quality & Inventory", // ✅ NEW: Optimized grouping
  "nav.group.shipping-export": "Shipping & Export", // ✅ NEW: Optimized grouping
  "nav.group.export-trade": "Export & Trade", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.finance-reporting": "Finance & Reporting",
  "nav.group.settings": "Settings", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.administration": "Administration", // ✅ NEW: Optimized grouping
  "nav.item.dashboard": "Dashboard",
  "nav.item.customers": "Customers",
  "nav.item.suppliers": "Suppliers",
  "nav.item.products": "Products",
  "nav.item.samples": "Samples",
  "nav.item.sales-contracts": "Sales Contracts",
  "nav.item.purchase-contracts": "Purchase Contracts",
  "nav.item.contract-templates": "Contract Templates",
  "nav.item.work-orders": "Work Orders",
  "nav.item.bom-management": "Bill of Materials",
  "nav.item.quality-control": "Quality Control",
  "nav.item.mrp-planning": "MRP Planning",
  "nav.item.inventory": "Inventory",
  "nav.item.raw-materials": "Raw Materials",
  "nav.item.locations": "Locations",
  "nav.item.shipping": "Shipping",
  "nav.item.export-declarations": "Export Declarations",
  "nav.item.trade-compliance": "Trade Compliance",
  "nav.item.documentation": "Documentation",
  "nav.item.accounting": "Accounting",
  "nav.item.financial-dashboard": "Financial Dashboard",
  "nav.item.accounts-receivable": "Accounts Receivable",
  "nav.item.accounts-payable": "Accounts Payable",
  "nav.item.reports": "Reports",
  "nav.item.company-profile": "Company Profile",

  // Command
  "cmd.placeholder": "Search or jump to...",

  // Dashboard
  "home.title": "Export Manufacturing ERP",
  "home.subtitle":
    "One place to manage master data, contracts, production, inventory, export declarations, and finance.",
  "home.quick.master": "Add Master Data",
  "home.quick.contract": "Create Contract",
  "home.quick.stock": "Record Stock",
  "home.quick.declaration": "New Declaration",
  "kpi.customers": "Customers",
  "kpi.products": "Products",
  "kpi.suppliers": "Suppliers",
  "kpi.contracts": "Contracts",
  "kpi.suppliers.desc": "Active suppliers",
  "kpi.contracts.desc": "Sales & purchase contracts",
  "kpi.onhand": "On-hand Stock (sum)",
  "kpi.openWos": "Open Work Orders",

  // Dashboard Quick Actions
  "dashboard.quick_actions.title": "Quick Actions",
  "dashboard.quick_actions.subtitle": "Common tasks to get you started",
  "dashboard.quick_actions.manage_customers": "Manage Customers",
  "dashboard.quick_actions.view_products": "View Products",
  "dashboard.quick_actions.create_contract": "Create Sales Contract",
  "dashboard.quick_actions.update_profile": "Update Company Profile",

  // Dashboard System Status
  "dashboard.system_status.title": "System Status",
  "dashboard.system_status.subtitle": "Your ERP system setup progress",
  "dashboard.system_status.company_profile": "Company Profile",
  "dashboard.system_status.customer_database": "Customer Database",
  "dashboard.system_status.product_catalog": "Product Catalog",
  "dashboard.system_status.first_contract": "First Sales Contract",
  "dashboard.system_status.inventory_setup": "Inventory Setup",
  "dashboard.system_status.complete": "Complete",
  "dashboard.system_status.active": "Active",
  "dashboard.system_status.ready": "Ready",
  "dashboard.system_status.pending": "Pending",

  // Dashboard Getting Started
  "dashboard.getting_started.title": "🚀 Getting Started",
  "dashboard.getting_started.subtitle": "Complete these steps to get the most out of your Manufacturing ERP system",
  "dashboard.getting_started.step1.title": "1. Company Setup",
  "dashboard.getting_started.step1.desc": "Your company profile is complete and ready for business.",
  "dashboard.getting_started.step2.title": "2. Create Your First Contract",
  "dashboard.getting_started.step2.desc": "Start generating revenue by creating your first sales contract.",
  "dashboard.getting_started.step2.action": "Create Contract",
  "dashboard.getting_started.step3.title": "3. Set Up Inventory",
  "dashboard.getting_started.step3.desc": "Track your raw materials and finished goods inventory.",
  "dashboard.getting_started.step3.action": "Setup Inventory",
  "sample.title": "Sample Data",
  "sample.desc": "This workspace includes realistic sample data to demonstrate relationships across modules.",
  "sample.reset": "Reset sample data",
  "sample.clear": "Clear sample data",
  "alert.db.title": "Database not configured",
  "alert.db.desc":
    "Set the DATABASE_URL (Neon Postgres). The app runs a safe, one-time auto-migration at first load and seeds a minimal dataset. You can also run scripts/sql/001_init.sql and 002_seed.sql manually.",
  "alert.prep.title": "Preparing your workspace",
  "alert.prep.desc": "The schema is initializing. Refresh in a few seconds.",

  // Common fields/actions
  "field.code": "Code",
  "field.name": "Name",
  "field.spec": "Specification",
  "field.moq": "MOQ",
  "field.inStock": "In Stock",
  "field.contact": "Contact",
  "field.incoterm": "Incoterm",
  "field.paymentTerm": "Payment Term",
  "field.address": "Address",
  "field.sku": "SKU",
  "field.unit": "Unit",
  "field.hsCode": "HS Code",
  "field.origin": "Origin",
  "field.packaging": "Packaging",
  "field.currency": "Currency",
  "field.number": "Number",
  "field.customer": "Customer",
  "field.supplier": "Supplier",
  "field.product": "Product",
  "field.qty": "Qty",
  "field.price": "Price",
  "field.total": "Total",
  "field.woNumber": "WO Number",
  "field.salesContract": "Sales Contract",
  "field.location": "Location",
  "field.note": "Note",
  "field.reference": "Reference",
  "field.declarationNo": "Declaration No.",
  "field.amount": "Amount",
  "field.received": "Received",
  "field.paid": "Paid",
  "field.invoiceNo": "Invoice No.",
  "table.actions": "Actions",
  "table.noData": "No data available.",
  "action.add": "Add",
  "action.addItem": "Add Item",
  "action.remove": "Remove",
  "action.delete": "Delete",
  "action.create": "Create",
  "action.createContract": "Create Contract",
  "action.createPO": "Create PO",
  "action.createWO": "Create WO",
  "action.addInbound": "Add Inbound",
  "action.addOutbound": "Add Outbound",
  "action.createDeclaration": "Create Declaration",
  "action.submit": "Submit",
  "action.createAR": "Create AR",
  "action.createAP": "Create AP",
  "cta.open": "Open",

  // Basic Info
  "basic.samples.title": "Sample Management Center",
  "basic.samples.desc": "Code, name, specs, MOQ, available from stock.",
  "basic.customers.title": "Customers",
  "basic.customers.desc": "Store customer profiles and trading terms.",
  "basic.suppliers.title": "Suppliers",
  "basic.suppliers.desc": "Approved suppliers and contacts.",
  "basic.products.title": "Products/SKUs",
  "basic.products.desc": "Units, HS codes, origin, packaging.",

  // Contracts
  "contracts.sales.title": "Sales Contracts",
  "contracts.sales.desc": "Create basic sales contracts from products and customers.",
  "contracts.sales.empty": "No sales contracts.",
  "contracts.purchase.title": "Purchase Contracts",
  "contracts.purchase.desc": "Issue POs against suppliers.",
  "contracts.purchase.empty": "No purchase contracts.",

  // Production
  "production.title": "Work Orders",
  "production.desc": "Track routing and operation progress.",
  "production.empty": "No work orders.",

  // Inventory
  "inventory.inbound.title": "Inbound (GRN)",
  "inventory.inbound.desc": "Receive goods into stock.",
  "inventory.outbound.title": "Outbound (GDN)",
  "inventory.outbound.desc": "Ship goods and reduce stock.",
  "inventory.stock.title": "Stock",
  "inventory.stock.desc": "Current lots and on-hand summary.",
  "inventory.stock.empty": "No stock lots.",
  "inventory.txns.title": "Stock Transactions",
  "inventory.txns.desc": "FIFO is applied when shipping.",
  "inventory.txns.empty": "No transactions.",

  // Export
  "export.title": "Export Declarations",
  "export.desc": "Validate HS codes and track submission.",
  "export.empty": "No declarations.",

  // Finance
  "finance.title": "Finance & Accounting",
  "finance.description": "Manage invoices, payments, and financial reporting",
  "finance.summary.totalAR": "Total Receivables",
  "finance.summary.outstandingAR": "Outstanding AR",
  "finance.summary.totalAP": "Total Payables",
  "finance.summary.netCashFlow": "Net Cash Flow",

  // Enhanced KPI Dashboard
  "finance.kpis.coreMetrics": "Core Financial Metrics",
  "finance.kpis.totalRevenue": "Total Revenue (YTD)",
  "finance.kpis.totalExpenses": "Total Expenses (YTD)",
  "finance.kpis.profitLoss": "Profit/Loss (YTD)",
  "finance.kpis.netCashFlow": "Net Cash Flow",
  "finance.kpis.overdueIntelligence": "Overdue Intelligence",
  "finance.kpis.overdueAR": "Overdue Receivables",
  "finance.kpis.overdueAP": "Overdue Payables",
  "finance.kpis.manufacturingIntelligence": "Manufacturing Intelligence",
  "finance.kpis.contractProfitability": "Contract Profitability",
  "finance.kpis.avgCollectionDays": "Avg Collection Days",
  "finance.kpis.manufacturingMargin": "Manufacturing Margin",
  "finance.kpis.activeContracts": "Active Contracts",

  // Enhanced AR/AP
  "finance.ar.title": "Accounts Receivable",
  "finance.ar.description": "Track AR invoices and aging with contract integration",
  "finance.ar.invoiceNumber": "Invoice Number",
  "finance.ar.customer": "Customer",
  "finance.ar.salesContract": "Sales Contract",
  "finance.ar.amount": "Amount",
  "finance.ar.received": "Received",
  "finance.ar.currency": "Currency",
  "finance.ar.status": "Status",
  "finance.ar.invoiceDate": "Invoice Date",
  "finance.ar.dueDate": "Due Date",
  "finance.ar.paymentTerms": "Payment Terms",
  "finance.ar.aging": "Aging",
  "finance.ar.contract": "Contract",
  "finance.ar.createInvoice": "Create AR Invoice",
  "finance.ar.noInvoices": "No AR invoices found",

  "finance.ap.title": "Accounts Payable",
  "finance.ap.description": "Track AP invoices and payments with contract integration",
  "finance.ap.invoiceNumber": "Invoice Number",
  "finance.ap.supplier": "Supplier",
  "finance.ap.purchaseContract": "Purchase Contract",
  "finance.ap.amount": "Amount",
  "finance.ap.paid": "Paid",
  "finance.ap.currency": "Currency",
  "finance.ap.status": "Status",
  "finance.ap.invoiceDate": "Invoice Date",
  "finance.ap.dueDate": "Due Date",
  "finance.ap.paymentTerms": "Payment Terms",
  "finance.ap.aging": "Aging",
  "finance.ap.contract": "Contract",
  "finance.ap.createInvoice": "Create AP Invoice",
  "finance.ap.noInvoices": "No AP invoices found",

  // Manufacturing Payment Terms
  "finance.paymentTerms.tt": "TT (Telegraphic Transfer)",
  "finance.paymentTerms.dp": "DP (Documents against Payment)",
  "finance.paymentTerms.lc": "LC (Letter of Credit)",
  "finance.paymentTerms.deposit": "Deposit (Advance Payment)",
  "finance.paymentTerms.depositTT": "30% Deposit + 70% TT",
  "finance.paymentTerms.depositLC": "50% Deposit + 50% LC",

  // Manufacturing Invoice Statuses
  "finance.status.depositReceived": "Deposit Received",
  "finance.status.partialPaid": "Partial Paid",
  "finance.status.depositPaid": "Deposit Paid",
  "finance.ar.title": "Accounts Receivable",
  "finance.ar.desc": "Track AR and aging.",
  "finance.ar.empty": "No AR invoices.",
  "finance.ap.title": "Accounts Payable",
  "finance.ap.desc": "Track AP and payments.",
  "finance.ap.empty": "No AP invoices.",

  // Docs
  "docs.plan.title": "Reference Mind Maps",
  "docs.plan.desc": "Original Chinese and translated English plan visuals used to guide implementation.",

  // Module cards
  "module.master.title": "Master Data",
  "module.master.desc": "Samples, customers, suppliers, products, and trading terms.",
  "module.contracts.title": "Contracts",
  "module.contracts.desc": "Create sales contracts and purchase orders from SKUs.",
  "module.production.title": "Production",
  "module.production.desc": "Generate work orders, track routing and QC.",
  "module.inventory.title": "Inventory",
  "module.inventory.desc": "Inbound/outbound, FIFO, and current lots.",
  "module.export.title": "Export",
  "module.export.desc": "Build declarations and validate HS codes.",
  "module.export.declarations": "Declarations",
  "module.finance.title": "Finance",
  "module.finance.desc": "Track receivables and payables with basic aging.",

  // Landing Page
  "landing.login": "Login",
  "landing.getStarted": "Get Started",
  "landing.learnMore": "Learn More",
  "landing.badge": "Trusted by 500+ Export Manufacturers",
  "landing.hero.title": "All-in-One ERP for Textile Manufacturing & Export",
  "landing.hero.subtitle": "Streamline your textile manufacturing operations from production to export. Manage customers, products, quality control, and international trade compliance in one platform.",
  "landing.features.noCredit": "No credit card required",
  "landing.features.freeTrial": "30-day free trial",
  "landing.features.quickSetup": "Setup in minutes",
  "landing.features.title": "Everything You Need for Export Manufacturing",
  "landing.features.subtitle": "From raw materials to international shipping, manage your entire manufacturing workflow",
  "landing.features.crm.title": "Customer & Supplier Management",
  "landing.features.crm.description": "Comprehensive CRM for managing international customers and suppliers with contact details, payment terms, and trade history.",
  "landing.features.inventory.title": "Product Catalog & Inventory",
  "landing.features.inventory.description": "Detailed product management with SKUs, specifications, quality standards, and real-time inventory tracking.",
  "landing.features.production.title": "Production Management",
  "landing.features.production.description": "Work order management, production scheduling, and real-time tracking from cutting to packaging.",
  "landing.features.quality.title": "Quality Control",
  "landing.features.quality.description": "Integrated quality inspections, defect tracking, and compliance management for export standards.",
  "landing.features.export.title": "Export Documentation",
  "landing.features.export.description": "Automated export declarations, shipping documents, and international trade compliance management.",
  "landing.features.analytics.title": "Analytics & Reporting",
  "landing.features.analytics.description": "Real-time dashboards, production analytics, and comprehensive reporting for business insights.",
  "landing.benefits.title": "Why Choose Our Manufacturing ERP?",
  "landing.benefits.subtitle": "Built specifically for export-oriented textile manufacturers",
  "landing.benefits.speed.title": "50% Faster Operations",
  "landing.benefits.speed.description": "Streamlined workflows reduce manual work and accelerate production cycles",
  "landing.benefits.compliance.title": "100% Compliance",
  "landing.benefits.compliance.description": "Built-in export compliance ensures all international trade requirements are met",
  "landing.benefits.global.title": "Global Ready",
  "landing.benefits.global.description": "Multi-currency, multi-language support for international business operations",
  "landing.cta.title": "Ready to Transform Your Manufacturing?",
  "landing.cta.subtitle": "Join hundreds of manufacturers who have streamlined their operations with our ERP solution",
  "landing.cta.button": "Start Your Free Trial Today",
  "landing.cta.features": "No credit card required • 30-day free trial • Setup in minutes",
  "landing.footer.copyright": "© 2024 FC-CHINA. Built for export manufacturers worldwide.",

  // Landing Page (Hero mock labels)
  "landing.hero.mock.last30days": "Last 30 days",
  "landing.hero.mock.onTimeShipments": "On-Time Shipments",


  // Dashboard Page
  "dashboard.loading": "Loading dashboard...",
  "dashboard.error.title": "Dashboard Error",
  "dashboard.error.description": "This may indicate you need to complete your company profile setup.",
  "dashboard.error.retry": "Retry",
  "dashboard.welcome": "Welcome back",
  "dashboard.subtitle": "Here's what's happening with your manufacturing operations today.",
  "dashboard.stats.customers.title": "Total Customers",
  "dashboard.stats.customers.description": "Active customer accounts",
  "dashboard.stats.products.title": "Products",
  "dashboard.stats.products.description": "Products in catalog",
  "dashboard.stats.suppliers.title": "Suppliers",
  "dashboard.stats.suppliers.description": "Active suppliers",
  "dashboard.stats.contracts.title": "Active Contracts",
  "dashboard.stats.contracts.description": "Sales and purchase contracts",

  // Common UI Elements
  "common.loading": "Loading...",
  "common.error": "Error",
  "common.success": "Success",
  "common.cancel": "Cancel",
  "common.save": "Save",
  "common.delete": "Delete",
  "common.edit": "Edit",
  "common.view": "View",
  "common.add": "Add",
  "common.create": "Create",
  "common.update": "Update",
  "common.search": "Search",
  "common.filter": "Filter",
  "common.actions": "Actions",
  "common.status": "Status",
  "common.name": "Name",
  "common.description": "Description",
  "common.date": "Date",
  "common.created": "Created",
  "common.updated": "Updated",
  "common.active": "Active",
  "common.inactive": "Inactive",
  "common.yes": "Yes",
  "common.no": "No",
  "common.confirm": "Confirm",
  "common.close": "Close",
  "common.retry": "Retry",
  "common.optional": "Optional",

  // Status Values
  "status.active": "Active",
  "status.inactive": "Inactive",
  "status.draft": "Draft",
  "status.approved": "Approved",
  "status.pending": "Pending",
  "status.completed": "Completed",
  "status.cancelled": "Cancelled",
  "status.done": "Done",

  // Table Headers
  "header.wo": "WO",
  "header.operations": "Operations",
  "header.type": "Type",
  "header.time": "Time",
  "header.lot": "Lot",

  // Loading States
  "loading.inventory": "Loading inventory...",
  "loading.try_again": "Try again",

  // Form Fields
  "field.name": "Name",
  "field.email": "Email",
  "field.phone": "Phone",
  "field.address": "Address",
  "field.company": "Company",
  "field.contact": "Contact",
  "field.qty": "Quantity",
  "field.price": "Price",
  "field.total": "Total",
  "field.currency": "Currency",
  "field.status": "Status",
  "field.type": "Type",
  "field.date": "Date",
  "field.notes": "Notes",
  "field.product": "Product",
  "field.customer": "Customer",
  "field.supplier": "Supplier",
  "field.contract": "Contract",
  "field.template": "Template",

  // Customers Page
  "customers.title": "Customers",
  "customers.subtitle": "Manage your customer database and relationships",
  "customers.add": "Add Customer",
  "customers.add.title": "Add New Customer",
  "customers.add.description": "Create a new customer record for your business.",
  "customers.edit.title": "Edit Customer",
  "customers.edit.description": "Update customer information.",
  "customers.delete.title": "Delete Customer",
  "customers.delete.description": "Are you sure you want to delete this customer? This action cannot be undone.",
  "customers.form.company_name": "Company Name",
  "customers.form.contact_name": "Contact Person",
  "customers.form.contact_phone": "Phone Number",
  "customers.form.contact_email": "Email Address",
  "customers.form.address": "Address",
  "customers.form.tax_id": "Tax ID",
  "customers.form.bank": "Bank Details",
  "customers.form.incoterm": "Incoterm",
  "customers.form.payment_term": "Payment Terms",
  "customers.form.status": "Status",
  "customers.table.company_name": "Company Name",
  "customers.table.contact_person": "Contact Person",
  "customers.table.phone": "Phone",
  "customers.table.email": "Email",
  "customers.table.address": "Address",
  "customers.table.incoterm": "Incoterm",
  "customers.table.payment_terms": "Payment Terms",
  "customers.table.status": "Status",
  "customers.table.actions": "Actions",
  "customers.success.created": "Customer created successfully!",
  "customers.success.updated": "Customer updated successfully!",
  "customers.success.deleted": "Customer deleted successfully!",
  "customers.error.create": "Failed to create customer",
  "customers.error.update": "Failed to update customer",
  "customers.error.delete": "Failed to delete customer",
  "customers.empty": "No customers found",
  "customers.empty.description": "Get started by adding your first customer.",

  // Products Page
  "products.title": "Products",
  "products.subtitle": "Manage your product catalog and inventory",
  "products.add": "Add Product",
  "products.add.title": "Add New Product",
  "products.add.description": "Create a new product in your catalog.",
  "products.edit.title": "Edit Product",
  "products.edit.description": "Update product information.",
  "products.delete.title": "Delete Product",
  "products.delete.description": "Are you sure you want to delete this product? This action cannot be undone.",
  "products.form.name": "Product Name",
  "products.form.sku": "SKU",
  "products.form.unit": "Unit",
  "products.form.hs_code": "HS Code",
  "products.form.origin": "Origin",
  "products.form.package": "Package",
  "products.form.status": "Status",

  // ✅ PRICING FIELDS: Enhanced product pricing system
  "products.form.pricing_information": "Pricing Information",
  "products.form.base_price": "Base Price",
  "products.form.cost_price": "Cost Price",
  "products.form.margin_percentage": "Margin %",
  "products.form.currency": "Currency",
  "products.table.name": "Product Name",
  "products.table.sku": "SKU",
  "products.table.unit": "Unit",
  "products.table.hs_code": "HS Code",
  "products.table.origin": "Origin",
  "products.table.package": "Package",
  "products.table.price": "Price",
  "products.table.status": "Status",
  "products.table.actions": "Actions",
  "products.success.created": "Product created successfully!",
  "products.success.updated": "Product updated successfully!",
  "products.success.deleted": "Product deleted successfully!",
  "products.error.create": "Failed to create product",
  "products.error.update": "Failed to update product",
  "products.error.delete": "Failed to delete product",
  "products.empty": "No products found",
  "products.empty.description": "Get started by adding your first product.",
  "products.view.title": "Product Details",
  "products.view.description": "View product information (read-only).",

  // Sales Contracts Page
  "sales_contracts.title": "Sales Contracts",
  "sales_contracts.subtitle": "Manage your company's sales contracts.",
  "sales_contracts.add": "Add Contract",
  "sales_contracts.add.title": "Create Sales Contract",
  "sales_contracts.add.description": "Create a new sales contract for your customer.",
  "sales_contracts.edit.title": "Edit Sales Contract",
  "sales_contracts.edit.description": "Update sales contract information.",
  "sales_contracts.delete.title": "Are you sure?",
  "sales_contracts.delete.description": "This will permanently delete the contract. This action cannot be undone.",
  "sales_contracts.form.number": "Contract Number",
  "sales_contracts.form.customer": "Customer",
  "sales_contracts.form.template": "Template",
  "sales_contracts.form.currency": "Currency",
  "sales_contracts.form.items": "Items",
  "sales_contracts.table.contract_number": "Contract Number",
  "sales_contracts.table.number": "Contract #",
  "sales_contracts.table.customer": "Customer",
  "sales_contracts.table.date": "Date",
  "sales_contracts.table.currency": "Currency",
  "sales_contracts.table.items": "Items",
  "sales_contracts.table.total": "Total",
  "sales_contracts.table.status": "Status",
  "sales_contracts.table.created": "Created",
  "sales_contracts.table.actions": "Actions",
  "sales_contracts.search_placeholder": "Search contracts...",
  "sales_contracts.success.created": "Sales contract created successfully!",
  "sales_contracts.success.updated": "Sales contract updated successfully!",
  "sales_contracts.success.deleted": "Contract deleted successfully.",
  "sales_contracts.error.create": "Failed to create sales contract",
  "sales_contracts.error.update": "Failed to update sales contract",
  "sales_contracts.error.delete": "Failed to delete contract.",
  "sales_contracts.empty": "No sales contracts found",
  "sales_contracts.empty.description": "Create your first sales contract to get started.",

  // Purchase Contracts Page
  "purchase_contracts.title": "Purchase Contracts",
  "purchase_contracts.subtitle": "Manage your company's purchase contracts.",
  "purchase_contracts.add": "Add Contract",
  "purchase_contracts.add.title": "Create Purchase Contract",
  "purchase_contracts.add.description": "Create a new purchase contract with your supplier.",
  "purchase_contracts.edit.title": "Edit Purchase Contract",
  "purchase_contracts.edit.description": "Update purchase contract information.",
  "purchase_contracts.delete.title": "Are you sure?",
  "purchase_contracts.delete.description": "This will permanently delete the contract. This action cannot be undone.",
  "purchase_contracts.form.number": "Contract Number",
  "purchase_contracts.form.supplier": "Supplier",
  "purchase_contracts.form.template": "Template",
  "purchase_contracts.form.currency": "Currency",
  "purchase_contracts.form.items": "Items",
  "purchase_contracts.table.contract_number": "Contract Number",
  "purchase_contracts.table.number": "Contract #",
  "purchase_contracts.table.supplier": "Supplier",
  "purchase_contracts.table.date": "Date",
  "purchase_contracts.table.currency": "Currency",
  "purchase_contracts.table.items": "Items",
  "purchase_contracts.table.total": "Total",
  "purchase_contracts.table.status": "Status",
  "purchase_contracts.table.created": "Created",
  "purchase_contracts.table.actions": "Actions",
  "purchase_contracts.search_placeholder": "Search contracts...",
  "purchase_contracts.success.created": "Purchase contract created successfully!",
  "purchase_contracts.success.updated": "Purchase contract updated successfully!",
  "purchase_contracts.success.deleted": "Contract deleted successfully.",
  "purchase_contracts.error.create": "Failed to create purchase contract",
  "purchase_contracts.error.update": "Failed to update purchase contract",
  "purchase_contracts.error.delete": "Failed to delete contract.",
  "purchase_contracts.empty": "No purchase contracts found",
  "purchase_contracts.empty.description": "Create your first purchase contract to get started.",

  // Suppliers Page
  "suppliers.title": "Suppliers",
  "suppliers.subtitle": "Manage your supplier database and relationships",
  "suppliers.add": "Add Supplier",
  "suppliers.add.title": "Add New Supplier",
  "suppliers.add.description": "Create a new supplier record for your business.",
  "suppliers.edit.title": "Edit Supplier",
  "suppliers.edit.description": "Update supplier information.",
  "suppliers.delete.title": "Delete Supplier",
  "suppliers.delete.description": "Are you sure you want to delete this supplier? This action cannot be undone.",
  "suppliers.delete.confirmation": "This will permanently delete the supplier \"{name}\" and remove their data from our servers.",
  "suppliers.form.name": "Supplier Name",
  "suppliers.form.contact_name": "Contact Person",
  "suppliers.form.contact_phone": "Phone Number",
  "suppliers.form.contact_email": "Email Address",
  "suppliers.form.address": "Address",
  "suppliers.form.bank": "Bank Details",
  "suppliers.form.tax_id": "Tax ID",
  "suppliers.form.status": "Status",
  "suppliers.table.company_name": "Company Name",
  "suppliers.table.name": "Supplier Name",
  "suppliers.table.contact_person": "Contact Person",
  "suppliers.table.phone": "Phone",
  "suppliers.table.email": "Email",
  "suppliers.table.address": "Address",
  "suppliers.table.bank": "Bank Details",
  "suppliers.table.status": "Status",
  "suppliers.table.actions": "Actions",
  "suppliers.success.created": "Supplier created successfully!",
  "suppliers.success.updated": "Supplier updated successfully!",
  "suppliers.success.deleted": "Supplier deleted successfully!",
  "suppliers.error.create": "Failed to create supplier",
  "suppliers.error.update": "Failed to update supplier",
  "suppliers.error.delete": "Failed to delete supplier",
  "suppliers.empty": "No suppliers found",
  "suppliers.empty.description": "Get started by adding your first supplier.",
  "suppliers.view.subtitle": "View supplier details and related purchase contracts",
  "suppliers.view.supplier_info": "Supplier Information",
  "suppliers.view.supplier_info_desc": "Basic supplier details and contact information",
  "suppliers.view.purchase_contracts": "Purchase Contracts",
  "suppliers.view.purchase_contracts_desc": "Related purchase contracts with this supplier",
  "suppliers.view.no_contracts": "No Purchase Contracts",
  "suppliers.view.no_contracts_desc": "This supplier doesn't have any purchase contracts yet.",
  "suppliers.view.create_contract": "Create Purchase Contract",
  "suppliers.view.view_all_contracts": "View All Contracts",

  // Samples Page - Main
  "samples.title": "Sample Management",
  "samples.subtitle": "Manage product samples and approval workflow",
  "samples.add": "New Sample",
  "samples.refresh": "Refresh",
  "samples.loading": "Loading samples...",
  "samples.found": "{count} samples found",

  // Dashboard Cards
  "samples.cards.outbound.title": "📤 Outbound Samples",
  "samples.cards.outbound.description": "We send to customers",
  "samples.cards.inbound.title": "📥 Inbound Samples",
  "samples.cards.inbound.description": "From customers & suppliers",
  "samples.cards.internal.title": "🏭 Internal Samples",
  "samples.cards.internal.description": "R&D and testing",
  "samples.cards.quality.title": "🧪 Quality Pipeline",
  "samples.cards.quality.description": "Awaiting QC approval",

  // Table Headers
  "samples.table.sample": "Sample",
  "samples.table.direction": "Direction",
  "samples.table.purpose": "Purpose",
  "samples.table.relationship": "Relationship",
  "samples.table.product": "Product",
  "samples.table.status": "Status",
  "samples.table.priority": "Priority",
  "samples.table.created": "Created",
  "samples.table.actions": "Actions",
  "samples.table.empty": "No samples found. Create your first sample to get started.",

  // Actions
  "samples.actions.view": "View",
  "samples.actions.edit": "Edit",
  "samples.actions.delete": "Delete",
  "samples.actions.approve": "Approve",
  "samples.actions.reject": "Reject",

  // Sample Filters
  "samples.filters.search": "Search samples by name, code, or notes...",
  "samples.filters.status.all": "All Statuses",
  "samples.filters.status.pending": "Pending",
  "samples.filters.status.approved": "Approved",
  "samples.filters.status.rejected": "Rejected",
  "samples.filters.type.all": "All Types",
  "samples.filters.type.development": "Development",
  "samples.filters.type.production": "Production",
  "samples.filters.type.quality": "Quality",
  "samples.filters.type.prototype": "Prototype",
  "samples.filters.direction.all": "All Directions",
  "samples.filters.direction.outbound": "Outbound",
  "samples.filters.direction.inbound": "Inbound",
  "samples.filters.direction.internal": "Internal",
  "samples.filters.advanced": "Advanced",
  "samples.filters.clear": "Clear Filters",

  // Sample Delete
  "samples.delete.success.title": "Sample Deleted",
  "samples.delete.success.description": "Sample '{name}' has been successfully deleted",
  "samples.delete.error.title": "Delete Failed",
  "samples.delete.error.description": "Failed to delete sample. Please try again.",
  "samples.delete.dialog.title": "Delete Sample",
  "samples.delete.dialog.description": "Are you sure you want to delete this sample? This action cannot be undone.",
  "samples.delete.dialog.warning": "This action is permanent and cannot be undone.",
  "samples.delete.dialog.confirm": "Delete Sample",
  "samples.delete.dialog.deleting": "Deleting...",

  // Sample Fields
  "samples.fields.code": "Sample Code",
  "samples.fields.name": "Sample Name",
  "samples.fields.date": "Date",
  "samples.fields.status": "Status",
  "samples.fields.priority": "Priority",
  "samples.fields.type": "Sample Type",
  "samples.fields.direction": "Direction",
  "samples.fields.purpose": "Purpose",
  "samples.fields.customer": "Customer",
  "samples.fields.supplier": "Supplier",
  "samples.fields.product": "Product",
  "samples.fields.quantity": "Quantity",
  "samples.fields.unit": "Unit",
  "samples.fields.cost": "Cost",
  "samples.fields.currency": "Currency",
  "samples.fields.delivery_date": "Delivery Date",
  "samples.fields.specifications": "Technical Specifications",
  "samples.fields.quality_requirements": "Quality Requirements",
  "samples.fields.notes": "Notes",

  // Sample Create
  "samples.create.title": "Create Sample",
  "samples.create.description": "Create a new sample record for tracking and approval",
  "samples.create.basic_info": "Basic Information",
  "samples.create.workflow": "Sample Workflow",
  "samples.create.relationships": "Business Relationships",
  "samples.create.specifications": "Specifications & Details",
  "samples.create.success.title": "Sample Created",
  "samples.create.success.description": "Sample has been created successfully",
  "samples.create.error.title": "Creation Failed",
  "samples.create.error.description": "Failed to create sample. Please try again.",
  "samples.create.cancel": "Cancel",
  "samples.create.save": "Create Sample",
  "samples.create.saving": "Creating...",

  // Sample View
  "samples.view.back": "Back to Samples",
  "samples.view.pending_request": "Pending Request",
  "samples.view.approved": "Approved",
  "samples.view.rejected": "Rejected",
  "samples.view.edit": "Edit",
  "samples.view.sample_info": "Sample Information",
  "samples.view.sample_type": "Sample Type",
  "samples.view.priority": "Priority",
  "samples.view.sample_date": "Sample Date",
  "samples.view.quantity": "Quantity",
  "samples.view.delivery_date": "Delivery Date",
  "samples.view.cost": "Cost",
  "samples.view.relationships": "Relationships",
  "samples.view.customer": "Customer",
  "samples.view.contact": "Contact",
  "samples.view.email": "Email",
  "samples.view.product": "Product",
  "samples.view.sku": "SKU",
  "samples.view.supplier": "Supplier",
  "samples.view.specifications": "Specifications & Notes",
  "samples.view.technical_specs": "Technical Specifications",
  "samples.view.quality_requirements": "Quality Requirements",
  "samples.view.notes": "Notes",
  "samples.view.approval_history": "Approval History",
  "samples.view.created": "Created",
  "samples.view.revised": "Revised",
  "samples.view.pending": "pending",
  "samples.view.revision_required": "revision_required",
  "samples.view.by_system": "by System",
  "samples.view.by_current_user": "by Current User",
  "samples.view.sample_created": "Sample created and submitted for approval",
  "samples.view.sample_processed": "Sample processed",
  "samples.view.metadata": "Metadata",
  "samples.view.created_date": "Created",
  "samples.view.approved_by": "Approved by",
  "samples.view.approved_date": "Approved Date",

  // Approval History Status Labels
  "samples.view.status.created": "Created",
  "samples.view.status.pending": "Pending",
  "samples.view.status.approved": "Approved",
  "samples.view.status.rejected": "Rejected",
  "samples.view.status.revised": "Revised",
  "samples.view.status.revision_required": "Revision Required",

  // Approval History Actions
  "samples.view.actions.sample_created": "Sample created and submitted for approval",
  "samples.view.actions.sample_processed": "Sample processed",
  "samples.view.actions.sample_approved": "Sample approved",
  "samples.view.actions.sample_rejected": "Sample rejected",
  "samples.view.actions.revision_requested": "Revision requested",

  // User References
  "samples.view.by_system_on": "by System on",
  "samples.view.by_current_user_on": "by Current User on",
  "samples.view.by_user_on": "by {user} on",

  // Sample Edit
  "samples.edit.title": "Edit Sample",
  "samples.edit.description": "Update sample information and specifications",
  "samples.edit.loading": "Loading sample data...",
  "samples.edit.success.title": "Sample Updated",
  "samples.edit.success.description": "Sample has been updated successfully",
  "samples.edit.error.title": "Update Failed",
  "samples.edit.error.description": "Failed to update sample. Please try again.",
  "samples.edit.error.load": "Failed to load sample data. Please try again.",
  "samples.edit.back": "Back to Sample",
  "samples.edit.cancel": "Cancel",
  "samples.edit.save": "Update Sample",
  "samples.edit.saving": "Updating...",
  "samples.edit.validation.name": "Sample name is required",
  "samples.edit.code.readonly": "Sample code cannot be changed",
  "samples.edit.basic_info": "Basic Information",
  "samples.edit.basic_info_desc": "Update the basic sample information",
  "samples.edit.sample_code": "Sample Code",
  "samples.edit.sample_name": "Sample Name",
  "samples.edit.sample_name_placeholder": "Enter sample name",
  "samples.edit.sample_type": "Sample Type",
  "samples.edit.priority": "Priority",
  "samples.edit.relationships": "Relationships",
  "samples.edit.relationships_desc": "Associate this sample with customers, products, and suppliers",
  "samples.edit.customer": "Customer",
  "samples.edit.customer_placeholder": "Search customers...",
  "samples.edit.product": "Product",
  "samples.edit.product_placeholder": "Search products...",
  "samples.edit.supplier": "Supplier",
  "samples.edit.supplier_placeholder": "Search suppliers...",
  "samples.edit.specifications": "Specifications & Details",
  "samples.edit.specifications_desc": "Add technical specifications and additional details",
  "samples.edit.quantity": "Quantity",
  "samples.edit.unit": "Unit",
  "samples.edit.cost": "Cost",
  "samples.edit.currency": "Currency",
  "samples.edit.delivery_date": "Delivery Date",
  "samples.edit.technical_specs": "Technical Specifications",
  "samples.edit.technical_specs_placeholder": "Enter technical specifications...",
  "samples.edit.quality_requirements": "Quality Requirements",
  "samples.edit.quality_requirements_placeholder": "Enter quality requirements...",
  "samples.edit.notes": "Notes",
  "samples.edit.notes_placeholder": "Enter additional notes...",

  // Search Dropdowns
  "samples.edit.search.no_results": "No results found",
  "samples.edit.search.add_new_customer": "Add new customer",
  "samples.edit.search.add_new_product": "Add new product",
  "samples.edit.search.add_new_supplier": "Add new supplier",
  "samples.edit.search.loading": "Loading...",
  "samples.edit.search.type_to_search": "Type to search...",

  // Inventory Page
  "inventory.title": "Inventory Management",
  "inventory.subtitle": "Manage stock levels, inbound and outbound operations",
  "inventory.tabs.inbound": "Inbound",
  "inventory.tabs.outbound": "Outbound",
  "inventory.tabs.stock": "Stock",
  "inventory.tabs.transactions": "Transactions",
  "inventory.inbound.form.product": "Product",
  "inventory.inbound.form.qty": "Quantity",
  "inventory.inbound.form.location": "Location",
  "inventory.inbound.form.ref": "Reference",
  "inventory.inbound.button": "Receive",
  "inventory.outbound.form.product": "Product",
  "inventory.outbound.form.qty": "Quantity",
  "inventory.outbound.form.location": "Location",
  "inventory.outbound.form.ref": "Reference",
  "inventory.outbound.button": "Ship",
  "inventory.stock.loading": "Loading inventory...",
  "inventory.stock.error": "Failed to load inventory data",
  "inventory.stock.retry": "Try again",
  "inventory.stock.table.lot": "Lot",
  "inventory.stock.table.location": "Location",

  // Additional inventory keys
  "inventory.inbound.title": "Inbound",
  "inventory.inbound.desc": "Receive inventory",
  "inventory.outbound.title": "Outbound",
  "inventory.outbound.desc": "Ship inventory",
  "inventory.stock.title": "Stock",
  "inventory.stock.desc": "Current inventory levels",
  "inventory.transactions.title": "Transactions",
  "inventory.transactions.desc": "Inventory movement history",
  "field.location": "Location",
  "field.note": "Note",
  "field.reference": "Reference",
  "action.addInbound": "Receive",
  "action.addOutbound": "Ship",

  // ✅ NEW: Comprehensive inventory transaction translations
  "inventory.transaction_forms": "Transaction Forms",
  "inventory.transaction_history": "Transaction History",
  "inventory.transaction_success": "Transaction Successful",
  "inventory.transaction_error": "Transaction Failed",
  "inventory.inbound": "Inbound",
  "inventory.outbound": "Outbound",
  "inventory.transfer": "Transfer",
  "inventory.adjustment": "Adjustment",
  "inventory.product": "Product",
  "inventory.quantity": "Quantity",
  "inventory.location": "Location",
  "inventory.source_location": "Source Location",
  "inventory.destination_location": "Destination Location",
  "inventory.adjustment_quantity": "Adjustment Quantity",
  "inventory.reason_code": "Reason Code",
  "inventory.notes": "Notes",
  "inventory.reference": "Reference",
  "inventory.status": "Status",
  "inventory.date": "Date",
  "inventory.type": "Type",
  "inventory.select_product": "Select Product",
  "inventory.select_location": "Select Location",
  "inventory.reference_placeholder": "PO/SO number, receipt number, etc.",
  "inventory.notes_placeholder": "Additional notes or comments",
  "inventory.transfer_notes_placeholder": "Reason for transfer",
  "inventory.adjustment_notes_placeholder": "Explain the reason for adjustment",
  "inventory.positive_negative_allowed": "Positive or negative values allowed",
  "inventory.process_inbound": "Process Inbound",
  "inventory.process_outbound": "Process Outbound",
  "inventory.process_transfer": "Process Transfer",
  "inventory.process_adjustment": "Process Adjustment",
  "inventory.adjustment_warning": "Warning",
  "inventory.adjustment_warning_text": "Adjustments directly modify inventory quantities. Ensure proper authorization and documentation.",
  "inventory.search_transactions": "Search transactions...",
  "inventory.filter_by_type": "Filter by Type",
  "inventory.filter_by_location": "Filter by Location",
  "inventory.all_types": "All Types",
  "inventory.all_locations": "All Locations",
  "inventory.no_transactions": "No transactions found",
  "inventory.showing_transactions": "Showing {count} of {total} transactions",
  "inventory.fetch_error": "Failed to Load Data",
  "inventory.adjustment_notes": "Adjustment Notes",
  "inventory.reason_receipt": "Receipt",
  "inventory.reason_shipment": "Shipment",
  "inventory.reason_transfer": "Transfer",
  "inventory.reason_cycle_count": "Cycle Count",
  "inventory.reason_damage": "Damage",
  "inventory.reason_obsolete": "Obsolete",
  "inventory.reason_adjustment": "Adjustment",
  "inventory.reason_return": "Return",
  "inventory.reason_sample": "Sample",
  "inventory.status_pending": "Pending",
  "inventory.status_approved": "Approved",
  "inventory.status_rejected": "Rejected",

  // ✅ NEW: Enhanced Inventory KPI Cards
  "inventory.finishedGoods": "Finished Goods",
  "inventory.rawMaterials": "Raw Materials",
  "inventory.totalValue": "Total Value",

  // Company Profile
  "company.profile.title": "Company Profile",
  "company.profile.subtitle": "Manage your company information and settings",
  "company.profile.not_found": "No Company Profile Found",
  "company.profile.not_found_desc": "It looks like you haven't completed your company profile setup yet.",
  "company.profile.complete_setup": "Complete Company Setup",
  "company.profile.complete": "Complete",
  "company.profile.incomplete": "Incomplete",
  "company.profile.edit": "Edit Profile",
  "company.profile.save": "Save Changes",
  "company.profile.cancel": "Cancel",
  "company.profile.tabs.basic": "Basic Information",
  "company.profile.tabs.business": "Business Details",
  "company.profile.tabs.banking": "Banking",
  "company.profile.tabs.export": "Export & Trade",
  "company.profile.basic.description": "Your company's basic contact and address information",
  "company.profile.business.description": "Business registration and operational information",
  "company.profile.banking.description": "Banking and financial account details",
  "company.profile.export.description": "Export licensing and trade compliance information",
  "company.profile.success.updated": "Company profile updated successfully!",
  "company.profile.error.update": "Failed to update profile",

  // Company Profile Form Fields
  "company.field.name": "Company Name",
  "company.field.legal_name": "Legal Company Name",
  "company.field.email": "Email Address",
  "company.field.phone": "Phone Number",
  "company.field.website": "Website",
  "company.field.country": "Country",
  "company.field.address_line1": "Street Address",
  "company.field.address_line2": "Address Line 2",
  "company.field.city": "City",
  "company.field.state_province": "State/Province",
  "company.field.postal_code": "Postal Code",
  "company.field.industry": "Industry",
  "company.field.business_type": "Business Type",
  "company.field.employee_count": "Employee Count",
  "company.field.annual_revenue": "Annual Revenue",
  "company.field.registration_number": "Registration Number",
  "company.field.tax_id": "Tax ID",
  "company.field.vat_number": "VAT Number",
  "company.field.bank_name": "Bank Name",
  "company.field.bank_account": "Account Number",
  "company.field.bank_swift": "SWIFT/BIC Code",
  "company.field.bank_address": "Bank Address",
  "company.field.export_license": "Export License",
  "company.field.customs_code": "Customs Code",
  "company.field.preferred_incoterms": "Preferred Incoterms",
  "company.field.preferred_payment_terms": "Preferred Payment Terms",

  // Quality Control
  "quality.title": "Quality Control",
  "quality.subtitle": "Manage quality inspections, defect tracking, and compliance reports",
  "quality.metrics.title": "Quality Metrics",
  "quality.metrics.pass_rate": "Pass Rate",
  "quality.metrics.total_inspections": "Total Inspections",
  "quality.metrics.pending": "Pending Inspections",
  "quality.metrics.defect_rate": "Defect Rate",
  "quality.inspections.title": "Recent Inspections",
  "quality.inspections.subtitle": "Latest quality inspection results",
  "quality.defects.title": "Defect Tracking",
  "quality.defects.subtitle": "Track and manage quality defects",

  // Contract Templates
  "contract_templates.title": "Contract Templates",
  "contract_templates.subtitle": "Manage reusable contract templates for sales and purchase agreements",
  "contract_templates.add": "Add Template",
  "contract_templates.table.name": "Template Name",
  "contract_templates.table.type": "Type",
  "contract_templates.table.language": "Language",
  "contract_templates.table.version": "Version",
  "contract_templates.table.status": "Status",
  "contract_templates.table.actions": "Actions",

  // Contract Forms (Add/Edit)
  "contracts.form.number": "Contract Number",
  "contracts.form.customer": "Customer",
  "contracts.form.supplier": "Supplier",
  "contracts.form.currency": "Currency",
  "contracts.form.template": "Template",
  "contracts.form.items": "Contract Items",
  "contracts.form.product": "Product",
  "contracts.form.quantity": "Quantity",
  "contracts.form.price": "Price",
  "contracts.form.total": "Total",
  "contracts.form.add_item": "Add Item",
  "contracts.form.remove_item": "Remove",
  "contracts.form.contract_info": "Contract Information",
  "contracts.form.contract_info_desc": "Basic contract details and customer information",
  "contracts.form.items_section": "Contract Items",
  "contracts.form.items_section_desc": "Products and quantities for this contract",
  "contracts.form.template_section": "Contract Template",
  "contracts.form.template_section_desc": "Choose a template for contract document generation",

  // Contract View
  "contracts.view.back": "Back to Contracts",
  "contracts.view.back_sales": "Back to Sales Contracts",
  "contracts.view.back_purchase": "Back to Purchase Contracts",
  "contracts.view.edit_contract": "Edit Contract",
  "contracts.view.export_pdf": "Export PDF",
  "contracts.view.loading": "Loading contract document...",
  "contracts.view.no_document": "No contract document available",
  "contracts.view.contract_summary": "Contract Summary",
  "contracts.view.contract_document": "Contract Document",
  "contracts.view.customer": "Customer",
  "contracts.view.supplier": "Supplier",
  "contracts.view.contract_date": "Contract Date",
  "contracts.view.total_value": "Total Value",
  "contracts.view.template": "Template",
  "contracts.view.no_email": "No email",
  "contracts.view.items_count": "{count} items",
  "contracts.view.sales_template": "sales template",
  "contracts.view.purchase_template": "purchase template",

  // Contract Edit
  "contracts.edit.title_sales": "Edit Sales Contract",
  "contracts.edit.title_purchase": "Edit Purchase Contract",
  "contracts.edit.subtitle": "Update the details of contract {number}",
  "contracts.edit.back": "Back to Contracts",
  "contracts.edit.template_optional": "Contract Template (Optional)",
  "contracts.edit.template_desc": "sales contract template",
  "contracts.edit.preview": "Preview",
  "contracts.edit.items_title": "Contract Items",
  "contracts.edit.add_item": "Add Item",
  "contracts.edit.product": "Product",
  "contracts.edit.quantity": "Quantity",
  "contracts.edit.unit_price": "Unit Price",
  "contracts.edit.sku_label": "SKU: {sku}",
  "contracts.edit.unit_label": "Unit: {unit}",

  // Contract Creation
  "contracts.create.title_sales": "Create Sales Contract",
  "contracts.create.title_purchase": "Create Purchase Contract",
  "contracts.create.subtitle_sales": "Enter the details for your new sales contract",
  "contracts.create.subtitle_purchase": "Enter the details for your new purchase contract",
  "contracts.create.back_sales": "Sales Contracts",
  "contracts.create.back_purchase": "Purchase Contracts",
  "contracts.create.add_new": "Add New Contract",
  "contracts.create.contract_info": "Contract Information",
  "contracts.create.contract_info_desc": "Basic contract details and customer information",
  "contracts.create.contract_info_desc_purchase": "Basic contract details and supplier information",
  "contracts.create.number": "Contract Number",
  "contracts.create.number_placeholder": "e.g., PC-2025-001",
  "contracts.create.supplier": "Supplier",
  "contracts.create.supplier_placeholder": "Select supplier...",
  "contracts.create.customer": "Customer",
  "contracts.create.customer_placeholder": "Select customer...",
  "contracts.create.currency": "Currency",
  "contracts.create.currency_placeholder": "e.g., USD",
  "contracts.create.template": "Contract Template (Optional)",
  "contracts.create.template_placeholder": "Select template...",
  "contracts.create.items": "Contract Items",
  "contracts.create.items_desc": "Add products and quantities for this contract",
  "contracts.create.add_item": "Add Item",
  "contracts.create.remove_item": "Remove Item",
  "contracts.create.product": "Product",
  "contracts.create.product_placeholder": "Select product...",
  "contracts.create.quantity": "Quantity",
  "contracts.create.quantity_placeholder": "0",
  "contracts.create.unit_price": "Unit Price",
  "contracts.create.unit_price_placeholder": "0.00",
  "contracts.create.total": "Total",
  "contracts.create.cancel": "Cancel",
  "contracts.create.create": "Create Contract",
  "contracts.create.creating": "Creating...",
  "contracts.create.success": "Contract created successfully!",
  "contracts.create.error": "Failed to create contract",
  "contracts.create.summary": "Contract Summary",
  "contracts.create.summary_desc": "Review the total contract value and details",
  "contracts.create.items_count": "Items:",
  "contracts.create.currency_label": "Currency:",
  "contracts.create.total_value": "Total Contract Value:",

  // Contract Templates
  "contracts.templates.page_title": "Contract Templates",
  "contracts.templates.page_desc": "Manage contract templates for sales and purchase agreements",
  "contracts.templates.sales_section": "Sales Contract Templates",
  "contracts.templates.sales_desc": "Create and manage sales contract templates",
  "contracts.templates.purchase_section": "Purchase Contract Templates",
  "contracts.templates.purchase_desc": "Create and manage purchase contract templates",
  "contracts.templates.template_name": "Template Name",
  "contracts.templates.currency": "Currency",
  "contracts.templates.payment_terms": "Payment Terms",
  "contracts.templates.delivery_terms": "Delivery Terms",
  "contracts.templates.template_content": "Template Content",
  "contracts.templates.content_placeholder": "Enter contract template content with placeholders like {{customer_name}}, {{product_name}}, etc.",
  "contracts.templates.content_placeholder_purchase": "Enter contract template content with placeholders like {{supplier_name}}, {{material_name}}, etc.",
  "contracts.templates.create_template": "Create Template",
  "contracts.templates.existing_templates": "Existing Templates",
  "contracts.templates.name": "Name",
  "contracts.templates.actions": "Actions",
  "contracts.templates.payment_30_days": "30 days",
  "contracts.templates.payment_60_days": "60 days",
  "contracts.templates.delivery_fob": "FOB",
  "contracts.templates.delivery_cif": "CIF",
  "contracts.templates.delivery_exw": "EXW",

  // Contract Templates Cards
  "contract_templates.sales.title": "Sales Contract Templates",
  "contract_templates.sales.description": "Create and manage templates for sales contracts",
  "contract_templates.sales.sample": "Sample Templates",
  "contract_templates.sales.sample_title": "Professional Sales Contract Template",
  "contract_templates.sales.sample_desc": "Copy this professional template and paste it into the Template Content field below.",
  "contract_templates.purchase.title": "Purchase Contract Templates",
  "contract_templates.purchase.description": "Create and manage templates for purchase contracts",
  "contract_templates.purchase.sample": "Sample Templates",
  "contract_templates.purchase.sample_title": "Professional Purchase Contract Template",
  "contract_templates.purchase.sample_desc": "Copy this professional template and paste it into the Template Content field below.",

  // Quality Control
  "quality.title": "Quality Control",
  "quality.subtitle": "Manage quality inspections and certificates",
  "quality.attachments.documents.title": "Document Attachments",
  "quality.attachments.documents.upload": "Upload Documents",
  "quality.attachments.documents.formats": "Supported formats: PDF, DOC, DOCX, XLS, XLSX, TXT",
  "quality.attachments.documents.none": "No documents attached",
  "quality.attachments.photos.title": "Photo Attachments",
  "quality.attachments.photos.upload": "Upload Photos",
  "quality.attachments.photos.formats": "Supported formats: JPG, PNG, GIF, WebP",
  "quality.attachments.photos.none": "No photos attached",
  "quality.attachments.preview": "Preview",
  "quality.attachments.download": "Download",
  "quality.attachments.remove": "Remove",
  "quality.attachments.uploading": "Uploading files...",
  "quality.attachments.upload_success": "Upload Successful",
  "quality.attachments.upload_success_desc": "file(s) uploaded successfully",
  "quality.attachments.download_success": "Download Complete",
  "quality.attachments.download_success_desc": "downloaded successfully",
  "quality.attachments.download_failed": "Download Failed",
  "quality.attachments.download_failed_desc": "Failed to download file. Please try again.",
  "quality.attachments.remove_success": "File Removed",
  "quality.attachments.remove_success_desc": "File removed successfully",
  "quality.attachments.remove_failed": "Remove Failed",
  "quality.attachments.remove_failed_desc": "Failed to remove file. Please try again.",

  // Quality Status
  "quality.status": "Quality Status",
  "quality.status.pending": "Pending Quality",
  "quality.status.approved": "Approved",
  "quality.status.quarantined": "Quarantined",
  "quality.status.rejected": "Rejected",

  // Work Order
  "workOrder.title": "Work Order",
  "workOrder.number": "Work Order Number",
  "workOrder.status.completed": "Completed",
  "workOrder.status.pending": "Pending",
  "workOrder.status.in-progress": "In Progress",

  // Product Quality Requirements
  "products.form.quality_requirements": "Quality Requirements",
  "products.form.inspection_required": "Quality Inspection Required",
  "products.form.inspection_required_desc": "Enable this if the product requires quality inspection before approval",
  "products.form.quality_tolerance": "Quality Tolerance",
  "products.form.quality_notes": "Quality Notes",
  "products.form.quality_notes_placeholder": "Enter specific quality requirements, standards, or inspection criteria...",
  "products.quality.not_required": "Not Required",
  "products.success.updated": "Product Updated",
  "products.success.updated_desc": "Product quality requirements have been updated successfully.",
  "products.success.created_desc": "Product with quality requirements has been created successfully.",
  "products.error.update": "Update Failed",

  // Work Orders Quality Gate Modal
  "work_orders.quality_gate.title": "Quality Approval Required",
  "work_orders.quality_gate.description": "This work order cannot be completed until all required quality inspections are approved.",
  "work_orders.quality_gate.work_order_info": "Work Order Information",
  "work_orders.quality_gate.inspections_status": "Quality Inspections Status",
  "work_orders.quality_gate.no_inspections": "No quality inspections found. Inspection may need to be created.",
  "work_orders.quality_gate.completion_status": "Completion Status",
  "work_orders.quality_gate.can_complete": "All quality requirements met. Work order can be completed.",
  "work_orders.quality_gate.cannot_complete": "Quality approval required before completion.",
  "work_orders.quality_gate.pending_inspections": "Pending Inspections",
  "work_orders.quality_gate.complete_inspections_first": "Please complete all pending quality inspections before proceeding.",
  "work_orders.quality_gate.go_to_quality_control": "Go to Quality Control",
  "work_orders.quality_gate.complete_work_order": "Complete Work Order",

  // Additional translations for quality gate modal
  "quality.inspector": "Inspector",
  "quality.inspection_types.final": "Final Inspection",
  "quality.inspection_types.incoming": "Incoming Inspection",
  "quality.inspection_types.in_process": "In-Process Inspection",
  "quality.status.pending": "Pending",
  "quality.status.passed": "Passed",
  "quality.status.failed": "Failed",
  "common.close": "Close",
  "common.processing": "Processing...",
}

const zh: Dict = {
  // App + Nav
  "app.name": "FC-CHINA",
  "nav.group.overview": "总览",
  "nav.group.master-data": "主数据",
  "nav.group.sales-purchasing": "销售采购", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.sales-process": "销售流程", // ✅ NEW: Optimized grouping
  "nav.group.production": "生产管理", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.production-planning": "生产计划", // ✅ NEW: Optimized grouping
  "nav.group.inventory-logistics": "库存物流", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.quality-inventory": "质量库存", // ✅ NEW: Optimized grouping
  "nav.group.shipping-export": "运输出口", // ✅ NEW: Optimized grouping
  "nav.group.export-trade": "出口贸易", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.finance-reporting": "财务报表",
  "nav.group.settings": "设置", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.administration": "系统管理", // ✅ NEW: Optimized grouping
  "nav.item.dashboard": "仪表盘",
  "nav.item.customers": "客户管理",
  "nav.item.suppliers": "供应商",
  "nav.item.products": "产品管理",
  "nav.item.samples": "样品管理",
  "nav.item.sales-contracts": "销售合同",
  "nav.item.purchase-contracts": "采购合同",
  "nav.item.contract-templates": "合同模板",
  "nav.item.work-orders": "生产工单",
  "nav.item.bom-management": "物料清单",
  "nav.item.quality-control": "质量控制",
  "nav.item.mrp-planning": "MRP计划",
  "nav.item.inventory": "库存管理",
  "nav.item.raw-materials": "原材料",
  "nav.item.locations": "仓库位置",
  "nav.item.shipping": "物流运输",
  "nav.item.export-declarations": "出口报关",
  "nav.item.trade-compliance": "贸易合规",
  "nav.item.documentation": "文档管理",
  "nav.item.accounting": "财务会计",
  "nav.item.financial-dashboard": "财务仪表盘",
  "nav.item.accounts-receivable": "应收账款",
  "nav.item.accounts-payable": "应付账款",
  "nav.item.reports": "报表分析",
  "nav.item.company-profile": "公司资料",

  // Command
  "cmd.placeholder": "搜索或快速跳转...",

  // Dashboard
  "home.title": "外贸制造 ERP",
  "home.subtitle": "统一管理主数据、合同、生产、库存、出口报关与财务。",
  "home.quick.master": "添加主数据",
  "home.quick.contract": "创建合同",
  "home.quick.stock": "记录出入库",
  "home.quick.declaration": "新建报关单",
  "kpi.customers": "客户数",
  "kpi.products": "产品数",
  "kpi.suppliers": "供应商",
  "kpi.contracts": "合同",
  "kpi.suppliers.desc": "活跃供应商",
  "kpi.contracts.desc": "销售和采购合同",
  "kpi.onhand": "在库数量（合计）",
  "kpi.openWos": "未完成工单",

  // Dashboard Quick Actions
  "dashboard.quick_actions.title": "快速操作",
  "dashboard.quick_actions.subtitle": "常用任务帮助您快速开始",
  "dashboard.quick_actions.manage_customers": "管理客户",
  "dashboard.quick_actions.view_products": "查看产品",
  "dashboard.quick_actions.create_contract": "创建销售合同",
  "dashboard.quick_actions.update_profile": "更新公司资料",

  // Dashboard System Status
  "dashboard.system_status.title": "系统状态",
  "dashboard.system_status.subtitle": "您的ERP系统设置进度",
  "dashboard.system_status.company_profile": "公司资料",
  "dashboard.system_status.customer_database": "客户数据库",
  "dashboard.system_status.product_catalog": "产品目录",
  "dashboard.system_status.first_contract": "首个销售合同",
  "dashboard.system_status.inventory_setup": "库存设置",
  "dashboard.system_status.complete": "完成",
  "dashboard.system_status.active": "活跃",
  "dashboard.system_status.ready": "就绪",
  "dashboard.system_status.pending": "待处理",

  // Dashboard Getting Started
  "dashboard.getting_started.title": "🚀 快速开始",
  "dashboard.getting_started.subtitle": "完成这些步骤以充分利用您的制造业ERP系统",
  "dashboard.getting_started.step1.title": "1. 公司设置",
  "dashboard.getting_started.step1.desc": "您的公司资料已完成，可以开始业务。",
  "dashboard.getting_started.step2.title": "2. 创建您的第一个合同",
  "dashboard.getting_started.step2.desc": "通过创建您的第一个销售合同开始创收。",
  "dashboard.getting_started.step2.action": "创建合同",
  "dashboard.getting_started.step3.title": "3. 设置库存",
  "dashboard.getting_started.step3.desc": "跟踪您的原材料和成品库存。",
  "dashboard.getting_started.step3.action": "设置库存",
  "sample.title": "示例数据",
  "sample.desc": "此工作区内置了贴近真实的示例数据，便于理解各模块之间的关联。",
  "sample.reset": "重置示例数据",
  "sample.clear": "清空示例数据",
  "alert.db.title": "数据库未配置",
  "alert.db.desc":
    "请设置 DATABASE_URL（Neon Postgres）。应用将在首次加载时安全地自动迁移并种子最小数据。你也可以手动执行 scripts/sql/001_init.sql 和 002_seed.sql。",
  "alert.prep.title": "正在准备工作区",
  "alert.prep.desc": "正在初始化数据库结构，请稍后刷新。",

  // Common fields/actions
  "field.code": "编码",
  "field.name": "名称",
  "field.spec": "规格",
  "field.moq": "起订量",
  "field.inStock": "现货",
  "field.contact": "联系人",
  "field.incoterm": "贸易术语",
  "field.paymentTerm": "付款条款",
  "field.address": "地址",
  "field.sku": "SKU",
  "field.unit": "单位",
  "field.hsCode": "HS 编码",
  "field.origin": "原产地",
  "field.packaging": "包装",
  "field.currency": "币种",
  "field.number": "编号",
  "field.customer": "客户",
  "field.supplier": "供应商",
  "field.product": "产品",
  "field.qty": "数量",
  "field.price": "单价",
  "field.total": "合计",
  "field.woNumber": "工单号",
  "field.salesContract": "销售合同",
  "field.location": "库位",
  "field.note": "备注",
  "field.reference": "参考",
  "field.declarationNo": "报关单号",
  "field.amount": "金额",
  "field.received": "已收",
  "field.paid": "已付",
  "field.invoiceNo": "发票号",
  "table.actions": "操作",
  "table.noData": "暂无数据",
  "action.add": "添加",
  "action.addItem": "添加明细",
  "action.remove": "移除",
  "action.delete": "删除",
  "action.create": "创建",
  "action.createContract": "创建合同",
  "action.createPO": "创建采购单",
  "action.createWO": "创建工单",
  "action.addInbound": "添加入库",
  "action.addOutbound": "添加出库",
  "action.createDeclaration": "创建报关单",
  "action.submit": "提交",
  "action.createAR": "创建应收",
  "action.createAP": "创建应付",
  "cta.open": "进入",

  // Basic Info
  "basic.samples.title": "样品中心",
  "basic.samples.desc": "编码、名称、规格、起订量、是否现货。",
  "basic.customers.title": "客户",
  "basic.customers.desc": "客户档案与贸易条款。",
  "basic.suppliers.title": "供应商",
  "basic.suppliers.desc": "合格供应商与联系方式。",
  "basic.products.title": "产品/SKU",
  "basic.products.desc": "单位、HS 编码、原产地、包装。",

  // Contracts
  "contracts.sales.title": "销售合同",
  "contracts.sales.desc": "根据产品与客户创建销售合同。",
  "contracts.sales.empty": "暂无销售合同。",
  "contracts.purchase.title": "采购合同",
  "contracts.purchase.desc": "向供应商下达采购订单。",
  "contracts.purchase.empty": "暂无采购合同。",

  // Production
  "production.title": "生产工单",
  "production.desc": "跟踪工序进度与路由。",
  "production.empty": "暂无工单。",

  // Inventory
  "inventory.inbound.title": "入库（GRN）",
  "inventory.inbound.desc": "收货并入库。",
  "inventory.outbound.title": "出库（GDN）",
  "inventory.outbound.desc": "发货减少库存。",
  "inventory.stock.title": "库存",
  "inventory.stock.desc": "当前批次与库存汇总。",
  "inventory.stock.empty": "暂无库存批次。",
  "inventory.txns.title": "库存交易",
  "inventory.txns.desc": "出库时应用先进先出（FIFO）。",
  "inventory.txns.empty": "暂无交易记录。",

  // Export
  "export.title": "出口报关",
  "export.desc": "校验 HS 编码并跟踪报关状态。",
  "export.empty": "暂无报关单。",

  // Finance
  "finance.title": "财务会计",
  "finance.description": "管理发票、付款和财务报表",
  "finance.summary.totalAR": "应收总额",
  "finance.summary.outstandingAR": "未收应收",
  "finance.summary.totalAP": "应付总额",
  "finance.summary.netCashFlow": "净现金流",

  // Enhanced KPI Dashboard
  "finance.kpis.coreMetrics": "核心财务指标",
  "finance.kpis.totalRevenue": "总收入（年度）",
  "finance.kpis.totalExpenses": "总支出（年度）",
  "finance.kpis.profitLoss": "损益（年度）",
  "finance.kpis.netCashFlow": "净现金流",
  "finance.kpis.overdueIntelligence": "逾期智能分析",
  "finance.kpis.overdueAR": "逾期应收",
  "finance.kpis.overdueAP": "逾期应付",
  "finance.kpis.manufacturingIntelligence": "制造业智能分析",
  "finance.kpis.contractProfitability": "合同盈利能力",
  "finance.kpis.avgCollectionDays": "平均收款天数",
  "finance.kpis.manufacturingMargin": "制造业利润率",
  "finance.kpis.activeContracts": "活跃合同",

  // Enhanced AR/AP
  "finance.ar.title": "应收账款",
  "finance.ar.description": "跟踪应收发票和账龄，集成合同管理",
  "finance.ar.invoiceNumber": "发票号码",
  "finance.ar.customer": "客户",
  "finance.ar.salesContract": "销售合同",
  "finance.ar.amount": "金额",
  "finance.ar.received": "已收款",
  "finance.ar.currency": "货币",
  "finance.ar.status": "状态",
  "finance.ar.invoiceDate": "发票日期",
  "finance.ar.dueDate": "到期日期",
  "finance.ar.paymentTerms": "付款条件",
  "finance.ar.aging": "账龄",
  "finance.ar.contract": "合同",
  "finance.ar.createInvoice": "创建应收发票",
  "finance.ar.noInvoices": "未找到应收发票",

  "finance.ap.title": "应付账款",
  "finance.ap.description": "跟踪应付发票和付款，集成合同管理",
  "finance.ap.invoiceNumber": "发票号码",
  "finance.ap.supplier": "供应商",
  "finance.ap.purchaseContract": "采购合同",
  "finance.ap.amount": "金额",
  "finance.ap.paid": "已付款",
  "finance.ap.currency": "货币",
  "finance.ap.status": "状态",
  "finance.ap.invoiceDate": "发票日期",
  "finance.ap.dueDate": "到期日期",
  "finance.ap.paymentTerms": "付款条件",
  "finance.ap.aging": "账龄",
  "finance.ap.contract": "合同",
  "finance.ap.createInvoice": "创建应付发票",
  "finance.ap.noInvoices": "未找到应付发票",

  // Manufacturing Payment Terms
  "finance.paymentTerms.tt": "TT（电汇）",
  "finance.paymentTerms.dp": "DP（付款交单）",
  "finance.paymentTerms.lc": "LC（信用证）",
  "finance.paymentTerms.deposit": "定金（预付款）",
  "finance.paymentTerms.depositTT": "30%定金 + 70%电汇",
  "finance.paymentTerms.depositLC": "50%定金 + 50%信用证",

  // Manufacturing Invoice Statuses
  "finance.status.depositReceived": "已收定金",
  "finance.status.partialPaid": "部分付款",
  "finance.status.depositPaid": "已付定金",
  "finance.ar.title": "应收账款",
  "finance.ar.desc": "跟踪应收与账龄。",
  "finance.ar.empty": "暂无应收发票。",
  "finance.ap.title": "应付账款",
  "finance.ap.desc": "跟踪应付与付款。",
  "finance.ap.empty": "暂无应付发票。",

  // Docs
  "docs.plan.title": "参考脑图",
  "docs.plan.desc": "用于指导实施的中文原稿与英文译稿。",

  // 模块卡片
  "module.master.title": "主数据",
  "module.master.desc": "样品、客户、供应商、产品及贸易条款。",
  "module.contracts.title": "合同",
  "module.contracts.desc": "基于 SKU 创建销售合同与采购订单。",
  "module.production.title": "生产",
  "module.production.desc": "生成工单并跟踪工序与质检。",
  "module.inventory.title": "库存",
  "module.inventory.desc": "入库/出库、先进先出及当前库位。",
  "module.export.title": "出口",
  "module.export.desc": "创建报关单并校验 HS 编码。",
  "module.export.declarations": "报关单",
  "module.finance.title": "财务",
  "module.finance.desc": "跟踪应收与应付及基础账龄。",

  // Landing Page
  "landing.login": "登录",
  "landing.getStarted": "开始使用",
  "landing.learnMore": "了解更多",
  "landing.badge": "500+ 出口制造商信赖之选",
  "landing.hero.title": "一体化纺织制造与出口 ERP",
  "landing.hero.subtitle": "从生产到出口，简化您的纺织制造业务流程。在一个平台上管理客户、产品、质量控制和国际贸易合规。",
  "landing.features.noCredit": "无需信用卡",
  "landing.features.freeTrial": "30天免费试用",
  "landing.features.quickSetup": "几分钟完成设置",
  "landing.features.title": "出口制造所需的一切功能",
  "landing.features.subtitle": "从原材料到国际运输，管理您的整个制造工作流程",
  "landing.features.crm.title": "客户与供应商管理",
  "landing.features.crm.description": "全面的客户关系管理系统，管理国际客户和供应商的联系信息、付款条款和贸易历史。",
  "landing.features.inventory.title": "产品目录与库存",
  "landing.features.inventory.description": "详细的产品管理，包括SKU、规格、质量标准和实时库存跟踪。",
  "landing.features.production.title": "生产管理",
  "landing.features.production.description": "工单管理、生产调度，从裁剪到包装的实时跟踪。",
  "landing.features.quality.title": "质量控制",
  "landing.features.quality.description": "集成质量检验、缺陷跟踪和出口标准合规管理。",
  "landing.features.export.title": "出口文档",
  "landing.features.export.description": "自动化出口报关、运输文件和国际贸易合规管理。",
  "landing.features.analytics.title": "分析与报告",
  "landing.features.analytics.description": "实时仪表板、生产分析和全面的业务洞察报告。",
  "landing.benefits.title": "为什么选择我们的制造 ERP？",
  "landing.benefits.subtitle": "专为出口导向的纺织制造商打造",
  "landing.benefits.speed.title": "提升 50% 运营效率",
  "landing.benefits.speed.description": "简化的工作流程减少手工作业，加速生产周期",
  "landing.benefits.compliance.title": "100% 合规保障",
  "landing.benefits.compliance.description": "内置出口合规功能，确保满足所有国际贸易要求",
  "landing.benefits.global.title": "全球化就绪",
  "landing.benefits.global.description": "多币种、多语言支持，满足国际业务运营需求",

  // Landing Page (Hero mock labels)
  "landing.hero.mock.last30days": "近30天",
  "landing.hero.mock.onTimeShipments": "准时发货率",

  "landing.cta.title": "准备好转型您的制造业务了吗？",
  "landing.cta.subtitle": "加入数百家已通过我们的 ERP 解决方案简化运营的制造商",
  "landing.cta.button": "立即开始免费试用",
  "landing.cta.features": "无需信用卡 • 30天免费试用 • 几分钟完成设置",
  "landing.footer.copyright": "© 2024 FC-CHINA. 为全球出口制造商而建。",

  // Dashboard Page
  "dashboard.loading": "正在加载仪表板...",
  "dashboard.error.title": "仪表板错误",
  "dashboard.error.description": "这可能表示您需要完成公司资料设置。",
  "dashboard.error.retry": "重试",
  "dashboard.welcome": "欢迎回来",
  "dashboard.subtitle": "这是您今天制造业务的最新情况。",
  "dashboard.stats.customers.title": "客户总数",
  "dashboard.stats.customers.description": "活跃客户账户",
  "dashboard.stats.products.title": "产品",
  "dashboard.stats.products.description": "目录中的产品",
  "dashboard.stats.suppliers.title": "供应商",
  "dashboard.stats.suppliers.description": "活跃供应商",
  "dashboard.stats.contracts.title": "活跃合同",
  "dashboard.stats.contracts.description": "销售和采购合同",

  // Common UI Elements
  "common.loading": "加载中...",
  "common.error": "错误",
  "common.success": "成功",
  "common.cancel": "取消",
  "common.save": "保存",
  "common.delete": "删除",
  "common.edit": "编辑",
  "common.view": "查看",
  "common.add": "添加",
  "common.create": "创建",
  "common.update": "更新",
  "common.search": "搜索",
  "common.filter": "筛选",
  "common.actions": "操作",
  "common.status": "状态",
  "common.name": "名称",
  "common.description": "描述",
  "common.date": "日期",
  "common.created": "创建时间",
  "common.updated": "更新时间",
  "common.active": "活跃",
  "common.inactive": "非活跃",
  "common.yes": "是",
  "common.no": "否",
  "common.confirm": "确认",
  "common.close": "关闭",
  "common.retry": "重试",
  "common.optional": "可选",

  // Status Values
  "status.active": "活跃",
  "status.inactive": "非活跃",
  "status.draft": "草稿",
  "status.approved": "已审批",
  "status.pending": "待处理",
  "status.completed": "已完成",
  "status.cancelled": "已取消",
  "status.done": "完成",

  // Table Headers
  "header.wo": "工单",
  "header.operations": "工序",
  "header.type": "类型",
  "header.time": "时间",
  "header.lot": "批次",

  // Loading States
  "loading.inventory": "正在加载库存...",
  "loading.try_again": "重试",

  // Form Fields
  "field.name": "名称",
  "field.email": "邮箱",
  "field.phone": "电话",
  "field.address": "地址",
  "field.company": "公司",
  "field.contact": "联系人",
  "field.qty": "数量",
  "field.price": "价格",
  "field.total": "总计",
  "field.currency": "货币",
  "field.status": "状态",
  "field.type": "类型",
  "field.date": "日期",
  "field.notes": "备注",
  "field.product": "产品",
  "field.customer": "客户",
  "field.supplier": "供应商",
  "field.contract": "合同",
  "field.template": "模板",

  // Customers Page
  "customers.title": "客户管理",
  "customers.subtitle": "管理您的客户数据库和关系",
  "customers.add": "添加客户",
  "customers.add.title": "添加新客户",
  "customers.add.description": "为您的业务创建新的客户记录。",
  "customers.edit.title": "编辑客户",
  "customers.edit.description": "更新客户信息。",
  "customers.delete.title": "删除客户",
  "customers.delete.description": "您确定要删除此客户吗？此操作无法撤销。",
  "customers.form.company_name": "公司名称",
  "customers.form.contact_name": "联系人",
  "customers.form.contact_phone": "电话号码",
  "customers.form.contact_email": "邮箱地址",
  "customers.form.address": "地址",
  "customers.form.tax_id": "税号",
  "customers.form.bank": "银行信息",
  "customers.form.incoterm": "贸易条款",
  "customers.form.payment_term": "付款条件",
  "customers.form.status": "状态",
  "customers.table.company_name": "公司名称",
  "customers.table.contact_person": "联系人",
  "customers.table.phone": "电话",
  "customers.table.email": "邮箱",
  "customers.table.address": "地址",
  "customers.table.incoterm": "贸易条款",
  "customers.table.payment_terms": "付款条件",
  "customers.table.status": "状态",
  "customers.table.actions": "操作",
  "customers.success.created": "客户创建成功！",
  "customers.success.updated": "客户更新成功！",
  "customers.success.deleted": "客户删除成功！",
  "customers.error.create": "创建客户失败",
  "customers.error.update": "更新客户失败",
  "customers.error.delete": "删除客户失败",
  "customers.empty": "未找到客户",
  "customers.empty.description": "添加您的第一个客户开始使用。",

  // Products Page
  "products.title": "产品管理",
  "products.subtitle": "管理您的产品目录和库存",
  "products.add": "添加产品",
  "products.add.title": "添加新产品",
  "products.add.description": "在您的目录中创建新产品。",
  "products.edit.title": "编辑产品",
  "products.edit.description": "更新产品信息。",
  "products.delete.title": "删除产品",
  "products.delete.description": "您确定要删除此产品吗？此操作无法撤销。",
  "products.form.name": "产品名称",
  "products.form.sku": "SKU",
  "products.form.unit": "单位",
  "products.form.hs_code": "HS编码",
  "products.form.origin": "原产地",
  "products.form.package": "包装",
  "products.form.status": "状态",

  // ✅ PRICING FIELDS: Enhanced product pricing system
  "products.form.pricing_information": "价格信息",
  "products.form.base_price": "基础价格",
  "products.form.cost_price": "成本价格",
  "products.form.margin_percentage": "利润率 %",
  "products.form.currency": "货币",
  "products.table.name": "产品名称",
  "products.table.sku": "SKU",
  "products.table.unit": "单位",
  "products.table.hs_code": "HS编码",
  "products.table.origin": "原产地",
  "products.table.package": "包装",
  "products.table.price": "价格",
  "products.table.status": "状态",
  "products.table.actions": "操作",
  "products.success.created": "产品创建成功！",
  "products.success.updated": "产品更新成功！",
  "products.success.deleted": "产品删除成功！",
  "products.error.create": "创建产品失败",
  "products.error.update": "更新产品失败",
  "products.error.delete": "删除产品失败",
  "products.empty": "未找到产品",
  "products.empty.description": "添加您的第一个产品开始使用。",
  "products.view.title": "产品详情",
  "products.view.description": "查看产品信息（只读）。",

  // Sales Contracts Page
  "sales_contracts.title": "销售合同",
  "sales_contracts.subtitle": "管理您公司的销售合同。",
  "sales_contracts.add": "添加合同",
  "sales_contracts.add.title": "创建销售合同",
  "sales_contracts.add.description": "为您的客户创建新的销售合同。",
  "sales_contracts.edit.title": "编辑销售合同",
  "sales_contracts.edit.description": "更新销售合同信息。",
  "sales_contracts.delete.title": "您确定吗？",
  "sales_contracts.delete.description": "这将永久删除该合同。此操作无法撤销。",
  "sales_contracts.form.number": "合同编号",
  "sales_contracts.form.customer": "客户",
  "sales_contracts.form.template": "模板",
  "sales_contracts.form.currency": "货币",
  "sales_contracts.form.items": "明细",
  "sales_contracts.table.contract_number": "合同编号",
  "sales_contracts.table.number": "合同编号",
  "sales_contracts.table.customer": "客户",
  "sales_contracts.table.date": "日期",
  "sales_contracts.table.currency": "货币",
  "sales_contracts.table.items": "明细",
  "sales_contracts.table.total": "总计",
  "sales_contracts.table.status": "状态",
  "sales_contracts.table.created": "创建时间",
  "sales_contracts.table.actions": "操作",
  "sales_contracts.search_placeholder": "搜索合同...",
  "sales_contracts.success.created": "销售合同创建成功！",
  "sales_contracts.success.updated": "销售合同更新成功！",
  "sales_contracts.success.deleted": "合同删除成功。",
  "sales_contracts.error.create": "创建销售合同失败",
  "sales_contracts.error.update": "更新销售合同失败",
  "sales_contracts.error.delete": "删除合同失败。",
  "sales_contracts.empty": "未找到销售合同",
  "sales_contracts.empty.description": "创建您的第一个销售合同开始使用。",

  // Purchase Contracts Page
  "purchase_contracts.title": "采购合同",
  "purchase_contracts.subtitle": "管理您公司的采购合同。",
  "purchase_contracts.add": "添加合同",
  "purchase_contracts.add.title": "创建采购合同",
  "purchase_contracts.add.description": "与您的供应商创建新的采购合同。",
  "purchase_contracts.edit.title": "编辑采购合同",
  "purchase_contracts.edit.description": "更新采购合同信息。",
  "purchase_contracts.delete.title": "您确定吗？",
  "purchase_contracts.delete.description": "这将永久删除该合同。此操作无法撤销。",
  "purchase_contracts.form.number": "合同编号",
  "purchase_contracts.form.supplier": "供应商",
  "purchase_contracts.form.template": "模板",
  "purchase_contracts.form.currency": "货币",
  "purchase_contracts.form.items": "明细",
  "purchase_contracts.table.contract_number": "合同编号",
  "purchase_contracts.table.number": "合同编号",
  "purchase_contracts.table.supplier": "供应商",
  "purchase_contracts.table.date": "日期",
  "purchase_contracts.table.currency": "货币",
  "purchase_contracts.table.items": "明细",
  "purchase_contracts.table.total": "总计",
  "purchase_contracts.table.status": "状态",
  "purchase_contracts.table.created": "创建时间",
  "purchase_contracts.table.actions": "操作",
  "purchase_contracts.search_placeholder": "搜索合同...",
  "purchase_contracts.success.created": "采购合同创建成功！",
  "purchase_contracts.success.updated": "采购合同更新成功！",
  "purchase_contracts.success.deleted": "合同删除成功。",
  "purchase_contracts.error.create": "创建采购合同失败",
  "purchase_contracts.error.update": "更新采购合同失败",
  "purchase_contracts.error.delete": "删除合同失败。",
  "purchase_contracts.empty": "未找到采购合同",
  "purchase_contracts.empty.description": "创建您的第一个采购合同开始使用。",

  // Suppliers Page
  "suppliers.title": "供应商管理",
  "suppliers.subtitle": "管理您的供应商数据库和关系",
  "suppliers.add": "添加供应商",
  "suppliers.add.title": "添加新供应商",
  "suppliers.add.description": "为您的业务创建新的供应商记录。",
  "suppliers.edit.title": "编辑供应商",
  "suppliers.edit.description": "更新供应商信息。",
  "suppliers.delete.title": "删除供应商",
  "suppliers.delete.description": "您确定要删除此供应商吗？此操作无法撤销。",
  "suppliers.delete.confirmation": "这将永久删除供应商\"{name}\"并从我们的服务器中删除其数据。",
  "suppliers.form.name": "供应商名称",
  "suppliers.form.contact_name": "联系人",
  "suppliers.form.contact_phone": "电话号码",
  "suppliers.form.contact_email": "邮箱地址",
  "suppliers.form.address": "地址",
  "suppliers.form.bank": "银行详情",
  "suppliers.form.tax_id": "税务编号",
  "suppliers.form.status": "状态",
  "suppliers.table.company_name": "公司名称",
  "suppliers.table.name": "供应商名称",
  "suppliers.table.contact_person": "联系人",
  "suppliers.table.phone": "电话",
  "suppliers.table.email": "邮箱",
  "suppliers.table.address": "地址",
  "suppliers.table.bank": "银行详情",
  "suppliers.table.status": "状态",
  "suppliers.table.actions": "操作",
  "suppliers.success.created": "供应商创建成功！",
  "suppliers.success.updated": "供应商更新成功！",
  "suppliers.success.deleted": "供应商删除成功！",
  "suppliers.error.create": "创建供应商失败",
  "suppliers.error.update": "更新供应商失败",
  "suppliers.error.delete": "删除供应商失败",
  "suppliers.empty": "未找到供应商",
  "suppliers.empty.description": "添加您的第一个供应商开始使用。",
  "suppliers.view.subtitle": "查看供应商详情和相关采购合同",
  "suppliers.view.supplier_info": "供应商信息",
  "suppliers.view.supplier_info_desc": "基本供应商详情和联系信息",
  "suppliers.view.purchase_contracts": "采购合同",
  "suppliers.view.purchase_contracts_desc": "与此供应商相关的采购合同",
  "suppliers.view.no_contracts": "无采购合同",
  "suppliers.view.no_contracts_desc": "此供应商尚未有任何采购合同。",
  "suppliers.view.create_contract": "创建采购合同",
  "suppliers.view.view_all_contracts": "查看所有合同",

  // Samples Page - Main
  "samples.title": "样品管理",
  "samples.subtitle": "管理产品样品和审批流程",
  "samples.add": "新建样品",
  "samples.refresh": "刷新",
  "samples.loading": "正在加载样品...",
  "samples.found": "找到 {count} 个样品",

  // Dashboard Cards
  "samples.cards.outbound.title": "📤 出库样品",
  "samples.cards.outbound.description": "我们发送给客户",
  "samples.cards.inbound.title": "📥 入库样品",
  "samples.cards.inbound.description": "来自客户和供应商",
  "samples.cards.internal.title": "🏭 内部样品",
  "samples.cards.internal.description": "研发和测试",
  "samples.cards.quality.title": "🧪 质量流水线",
  "samples.cards.quality.description": "等待质检审批",

  // Table Headers
  "samples.table.sample": "样品",
  "samples.table.direction": "方向",
  "samples.table.purpose": "用途",
  "samples.table.relationship": "业务关系",
  "samples.table.product": "产品",
  "samples.table.status": "状态",
  "samples.table.priority": "优先级",
  "samples.table.created": "创建时间",
  "samples.table.actions": "操作",
  "samples.table.empty": "未找到样品。创建您的第一个样品开始使用。",

  // Actions
  "samples.actions.view": "查看",
  "samples.actions.edit": "编辑",
  "samples.actions.delete": "删除",
  "samples.actions.approve": "审批",
  "samples.actions.reject": "拒绝",

  // Sample Filters
  "samples.filters.search": "按名称、编码或备注搜索样品...",
  "samples.filters.status.all": "所有状态",
  "samples.filters.status.pending": "待审批",
  "samples.filters.status.approved": "已审批",
  "samples.filters.status.rejected": "已拒绝",
  "samples.filters.type.all": "所有类型",
  "samples.filters.type.development": "开发",
  "samples.filters.type.production": "生产",
  "samples.filters.type.quality": "质量",
  "samples.filters.type.prototype": "原型",
  "samples.filters.direction.all": "所有方向",
  "samples.filters.direction.outbound": "出库",
  "samples.filters.direction.inbound": "入库",
  "samples.filters.direction.internal": "内部",
  "samples.filters.advanced": "高级",
  "samples.filters.clear": "清除筛选",

  // Sample Delete
  "samples.delete.success.title": "样品已删除",
  "samples.delete.success.description": "样品 '{name}' 已成功删除",
  "samples.delete.error.title": "删除失败",
  "samples.delete.error.description": "删除样品失败，请重试。",
  "samples.delete.dialog.title": "删除样品",
  "samples.delete.dialog.description": "您确定要删除此样品吗？此操作无法撤销。",
  "samples.delete.dialog.warning": "此操作是永久性的，无法撤销。",
  "samples.delete.dialog.confirm": "删除样品",
  "samples.delete.dialog.deleting": "删除中...",

  // Sample Fields
  "samples.fields.code": "样品编码",
  "samples.fields.name": "样品名称",
  "samples.fields.date": "日期",
  "samples.fields.status": "状态",
  "samples.fields.priority": "优先级",
  "samples.fields.type": "样品类型",
  "samples.fields.direction": "方向",
  "samples.fields.purpose": "用途",
  "samples.fields.customer": "客户",
  "samples.fields.supplier": "供应商",
  "samples.fields.product": "产品",
  "samples.fields.quantity": "数量",
  "samples.fields.unit": "单位",
  "samples.fields.cost": "成本",
  "samples.fields.currency": "货币",
  "samples.fields.delivery_date": "交付日期",
  "samples.fields.specifications": "技术规格",
  "samples.fields.quality_requirements": "质量要求",
  "samples.fields.notes": "备注",

  // Sample Create
  "samples.create.title": "创建样品",
  "samples.create.description": "创建新的样品记录用于跟踪和审批",
  "samples.create.basic_info": "基本信息",
  "samples.create.workflow": "样品流程",
  "samples.create.relationships": "业务关系",
  "samples.create.specifications": "规格和详情",
  "samples.create.success.title": "样品已创建",
  "samples.create.success.description": "样品已成功创建",
  "samples.create.error.title": "创建失败",
  "samples.create.error.description": "创建样品失败，请重试。",
  "samples.create.cancel": "取消",
  "samples.create.save": "创建样品",
  "samples.create.saving": "创建中...",

  // Sample View
  "samples.view.back": "返回样品",
  "samples.view.pending_request": "待处理请求",
  "samples.view.approved": "已审批",
  "samples.view.rejected": "已拒绝",
  "samples.view.edit": "编辑",
  "samples.view.sample_info": "样品信息",
  "samples.view.sample_type": "样品类型",
  "samples.view.priority": "优先级",
  "samples.view.sample_date": "样品日期",
  "samples.view.quantity": "数量",
  "samples.view.delivery_date": "交付日期",
  "samples.view.cost": "成本",
  "samples.view.relationships": "业务关系",
  "samples.view.customer": "客户",
  "samples.view.contact": "联系人",
  "samples.view.email": "邮箱",
  "samples.view.product": "产品",
  "samples.view.sku": "SKU",
  "samples.view.supplier": "供应商",
  "samples.view.specifications": "规格和备注",
  "samples.view.technical_specs": "技术规格",
  "samples.view.quality_requirements": "质量要求",
  "samples.view.notes": "备注",
  "samples.view.approval_history": "审批历史",
  "samples.view.created": "已创建",
  "samples.view.revised": "已修订",
  "samples.view.pending": "待审批",
  "samples.view.revision_required": "需要修订",
  "samples.view.by_system": "由系统",
  "samples.view.by_current_user": "由当前用户",
  "samples.view.sample_created": "样品已创建并提交审批",
  "samples.view.sample_processed": "样品已处理",
  "samples.view.metadata": "元数据",
  "samples.view.created_date": "创建时间",
  "samples.view.approved_by": "审批人",
  "samples.view.approved_date": "审批日期",

  // Approval History Status Labels
  "samples.view.status.created": "已创建",
  "samples.view.status.pending": "待审批",
  "samples.view.status.approved": "已审批",
  "samples.view.status.rejected": "已拒绝",
  "samples.view.status.revised": "已修订",
  "samples.view.status.revision_required": "需要修订",

  // Approval History Actions
  "samples.view.actions.sample_created": "样品已创建并提交审批",
  "samples.view.actions.sample_processed": "样品已处理",
  "samples.view.actions.sample_approved": "样品已审批",
  "samples.view.actions.sample_rejected": "样品已拒绝",
  "samples.view.actions.revision_requested": "要求修订",

  // User References
  "samples.view.by_system_on": "由系统于",
  "samples.view.by_current_user_on": "由当前用户于",
  "samples.view.by_user_on": "由 {user} 于",

  // Sample Edit
  "samples.edit.title": "编辑样品",
  "samples.edit.description": "更新样品信息和规格",
  "samples.edit.loading": "正在加载样品数据...",
  "samples.edit.success.title": "样品已更新",
  "samples.edit.success.description": "样品已成功更新",
  "samples.edit.error.title": "更新失败",
  "samples.edit.error.description": "更新样品失败，请重试。",
  "samples.edit.error.load": "加载样品数据失败，请重试。",
  "samples.edit.back": "返回样品",
  "samples.edit.cancel": "取消",
  "samples.edit.save": "更新样品",
  "samples.edit.saving": "更新中...",
  "samples.edit.validation.name": "样品名称为必填项",
  "samples.edit.code.readonly": "样品编码无法更改",
  "samples.edit.basic_info": "基本信息",
  "samples.edit.basic_info_desc": "更新基本样品信息",
  "samples.edit.sample_code": "样品编码",
  "samples.edit.sample_name": "样品名称",
  "samples.edit.sample_name_placeholder": "输入样品名称",
  "samples.edit.sample_type": "样品类型",
  "samples.edit.priority": "优先级",
  "samples.edit.relationships": "业务关系",
  "samples.edit.relationships_desc": "将此样品与客户、产品和供应商关联",
  "samples.edit.customer": "客户",
  "samples.edit.customer_placeholder": "搜索客户...",
  "samples.edit.product": "产品",
  "samples.edit.product_placeholder": "搜索产品...",
  "samples.edit.supplier": "供应商",
  "samples.edit.supplier_placeholder": "搜索供应商...",
  "samples.edit.specifications": "规格和详情",
  "samples.edit.specifications_desc": "添加技术规格和其他详情",
  "samples.edit.quantity": "数量",
  "samples.edit.unit": "单位",
  "samples.edit.cost": "成本",
  "samples.edit.currency": "货币",
  "samples.edit.delivery_date": "交付日期",
  "samples.edit.technical_specs": "技术规格",
  "samples.edit.technical_specs_placeholder": "输入技术规格...",
  "samples.edit.quality_requirements": "质量要求",
  "samples.edit.quality_requirements_placeholder": "输入质量要求...",
  "samples.edit.notes": "备注",
  "samples.edit.notes_placeholder": "输入其他备注...",

  // Search Dropdowns
  "samples.edit.search.no_results": "未找到结果",
  "samples.edit.search.add_new_customer": "添加新客户",
  "samples.edit.search.add_new_product": "添加新产品",
  "samples.edit.search.add_new_supplier": "添加新供应商",
  "samples.edit.search.loading": "加载中...",
  "samples.edit.search.type_to_search": "输入以搜索...",

  // Inventory Page
  "inventory.title": "库存管理",
  "inventory.subtitle": "管理库存水平、入库和出库操作",
  "inventory.tabs.inbound": "入库",
  "inventory.tabs.outbound": "出库",
  "inventory.tabs.stock": "库存",
  "inventory.tabs.transactions": "交易记录",
  "inventory.inbound.form.product": "产品",
  "inventory.inbound.form.qty": "数量",
  "inventory.inbound.form.location": "位置",
  "inventory.inbound.form.ref": "参考",
  "inventory.inbound.button": "收货",
  "inventory.outbound.form.product": "产品",
  "inventory.outbound.form.qty": "数量",
  "inventory.outbound.form.location": "位置",
  "inventory.outbound.form.ref": "参考",
  "inventory.outbound.button": "发货",
  "inventory.stock.loading": "正在加载库存...",
  "inventory.stock.error": "加载库存数据失败",
  "inventory.stock.retry": "重试",
  "inventory.stock.table.lot": "批次",
  "inventory.stock.table.location": "位置",

  // Additional inventory keys
  "inventory.inbound.title": "入库",
  "inventory.inbound.desc": "接收库存",
  "inventory.outbound.title": "出库",
  "inventory.outbound.desc": "发货库存",
  "inventory.stock.title": "库存",
  "inventory.stock.desc": "当前库存水平",
  "inventory.transactions.title": "交易记录",
  "inventory.transactions.desc": "库存移动历史",
  "field.location": "位置",
  "field.note": "备注",
  "field.reference": "参考",
  "action.addInbound": "收货",
  "action.addOutbound": "发货",

  // ✅ NEW: Comprehensive inventory transaction translations (Chinese)
  "inventory.transaction_forms": "交易表单",
  "inventory.transaction_history": "交易历史",
  "inventory.transaction_success": "交易成功",
  "inventory.transaction_error": "交易失败",
  "inventory.inbound": "入库",
  "inventory.outbound": "出库",
  "inventory.transfer": "调拨",
  "inventory.adjustment": "调整",
  "inventory.product": "产品",
  "inventory.quantity": "数量",
  "inventory.location": "位置",
  "inventory.source_location": "源位置",
  "inventory.destination_location": "目标位置",
  "inventory.adjustment_quantity": "调整数量",
  "inventory.reason_code": "原因代码",
  "inventory.notes": "备注",
  "inventory.reference": "参考",
  "inventory.status": "状态",
  "inventory.date": "日期",
  "inventory.type": "类型",
  "inventory.select_product": "选择产品",
  "inventory.select_location": "选择位置",
  "inventory.reference_placeholder": "采购单/销售单号、收货单号等",
  "inventory.notes_placeholder": "附加备注或说明",
  "inventory.transfer_notes_placeholder": "调拨原因",
  "inventory.adjustment_notes_placeholder": "说明调整原因",
  "inventory.positive_negative_allowed": "允许正负值",
  "inventory.process_inbound": "处理入库",
  "inventory.process_outbound": "处理出库",
  "inventory.process_transfer": "处理调拨",
  "inventory.process_adjustment": "处理调整",
  "inventory.adjustment_warning": "警告",
  "inventory.adjustment_warning_text": "调整会直接修改库存数量。请确保有适当的授权和文档记录。",
  "inventory.search_transactions": "搜索交易记录...",
  "inventory.filter_by_type": "按类型筛选",
  "inventory.filter_by_location": "按位置筛选",
  "inventory.all_types": "所有类型",
  "inventory.all_locations": "所有位置",
  "inventory.no_transactions": "未找到交易记录",
  "inventory.showing_transactions": "显示 {count} 条，共 {total} 条交易记录",
  "inventory.fetch_error": "数据加载失败",
  "inventory.adjustment_notes": "调整备注",
  "inventory.reason_receipt": "收货",
  "inventory.reason_shipment": "发货",
  "inventory.reason_transfer": "调拨",
  "inventory.reason_cycle_count": "盘点",
  "inventory.reason_damage": "损坏",
  "inventory.reason_obsolete": "报废",
  "inventory.reason_adjustment": "调整",
  "inventory.reason_return": "退货",
  "inventory.reason_sample": "样品",
  "inventory.status_pending": "待处理",
  "inventory.status_approved": "已批准",
  "inventory.status_rejected": "已拒绝",

  // ✅ NEW: Enhanced Inventory KPI Cards (Chinese)
  "inventory.finishedGoods": "成品库存",
  "inventory.rawMaterials": "原材料库存",
  "inventory.totalValue": "总价值",

  // Company Profile
  "company.profile.title": "公司资料",
  "company.profile.subtitle": "管理您的公司信息和设置",
  "company.profile.not_found": "未找到公司资料",
  "company.profile.not_found_desc": "看起来您还没有完成公司资料设置。",
  "company.profile.complete_setup": "完成公司设置",
  "company.profile.complete": "完整",
  "company.profile.incomplete": "不完整",
  "company.profile.edit": "编辑资料",
  "company.profile.save": "保存更改",
  "company.profile.cancel": "取消",
  "company.profile.tabs.basic": "基本信息",
  "company.profile.tabs.business": "业务详情",
  "company.profile.tabs.banking": "银行信息",
  "company.profile.tabs.export": "出口贸易",
  "company.profile.basic.description": "您公司的基本联系方式和地址信息",
  "company.profile.business.description": "业务注册和运营信息",
  "company.profile.banking.description": "银行和金融账户详情",
  "company.profile.export.description": "出口许可和贸易合规信息",
  "company.profile.success.updated": "公司资料更新成功！",
  "company.profile.error.update": "更新资料失败",

  // Company Profile Form Fields
  "company.field.name": "公司名称",
  "company.field.legal_name": "法定公司名称",
  "company.field.email": "邮箱地址",
  "company.field.phone": "电话号码",
  "company.field.website": "网站",
  "company.field.country": "国家",
  "company.field.address_line1": "街道地址",
  "company.field.address_line2": "地址第二行",
  "company.field.city": "城市",
  "company.field.state_province": "省/州",
  "company.field.postal_code": "邮政编码",
  "company.field.industry": "行业",
  "company.field.business_type": "业务类型",
  "company.field.employee_count": "员工数量",
  "company.field.annual_revenue": "年收入",
  "company.field.registration_number": "注册号",
  "company.field.tax_id": "税务编号",
  "company.field.vat_number": "增值税号",
  "company.field.bank_name": "银行名称",
  "company.field.bank_account": "账户号码",
  "company.field.bank_swift": "SWIFT/BIC代码",
  "company.field.bank_address": "银行地址",
  "company.field.export_license": "出口许可证",
  "company.field.customs_code": "海关代码",
  "company.field.preferred_incoterms": "首选贸易条款",
  "company.field.preferred_payment_terms": "首选付款条件",

  // Quality Control
  "quality.title": "质量控制",
  "quality.subtitle": "管理质量检验、缺陷跟踪和合规报告",
  "quality.metrics.title": "质量指标",
  "quality.metrics.pass_rate": "合格率",
  "quality.metrics.total_inspections": "总检验数",
  "quality.metrics.pending": "待检验数",
  "quality.metrics.defect_rate": "缺陷率",
  "quality.inspections.title": "最近检验",
  "quality.inspections.subtitle": "最新质量检验结果",
  "quality.defects.title": "缺陷跟踪",
  "quality.defects.subtitle": "跟踪和管理质量缺陷",

  // Contract Templates
  "contract_templates.title": "合同模板",
  "contract_templates.subtitle": "管理销售和采购协议的可重用合同模板",
  "contract_templates.add": "添加模板",
  "contract_templates.table.name": "模板名称",
  "contract_templates.table.type": "类型",
  "contract_templates.table.language": "语言",
  "contract_templates.table.version": "版本",
  "contract_templates.table.status": "状态",
  "contract_templates.table.actions": "操作",

  // Contract Forms (Add/Edit)
  "contracts.form.number": "合同编号",
  "contracts.form.customer": "客户",
  "contracts.form.supplier": "供应商",
  "contracts.form.currency": "货币",
  "contracts.form.template": "模板",
  "contracts.form.items": "合同项目",
  "contracts.form.product": "产品",
  "contracts.form.quantity": "数量",
  "contracts.form.price": "价格",
  "contracts.form.total": "总计",
  "contracts.form.add_item": "添加项目",
  "contracts.form.remove_item": "移除",
  "contracts.form.contract_info": "合同信息",
  "contracts.form.contract_info_desc": "基本合同详情和客户信息",
  "contracts.form.items_section": "合同项目",
  "contracts.form.items_section_desc": "此合同的产品和数量",
  "contracts.form.template_section": "合同模板",
  "contracts.form.template_section_desc": "选择合同文档生成模板",

  // Contract View
  "contracts.view.back": "返回合同",
  "contracts.view.back_sales": "返回销售合同",
  "contracts.view.back_purchase": "返回采购合同",
  "contracts.view.edit_contract": "编辑合同",
  "contracts.view.export_pdf": "导出PDF",
  "contracts.view.loading": "正在加载合同文档...",
  "contracts.view.no_document": "无可用合同文档",
  "contracts.view.contract_summary": "合同摘要",
  "contracts.view.contract_document": "合同文档",
  "contracts.view.customer": "客户",
  "contracts.view.supplier": "供应商",
  "contracts.view.contract_date": "合同日期",
  "contracts.view.total_value": "合同总额",
  "contracts.view.template": "模板",
  "contracts.view.no_email": "无邮箱",
  "contracts.view.items_count": "{count} 项",
  "contracts.view.sales_template": "销售模板",
  "contracts.view.purchase_template": "采购模板",

  // Contract Edit
  "contracts.edit.title_sales": "编辑销售合同",
  "contracts.edit.title_purchase": "编辑采购合同",
  "contracts.edit.subtitle": "更新合同 {number} 的详细信息",
  "contracts.edit.back": "返回合同",
  "contracts.edit.template_optional": "合同模板（可选）",
  "contracts.edit.template_desc": "销售合同模板",
  "contracts.edit.preview": "预览",
  "contracts.edit.items_title": "合同项目",
  "contracts.edit.add_item": "添加项目",
  "contracts.edit.product": "产品",
  "contracts.edit.quantity": "数量",
  "contracts.edit.unit_price": "单价",
  "contracts.edit.sku_label": "SKU: {sku}",
  "contracts.edit.unit_label": "单位: {unit}",

  // Contract Creation
  "contracts.create.title_sales": "创建销售合同",
  "contracts.create.title_purchase": "创建采购合同",
  "contracts.create.subtitle_sales": "输入新销售合同的详细信息",
  "contracts.create.subtitle_purchase": "输入新采购合同的详细信息",
  "contracts.create.back_sales": "销售合同",
  "contracts.create.back_purchase": "采购合同",
  "contracts.create.add_new": "添加新合同",
  "contracts.create.contract_info": "合同信息",
  "contracts.create.contract_info_desc": "基本合同详情和客户信息",
  "contracts.create.contract_info_desc_purchase": "基本合同详情和供应商信息",
  "contracts.create.number": "合同编号",
  "contracts.create.number_placeholder": "例如：PC-2025-001",
  "contracts.create.supplier": "供应商",
  "contracts.create.supplier_placeholder": "选择供应商...",
  "contracts.create.customer": "客户",
  "contracts.create.customer_placeholder": "选择客户...",
  "contracts.create.currency": "货币",
  "contracts.create.currency_placeholder": "例如：USD",
  "contracts.create.template": "合同模板（可选）",
  "contracts.create.template_placeholder": "选择模板...",
  "contracts.create.items": "合同项目",
  "contracts.create.items_desc": "为此合同添加产品和数量",
  "contracts.create.add_item": "添加项目",
  "contracts.create.remove_item": "删除项目",
  "contracts.create.product": "产品",
  "contracts.create.product_placeholder": "选择产品...",
  "contracts.create.quantity": "数量",
  "contracts.create.quantity_placeholder": "0",
  "contracts.create.unit_price": "单价",
  "contracts.create.unit_price_placeholder": "0.00",
  "contracts.create.total": "总计",
  "contracts.create.cancel": "取消",
  "contracts.create.create": "创建合同",
  "contracts.create.creating": "创建中...",
  "contracts.create.success": "合同创建成功！",
  "contracts.create.error": "创建合同失败",
  "contracts.create.summary": "合同摘要",
  "contracts.create.summary_desc": "查看合同总价值和详细信息",
  "contracts.create.items_count": "项目数量:",
  "contracts.create.currency_label": "货币:",
  "contracts.create.total_value": "合同总价值:",

  // Contract Templates
  "contracts.templates.page_title": "合同模板",
  "contracts.templates.page_desc": "管理销售和采购协议的合同模板",
  "contracts.templates.sales_section": "销售合同模板",
  "contracts.templates.sales_desc": "创建和管理销售合同模板",
  "contracts.templates.purchase_section": "采购合同模板",
  "contracts.templates.purchase_desc": "创建和管理采购合同模板",
  "contracts.templates.template_name": "模板名称",
  "contracts.templates.currency": "货币",
  "contracts.templates.payment_terms": "付款条款",
  "contracts.templates.delivery_terms": "交付条款",
  "contracts.templates.template_content": "模板内容",
  "contracts.templates.content_placeholder": "输入合同模板内容，使用占位符如 {{customer_name}}、{{product_name}} 等",
  "contracts.templates.content_placeholder_purchase": "输入合同模板内容，使用占位符如 {{supplier_name}}、{{material_name}} 等",
  "contracts.templates.create_template": "创建模板",
  "contracts.templates.existing_templates": "现有模板",
  "contracts.templates.name": "名称",
  "contracts.templates.actions": "操作",
  "contracts.templates.payment_30_days": "30天",
  "contracts.templates.payment_60_days": "60天",
  "contracts.templates.delivery_fob": "FOB",
  "contracts.templates.delivery_cif": "CIF",
  "contracts.templates.delivery_exw": "EXW",

  // Contract Templates Cards
  "contract_templates.sales.title": "销售合同模板",
  "contract_templates.sales.description": "创建和管理销售合同模板",
  "contract_templates.sales.sample": "示例模板",
  "contract_templates.sales.sample_title": "专业销售合同模板",
  "contract_templates.sales.sample_desc": "复制此专业模板并粘贴到下面的模板内容字段中。",
  "contract_templates.purchase.title": "采购合同模板",
  "contract_templates.purchase.description": "创建和管理采购合同模板",
  "contract_templates.purchase.sample": "示例模板",
  "contract_templates.purchase.sample_title": "专业采购合同模板",
  "contract_templates.purchase.sample_desc": "复制此专业模板并粘贴到下面的模板内容字段中。",

  // Quality Control
  "quality.title": "质量控制",
  "quality.subtitle": "管理质量检验和证书",
  "quality.attachments.documents.title": "文档附件",
  "quality.attachments.documents.upload": "上传文档",
  "quality.attachments.documents.formats": "支持格式：PDF、DOC、DOCX、XLS、XLSX、TXT",
  "quality.attachments.documents.none": "无文档附件",
  "quality.attachments.photos.title": "照片附件",
  "quality.attachments.photos.upload": "上传照片",
  "quality.attachments.photos.formats": "支持格式：JPG、PNG、GIF、WebP",
  "quality.attachments.photos.none": "无照片附件",
  "quality.attachments.preview": "预览",
  "quality.attachments.download": "下载",
  "quality.attachments.remove": "删除",
  "quality.attachments.uploading": "正在上传文件...",
  "quality.attachments.upload_success": "上传成功",
  "quality.attachments.upload_success_desc": "个文件上传成功",
  "quality.attachments.download_success": "下载完成",
  "quality.attachments.download_success_desc": "下载成功",
  "quality.attachments.download_failed": "下载失败",
  "quality.attachments.download_failed_desc": "下载文件失败，请重试。",
  "quality.attachments.remove_success": "文件已删除",
  "quality.attachments.remove_success_desc": "文件删除成功",
  "quality.attachments.remove_failed": "删除失败",
  "quality.attachments.remove_failed_desc": "删除文件失败，请重试。",

  // Quality Status
  "quality.status": "质量状态",
  "quality.status.pending": "待检验",
  "quality.status.approved": "已通过",
  "quality.status.quarantined": "已隔离",
  "quality.status.rejected": "已拒绝",

  // Work Order
  "workOrder.title": "生产工单",
  "workOrder.number": "工单编号",
  "workOrder.status.completed": "已完成",
  "workOrder.status.pending": "待开始",
  "workOrder.status.in-progress": "进行中",

  // Product Quality Requirements
  "products.form.quality_requirements": "质量要求",
  "products.form.inspection_required": "需要质量检验",
  "products.form.inspection_required_desc": "如果产品需要质量检验才能通过，请启用此选项",
  "products.form.quality_tolerance": "质量公差",
  "products.form.quality_notes": "质量备注",
  "products.form.quality_notes_placeholder": "输入具体的质量要求、标准或检验标准...",
  "products.quality.not_required": "无需检验",
  "products.success.updated": "产品已更新",
  "products.success.updated_desc": "产品质量要求已成功更新。",
  "products.success.created_desc": "已成功创建具有质量要求的产品。",
  "products.error.update": "更新失败",

  // Work Orders Quality Gate Modal
  "work_orders.quality_gate.title": "需要质量审批",
  "work_orders.quality_gate.description": "此工单需要完成所有必需的质量检验审批后才能完成。",
  "work_orders.quality_gate.work_order_info": "工单信息",
  "work_orders.quality_gate.inspections_status": "质量检验状态",
  "work_orders.quality_gate.no_inspections": "未找到质量检验记录。可能需要创建检验。",
  "work_orders.quality_gate.completion_status": "完成状态",
  "work_orders.quality_gate.can_complete": "所有质量要求已满足。工单可以完成。",
  "work_orders.quality_gate.cannot_complete": "完成前需要质量审批。",
  "work_orders.quality_gate.pending_inspections": "待处理检验",
  "work_orders.quality_gate.complete_inspections_first": "请先完成所有待处理的质量检验。",
  "work_orders.quality_gate.go_to_quality_control": "前往质量控制",
  "work_orders.quality_gate.complete_work_order": "完成工单",

  // Additional translations for quality gate modal
  "quality.inspector": "检验员",
  "quality.inspection_types.final": "最终检验",
  "quality.inspection_types.incoming": "来料检验",
  "quality.inspection_types.in_process": "过程检验",
  "quality.status.pending": "待处理",
  "quality.status.passed": "通过",
  "quality.status.failed": "失败",
  "common.close": "关闭",
  "common.processing": "处理中...",
}

type I18nContextValue = {
  locale: Locale
  setLocale: (l: Locale) => void
  t: (key: string) => string
}

const I18nContext = createContext<I18nContextValue | null>(null)

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>("en")

  useEffect(() => {
    const saved = typeof window !== "undefined" ? (localStorage.getItem("locale") as Locale | null) : null
    if (saved === "en" || saved === "zh") setLocaleState(saved)
    else {
      const nav = typeof window !== "undefined" ? navigator.language.toLowerCase() : "en"
      if (nav.startsWith("zh")) setLocaleState("zh")
    }
  }, [])

  function setLocale(l: Locale) {
    setLocaleState(l)
    try {
      localStorage.setItem("locale", l)
    } catch { }
  }

  const dict = locale === "zh" ? zh : en

  const value = useMemo<I18nContextValue>(
    () => ({
      locale,
      setLocale,
      t: (key: string) => dict[key] ?? key,
    }),
    [locale, dict],
  )

  return <I18nContext.Provider value={value}>{children}</I18nContext.Provider>
}

export function useI18n() {
  const ctx = useContext(I18nContext)
  if (!ctx) throw new Error("I18nProvider missing")
  return ctx
}
