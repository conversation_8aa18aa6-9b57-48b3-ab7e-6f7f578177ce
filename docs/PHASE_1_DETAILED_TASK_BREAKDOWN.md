# 📋 PHASE 1: DETAILED TASK BREAKDOWN
## Manufacturing Intelligence Implementation - Actionable Development Tasks

---

## 🎯 TASK ORGANIZATION METHODOLOGY

**Task Naming Convention:** `[PHASE].[WEEK].[TASK].[SUBTASK]`  
**Time Estimates:** Based on professional ERP development experience  
**Dependencies:** Clearly marked with prerequisite tasks  
**Complexity Levels:** Low (1-2 days), Medium (3-4 days), High (5+ days)  

---

## 🏗️ PHASE 1A: ADVANCED MRP SYSTEM

### **Week 1: Demand Forecasting Foundation**

#### **Task 1.1: Database Schema Implementation**
**Complexity:** Medium | **Duration:** 3 days | **Dependencies:** None

**1.1.1 Create Demand Forecasting Tables** (1 day)
```sql
-- File: lib/schema-postgres.ts additions
export const demandForecasts = pgTable("demand_forecasts", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  product_id: text("product_id").notNull().references(() => products.id),
  forecast_period: text("forecast_period").notNull(), // "2025-Q1", "2025-02"
  forecasted_demand: text("forecasted_demand").notNull(),
  confidence_level: text("confidence_level").default("medium"), // low, medium, high
  forecast_method: text("forecast_method").default("pipeline"), // pipeline, historical, manual
  created_by: text("created_by").notNull(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("demand_forecasts_company_id_idx").on(table.company_id),
  productIdIdx: index("demand_forecasts_product_id_idx").on(table.product_id),
  forecastPeriodIdx: index("demand_forecasts_period_idx").on(table.forecast_period),
}))
```

**1.1.2 Create Forecast Parameters Table** (1 day)
```sql
export const forecastParameters = pgTable("forecast_parameters", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  product_id: text("product_id").notNull().references(() => products.id),
  seasonality_factor: text("seasonality_factor").default("1.0"),
  trend_factor: text("trend_factor").default("1.0"),
  lead_time_buffer_days: text("lead_time_buffer_days").default("14"),
  safety_stock_percentage: text("safety_stock_percentage").default("0.15"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
})
```

**1.1.3 Add Relations and Indexes** (1 day)
- Define comprehensive relations between new and existing tables
- Add performance indexes for query optimization
- Update database migration scripts

#### **Task 1.2: Demand Forecasting Service Development**
**Complexity:** High | **Duration:** 4 days | **Dependencies:** 1.1

**1.2.1 Core Forecasting Service** (2 days)
```typescript
// File: lib/services/demand-forecasting.ts
export class DemandForecastingService extends BaseService {
  /**
   * Generate demand forecast based on sales contract pipeline
   */
  async generatePipelineForecast(
    productId: string, 
    periodMonths: number
  ): Promise<DemandForecast> {
    // Analyze pending and approved sales contracts
    // Calculate trend based on historical contract data
    // Apply seasonality factors if configured
    // Return forecast with confidence level
  }

  /**
   * Calculate material requirements from demand forecast
   */
  async explodeForecastToBOM(
    forecastId: string
  ): Promise<MaterialRequirement[]> {
    // Get forecast details
    // Explode BOM for forecasted quantity
    // Apply waste factors and safety stock
    // Return material requirements with timing
  }
}
```

**1.2.2 Pipeline Analysis Logic** (1 day)
- Analyze sales contract pipeline for demand signals
- Weight contracts by probability and timeline
- Calculate rolling averages and trends

**1.2.3 BOM Integration** (1 day)
- Integrate with existing BOM explosion logic
- Apply waste factors to forecasted requirements
- Calculate timing based on lead times

#### **Task 1.3: Demand Forecasting API Endpoints**
**Complexity:** Medium | **Duration:** 2 days | **Dependencies:** 1.2

**1.3.1 CRUD API Endpoints** (1 day)
```typescript
// File: app/api/planning/demand-forecast/route.ts
export const GET = withTenantAuth(async function GET(request, context) {
  // List demand forecasts with filtering and pagination
})

export const POST = withTenantAuth(async function POST(request, context) {
  // Create new demand forecast
  // Validate input data with Zod schema
  // Generate forecast using service
})

// File: app/api/planning/demand-forecast/[id]/route.ts
export const GET = withTenantAuth(async function GET(request, context, { params }) {
  // Get specific forecast with material requirements
})

export const PATCH = withTenantAuth(async function PATCH(request, context, { params }) {
  // Update forecast parameters and regenerate
})
```

**1.3.2 Material Requirements API** (1 day)
```typescript
// File: app/api/planning/demand-forecast/[id]/material-requirements/route.ts
export const GET = withTenantAuth(async function GET(request, context, { params }) {
  // Get material requirements for specific forecast
  // Include availability analysis
  // Return procurement recommendations
})
```

### **Week 2: Procurement Planning System**

#### **Task 2.1: Procurement Database Schema**
**Complexity:** Medium | **Duration:** 2 days | **Dependencies:** 1.1

**2.1.1 Procurement Planning Tables** (1 day)
```sql
export const procurementPlans = pgTable("procurement_plans", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  raw_material_id: text("raw_material_id").notNull().references(() => rawMaterials.id),
  planned_qty: text("planned_qty").notNull(),
  target_date: text("target_date").notNull(),
  supplier_id: text("supplier_id").references(() => suppliers.id),
  status: text("status").default("planned"), // planned, ordered, received
  priority: text("priority").default("normal"), // low, normal, high, urgent
  container_optimization: text("container_optimization"), // JSON field
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
})
```

**2.1.2 Supplier Lead Time Management** (1 day)
```sql
export const supplierLeadTimes = pgTable("supplier_lead_times", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  supplier_id: text("supplier_id").notNull().references(() => suppliers.id),
  raw_material_id: text("raw_material_id").references(() => rawMaterials.id),
  lead_time_days: text("lead_time_days").notNull(),
  minimum_order_qty: text("minimum_order_qty").default("0"),
  container_size: text("container_size"), // For container optimization
  reliability_score: text("reliability_score").default("0.8"), // 0.0 to 1.0
  last_updated: timestamp("last_updated", { withTimezone: true }).defaultNow(),
})
```

#### **Task 2.2: Procurement Planning Service**
**Complexity:** High | **Duration:** 4 days | **Dependencies:** 2.1, 1.2

**2.2.1 Core Procurement Service** (2 days)
```typescript
// File: lib/services/procurement-planning.ts
export class ProcurementPlanningService extends BaseService {
  /**
   * Generate procurement plan from material requirements
   */
  async generateProcurementPlan(
    materialRequirements: MaterialRequirement[]
  ): Promise<ProcurementPlan> {
    // Calculate optimal order quantities
    // Consider supplier lead times and minimums
    // Optimize for container shipping
    // Generate procurement recommendations
  }

  /**
   * Optimize orders for container shipping
   */
  async optimizeContainerLoads(
    orders: ProcurementOrder[]
  ): Promise<OptimizedContainerPlan> {
    // Group materials by supplier and shipping route
    // Calculate container utilization
    // Suggest order adjustments for efficiency
    // Return optimized shipping plan
  }
}
```

**2.2.2 Container Optimization Logic** (1 day)
- Calculate optimal container utilization
- Consider material density and volume
- Suggest order quantity adjustments

**2.2.3 Supplier Performance Integration** (1 day)
- Factor supplier reliability into planning
- Consider historical delivery performance
- Adjust lead times based on performance data

#### **Task 2.3: Procurement Planning UI Components**
**Complexity:** High | **Duration:** 4 days | **Dependencies:** 2.2

**2.3.1 Procurement Dashboard Component** (2 days)
```typescript
// File: components/planning/procurement-dashboard.tsx
export function ProcurementDashboard() {
  // Display procurement plan overview
  // Show material requirements vs availability
  // Highlight urgent procurement needs
  // Container optimization suggestions
}
```

**2.3.2 Material Requirements Table** (1 day)
```typescript
// File: components/planning/material-requirements-table.tsx
export function MaterialRequirementsTable({ requirements }: Props) {
  // Professional table showing material needs
  // Availability status indicators
  // Procurement recommendations
  // Action buttons for creating purchase orders
}
```

**2.3.3 Container Optimization View** (1 day)
```typescript
// File: components/planning/container-optimization.tsx
export function ContainerOptimization({ orders }: Props) {
  // Visual container packing optimization
  // Cost savings calculations
  // Alternative shipping suggestions
  // Export to purchase order functionality
}
```

### **Week 3-4: MRP Integration & Dashboard**

#### **Task 3.1: MRP Dashboard Page**
**Complexity:** Medium | **Duration:** 3 days | **Dependencies:** 1.3, 2.3

**3.1.1 Main Planning Page** (2 days)
```typescript
// File: app/planning/page.tsx
export default async function PlanningPage() {
  const context = await getTenantContext()
  if (!context) redirect('/api/auth/login')

  // Fetch demand forecasts, material requirements, procurement plans
  // Display comprehensive MRP dashboard
  // Include KPIs and actionable insights
}
```

**3.1.2 Navigation Integration** (1 day)
- Add planning section to main navigation
- Update breadcrumbs and routing
- Ensure proper access control

#### **Task 3.2: Workflow Integration**
**Complexity:** High | **Duration:** 4 days | **Dependencies:** 3.1

**3.2.1 Work Order Integration** (2 days)
- Enhance work order generation with material availability checks
- Display material shortages in work order details
- Prevent work order start if critical materials unavailable

**3.2.2 Inventory Integration** (1 day)
- Add procurement recommendations to inventory alerts
- Display forecasted demand in stock level calculations
- Integrate reorder point calculations

**3.2.3 Contract Integration** (1 day)
- Show material availability during contract approval
- Display lead time warnings for tight delivery schedules
- Integrate with existing contract workflow

---

## 🏗️ PHASE 1B: ENHANCED FINANCIAL INTEGRATION

### **Week 1: Multi-Currency Foundation**

#### **Task 4.1: Multi-Currency Database Schema**
**Complexity:** Medium | **Duration:** 2 days | **Dependencies:** None

**4.1.1 Currency Management Tables** (1 day)
```sql
export const currencies = pgTable("currencies", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  code: text("code").notNull(), // USD, EUR, CNY, etc.
  name: text("name").notNull(),
  symbol: text("symbol").notNull(),
  exchange_rate: text("exchange_rate").notNull(), // Rate to base currency
  is_base_currency: text("is_base_currency").default("false"),
  last_updated: timestamp("last_updated", { withTimezone: true }).defaultNow(),
})

export const exchangeRateHistory = pgTable("exchange_rate_history", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  from_currency: text("from_currency").notNull(),
  to_currency: text("to_currency").notNull(),
  rate: text("rate").notNull(),
  effective_date: text("effective_date").notNull(),
  source: text("source").default("manual"), // manual, api, bank
})
```

**4.1.2 Enhance Existing Financial Tables** (1 day)
```sql
-- Add currency fields to existing tables
ALTER TABLE sales_contracts ADD COLUMN currency_code TEXT DEFAULT 'USD';
ALTER TABLE purchase_contracts ADD COLUMN currency_code TEXT DEFAULT 'USD';
ALTER TABLE ar_invoices ADD COLUMN currency_code TEXT DEFAULT 'USD';
ALTER TABLE ap_invoices ADD COLUMN currency_code TEXT DEFAULT 'USD';
ALTER TABLE products ADD COLUMN base_price_currency TEXT DEFAULT 'USD';
```

#### **Task 4.2: Currency Management Service**
**Complexity:** High | **Duration:** 4 days | **Dependencies:** 4.1

**4.2.1 Core Currency Service** (2 days)
```typescript
// File: lib/services/currency-management.ts
export class CurrencyManagementService extends BaseService {
  /**
   * Convert amount between currencies
   */
  async convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string,
    effectiveDate?: string
  ): Promise<CurrencyConversion> {
    // Get exchange rate for date
    // Perform conversion calculation
    // Log conversion for audit trail
    // Return conversion details
  }

  /**
   * Update exchange rates from external API
   */
  async updateExchangeRates(): Promise<ExchangeRateUpdate> {
    // Fetch rates from reliable API (e.g., exchangerate-api.com)
    // Update currency table
    // Log rate changes in history
    // Notify of significant changes
  }
}
```

**4.2.2 Exchange Rate API Integration** (1 day)
- Integrate with external exchange rate API
- Implement rate caching and fallback mechanisms
- Add rate change notifications

**4.2.3 Currency Conversion Utilities** (1 day)
- Create utility functions for common conversions
- Add formatting helpers for different currencies
- Implement rounding rules per currency

### **Week 2: Export Financial Features**

#### **Task 5.1: Export Financial Reporting**
**Complexity:** High | **Duration:** 4 days | **Dependencies:** 4.2

**5.1.1 Export Revenue Analytics** (2 days)
```typescript
// File: lib/services/export-financial-analytics.ts
export class ExportFinancialAnalyticsService extends BaseService {
  /**
   * Generate export revenue report by destination
   */
  async getExportRevenueByDestination(
    dateRange: DateRange
  ): Promise<ExportRevenueReport> {
    // Analyze sales contracts by customer country
    // Convert all amounts to base currency
    // Calculate margins and profitability
    // Return comprehensive report
  }

  /**
   * Calculate container shipping cost allocation
   */
  async allocateShippingCosts(
    contractIds: string[]
  ): Promise<ShippingCostAllocation> {
    // Get shipping costs from shipping module
    // Allocate costs based on volume/weight
    // Update contract profitability
    // Return allocation details
  }
}
```

**5.1.2 Multi-Currency Financial Reports** (1 day)
- Enhance existing financial reports with currency support
- Add currency conversion options
- Display exchange rate impact on profitability

**5.1.3 Export Documentation Cost Tracking** (1 day)
- Track costs associated with export documentation
- Integrate with export declaration module
- Allocate costs to specific contracts

#### **Task 5.2: Cash Flow Forecasting**
**Complexity:** High | **Duration:** 4 days | **Dependencies:** 4.2

**5.2.1 Cash Flow Forecasting Service** (2 days)
```typescript
// File: lib/services/cash-flow-forecasting.ts
export class CashFlowForecastingService extends BaseService {
  /**
   * Generate cash flow forecast for export business
   */
  async generateCashFlowForecast(
    periodMonths: number
  ): Promise<CashFlowForecast> {
    // Analyze pending AR invoices with payment terms
    // Consider container shipping payment schedules
    // Factor in AP obligations and material purchases
    // Apply currency conversion and hedging
    // Return detailed forecast
  }

  /**
   * Calculate export payment schedule
   */
  async calculateExportPaymentSchedule(
    contractId: string
  ): Promise<PaymentSchedule> {
    // Get contract payment terms
    // Consider shipping and documentation timeline
    // Calculate expected payment dates
    // Factor in currency conversion timing
  }
}
```

**5.2.2 Payment Terms Integration** (1 day)
- Enhance payment terms handling for export scenarios
- Support complex payment structures (deposits, milestones, final payment)
- Integrate with shipping timeline

**5.2.3 Currency Risk Assessment** (1 day)
- Calculate currency exposure by contract
- Identify hedging opportunities
- Generate risk reports

### **Week 3: Financial Dashboard Enhancement**

#### **Task 6.1: Enhanced Financial Dashboard**
**Complexity:** Medium | **Duration:** 3 days | **Dependencies:** 5.1, 5.2

**6.1.1 Multi-Currency KPI Dashboard** (2 days)
```typescript
// File: components/finance/export-financial-dashboard.tsx
export function ExportFinancialDashboard() {
  // Display KPIs in multiple currencies
  // Show currency exposure and risk metrics
  // Export market profitability analysis
  // Cash flow forecasting visualization
}
```

**6.1.2 Currency Exposure Monitoring** (1 day)
- Real-time currency exposure display
- Risk threshold alerts
- Hedging recommendations

#### **Task 6.2: Integration Testing**
**Complexity:** Medium | **Duration:** 4 days | **Dependencies:** All previous tasks

**6.2.1 End-to-End Testing** (2 days)
- Test complete MRP workflow
- Verify multi-currency calculations
- Test integration points

**6.2.2 Performance Testing** (1 day)
- Load testing with large datasets
- Query optimization verification
- Response time benchmarking

**6.2.3 User Acceptance Testing** (1 day)
- Test with realistic business scenarios
- Verify UI/UX meets requirements
- Validate business logic accuracy

---

## 📊 TASK SUMMARY

**Total Tasks:** 24 major tasks, 48 subtasks  
**Estimated Duration:** 6-8 weeks  
**Critical Path:** Demand Forecasting → Procurement Planning → MRP Dashboard  
**Parallel Development:** Financial integration can run parallel to MRP development  

**Resource Requirements:**
- **Senior Developer:** Full-time for complex services and integration
- **Database Specialist:** Part-time for schema design and optimization  
- **UI/UX Developer:** Part-time for component development
- **QA Engineer:** Part-time for testing and validation
