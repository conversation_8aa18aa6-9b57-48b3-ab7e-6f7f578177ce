# Translation Reviewer Quick Reference

**Manufacturing ERP i18n Review Guide**

---

## 👥 **CSV COLLABORATION WORKFLOW**

### **Step-by-Step Process**

1. **Receive CSV File**: Developer exports translations to CSV format
2. **Open in Familiar Tools**: Excel, Google Sheets, or any CSV editor
3. **Review & Edit**: Focus on professional Manufacturing ERP terminology
4. **Save & Return**: Send back completed CSV file for integration

### **CSV File Format**

| Column | Description | Example |
|--------|-------------|---------|
| **Key** | Translation identifier | `customers.title` |
| **English** | Original English text | `Customers` |
| **Chinese** | Chinese translation | `客户` |
| **Context** | Usage context | `Page header` |
| **Priority** | Importance level | `High/Medium/Low` |
| **Notes** | Additional information | `Business term` |

---

## 🏭 **MANUFACTURING ERP TERMINOLOGY**

### **Core Business Terms**

| English | Chinese | Context |
|---------|---------|---------|
| **Customer** | 客户 | Business relationship |
| **Supplier** | 供应商 | Vendor/provider |
| **Contract** | 合同 | Legal agreement |
| **Work Order** | 工单 | Production instruction |
| **Quality Control** | 质量控制 | QC process |
| **Inventory** | 库存 | Stock management |
| **Shipment** | 发货 | Delivery process |
| **Export Declaration** | 出口报关单 | Customs document |

### **Textile Industry Terms**

| English | Chinese | Context |
|---------|---------|---------|
| **Fabric** | 面料 | Material type |
| **Yarn** | 纱线 | Raw material |
| **Weaving** | 织造 | Production process |
| **Dyeing** | 染色 | Color process |
| **Finishing** | 整理 | Final treatment |
| **Quality Grade** | 质量等级 | Product classification |
| **Sample** | 样品 | Product sample |
| **Batch** | 批次 | Production lot |

### **Export Trade Terms**

| English | Chinese | Context |
|---------|---------|---------|
| **FOB** | 离岸价 | Shipping term |
| **CIF** | 到岸价 | Shipping term |
| **Letter of Credit** | 信用证 | Payment method |
| **Bill of Lading** | 提单 | Shipping document |
| **Certificate of Origin** | 原产地证书 | Export document |
| **Customs Declaration** | 报关单 | Customs form |
| **Inspection Certificate** | 检验证书 | Quality document |

---

## 🎯 **QUALITY STANDARDS**

### **Professional Language Requirements**

1. **Business-Grade Chinese**: Use formal business terminology
2. **Industry Context**: Manufacturing and export-specific terms
3. **Consistency**: Uniform terminology across all modules
4. **Completeness**: All translations properly formatted
5. **Cultural Appropriateness**: Suitable for Chinese business environment

### **Translation Quality Checklist**

- [ ] **Terminology Accuracy**: Uses correct Manufacturing ERP terms
- [ ] **Professional Language**: Business-grade Chinese expressions
- [ ] **Context Appropriateness**: Suitable for UI/business context
- [ ] **Consistency**: Matches existing terminology patterns
- [ ] **Completeness**: No missing or incomplete translations
- [ ] **Cultural Sensitivity**: Appropriate for Chinese business culture

---

## 📝 **REVIEW GUIDELINES**

### **What to Focus On**

1. **Technical Accuracy**: Ensure manufacturing terms are correct
2. **Business Context**: Consider how terms are used in business processes
3. **User Experience**: Think about end-user understanding
4. **Consistency**: Match existing terminology in the system
5. **Professional Tone**: Maintain business-appropriate language

### **Common Review Areas**

| Area | Focus | Example |
|------|-------|---------|
| **Form Labels** | Clear, concise | "Customer Name" → "客户名称" |
| **Button Text** | Action-oriented | "Save Contract" → "保存合同" |
| **Status Values** | Consistent terms | "Pending" → "待处理" |
| **Error Messages** | Helpful, professional | "Required field" → "必填字段" |
| **Navigation** | Clear hierarchy | "Dashboard" → "仪表板" |

### **Quality Indicators**

✅ **Good Translation:**
- Uses established business terminology
- Maintains professional tone
- Fits the UI context appropriately
- Consistent with existing translations

❌ **Needs Improvement:**
- Uses casual or informal language
- Inconsistent with established terms
- Too literal or doesn't fit context
- Missing cultural appropriateness

---

## 🔧 **TOOLS & SETUP**

### **Recommended CSV Editors**

1. **Microsoft Excel**
   - Full formatting support
   - Easy sorting and filtering
   - Professional editing features

2. **Google Sheets**
   - Collaborative editing
   - Cloud-based access
   - Real-time sharing

3. **LibreOffice Calc**
   - Free alternative
   - Full CSV support
   - Cross-platform

### **File Handling Tips**

- **Encoding**: Always save as UTF-8 to preserve Chinese characters
- **Format**: Keep CSV format (comma-separated values)
- **Backup**: Save a copy before making changes
- **Naming**: Use descriptive filenames (e.g., `analytics-reviewed-2025-09-15.csv`)

---

## 📊 **REVIEW WORKFLOW**

### **Efficient Review Process**

1. **Initial Scan**: Quick overview of all translations
2. **Priority Focus**: Review high-priority items first
3. **Context Check**: Consider usage context for each term
4. **Consistency Verify**: Ensure terms match existing patterns
5. **Final Review**: Double-check all changes before saving

### **Batch Review Strategy**

```
Review Order:
1. High Priority Items (critical UI elements)
2. Business Terms (contracts, orders, customers)
3. Technical Terms (manufacturing processes)
4. Common UI Elements (buttons, labels, messages)
5. Low Priority Items (help text, descriptions)
```

### **Quality Assurance Steps**

- [ ] **Terminology Check**: Verify manufacturing terms are accurate
- [ ] **Context Review**: Ensure translations fit their usage context
- [ ] **Consistency Audit**: Match existing system terminology
- [ ] **Professional Tone**: Maintain business-appropriate language
- [ ] **Completeness Check**: No missing or incomplete translations
- [ ] **Final Proofread**: Review all changes before submission

---

## 🎯 **COMMON SCENARIOS**

### **Scenario 1: New Feature Module**

**Received**: CSV with 25 new translations for customer analytics dashboard

**Review Focus**:
- Dashboard terminology consistency
- Analytics and reporting terms
- Chart and graph labels
- Filter and search options

**Key Considerations**:
- Match existing dashboard terminology
- Use professional analytics language
- Ensure chart labels are concise
- Maintain consistency with existing filters

### **Scenario 2: Form Validation Messages**

**Received**: CSV with 15 error message translations

**Review Focus**:
- Clear, helpful error messages
- Professional but user-friendly tone
- Consistent error message patterns
- Appropriate urgency level

**Key Considerations**:
- Help users understand what went wrong
- Provide actionable guidance
- Maintain professional tone
- Match existing error message style

### **Scenario 3: Business Process Updates**

**Received**: CSV with 30 translations for updated work order process

**Review Focus**:
- Manufacturing process terminology
- Work order status terms
- Operation and task descriptions
- Quality control language

**Key Considerations**:
- Ensure manufacturing accuracy
- Match existing work order terms
- Consider production workflow context
- Maintain technical precision

---

## 📋 **QUALITY METRICS**

### **Review Success Indicators**

- **Terminology Accuracy**: 95%+ correct manufacturing terms
- **Professional Language**: Business-grade Chinese throughout
- **Consistency Score**: Matches existing system terminology
- **Context Appropriateness**: Suitable for intended usage
- **Completeness Rate**: All translations properly formatted

### **Common Quality Issues**

| Issue | Example | Solution |
|-------|---------|----------|
| **Too Literal** | "Work Order" → "工作订单" | Use "工单" (established term) |
| **Inconsistent** | Sometimes "客户", sometimes "顾客" | Always use "客户" for business |
| **Too Casual** | "OK" → "好的" | Use "确定" for professional UI |
| **Missing Context** | Generic "Status" → "状态" | Specify "订单状态" for order status |

---

## 🔄 **FEEDBACK & ITERATION**

### **Providing Feedback**

When returning reviewed CSV files:

1. **Summary Note**: Brief overview of changes made
2. **Key Decisions**: Explain major terminology choices
3. **Questions**: Flag any unclear contexts or terms
4. **Suggestions**: Recommend improvements for future batches

### **Continuous Improvement**

- **Pattern Recognition**: Note recurring translation patterns
- **Terminology Building**: Contribute to term glossary
- **Process Feedback**: Suggest workflow improvements
- **Quality Tracking**: Monitor translation quality trends

---

## 📞 **SUPPORT & RESOURCES**

### **Quick Help**

- **Translation Questions**: Localization team lead
- **Technical Issues**: Development team
- **Process Questions**: Project manager
- **Terminology Clarification**: Manufacturing subject matter expert

### **Reference Materials**

- 📚 **Complete Guide**: `docs/i18n-team-guide.md`
- 🎓 **Training Materials**: `docs/i18n-training-presentation.md`
- 🔧 **Technical Reference**: `docs/i18n-technical-reference.md`
- 👨‍💻 **Developer Guide**: `docs/i18n-developer-cheatsheet.md`

### **Terminology Resources**

- **Manufacturing Glossary**: 1,000+ specialized terms
- **Business Term Database**: Established ERP terminology
- **Industry Standards**: Textile export terminology
- **Existing Translations**: 1,050+ current system terms

---

## 🎉 **SUCCESS TIPS**

### **Best Practices**

1. **Context First**: Always consider how the term is used
2. **Consistency Matters**: Match existing system terminology
3. **Professional Tone**: Maintain business-appropriate language
4. **User Perspective**: Think about end-user understanding
5. **Quality Focus**: Prioritize accuracy over speed

### **Efficiency Tips**

- **Batch Similar Items**: Group related translations together
- **Use Find/Replace**: Maintain consistency across similar terms
- **Reference Existing**: Check current system for established terms
- **Ask Questions**: Clarify unclear contexts before translating
- **Document Decisions**: Note reasoning for future reference

---

**🌟 Your expertise ensures professional-quality Manufacturing ERP translations!**

*Thank you for maintaining the high standards that make our Manufacturing ERP system world-class.*
