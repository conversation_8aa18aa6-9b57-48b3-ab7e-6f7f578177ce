# 🌐 API Documentation - Export Declaration Module

## **📋 EXPORT DECLARATION API ENDPOINTS**

### **✅ COMPLETED API IMPLEMENTATION**

#### **Base URL**: `/api/export/declarations`

---

## **🔍 GET /api/export/declarations**

### **Description**
Retrieve all export declarations for the authenticated company with optional sales contract relationships.

### **Authentication**
- **Required**: Yes (withTenantAuth middleware)
- **Multi-tenant**: Automatically filtered by company_id

### **Response Format**
```json
{
  "success": true,
  "data": [
    {
      "id": "decl_abc123",
      "company_id": "company_xyz789",
      "number": "EXP-2025-001",
      "status": "draft",
      "sales_contract_id": "sc_def456",
      "contract_reference": "multi-local-2",
      "created_at": "2025-01-15T10:30:00Z",
      "updated_at": "2025-01-15T10:30:00Z",
      "salesContract": {
        "id": "sc_def456",
        "number": "multi-local-2",
        "status": "approved",
        "customer": {
          "id": "cust_ghi789",
          "name": "Local Customer 1"
        }
      },
      "items": [
        {
          "id": "dcli_item123",
          "product_id": "prod_jkl012",
          "qty": "1000",
          "hs_code": "5007.20",
          "quality_status": "pending",
          "product": {
            "id": "prod_jkl012",
            "name": "Cotton Fabric",
            "hs_code": "5007.20"
          }
        }
      ]
    }
  ],
  "message": null,
  "timestamp": "2025-01-15T10:30:00Z"
}
```

---

## **📝 POST /api/export/declarations**

### **Description**
Create a new export declaration with optional sales contract linking and product inheritance.

### **Authentication**
- **Required**: Yes (withTenantAuth middleware)
- **Multi-tenant**: Automatically associates with company_id

### **Request Body**
```json
{
  "number": "EXP-2025-002",
  "status": "draft",
  "sales_contract_id": "sc_def456",
  "contract_reference": "multi-local-2",
  "items": [
    {
      "product_id": "prod_jkl012",
      "qty": "1000",
      "hs_code": "5007.20",
      "quality_status": "pending"
    }
  ]
}
```

### **Validation Schema**
```typescript
const declarationSchema = z.object({
  number: z.string().min(1, "Declaration number is required").max(100),
  status: z.enum(["draft", "submitted", "cleared", "exception"]).default("draft"),
  sales_contract_id: z.string().optional(),
  contract_reference: z.string().optional(),
  items: z.array(declarationItemSchema).min(1, "At least one item is required")
})

const declarationItemSchema = z.object({
  product_id: z.string().min(1, "Product is required"),
  qty: z.union([z.number().positive(), z.string().min(1)]),
  hs_code: z.string().max(20).optional(),
  quality_status: z.string().optional()
})
```

### **Response Format**
```json
{
  "success": true,
  "data": {
    "id": "decl_new123",
    "company_id": "company_xyz789",
    "number": "EXP-2025-002",
    "status": "draft",
    "sales_contract_id": "sc_def456",
    "contract_reference": "multi-local-2",
    "created_at": "2025-01-15T11:00:00Z",
    "updated_at": "2025-01-15T11:00:00Z",
    "items": [...]
  },
  "message": "Declaration created successfully",
  "timestamp": "2025-01-15T11:00:00Z"
}
```

---

## **🔍 GET /api/export/declarations/[id]**

### **Description**
Retrieve a specific export declaration with full relationship data.

### **Authentication**
- **Required**: Yes (withTenantAuth middleware)
- **Security**: Validates declaration belongs to authenticated company

### **Response Format**
```json
{
  "success": true,
  "data": {
    "id": "decl_abc123",
    "company_id": "company_xyz789",
    "number": "EXP-2025-001",
    "status": "draft",
    "sales_contract_id": "sc_def456",
    "contract_reference": "multi-local-2",
    "created_at": "2025-01-15T10:30:00Z",
    "updated_at": "2025-01-15T10:30:00Z",
    "salesContract": {
      "id": "sc_def456",
      "number": "multi-local-2",
      "status": "approved",
      "customer": {
        "id": "cust_ghi789",
        "name": "Local Customer 1"
      }
    },
    "items": [...],
    "documents": [...]
  },
  "message": null,
  "timestamp": "2025-01-15T10:30:00Z"
}
```

---

## **✏️ PATCH /api/export/declarations/[id]**

### **Description**
Update an existing export declaration (preserves existing functionality).

### **Authentication**
- **Required**: Yes (withTenantAuth middleware)
- **Security**: Validates declaration belongs to authenticated company

### **Request Body**
```json
{
  "status": "submitted",
  "sales_contract_id": "sc_new789",
  "contract_reference": "updated-contract-ref"
}
```

---

## **🗑️ DELETE /api/export/declarations/[id]**

### **Description**
Delete an export declaration with professional confirmation dialog.

### **Authentication**
- **Required**: Yes (withTenantAuth middleware)
- **Security**: Validates declaration belongs to authenticated company

### **Response Format**
```json
{
  "success": true,
  "data": null,
  "message": "Declaration deleted successfully",
  "timestamp": "2025-01-15T11:30:00Z"
}
```

---

## **🔗 INTEGRATION ENDPOINTS**

### **GET /api/contracts/sales**
Used by ContractSelector component to load available sales contracts.

**Response includes:**
- Contract basic information (id, number, status, customer)
- Contract items with product details for inheritance
- Filtered to show only approved/active contracts

---

## **🛡️ SECURITY IMPLEMENTATION**

### **Multi-Tenant Security**
```typescript
// ✅ ALL ENDPOINTS: Automatic company isolation
export const GET = withTenantAuth(async function GET(request, context) {
  const declarations = await db.query.declarations.findMany({
    where: eq(declarations.company_id, context.companyId), // ALWAYS filtered
    with: { salesContract: { with: { customer: true } } }
  })
  return createSuccessResponse(declarations)
})
```

### **Input Validation**
```typescript
// ✅ ALL POST/PATCH: Zod schema validation
const validation = await validateRequestBody(req, declarationSchema)
if (!validation.success) {
  return createValidationErrorResponse(validation.error.issues)
}
```

### **Error Handling**
```typescript
// ✅ PROFESSIONAL: Consistent error responses
try {
  // API logic
} catch (error) {
  console.error("API Error:", error)
  return createErrorResponse(error)
}
```

---

## **📊 FIELD FORMATS**

### **Contract Linking Fields**
- **sales_contract_id**: Optional UUID string referencing sales_contracts.id
- **contract_reference**: Optional human-readable string for display purposes
- **Relationship**: Optional (NULL allowed for manual declarations)

### **Item Fields**
- **product_id**: Required UUID string referencing products.id
- **qty**: String or number (converted to string for storage)
- **hs_code**: Optional string (max 20 characters) for customs classification
- **quality_status**: Optional string (default: "pending")

---

**✅ API STATUS**: Production-ready with comprehensive CRUD operations, multi-tenant security, and professional error handling.
