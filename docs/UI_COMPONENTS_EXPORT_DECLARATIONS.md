# 🎨 UI Components Documentation - Export Declaration Module

## **📋 EXPORT DECLARATION UI COMPONENTS**

### **✅ COMPLETED COMPONENT IMPLEMENTATION**

---

## **🔽 ContractSelector Component**

### **Location**: `components/export/contract-selector.tsx`

### **Purpose**
Professional dropdown component for selecting sales contracts when creating export declarations.

### **Features**
- ✅ **Smart Filtering**: Only shows approved/active contracts
- ✅ **Rich Information**: Displays contract number, customer, date, status, item count
- ✅ **Optional Selection**: Includes "No contract - Manual entry" option
- ✅ **Professional Styling**: Consistent with Shadcn/ui design system
- ✅ **Error Handling**: Graceful fallbacks and retry mechanisms
- ✅ **Loading States**: Professional loading indicators

### **Usage**
```typescript
import { ContractSelector } from "@/components/export/contract-selector"

<ContractSelector
  selectedContractId={selectedContract?.id}
  onContractSelect={handleContractSelect}
  className="mb-6"
/>
```

### **Props Interface**
```typescript
interface ContractSelectorProps {
  selectedContractId?: string
  onContractSelect: (contract: SalesContract | null) => void
  className?: string
}

interface SalesContract {
  id: string
  number: string
  status: string
  customer: { name: string }
  created_at: string
  items: Array<{
    id: string
    product: { id: string; name: string; hs_code?: string }
    qty: string
  }>
}
```

### **Key Implementation Details**
- **API Integration**: Fetches from `/api/contracts/sales` with proper error handling
- **Business Logic**: Filters contracts by status (approved/active only)
- **User Experience**: Clear visual hierarchy with icons and badges
- **Accessibility**: Proper ARIA labels and keyboard navigation

---

## **📦 ProductInheritance Component**

### **Location**: `components/export/product-inheritance.tsx`

### **Purpose**
Smart component for managing product inheritance from sales contracts with full user editing control.

### **Features**
- ✅ **Automatic Inheritance**: Populates products when contract selected
- ✅ **User Control**: Full editing capability after inheritance
- ✅ **Manual Addition**: Add products independently of contracts
- ✅ **Smart Defaults**: Auto-populates HS codes from product data
- ✅ **Professional Table**: Consistent with ERP table standards
- ✅ **Flexible Management**: Add, edit, remove products as needed

### **Usage**
```typescript
import { ProductInheritance } from "@/components/export/product-inheritance"

<ProductInheritance
  selectedContract={selectedContract}
  items={items}
  onItemsChange={handleItemsChange}
  className="mt-6"
/>
```

### **Props Interface**
```typescript
interface ProductInheritanceProps {
  selectedContract: SalesContract | null
  items: DeclarationItem[]
  onItemsChange: (items: DeclarationItem[]) => void
  className?: string
}

interface DeclarationItem {
  id: string // Temporary UI ID
  product_id: string
  product?: Product
  qty: string
  hs_code: string
  quality_status: string
}
```

### **Key Implementation Details**
- **Inheritance Logic**: Automatically populates from contract.items when contract selected
- **User Override**: All inherited data can be modified by user
- **Product Management**: Dropdown selection with search functionality
- **Validation**: Real-time validation with visual feedback
- **Professional UX**: Empty states, loading indicators, clear actions

---

## **📝 CreateDeclarationForm Component**

### **Location**: `components/export/create-declaration-form.tsx`

### **Purpose**
Integrated form component combining contract selection and product inheritance for export declaration creation.

### **Features**
- ✅ **Integrated Workflow**: Combines ContractSelector and ProductInheritance
- ✅ **Auto-Generation**: Generates declaration numbers automatically
- ✅ **Professional Validation**: Comprehensive form validation with Zod
- ✅ **Contract Integration**: Seamless contract linking and product inheritance
- ✅ **User Feedback**: Professional toast notifications and error handling
- ✅ **Navigation**: Automatic navigation to created declaration

### **Usage**
```typescript
// Used in: app/export/create/page.tsx
import { CreateDeclarationForm } from "@/components/export/create-declaration-form"

<CreateDeclarationForm />
```

### **Key Implementation Details**
- **Form State Management**: React state with proper validation
- **API Integration**: Creates declarations via `/api/export/declarations`
- **Contract Linking**: Includes sales_contract_id and contract_reference
- **Product Handling**: Supports both inherited and manual products
- **Error Handling**: Professional error messages and recovery

---

## **🎨 DESIGN SYSTEM COMPLIANCE**

### **✅ Shadcn/ui Components Used**
- **Select**: Professional dropdown with search functionality
- **Input**: Consistent form inputs with validation states
- **Button**: Standard action buttons with loading states
- **Table**: Professional data tables with proper styling
- **Badge**: Status indicators and labels
- **Card**: Content organization and visual hierarchy
- **Label**: Accessible form labels

### **✅ Professional Styling Patterns**
```typescript
// ✅ CONSISTENT: Professional table layout
<Table>
  <TableHeader>
    <TableRow>
      <TableHead>Product</TableHead>
      <TableHead>Quantity</TableHead>
      <TableHead>HS Code</TableHead>
      <TableHead>Quality Status</TableHead>
      <TableHead className="w-[50px]">Actions</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    {/* Professional table rows */}
  </TableBody>
</Table>

// ✅ CONSISTENT: Professional form layout
<div className="grid gap-6 md:grid-cols-2">
  <div className="space-y-2">
    <Label htmlFor="field">Field Label *</Label>
    <Input id="field" placeholder="Professional placeholder" required />
  </div>
</div>
```

### **✅ Responsive Design**
- **Mobile-First**: All components work on mobile devices
- **Breakpoints**: Proper responsive breakpoints (md:, lg:)
- **Touch-Friendly**: Appropriate touch targets and spacing
- **Accessibility**: Keyboard navigation and screen reader support

---

## **🔄 INTEGRATION PATTERNS**

### **✅ State Management Pattern**
```typescript
// ✅ PARENT COMPONENT: Manages overall state
const [selectedContract, setSelectedContract] = useState<SalesContract | null>(null)
const [items, setItems] = useState<DeclarationItem[]>([])

// ✅ CHILD COMPONENTS: Receive props and call callbacks
<ContractSelector onContractSelect={setSelectedContract} />
<ProductInheritance items={items} onItemsChange={setItems} />
```

### **✅ API Integration Pattern**
```typescript
// ✅ DEFENSIVE: Handle API response formats
const result = await response.json()
const contractsArray = Array.isArray(result.data) ? result.data : []

// ✅ ERROR HANDLING: Professional error management
try {
  // API call
} catch (error) {
  console.error('Error:', error)
  setError(error instanceof Error ? error.message : 'Operation failed')
}
```

### **✅ User Experience Pattern**
```typescript
// ✅ LOADING STATES: Professional loading indicators
{loading ? (
  <div className="text-center py-8 text-muted-foreground">
    Loading contracts...
  </div>
) : (
  // Content
)}

// ✅ EMPTY STATES: Clear guidance for users
{items.length === 0 ? (
  <div className="border rounded-md p-8 text-center text-muted-foreground">
    <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
    <div className="text-lg font-medium mb-2">No products added yet</div>
    <div className="text-sm mb-4">Click "Add Product" to get started</div>
  </div>
) : (
  // Table content
)}
```

---

## **📊 COMPONENT ARCHITECTURE**

### **✅ Component Hierarchy**
```
CreateDeclarationForm (Parent)
├── ContractSelector (Contract selection)
├── ProductInheritance (Product management)
│   ├── Product selection dropdowns
│   ├── Quantity/HS code inputs
│   └── Add/Remove actions
└── Form actions (Save/Cancel)
```

### **✅ Data Flow**
```
User selects contract → ContractSelector calls onContractSelect → 
Parent updates selectedContract → ProductInheritance receives contract → 
Auto-populates products → User edits products → ProductInheritance calls onItemsChange → 
Parent updates items → Form submission includes contract and items
```

---

**✅ COMPONENT STATUS**: Production-ready with professional design, comprehensive functionality, and seamless integration with existing ERP modules.
