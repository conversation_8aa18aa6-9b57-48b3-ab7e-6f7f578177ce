# Developer Quick Reference - i18n Acceleration

**Manufacturing ERP i18n Cheat Sheet**

---

## 🚀 **DAILY WORKFLOW (UNCHANGED)**

```typescript
// Continue coding exactly as you always have
const { t } = useI18n()

return (
  <div>
    <h1>{t("customers.title")}</h1>
    <p>{t("customers.description")}</p>
    <Button>{t("common.save")}</Button>
  </div>
)
```

**Key Point:** Your existing code continues working exactly as before!

---

## 🔍 **WHEN CI/CD DETECTS NEW STRINGS**

### **Quick 3-Step Process**

```bash
# 1. Generate AI translations (30 seconds)
node scripts/i18n-ai-processor.js

# 2. Optional: Export for team review
node scripts/i18n-csv-workflow.js export batch-name.json

# 3. Integrate safely (with rollback capability)
node scripts/i18n-sync-mechanism.js integrate batch-name.json
```

### **Alternative: Manual Processing**

```bash
# If AI is unavailable, use manual templates
node scripts/i18n-extract.js
# Edit the generated templates manually
node scripts/i18n-sync-mechanism.js integrate manual-batch.json
```

---

## 📊 **ESSENTIAL COMMANDS**

| Command | Purpose | Usage |
|---------|---------|-------|
| **Detection** | Find hardcoded strings | `node scripts/i18n-auto-detect.js` |
| **AI Processing** | Generate translations | `node scripts/i18n-ai-processor.js` |
| **CSV Export** | Team collaboration | `node scripts/i18n-csv-workflow.js export batch.json` |
| **CSV Import** | Import reviewed translations | `node scripts/i18n-csv-workflow.js import reviewed.csv` |
| **Integration** | Apply translations safely | `node scripts/i18n-sync-mechanism.js integrate batch.json` |
| **Validation** | Check quality | `node scripts/i18n-translation-validator.js validate batch.json` |
| **Testing** | System integration test | `node scripts/i18n-integration-tester.js test` |
| **Rollback** | Emergency restore | `node scripts/i18n-rollback-validator.js validate` |

---

## 🎯 **BEST PRACTICES**

### **Code Patterns**

```typescript
// ✅ GOOD: Use t() function for user-facing text
const { t } = useI18n()
return <h1>{t("customers.title")}</h1>

// ❌ AVOID: Hardcoded strings
return <h1>Customers</h1>

// ✅ GOOD: Descriptive key names
t("customers.form.validation.email.required")

// ❌ AVOID: Generic key names
t("error1")

// ✅ GOOD: Context-aware keys
t("buttons.save")  // For buttons
t("labels.name")   // For form labels
t("messages.success.created")  // For success messages
```

### **Key Naming Conventions**

```typescript
// Module-based hierarchy
"customers.title"              // Page titles
"customers.form.name"          // Form fields
"customers.table.actions"      // Table actions
"customers.messages.created"   // Success messages
"customers.errors.validation"  // Error messages

// Common patterns
"common.save"                  // Shared buttons
"common.cancel"               // Shared actions
"navigation.dashboard"        // Navigation items
"status.pending"              // Status values
```

---

## 🔧 **TROUBLESHOOTING**

### **Common Issues**

| Problem | Solution |
|---------|----------|
| **AI translation fails** | Use manual: `node scripts/i18n-extract.js` |
| **"Command not found"** | Check you're in project root directory |
| **CSV import errors** | Ensure UTF-8 encoding and proper format |
| **Integration conflicts** | Run rollback: `node scripts/i18n-rollback-validator.js` |
| **Performance issues** | Check: `node scripts/i18n-performance-assessor.js assess` |
| **Quality score low** | Review: `node scripts/i18n-translation-validator.js validate` |

### **Emergency Procedures**

```bash
# 1. Immediate rollback (5ms restore)
node scripts/i18n-rollback-validator.js validate

# 2. Check system status
node scripts/i18n-integration-tester.js test

# 3. Restore from backup (if needed)
# Use latest backup from i18n-backup/ directory

# 4. Reset workflow (if needed)
rm -rf i18n-parallel/pending/*
rm -rf i18n-parallel/approved/*
```

---

## 📁 **DIRECTORY STRUCTURE**

```
Manufacturing ERP/
├── components/i18n-provider.tsx    # Main i18n system (NEVER TOUCH)
├── scripts/                        # Automation tools
│   ├── i18n-auto-detect.js        # String detection
│   ├── i18n-ai-processor.js       # AI translations
│   ├── i18n-csv-workflow.js       # Team collaboration
│   └── i18n-sync-mechanism.js     # Safe integration
├── i18n-parallel/                  # Parallel workflow
│   ├── pending/                    # New translations
│   ├── approved/                   # Reviewed translations
│   ├── integrated/                 # Applied translations
│   └── csv/                        # Team collaboration files
├── i18n-temp/                      # Temporary files
├── i18n-backup/                    # Safety backups
└── i18n-cicd/                      # CI/CD reports
```

---

## 🌐 **ENVIRONMENT VARIABLES**

```bash
# Optional: OpenAI API key for AI translations
OPENAI_API_KEY=your-api-key-here

# CI/CD Configuration (usually set automatically)
I18N_MAX_NEW_STRINGS=20            # Warning threshold
I18N_OUTPUT_FORMAT=json            # Report format
I18N_CHECK_LEVEL=standard          # Quality check level
I18N_CREATE_ARTIFACTS=true         # Create CI artifacts
```

---

## 📊 **CI/CD INTEGRATION**

### **GitHub Actions**

Your commits automatically trigger i18n quality checks:

- ✅ **Non-blocking**: Never fails your builds
- 📊 **Quality Reports**: Detailed analysis in job summary
- 💬 **PR Comments**: Automatic feedback on pull requests
- 📋 **Artifacts**: Downloadable reports and logs

### **Reading CI/CD Reports**

```
📊 i18n Quality Check Results:
Overall Status: ✅ PASSED / ⚠️ ATTENTION NEEDED
Quality Score: 95%
New Strings: 15
Warnings: 2

🎯 Recommendations:
- Consider processing detected hardcoded strings
- Review translation quality for new strings
```

---

## 🎯 **QUICK WINS**

### **5-Minute Setup**

```bash
# 1. Check current status
node scripts/i18n-auto-detect.js

# 2. Process any detected strings
node scripts/i18n-ai-processor.js

# 3. Integrate safely
node scripts/i18n-sync-mechanism.js integrate latest-batch.json
```

### **Team Collaboration**

```bash
# Export for team review
node scripts/i18n-csv-workflow.js export feature-batch.json

# Share CSV file with translators
# (They edit in Excel/Google Sheets)

# Import reviewed translations
node scripts/i18n-csv-workflow.js import reviewed-translations.csv

# Integrate team-reviewed translations
node scripts/i18n-sync-mechanism.js integrate reviewed-batch.json
```

---

## 🔒 **SAFETY REMINDERS**

### **Zero Breaking Changes Guarantee**

- ✅ **Existing System**: Completely untouched and functional
- ✅ **All Translations**: 1,050+ existing translations preserved
- ✅ **useI18n() Hook**: Continues working exactly as before
- ✅ **Production Stability**: No risk to live system

### **Safety Features**

- 💾 **Automatic Backups**: Before any changes
- 🔄 **5ms Rollback**: Instant restore capability
- 🧪 **Parallel Processing**: Isolated from main system
- 📋 **Audit Trail**: Complete operation logging
- 🔍 **Quality Validation**: Multi-stage quality checks

---

## 📞 **SUPPORT**

### **Quick Help**

- **Technical Issues**: Development team lead
- **Process Questions**: Project manager
- **Translation Quality**: Localization team
- **Emergency Support**: System administrator

### **Self-Service Resources**

- 📚 **Complete Guide**: `docs/i18n-team-guide.md`
- 🎓 **Training**: `docs/i18n-training-presentation.md`
- 🔧 **Technical Reference**: `docs/i18n-technical-reference.md`
- 👥 **Reviewer Guide**: `docs/i18n-reviewer-cheatsheet.md`

---

**🚀 Remember: Same coding patterns, 80% faster translation processing!**

*Your Manufacturing ERP system now has world-class i18n acceleration with enterprise-grade quality assurance.*
